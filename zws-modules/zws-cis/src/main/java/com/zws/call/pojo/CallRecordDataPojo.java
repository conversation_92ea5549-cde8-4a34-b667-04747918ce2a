package com.zws.call.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 通话记录（预测试外呼）-实体类
 *
 * @author: 马博新
 * @date ：Created in 2024/09/06 13:57
 */
@Data
public class CallRecordDataPojo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 案件id
     */
    @Excel(name = "案件ID", sort = 1)
    private Long caseId;

    /**
     * 主叫号码
     */
    private String callFrom;

    /**
     * 被叫号码
     */
    @Excel(name = "被叫号码", sort = 2)
    private String callTo;

    /**
     * 姓名
     */
    @Excel(name = "姓名", sort = 3)
    private String name;

    /**
     * 通话时间（呼叫时间）
     */
    @Excel(name = "通话时间", dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 4)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date callTime;

    /**
     * 呼叫坐席
     */
    private String sipNumber;

    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 呼叫坐席(员工姓名)-导出用
     */
    @Excel(name = "呼叫坐席（员工姓名）", sort = 5)
    private String employeeNameAndSip;

    /**
     * 通话状态
     * 0=待拨打，1=已接通，2=未接通，3=漏接
     */
    @Excel(name = "通话状态", readConverterExp = "0=待拨打,1=已接听,2=未接通,3=漏接,100=未接听", sort = 7)
    private Integer connectFlag;

    /**
     * 通话时长
     */
    @Excel(name = "通话时长", sort = 8,suffix = "秒")
    private Integer agentDuration;

    /**
     * 录音地址
     */
    private String recording;

    /**
     * 服务主机地址
     */
    private String serviceHost;

    /**
     * 号码状态
     */
    @Excel(name = "号码状态", sort = 6)
    private String callResult;

    /**
     * 判断该案件是否属于本团队（0-不属于,1-属于）
     * -- saas 不需要做这个判断
     */
    private Integer button;
}
