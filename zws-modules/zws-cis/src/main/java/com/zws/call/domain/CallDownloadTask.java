package com.zws.call.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.enums.call.TaskStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * 下载任务
 */
@Data
public class CallDownloadTask {
    /**
     *  下载任务id
     */
    private Long id;
    /**
     * 任务名称- 唯一
     */
    private String taskName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建人id
     */
    private Long createById;
    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     *
     */
    private String updateBy;
    /**
     * 任务状态
     * 0-进行中，1-已完成，2-已失败，3-已过期
     */
    private Integer taskStatus;
    /**
     *  下载的url地址
     */
    private String fileUrl;
    /**
     *  备注说明
     */
    private String remarks;
    /**
     * 操作人类型（0-主账号;1-员工账号;2-资产端账号）
     */
    private Integer operationType;



    /**
     * 任务状态-中文信息
     */
    private String taskStatusInfo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName == null ? null : taskName.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    public Long getCreateById() {
        return createById;
    }

    public void setCreateById(Long createById) {
        this.createById = createById;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl == null ? null : fileUrl.trim();
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    public String getTaskStatusInfo() {
        TaskStatusEnum enumer=  TaskStatusEnum.valueOfCode(this.taskStatus);
        if (enumer!=null) {
            return enumer.getInfo();
        }
        return taskStatusInfo;
    }
}
