package com.zws.call.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 工作手机配置
 * <AUTHOR>
 * @date ：Created in 2025/1/13 11:29
 */
@Data
@Configuration
@ConfigurationProperties(prefix  = "work-phone")
public class WorkPhoneConfig {

    /**
     * 云客接口地址
     */
    private String hostUrl;

    /**
     * 接口签名key
     */
    private String interfaceKey;

    /**
     * 管理员ID
     */
    private String adminId;

    /**
     * 企业串码
     */
    private String enterpriseSerialNumber;


}
