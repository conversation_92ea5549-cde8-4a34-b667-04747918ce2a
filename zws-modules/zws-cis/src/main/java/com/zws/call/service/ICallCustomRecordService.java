package com.zws.call.service;

import com.zws.call.domain.CallCustomRecord;
import com.zws.call.pojo.DeptPojo;
import com.zws.call.pojo.EmployeesPojo;
import com.zws.call.pojo.EnterpriseDataPojo;

import java.util.List;
import java.util.Map;

/**
 * 客户资料导入记录（预测试外呼）-service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 10:55
 */
public interface ICallCustomRecordService {

    /**
     * 写入客户导入记录
     *
     * @param record
     * @return
     */
    Long insert(CallCustomRecord record);

    /**
     * 根据工号查询该机构员工id
     *
     * @param map
     * @return
     */
    Integer selectEmployeesId(Map<String, Object> map);

    /**
     * 根据机构id查询机构企业编码
     *
     * @param id
     * @return
     */
    EnterpriseDataPojo selectCompanyNum(Integer id);

    /**
     * 根据员工id查询坐席账号
     *
     * @param map
     * @return
     */
    String selectSip(Map<String, Object> map);

    /**
     * 根据团队id查询该团队所有部门信息
     *
     * @return
     */
    List<DeptPojo> selectDept(int caeateId);

    /**
     * 根据部门id查询该部门人员信息
     *
     * @param
     * @return
     */
    List<EmployeesPojo> selectEmployeesParentId(int parentId, int createId);

    /**
     * 根据parentId查询对应部门信息
     *
     * @param
     * @return
     */
    List<DeptPojo> selectDeptParentId(int parentId);

    /**
     * 查询机构已分配预测试外呼坐席催员id
     *
     * @param teamId
     * @return
     */
    List<Long> selectCallSip(Integer teamId);
}
