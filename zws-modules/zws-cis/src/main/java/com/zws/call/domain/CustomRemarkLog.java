package com.zws.call.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 备注记录-实体类
 *
 * @author: 马博新
 * @date ：Created in 2024/09/03 11:03
 */
@Data
public class CustomRemarkLog {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 备注内容
     */
    private String remark;

    /**
     * 备注时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date noteTime;

    /**
     * 备注人
     */
    private String noteBy;

    /**
     * 备注人id
     */
    private Integer noteById;

    /**
     * 备注人类型（0-主账号;1-员工账号;2-资产端账号）
     */
    private Integer noteByType;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 机构id
     */
    private Integer teamId;

    /**
     * 关联客户id
     */
    private Long customId;
}