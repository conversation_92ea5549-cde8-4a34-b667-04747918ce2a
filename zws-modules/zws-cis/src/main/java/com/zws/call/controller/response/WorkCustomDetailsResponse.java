package com.zws.call.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 工作手机弹屏详细信息表-实体类
 *
 * @author: h<PERSON><PERSON><PERSON>
 * @date ：Created in 2025/1/17 10:57
 */
@Data
public class WorkCustomDetailsResponse {

    /**
     * 案件id
     */
    private Long caseId;

    /**
     * 客户名称
     */
    private String caseName;

    /**
     * 联系方式
     */
    private String casePhone;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 债权总金额
     */
    private BigDecimal entrustMoney;

    /**
     * 剩余应还债权金额
     */
    private BigDecimal remainMoney;

    /**
     * 剩余应还本金
     */
    private BigDecimal residualPrincipal;

    /**
     * 逾期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date overTime;

}