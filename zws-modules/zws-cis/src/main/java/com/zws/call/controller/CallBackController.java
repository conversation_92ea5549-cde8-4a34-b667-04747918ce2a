package com.zws.call.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.util.StringUtil;
import com.zws.call.agservice.AgCallBackService;
import com.zws.call.config.CallConfig;
import com.zws.call.domain.CallRecord;
import com.zws.call.domain.CallSip;
import com.zws.call.pojo.*;
import com.zws.call.service.ICallRecordService;
import com.zws.call.service.IEnterpriseBillService;
import com.zws.call.service.ISpendingDetailService;
import com.zws.common.core.callcenter.domain.EnterpriseBill;
import com.zws.common.core.callcenter.domain.EnterpriseSpendingDetail;
import com.zws.common.core.callcenter.enums.CallroterEnum;
import com.zws.common.core.callcenter.pojo.CCResult;
import com.zws.common.core.callcenter.request.EnterpriseBillRequest;
import com.zws.common.core.callcenter.request.SpendingDetailRequest;
import com.zws.common.core.callcenter.service.YueCaiCallCenter;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.SplitUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.system.api.RemoteMessageService;
import com.zws.system.api.pojo.CallMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 点击呼叫 回调
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/18 17:27
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("/callSaas")
public class CallBackController {

    @Autowired
    private ICallRecordService callRecordService;
    @Autowired
    private RemoteMessageService remoteMessageService;
    @Autowired
    private AgCallBackService agCallBackService;
    @Autowired
    private YueCaiCallCenter yueCaiCallCenter;
    @Autowired
    private CallConfig callConfig;
    @Autowired
    private ISpendingDetailService spendingDetailService;
    @Autowired
    private IEnterpriseBillService enterpriseBillService;

    /**
     * 点击呼叫 回调
     *
     * @return
     */
    @PostMapping("/callback")
    public String callback(HttpServletResponse response, @RequestBody String jsonStr) {
        log.info("点击呼叫回调:{}", jsonStr);
        CallbackPojo param = JSONUtil.toBean(jsonStr, CallbackPojo.class);
        if (param == null) {
            return "数据格式错误";
        }
        log.info("点击呼叫回调:{}", JSONUtil.toJsonStr(param));
        CallroterEnum callroterEnum = CallroterEnum.valueOfCode(param.getType());
//        呼叫类型
        CallroterEnum callroter = CallroterEnum.valueOfCode(param.getCallroter());
//        判断是预测试外呼话单还是普通话单
        if (callroter == CallroterEnum.newauto) {
            agCallBackService.callback(param);
            return "SUCCESS";
        }
        //判断是否是AI语音通知话单
        if (callroter == CallroterEnum.newvoccall){
            agCallBackService.callbackAiVoice(param);
            return "SUCCESS";
        }

//        根据呼叫callid查找该话单是否存在
        Long aLong = callRecordService.selectByCallId(param.getCallid());
        if (aLong != null) {
            return "重复回调";
        }

        CallRecord callRecord = new CallRecord();

        BeanUtil.copyProperties(param, callRecord);
        callRecord.setCompanyNum(param.getEnterpriseNum());
        callRecord.setCompanyName(param.getEnterpriseName());
        callRecord.setAgentDuration(param.getExtDuration());
        callRecord.setCallroter(param.getCallroter());
        callRecord.setNumber(param.getTrunkNumber());

        if (param.getExtDuration() != null) {
            String answer = param.getExtDuration() > 0 ? "y" : "n";
            callRecord.setAnswer(answer);
        } else {
            callRecord.setAnswer("n");
        }

        callRecord.setServiceHost(param.getRecordHost());
        callRecord.setCallFrom(param.getCallfrom());
        callRecord.setCallTo(param.getCallto());
        callRecord.setCallbackTime(new Date());

        if (!ObjectUtils.isEmpty(param.getStartStamp())) {
            Date callTime = new Date(param.getStartStamp());
            String formatCall = DateUtil.format(callTime, "yyyy-MM-dd HH:mm:ss");
            Date dateCall = DateUtil.parse(formatCall, "yyyy-MM-dd HH:mm:ss");
            callRecord.setCallTime(dateCall);
        }

        Date answerStart = null, answerEnd = null;
        if (param.getAnswerStamp() != null && param.getAnswerStamp() > 0) {
            Date answerStartDate = new Date(param.getAnswerStamp());
            String formatStart = DateUtil.format(answerStartDate, "yyyy-MM-dd HH:mm:ss");
            answerStart = DateUtil.parse(formatStart, "yyyy-MM-dd HH:mm:ss");
        }
        if (param.getEndStamp() != null && param.getEndStamp() > 0) {
            Date answerEndDate = new Date(param.getEndStamp());
            String formatEnd = DateUtil.format(answerEndDate, "yyyy-MM-dd HH:mm:ss");
            answerEnd = DateUtil.parse(formatEnd, "yyyy-MM-dd HH:mm:ss");
        }
        callRecord.setAnswerStart(answerStart);
        callRecord.setAnswerEnd(answerEnd);

        //呼叫中心服务器话单地址
        String serviceHost = StringUtil.isEmpty(param.getRecordHost()) ? "https://fy.hndexin.net:7444/record/" : param.getRecordHost();

        log.info("呼叫中心请求地址:" + param.getRecordHost());

        //客户ID
        String caseId = param.getuID();
        if (StringUtils.isNotEmpty(caseId)) {
            callRecord.setCaseId(Long.valueOf(caseId));
        }
        log.info("客户id String：" + caseId);
        if (!ObjectUtils.isEmpty(caseId) && !caseId.equals("null")) {
            List<Long> stringList = SplitUtils.strSplitToLong(caseId, ":");
            log.info("客户id：" + stringList);
//             根据案件id集合查询借款人
            List<String> clientName = null;
            if (!ObjectUtils.isEmpty(stringList) && stringList.size() > 0) {
                clientName = callRecordService.getClientName(stringList);
                String join = StringUtils.join(stringList, ",");
                callRecord.setCaseIdStr(join);
            }
            log.info("借款人：" + stringList);
            if (!ObjectUtils.isEmpty(clientName) && clientName.size() > 0) {
                List<String> nameList = new ArrayList<>();
//                解密
                for (String row : clientName) {
                    String decrypt = FieldEncryptUtil.decrypt(row);
                    nameList.add(decrypt);
                }
                String join = StringUtils.join(nameList, ",");
                callRecord.setBorrower(join);
            }
        }

        Long customerId = null;//联系人id
        if (StringUtils.isNotEmpty(param.getCustomerId()) && !StringUtils.equals(param.getCustomerId().toLowerCase(), "null")) {
            customerId = Long.parseLong(param.getCustomerId());
        }
        //联系人ID
        callRecord.setContactId(customerId);

        String sip = null;//SIP账号
        String phone = null;
        //如果是呼入  call_to被叫号码 即是坐席号码
        if (callroterEnum == CallroterEnum.inbound) {
            //呼入 callto是坐席  callfrom是客户呼叫进来电话
            phone = param.getCallfrom();
            sip = param.getCallto();
        } else if (callroterEnum == CallroterEnum.outbound) {
            //呼出 callfrom是坐席  callto是联系人电话
            phone = param.getCallto();
            sip = param.getCallfrom();
        }
        callRecord.setSipNumber(sip);
//        如果是呼入或者直呼，需要用手机号码查询案件id以及借款人信息
        CallSip callSip = callRecordService.selectBySipNumber(sip);
//        根据sip账号查询员工id以及团队id
        log.info("员工sip账号:" + callRecord.getSipNumber());
        TeamPojo teamPojo = callRecordService.selectByTeamId(callRecord.getSipNumber());
        log.info("根据sip账号查询出的员工/团队id:" + teamPojo);
        if (!ObjectUtils.isEmpty(teamPojo) && !ObjectUtils.isEmpty(phone)) {
            if (teamPojo.getTeamId() != null && teamPojo.getUserId() != null) {

                //手机号加密
                String encrypt = FieldEncryptUtil.encrypt(phone);
                teamPojo.setContactPhone(encrypt);
                List<Long> longs = callRecordService.selectCaseIdPhone(teamPojo);
                //                如果匹配催员查询案件id为空则匹配整个团队
                if (ObjectUtils.isEmpty(longs) || longs.isEmpty()) {
                    teamPojo.setUserId(null);
                    longs = callRecordService.selectCaseIdPhone(teamPojo);
                }
                log.info("根据条件所查询出的案件id:" + longs);
                String idStr = null;
                if (!ObjectUtils.isEmpty(longs) && longs.size() > 0) {
                    String join = StringUtils.join(longs, ",");
                    idStr = join;
                    callRecord.setCaseIdStr(idStr);
                    log.info("根据条件所查询出的案件id字符串:" + idStr);
                    //                根据案件id集合查询借款人信息
                    List<String> clientName = callRecordService.getClientName(longs);
                    if (!ObjectUtils.isEmpty(clientName) && clientName.size() > 0 && StringUtils.isNotEmpty(param.getuID())) {
                        List<String> nameList = new ArrayList<>();
                        //                    解密
                        for (String row : clientName) {
                            String decrypt = FieldEncryptUtil.decrypt(row);
                            nameList.add(decrypt);
                        }
                        String joins = StringUtils.join(nameList, ",");
                        callRecord.setBorrower(joins);
                    }
                }

            }
        }

        String relationID = param.getRelationID();
        Long odvId = null;
        Long teamId = null;
        Integer type = null;
        if (callSip != null) {
            teamId = callSip.getTeamId();
            odvId = callSip.getOdvId();
        }

        List<String> stringList = SplitUtils.strSplit(relationID, ":");
        if (stringList.size() == 4) {
            String accountType = stringList.get(0);
            if (StringUtils.isNotEmpty(accountType) && !StringUtils.equals(accountType.toLowerCase(), "null")) {
                log.info("get[0]:" + accountType);
            }

            String teamIdStr = stringList.get(1);
            if (StringUtils.isNotEmpty(teamIdStr) && !StringUtils.equals(teamIdStr.toLowerCase(), "null")) {
                teamId = Long.parseLong(teamIdStr);
                log.info("get[1]:" + teamIdStr);
            }

            String odvIdStr = stringList.get(2);
            if (StringUtils.isNotEmpty(odvIdStr) && !StringUtils.equals(odvIdStr.toLowerCase(), "null")) {
                odvId = Long.parseLong(odvIdStr);
                log.info("get[2]:" + odvIdStr);
            }

//            账号类型
            String typeStr = stringList.get(3);
            log.info("get[3]:" + typeStr);
            if (StringUtils.isNotEmpty(typeStr) && !StringUtils.equals(typeStr.toLowerCase(), "null")) {
                int parseInt = Integer.parseInt(typeStr);
                if (parseInt == -1) {
                    type = null;
                } else {
                    type = parseInt;
                }
            }
        }

        callRecord.setTeamId(teamId);
        callRecord.setOdvId(odvId);
        callRecord.setServiceHost(serviceHost);
        callRecord.setTeamType(type);
        callRecordService.insert(callRecord);

        CallMessage callMessage = new CallMessage();
        callMessage.setIdentification(UserConstants.ACCOUNT_TYPE_1);
        callMessage.setUserId(odvId);
//        remoteMessageService.pushCallEnd(callMessage, SecurityConstants.INNER);

        //查询呼叫中心消费明细
//        if (callRecord.getAgentDuration() > 0 && StringUtils.isNotEmpty(callRecord.getSipNumber()) && callRecord.getCompanyNum() != null) {
//            SpendingDetailRequest request = new SpendingDetailRequest();
//            request.setSipAccount(callRecord.getSipNumber());
//            request.setApiFlag(callConfig.getApiFlag());
//            request.setServiceHostUrl(callConfig.getHostUrl());
//            request.setEnterpriseNum(callRecord.getCompanyNum());
//            //获取当前日期，格式为yyyy-MM
//            request.setMonth(DateUtil.format(new Date(), "yyyy-MM"));
//            request.setPage(1);
//            request.setLimit(50);
//            log.info("查询呼叫中心消费明细请求参数:{}", request);
//            CCResult<List<EnterpriseSpendingDetail>> result = yueCaiCallCenter.getEnterpriseSpendingDetail(request);
//            log.info("查询呼叫中心消费明细返回结果:{}", result);
//            List<EnterpriseSpendingDetail> list = new ArrayList<>();
//            list.addAll(result.getData());
//            while (result.getCount() > request.getPage() * request.getLimit()) {
//                request.setPage(request.getPage() + 1);
//                result = yueCaiCallCenter.getEnterpriseSpendingDetail(request);
//                if (result == null || result.getData() == null || result.getData().size() == 0) {
//                    break;
//                }
//                list.addAll(result.getData());
//            }
//            //保存消费明细
//            if (!CollectionUtils.isEmpty(list)) {
//                spendingDetailService.saveOrUpdateBatch(list);
//            }
//        }
        return "SUCCESS";
    }


    /**
     * 预测试外呼-通知接口
     *
     * @return
     */
    @PostMapping("/outboundCallNotification")
    public String outboundCallNotification(@Validated @RequestBody CallNoticePojo param) {
        if (param == null) {
            return "数据格式错误";
        }
        log.info("预测试外呼-通知接口{}", param.toString());
        agCallBackService.outboundCallNotification(param);
        return "SUCCESS";
    }

    /**
     * 出入账更新-通知接口
     *
     */
    @PostMapping("/billNotification")
    public String billNotification(@RequestBody BillNotificationPojo billNotificationPojo) {
        log.info("出入账更新-通知 唯一编号{}", billNotificationPojo.getBillUuid());
        log.info("出入账更新-通知 出入账类型0代表入账 1代表出账 :{}", billNotificationPojo.getType());
        log.info("出入账更新-通知 企业编号{}", billNotificationPojo.getEnterpriseNum());

        //查询出入账
        EnterpriseBillRequest enterpriseBillRequest = new EnterpriseBillRequest();
        enterpriseBillRequest.setApiFlag(callConfig.getApiFlag());
        enterpriseBillRequest.setServiceHostUrl(callConfig.getHostUrl());
        enterpriseBillRequest.setEnterpriseNum(billNotificationPojo.getEnterpriseNum().toString());
        //获取当前日期，格式为yyyy-MM
        enterpriseBillRequest.setMonth(DateUtil.format(new Date(), "yyyy-MM"));
        enterpriseBillRequest.setPage(1);
        enterpriseBillRequest.setLimit(50);
        log.info("查询呼叫中心出入账请求参数:{}", enterpriseBillRequest);
        CCResult<List<EnterpriseBill>> enterpriseBillResult = yueCaiCallCenter.getEnterpriseBill(enterpriseBillRequest);
        log.info("查询呼叫中心出入账返回结果:{}", enterpriseBillResult);
        List<EnterpriseBill> enterpriseBills = new ArrayList<>();
        enterpriseBills.addAll(enterpriseBillResult.getData());
        while (enterpriseBillResult.getCount() > enterpriseBillRequest.getPage() * enterpriseBillRequest.getLimit()) {
            enterpriseBillRequest.setPage(enterpriseBillRequest.getPage() + 1);
            enterpriseBillResult = yueCaiCallCenter.getEnterpriseBill(enterpriseBillRequest);
            if (enterpriseBillResult == null || enterpriseBillResult.getData() == null || enterpriseBillResult.getData().size() == 0) {
                break;
            }
            enterpriseBills.addAll(enterpriseBillResult.getData());
        }
        //保存充值明细
        if (!CollectionUtils.isEmpty(enterpriseBills)) {
            enterpriseBillService.saveOrUpdateBatch(enterpriseBills);
        }

        //出账
        if (BaseConstant.BILL_OUTCOME.equals(billNotificationPojo.getType())) {
            SpendingDetailRequest request = new SpendingDetailRequest();
            request.setApiFlag(callConfig.getApiFlag());
            request.setServiceHostUrl(callConfig.getHostUrl());
            request.setEnterpriseNum(String.valueOf(billNotificationPojo.getEnterpriseNum()));
            //获取当前日期，格式为yyyy-MM
            request.setMonth(DateUtil.format(new Date(), "yyyy-MM"));
            request.setPage(1);
            request.setLimit(50);
            log.info("查询呼叫中心出账请求参数:{}", request);
            CCResult<List<EnterpriseSpendingDetail>> result = yueCaiCallCenter.getEnterpriseSpendingDetail(request);
            log.info("查询呼叫中心出账返回结果:{}", result);
            List<EnterpriseSpendingDetail> list = new ArrayList<>();
            list.addAll(result.getData());
            while (result.getCount() > request.getPage() * request.getLimit()) {
                request.setPage(request.getPage() + 1);
                result = yueCaiCallCenter.getEnterpriseSpendingDetail(request);
                if (result == null || result.getData() == null || result.getData().size() == 0) {
                    break;
                }
                list.addAll(result.getData());
            }
            //保存消费明细
            if (!CollectionUtils.isEmpty(list)) {
                spendingDetailService.saveOrUpdateBatch(list);
            }
        }
        return "SUCCESS";
    }
}
