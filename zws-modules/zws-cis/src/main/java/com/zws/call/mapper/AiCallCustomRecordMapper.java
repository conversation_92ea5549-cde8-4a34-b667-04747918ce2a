package com.zws.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zws.call.domain.AiCallCustomRecord;
import com.zws.call.domain.CallCustomRecord;
import com.zws.call.domain.CallRecord;
import com.zws.call.pojo.DeptPojo;
import com.zws.call.pojo.EmployeesPojo;
import com.zws.call.pojo.EnterpriseDataPojo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 客户资料导入记录（AI语音通知）-Dao层
 *
 */
public interface AiCallCustomRecordMapper extends BaseMapper<CallRecord> {
    int deleteByPrimaryKey(Long id);

    int insert(AiCallCustomRecord record);

    int insertSelective(CallCustomRecord record);

    CallCustomRecord selectByPrimaryKey(Long id);

    /**
     * 根据工号查询该机构员工id
     *
     * @param map
     * @return
     */
    Integer selectEmployeesId(Map<String, Object> map);

    /**
     * 根据机构id查询机构企业编码
     *
     * @param id
     * @return
     */
    EnterpriseDataPojo selectCompanyNum(Integer id);

    /**
     * 根据员工id查询坐席账号
     *
     * @param map
     * @return
     */
    String selectSip(Map<String, Object> map);

    /**
     * 根据团队id查询该团队所有部门信息
     *
     * @return
     */
    List<DeptPojo> selectDept(int caeateId);

    /**
     * 根据部门id查询该部门人员信息
     *
     * @param
     * @return
     */
    List<EmployeesPojo> selectEmployeesParentId(@Param("parentId") int parentId, @Param("createId") int createId);

    /**
     * 根据parentId查询对应部门信息
     *
     * @param
     * @return
     */
    List<DeptPojo> selectDeptParentId(int parentId);

    /**
     * 查询机构已分配预测试外呼坐席催员id
     *
     * @param teamId
     * @return
     */
    List<Long> selectCallSip(Integer teamId);

    int updateByPrimaryKeySelective(CallCustomRecord record);

    int updateByPrimaryKey(CallCustomRecord record);
}