package com.zws.call.service.impl;

import com.zws.call.config.CallConfig;
import com.zws.call.domain.CallSip;
import com.zws.call.mapper.CallTeamSipMapper;
import com.zws.call.pojo.EnterpriseDataPojo;
import com.zws.call.pojo.TeamPojo;
import com.zws.call.service.ICallCustomRecordService;
import com.zws.call.service.ICallTeamSipService;
import com.zws.common.core.callcenter.pojo.CCResult;
import com.zws.common.core.callcenter.pojo.SipNumber;
import com.zws.common.core.callcenter.pojo.SipNumberParam;
import com.zws.common.core.callcenter.service.YueCaiCallCenter;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.common.core.enums.call.BindStatusEnum;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SIP 服务实现类
 *
 * <AUTHOR>
 * @date ：Created in 2024/10/9 11:59
 */
@Slf4j
@Service
@Primary
public class CallTeamSipServiceImpl implements ICallTeamSipService {

    @Autowired
    private CallTeamSipMapper mapper;
    @Autowired
    private YueCaiCallCenter yueCaiCallCenter;
    @Autowired
    private CallConfig callConfig;
    @Autowired
    private ICallCustomRecordService customRecordService;

    /**
     * 同步 呼叫中心 SIP数据--（Java）
     *
     * @param entityPojo
     */
    @Async
    @Override
    public void dataSyncJava(ThreadEntityPojo entityPojo) {
        if (ObjectUtils.isEmpty(entityPojo)) return;
        if (entityPojo.getCreateId() == null) return;
        SipNumberParam param = new SipNumberParam();
        param.setPage(1);
        param.setLimit(50);
        param.setServiceHostUrl(callConfig.getHostUrl());
//        根据登录人机构id查询机构入网企业编号以及企业api标识
        EnterpriseDataPojo dataPojo = customRecordService.selectCompanyNum(entityPojo.getCreateId());
        if (ObjectUtils.isEmpty(dataPojo)) throw new GlobalException("企业信息查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(callConfig.getApiFlag()))
            throw new GlobalException("企业api标识查询为空，无法同步坐席");
        if (StringUtils.isEmpty(dataPojo.getCompanyNum()))
            throw new GlobalException("企业编码查询为空，无法同步坐席");
//        param.setApiFlag(dataPojo.getApiFlag());
        param.setApiFlag(callConfig.getApiFlag());
        param.setEnterpriseNum(dataPojo.getCompanyNum());//入网企业编号
        List<SipNumber> list = new ArrayList<>();

        CCResult<List<SipNumber>> result = yueCaiCallCenter.getSipNumberDataJava(param);
        list.addAll(result.getData());
        while (result.getCount() > param.getPageNum() * param.getPageSize()) {
            param.setPage(param.getPage() + 1);
            result = yueCaiCallCenter.getSipNumberDataJava(param);
            if (result == null || result.getData() == null || result.getData().size() == 0) {
                break;
            }
            list.addAll(result.getData());
        }

        List<String> accountList = null;
        if (list != null && list.size() > 0) {
            accountList = list.stream().map(SipNumber::getAccount).collect(Collectors.toList());
        }
        //根据团队ID 获取机构下的所有的坐席
        List<CallSip> callSipList = mapper.selectByTeamId(entityPojo.getCreateId());
        for (CallSip callSip : callSipList) {
            if (!accountList.contains(callSip.getSipNumber())) {
                //数据坐席未同步回来、则表示该坐席  被删除/回收/禁用(都不会返回数据)
                callSip.setDelFlag(BaseConstant.DelFlag_Delete);

                callSip.setBindStatus(BindStatusEnum.UNBOUND.getCode());
                mapper.updateById(callSip.getId());
                mapper.updateEmployees(callSip.getSipNumber());
                this.deleteBySipNumber(callSip.getSipNumber());
            }
        }

        for (SipNumber temp : list) {
            CallSip sip = new CallSip();
            sip.setSipNumber(temp.getAccount());
            sip.setSipPassword(temp.getPassword());
            sip.setCompanyNum(temp.getEnterpriseNum());
            sip.setCompanyName(temp.getEnterpriseName());
            //ylq 无联通坐席、不返回删除标识/（增加该字段用来区分普通坐席与预测试外呼坐席）
            sip.setSeatsType(temp.getTypeFlag());
            TeamPojo team = getByCompanyNum(temp.getEnterpriseNum());
            if (team != null) {
                sip.setTeamId(team.getId());
            }
            CallSip entity = mapper.selectBySipNumber(sip.getSipNumber());
            if (entity == null) {
                sip.setCreateTime(DateUtils.getNowDate());
                sip.setCreateBy(SecurityUtils.getUsername());
                sip.setBindStatus(BindStatusEnum.UNBOUND.getCode());
                sip.setDelFlag(BaseConstant.DelFlag_Being);
                this.mapper.insert(sip);
            } else {
                //判断坐席是否重新分配给其他机构
                judgingSeats(entity, team, sip);
                this.updateBySipNumber(sip);

            }
        }
    }

    /**
     * 工作手机数据同步
     */
    @Async
    @Override
    public void workPhoneDataSync(ThreadEntityPojo entityPojo) {

    }

    @Override
    public void deleteBySipNumber(String sipNumber) {
        //如果是删除坐席号，要删除跟催员的关联
        CallSip sip = new CallSip();
        sip.setSipNumber(sipNumber);
        sip.setDelFlag(BaseConstant.DelFlag_Delete);
        mapper.updateBySipNumberSelective(sip);
    }

    @Override
    public TeamPojo getByCompanyNum(String companyNum) {
        return mapper.selectByCompanyNum(companyNum);
    }

    @Override
    public void updateBySipNumber(CallSip callSip) {
        mapper.updateBySipNumberSelective(callSip);
    }

    /**
     * 判断坐席是否重新分配给其他机构
     *
     * @param entity
     * @param team
     * @param sip
     */
    public void judgingSeats(CallSip entity, TeamPojo team, CallSip sip) {
        Long aLong = mapper.selectEmployees(entity.getSipNumber());
        if (aLong == null || ObjectUtils.isEmpty(team)) {
            sip.setBindStatus(BindStatusEnum.UNBOUND.getCode());
            mapper.updateById(entity.getId());
//                修改员工信息sip账号密码为空
            mapper.updateEmployees(entity.getSipNumber());
        } else {
            if (!aLong.equals(team.getId())) {
                sip.setBindStatus(BindStatusEnum.UNBOUND.getCode());
                mapper.updateById(entity.getId());
//                修改员工信息sip账号密码为空
                mapper.updateEmployees(entity.getSipNumber());
            } else {
                if (StringUtils.equals(sip.getDelFlag(), BaseConstant.DelFlag_Delete)) {
                    sip.setBindStatus(BindStatusEnum.UNBOUND.getCode());
                    mapper.updateById(entity.getId());
//                修改员工信息sip账号密码为空
                    mapper.updateEmployees(entity.getSipNumber());
                }
            }
        }
    }
}
