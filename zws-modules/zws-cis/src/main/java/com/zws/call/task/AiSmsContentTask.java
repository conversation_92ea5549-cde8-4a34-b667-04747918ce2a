package com.zws.call.task;

import com.zws.call.agservice.AiVoiceTaskService;
import com.zws.call.service.IAiVoiceTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

/**
 * AI语音通知定时任务
 */
@Component
public class AiSmsContentTask {

    @Autowired
    private AiVoiceTaskService aiVoiceTaskService;

    /**
     * 查询短信回复数据定时任务
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public Integer selectSmsContentTask() {
        return aiVoiceTaskService.selectSmsContentTask();
    }



}
