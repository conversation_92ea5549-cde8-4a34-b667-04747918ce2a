package com.zws.call.pojo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Ai语音通知-推送呼叫中心（实体类）
 */
@Data
public class AiCallCenterTaskPojo {

    /**
     * 呼叫中心服务地址
     */
    private String serviceHostUrl;

    /**
     * 企业API标识
     */
    private String apiFlag;

    /**
     * 企业编号
     */
    private String enterpriseNum;

    /**
     * 语音通知模板唯一编号
     */
    private String voiceTplUuid;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 执行时间，周一到周日，按顺序1到7，每天8，一至五9
     */
    private String executionTime;

    /**
     * 外呼时间段，如9:30-12:00,14:00-15:30
     */
    private String executionCallTime;

    /**
     * 任务开始时间
     */
    private String taskStartTime;

    /**
     * 任务结束时间
     */
    private String taskEndTime;

    /**
     * 重呼次数
     */
    private Integer recall;

    /**
     * 重呼间隔
     */
    private Integer recallMinute;


    /**
     * 备注
     */
    private String remark;

    /**
     * 是否发送短信0-不发送，1-发送
     */
    private Integer smsFlag;

    /**
     * 发送短信对象
     */
    private String smsCondition;

    /**
     * 呼叫对象 1本人 2全部号码
     */
    private Integer callTarget;

    /**
     * 任务标志 1导入 2案件
     */
    private Integer taskFlag;

    /**
     * 客户信息集合
     */
    private List<AiCallCustomDataPojo> callList;

//--------------------------------------------------------修改任务状态增加参数-------------------------------------------------

    /**
     * 任务唯一编号uuid集合
     */
    private List<String> taskUuids;

    /**
     * 状态 2执行 4暂停 5撤销
     */
    private Integer status;

//--------------------------------------------------------查询任务状态增加参数----------------------------------------------

    /**
     * 查询参数
     */
    private Map<String, Object> map;

    /**
     * 任务唯一编号uuid（返回参数）
     */
    private String taskUuid;


}
