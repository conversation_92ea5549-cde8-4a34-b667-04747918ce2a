package com.zws.call.pojo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 预测试任务-推送呼叫中心（实体类）
 *
 * @author: 马博新
 * @date ：Created in 2024/09/04 14:44
 */
@Data
public class CallCenterTaskPojo {

    /**
     * 呼叫中心服务地址
     */
    private String serviceHostUrl;

    /**
     * 企业API标识
     */
    private String apiFlag;

    /**
     * 企业编号
     */
    private String enterpriseNum;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 执行时间，周一到周日，按顺序1到7，每天8，一至五9
     */
    private String executionTime;

    /**
     * 外呼时间段，如9:30-12:00,14:00-15:30
     */
    private String executionCallTime;

    /**
     * 重呼次数
     */
    private Integer recall;

    /**
     * 重呼间隔
     */
    private Integer recallMinute;

    /**
     * 是否弹屏 1是 2否
     */
    private Integer isScreen;

    /**
     * 单次批量外呼数量
     */
    private Integer singleCallNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分配的坐席
     */
    private String allocatedSip;

    /**
     * 接听设置 1只允许归属坐席接听 2随机接听
     */
    private Integer answerSettings;

    /**
     * 呼叫对象 1本人 2全部号码
     */
    private Integer callTarget;

    /**
     * 任务标志 1导入 2案件
     */
    private Integer taskFlag;

    /**
     * 客户信息集合
     */
    private List<CallCustomDataPojo> callList;

//--------------------------------------------------------更新任务增加参数-------------------------------------------------

    /**
     * 任务唯一编号uuid集合
     */
    private List<String> taskUuids;

    /**
     * 状态 2执行 4暂停 5撤销
     */
    private Integer status;

//--------------------------------------------------------查询任务状态增加参数----------------------------------------------

    /**
     * 查询参数
     */
    private Map<String, Object> map;

    /**
     * 任务唯一编号uuid（返回参数）
     */
    private String taskUuid;

//--------------------------------------------------------未接通重呼增加参数------------------------------------------------

    /**
     * 客户uuid
     */
    private List<String> customerIds;

//----------------------------------------------------修改坐席状态增加参数--------------------------------------------------

    /**
     * sip坐席账号
     */
    private String sipAccount;

    /**
     * sip状态：1=置闲，2=置忙
     */
    private Integer sipState;
}
