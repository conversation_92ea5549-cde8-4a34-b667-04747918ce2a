package com.zws.call.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zws.call.controller.request.WorkPhoneCustomDetailsRequest;
import com.zws.call.controller.response.WorkCustomDetailsResponse;
import com.zws.call.domain.CallCustomDetails;
import com.zws.call.mapper.CallCustomDetailsMapper;
import com.zws.call.pojo.ExportCustomPojo;
import com.zws.call.service.ICallCustomDetailsService;
import com.zws.cis.domain.UrgeRecord;
import com.zws.common.core.domain.sms.InfoContact;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zws.common.core.utils.PageUtils.startPage;

/**
 * 客户资料导入记录详细信息（预测试外呼）-service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 10:57
 */
@Service
public class CallCustomDetailsServiceImpl implements ICallCustomDetailsService {

    @Resource
    private CallCustomDetailsMapper mapper;

    /**
     * 根据部门id查询员工id
     *
     * @param deptId
     * @return
     */
    @Override
    public List<Integer> selectProgenyDept(Integer deptId, Integer teamId) {
        List<Integer> list = mapper.selectProgenyDept(deptId, teamId);
        if (ObjectUtils.isEmpty(list)) list = new ArrayList<>();
        list.add(deptId);
//        根据部门id集合查询该部门人员id
        return mapper.selectEmployeesIdList(list, teamId);
    }

    /**
     * 客户列表查询参数处理
     *
     * @param customDetails
     * @return
     */
    @Override
    public Map<String, Object> verifyParameters(CallCustomDetails customDetails) {
        customDetails.setTeamId(SecurityUtils.getTeamId().intValue());
        if (SecurityUtils.getAccountType() == null) throw new GlobalException("登录人信息获取错误");
        if (SecurityUtils.getAccountType() == 1) {
            List<Integer> list = new ArrayList<>();
            list.add(SecurityUtils.getUserId().intValue());
//            获取是否是部门负责人
            boolean departmentHead = SecurityUtils.isDepartmentHead();
            if (ObjectUtils.isEmpty(departmentHead)) throw new GlobalException("登录人信息获取错误");
            if (departmentHead) {
                Integer deptId = SecurityUtils.getDeptId();
                if (deptId != null) {
//                    根据部门id查询员工id
                    List<Integer> userIds = selectProgenyDept(deptId, SecurityUtils.getTeamId().intValue());
                    if (ObjectUtils.isEmpty(userIds)) userIds = new ArrayList<>();
                    list.addAll(userIds);
                }
            }
            customDetails.setMemberIdList(list);
        }

        if (!ObjectUtils.isEmpty(customDetails.getCreateTime2())) {
            DateTime dateTime1 = DateUtil.endOfDay(customDetails.getCreateTime2());
            customDetails.setCreateTime2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(customDetails.getNoteTime2())) {
            DateTime dateTime1 = DateUtil.endOfDay(customDetails.getNoteTime2());
            customDetails.setNoteTime2(dateTime1);
        }

        Map<String, Object> map = BeanUtil.beanToMap(customDetails);
        return map;
    }

    /**
     * 客户列表信息查询
     *
     * @param customDetails
     * @return
     */
    @Override
    public List<CallCustomDetails> selectByList(CallCustomDetails customDetails) {
        Map<String, Object> map = verifyParameters(customDetails);
        startPage();
        return mapper.selectByList(map);
    }

    /**
     * 来电弹屏-客户信息查询
     *
     * @param customDetails
     * @return
     */
    @Override
    public List<CallCustomDetails> selectPopOnScreen(CallCustomDetails customDetails) {
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("casePhone", customDetails.getCasePhone());
        map.put("taskUuid", customDetails.getTaskUuid());
        return mapper.selectPopOnScreen(map);
    }

    /**
     * 工作手机来电弹屏-客户信息查询
     *
     * @param workPhoneCustomDetailsRequest
     * @return
     */
    @Override
    public List<WorkCustomDetailsResponse> selectWorkPopOnScreen(WorkPhoneCustomDetailsRequest workPhoneCustomDetailsRequest) {
        workPhoneCustomDetailsRequest.setDecryptKey(FieldEncryptUtil.fieldKey);
        return mapper.selectWorkPopOnScreen(workPhoneCustomDetailsRequest);
    }

    /**
     * 来电弹屏-客户id查询
     *
     * @param customDetails
     * @return
     */
    @Override
    public List<Long> selectPopOnScreenIds(CallCustomDetails customDetails) {
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("casePhone", customDetails.getCasePhone());
        map.put("id", customDetails.getId());
        return mapper.selectPopOnScreenIds(map);
    }

    /**
     * 客户列表信息查询--导出
     *
     * @param customDetails
     * @return
     */
    @Override
    public List<ExportCustomPojo> selectByListExport(CallCustomDetails customDetails) {
        Map<String, Object> map = verifyParameters(customDetails);
        return mapper.selectByListExport(map);
    }

    /**
     * 客户Id查询--删除
     *
     * @param customDetails
     * @return
     */
    @Override
    public List<Long> selectByListDelete(CallCustomDetails customDetails) {
        Map<String, Object> map = verifyParameters(customDetails);
        List<Long> longList = mapper.selectByListDelete(map);
        if (ObjectUtils.isEmpty(longList)) longList = new ArrayList<>();
        return longList;
    }

    /**
     * 根据客户Id删除
     *
     * @param ids
     * @return
     */
    @Override
    public int deleteByIdList(List<Long> ids) {
//        判断客户是否有没完成的任务存在
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("ids", ids);
        Integer integer = mapper.selectCount(map);
        if (integer > 0) throw new GlobalException("选中客户中存在任务未完成的客户信息，无法删除");
        return mapper.deleteByIdList(ids);
    }

    /**
     * 根据客户uuid查询客户对应员工id
     *
     * @param uuid
     * @return
     */
    @Override
    public Integer selectByCustomUuid(String uuid) {
        return mapper.selectByCustomUuid(uuid);
    }

    /**
     * 根据客户uuid查询案件id以及联系人id
     *
     * @param uuid
     * @return
     */
    @Override
    public CallCustomDetails selectCaseIdOrContactsId(String uuid) {
        return mapper.selectCaseIdOrContactsId(uuid);
    }

    /**
     * 根据案件联系人信息id查询案件联系人信息
     *
     * @param id
     * @return
     */
    @Override
    public InfoContact selectInfoContactById(Long id) {
        return mapper.selectInfoContactById(id);
    }

    /**
     * 根据催员id查找催员姓名
     *
     * @param id
     * @return
     */
    @Override
    public String selectOdvName(Integer id) {
        return mapper.selectOdvName(id);
    }

    /**
     * 根据团队id查找团队类型
     *
     * @param id
     * @return
     */
    @Override
    public String selectTeamLevelType(Integer id) {
        return mapper.selectTeamLevelType(id);
    }

    /**
     * 写入催收记录
     *
     * @param urgeRecord
     * @return
     */
    @Override
    public int insertUrgeRecord(UrgeRecord urgeRecord) {
        return mapper.insertUrgeRecord(urgeRecord);
    }

    /**
     * 批量插入客户信息
     *
     * @param customDetails
     * @return
     */
    @Override
    public int insertList(List<CallCustomDetails> customDetails) {
        return mapper.insertList(customDetails);
    }

    /**
     * 根据id修改客户信息
     *
     * @param record
     * @return
     */
    @Override
    public int updateByPrimaryKeySelective(CallCustomDetails record) {
        return mapper.updateByPrimaryKeySelective(record);
    }
}
