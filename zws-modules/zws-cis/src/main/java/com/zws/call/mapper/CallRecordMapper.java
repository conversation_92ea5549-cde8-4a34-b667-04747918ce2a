package com.zws.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.mapper.Mapper;
import com.zws.call.domain.CallRecord;
import com.zws.call.domain.CallSip;
import com.zws.call.pojo.AiCallCenterTaskPojo;
import com.zws.call.pojo.TeamPojo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CallRecordMapper extends BaseMapper<CallRecord> {
    int deleteByPrimaryKey(Long id);

    int insert(CallRecord record);

    int insertSelective(CallRecord record);

    CallRecord selectByPrimaryKey(Long id);

    /**
     * 根据id修改话单-（预测试外呼）
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(CallRecord record);

    int updateByPrimaryKey(CallRecord record);

    List<CallRecord> selectList(Map<String, Object> params);

    /**
     * 根据手机号等条件查询对应案件id
     *
     * @param teamPojo
     * @return
     */
    List<Long> selectCaseIdPhone(TeamPojo teamPojo);

    /**
     * 根据sip账号查询员工以及团队id
     *
     * @param sipNumber
     * @return
     */
    TeamPojo selectByTeamId(String sipNumber);

    /**
     * 根据案件id集合查询借款人
     *
     * @param caseIds
     * @return
     */
    List<String> getClientName(@Param("caseIds") List<Long> caseIds);

    /**
     * SIP账号查询
     *
     * @param sipNumber
     * @return
     */
    CallSip selectBySipNumber(String sipNumber);

    /**
     * 查询登录人的sip坐席信息
     *
     * @param map
     * @return
     */
    CallSip selectSipData(Map<String, Object> map);

    /**
     * 根据callid查找通话记录主键id
     *
     * @param callid
     * @return
     */
    Long selectByCallId(String callid);

    /**
     * 根据id修改sip坐席状态
     *
     * @param callSip
     * @return
     */
    int updateSipState(CallSip callSip);


    Integer selectCountByCallResult(Map<String, Object> map);

    @Select("select count(1) from call_record where del_flag = 0 and team_id = #{teamId} and task_uuid = #{taskUuid}")
    Integer selectCallCount(Map<String, Object> map);

    @MapKey("call_result")
    Map<String, Object> selectCountByCallResultGroupBy(Map<String, Object> map);

    /**
     * 获取话单表的uuid
     * @param now
     * @param taskPojo
     * @return
     */
    List<String> selectTaskUuidList(@Param("now") Date now, AiCallCenterTaskPojo taskPojo,@Param("callroter") String callroter);
}