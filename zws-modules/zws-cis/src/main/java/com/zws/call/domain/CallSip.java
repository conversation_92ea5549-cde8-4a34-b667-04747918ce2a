package com.zws.call.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * SIP坐席
 */
@Data
public class CallSip extends BaseEntity {

    private Long id;
    /**
     * SIP 账号
     */
    @Excel(name = "坐席账号", sort = 1)
    private String sipNumber;
    /**
     * SIP密码
     */
    @Excel(name = "密码", sort = 2)
    private String sipPassword;
    /**
     * 团队id
     */
    private Long teamId;
    /**
     * 催员id
     */
    private Long odvId;
    /**
     * 企业编号
     */
    private String companyNum;
    /**
     * 企业api标识
     */
    private String apiFlag;
    /**
     * 企业名称
     */
    @Excel(name = "入网企业")
    private String companyName;
    /**
     * 绑定状态（0-未绑定，1-已绑定）
     */
    @Excel(name = "绑定状态", readConverterExp = "0=未绑定,1=已绑定", sort = 5)
    private String bindStatus;
    /**
     * 绑定时间
     */
    @Excel(name = "绑定时间", dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 6)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bindTime;

    private String delFlag;

    /**
     * 团队名称
     */
    @Excel(name = "机构名称", sort = 3)
    private String teamName;
    /**
     * 机构类型（0-自营，1-委外）
     */
    private Integer teamType;
    /**
     * 催员名称
     */
    @Excel(name = "姓名", sort = 4)
    private String odvName;

    /**
     * 坐席类别（0-联通坐席，1-普通坐席，2-预测式外呼坐席）
     */
    private Integer seatsType;

    /**
     * sip状态：1=置闲，2=置忙 （预测试外呼坐席）
     */
    private Integer sipState;

    public String getSipPassword() {
        return sipPassword;
    }

    public void setSipPassword(String sipPassword) {
        this.sipPassword = sipPassword;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getOdvName() {
        return odvName;
    }

    public void setOdvName(String odvName) {
        this.odvName = odvName;
    }

    public Integer getSeatsType() {
        return seatsType;
    }

    public void setSeatsType(Integer seatsType) {
        this.seatsType = seatsType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSipNumber() {
        return sipNumber;
    }

    public void setSipNumber(String sipNumber) {
        this.sipNumber = sipNumber == null ? null : sipNumber.trim();
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getOdvId() {
        return odvId;
    }

    public void setOdvId(Long odvId) {
        this.odvId = odvId;
    }

    public String getCompanyNum() {
        return companyNum;
    }

    public void setCompanyNum(String companyNum) {
        this.companyNum = companyNum == null ? null : companyNum.trim();
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    public String getBindStatus() {
        return bindStatus;
    }

    public void setBindStatus(String bindStatus) {
        this.bindStatus = bindStatus == null ? null : bindStatus.trim();
    }

    public Date getBindTime() {
        return bindTime;
    }

    public void setBindTime(Date bindTime) {
        this.bindTime = bindTime;
    }


    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }
}
