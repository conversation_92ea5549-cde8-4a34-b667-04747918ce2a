package com.zws.call.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 团队部门表（实体类）
 */
@Data
public class DeptPojo implements Serializable {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 团队id
     */
    private Integer createId;

    /**
     * 父部门id
     */
    private Integer parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 部门名称
     */
    @Size(max = 30,message = "部门名称不可超过(30)字")
    @NotNull(message = "部门名称不能为空")
    private String deptName;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 部门状态（0:启用,1:停用）
     */
    private Integer status;

    /**
     * 删除标志（0:未删除,1:已删除）
     */
    private Integer deleteLogo;

    /**
     * 创建人
     */
    private String founder;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creationtime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

}
