package com.zws.call.pojo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 预测试外呼通知接收-实体类
 *
 * @author: 马博新
 * @date ：Created in 2024/09/10 16:07
 */
@Data
public class CallNoticePojo implements Serializable {

    /**
     * 事件类型
     */
    @NotBlank(message = "事件类型不能为空")
    private String eventType;

    /**
     * 任务唯一编号-uuid
     */
//    @NotBlank(message = "任务唯一编号不能为空")
    private String taskUuid;

    /**
     * 数据（json）
     */
    private String eventData;

    /**
     * 任务类型
     * 1=预测式外呼，2=智能语音 3=回铃监测
     */
//    @NotNull(message = "任务类型不能为空")
    private Integer taskType;
}
