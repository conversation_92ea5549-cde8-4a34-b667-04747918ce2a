package com.zws.call.service.impl;

import com.zws.call.domain.CallCustomRecord;
import com.zws.call.mapper.CallCustomRecordMapper;
import com.zws.call.pojo.DeptPojo;
import com.zws.call.pojo.EmployeesPojo;
import com.zws.call.pojo.EnterpriseDataPojo;
import com.zws.call.service.ICallCustomRecordService;
import com.zws.common.core.constant.BaseConstant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 客户资料导入记录（预测试外呼）-service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 10:55
 */
@Service
public class CallCustomRecordServiceImpl implements ICallCustomRecordService {

    @Resource
    private CallCustomRecordMapper mapper;


    /**
     * 写入客户导入记录
     *
     * @param record
     * @return
     */
    @Override
    public Long insert(CallCustomRecord record) {
        record.setCreateTime(new Date());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        mapper.insert(record);
        return record.getId();
    }

    /**
     * 根据工号查询该机构员工id
     *
     * @param map
     * @return
     */
    @Override
    public Integer selectEmployeesId(Map<String, Object> map) {
        return mapper.selectEmployeesId(map);
    }

    /**
     * 根据机构id查询机构企业编码
     *
     * @param id
     * @return
     */
    @Override
    public EnterpriseDataPojo selectCompanyNum(Integer id) {
        return mapper.selectCompanyNum(id);
    }

    /**
     * 根据员工id查询坐席账号
     *
     * @param map
     * @return
     */
    @Override
    public String selectSip(Map<String, Object> map) {
        return mapper.selectSip(map);
    }

    /**
     * 根据团队id查询该团队所有部门信息
     *
     * @return
     */
    @Override
    public List<DeptPojo> selectDept(int caeateId) {
        return mapper.selectDept(caeateId);
    }

    /**
     * 根据部门id查询该部门人员信息
     *
     * @param
     * @return
     */
    @Override
    public List<EmployeesPojo> selectEmployeesParentId(int parentId, int createId) {
        return mapper.selectEmployeesParentId(parentId, createId);
    }

    /**
     * 根据parentId查询对应部门信息
     *
     * @param
     * @return
     */
    @Override
    public List<DeptPojo> selectDeptParentId(int parentId) {
        return mapper.selectDeptParentId(parentId);
    }

    /**
     * 查询机构已分配预测试外呼坐席催员id
     *
     * @param teamId
     * @return
     */
    @Override
    public List<Long> selectCallSip(Integer teamId) {
        return mapper.selectCallSip(teamId);
    }
}
