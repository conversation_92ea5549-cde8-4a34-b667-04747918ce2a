package com.zws.call.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 呼叫中心配置
 * <AUTHOR>
 * @date ：Created in 2022/12/1 16:29
 */
@Data
@Configuration
@ConfigurationProperties(prefix  = "call")
public class CallConfig {

    /**
     * 呼叫中心地址
     */
    private String hostUrl;

    /**
     * 呼叫中心-企业API标识
     */
    private String apiFlag;


}
