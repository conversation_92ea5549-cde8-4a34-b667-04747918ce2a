package com.zws.call.mapper;

import com.zws.call.domain.AiCallIntelligenceTask;
import com.zws.call.domain.AiField;
import com.zws.call.domain.CallRecord;
import com.zws.call.pojo.AiCallRecordDataPojo;
import com.zws.call.pojo.CallRecordDataPojo;
import com.zws.call.pojo.SmsContentPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface AiVoiceTaskMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AiCallIntelligenceTask record);

    int insertSelective(AiCallIntelligenceTask record);

    AiCallIntelligenceTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AiCallIntelligenceTask record);

    int updateByPrimaryKeyWithBLOBs(AiCallIntelligenceTask record);

    int updateByPrimaryKey(AiCallIntelligenceTask record);


    /**
     * 查询所有的数据
     * @return
     */
    List<AiCallIntelligenceTask> selectAllList(Map<String, Object> map);

    /**
     * 查询机构中所有任务名称
     * @param teamId
     * @return
     */
    List<String> selectAiTaskNameList(Integer teamId);

    /**
     * 根据uuidList修改
     * @param task
     */
    int updateByUuidList(AiCallIntelligenceTask task);

    /**
     * 根据id查询对应的uuid
     * @param map
     * @return
     */
    List<String> selectTaskUuidList(Map<String, Object> map);

    List<String> selectCallReloadList(Map<String, Object> map);

    /**
     * 条件查询客户id
     * @param objectMap
     * @return
     */
    List<Long> selectRecordId(Map<String, Object> objectMap);

    /**
     * 根据id查询AI语音通知
     * @param id
     * @param teamId
     * @return
     */
    AiCallIntelligenceTask selectById(@Param("id") Long id,@Param("teamId") Integer teamId);

    /**
     * 查询任务总通话时长
     * @param map
     * @return
     */
    Integer selectCallRecordAgentDuration(Map<String, Object> map);

    /**
     * 查询任务对应的话单信息-（AI语音通知）
     *
     * @param map
     * @return
     */
    List<AiCallRecordDataPojo> selectCallRecordList(Map<String, Object> map);

    /**
     * 查询所有模板参数
     * @return
     */
    List<AiField> selectAiFieldList();

    /**
     * 根据通话记录id修改
     * @param callRecord
     */
    void updateByAiCallRecordDataPojo(CallRecord callRecord);

    /**
     * 根据任务名称查询是否重复
     * @param taskName
     * @param teamId
     * @return
     */
    Integer selectByCount(@Param("taskName")String taskName,@Param("teamId") Integer teamId);

    /**
     * 插入短信回复内容
     * @param smsContents
     * @return
     */
    Integer insertSmsContent(SmsContentPojo smsContents);

    /**
     * 根据uuid查询
     * @param callCenterUuid
     * @return
     */
    AiCallIntelligenceTask selectByUuid(String callCenterUuid);

    /**
     * 判断数据是否已经存在数据短信回复内容中
     * @param smsContents
     * @return
     */
    Integer selectByCallIdAndPhoneCount(SmsContentPojo smsContents);

    /**
     * 修改短信回复内容
     * @param smsContents
     * @return
     */
    Integer updateSmsContent(SmsContentPojo smsContents);

    /**
     * 修改短信回复内容
     * @param smsContentPojo
     * @return
     */
    Integer updateSmsContentStatusAndContent(SmsContentPojo smsContentPojo);
}