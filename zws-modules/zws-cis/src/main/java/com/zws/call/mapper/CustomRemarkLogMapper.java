package com.zws.call.mapper;

import com.zws.call.domain.CustomRemarkLog;

public interface CustomRemarkLogMapper {
    int deleteByPrimaryKey(Long id);

    /**
     * 写入备注记录
     *
     * @param record
     * @return
     */
    int insert(CustomRemarkLog record);

    int insertSelective(CustomRemarkLog record);

    CustomRemarkLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CustomRemarkLog record);

    int updateByPrimaryKey(CustomRemarkLog record);
}