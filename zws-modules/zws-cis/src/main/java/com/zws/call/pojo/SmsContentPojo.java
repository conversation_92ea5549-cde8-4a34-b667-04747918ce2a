package com.zws.call.pojo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 短信回复数据
 */
@Data
public class SmsContentPojo {
    /**
     * 回复数据ID
     */
    private Long id;
    /**
     * 回复数据手机号
     */
    private String phone;
    /**
     * 回复数据内容
     */
    private String content;

    /**
     * 特服号
     */
    private String number;

    /**
     * 回复数据时间
     */
    private Date  createTime;


    /**
     * 短信发送状态
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String result;

    /**
     * 呼叫ID
     */
    private String callId;

//-------------------------------------判断条件----------------------------
    /**
     * 当前时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nowTime;

    /**
     * 呼叫类型
     */
    private String callType;


}
