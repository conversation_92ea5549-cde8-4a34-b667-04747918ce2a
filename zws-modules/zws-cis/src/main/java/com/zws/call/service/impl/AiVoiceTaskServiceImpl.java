package com.zws.call.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zws.call.domain.AiCallIntelligenceTask;
import com.zws.call.domain.AiField;
import com.zws.call.domain.CallRecord;
import com.zws.call.mapper.AiVoiceTaskMapper;
import com.zws.call.pojo.AiCallRecordDataPojo;
import com.zws.call.pojo.CallRecordDataPojo;
import com.zws.call.pojo.SmsContentPojo;
import com.zws.call.service.IAiVoiceTaskService;
import com.zws.call.service.ICallCustomDetailsService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.utils.SplitUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.IntelligenceTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;

@Service
public class AiVoiceTaskServiceImpl implements IAiVoiceTaskService {


    @Autowired
    private AiVoiceTaskMapper aiVoiceTaskMapper;
    @Autowired
    private ICallCustomDetailsService customDetailsService;

    /**
     * 查询AI语音通知任务列表
     * @param aiVoiceList
     * @return
     */
    @Override
    public List<AiCallIntelligenceTask> selectAllList(AiCallIntelligenceTask aiVoiceList) {
        Map<String, Object> map = verifyParameters(aiVoiceList);
        //查询出所有的数据
        return aiVoiceTaskMapper.selectAllList(map);
    }

    /**
     * 客户列表查询参数处理
     *
     * @param aiVoiceList
     * @return
     */
    @Override
    public Map<String, Object> verifyParameters(AiCallIntelligenceTask aiVoiceList) {
        aiVoiceList.setTeamId(SecurityUtils.getTeamId().intValue());
        if (SecurityUtils.getAccountType() == null) throw new GlobalException("登录人信息获取错误");
        if (SecurityUtils.getAccountType() == 1) {
            List<Integer> list = new ArrayList<>();
//            获取是否是部门负责人
            boolean departmentHead = SecurityUtils.isDepartmentHead();
            if (ObjectUtils.isEmpty(departmentHead)) throw new GlobalException("登录人信息获取错误");
            if (departmentHead) {
                Integer deptId = SecurityUtils.getDeptId();
                if (deptId != null) {
//                    根据部门id查询员工id
                    List<Integer> userIds = customDetailsService.selectProgenyDept(deptId, SecurityUtils.getTeamId().intValue());
                    if (ObjectUtils.isEmpty(userIds)) userIds = new ArrayList<>();
                    list.addAll(userIds);
                }
            }else {
                list.add(SecurityUtils.getUserId().intValue());
            }
            aiVoiceList.setMemberIdList(list);
        }

        if (!ObjectUtils.isEmpty(aiVoiceList.getCreateTime2())) {
            DateTime dateTime1 = DateUtil.endOfDay(aiVoiceList.getCreateTime2());
            aiVoiceList.setCreateTime2(dateTime1);
        }
        aiVoiceList.setTaskNameList(SplitUtils.splitCharacterString(aiVoiceList.getTaskName()));
        aiVoiceList.setStatusList(SplitUtils.splitCharacterInteger(aiVoiceList.getStatusStr()));
        aiVoiceList.setDialogueTemplateNameList(SplitUtils.splitCharacterString(aiVoiceList.getDialogueTemplateName()));

        if (!ObjectUtils.isEmpty(aiVoiceList.getMemberIdList())) {
//            根据条件查询对应的客户id带入查询任务条件
            Map<String, Object> objectMap = new HashMap<>();
            objectMap.put("teamId", aiVoiceList.getTeamId());
            objectMap.put("memberIdList", aiVoiceList.getMemberIdList());
            PageUtils.clearPage();
            List<Long> longList = aiVoiceTaskMapper.selectRecordId(objectMap);
            aiVoiceList.setCustomReloadIdList(longList);
        }
        PageUtils.startPage();
        Map<String, Object> map = BeanUtil.beanToMap(aiVoiceList);
        return map;
    }

    /**
     * 根据ID修改状态
     * @param aiVoiceTask
     * @return
     */
    @Override
    public Integer updateStatusById(AiCallIntelligenceTask aiVoiceTask) {
        //return aiCallIntelligenceTaskMapper.updateByPrimaryKeySelective(aiVoiceTask);
        return null;
    }

    /**
     * 查询机构中所有的任务名称
     * @param teamId
     * @return
     */
    @Override
    public List<String> selectAiTaskNameList(Integer teamId) {
        return aiVoiceTaskMapper.selectAiTaskNameList(teamId);
    }

    /**
     * 根据任务UuidList修改
     *
     * @param task
     */
    @Override
    public int updateByUuidList(AiCallIntelligenceTask task) {
        return aiVoiceTaskMapper.updateByUuidList(task);
    }

    /**
     * AI语音通知添加数据
     * @param task
     * @return
     */
    @Override
    public Long insert(AiCallIntelligenceTask task) {
        aiVoiceTaskMapper.insert(task);
        return task.getId();
    }


    /**
     * 根据主键id查询数据对于的uuid
     *
     * @param map
     * @return
     */
    @Override
    public List<String> selectTaskUuidList(Map<String, Object> map) {
        return aiVoiceTaskMapper.selectTaskUuidList(map);
    }

    /**
     * 根据主键id修改
     *
     * @param aiTask
     */
    @Override
    public void updateById(AiCallIntelligenceTask aiTask) {
        aiVoiceTaskMapper.updateByPrimaryKeySelective(aiTask);
    }

    /**
     * 根据主键id查询数据结果报告
     *
     * @param id
     * @param teamId
     * @return
     */
    @Override
    public AiCallIntelligenceTask selectById(Long id, Integer teamId) {
        return aiVoiceTaskMapper.selectById(id, teamId);
    }

    /**
     * 查询已接听电话的通话时长
     *
     * @param map
     * @return
     */
    @Override
    public Integer selectCallRecordAgentDuration(Map<String, Object> map) {
        return aiVoiceTaskMapper.selectCallRecordAgentDuration(map);
    }

    /**
     * 查询任务对应的话单信息-（AI语音通知）
     *
     * @param map
     * @return
     */
    @Override
    public List<AiCallRecordDataPojo> selectCallRecordList(Map<String, Object> map) {
        return aiVoiceTaskMapper.selectCallRecordList(map);
    }

    /**
     * 查询所有模板参数
     * @return
     */
    @Override
    public List<AiField> selectAiFieldList() {
        return aiVoiceTaskMapper.selectAiFieldList();
    }

    /**
     * 删除通话记录
     *
     * @param id
     */
    @Override
    public void deleteAiTask(Long id) {
        CallRecord callRecord = new CallRecord();
        callRecord.setId(id);
        callRecord.setDelFlag(BaseConstant.DelFlag_Delete);
        this.updateByAiCallRecordDataPojo(callRecord);
    }

    /**
     * 根据通话记录id修改
     *
     * @param callRecord
     */
    @Override
    public void updateByAiCallRecordDataPojo(CallRecord callRecord) {
        aiVoiceTaskMapper.updateByAiCallRecordDataPojo(callRecord);
    }

    /**
     * 查询任务名称是否重复
     *
     * @param taskName
     * @param teamId
     * @return
     */
    @Override
    public Integer selectByCount(String taskName, Integer teamId) {
        return aiVoiceTaskMapper.selectByCount(taskName, teamId);
    }

    /**
     * 批量插入短信回复内容
     *
     * @param smsContents
     * @return
     */
    @Override
    public Integer insertSmsContent(SmsContentPojo smsContents) {
        return aiVoiceTaskMapper.insertSmsContent(smsContents);
    }

    /**
     * 根据uuid查询
     *
     * @param callCenterUuid
     */
    @Override
    public AiCallIntelligenceTask selectByUuid(String callCenterUuid) {
        return aiVoiceTaskMapper.selectByUuid(callCenterUuid);

    }

    /**
     * 判断数据是否已经存在数据短信回复内容中
     *
     * @param smsContents
     * @return
     */
    @Override
    public Integer selectByCallIdAndPhoneCount(SmsContentPojo smsContents) {
        return aiVoiceTaskMapper.selectByCallIdAndPhoneCount(smsContents);
    }

    /**
     * 更新短信回复内容
     *
     * @param smsContents
     */
    @Override
    public Integer updateSmsContent(SmsContentPojo smsContents) {
        return aiVoiceTaskMapper.updateSmsContent(smsContents);
    }

    /**
     * 更新短信回复内容
     *
     * @param smsContentPojo
     * @return
     */
    @Override
    public Integer updateSmsContentStatusAndContent(SmsContentPojo smsContentPojo) {
        return aiVoiceTaskMapper.updateSmsContentStatusAndContent(smsContentPojo);
    }
}
