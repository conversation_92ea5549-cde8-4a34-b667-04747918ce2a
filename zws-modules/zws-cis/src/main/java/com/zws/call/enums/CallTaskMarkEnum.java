package com.zws.call.enums;

/**
 * 预测试外呼任务接通标识：0=待拨打，1=已接听，2=未接通，3=漏接
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 10:57
 */
public enum CallTaskMarkEnum {

    /**
     * 0-待拨打
     */
    TO_BE_DIALED(0, "待拨打"),
    /**
     * 1-已接听
     */
    CONNECTED(1, "已接听"),
    /**
     * 2-未接通
     */
    NOT_CONNECTED(2, "未接通"),
    /**
     * 3-漏接
     */
    FUMBLE(3, "漏接"),
    /**
     * 100-未接听
     */
    NOT_ANSWER(100, "未接听"),
    ;


    private final String info;
    private final Integer code;

    CallTaskMarkEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }


    public String getInfo() {
        return info;
    }

    public Integer getCode() {
        return code;
    }

    public static CallTaskMarkEnum valueOfCode(Integer code) {
        CallTaskMarkEnum[] enums = CallTaskMarkEnum.values();
        for (CallTaskMarkEnum temp : enums) {
            if (temp.code.equals(code)) {
                return temp;
            }
        }
        return null;
    }
}
