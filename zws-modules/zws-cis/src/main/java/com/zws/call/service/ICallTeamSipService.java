package com.zws.call.service;

import com.zws.call.domain.CallSip;
import com.zws.call.pojo.TeamPojo;
import com.zws.common.core.domain.sms.ThreadEntityPojo;

/**
 * 呼叫-坐席
 *
 * <AUTHOR>
 * @date ：Created in 2024/10/9 11:59
 */
public interface ICallTeamSipService {

    /**
     * 同步 呼叫中心 SIP数据--（Java）
     *
     * @param entityPojo
     */
    void dataSyncJava(ThreadEntityPojo entityPojo);

    /**
     * 工作手机数据同步
     */
    void workPhoneDataSync(ThreadEntityPojo entityPojo);

    /**
     * 根据SIP账号删除
     *
     * @param sipNumber
     */
    void deleteBySipNumber(String sipNumber);

    /**
     * 入网企业编号获取团队
     *
     * @param companyNum
     * @return
     */
    TeamPojo getByCompanyNum(String companyNum);

    /**
     * 根据SIP账号更新
     *
     * @param callSip
     */
    void updateBySipNumber(CallSip callSip);
}
