package com.zws.call.pojo;

import lombok.Data;

/**
 * 编辑名称
 *
 * @Author：liuxifeng
 * @Date：2024/5/29 11:34
 * @Describe：编辑描述
 */
@Data
public class CallbackPojo {


    /**
     * 通话双方摘机的时间 answerStart
     */
    private Long answerStamp;
    /**
     * 通话双方挂机的时间 answerEnd
     */
    private Long endStamp;

    /**
     * 通话的唯一编码
     */
    private String callid;
//    /**
//     * 是否接通
//     */
//    private String answer;
    /**
     * 被叫号码（客户电话）
     */
    private String callto;
    /**
     * 主叫电话号码（员工SIP号码）
     */
    private String callfrom;
    /**
     * 客户ID,通过点击外呼API传过来的
     * 联系人id
     * (预测试外呼客户uuid)
     */
    private String customerId;
    /**
     * 通话的录音文件名，文件名格式为：年月日/called+主叫号码+被叫号码
     */
    private String recording;
    /**
     * 案件关联ID, ,通过点击外呼API传过来的
     * 格式:  账号类型:团队id:催员id   (任意类型没有值时用字符串的null代替)
     */
    private String relationID;
    /**
     * 案件ID,通过点击外呼API传过来的
     * 案件id
     * 客户id
     */
    private String uID;
    /**
     * 案件ID,通过点击外呼API传过来的
     * 案件id
     * 客户id
     */
    private String UID;
    /**
     * 公司编码
     */
    private String enterpriseNum;
    /**
     * 企业名称
     */
    private String enterpriseName;
    /**
     * 通话时长
     */
    private Integer extDuration;
    /**
     * 记录大小
     */
    private Integer recordSize;
    /**
     * 通话开始时间 时间戳(呼叫开始时间,没有接听时间)
     */
    private Long startStamp;
    /**
     * 透传号码
     */
    private String trunkNumber;
    /**
     * 呼入/呼出
     */
    private String type;

    /**
     * 呼叫类型
     * inbound=呼入
     * callOutManual=手动呼出
     * newauto=预测式外呼
     */
    private String callroter;

    /**
     * 服务器地址（录音文件路径 拼接前缀）
     */
    private String recordHost;
    /**
     * 挂机原因
     */
    private String hangupCause;
    /**
     * 中继名称
     */
    private String trunkName;
    /**
     * 外呼前缀
     */
    private String callPrefix;
//    /**
//     * Sip号码
//     */
//    private String sipNumber;

    /**
     * 预测试外呼任务uuid
     */
    private String taskUuid;

    /**
     * 预测试外呼任务接通标识：0=待拨打，1=已接听，2=未接通，3=漏接
     */
    private Integer connectFlag;

    /**
     * 预测试外呼sip账号
     */
    private String sip;

    public String getuID() {
        return uID;
    }
}


