package com.zws.call.service;

import com.zws.call.domain.AiCallIntelligenceTask;
import com.zws.call.domain.AiField;
import com.zws.call.domain.CallRecord;
import com.zws.call.pojo.AiCallRecordDataPojo;
import com.zws.call.pojo.CallRecordDataPojo;
import com.zws.call.pojo.SmsContentPojo;
import com.zws.system.api.domain.IntelligenceTask;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface IAiVoiceTaskService {


    /**
     * 根据条件查询
     * @param aiVoiceList
     * @return
     */
    public List<AiCallIntelligenceTask> selectAllList(AiCallIntelligenceTask aiVoiceList);

    /**
     * 客户列表查询参数处理
     *
     * @param aiVoiceList
     * @return
     */
    Map<String, Object> verifyParameters(AiCallIntelligenceTask aiVoiceList);


    public Integer updateStatusById(AiCallIntelligenceTask aiVoiceTask);

    List<String> selectAiTaskNameList(Integer teamId);

    /**
     * 根据任务UuidList修改
     * @param task
     */
    int updateByUuidList(AiCallIntelligenceTask task);

    /**
     * 保存AI语音通知任务
     * @param task
     * @return
     */
    Long insert(AiCallIntelligenceTask task);

    /**
     * 根据主键id查询数据对于的uuid
     * @param map
     * @return
     */
    List<String> selectTaskUuidList(Map<String, Object> map);

    /**
     * 根据主键id修改
     * @param aiTask
     */
    void updateById(AiCallIntelligenceTask aiTask);


    /**
     * 根据主键id查询数据结果报告
     * @param id
     * @param teamId
     * @return
     */
    AiCallIntelligenceTask selectById(Long id, Integer teamId);

    /**
     * 查询已接听电话的通话时长
     * @param hashMap
     * @return
     */
    Integer selectCallRecordAgentDuration(Map<String, Object> hashMap);

    /**
     * 查询任务对应的话单信息-（AI语音通知）
     *
     * @param map
     * @return
     */
    List<AiCallRecordDataPojo> selectCallRecordList(Map<String, Object> map);

    /**
     * 查询所有模板参数
     * @param
     * @return
     */
    List<AiField> selectAiFieldList();

    /**
     * 删除通话记录
     * @param id
     */
    void deleteAiTask(Long id);

    /**
     * 根据通话记录id修改
     * @param callRecord
     */
    void updateByAiCallRecordDataPojo(CallRecord callRecord);

    /**
     * 查询任务名称是否重复
     * @param taskName
     * @param teamId
     * @return
     */
    Integer selectByCount(String taskName, Integer teamId);


    /**
     * 批量插入短信回复内容
     * @param smsContents
     * @return
     */
    Integer insertSmsContent(SmsContentPojo smsContents);

    /**
     * 根据uuid查询
     * @param taskUuid
     */
    AiCallIntelligenceTask selectByUuid(String taskUuid);

    /**
     * 判断数据是否已经存在数据短信回复内容中
     * @param smsContents
     * @return
     */
    Integer selectByCallIdAndPhoneCount(SmsContentPojo smsContents);

    /**
     * 更新短信回复内容
     * @param smsContents
     */
    Integer updateSmsContent(SmsContentPojo smsContents);

    /**
     * 更新短信回复内容
     * @param smsContentPojo
     * @return
     */
    Integer updateSmsContentStatusAndContent(SmsContentPojo smsContentPojo);
}
