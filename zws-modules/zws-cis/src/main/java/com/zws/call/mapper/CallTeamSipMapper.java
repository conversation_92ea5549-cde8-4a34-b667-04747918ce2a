package com.zws.call.mapper;


import com.zws.call.domain.CallSip;
import com.zws.call.pojo.TeamPojo;

import java.util.List;

public interface CallTeamSipMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CallSip record);

    int insertSelective(CallSip record);

    CallSip selectByPrimaryKey(Long id);

    /**
     * SIP账号查询
     *
     * @param sipNumber
     * @return
     */
    CallSip selectBySipNumber(String sipNumber);

    /**
     * 查询所有机构的入网企业编号
     *
     * @return
     */
    List<String> selectTeamNumber();

    int updateByPrimaryKeySelective(CallSip record);

    int updateByPrimaryKey(CallSip record);

    /**
     * 根据sip账号更新
     *
     * @param record
     * @return
     */
    int updateBySipNumberSelective(CallSip record);

    /**
     * 查询该团队所有的SIP坐席
     *
     * @return
     */
    List<CallSip> selectByTeamId(Integer teamId);

    int updateById(Long id);

    /**
     * 根据sip账号修改员工sip信息为空
     *
     * @param sipNumber
     * @return
     */
    int updateEmployees(String sipNumber);

    /**
     * 根据入网企业编号获取团队
     *
     * @param companyNum
     * @return
     */
    TeamPojo selectByCompanyNum(String companyNum);

    /**
     * 根据sip账号查询已分配给员工的团队id
     *
     * @param sipNumber
     * @return
     */
    Long selectEmployees(String sipNumber);
}
