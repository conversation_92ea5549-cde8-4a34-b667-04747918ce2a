package com.zws.call.pojo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class TreeTypePojo {

    /**
     * id
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 员工sip坐席账号
     */
    private String sipAccountNumber;

    /**
     * 工号
     */
    private Integer employeesWorking;

    /**
     * 字列表集合
     */
    private List<TreeTypePojo> children;

    public List<TreeTypePojo> getChildren() {
        return children == null ? new ArrayList<>() : children;
    }

    public void setChildren(List<TreeTypePojo> children) {
        this.children = children;
    }
}
