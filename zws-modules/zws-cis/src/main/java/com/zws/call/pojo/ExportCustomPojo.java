package com.zws.call.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 导出客户信息（预测试外呼功能）-实体类
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 10:57
 */
@Data
public class ExportCustomPojo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 文件名称
     */
    @Excel(name = "文件名称", sort = 1)
    private String originalFileName;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话", sort = 2)
    private String casePhone;

    /**
     * 员工姓名
     */
    private String memberName;

    /**
     * 员工工号
     */
    private Integer userNo;

    /**
     * 员工工号+姓名
     */
    @Excel(name = "所属坐席", sort = 3)
    private String employeeIdAndName;

    /**
     * 客户名称
     */
    @Excel(name = "姓名", sort = 4)
    private String caseName;

    /**
     * 产品名称
     */
    @Excel(name = "产品名称", sort = 5)
    private String productName;

    /**
     * 委托金额
     */
    @Excel(name = "债权总金额", sort = 6, bigDecimalFormatterSwitch = 1)
    private BigDecimal entrustMoney;

    /**
     * 剩余应还债权金额
     */
    @Excel(name = "剩余应还债权金额", sort = 7, bigDecimalFormatterSwitch = 1)
    private BigDecimal remainingDueMoney;

    /**
     * 剩余本金
     */
    @Excel(name = "剩余应还本金", sort = 8, bigDecimalFormatterSwitch = 1)
    private BigDecimal remainMoney;

    /**
     * 贷款时间
     */
    @Excel(name = "贷款时间", dateFormat = "yyyy-MM-dd", sort = 9)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date loanTime;

    /**
     * 逾期时间
     */
    @Excel(name = "逾期日期", dateFormat = "yyyy-MM-dd", sort = 10)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date overTime;

    /**
     * 备注
     */
    @Excel(name = "备注", sort = 11)
    private String remark;

    /**
     * 备注时间
     */
    @Excel(name = "备注时间", dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 12)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date noteTime;

    /**
     * 备注人
     */
    @Excel(name = "备注人", sort = 13)
    private String noteBy;

    /**
     * 创建时间
     */
    @Excel(name = "导入时间", dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 14)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 备注人id
     */
    private Integer noteById;

    /**
     * 备注人类型（0-主账号;1-员工账号;2-资产端账号）
     */
    private Integer noteByType;

//    public String getNoteBy() {
//        if (noteByType != null) {
//            if (noteByType == 0) noteBy = cname;
//            else if (noteByType == 1) noteBy = employeeName;
//        }
//        return noteBy;
//    }
//
//    public String getEmployeeIdAndName() {
//        if (!ObjectUtils.isEmpty(memberName) && userNo != null) {
//            employeeIdAndName = userNo + "(" + memberName + ")";
//        }
//        return employeeIdAndName;
//    }

//--------------------------------------------------备注人姓名参数处理------------------------------------------------------

    /**
     * 批次名称
     */
    private String name;

    /**
     * 员工名称
     */
    private String employeeName;

    /**
     * 机构名称
     */
    private String cname;
}