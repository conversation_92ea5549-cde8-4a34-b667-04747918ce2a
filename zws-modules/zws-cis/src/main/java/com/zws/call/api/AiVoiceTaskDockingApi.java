package com.zws.call.api;


import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.zws.call.pojo.*;
import com.zws.common.core.callcenter.pojo.AjaxRequesPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * AI 语音通知接口对接呼叫中心接口Api
 */
@Component
@Slf4j
public class AiVoiceTaskDockingApi {

    /**
     * JAVA呼叫中心 AI语音通知-获取模板
     */
    private final String aiVoiceNotificationGetTemplate ="/call/cti/getIntelligentVoiceTplList";

    /**
     * JAVA呼叫中心 AI语音通知-更新任务
     */
    private final String aiVoiceNotificationUpdateTask ="/call/cti/setIntelligentTaskStatus";

    /**
     * JAVA呼叫中心 AI语音通知-添加任务
     */
    private final String aiVoiceNotificationSubmissionTask ="/call/cti/addIntelligentTask";

    /**
     * JAVA呼叫中心 AI语音通知-获取短信回复内容
     */
    private final String aiVoiceNotificationSmsContentTask ="/call/cti/getSmsContentList";


    /**
     * AI语音通知-获取模板
     * @param param
     * @return
     */
    public List<AiVoiceTplListPojo> getAiVoiceTplList(AiCallCenterTaskPojo param) {
        if (StringUtils.isEmpty(param.getServiceHostUrl())) {
            //未设置呼叫中心
            throw new ServiceException("未配置呼叫中心地址，无法获取");
        }
        String jsonStr = JSONUtil.toJsonStr(param);
        log.info("AI语音通知-获取模板，请求参数:{}", jsonStr);
        log.info("AI语音通知-获取模板，请求路径:{}", param.getServiceHostUrl() + aiVoiceNotificationGetTemplate);
        String result = HttpUtil.post(param.getServiceHostUrl() + aiVoiceNotificationGetTemplate, jsonStr);
        log.info("AI语音通知-获取模板，响应参数:{}", result);
        AjaxRequesPojo ajaxRequesPojo = JSONUtil.toBean(result, AjaxRequesPojo.class);
        if (!ObjectUtils.isEmpty(ajaxRequesPojo)) {
            if (ajaxRequesPojo.getCode().equals("200")) {
                //把获取的数据存入到List<AiVoiceTplListPojo>
                List<AiVoiceTplListPojo> returnPojo = JSONUtil.toList(ajaxRequesPojo.getData(), AiVoiceTplListPojo.class);
                //AiVoiceTplListPojo aiVoiceTplListPojo = JSONUtil.toBean(ajaxRequesPojo.getData(), AiVoiceTplListPojo.class);
                if (ObjectUtils.isEmpty(returnPojo)) throw new GlobalException("呼叫中心返回信息为空");
                return returnPojo;
            } else {
                throw new ServiceException("呼叫中心请求错误:" + ajaxRequesPojo.getMsg());
            }
        } else {
            log.error("呼叫中心-AI语音通知-获取模板请求错误：返回为空");
            throw new ServiceException("呼叫中心请求错误:返回为空");
        }
    }

    /**
     * AI语音通知-更新任务（执行/暂停/撤销）
     * @param param
     */
    public void updateCallTask(AiCallCenterTaskPojo param) {
        if (StringUtils.isEmpty(param.getServiceHostUrl())) {
            //未设置呼叫中心
            throw new ServiceException("未配置呼叫中心地址，无法获取");
        }
        String jsonStr = JSONUtil.toJsonStr(param);
        log.info("AI语音通知-更新任务，请求参数:{}", jsonStr);
        log.info("AI语音通知-更新任务，请求路径:{}", param.getServiceHostUrl() + aiVoiceNotificationUpdateTask);
        String result = HttpUtil.post(param.getServiceHostUrl() + aiVoiceNotificationUpdateTask, jsonStr);
        log.info("AI语音通知-更新任务，响应参数:{}", result);
        AjaxRequesPojo ajaxRequesPojo = JSONUtil.toBean(result, AjaxRequesPojo.class);
        if (!ObjectUtils.isEmpty(ajaxRequesPojo)) {
            if (ajaxRequesPojo.getCode().equals("200")) {
                log.info("AI语音通知-更新任务请求成功" + ajaxRequesPojo.getMsg());
            } else {
                throw new ServiceException("呼叫中心请求错误:" + ajaxRequesPojo.getMsg());
            }
        } else {
            log.error("呼叫中心-AI语音通知-更新任务请求错误：返回为空");
            throw new ServiceException("呼叫中心请求错误:返回为空");
        }
    }


    /**
     * AI语音通知-添加任务
     *
     * @param param
     */
    public CallTaskReturnPojo submitCallTask(AiCallCenterTaskPojo param) {
        if (StringUtils.isEmpty(param.getServiceHostUrl())) {
            //未设置呼叫中心
            throw new ServiceException("未配置呼叫中心地址，无法获取");
        }
        String jsonStr = JSONUtil.toJsonStr(param);
        log.info("AI语音通知-提交任务，请求参数:{}", jsonStr);
        log.info("AI语音通知-提交任务，请求路径:{}", param.getServiceHostUrl() + aiVoiceNotificationSubmissionTask);
        String result = HttpUtil.post(param.getServiceHostUrl() + aiVoiceNotificationSubmissionTask, jsonStr);
        log.info("AI语音通知-提交任务，响应参数:{}", result);
        AjaxRequesPojo ajaxRequesPojo = JSONUtil.toBean(result, AjaxRequesPojo.class);
        if (!ObjectUtils.isEmpty(ajaxRequesPojo)) {
            if (ajaxRequesPojo.getCode().equals("200")) {
                CallTaskReturnPojo returnPojo = JSONUtil.toBean(ajaxRequesPojo.getData(), CallTaskReturnPojo.class);
                if (ObjectUtils.isEmpty(returnPojo)) throw new GlobalException("呼叫中心返回信息为空");
                if (StringUtils.isEmpty(returnPojo.getTaskUuid())) throw new GlobalException("任务uuid返回为空");
                return returnPojo;
            } else {
                throw new ServiceException("呼叫中心请求错误:" + ajaxRequesPojo.getMsg());
            }
        } else {
            log.error("呼叫中心-AI语音通知-提交任务请求错误：返回为空");
            throw new ServiceException("呼叫中心请求错误:返回为空");
        }
    }


    /**
     * 获取短信回复内容
     */
    public List<SmsContentPojo> selectSmsContentTask(AiCallCenterTaskPojo param) {
        if (StringUtils.isEmpty(param.getServiceHostUrl())) {
            //未设置呼叫中心
            throw new ServiceException("未配置呼叫中心地址，无法获取");
        }
        String jsonStr = JSONUtil.toJsonStr(param);
        log.info("AI语音通知-提交任务，请求参数:{}", jsonStr);
        log.info("AI语音通知-短信回复数据，请求路径:{}", param.getServiceHostUrl() + aiVoiceNotificationSmsContentTask);
        String result = HttpUtil.post(param.getServiceHostUrl() + aiVoiceNotificationSmsContentTask , jsonStr);
        log.info("AI语音通知-短信回复数据，响应参数:{}", result);
        AjaxRequesPojo ajaxRequesPojo = JSONUtil.toBean(result, AjaxRequesPojo.class);
        if (!ObjectUtils.isEmpty(ajaxRequesPojo)) {
            List<SmsContentPojo> smsContentPojo = JSONUtil.toList(ajaxRequesPojo.getData(), SmsContentPojo.class);
            return smsContentPojo;

        } else {
            log.error("呼叫中心-AI语音通知-短信回复数据：返回为空");
            throw new ServiceException("展示没有新的短信回复内容");
        }

    }
}
