package com.zws.call.mapper;

import com.zws.call.controller.request.WorkPhoneCustomDetailsRequest;
import com.zws.call.controller.response.WorkCustomDetailsResponse;
import com.zws.call.domain.CallCustomDetails;
import com.zws.call.pojo.ExportCustomPojo;
import com.zws.cis.domain.UrgeRecord;
import com.zws.common.core.domain.sms.InfoContact;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 客户资料导入记录详细信息（预测试外呼）-Dao层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 10:57
 */
public interface CallCustomDetailsMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CallCustomDetails record);

    /**
     * 批量插入客户信息
     *
     * @param customDetails
     * @return
     */
    int insertList(@Param("customDetails") List<CallCustomDetails> customDetails);

    int insertSelective(CallCustomDetails record);

    CallCustomDetails selectByPrimaryKey(Long id);

    /**
     * 客户列表信息查询
     *
     * @param map
     * @return
     */
    List<CallCustomDetails> selectByList(Map<String, Object> map);

    /**
     * 来电弹屏-客户信息查询
     *
     * @param map
     * @return
     */
    List<CallCustomDetails> selectPopOnScreen(Map<String, Object> map);

    /**
     * 来电弹屏-客户信息查询
     *
     * @param workPhoneCustomDetailsRequestt
     * @return
     */
    List<WorkCustomDetailsResponse> selectWorkPopOnScreen(WorkPhoneCustomDetailsRequest workPhoneCustomDetailsRequestt);

    /**
     * 来电弹屏-客户信息查询
     *
     * @param map
     * @return
     */
    List<Long> selectPopOnScreenIds(Map<String, Object> map);

    /**
     * 客户列表信息查询--导出
     *
     * @param map
     * @return
     */
    List<ExportCustomPojo> selectByListExport(Map<String, Object> map);

    /**
     * 客户Id查询--删除
     *
     * @param map
     * @return
     */
    List<Long> selectByListDelete(Map<String, Object> map);

    /**
     * 查询后带部门ID
     *
     * @param deptId 部门ID
     * @param teamId 团队ID
     * @return
     */
    List<Integer> selectProgenyDept(@Param("deptId") Integer deptId, @Param("teamId") Integer teamId);

    /**
     * 根据部门id查询员工id
     *
     * @param deptIds 部门ID集合
     * @param teamId  团队ID
     * @return
     */
    List<Integer> selectEmployeesIdList(@Param("deptIds") List<Integer> deptIds, @Param("teamId") Integer teamId);

    /**
     * 客户id集合统计是否有未完成的任务
     *
     * @param map
     * @return
     */
    Integer selectCount(Map<String, Object> map);

    /**
     * 根据客户uuid查询客户对应员工id
     *
     * @param uuid
     * @return
     */
    Integer selectByCustomUuid(String uuid);

    /**
     * 根据客户uuid查询案件id以及联系人id
     *
     * @param uuid
     * @return
     */
    CallCustomDetails selectCaseIdOrContactsId(String uuid);

    /**
     * 根据案件联系人信息id查询案件联系人信息
     *
     * @param id
     * @return
     */
    InfoContact selectInfoContactById(Long id);

    /**
     * 根据催员id查找催员姓名
     *
     * @param id
     * @return
     */
    String selectOdvName(Integer id);

    /**
     * 根据团队id查找团队类型
     *
     * @param id
     * @return
     */
    String selectTeamLevelType(Integer id);

    /**
     * 写入催收记录
     *
     * @param urgeRecord
     * @return
     */
    int insertUrgeRecord(UrgeRecord urgeRecord);

    /**
     * 根据id修改客户信息
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(CallCustomDetails record);

    int updateByPrimaryKey(CallCustomDetails record);

    /**
     * 根据客户Id删除
     *
     * @param ids
     * @return
     */
    int deleteByIdList(@Param("ids") List<Long> ids);
}