package com.zws.call.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 客户资料导入记录表（语音通知功能）-实体类
 */
@Data
public class AiCallCustomRecord {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 机构id
     */
    private Integer teamId;

    /**
     * 批次名称
     */
    private String name;

    /**
     * 文件路径
     */
    private String fileUrl;

    /**
     * 文件原名称
     */
    private String originalFileName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人id
     */
    private Integer createId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人类型（0-主账号;1-员工账号;2-资产端账号）
     */
    private Integer createType;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 客户类型（1-导入客户，2-案件生成）
     */
    private Integer customerType;
}