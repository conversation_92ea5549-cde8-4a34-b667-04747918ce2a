package com.zws.call.agservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.zws.call.api.CallingTaskDockingApi;
import com.zws.call.config.CallConfig;
import com.zws.call.constant.EventTaskConstants;
import com.zws.call.domain.AiCallIntelligenceTask;
import com.zws.call.domain.CallCustomDetails;
import com.zws.call.domain.CallRecord;
import com.zws.call.domain.CallSip;
import com.zws.call.pojo.*;
import com.zws.call.service.IAiVoiceTaskService;
import com.zws.call.service.ICallCustomDetailsService;
import com.zws.call.service.ICallRecordService;
import com.zws.call.service.IIntelligenceTaskService;
import com.zws.cis.domain.UrgeRecord;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.domain.sms.InfoContact;
import com.zws.common.core.enums.SendIpEnum;
import com.zws.common.core.enums.approval.WebSideEnum;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.StringUtils;
import com.zws.system.api.RemoteMessageService;
import com.zws.system.api.domain.IntelligenceTask;
import com.zws.system.api.pojo.CallMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;

/**
 * @author: 马博新
 * @date ：Created in 2024/09/09 17:22
 */
@Slf4j
@Component
public class AgCallBackService {

    @Autowired
    private ICallRecordService callRecordService;
    @Autowired
    private IIntelligenceTaskService intelligenceTaskService;
    @Autowired
    private CallingTaskDockingApi callingTaskDockingApi;
    @Autowired
    private CallConfig callConfig;
    @Autowired
    private RemoteMessageService remoteMessageService;
    @Autowired
    private ICallCustomDetailsService customDetailsService;
    @Autowired
    private IAiVoiceTaskService aiVoiceTaskService;

    /**
     * 预测试外呼话单-回调
     *
     * @param param
     */
    public void callback(CallbackPojo param) {
        if (StringUtils.isEmpty(param.getCallid())) throw new GlobalException("通话唯一编码不能为空");
        CallRecord callRecord = new CallRecord();

        BeanUtil.copyProperties(param, callRecord);
        callRecord.setCustomUuid(param.getCustomerId());
        callRecord.setCompanyNum(param.getEnterpriseNum());
        callRecord.setCompanyName(param.getEnterpriseName());
        callRecord.setAgentDuration(param.getExtDuration());
        callRecord.setCallroter(param.getCallroter());
        callRecord.setNumber(param.getTrunkNumber());
        if (!StringUtils.isEmpty(param.getuID()))
            callRecord.setCaseId(Long.parseLong(param.getuID()));

        if (!ObjectUtils.isEmpty(param.getExtDuration())) {
            String answer = param.getExtDuration() > 0 ? "y" : "n";
            callRecord.setAnswer(answer);
        }
        callRecord.setServiceHost(param.getRecordHost());
        callRecord.setCallFrom(param.getCallfrom());
        callRecord.setCallTo(param.getCallto());
        callRecord.setCallbackTime(new Date());

        if (!ObjectUtils.isEmpty(param.getStartStamp())) {
            Date callTime = new Date(param.getStartStamp());
            String formatCall = DateUtil.format(callTime, "yyyy-MM-dd HH:mm:ss");
            Date dateCall = DateUtil.parse(formatCall, "yyyy-MM-dd HH:mm:ss");
            callRecord.setCallTime(dateCall);
        }

        Date answerStart = null, answerEnd = null;
        if (param.getAnswerStamp() != null && param.getAnswerStamp() > 0) {
            Date answerStartDate = new Date(param.getAnswerStamp());
            String formatStart = DateUtil.format(answerStartDate, "yyyy-MM-dd HH:mm:ss");
            answerStart = DateUtil.parse(formatStart, "yyyy-MM-dd HH:mm:ss");
        }
        if (param.getEndStamp() != null && param.getEndStamp() > 0) {
            Date answerEndDate = new Date(param.getEndStamp());
            String formatEnd = DateUtil.format(answerEndDate, "yyyy-MM-dd HH:mm:ss");
            answerEnd = DateUtil.parse(formatEnd, "yyyy-MM-dd HH:mm:ss");
        }
        callRecord.setAnswerStart(answerStart);
        callRecord.setAnswerEnd(answerEnd);

        //呼叫中心服务器话单地址
        String serviceHost = StringUtil.isEmpty(param.getRecordHost()) ? "https://fy.hndexin.net:7444/record/" : param.getRecordHost();

        log.info("呼叫中心请求地址:" + param.getRecordHost());

        String sip = param.getSip();//SIP账号

        Integer operationType = null;  //操作类型（1-导入客户，2-案件生成）
        Long odvId = null;
        Long teamId = null;
        Integer type = null;
//        如果坐席为空，则根据任务uuid匹配机构id
        if (StringUtils.isEmpty(param.getTaskUuid())) throw new GlobalException("预测试外呼-任务uuid为空");
        IntelligenceTask task = intelligenceTaskService.selectByUuid(param.getTaskUuid());
        if (!ObjectUtils.isEmpty(task)) {
            operationType = task.getOperationType();
        }
        if (StringUtils.isEmpty(sip)) {
            if (!ObjectUtils.isEmpty(task)) {
                teamId = task.getTeamId() == null ? null : task.getTeamId().longValue();
                type = task.getTeamType();
            }
//            根据客户uuid查找客户对应的员工id
            if (!StringUtils.isEmpty(param.getCustomerId())) {
                Integer integer = customDetailsService.selectByCustomUuid(param.getCustomerId());
                odvId = integer == null ? null : integer.longValue();
            }
        }
        callRecord.setSipNumber(sip);
        CallSip callSip = null;
        if (!StringUtils.isEmpty(sip)) {
            callSip = callRecordService.selectBySipNumber(sip);
        }

        if (callSip != null) {
            teamId = callSip.getTeamId();
            odvId = callSip.getOdvId();
            type = callSip.getTeamType();
        }

        callRecord.setTeamId(teamId);
        callRecord.setOdvId(odvId);
        callRecord.setServiceHost(serviceHost);
        callRecord.setTeamType(type);
//        根据呼叫callid查找该话单是否存在
        Long aLong = callRecordService.selectByCallId(param.getCallid());
        if (aLong == null) {
//            不存在则新增
            callRecordService.insert(callRecord);
        } else {
            if (param.getConnectFlag() == null) throw new GlobalException("预测试外呼任务接通标识不能为空");
            if (param.getConnectFlag() == 0) return;
//            存在则修改
            callRecord.setId(aLong);
            callRecordService.updateCallRecord(callRecord);
        }

//        判断是否要写入催记
//        log.info("判断是否要写入催记ConnectFlag："+  callRecord.getConnectFlag());
//        if (operationType == null || callRecord.getConnectFlag() == null) return;
//        try {
////            操作类型（1-导入客户，2-案件生成）
//            if (operationType == 2 && callRecord.getConnectFlag() != 0) {
//                if (!StringUtils.isEmpty(param.getCustomerId())) {
//                    CallCustomDetails customDetails = customDetailsService.selectCaseIdOrContactsId(param.getCustomerId());
//                    if (ObjectUtils.isEmpty(customDetails)) return;
//                    if (ObjectUtils.isEmpty(customDetails.getCaseId()) || ObjectUtils.isEmpty(customDetails.getContactsId()))
//                        return;
////                    写入固定催记信息
//                    UrgeRecord urgeRecord = new UrgeRecord();
//                    urgeRecord.setCaseId(customDetails.getCaseId());
//                    urgeRecord.setContactId(customDetails.getContactsId());
//                    urgeRecord.setCreateId(teamId == null ? null : teamId.intValue());
//                    urgeRecord.setOdvId(odvId);
//                    log.info("进入自动生成催记："+ JSON.toJSONString(urgeRecord));
//                    updateFollowUpStatus(urgeRecord);
//                }
//            }
//        } catch (Exception e) {
//            log.error("自动生成催记失败：" + e.getMessage());
//        }
    }

    /**
     * 写入催收记录并修改案件的跟进以及催收状态
     *
     * @param urgeRecord
     */
    public void updateFollowUpStatus(UrgeRecord urgeRecord) {
//        判断催收员id不为空则查询催收员姓名
        if (!ObjectUtils.isEmpty(urgeRecord.getOdvId())) {
            String odvName = customDetailsService.selectOdvName(urgeRecord.getOdvId().intValue());
            urgeRecord.setOdvName(odvName);
            urgeRecord.setCreateBy(odvName);
        }
        InfoContact infoContact = customDetailsService.selectInfoContactById(urgeRecord.getContactId());
        if (ObjectUtils.isEmpty(infoContact)) {
            throw new GlobalException("请选择有效的联系人");
        }
        urgeRecord.setLiaison(infoContact.getContactName());
        urgeRecord.setContactMode(infoContact.getContactPhone());
        urgeRecord.setRelation(infoContact.getContactRelation());
        urgeRecord.setUrgeTpye(BaseConstant.URGE_TYPE_TALL);
//        根据团队id查询属于哪个类型机构（调执类/调诉类/催收类）
        if (!ObjectUtils.isEmpty(urgeRecord.getCreateId())) {
            String teamLevelType = customDetailsService.selectTeamLevelType(urgeRecord.getCreateId());
            if (!StringUtils.isEmpty(teamLevelType)) {
                Integer code = null;
                if (teamLevelType.equals(SendIpEnum.CUISHOU.getType())) {
                    code = WebSideEnum.DISPOSE.getCode();
                } else if (teamLevelType.equals(SendIpEnum.TIAOSU.getType())) {
                    code = WebSideEnum.APPEAL.getCode();
                } else if (teamLevelType.equals(SendIpEnum.TIAOZHI.getType())) {
                    code = WebSideEnum.MEDIATE.getCode();
                }
                urgeRecord.setWebSide(code);
            }
        }
        urgeRecord.setCreateTime(new Date());
        urgeRecord.setOperationType(1);
        urgeRecord.setFollowUpState("每日跟进");
        urgeRecord.setUrgeState("其他");
        urgeRecord.setContent("外呼通知");
        urgeRecord.setContactMedium("电话");
        urgeRecord.setDelFlag(BaseConstant.DelFlag_Being);
        urgeRecord.setRemarks("/");
        log.info("自动生成催记入库："+ JSON.toJSONString(urgeRecord));
        customDetailsService.insertUrgeRecord(urgeRecord);   //写入催收记录
    }


    /**
     * 预测试外呼-处理通知信息
     *
     * @param param
     */
    public void outboundCallNotification(CallNoticePojo param) {
//        判断是预测试外呼任务还是智能语音任务
        if (param.getTaskType() == 1) {
//            判断该任务是否存在业务系统
            IntelligenceTask task = intelligenceTaskService.selectByUuid(param.getTaskUuid());
            if (param.getEventType().equals(EventTaskConstants.EVENT_TASK_FINISHED)) {
                eventTaskFinished(task, param);
            } else if (param.getEventType().equals(EventTaskConstants.EVENT_TASK_SCREEN)) {
                eventTaskScreen(task, param);
            } else {
                throw new GlobalException("通知事件类型不存在：" + param.getEventType());
            }
        } else if (param.getTaskType() == 2) {
            log.info("智能语音任务处理-------------------------------");
        } else if (param.getTaskType() == 3) {
            log.info("回铃监测任务处理-------------------------------");
            CallBackNoticePojo callBackNoticePojo = JSONObject.parseObject(param.getEventData(), CallBackNoticePojo.class);
            eventTaskRingBack(callBackNoticePojo);
        } else {
            throw new GlobalException("任务类型错误：" + param.getTaskType() + " 该类型不存在");
        }
    }

    /**
     * （预测试外呼）任务完成
     *
     * @param task
     * @param param
     */
    public void eventTaskFinished(IntelligenceTask task, CallNoticePojo param) {
        if (task == null || ObjectUtils.isEmpty(task))
            throw new GlobalException("该任务不存在，无法更新任务状态");
        if (StringUtils.isEmpty(task.getEnterpriseNum()))
            throw new GlobalException("该任务企业编号查询为空");
//            根据任务uuid调用呼叫中心接口查询该任务的最新状态
        List<String> list = new ArrayList<>();
        list.add(param.getTaskUuid());
        Map<String, Object> map = new HashMap<>();
        map.put("apiFlag", callConfig.getApiFlag());
        map.put("enterpriseNum", task.getEnterpriseNum());
        map.put("taskUuids", list);
        CallCenterTaskPojo taskPojo = new CallCenterTaskPojo();
        taskPojo.setServiceHostUrl(callConfig.getHostUrl());
        taskPojo.setMap(map);
        List<CallCenterTaskPojo> taskStatus = callingTaskDockingApi.getCallTaskStatus(taskPojo);
        if (ObjectUtils.isEmpty(taskStatus)) throw new GlobalException("查询任务解析数据为空");
        IntelligenceTask intelligenceTask = null;
        for (CallCenterTaskPojo row : taskStatus) {
            if (StringUtils.isEmpty(row.getTaskUuid())) continue;
            if (row.getStatus() == null) continue;
            if (param.getTaskUuid().equals(row.getTaskUuid())) {
                intelligenceTask = new IntelligenceTask();
                intelligenceTask.setStatus(row.getStatus());
                break;
            }
        }
        if (ObjectUtils.isEmpty(intelligenceTask))
            throw new GlobalException("拉取任务信息错误：" + param.getTaskUuid());
        intelligenceTask.setId(task.getId());
        intelligenceTask.setUpdateTime(new Date());
//            根据主键修改任务状态
        intelligenceTaskService.updateById(intelligenceTask);
    }

    /**
     * （预测试外呼）来电弹屏
     *
     * @param task
     * @param param
     */
    public void eventTaskScreen(IntelligenceTask task, CallNoticePojo param) {
//            来电弹屏：用websocket通知前端那个任务的那个手机号来电
        if (StringUtils.isEmpty(param.getEventData())) throw new GlobalException("来电弹屏：任务信息不能为空");
        if (task == null || ObjectUtils.isEmpty(task)) throw new GlobalException("该任务不存在，无法弹屏");
//            判断该任务是否需要弹屏
        if (task.getIsScreen() == 1) {
            TaskEventScreenPojo pojo = JSONUtil.toBean(param.getEventData(), TaskEventScreenPojo.class);
            if (ObjectUtils.isEmpty(pojo)) throw new GlobalException("解析信息为空");
            if (StringUtils.isEmpty(pojo.getPhone())) throw new GlobalException("手机号信息不能为空");
            if (StringUtils.isEmpty(pojo.getAnswerSip())) throw new GlobalException("sip账号不能为空");
            if (StringUtils.isEmpty(pojo.getCustomerId())) throw new GlobalException("客户id不能为空");
//                根据坐席账号查找催员id
            CallSip callSip = callRecordService.selectBySipNumber(pojo.getAnswerSip());
            if (ObjectUtils.isEmpty(callSip)) throw new GlobalException("坐席信息查询为空");
            Long odvId = callSip.getOdvId();
            if (odvId == null) throw new GlobalException("该坐席未分配给催员，无法来电弹屏");
            if (task.getOperationType() == null) return;

            CallMessage callMessage = new CallMessage();
            callMessage.setIdentification(UserConstants.ACCOUNT_TYPE_1);
            callMessage.setUserId(odvId);
            callMessage.setPhone(pojo.getPhone());
            callMessage.setTaskUuid(param.getTaskUuid());
            callMessage.setCustomUuid(pojo.getCustomerId());

            if (task.getOperationType() == 1) {
                remoteMessageService.pushCallPopUpScreen(callMessage, SecurityConstants.INNER);
            } else if (task.getOperationType() == 2) {
//                根据客户uuid查询案件id以及联系人id
                CallCustomDetails callCustomDetails = customDetailsService.selectCaseIdOrContactsId(pojo.getCustomerId());
                if (ObjectUtils.isEmpty(callCustomDetails)) throw new GlobalException("客户信息查询为空");
                callMessage.setCaseId(callCustomDetails.getCaseId());
                callMessage.setContactsId(callCustomDetails.getContactsId());
                remoteMessageService.pushCaseCallPopUpScreen(callMessage, SecurityConstants.INNER);
            } else {
                log.error("任务操作类型错误:" + task.getOperationType());
            }
        }
    }

    /**
     * （预测试外呼）回铃监测
     *
     * @param callBackNoticePojo
     */
    public void eventTaskRingBack(CallBackNoticePojo callBackNoticePojo) {
        log.info("回铃监测：" + JSON.toJSONString(callBackNoticePojo));
        Long id = callRecordService.selectByCallId(callBackNoticePojo.getCallid());
        log.info("回铃监测查询的话单id：" + id);
        if(ObjectUtil.isNotNull(id)){
            CallRecord callRecord = new CallRecord();
            callRecord.setId(id);
            callRecord.setCallResult(callBackNoticePojo.getVoskResult());
            callRecordService.updateCallRecord(callRecord);
        }
    }


    /**
     * AI语音通知话单-回调
     * @param param
     */
    public void callbackAiVoice(CallbackPojo param) {
        if (StringUtils.isEmpty(param.getCallid())) throw new GlobalException("通话唯一编码不能为空");
        CallRecord callRecord = new CallRecord();

        BeanUtil.copyProperties(param, callRecord);
        callRecord.setCustomUuid(param.getCustomerId());
        callRecord.setCompanyNum(param.getEnterpriseNum());
        callRecord.setCompanyName(param.getEnterpriseName());
        callRecord.setAgentDuration(param.getExtDuration());
        callRecord.setCallroter(param.getCallroter());
        callRecord.setNumber(param.getTrunkNumber());
        if (!StringUtils.isEmpty(param.getuID()))
            callRecord.setCaseId(Long.parseLong(param.getuID()));

        if (!ObjectUtils.isEmpty(param.getExtDuration())) {
            String answer = param.getExtDuration() > 0 ? "y" : "n";
            callRecord.setAnswer(answer);
        }
        callRecord.setServiceHost(param.getRecordHost());
        callRecord.setCallFrom(param.getCallfrom());
        callRecord.setCallTo(param.getCallto());
        callRecord.setCallbackTime(new Date());

        if (!ObjectUtils.isEmpty(param.getStartStamp())) {
            Date callTime = new Date(param.getStartStamp());
            String formatCall = DateUtil.format(callTime, "yyyy-MM-dd HH:mm:ss");
            Date dateCall = DateUtil.parse(formatCall, "yyyy-MM-dd HH:mm:ss");
            callRecord.setCallTime(dateCall);
        }

        Date answerStart = null, answerEnd = null;
        if (param.getAnswerStamp() != null && param.getAnswerStamp() > 0) {
            Date answerStartDate = new Date(param.getAnswerStamp());
            String formatStart = DateUtil.format(answerStartDate, "yyyy-MM-dd HH:mm:ss");
            answerStart = DateUtil.parse(formatStart, "yyyy-MM-dd HH:mm:ss");
        }
        if (param.getEndStamp() != null && param.getEndStamp() > 0) {
            Date answerEndDate = new Date(param.getEndStamp());
            String formatEnd = DateUtil.format(answerEndDate, "yyyy-MM-dd HH:mm:ss");
            answerEnd = DateUtil.parse(formatEnd, "yyyy-MM-dd HH:mm:ss");
        }
        callRecord.setAnswerStart(answerStart);
        callRecord.setAnswerEnd(answerEnd);

        //呼叫中心服务器话单地址
        String serviceHost = StringUtil.isEmpty(param.getRecordHost()) ? "https://fy.hndexin.net:7444/record/" : param.getRecordHost();

        log.info("呼叫中心请求地址:" + param.getRecordHost());


        Integer operationType = null;  //操作类型（1-导入客户，2-案件生成）
        //Long odvId = null;
        Long teamId = null;
        Integer type = null;

        //根据任务uuid匹配机构id
        if (StringUtils.isEmpty(param.getTaskUuid())) throw new GlobalException("AI语音通知-任务uuid不能为空");
        AiCallIntelligenceTask task = aiVoiceTaskService.selectByUuid(param.getTaskUuid());
        if (!ObjectUtils.isEmpty(task)){
            operationType=task.getOperationType();
            teamId= Long.valueOf(task.getTeamId());
            type=task.getTeamType();
        }

        callRecord.setTeamId(teamId);
        callRecord.setServiceHost(serviceHost);
        callRecord.setTeamType(type);
//        根据呼叫callid查找该话单是否存在
        Long aLong = callRecordService.selectByCallId(param.getCallid());
        if (aLong == null) {
//            不存在则新增
            callRecordService.insert(callRecord);
        } else {
            if (param.getConnectFlag() == null) throw new GlobalException("AI语音通知任务接通标识不能为空");
            if (param.getConnectFlag() == 0) return;
//            存在则修改
            callRecord.setId(aLong);
            callRecordService.updateCallRecord(callRecord);
        }

    }
}
