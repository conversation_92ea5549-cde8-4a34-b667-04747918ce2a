package com.zws.call.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 客户资料导入记录详细信息表（工作手机）-实体类
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 10:57
 */
@Data
public class WorkPhoneCustomDetails {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 案件id
     */
    private String caseId;

    /**
     * 客户名称
     */
    private String caseName;

    /**
     * 联系方式
     */
    private String casePhone;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 委托金额
     */
    private BigDecimal entrustMoney;

    /**
     * 剩余应还债权金额
     */
    private BigDecimal remainingDueMoney;

    /**
     * 剩余本金
     */
    private BigDecimal remainMoney;

    /**
     * 逾期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date overTime;


}