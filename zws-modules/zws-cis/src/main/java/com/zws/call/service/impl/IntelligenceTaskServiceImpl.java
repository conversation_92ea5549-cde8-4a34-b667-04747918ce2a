package com.zws.call.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zws.call.domain.CallCustomDetails;
import com.zws.call.mapper.IntelligenceTaskMapper;
import com.zws.call.pojo.CallRecordDataPojo;
import com.zws.call.pojo.EmployeesPojo;
import com.zws.call.service.ICallCustomDetailsService;
import com.zws.call.service.IIntelligenceTaskService;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.utils.SplitUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.CaseManage;
import com.zws.system.api.domain.IntelligenceTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: 马博新
 * @date ：Created in 2024/08/30 17:54
 */
@Service
public class IntelligenceTaskServiceImpl implements IIntelligenceTaskService {

    @Resource
    private IntelligenceTaskMapper mapper;
    @Autowired
    private ICallCustomDetailsService customDetailsService;

    /**
     * 客户列表查询参数处理
     *
     * @param intelligenceTask
     * @return
     */
    @Override
    public Map<String, Object> verifyParameters(IntelligenceTask intelligenceTask) {
        intelligenceTask.setTeamId(SecurityUtils.getTeamId().intValue());
        if (SecurityUtils.getAccountType() == null) throw new GlobalException("登录人信息获取错误");
        if (SecurityUtils.getAccountType() == 1) {
            List<Integer> list = new ArrayList<>();
//            获取是否是部门负责人
            boolean departmentHead = SecurityUtils.isDepartmentHead();
            if (ObjectUtils.isEmpty(departmentHead)) throw new GlobalException("登录人信息获取错误");
            if (departmentHead) {
                Integer deptId = SecurityUtils.getDeptId();
                if (deptId != null) {
//                    根据部门id查询员工id
                    List<Integer> userIds = customDetailsService.selectProgenyDept(deptId, SecurityUtils.getTeamId().intValue());
                    if (ObjectUtils.isEmpty(userIds)) userIds = new ArrayList<>();
                    list.addAll(userIds);
                }
            }else {
                list.add(SecurityUtils.getUserId().intValue());
            }
            intelligenceTask.setMemberIdList(list);
        }

        if (!ObjectUtils.isEmpty(intelligenceTask.getCreateTime2())) {
            DateTime dateTime1 = DateUtil.endOfDay(intelligenceTask.getCreateTime2());
            intelligenceTask.setCreateTime2(dateTime1);
        }
        intelligenceTask.setTaskNameList(SplitUtils.splitCharacterString(intelligenceTask.getTaskName()));
        intelligenceTask.setStatusList(SplitUtils.splitCharacterInteger(intelligenceTask.getStatusStr()));

        if (!ObjectUtils.isEmpty(intelligenceTask.getMemberIdList())) {
//            根据条件查询对应的客户id带入查询任务条件
            Map<String, Object> objectMap = new HashMap<>();
            objectMap.put("teamId", intelligenceTask.getTeamId());
            objectMap.put("memberIdList", intelligenceTask.getMemberIdList());
            PageUtils.clearPage();
            List<Long> longList = mapper.selectRecordId(objectMap);
            intelligenceTask.setCustomReloadIdList(longList);
        }
        PageUtils.startPage();
        Map<String, Object> map = BeanUtil.beanToMap(intelligenceTask);
        return map;
    }

    /**
     * 写入预测式外呼任务
     *
     * @param record
     * @return
     */
    @Override
    public Long insert(IntelligenceTask record) {
        mapper.insert(record);
        return record.getId();
    }

    /**
     * 根据任务名称机构内去重查询
     *
     * @param taskName
     * @param teamId
     * @return
     */
    @Override
    public Integer selectByCount(String taskName, Integer teamId) {
        return mapper.selectByCount(taskName, teamId);
    }

    /**
     * 条件查询预测试外呼任务列表
     *
     * @param intelligenceTask
     * @return
     */
    @Override
    public List<IntelligenceTask> selectList(IntelligenceTask intelligenceTask) {
        Map<String, Object> map = verifyParameters(intelligenceTask);
        return mapper.selectList(map);
    }

    /**
     * id集合查找任务对应uuid
     *
     * @param map
     * @return
     */
    @Override
    public List<String> selectTaskUuidList(Map<String, Object> map) {
        return mapper.selectTaskUuidList(map);
    }

    /**
     * 根据任务uuid查询已拨通的客户uuid
     *
     * @param map
     * @return
     */
    @Override
    public List<String> selectCustomUuidList(Map<String, Object> map) {
        return mapper.selectCustomUuidList(map);
    }

    /**
     * 根据条件查询客户uuid
     *
     * @param map
     * @return
     */
    @Override
    public List<String> getNotConnectedCustomUuid(Map<String, Object> map) {
        return mapper.getNotConnectedCustomUuid(map);
    }

    /**
     * 查询机构所有任务名称
     *
     * @param teamId
     * @return
     */
    @Override
    public List<String> selectTaskNameList(Integer teamId) {
        return mapper.selectTaskNameList(teamId);
    }

    /**
     * 根据主键id查询预测试外呼任务信息
     *
     * @param id
     * @return
     */
    @Override
    public IntelligenceTask selectById(Long id, Integer teamId) {
        return mapper.selectById(id, teamId);
    }

    /**
     * 根据任务uuid查询主键id
     *
     * @param callCenterUuid
     * @return
     */
    @Override
    public IntelligenceTask selectByUuid(String callCenterUuid) {
        return mapper.selectByUuid(callCenterUuid);
    }

    /**
     * 统计话单各状态数量
     *
     * @param map
     * @return
     */
    @Override
    public Integer selectCallRecordCount(Map<String, Object> map) {
        return mapper.selectCallRecordCount(map);
    }

    /**
     * 根据呼叫状态查询客户uuid
     *
     * @param map
     * @return
     */
    @Override
    public List<String> selectCallReloadList(Map<String, Object> map) {
        return mapper.selectCallReloadList(map);
    }

    /**
     * 查询任务对应的话单信息-（预测试外呼）
     *
     * @param map
     * @return
     */
    @Override
    public List<CallRecordDataPojo> selectCallRecordList(Map<String, Object> map) {
        return mapper.selectCallRecordList(map);
    }

    /**
     * 根据员工id集合查询员工信息
     *
     * @param map
     * @return
     */
    @Override
    public List<EmployeesPojo> selectEmployeesData(Map<String, Object> map) {
        return mapper.selectEmployeesData(map);
    }

    /**
     * 根据案件id集合查询催员id集合信息
     *
     * @param map
     * @return
     */
    @Override
    public List<Integer> selectEmployeeIdList(Map<String, Object> map) {
        return mapper.selectEmployeeIdList(map);
    }

    /**
     * 根据案件id集合查询催员id集合信息(调诉端)
     *
     * @param map
     * @return
     */
    @Override
    public List<Integer> selectEmployeeIdListAppeal(Map<String, Object> map) {
        return  mapper.selectEmployeeIdListAppeal(map);
    }

    /**
     * 根据案件id集合查询催员id集合信息
     *
     * @param map
     * @return
     */
    @Override
    public List<Integer> selectTsEmployeeIdList(Map<String, Object> map) {
        return mapper.selectTsEmployeeIdList(map);
    }

    /**
     * 根据案件id集合查询催员id集合信息
     *
     * @param map
     * @return
     */
    @Override
    public List<Integer> selectStEmployeeIdList(Map<String, Object> map) {
        return mapper.selectStEmployeeIdList(map);
    }


    /**
     * 查询有预测试外呼坐席的催员id
     *
     * @param map
     * @return
     */
    @Override
    public List<Integer> selectSipOdvIdList(Map<String, Object> map) {
        return mapper.selectSipOdvIdList(map);
    }

    /**
     * 根据案件id集合查询联系人信息
     *
     * @param map
     * @return
     */
    @Override
    public List<CallCustomDetails> selectContactsList(Map<String, Object> map) {
        return mapper.selectContactsList(map);
    }

    /**
     * 根据案件id查询案件详情
     *
     * @param caseId
     * @return
     */
    @Override
    public CaseManage selectCaseManageCaseId(Long caseId) {
        return mapper.selectCaseManageCaseId(caseId);
    }

    /**
     * 根据id修改
     *
     * @param record
     * @return
     */
    @Override
    public int updateById(IntelligenceTask record) {
        return mapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据任务uuid修改
     *
     * @param record
     * @return
     */
    @Override
    public int updateByUuidList(IntelligenceTask record) {
        return mapper.updateByUuidList(record);
    }
}
