package com.zws.call.agservice;

import com.zws.call.domain.CallCustomDetails;
import com.zws.call.domain.CustomRemarkLog;
import com.zws.call.pojo.DeptPojo;
import com.zws.call.pojo.EmployeesPojo;
import com.zws.call.pojo.TreeTypePojo;
import com.zws.call.service.ICallCustomDetailsService;
import com.zws.call.service.ICallCustomRecordService;
import com.zws.call.service.ICustomRemarkLogService;
import com.zws.call.service.IIntelligenceTaskService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 客户资料导入（预测试外呼）-业务处理层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 11:12
 */
@Slf4j
@Component
//@Transactional(rollbackFor = Exception.class)
public class AgCallCustomService {

    @Autowired
    private ICallCustomRecordService customRecordService;
    @Autowired
    private ICallCustomDetailsService customDetailsService;
    @Autowired
    private IIntelligenceTaskService taskService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ICustomRemarkLogService remarkLogService;


    /**
     * 校验上传客户名单
     *
     * @param userList
     * @param threadEntityPojo
     * @return
     */
    public Map<String, Object> verifyCustomerList(List<CallCustomDetails> userList, ThreadEntityPojo threadEntityPojo, String taskName) {
//        校验任务名称机构内去重
        Integer count = taskService.selectByCount(taskName, threadEntityPojo.getCreateId());
        if (count > 0) throw new GlobalException("任务名称已存在，请重新输入");
        Map<String, Object> map = new HashMap<>();
//        存储文件里面写的员工工号
        List<Integer> integerList = new ArrayList<>();
//        员工id集合
        List<Integer> idList = new ArrayList<>();
        for (CallCustomDetails row : userList) {
            if (ObjectUtils.isEmpty(row.getCasePhone()))
                throw new GlobalException("缺失内容（联系方式）");
            if (row.getUserNo() != null)
                if (!integerList.contains(row.getUserNo())) integerList.add(row.getUserNo());
        }
//        根据员工工号以及机构id查询该工号是否存在
        if (!ObjectUtils.isEmpty(integerList)) {
            for (Integer row : integerList) {
                Map<String, Object> hashMap = new HashMap<>();
                hashMap.put("teamId", threadEntityPojo.getCreateId());
                hashMap.put("employeesWorking", row);
                Integer integer = customRecordService.selectEmployeesId(hashMap);
                if (integer == null) throw new GlobalException("所属坐席（工号）：" + row + " 不存在");
//                根据员工id以及机构id判断该员工是否有预测试外呼坐席
                Map<String, Object> objectMap = new HashMap<>();
                objectMap.put("teamId", threadEntityPojo.getCreateId());
                objectMap.put("odvId", integer);
                objectMap.put("seatsType", 2);
                String sip = customRecordService.selectSip(objectMap);
                if (StringUtils.isEmpty(sip))
                    throw new GlobalException("所属坐席（工号）：" + row + " 未分配预测试外呼坐席");
                idList.add(integer);
            }
        }
//        设置随机key
        String uuid = "VerifyCustomer:" + UUID.randomUUID();
//        设置超时时间
        redisService.expire(uuid, 1, TimeUnit.HOURS);
//        将正确信息存入redis中
        redisService.setCacheList(uuid, userList);
        map.put("key", uuid);
        map.put("employeesIdList", idList);
        return map;
    }

    /**
     * 返回部门列表树类型信息
     *
     * @param
     * @return
     */
    public List<TreeTypePojo> DeptTreeType() {
        List<DeptPojo> deptList = customRecordService.selectDept(SecurityUtils.getTeamId().intValue());//根据团队id查询该团队所有部门信息
        List<Long> longList = customRecordService.selectCallSip(SecurityUtils.getTeamId().intValue());
        if (ObjectUtils.isEmpty(longList)) longList = new ArrayList<>();
        List<TreeTypePojo> treeTypes = new ArrayList<>();
        for (DeptPojo dept : deptList) {
            if (dept.getParentId().equals(0)) {    //如果为顶级部门
                TreeTypePojo treeType = new TreeTypePojo();
                treeType.setId("Dept:" + dept.getId());
                treeType.setName(dept.getDeptName());
                Boolean aBoolean = ChildNode(dept.getId());
                if (aBoolean) {
                    List<TreeTypePojo> listr = listr(deptList, dept, longList);
                    treeType.setChildren(listr);
                } else {
                    List<EmployeesPojo> employeess = customRecordService.selectEmployeesParentId(dept.getId(), SecurityUtils.getTeamId().intValue());
                    List<TreeTypePojo> arrlist = new ArrayList<>();
                    for (EmployeesPojo employees1 : employeess) {
                        if (!longList.contains(employees1.getId().longValue())) continue;
                        TreeTypePojo treeType1 = new TreeTypePojo();
                        treeType1.setId(employees1.getId().toString());
                        treeType1.setName(employees1.getEmployeeName());
                        treeType1.setSipAccountNumber(employees1.getSipNumber());
                        treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
                        arrlist.add(treeType1);

                    }
                    treeType.setChildren(arrlist);
                }
                treeTypes.add(treeType);
            }
        }

        return treeTypes;
    }

    /**
     * 判断是否有子列表
     *
     * @param parentId
     * @return
     */
    public Boolean ChildNode(int parentId) {
        List<DeptPojo> deptList = customRecordService.selectDeptParentId(parentId);
        return !ObjectUtils.isEmpty(deptList);
    }

    /**
     * 迭代查询子列表
     *
     * @param depts
     * @param dept
     * @return
     */
    public List<TreeTypePojo> listr(List<DeptPojo> depts, DeptPojo dept, List<Long> longList) {
        List<TreeTypePojo> list = new ArrayList<>();
        for (DeptPojo dept1 : depts) {

            if (dept1.getParentId() == dept.getId()) {

                TreeTypePojo treeType = new TreeTypePojo();
                treeType.setId("Dept:" + dept1.getId());
                treeType.setName(dept1.getDeptName());
                List<TreeTypePojo> childrens = listr(depts, dept1, longList);

                treeType.setChildren(childrens);
                list.add(treeType);

            }
        }
        List<EmployeesPojo> employees = customRecordService.selectEmployeesParentId(dept.getId(), SecurityUtils.getTeamId().intValue());  //根据部门id以及团队id查询该部门人员信息
        List<TreeTypePojo> arrlist = new ArrayList<>();
        for (EmployeesPojo employees1 : employees) {
            if (!longList.contains(employees1.getId().longValue())) continue;
            TreeTypePojo treeType1 = new TreeTypePojo();
            treeType1.setId(employees1.getId().toString());
            treeType1.setName(employees1.getEmployeeName());
            treeType1.setSipAccountNumber(employees1.getSipNumber());
            treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
            arrlist.add(treeType1);
        }

        list.addAll(arrlist);


        return list;
    }

    /**
     * 客户列表-编辑（同时记录备注历史信息）
     *
     * @param customDetails
     * @return
     */
    @Transactional
    public void updateCustomDetails(CallCustomDetails customDetails) {
        if (ObjectUtils.isEmpty(customDetails.getIds())) throw new GlobalException("主键id获取为空");
        if (customDetails.getRemark()!=null&&customDetails.getRemark().length() > 500) throw new GlobalException("输入内容超长，备注内容限制长度500");
        for (Long row : customDetails.getIds()) {
//            写入备注历史信息表
            CustomRemarkLog remarkLog = new CustomRemarkLog();
            remarkLog.setRemark(customDetails.getRemark());
            remarkLog.setCustomId(row);
            remarkLog.setNoteBy(SecurityUtils.getUsername());
            remarkLog.setNoteTime(new Date());
            remarkLog.setNoteById(SecurityUtils.getUserId().intValue());
            remarkLog.setDelFlag(BaseConstant.DelFlag_Being);
            remarkLog.setNoteByType(SecurityUtils.getAccountType());
            remarkLog.setTeamId(SecurityUtils.getTeamId().intValue());
            remarkLogService.insert(remarkLog);
        }
        CallCustomDetails details = new CallCustomDetails();
//        details.setId(customDetails.getId());
        details.setIds(customDetails.getIds());
        details.setRemark(customDetails.getRemark());
        details.setNoteBy(SecurityUtils.getUsername());
        details.setNoteById(SecurityUtils.getUserId().intValue());
        details.setNoteByType(SecurityUtils.getAccountType());
        details.setNoteTime(new Date());
//        修改客户备注信息
        customDetailsService.updateByPrimaryKeySelective(details);
    }
}
