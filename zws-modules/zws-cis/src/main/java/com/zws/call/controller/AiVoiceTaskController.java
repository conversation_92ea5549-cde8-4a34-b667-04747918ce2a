package com.zws.call.controller;


import com.zws.call.agservice.AiVoiceTaskService;
import com.zws.call.domain.AiCallIntelligenceTask;
import com.zws.call.enums.AiCallStateEnum;
import com.zws.call.enums.CallStateEnum;
import com.zws.call.pojo.*;
import com.zws.call.service.IAiVoiceTaskService;
import com.zws.call.service.ICallRecordService;
import com.zws.cis.pojo.TeamStatePojo;
import com.zws.common.core.constant.FileConstant;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.AiVoiceTask;
import com.zws.system.api.domain.IntelligenceTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * AI语音任务管理
 */
@CrossOrigin
@RequestMapping("/aiVoiceTask")
@RestController
public class AiVoiceTaskController extends BaseController {

    @Autowired
    private AiVoiceTaskService aiVoiceTaskService;

    @Autowired
    private IAiVoiceTaskService iAiVoiceTaskService;

    @Autowired
    private ICallRecordService callRecordService;

    /**
     * AI智能语音列表 - 查询
     * @param aiVoiceList 高级查询条件
     * @return
     */
    @GetMapping("/findAiVoiceTaskList")
    public TableDataInfo findAiVoiceTaskList(AiCallIntelligenceTask aiVoiceList){
        startPage();
        if (Objects.isNull(aiVoiceList)){
            aiVoiceList = new AiCallIntelligenceTask();
        }
        //AI智能语音列表-列表查询
        List<AiCallIntelligenceTask> list = aiVoiceTaskService.findAiVoiceTaskList(aiVoiceList);
        return getDataTable(list);
    }

    /**
     * 话术/短信模板列表下拉
     * @return
     */
    @GetMapping("/getAiVoiceTplList")
    public AjaxResult getAiVoiceTplList(){
        List<AiVoiceTplListPojo> list = aiVoiceTaskService.getAiVoiceTplList();
        return AjaxResult.success("操作成功", list);
    }

    /**
     * 任务名称列表下拉
     * @return
     */
    @GetMapping("/getAiTaskNameList")
    public AjaxResult getAiTaskNameList(){
        List<String> list = iAiVoiceTaskService.selectAiTaskNameList(SecurityUtils.getTeamId().intValue());
        return AjaxResult.success("操作成功", list);
    }

    /**
     * 任务状态列表下拉
     * @return
     */
    @GetMapping("/getTaskStatus")
    public AjaxResult getTaskStatus(){
        AiCallStateEnum[] startS = AiCallStateEnum.values();
        ArrayList<TeamStatePojo> list = new ArrayList<>();
        for (AiCallStateEnum temp : startS) {
            list.add(new TeamStatePojo(temp.getCode(), temp.getInfo()));
        }
        return AjaxResult.success("操作成功", list);
    }

    /**
     * AI语音通知列表  -执行
     * @return
     */
    @PostMapping("/executeAiTask")
    public AjaxResult updateAiVoiceStatus(@RequestBody AiCallIntelligenceTask aiVoiceTask){
        if (Objects.isNull(aiVoiceTask.getId()) || Objects.isNull(aiVoiceTask.getTaskStatus())){
            new GlobalException("任务ID或任务状态不能为空！");
        }
        aiVoiceTask.setTeamId(SecurityUtils.getTeamId().intValue());
        aiVoiceTask.setTaskStatus(CallStateEnum.IN_PROGRESS.getCode());
        aiVoiceTaskService.updateAiVoiceStatus(aiVoiceTask);
        return AjaxResult.success("操作成功");
    }

    /**
     * AI语音通知列表  -暂停
     * @return
     */
    @PostMapping("/suspendAiTask")
    public AjaxResult suspendAiTask(@RequestBody AiCallIntelligenceTask aiVoiceTask){
        if (Objects.isNull(aiVoiceTask.getId()) || Objects.isNull(aiVoiceTask.getTaskStatus())){
            new GlobalException("任务ID或任务状态不能为空！");
        }
        aiVoiceTask.setTeamId(SecurityUtils.getTeamId().intValue());
        aiVoiceTask.setTaskStatus(CallStateEnum.PAUSED.getCode());
        aiVoiceTaskService.updateAiVoiceStatus(aiVoiceTask);
        return AjaxResult.success("操作成功");
    }


    /**
     * AI语音通知列表  -撤销
     * @return
     */
    @PostMapping("/revokeAiTask")
    public AjaxResult revokeAiTask(@RequestBody AiCallIntelligenceTask aiVoiceTask){
        if (Objects.isNull(aiVoiceTask.getId()) || Objects.isNull(aiVoiceTask.getTaskStatus())){
            new GlobalException("任务ID或任务状态不能为空！");
        }
        aiVoiceTask.setTeamId(SecurityUtils.getTeamId().intValue());
        aiVoiceTask.setTaskStatus(CallStateEnum.REVOKED.getCode());
        aiVoiceTaskService.updateAiVoiceStatus(aiVoiceTask);
        return AjaxResult.success("操作成功");
    }


    /**
     * 提交任务不执行
     *
     * @param dataPojo 任务信息
     * @return
     * @throws Exception
     */
    @PostMapping("/submitTask")
    public AjaxResult submitTask(@Validated @RequestBody ImportAiVoiceDataPojo dataPojo) {
        if (ObjectUtils.isEmpty(dataPojo)) return AjaxResult.error("必填参数不能为空");
//        登录人信息
        ThreadEntityPojo threadEntityPojo = new ThreadEntityPojo();
        threadEntityPojo.setType(SecurityUtils.getAccountType());
        threadEntityPojo.setUser(SecurityUtils.getUsername());
        threadEntityPojo.setUserId(SecurityUtils.getUserId().intValue());
        threadEntityPojo.setCreateId(SecurityUtils.getTeamId().intValue());
        Long integer = aiVoiceTaskService.submitTaskData(dataPojo, threadEntityPojo);
        return AjaxResult.success("操作成功", integer);
    }

    /**
     * 提交任务并执行
     *
     * @param dataPojo 任务信息
     * @return
     * @throws Exception
     */
    @PostMapping("/submitTaskAndExecuteAiTask")
    public AjaxResult submitTaskAndExecuteAiTask(@Validated @RequestBody ImportAiVoiceDataPojo dataPojo) {
        if (ObjectUtils.isEmpty(dataPojo)) return AjaxResult.error("必填参数不能为空");
//        登录人信息
        ThreadEntityPojo threadEntityPojo = new ThreadEntityPojo();
        threadEntityPojo.setType(SecurityUtils.getAccountType());
        threadEntityPojo.setUser(SecurityUtils.getUsername());
        threadEntityPojo.setUserId(SecurityUtils.getUserId().intValue());
        threadEntityPojo.setCreateId(SecurityUtils.getTeamId().intValue());
        Long id = aiVoiceTaskService.submitTaskAndExecuteAiTask(dataPojo, threadEntityPojo);
        return AjaxResult.success("操作成功", id);
    }

    /**
     * 查看结果报告-数据统计
     *
     * @param id AI语音通知任务主键id
     * @return
     */
    @GetMapping("/AiCallTaskStatistics")
    public AjaxResult AiCallTaskStatistics(Long id) {
        if (id == null) return AjaxResult.error("id不能为空");
        AiTaskStatisticsPojo pojo = aiVoiceTaskService.agCallTaskStatistics(id, SecurityUtils.getTeamId().intValue());
        return AjaxResult.success("操作成功", pojo);
    }

    /**
     * 查看结果报告-数据列表
     * @param aiCallIntelligenceTask 搜索条件
     * @return
     */
    @GetMapping("/callTaskDataList")
    public TableDataInfo callTaskDataList(AiCallIntelligenceTask aiCallIntelligenceTask) {
//        startPage();
        if (ObjectUtils.isEmpty(aiCallIntelligenceTask)) throw new GlobalException("必要参数不能为空");
        if (aiCallIntelligenceTask.getId() == null) throw new GlobalException("主键id不能为空");
        aiCallIntelligenceTask.setTeamId(SecurityUtils.getTeamId().intValue());
        List<AiCallRecordDataPojo> pojoList = aiVoiceTaskService.agCallTaskDataList(aiCallIntelligenceTask);
        return getDataTable(pojoList);
    }

    /**
     * 查看结果报告-（导出）
     */
    @PostMapping("/exportAiVoiceTask")
    public void exportAiVoiceTask(HttpServletResponse response, @RequestBody AiCallIntelligenceTask task) throws Exception {
        if (ObjectUtils.isEmpty(task)) throw new GlobalException("必要参数不能为空");
        if (task.getId() == null) throw new GlobalException("主键id不能为空");
        task.setTeamId(SecurityUtils.getTeamId().intValue());
        List<AiCallRecordDataPojo> pojoList = aiVoiceTaskService.agCallTaskDataList(task);
        for (AiCallRecordDataPojo pojo : pojoList){
            if (pojo.getSmsContent()==null){
                pojo.setSmsContent("目前短信无回复内容");
            }
        }

        ExcelUtil<AiCallRecordDataPojo> util = new ExcelUtil<>(AiCallRecordDataPojo.class);
        String fileName = "话单数据列表（AI语音通知任务）" + FileConstant.getExcelSuffix();
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, pojoList, "话单数据列表（AI语音通知任务）");
    }


    /**
     * 查看结果报告-（删除）
     */
    @DeleteMapping("/deleteAiTask")
    public AjaxResult deleteAiTask(Long id) {
        iAiVoiceTaskService.deleteAiTask(id);
        return AjaxResult.success("操作成功");
    }








//--------------------------------------------------勾选案件创建预测试外呼任务接口--------------------------------------------
    /**
     * 创建AI他、语音通知任务-（勾选案件）
     *
     * @param task 任务配置信息
     * @return
     */
    @PostMapping("/AiVoiceCaseSubmitTaskData")
    public AjaxResult AiVoiceCaseSubmitTaskData(@RequestBody AiVoiceTask task) {
        if (ObjectUtils.isEmpty(task)) return AjaxResult.error("任务配置信息不能为空");
        if (ObjectUtils.isEmpty(task.getEntityPojo())) return AjaxResult.error("必填参数不能为空");
        if (ObjectUtils.isEmpty(task.getEntityPojo().getCaseIdList())) return AjaxResult.error("案件id集合不能为空");
        if (task.getEntityPojo().getCreateId() == null) return AjaxResult.error("团队id集合不能为空");
        Long integer = aiVoiceTaskService.caseSubmitTaskData(task, task.getEntityPojo());
        return AjaxResult.success("操作成功", integer);
    }

    /**
     * 创建AI语音通知任务并执行-（勾选案件）
     *
     * @param task 任务配置信息
     * @return
     */
    @PostMapping("/AiVoiceCaseSubmitTaskDataAndExecute")
    public AjaxResult AiVoiceCaseSubmitTaskDataAndExecute(@RequestBody AiVoiceTask task) {
        if (ObjectUtils.isEmpty(task)) return AjaxResult.error("任务配置信息不能为空");
        if (ObjectUtils.isEmpty(task.getEntityPojo())) return AjaxResult.error("必填参数不能为空");
        if (ObjectUtils.isEmpty(task.getEntityPojo().getCaseIdList())) return AjaxResult.error("案件id集合不能为空");
        if (task.getEntityPojo().getCreateId() == null) return AjaxResult.error("团队id集合不能为空");
        Long integer = aiVoiceTaskService.caseSubmitTaskDataAndExecute(task, task.getEntityPojo());
        return AjaxResult.success("操作成功", integer);
    }



}
