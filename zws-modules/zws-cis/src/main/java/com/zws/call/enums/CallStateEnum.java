package com.zws.call.enums;

/**
 * 执行状态 1未启动 2进行中 3已完成 4已暂停 5已撤销 6未接通重呼进行中
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 10:57
 */
public enum CallStateEnum {

    /**
     * 1-未启动
     */
    NOT_STARTED(1, "未启动"),
    /**
     * 2-进行中
     */
    IN_PROGRESS(2, "进行中"),
    /**
     * 3-已完成
     */
    COMPLETED(3, "已完成"),
    /**
     * 4-已暂停
     */
    PAUSED(4, "已暂停"),
    /**
     * 5-已撤销
     */
    REVOKED(5, "已撤销"),
//    /**
//     * 6-未接通重呼进行中
//     */
//    UNRENECTED_CALL_IN_PROGRESS(6, "未接通重呼进行中"),
    ;


    private final String info;
    private final Integer code;

    CallStateEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }


    public String getInfo() {
        return info;
    }

    public Integer getCode() {
        return code;
    }

    public static CallStateEnum valueOfCode(Integer code) {
        CallStateEnum[] enums = CallStateEnum.values();
        for (CallStateEnum temp : enums) {
            if (temp.code.equals(code)) {
                return temp;
            }
        }
        return null;
    }
}
