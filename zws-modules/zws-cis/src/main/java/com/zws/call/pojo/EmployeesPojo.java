package com.zws.call.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 团队员工信息表（实体类）
 */
@Data
public class EmployeesPojo implements Serializable {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 团队id
     */
    private Integer createId;

    /**
     * 部门id
     */
    private Integer departmentId;

    /**
     * 部门id集合
     */
    private List<Long> departmentIds;

    /**
     * 员工角色id
     */
    private Integer roleId;

    /**
     * 员工姓名
     */
    @Size(max = 30, message = "员工姓名不可超过(30)字")
    @Excel(name = "员工姓名")
    private String employeeName;

    /**
     * 所属部门名称
     */
    @Size(max = 30, message = "部门名称不可超过(30)字")
    private String departments;

    /**
     * 登录账号
     */
    @Excel(name = "登录账号")
    private String loginAccount;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String phoneNumber;

    /**
     * 登录密码
     */
    @Excel(name = "账号密码(密码长度为8-20位，其中需同时包含字母、数字和符号)")
    private String password;

    /**
     * 员工角色
     */
    private String theRole;

    /**
     * 账号状态（0:启用,1:禁用）
     */
    private Integer accountStatus;

    /**
     * 工作状态（0:在职,1:离职）
     */
    private Integer workingState;

    /**
     * 创建人
     */
    private String founder;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creationtime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 删除标志（0:未删除,1:已删除）
     */
    private Integer deleteLogo;

    /**
     * 员工工号
     */
    private Integer employeesWorking;

    /**
     * 身份证号码
     */
    private String identityNumber;

    /**
     * 认证状态
     */
    private String authenticationStatus;

    /**
     * 模糊查询字段
     */
    private String value;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date loginDate;

    /**
     * SIP 坐席账号
     * sip_number
     */
    private String sipNumber;
    /**
     * SIP坐席 密码
     * sip_password
     */
    private String sipPassword;

    /**
     * 修改密码时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updatePasswordTime;

    /**
     * 绑定工作手机状态（0-否，1-是）
     */
    private Integer bindingPhoneState;

    /**
     * 外部主键id - bhzx
     */
    private Integer externalId;
}
