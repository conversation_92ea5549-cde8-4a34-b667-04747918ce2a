package com.zws.call.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 预测试外呼任务统计-实体类
 *
 * @author: 马博新
 * @date ：Created in 2024/09/05 14:27
 */
@Data
public class TaskStatisticsPojo {

    /**
     * 总数量
     */
    private Integer total;

    /**
     * 实际外呼总数量
     */
    private Integer callTotal;

    /**
     * 已呼叫
     */
    private Integer calledAlready;

    /**
     * 实际已呼叫
     */
    private Integer actuallyCalledAlready;

    /**
     * 已接听
     */
    private Integer connected;

    /**
     * 未接通
     */
    private Integer notConnected;

    /**
     * 未接听
     */
    private Integer noAnswer;

    /**
     * 待拨打
     */
    private Integer toBeDialed;

    /**
     * 漏接
     */
    private Integer fumble;

    /**
     * 有效接听率
     */
    private BigDecimal connectRate;

    /**
     * 任务创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 重呼次数
     */
    private Integer recallCount;

    /**
     * 重呼间隔
     */
    private Integer recallMinute;

    /**
     * 单次批量外呼数量
     */
    private Integer singleCallNumber;

    /**
     * 状态 1未启动 2进行中 3已完成 4已暂停 5已撤销
     */
    private Integer status;

    /**
     * 呼叫坐席（员工工号/员工姓名）
     */
    private List<String> callTheSeatList;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 外呼日期，1-7，多个用逗号隔开
     */
    private String executionTime;

    /**
     * 外呼时段
     */
    private String executionCallTime;

    /**
     * 是否弹屏 1是 2否
     */
    private Integer isScreen;

    /**
     * 备注
     */
    private String remark;

//-----------------------------------------------------外呼效果-----------------------------------------------------------

    /**
     * 接听量
     */
    private Integer answeringVolume;

    /**
     * 接听率
     */
    private BigDecimal answerRate;

    /**
     * 呼损量 漏接
     */
    private Integer callLossVolume;

    /**
     * 接通量
     */
    private Integer throughput;

    /**
     * 接通率
     */
    private BigDecimal reachability;

    /**
     * 停机数量
     */
    private Integer shutDownCount;

    /**
     * 关机数量
     */
    private Integer powerOffCount;

    /**
     * 忙线数量
     */
    private Integer beBusyCount;

    /**
     * 通话中数量
     */
    private Integer onTheLineCount;

    /**
     * 无人接听数量
     */
    private Integer noAnswerCount;

    /**
     * 空号数量
     */
    private Integer vacantNumberCount;

    /**
     * 无法接通数量
     */
    private Integer unableToConnectCount;

    /**
     * 其他数量
     */
    private Integer otherQuantity;


}
