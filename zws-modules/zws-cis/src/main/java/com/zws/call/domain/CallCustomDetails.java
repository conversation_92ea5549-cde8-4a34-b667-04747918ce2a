package com.zws.call.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 客户资料导入记录详细信息表（预测试外呼功能）-实体类
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 10:57
 */
@Data
public class CallCustomDetails {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 客户uuid
     */
    private String uuid;

    /**
     * 机构id
     */
    private Integer teamId;

    /**
     * 员工id（员工工号匹配）
     */
    private Integer memberId;

    /**
     * 员工姓名
     */
    private String memberName;

    /**
     * 员工工号
     */
    @Excel(name = "所属坐席（工号）", sort = 2)
    private Integer userNo;

    /**
     * 导入客户记录表id
     */
    private Long recordId;

    /**
     * 客户名称
     */
    @Excel(name = "姓名", sort = 3)
    private String caseName;

    /**
     * 联系方式
     */
    @Excel(name = "联系电话", sort = 1)
    private String casePhone;

    /**
     * 产品名称
     */
    @Excel(name = "产品名称", sort = 4)
    private String productName;

    /**
     * 委托金额
     */
    @Excel(name = "债权总金额", sort = 5)
    private BigDecimal entrustMoney;

    /**
     * 剩余应还债权金额
     */
    @Excel(name = "剩余应还债权金额", sort = 6)
    private BigDecimal remainingDueMoney;

    /**
     * 剩余本金
     */
    @Excel(name = "剩余应还本金", sort = 7)
    private BigDecimal remainMoney;

    /**
     * 贷款时间
     */
    @Excel(name = "贷款时间", dateFormat = "yyyy-MM-dd", sort = 8)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date loanTime;

    /**
     * 逾期日期
     */
    @Excel(name = "逾期日期", dateFormat = "yyyy-MM-dd", sort = 9)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date overTime;

    /**
     * 创建人id
     */
    private Integer createById;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人类型（0-主账号;1-员工账号;2-资产端账号）
     */
    private Integer createType;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 备注时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date noteTime;

    /**
     * 备注人
     */
    private String noteBy;

    /**
     * 备注人id
     */
    private Integer noteById;

    /**
     * 备注
     */
    private String remark;

    /**
     * 备注人类型（0-主账号;1-员工账号;2-资产端账号）
     */
    private Integer noteByType;

    /**
     * 客户类型（1-导入客户，2-案件生成）
     */
    private Integer customerType;

    /**
     * 案件id
     */
    private Long caseId;

    /**
     * 联系人id
     */
    private Long contactsId;

    public String getNoteBy() {
        if (noteByType != null) {
            if (noteByType == 0) noteBy = cname;
            else if (noteByType == 1) noteBy = employeeName;
        }
        return noteBy;
    }

//------------------------------------------------客户列表参数------------------------------------------------------------

    /**
     * 文件原名称
     */
    private String originalFileName;

    /**
     * 批次名称
     */
    private String name;

//--------------------------------------------------搜索条件--------------------------------------------------------------

    /**
     * 员工id集合
     */
    private List<Integer> memberIdList;

    /**
     * 备注时间-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date noteTime1;

    /**
     * 备注时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date noteTime2;

    /**
     * 导入时间-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime1;

    /**
     * 导入时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime2;

    /**
     * 员工工号或者姓名搜索
     */
    private String employeeIdAndName;

    /**
     * 判断是否是搜索条件全选
     * （true-搜索结果全选，false-本页选中）
     */
    private Boolean condition;

    /**
     * 主键id集合
     */
    private List<Long> ids;

//--------------------------------------------------备注人姓名参数处理------------------------------------------------------

    /**
     * 员工名称
     */
    private String employeeName;

    /**
     * 机构名称
     */
    private String cname;

//--------------------------------------------------来电弹屏新增参数-------------------------------------------------------

    /**
     * 任务uuid-（预测试外呼任务）
     */
    private String taskUuid;

//--------------------------------------------------勾选案件创建任务增加参数-------------------------------------------------

    /**
     * 案件状态  0-未分配，1-已分配，2-停催，3-留案，4-退案 5-回收案件，6-案件结清
     */
    private String caseState;
}