package com.zws.call.service;

import com.zws.call.controller.request.WorkPhoneCustomDetailsRequest;
import com.zws.call.controller.response.WorkCustomDetailsResponse;
import com.zws.call.domain.CallCustomDetails;
import com.zws.call.pojo.ExportCustomPojo;
import com.zws.cis.domain.UrgeRecord;
import com.zws.common.core.domain.sms.InfoContact;

import java.util.List;
import java.util.Map;

/**
 * 客户资料导入记录详细信息（预测试外呼）-service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 10:57
 */
public interface ICallCustomDetailsService {

    /**
     * 根据部门id查询员工id
     *
     * @param deptId
     * @return
     */
    List<Integer> selectProgenyDept(Integer deptId, Integer teamId);

    /**
     * 客户列表查询参数处理
     *
     * @param customDetails
     * @return
     */
    Map<String, Object> verifyParameters(CallCustomDetails customDetails);

    /**
     * 客户列表信息查询
     *
     * @param customDetails
     * @return
     */
    List<CallCustomDetails> selectByList(CallCustomDetails customDetails);

    /**
     * 来电弹屏-客户信息查询
     *
     * @param customDetails
     * @return
     */
    List<CallCustomDetails> selectPopOnScreen(CallCustomDetails customDetails);
    /**
     * 工作手机来电弹屏-客户信息查询
     *
     * @param workPhoneCustomDetailsRequest
     * @return
     */
    List<WorkCustomDetailsResponse> selectWorkPopOnScreen(WorkPhoneCustomDetailsRequest workPhoneCustomDetailsRequest);

    /**
     * 来电弹屏-客户id查询
     *
     * @param customDetails
     * @return
     */
    List<Long> selectPopOnScreenIds(CallCustomDetails customDetails);

    /**
     * 客户列表信息查询--导出
     *
     * @param customDetails
     * @return
     */
    List<ExportCustomPojo> selectByListExport(CallCustomDetails customDetails);

    /**
     * 客户Id查询--删除
     *
     * @param customDetails
     * @return
     */
    List<Long> selectByListDelete(CallCustomDetails customDetails);

    /**
     * 根据客户Id删除
     *
     * @param ids
     * @return
     */
    int deleteByIdList(List<Long> ids);

    /**
     * 根据客户uuid查询客户对应员工id
     *
     * @param uuid
     * @return
     */
    Integer selectByCustomUuid(String uuid);

    /**
     * 根据客户uuid查询案件id以及联系人id
     *
     * @param uuid
     * @return
     */
    CallCustomDetails selectCaseIdOrContactsId(String uuid);

    /**
     * 根据案件联系人信息id查询案件联系人信息
     *
     * @param id
     * @return
     */
    InfoContact selectInfoContactById(Long id);

    /**
     * 根据催员id查找催员姓名
     *
     * @param id
     * @return
     */
    String selectOdvName(Integer id);

    /**
     * 根据团队id查找团队类型
     *
     * @param id
     * @return
     */
    String selectTeamLevelType(Integer id);

    /**
     * 写入催收记录
     *
     * @param urgeRecord
     * @return
     */
    int insertUrgeRecord(UrgeRecord urgeRecord);

    /**
     * 批量插入客户信息
     *
     * @param customDetails
     * @return
     */
    int insertList(List<CallCustomDetails> customDetails);

    /**
     * 根据id修改客户信息
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(CallCustomDetails record);
}
