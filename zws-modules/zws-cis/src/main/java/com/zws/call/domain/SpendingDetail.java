package com.zws.call.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zws.common.core.annotation.Excel;
import com.zws.common.core.web.domain.BaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电话套餐
 * @TableName spending_detail
 */
@TableName(value ="spending_detail")
public class SpendingDetail extends BaseEntity implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * sip id
     */
    @TableField(value = "sip_id")

    private Long sipId;

    /**
     * sip账号
     */
    @TableField(value = "sip_account")
    @Excel(name = "SIP账号")
    private String sipAccount;

    /**
     * 企业id
     */
    @TableField(value = "enterprise_id")
    private Long enterpriseId;

    /**
     * 企业名称
     */
    @TableField(value = "enterprise_name")
    @Excel(name = "企业名称")
    private String enterpriseName;

    /**
     * 项目id
     */
    @TableField(value = "project_id")
    private Long projectId;

    /**
     * 项目名称
     */
    @TableField(value = "project_name")
    @Excel(name = "项目名称")
    private String projectName;

    /**
     * 套餐id
     */
    @TableField(value = "plan_id")
    private Long planId;

    /**
     * 套餐名称
     */
    @TableField(value = "plan_name")
    @Excel(name = "套餐名称")
    private String planName;

    /**
     * 套餐月租
     */
    @TableField(value = "rental_fee")
    @Excel(name = "套餐月租（元/月）")
    private BigDecimal rentalFee;

    /**
     * 消费类型('0' 套餐消费，'1'通话消费)
     */
    @TableField(value = "consumption_type")
    private String consumptionType;

    /**
     * 套餐含免费时长
     */
    @TableField(value = "free_time")
    @Excel(name = "套餐含免费时长（分钟）")
    private Integer freeTime;

    /**
     * 单价
     */
    @TableField(value = "price")
    @Excel(name = "单价（拨出/分钟）")
    private BigDecimal price;

    /**
     * 总拨打时长
     */
    @TableField(value = "all_ext_duration")
    @Excel(name = "总拨打时长（分钟）")
    private Integer allExtDuration;

    /**
     * 消费合计
     */
    @TableField(value = "total")
    @Excel(name = "合计(元)")
    private BigDecimal total;

    /**
     * 备注
     */
    @TableField(value = "notes")
    private String notes;

    /**
     * 状态（0代表禁用 1代表启用）
     */
    @TableField(value = "status")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableField(value = "del_flag")
    private String delFlag;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getSipId() {
        return sipId;
    }

    public void setSipId(Long sipId) {
        this.sipId = sipId;
    }

    public String getSipAccount() {
        return sipAccount;
    }

    public void setSipAccount(String sipAccount) {
        this.sipAccount = sipAccount;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public BigDecimal getRentalFee() {
        return rentalFee;
    }

    public void setRentalFee(BigDecimal rentalFee) {
        this.rentalFee = rentalFee;
    }

    public String getConsumptionType() {
        return consumptionType;
    }

    public void setConsumptionType(String consumptionType) {
        this.consumptionType = consumptionType;
    }

    public Integer getFreeTime() {
        return freeTime;
    }

    public void setFreeTime(Integer freeTime) {
        this.freeTime = freeTime;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getAllExtDuration() {
        return allExtDuration;
    }

    public void setAllExtDuration(Integer allExtDuration) {
        this.allExtDuration = allExtDuration;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "SpendingDetail{" +
                "id=" + id +
                ", sipId=" + sipId +
                ", sipAccount='" + sipAccount + '\'' +
                ", enterpriseId=" + enterpriseId +
                ", enterpriseName='" + enterpriseName + '\'' +
                ", projectId=" + projectId +
                ", projectName='" + projectName + '\'' +
                ", planId=" + planId +
                ", planName='" + planName + '\'' +
                ", rentalFee=" + rentalFee +
                ", consumptionType='" + consumptionType + '\'' +
                ", freeTime=" + freeTime +
                ", price=" + price +
                ", allExtDuration=" + allExtDuration +
                ", total=" + total +
                ", notes='" + notes + '\'' +
                ", status='" + status + '\'' +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
