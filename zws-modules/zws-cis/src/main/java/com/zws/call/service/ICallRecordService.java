package com.zws.call.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zws.call.domain.CallRecord;
import com.zws.call.domain.CallSip;
import com.zws.call.pojo.AiCallCenterTaskPojo;
import com.zws.call.pojo.TeamPojo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/19 17:20
 */
public interface ICallRecordService extends IService<CallRecord> {


    long insert(CallRecord record);

    /**
     * 根据id修改话单-（预测试外呼）
     *
     * @param record
     * @return
     */
    int updateCallRecord(CallRecord record);

    /**
     * 根据手机号查询对应案件id
     *
     * @param teamPojo
     * @return
     */
    List<Long> selectCaseIdPhone(TeamPojo teamPojo);

    /**
     * 根据sip账号查询员工以及团队id
     *
     * @param sipNumber
     * @return
     */
    TeamPojo selectByTeamId(String sipNumber);

    /**
     * 根据案件id集合查询借款人
     *
     * @param caseIds
     * @return
     */
    List<String> getClientName(List<Long> caseIds);

    /**
     * 根据SIP账号查找
     *
     * @param sipNumber
     * @return
     */
    CallSip selectBySipNumber(String sipNumber);

    /**
     * 查询登录人的sip坐席信息
     *
     * @param map
     * @return
     */
    CallSip selectSipData(Map<String, Object> map);

    /**
     * 根据callid查找通话记录主键id
     *
     * @param callid
     * @return
     */
    Long selectByCallId(String callid);

    /**
     * 根据id修改sip坐席状态
     *
     * @param callSip
     * @return
     */
    int updateSipState(CallSip callSip);

    /**
     * 获取话单表的uuid
     * @param now
     * @param taskPojo
     * @return
     */
    List<String> selectTaskUuidList(Date now, AiCallCenterTaskPojo taskPojo,String callroter);
}
