package com.zws.call.pojo;

import com.zws.system.api.domain.IntelligenceTask;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 导入客户名单参数-实体类
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 16:11
 */
@Data
public class ImportDataPojo {

    /**
     * 文件路径
     */
    @NotBlank(message = "文件路径不能为空")
    private String fileUrl;

    /**
     * 原文件名
     */
    @NotBlank(message = "原文件名不能为空")
    private String originalFileName;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    @Size(min = 0, max = 30, message = "任务名称不能超过30个字符")
    private String taskName;

    /**
     * 正确数据缓存key
     */
    private String key;

    /**
     * 任务配置信息
     */
    private IntelligenceTask intelligenceTask;
}
