package com.zws.call.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 通话记录（AI语音通知）-实体类

 */
@Data
public class AiCallRecordDataPojo {

    /**
     * 主键id
     */
    private Long id;


    /**
     * 主叫号码
     */
    @Excel(name = "主叫号码", sort = 4)
    private String callFrom;

    /**
     * 被叫号码
     */
    @Excel(name = "被叫号码", sort = 2)
    private String callTo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 通话时间（呼叫时间）
     */
    @Excel(name = "通话时间", dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 3)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date callTime;


    /**
     * 员工姓名
     */
    private String employeeName;


    /**
     * 通话状态
     * 0=待拨打，1=已接通，2=未接通，
     */
    @Excel(name = "通话状态", readConverterExp = "0=待拨打,1=已接听,2=未接通,3=漏接,100=未接听", sort = 5)
    private Integer connectFlag;

    /**
     * 通话时长
     */
    @Excel(name = "通话时长", sort = 6,suffix = "秒")
    private Integer agentDuration;

    /**
     * 录音地址
     */
    private String recording;

    /**
     * 服务主机地址
     */
    private String serviceHost;

    /**
     * 号码状态
     */
    private String callResult;

    /**
     * 判断该案件是否属于本团队（0-不属于,1-属于）
     * -- saas 不需要做这个判断
     */
    private Integer button;

    /**
     * 删除标签
     */
    private String delFlag;

    /**
     * 短信发送状态
     */
    @Excel(name = "短信发送状态", readConverterExp = "0=发送成功,1=发送失败,2=发送中,3=发送失败", sort = 7)
    private Integer smsStatus;

    /**
     * 短信回复失败原因
     */
    private String result;

    /**
     * 短信回复时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date smsCreateTime;

    /**
     * 短信回复内容
     */
    @Excel(name = "短信回复内容", sort = 8)
    private String smsContent;

}
