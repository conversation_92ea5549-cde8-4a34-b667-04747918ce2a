package com.zws.call.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * AI语音通知任务统计-实体类

 */
@Data
public class AiTaskStatisticsPojo {

    /**
     * 总数量
     */
    private Integer total;


    /**
     * 已呼叫
     */
    private Integer calledAlready;


    /**
     * 已接通
     */
    private Integer connected;

    /**
     * 未接通
     */
    private Integer notConnected;


    /**
     * 任务创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 重呼次数
     */
    private Integer recallCount;

    /**
     * 重呼间隔
     */
    private Integer recallMinute;


    /**
     * 状态 1未启动 2进行中 3已完成 4已暂停 5已撤销 6失败
     */
    private Integer status;


    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 外呼日期，1-7，多个用逗号隔开
     */
    private String executionTime;

    /**
     * 任务开始时间
     */
    private String taskStartTime;

    /**
     * 任务结束时间
     */
    private String taskEndTime;

    /**
     * 外呼时段
     */
    private String executionCallTime;

    /**
     * 话术模板名称
     */
    private String templateName;

    /**
     * 机器人数量
     */
    private Integer robotCount;

    /**
     * 短信发送对象
     */
    private String senderTarget;

    /**
     * 短信模板内容
     */
    private String messageContent;


    /**
     * 备注
     */
    private String remark;

    /**
     * 接通率
     */
    private BigDecimal reachability;

    /**
     * 通话总时长
     */
    private Integer totalCallTime;

    /**
     * 通话平均时长
     */
    private Integer averageCallTime;

    /**
     * 任务进度
     */
    private BigDecimal taskCompleteSchedule;


//-----------------------------------------------------外呼效果-----------------------------------------------------------

    /**
     * 接听量
     */
    private Integer answeringVolume;

    /**
     * 接听率
     */
    private BigDecimal answerRate;

    /**
     * 呼损量 漏接
     */
    private Integer callLossVolume;

    /**
     * 接通量
     */
    private Integer throughput;


    /**
     * 停机数量
     */
    private Integer shutDownCount;

    /**
     * 关机数量
     */
    private Integer powerOffCount;

    /**
     * 忙线数量
     */
    private Integer beBusyCount;

    /**
     * 通话中数量
     */
    private Integer onTheLineCount;

    /**
     * 无人接听数量
     */
    private Integer noAnswerCount;

    /**
     * 空号数量
     */
    private Integer vacantNumberCount;

    /**
     * 无法接通数量
     */
    private Integer unableToConnectCount;

    /**
     * 其他数量
     */
    private Integer otherQuantity;






}
