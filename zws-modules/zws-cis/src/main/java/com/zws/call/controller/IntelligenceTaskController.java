package com.zws.call.controller;

import com.zws.call.agservice.AgIntelligenceTaskService;
import com.zws.call.domain.CallSip;
import com.zws.call.enums.CallStateEnum;
import com.zws.call.pojo.CallRecordDataPojo;
import com.zws.call.pojo.ImportDataPojo;
import com.zws.call.pojo.TaskStatisticsPojo;
import com.zws.call.service.ICallRecordService;
import com.zws.call.service.IIntelligenceTaskService;
import com.zws.cis.pojo.TeamStatePojo;
import com.zws.common.core.constant.FileConstant;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.IntelligenceTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预测试外呼任务管理-接口层
 *
 * @author: 马博新
 * @date ：Created in 2024/09/03 14:12
 */
@CrossOrigin
@RestController
@RequestMapping("/intelligenceTask")
public class IntelligenceTaskController extends BaseController {

    @Autowired
    private AgIntelligenceTaskService agIntelligenceTaskService;
    @Autowired
    private IIntelligenceTaskService intelligenceTaskService;
    @Autowired
    private ICallRecordService callRecordService;


    /**
     * 提交任务
     *
     * @param dataPojo 任务信息
     * @return
     * @throws Exception
     */
    @PostMapping("/submitTask")
    public AjaxResult submitTask(@Validated @RequestBody ImportDataPojo dataPojo) {
        if (ObjectUtils.isEmpty(dataPojo)) return AjaxResult.error("必填参数不能为空");
//        登录人信息
        ThreadEntityPojo threadEntityPojo = new ThreadEntityPojo();
        threadEntityPojo.setType(SecurityUtils.getAccountType());
        threadEntityPojo.setUser(SecurityUtils.getUsername());
        threadEntityPojo.setUserId(SecurityUtils.getUserId().intValue());
        threadEntityPojo.setCreateId(SecurityUtils.getTeamId().intValue());
        Integer integer = agIntelligenceTaskService.submitTaskData(dataPojo, threadEntityPojo);
        return AjaxResult.success("操作成功", integer);
    }

    /**
     * 预测试外呼列表-查询
     *
     * @param intelligenceTask 搜索条件
     * @return
     */
    @GetMapping("/listIntelligenceTask")
    public TableDataInfo listIntelligenceTask(IntelligenceTask intelligenceTask) {
        startPage();
        if (ObjectUtils.isEmpty(intelligenceTask)) intelligenceTask = new IntelligenceTask();
        List<IntelligenceTask> list = agIntelligenceTaskService.agListIntelligenceTask(intelligenceTask);
        return getDataTable(list);
    }

    /**
     * 任务名称下拉
     *
     * @return
     */
    @GetMapping("/getTaskNameList")
    public AjaxResult getTaskNameList() {
        List<String> list = intelligenceTaskService.selectTaskNameList(SecurityUtils.getTeamId().intValue());
        return AjaxResult.success("操作成功", list);
    }

    /**
     * 任务状态下拉
     *
     * @return
     */
    @GetMapping("/getTaskStatus")
    public AjaxResult getTaskStatus() {
        CallStateEnum[] startS = CallStateEnum.values();
        List<TeamStatePojo> list = new ArrayList<>();
        for (CallStateEnum temp : startS) {
            list.add(new TeamStatePojo(temp.getCode(), temp.getInfo()));
        }
        return AjaxResult.success("操作成功", list);
    }

    /**
     * 预测试外呼列表-执行
     *
     * @param intelligenceTask 搜索条件
     * @return
     */
    @PostMapping("/executeTask")
    public AjaxResult executeTask(@RequestBody IntelligenceTask intelligenceTask) {
        if (ObjectUtils.isEmpty(intelligenceTask)) return AjaxResult.error("必要参数不能为空");
        if (ObjectUtils.isEmpty(intelligenceTask.getIds())) return AjaxResult.error("id集合不能为空");
        intelligenceTask.setTeamId(SecurityUtils.getTeamId().intValue());
        intelligenceTask.setStatus(CallStateEnum.IN_PROGRESS.getCode());
        agIntelligenceTaskService.updateIntelligenceTask(intelligenceTask);
        return AjaxResult.success("操作成功");
    }

    /**
     * 预测试外呼列表-暂停
     *
     * @param intelligenceTask 搜索条件
     * @return
     */
    @PostMapping("/suspendTask")
    public AjaxResult suspendTask(@RequestBody IntelligenceTask intelligenceTask) {
        if (ObjectUtils.isEmpty(intelligenceTask)) return AjaxResult.error("必要参数不能为空");
        if (ObjectUtils.isEmpty(intelligenceTask.getIds())) return AjaxResult.error("id集合不能为空");
        intelligenceTask.setTeamId(SecurityUtils.getTeamId().intValue());
        intelligenceTask.setStatus(CallStateEnum.PAUSED.getCode());
        agIntelligenceTaskService.updateIntelligenceTask(intelligenceTask);
        return AjaxResult.success("操作成功");
    }

    /**
     * 预测试外呼列表-撤销
     *
     * @param intelligenceTask 搜索条件
     * @return
     */
    @PostMapping("/revokeTask")
    public AjaxResult revokeTask(@RequestBody IntelligenceTask intelligenceTask) {
        if (ObjectUtils.isEmpty(intelligenceTask)) return AjaxResult.error("必要参数不能为空");
        if (ObjectUtils.isEmpty(intelligenceTask.getIds())) return AjaxResult.error("id集合不能为空");
        intelligenceTask.setTeamId(SecurityUtils.getTeamId().intValue());
        intelligenceTask.setStatus(CallStateEnum.REVOKED.getCode());
        agIntelligenceTaskService.updateIntelligenceTask(intelligenceTask);
        return AjaxResult.success("操作成功");
    }

    /**
     * 预测试外呼列表-未接通重呼
     *
     * @param intelligenceTask 搜索条件
     * @return
     */
    @PostMapping("/notConnectRecallTask")
    public AjaxResult notConnectRecallTask(@RequestBody IntelligenceTask intelligenceTask) {
        if (ObjectUtils.isEmpty(intelligenceTask)) return AjaxResult.error("必要参数不能为空");
        if (ObjectUtils.isEmpty(intelligenceTask.getId())) return AjaxResult.error("id不能为空");
        List<Long> list = new ArrayList<>();
        list.add(intelligenceTask.getId());
        intelligenceTask.setTeamId(SecurityUtils.getTeamId().intValue());
        intelligenceTask.setStatus(CallStateEnum.IN_PROGRESS.getCode());
        intelligenceTask.setIds(list);
        agIntelligenceTaskService.notConnectRecallTask(intelligenceTask);
        return AjaxResult.success("操作成功");
    }

    /**
     * 查看结果报告-数据统计
     *
     * @param id 预测试外呼任务主键id
     * @return
     */
    @GetMapping("/callTaskStatistics")
    public AjaxResult callTaskStatistics(Long id) {
        if (id == null) return AjaxResult.error("id不能为空");
        TaskStatisticsPojo pojo = agIntelligenceTaskService.agCallTaskStatistics(id, SecurityUtils.getTeamId().intValue());
        return AjaxResult.success("操作成功", pojo);
    }

    /**
     * 查看结果报告-数据列表
     *
     * @param intelligenceTask 搜索条件
     * @return
     */
    @GetMapping("/callTaskDataList")
    public TableDataInfo callTaskDataList(IntelligenceTask intelligenceTask) {
//        startPage();
        if (ObjectUtils.isEmpty(intelligenceTask)) throw new GlobalException("必要参数不能为空");
        if (intelligenceTask.getId() == null) throw new GlobalException("主键id不能为空");
        intelligenceTask.setTeamId(SecurityUtils.getTeamId().intValue());
        List<CallRecordDataPojo> pojoList = agIntelligenceTaskService.agCallTaskDataList(intelligenceTask);
        return getDataTable(pojoList);
    }

    /**
     * 话单数据列表-导出
     *
     * @param response         导出数据
     * @param intelligenceTask 搜索条件
     * @throws Exception
     */
    @PostMapping("/exportTaskCdr")
    public void exportTaskCdr(HttpServletResponse response, @RequestBody IntelligenceTask intelligenceTask) throws Exception {
        if (ObjectUtils.isEmpty(intelligenceTask)) throw new GlobalException("必要参数不能为空");
        if (intelligenceTask.getId() == null) throw new GlobalException("主键id不能为空");
        intelligenceTask.setTeamId(SecurityUtils.getTeamId().intValue());
        List<CallRecordDataPojo> pojoList = agIntelligenceTaskService.agCallTaskDataList(intelligenceTask);
        if (!ObjectUtils.isEmpty(pojoList)) {
            for (CallRecordDataPojo row : pojoList) {
                String sipNumber = row.getSipNumber() == null ? "" : row.getSipNumber();
                String employeeName = row.getEmployeeName() == null ? "" : "(" + row.getEmployeeName() + ")";
                row.setEmployeeNameAndSip(sipNumber + employeeName);
            }
        }

        ExcelUtil<CallRecordDataPojo> util = new ExcelUtil<>(CallRecordDataPojo.class);
        String fileName = "话单数据列表（预测试外呼）" + FileConstant.getExcelSuffix();
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, pojoList, "话单数据列表（预测试外呼）");
    }

    /**
     * 修改坐席状态-(预测试外呼坐席)
     *
     * @param sipState 坐席状态（1=置闲，2=置忙）
     * @throws Exception
     */
    @PostMapping("/updateSipState")
    public AjaxResult updateSipState(Integer sipState) {
        if (sipState == null) throw new GlobalException("坐席状态不能为空");
        if (SecurityUtils.getAccountType() == null) throw new GlobalException("登录人信息获取失败");
        if (SecurityUtils.getAccountType() != 1) throw new GlobalException("该账号不是员工账号，无法修改坐席状态");
        agIntelligenceTaskService.updateSipState(sipState);
        return AjaxResult.success("操作成功");
    }

    /**
     * 查询坐席状态
     *
     * @return
     */
    @GetMapping("/getSipState")
    public AjaxResult getSipState() {
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("odvId", SecurityUtils.getUserId());
        CallSip callSip = callRecordService.selectSipData(map);
        if (ObjectUtils.isEmpty(callSip)) throw new GlobalException("当前用户坐席信息查询为空");
        return AjaxResult.success(callSip.getSipState() == null ? 1 : callSip.getSipState());
    }

//--------------------------------------------------勾选案件创建预测试外呼任务接口--------------------------------------------

    /**
     * 根据案件id集合查询有预测试外呼坐席的催员id-（勾选案件创建预测试外呼任务）
     *
     * @param entityPojo 登录人信息以及案件id集合
     * @return
     */
    @PostMapping("/verifyCaseList")
    public AjaxResult verifyCaseList(@RequestBody ThreadEntityPojo entityPojo) {
        if (ObjectUtils.isEmpty(entityPojo)) return AjaxResult.error("必填参数不能为空");
        if (ObjectUtils.isEmpty(entityPojo.getCaseIdList())) return AjaxResult.error("案件id集合不能为空");
        if (entityPojo.getCreateId() == null) return AjaxResult.error("团队id集合不能为空");
        List<Integer> list = agIntelligenceTaskService.verifyCaseList(entityPojo);
        return AjaxResult.success("操作成功", list);
    }

    /**
     * 根据案件id集合查询有预测试外呼坐席的催员id-（勾选案件创建预测试外呼任务）
     *
     * @param entityPojo 登录人信息以及案件id集合
     * @return
     */
    @PostMapping("/verifyCaseListAppeal")
    public AjaxResult verifyCaseListAppeal(@RequestBody ThreadEntityPojo entityPojo) {
        if (ObjectUtils.isEmpty(entityPojo)) return AjaxResult.error("必填参数不能为空");
        if (ObjectUtils.isEmpty(entityPojo.getCaseIdList())) return AjaxResult.error("案件id集合不能为空");
        if (entityPojo.getCreateId() == null) return AjaxResult.error("团队id集合不能为空");
        List<Integer> list = agIntelligenceTaskService.verifyCaseListAppeal(entityPojo);
        return AjaxResult.success("操作成功", list);
    }

    /**
     * 调诉端根据案件id集合查询有预测试外呼坐席的催员id-（勾选案件创建预测试外呼任务）
     *
     * @param entityPojo 登录人信息以及案件id集合
     * @return
     */
    @PostMapping("/verifyMediatorCaseList")
    public AjaxResult verifyMediatorCaseList(@RequestBody ThreadEntityPojo entityPojo) {
        if (ObjectUtils.isEmpty(entityPojo)) return AjaxResult.error("必填参数不能为空");
        if (ObjectUtils.isEmpty(entityPojo.getCaseIdList())) return AjaxResult.error("案件id集合不能为空");
        if (entityPojo.getCreateId() == null) return AjaxResult.error("团队id集合不能为空");
        List<Integer> list = agIntelligenceTaskService.verifyMediatorCaseList(entityPojo);
        return AjaxResult.success("操作成功", list);
    }

    /**
     * 调诉端根据案件id集合查询有预测试外呼坐席的催员id-（勾选案件创建预测试外呼任务）
     *
     * @param entityPojo 登录人信息以及案件id集合
     * @return
     */
    @PostMapping("/verifyStageCaseList")
    public AjaxResult verifyStageCaseList(@RequestBody ThreadEntityPojo entityPojo) {
        if (ObjectUtils.isEmpty(entityPojo)) return AjaxResult.error("必填参数不能为空");
        if (ObjectUtils.isEmpty(entityPojo.getCaseIdList())) return AjaxResult.error("案件id集合不能为空");
        if (entityPojo.getCreateId() == null) return AjaxResult.error("团队id集合不能为空");
        List<Integer> list = agIntelligenceTaskService.verifyStageCaseList(entityPojo);
        return AjaxResult.success("操作成功", list);
    }

    /**
     * 创建预测试外呼任务-（勾选案件）
     *
     * @param task 任务配置信息
     * @return
     */
    @PostMapping("/caseSubmitTaskData")
    public AjaxResult caseSubmitTaskData(@RequestBody IntelligenceTask task) {
        if (ObjectUtils.isEmpty(task)) return AjaxResult.error("任务配置信息不能为空");
        if (ObjectUtils.isEmpty(task.getEntityPojo())) return AjaxResult.error("必填参数不能为空");
        if (ObjectUtils.isEmpty(task.getEntityPojo().getCaseIdList())) return AjaxResult.error("案件id集合不能为空");
        if (task.getEntityPojo().getCreateId() == null) return AjaxResult.error("团队id集合不能为空");
        Integer integer = agIntelligenceTaskService.caseSubmitTaskData(task, task.getEntityPojo());
        return AjaxResult.success("操作成功", integer);
    }
}
