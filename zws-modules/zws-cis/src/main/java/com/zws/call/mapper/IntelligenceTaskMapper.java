package com.zws.call.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zws.call.domain.CallCustomDetails;
import com.zws.call.domain.CallRecord;
import com.zws.call.pojo.CallRecordDataPojo;
import com.zws.call.pojo.EmployeesPojo;
import com.zws.system.api.domain.CaseManage;
import com.zws.system.api.domain.IntelligenceTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface IntelligenceTaskMapper extends BaseMapper<CallRecord> {
    int deleteByPrimaryKey(Long id);

    int insert(IntelligenceTask record);

    int insertSelective(IntelligenceTask record);

    IntelligenceTask selectByPrimaryKey(Long id);

    /**
     * 根据主键id查询预测试外呼任务信息
     *
     * @param id
     * @return
     */
    IntelligenceTask selectById(@Param("id") Long id, @Param("teamId") Integer teamId);

    /**
     * 根据任务uuid查询主键id
     *
     * @param callCenterUuid
     * @return
     */
    IntelligenceTask selectByUuid(String callCenterUuid);

    /**
     * 根据任务名称机构内去重查询
     *
     * @param taskName
     * @param teamId
     * @return
     */
    Integer selectByCount(@Param("taskName") String taskName, @Param("teamId") Integer teamId);

    /**
     * 条件查询预测试外呼任务列表
     *
     * @param map
     * @return
     */
    List<IntelligenceTask> selectList(Map<String, Object> map);

    /**
     * 条件查询客户记录id
     *
     * @param map
     * @return
     */
    List<Long> selectRecordId(Map<String, Object> map);

    /**
     * id集合查找任务对应uuid
     *
     * @param map
     * @return
     */
    List<String> selectTaskUuidList(Map<String, Object> map);

    /**
     * 根据任务uuid查询已拨通的客户uuid
     *
     * @param map
     * @return
     */
    List<String> selectCustomUuidList(Map<String, Object> map);

    /**
     * 根据条件查询客户uuid
     *
     * @param map
     * @return
     */
    List<String> getNotConnectedCustomUuid(Map<String, Object> map);

    /**
     * 查询机构所有任务名称
     *
     * @param teamId
     * @return
     */
    List<String> selectTaskNameList(Integer teamId);

    /**
     * 统计话单各状态数量
     *
     * @param map
     * @return
     */
    Integer selectCallRecordCount(Map<String, Object> map);

    /**
     * 根据呼叫状态查询客户uuid
     *
     * @param map
     * @return
     */
    List<String> selectCallReloadList(Map<String, Object> map);

    /**
     * 查询任务对应的话单信息-（预测试外呼）
     *
     * @param map
     * @return
     */
    List<CallRecordDataPojo> selectCallRecordList(Map<String, Object> map);

    /**
     * 根据员工id集合查询员工信息
     *
     * @param map
     * @return
     */
    List<EmployeesPojo> selectEmployeesData(Map<String, Object> map);

    /**
     * 根据案件id集合查询催员id集合信息
     *
     * @param map
     * @return
     */
    List<Integer> selectEmployeeIdList(Map<String, Object> map);


    List<Integer> selectTsEmployeeIdList(Map<String, Object> map);

    List<Integer> selectStEmployeeIdList(Map<String, Object> map);

    /**
     * 查询有预测试外呼坐席的催员id
     *
     * @param map
     * @return
     */
    List<Integer> selectSipOdvIdList(Map<String, Object> map);


    List<Integer> selectSipMediatorIdList(Map<String, Object> map);

    /**
     * 根据案件id集合查询联系人信息
     *
     * @param map
     * @return
     */
    List<CallCustomDetails> selectContactsList(Map<String, Object> map);

    /**
     * 根据案件id查询案件详情
     *
     * @param caseId
     * @return
     */
    CaseManage selectCaseManageCaseId(Long caseId);

    /**
     * 根据id修改
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(IntelligenceTask record);

    /**
     * 根据任务uuid修改
     *
     * @param record
     * @return
     */
    int updateByUuidList(IntelligenceTask record);

    int updateByPrimaryKeyWithBLOBs(IntelligenceTask record);

    int updateByPrimaryKey(IntelligenceTask record);


    List<Integer> selectEmployeeIdListAppeal(Map<String, Object> map);
}