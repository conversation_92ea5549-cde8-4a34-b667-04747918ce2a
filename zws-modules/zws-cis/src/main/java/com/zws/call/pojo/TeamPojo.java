package com.zws.call.pojo;

import lombok.Data;

import java.util.List;

/**
 * 团队
 *
 * <AUTHOR>
 * @date ：Created in 2022/3/3 14:19
 */
@Data
public class TeamPojo {

    /**
     * 团队id集合
     */
    private List<Integer> teamIdList;

    /**
     * 团队ID
     */
    private Long id;
    /**
     * 团队名称
     */
    private String cname;
    /**
     * 团队类别(从字典表获取)
     */
    private String category;

    /**
     * 入网企业编号
     */
    private String companyNum;

    /**
     * 删除标识
     */
    private Integer deleteLogo;

    /**
     * 登录账号
     */
    private String account;

    /**
     * 合作状态（0：合作中；1：合作暂停；2：合作关闭）
     */
    private Integer cooperation;

    /**
     * 结佣日
     */
    private Integer settlementDay;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 操作记录查看按钮置灰（1-置灰，2-不置灰）
     */
    private Integer rateButton;

    /**
     * 员工id
     */
    private Long userId;

    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 手机号码
     */
    private String contactPhone;
}
