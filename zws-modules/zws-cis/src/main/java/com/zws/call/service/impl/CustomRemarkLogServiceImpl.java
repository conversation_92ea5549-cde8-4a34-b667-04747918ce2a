package com.zws.call.service.impl;

import com.zws.call.domain.CustomRemarkLog;
import com.zws.call.mapper.CustomRemarkLogMapper;
import com.zws.call.service.ICustomRemarkLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 备注记录-service层
 *
 * @author: 马博新
 * @date ：Created in 2024/09/03 11:03
 */
@Service
public class CustomRemarkLogServiceImpl implements ICustomRemarkLogService {

    @Resource
    private CustomRemarkLogMapper mapper;


    /**
     * 写入备注记录
     *
     * @param record
     * @return
     */
    @Override
    public int insert(CustomRemarkLog record) {
        return mapper.insert(record);
    }
}
