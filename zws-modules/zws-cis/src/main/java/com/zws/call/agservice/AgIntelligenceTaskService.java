package com.zws.call.agservice;

import cn.hutool.core.util.IdUtil;
import com.zws.call.api.CallingTaskDockingApi;
import com.zws.call.config.CallConfig;
import com.zws.call.domain.CallCustomDetails;
import com.zws.call.domain.CallCustomRecord;
import com.zws.call.domain.CallRecord;
import com.zws.call.domain.CallSip;
import com.zws.call.enums.CallStateEnum;
import com.zws.call.enums.CallTaskMarkEnum;
import com.zws.call.mapper.CallRecordMapper;
import com.zws.call.pojo.*;
import com.zws.call.service.ICallCustomDetailsService;
import com.zws.call.service.ICallCustomRecordService;
import com.zws.call.service.ICallRecordService;
import com.zws.call.service.IIntelligenceTaskService;
import com.zws.common.core.callcenter.enums.CallResultEnum;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.utils.SplitUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.CaseManage;
import com.zws.system.api.domain.IntelligenceTask;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 预测试外呼任务管理-业务处理层
 *
 * @author: 马博新
 * @date ：Created in 2024/09/03 14:14
 */
@Slf4j
@Component
public class AgIntelligenceTaskService {

    @Autowired
    private IIntelligenceTaskService intelligenceTaskService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ICallCustomRecordService customRecordService;
    @Autowired
    private ICallCustomDetailsService customDetailsService;
    @Autowired
    private CallConfig callConfig;
    @Autowired
    private CallingTaskDockingApi callingTaskDockingApi;
    @Autowired
    private ICallRecordService callRecordService;
    @Autowired
    private CallRecordMapper callRecordMapper;

    /**
     * 提交任务-写入数据
     *
     * @param dataPojo
     * @param threadEntityPojo
     * @return
     */
    @Transactional
    public Integer submitTaskData(ImportDataPojo dataPojo, ThreadEntityPojo threadEntityPojo) {
        if (ObjectUtils.isEmpty(dataPojo.getKey())) throw new GlobalException("正确数据key不能为空");
        if (!redisService.hasKey(dataPojo.getKey())) throw new GlobalException("正确数据缓存不存在或已过期");
        List<CallCustomDetails> list = redisService.getCacheList(dataPojo.getKey(), CallCustomDetails.class);
        if (ObjectUtils.isEmpty(list)) throw new GlobalException("正确数据缓存查询为空");
//        写入导入客户记录表并返回主键id
        CallCustomRecord record = new CallCustomRecord();
//        record.setName(dataPojo.getTaskName());
        record.setFileUrl(dataPojo.getFileUrl());
        record.setOriginalFileName(dataPojo.getOriginalFileName());
        record.setCreateId(threadEntityPojo.getUserId());
        record.setCreateBy(threadEntityPojo.getUser());
        record.setCreateType(threadEntityPojo.getType());
        record.setTeamId(threadEntityPojo.getCreateId());
        record.setCustomerType(1);
        Long aLong = customRecordService.insert(record);

        List<CallCustomDataPojo> dataPojoList = new ArrayList<>();
//        批量写入客户信息
        for (CallCustomDetails row : list) {
//            生成客户uuid，与呼叫中心保持一致进行话单关联
            //生成的是不带-的字符串，类似于：b17f24ff026d40949c85a24f4f375d42
            String simpleUUID = IdUtil.simpleUUID();
            row.setUuid(simpleUUID);
            row.setRecordId(aLong);
            row.setCreateTime(new Date());
            row.setCreateType(threadEntityPojo.getType());
            row.setTeamId(threadEntityPojo.getCreateId());
            if (row.getUserNo() != null) {
                Map<String, Object> hashMap = new HashMap<>();
                hashMap.put("teamId", threadEntityPojo.getCreateId());
                hashMap.put("employeesWorking", row.getUserNo());
                Integer integer = customRecordService.selectEmployeesId(hashMap);
                row.setMemberId(integer);
            }
            row.setCreateById(threadEntityPojo.getUserId());
            row.setDelFlag(BaseConstant.DelFlag_Being);
            row.setCustomerType(1);

//            处理推送给呼叫中心的客户信息
            CallCustomDataPojo data = new CallCustomDataPojo();
            data.setPhone(row.getCasePhone());
            if (row.getMemberId() != null) {
//                根据员工id查询该员工的坐席Sip账号
                Map<String, Object> map = new HashMap<>();
                map.put("teamId", threadEntityPojo.getCreateId());
                map.put("odvId", row.getMemberId());
                String sip = customRecordService.selectSip(map);
                data.setBelongSip(sip);
            } else {
                data.setUseAllocatedSip(1);
            }
            data.setCustomerId(row.getUuid());
            dataPojoList.add(data);
        }
        customDetailsService.insertList(list);
//        插入任务信息
        IntelligenceTask task = dataPojo.getIntelligenceTask();
        checkTaskData(task);
        task.setTaskName(dataPojo.getTaskName());
        task.setTeamId(threadEntityPojo.getCreateId());
        task.setEmployeeId(threadEntityPojo.getUserId());
        task.setCreateBy(threadEntityPojo.getUser());
        task.setCreateTime(new Date());
        task.setCreateId(threadEntityPojo.getUserId());
        task.setCreateType(threadEntityPojo.getType());
        task.setCustomId(aLong);
        task.setStatus(CallStateEnum.IN_PROGRESS.getCode());
        task.setOperationType(1);
        task.setDelFlag(BaseConstant.DelFlag_Being);
//        呼叫总数量
        task.setCallCount(list.size());
        Long idLong = intelligenceTaskService.insert(task);
//        任务信息推送给呼叫中心
        return callCenterDocking(task, dataPojoList, idLong);
    }

    /**
     * 任务数据推送给呼叫中心
     *
     * @param task
     * @param dataPojoList
     * @param idLong       任务主键id
     */
    public Integer callCenterDocking(IntelligenceTask task, List<CallCustomDataPojo> dataPojoList, Long idLong) {
        if (idLong == null) throw new GlobalException("任务主键id为空");
//        根据机构id查询机构企业编码
        EnterpriseDataPojo dataPojo = customRecordService.selectCompanyNum(task.getTeamId());
        if (ObjectUtils.isEmpty(dataPojo)) throw new GlobalException("企业信息查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(callConfig.getApiFlag()))
            throw new GlobalException("企业api标识查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(dataPojo.getCompanyNum()))
            throw new GlobalException("企业编码查询为空，该机构无法创建任务");
        CallCenterTaskPojo centerTaskPojo = new CallCenterTaskPojo();
        centerTaskPojo.setCallList(dataPojoList);
//        centerTaskPojo.setApiFlag(dataPojo.getApiFlag());  //企业api标识
        centerTaskPojo.setEnterpriseNum(dataPojo.getCompanyNum());  //企业编号
        centerTaskPojo.setTaskName(task.getTaskName());
        centerTaskPojo.setExecutionTime(task.getExecutionTime());
        centerTaskPojo.setExecutionCallTime(task.getExecutionCallTime());
        centerTaskPojo.setRecall(task.getRecallCount());
        centerTaskPojo.setRecallMinute(task.getRecallMinute());
        centerTaskPojo.setIsScreen(task.getIsScreen());
        centerTaskPojo.setSingleCallNumber(task.getSingleCallNumber());
        centerTaskPojo.setRemark(task.getRemark());
        centerTaskPojo.setAllocatedSip(task.getTaskAllocationPersonnelSeating());
        centerTaskPojo.setAnswerSettings(task.getAnswerSettings());
        centerTaskPojo.setTaskFlag(task.getOperationType());
        centerTaskPojo.setServiceHostUrl(callConfig.getHostUrl());
        centerTaskPojo.setApiFlag(callConfig.getApiFlag());  //企业api标识
        CallTaskReturnPojo returnPojo = callingTaskDockingApi.submitCallTask(centerTaskPojo);

//        将任务uuid更新写入数据库
        IntelligenceTask intelligenceTask = new IntelligenceTask();
        intelligenceTask.setId(idLong);
        intelligenceTask.setCallCenterUuid(returnPojo.getTaskUuid());
        intelligenceTask.setUpdateTime(new Date());
        intelligenceTaskService.updateById(intelligenceTask);
        System.err.println("呼叫中心返回状态：" + returnPojo.getHasIdleSeat());
        return returnPojo.getHasIdleSeat() == null ? 0 : returnPojo.getHasIdleSeat();
    }

    /**
     * 校验预测试外呼任务参数
     *
     * @param task
     */
    public void checkTaskData(IntelligenceTask task) {
        if (ObjectUtils.isEmpty(task)) throw new GlobalException("任务配置信息不能为空");
        if (ObjectUtils.isEmpty(task.getExecutionTime())) throw new GlobalException("执行时间不能为空");
        if (ObjectUtils.isEmpty(task.getExecutionCallTime())) throw new GlobalException("外呼时段不能为空");
        if (task.getRecallCount() == null) throw new GlobalException("重呼次数不能为空");
        if (task.getRecallCount() > 3) throw new GlobalException("针对某号码第一次外呼未接通，重呼的次数，不能超过3次");
        if (task.getRecallCount() != 0) {
            if (task.getRecallMinute() == null) throw new GlobalException("重呼间隔不能为空");
            if (task.getRecallMinute() < 5 || task.getRecallMinute() > 120)
                throw new GlobalException("重呼间隔应在5-120分钟范围内");
        }
        if (task.getIsScreen() == null) throw new GlobalException("是否弹屏不能为空");
        if (task.getSingleCallNumber() == null) throw new GlobalException("单次批量外呼数量不能为空");
        if (task.getSingleCallNumber() > 30) throw new GlobalException("单次批量外呼数量不能超过30");
    }

    /**
     * 预测试外呼列表-查询
     *
     * @param intelligenceTask
     * @return
     */
    public List<IntelligenceTask> agListIntelligenceTask(IntelligenceTask intelligenceTask) {
//        查询列表数据
        List<IntelligenceTask> intelligenceTasks = intelligenceTaskService.selectList(intelligenceTask);
        if (ObjectUtils.isEmpty(intelligenceTasks)) return intelligenceTasks;
        for (IntelligenceTask row : intelligenceTasks) {
            List<String> stringList = new ArrayList<>();  //客户uuid
//            根据任务uuid查询统计已接听数量
            Map<String, Object> map = new HashMap<>();
            map.put("teamId", SecurityUtils.getTeamId());
            map.put("taskUuid", row.getCallCenterUuid());
            map.put("connectFlag", CallTaskMarkEnum.CONNECTED.getCode());
            int connected = 0;
            List<String> reloadList = intelligenceTaskService.selectCallReloadList(map);
            if (!ObjectUtils.isEmpty(reloadList)) {
                connected = reloadList.size();
                stringList.addAll(reloadList);
            }
//            根据任务uuid查询统计未接通数量
            map.put("customUuidList", stringList);
            map.put("connectFlag", CallTaskMarkEnum.NOT_CONNECTED.getCode());
            int notConnected = 0;
            List<String> strings = intelligenceTaskService.selectCallReloadList(map);
            if (!ObjectUtils.isEmpty(strings)) {
                notConnected = strings.size();
                stringList.addAll(strings);
            }

            TaskStatisticsPojo statisticsPojo = getTaskStatisticsPojoCount(row.getCallCenterUuid());

            //未接通：黑名单＋拦截+无法接通+空号（呼叫未到被叫侧）。
            int notConnected2 = statisticsPojo.getOtherQuantity() + statisticsPojo.getVacantNumberCount() + statisticsPojo.getUnableToConnectCount();

//            根据任务uuid查询统计漏接数量
            map.put("customUuidList", stringList);
            map.put("connectFlag", CallTaskMarkEnum.FUMBLE.getCode());
            int fumble = 0;
            List<String> list = intelligenceTaskService.selectCallReloadList(map);
            if (!ObjectUtils.isEmpty(list)) {
                fumble = list.size();
                stringList.addAll(list);
            }
//            呼叫总数量
            int total = row.getCallCount() == null ? 0 : row.getCallCount();
            //实际话单数量
//            Long callTotalCount = getCallTotalCount(row.getCallCenterUuid(), null);

            //未接听  已接通情况下除了（已接听和漏接）的情况： 通话中+无人接听+停机+忙线+关机等呼叫到达被叫侧；
            Long noAnswerCount = getNoAnswerCount(row.getCallCenterUuid());

//        查找漏接的话单数量
            map.put("connectFlag", CallTaskMarkEnum.FUMBLE.getCode());
            Integer fumbleCount = intelligenceTaskService.selectCallRecordCount(map);
//            已接通数量（已接听+漏接）
//            int connectedTotal = connected + fumble;
            // 接通量（已接听+漏接+未接听）
            int connectedTotal = connected + fumbleCount + noAnswerCount.intValue();
//            已呼叫数量
            int calledAlready = connected + notConnected + fumble;

            //实际已呼叫数量 =
            // 已接听 + 漏接
            // 未接通（黑名单＋拦截+无法接通+空号（呼叫未到被叫侧）） +
            // 未接听（通话中+无人接听+停机+忙线+关机）
            int actuallyCalledAlready = connected + fumbleCount + notConnected2 + noAnswerCount.intValue();

            //计算接通率
            BigDecimal bigDecimal = BigDecimal.ZERO;
            if (connectedTotal == 0 || actuallyCalledAlready == 0) bigDecimal = BigDecimal.ZERO;
            else
//                bigDecimal = new BigDecimal(connectedTotal).divide(new BigDecimal(row.getCallCount()), 4, BigDecimal.ROUND_HALF_UP);
                bigDecimal = new BigDecimal(connectedTotal).divide(new BigDecimal(actuallyCalledAlready), 4, BigDecimal.ROUND_HALF_UP);
//            row.setCallCompletionRate(bigDecimal.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
            //接通率：接通量÷呼叫总数x100%
            row.setCallCompletionRate(bigDecimal.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
//            计算任务完成进度(任务进度=已呼叫/总数量)
            BigDecimal taskProgress = BigDecimal.ZERO;
            if (total == 0 || calledAlready == 0) bigDecimal = BigDecimal.ZERO;
            else
                taskProgress = new BigDecimal(calledAlready).divide(new BigDecimal(total), 4, BigDecimal.ROUND_HALF_UP);
            row.setTaskProgress(taskProgress.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
        }
        return intelligenceTasks;
    }

    /**
     * 预测试外呼-更新任务
     *
     * @param intelligenceTask
     * @return
     */
    @Transactional
    public void updateIntelligenceTask(IntelligenceTask intelligenceTask) {
//        根据任务主键id集合查询任务对应的uuid
        Map<String, Object> map = new HashMap<>();
        map.put("idList", intelligenceTask.getIds());
        map.put("teamId", intelligenceTask.getTeamId());
        List<String> list = intelligenceTaskService.selectTaskUuidList(map);
        if (ObjectUtils.isEmpty(list)) throw new GlobalException("任务查询为空");
//        根据机构id查询机构企业编码
        EnterpriseDataPojo dataPojo = customRecordService.selectCompanyNum(intelligenceTask.getTeamId());
        if (ObjectUtils.isEmpty(dataPojo)) throw new GlobalException("企业信息查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(callConfig.getApiFlag()))
            throw new GlobalException("企业api标识查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(dataPojo.getCompanyNum()))
            throw new GlobalException("企业编码查询为空，该机构无法创建任务");
        CallCenterTaskPojo centerTaskPojo = new CallCenterTaskPojo();
        centerTaskPojo.setApiFlag(callConfig.getApiFlag());  //企业api标识
        centerTaskPojo.setEnterpriseNum(dataPojo.getCompanyNum());  //企业编号
        centerTaskPojo.setTaskUuids(list);
        centerTaskPojo.setStatus(intelligenceTask.getStatus());
        centerTaskPojo.setServiceHostUrl(callConfig.getHostUrl());
//        根据任务uuid修改任务状态
        IntelligenceTask task = new IntelligenceTask();
        task.setCallCenterUuidList(list);
        task.setStatus(intelligenceTask.getStatus());
        task.setUpdateTime(new Date());
        intelligenceTaskService.updateByUuidList(task);
//        推送给呼叫中心
        callingTaskDockingApi.updateCallTask(centerTaskPojo);
    }

    /**
     * 预测试外呼-未接通重呼
     *
     * @param intelligenceTask
     * @return
     */
    @Transactional
    public void notConnectRecallTask(IntelligenceTask intelligenceTask) {
//        根据任务主键id集合查询任务对应的uuid
        Map<String, Object> map = new HashMap<>();
        map.put("idList", intelligenceTask.getIds());
        map.put("teamId", intelligenceTask.getTeamId());
        List<String> list = intelligenceTaskService.selectTaskUuidList(map);
        if (ObjectUtils.isEmpty(list)) throw new GlobalException("任务查询为空");
        String taskUuid = list.get(0);  //任务uuid
//        根据机构id查询机构企业编码
        EnterpriseDataPojo dataPojo = customRecordService.selectCompanyNum(intelligenceTask.getTeamId());
        if (ObjectUtils.isEmpty(dataPojo)) throw new GlobalException("企业信息查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(callConfig.getApiFlag()))
            throw new GlobalException("企业api标识查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(dataPojo.getCompanyNum()))
            throw new GlobalException("企业编码查询为空，该机构无法创建任务");
//        根据任务uuid查询存在的客户uuid，查找已拨通的客户uuid
        map.put("idList", null);
        map.put("taskUuid", taskUuid);
        List<String> stringList = intelligenceTaskService.selectCustomUuidList(map);
        List<String> customUuidList = new ArrayList<>();
        if (ObjectUtils.isEmpty(stringList)) {
//          如果拨通客户为空，则查询该任务所有客户uuid信息
            customUuidList = intelligenceTaskService.getNotConnectedCustomUuid(map);
        } else {
//            如果不为空，则查找该任务uuid对应的未拨通的客户uuid
            map.put("customUuidList", stringList);
            customUuidList = intelligenceTaskService.getNotConnectedCustomUuid(map);
        }
        if (ObjectUtils.isEmpty(customUuidList)) throw new GlobalException("该任务已经不存在未拨通的客户");
        CallCenterTaskPojo centerTaskPojo = new CallCenterTaskPojo();
        centerTaskPojo.setApiFlag(callConfig.getApiFlag());  //企业api标识
        centerTaskPojo.setEnterpriseNum(dataPojo.getCompanyNum());  //企业编号
        centerTaskPojo.setTaskUuid(taskUuid);
        centerTaskPojo.setServiceHostUrl(callConfig.getHostUrl());
        centerTaskPojo.setCustomerIds(customUuidList);
//        根据任务uuid修改任务状态
        IntelligenceTask task = new IntelligenceTask();
        task.setCallCenterUuidList(list);
        task.setStatus(intelligenceTask.getStatus());
        task.setUpdateTime(new Date());
        intelligenceTaskService.updateByUuidList(task);
//        推送给呼叫中心
        callingTaskDockingApi.notConnectedCallTask(centerTaskPojo);
    }

    /**
     * 查看结果报告-数据统计
     *
     * @param id     主键id
     * @param teamId 机构id
     * @return
     */
    public TaskStatisticsPojo agCallTaskStatistics(Long id, Integer teamId) {
//        查询列表数据
        IntelligenceTask intelligenceTask = intelligenceTaskService.selectById(id, teamId);
        if (ObjectUtils.isEmpty(intelligenceTask)) throw new GlobalException("主键不存在，查询任务信息为空");
        if (ObjectUtils.isEmpty(intelligenceTask.getCallCenterUuid())) throw new GlobalException("uuid为空，数据不存在");

        Map<String, Object> map = new HashMap<>();
        TaskStatisticsPojo statisticsPojo = getTaskStatisticsPojoCount(intelligenceTask.getCallCenterUuid());

//        根据任务uuid查询统计已接听数量
        List<String> stringList = new ArrayList<>();  //客户uuid
//            根据任务uuid查询统计已接听数量
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("taskUuid", intelligenceTask.getCallCenterUuid());
        map.put("connectFlag", CallTaskMarkEnum.CONNECTED.getCode());
        int connected = 0;
        List<String> reloadList = intelligenceTaskService.selectCallReloadList(map);
        if (!ObjectUtils.isEmpty(reloadList)) {
            connected = reloadList.size();
            stringList.addAll(reloadList);
        }
//            根据任务uuid查询统计未接通数量
        map.put("customUuidList", stringList);
        map.put("connectFlag", CallTaskMarkEnum.NOT_CONNECTED.getCode());
        int notConnected2 = 0;
        List<String> strings = intelligenceTaskService.selectCallReloadList(map);
        if (!ObjectUtils.isEmpty(strings)) {
            notConnected2 = strings.size();
            stringList.addAll(strings);
        }
//            根据任务uuid查询统计漏接数量
        map.put("customUuidList", stringList);
        map.put("connectFlag", CallTaskMarkEnum.FUMBLE.getCode());
        int fumble = 0;
        List<String> list = intelligenceTaskService.selectCallReloadList(map);
        if (!ObjectUtils.isEmpty(list)) {
            fumble = list.size();
            stringList.addAll(list);
        }

        //未接通：黑名单＋拦截+无法接通+空号（呼叫未到被叫侧）。
        int notConnected = statisticsPojo.getOtherQuantity() + statisticsPojo.getVacantNumberCount() + statisticsPojo.getUnableToConnectCount();

//        查找已接听的话单数量
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("teamId", SecurityUtils.getTeamId());
        hashMap.put("taskUuid", intelligenceTask.getCallCenterUuid());
        hashMap.put("connectFlag", CallTaskMarkEnum.CONNECTED.getCode());
        Integer connectedCount = intelligenceTaskService.selectCallRecordCount(hashMap);
//        查找漏接的话单数量
        hashMap.put("connectFlag", CallTaskMarkEnum.FUMBLE.getCode());
        Integer fumbleCount = intelligenceTaskService.selectCallRecordCount(hashMap);

        //未接听  已接通情况下除了（已接听和漏接）的情况： 通话中+无人接听+停机+忙线+关机等呼叫到达被叫侧；
        Long noAnswerCount = getNoAnswerCount(intelligenceTask.getCallCenterUuid());

//        呼叫总数量
        int total = intelligenceTask.getCallCount() == null ? 0 : intelligenceTask.getCallCount();
        //所有话单数量
//        Long callTotalCount = getCallTotalCount(intelligenceTask.getCallCenterUuid(), null);

        //实际已呼叫数量 =
        // 已接听 + 漏接
        // 未接通（黑名单＋拦截+无法接通+空号（呼叫未到被叫侧）） +
        // 未接听（通话中+无人接听+停机+忙线+关机）
        int actuallyCalledAlready = connected + fumbleCount + notConnected + noAnswerCount.intValue();

//        总的接通数量
        int connectQuantity = connectedCount + fumbleCount;
//        计算接听率（已接听/接通数量）
        BigDecimal answerRate = BigDecimal.ZERO;
        if (connectedCount == 0 || actuallyCalledAlready == 0) answerRate = BigDecimal.ZERO;
        else
//            answerRate = new BigDecimal(connectedCount).divide(new BigDecimal(connectQuantity), 4, BigDecimal.ROUND_HALF_UP);
            answerRate = new BigDecimal(connectedCount).divide(new BigDecimal(actuallyCalledAlready), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal bigDecimal = answerRate.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);

//        已呼叫任务数量
        int calledAlready = connected + notConnected2 + fumble;
////        计算接通率
//        BigDecimal bigDecimal = BigDecimal.ZERO;
//        if (connected == 0 || calledAlready == 0) bigDecimal = BigDecimal.ZERO;
//        else bigDecimal = new BigDecimal(connected).divide(new BigDecimal(calledAlready), 4, BigDecimal.ROUND_HALF_UP);
//        已接通数量
        int connectedFumble = connected + fumble;
        //接通量：接听+漏接＋未接听（呼叫到达被叫侧）
        Integer throughput = connected + fumbleCount + noAnswerCount.intValue();
        //接通率：接通量÷呼叫总数x100%
        BigDecimal reachability;
        if (actuallyCalledAlready <= 0) {
            // 如果总呼叫数为 0 或空，接通率设为 0
            reachability = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            // 使用 divide 方法时指定舍入模式
            reachability = new BigDecimal(throughput)
                    .divide(new BigDecimal(actuallyCalledAlready), 4, BigDecimal.ROUND_HALF_UP) // 保留 4 位小数
                    .multiply(BigDecimal.valueOf(100)) // 转换为百分比
                    .setScale(2, BigDecimal.ROUND_HALF_UP); // 最终保留 2 位小数
        }

        //返回的统计数据
        statisticsPojo.setTotal(total);
//        statisticsPojo.setCallTotal(callCount);
        statisticsPojo.setCalledAlready(calledAlready);
        statisticsPojo.setActuallyCalledAlready(actuallyCalledAlready);
        statisticsPojo.setConnected(connected);
        statisticsPojo.setNotConnected(notConnected);
        statisticsPojo.setToBeDialed(total - calledAlready);
        statisticsPojo.setFumble(fumbleCount);
        statisticsPojo.setConnectRate(bigDecimal);
        statisticsPojo.setCreateTime(intelligenceTask.getCreateTime());
        statisticsPojo.setRecallCount(intelligenceTask.getRecallCount());
        statisticsPojo.setRecallMinute(intelligenceTask.getRecallMinute());
        statisticsPojo.setSingleCallNumber(intelligenceTask.getSingleCallNumber());
        statisticsPojo.setStatus(intelligenceTask.getStatus());
        statisticsPojo.setTaskName(intelligenceTask.getTaskName());
        statisticsPojo.setExecutionCallTime(intelligenceTask.getExecutionCallTime());
        statisticsPojo.setExecutionTime(intelligenceTask.getExecutionTime());
        statisticsPojo.setIsScreen(intelligenceTask.getIsScreen());
        statisticsPojo.setRemark(intelligenceTask.getRemark());
        statisticsPojo.setNoAnswer(noAnswerCount.intValue());
        statisticsPojo.setThroughput(throughput);
        statisticsPojo.setReachability(reachability);

//        根据员工id查找员工工号以及姓名
        String taskAllocationPersonnelId = intelligenceTask.getTaskAllocationPersonnelId();
        if (!ObjectUtils.isEmpty(taskAllocationPersonnelId)) {
//            获取分割员工id
            List<Integer> integerList = SplitUtils.splitCharacterInteger(taskAllocationPersonnelId);
            if (!ObjectUtils.isEmpty(integerList)) {
//            查找员工工号以及姓名
                Map<String, Object> objectMap = new HashMap<>();
                objectMap.put("teamId", teamId);
                objectMap.put("idList", integerList);
                List<EmployeesPojo> pojoList = intelligenceTaskService.selectEmployeesData(objectMap);
                if (!ObjectUtils.isEmpty(pojoList)) {
                    List<String> nameList = new ArrayList<>();
                    for (EmployeesPojo row : pojoList) {
                        String name = row.getEmployeeName();
                        String working = row.getEmployeesWorking().toString();
                        nameList.add(working + "(" + name + ")");
                    }
                    statisticsPojo.setCallTheSeatList(nameList);
                }
            }
        }
        statisticsPojo.setAnsweringVolume(connectedCount);
        statisticsPojo.setAnswerRate(bigDecimal);
        statisticsPojo.setCallLossVolume(fumbleCount);
        return statisticsPojo;
    }

    /**
     * 分组查询所有回铃状态数量
     */
    @NotNull
    private TaskStatisticsPojo getTaskStatisticsPojoCount(String callCenterUuid) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("taskUuid", callCenterUuid);
        map.put("callResults", Arrays.asList(CallResultEnum.CALL_RESULT_SHUT_DOWN.getInfo(),
                CallResultEnum.CALL_RESULT_POWER_OFF.getInfo(),
                CallResultEnum.CALL_RESULT_BE_BUSY.getInfo(),
                CallResultEnum.CALL_RESULT_ON_THE_LINE.getInfo(),
                CallResultEnum.CALL_RESULT_NO_ANSWER.getInfo(),
                CallResultEnum.CALL_RESULT_VACANT_NUMBER.getInfo(),
                CallResultEnum.CALL_RESULT_UNABLE_TO_CONNECT.getInfo(),
                CallResultEnum.CALL_RESULT_UNKNOWN.getInfo()));
        //分组查询多个外呼效果字段数量
        Map<String, Object> callResultMap = callRecordMapper.selectCountByCallResultGroupBy(map);

        BiConsumer<String, Consumer<Integer>> setCount = (callResult, setter) -> {
            Integer count = Optional.ofNullable(callResultMap.get(callResult))
                    .filter(resultMap -> resultMap instanceof Map)
                    .map(resultMap -> (Map<String, Object>) resultMap)
                    .map(resultMap -> resultMap.get("count"))
                    .filter(cnt -> cnt instanceof Long)
                    .map(cnt -> ((Long) cnt).intValue())
                    .orElse(0);
            setter.accept(count);
        };
        // 使用定义的方法设置各个统计值
        TaskStatisticsPojo statisticsPojo = new TaskStatisticsPojo();
        setCount.accept(CallResultEnum.CALL_RESULT_SHUT_DOWN.getInfo(), statisticsPojo::setShutDownCount);
        setCount.accept(CallResultEnum.CALL_RESULT_POWER_OFF.getInfo(), statisticsPojo::setPowerOffCount);
        setCount.accept(CallResultEnum.CALL_RESULT_BE_BUSY.getInfo(), statisticsPojo::setBeBusyCount);
        setCount.accept(CallResultEnum.CALL_RESULT_ON_THE_LINE.getInfo(), statisticsPojo::setOnTheLineCount);
        setCount.accept(CallResultEnum.CALL_RESULT_NO_ANSWER.getInfo(), statisticsPojo::setNoAnswerCount);
        setCount.accept(CallResultEnum.CALL_RESULT_VACANT_NUMBER.getInfo(), statisticsPojo::setVacantNumberCount);
        setCount.accept(CallResultEnum.CALL_RESULT_UNABLE_TO_CONNECT.getInfo(), statisticsPojo::setUnableToConnectCount);
        setCount.accept(CallResultEnum.CALL_RESULT_UNKNOWN.getInfo(), statisticsPojo::setOtherQuantity);
        return statisticsPojo;
    }

    private Long getCallTotalCount(String callCenterUuid, String callResult) {
        Long callTotalCount = callRecordService.lambdaQuery()
                .eq(CallRecord::getDelFlag, 0)
                .eq(CallRecord::getTeamId, SecurityUtils.getTeamId())
                .eq(CallRecord::getTaskUuid, callCenterUuid)
                .eq(StringUtils.isNotEmpty(callResult), CallRecord::getCallResult, callResult)
                .count();
        return callTotalCount;
    }

    private Long getNoAnswerCount(String callCenterUuid) {
        Long callRecordCount = callRecordService.lambdaQuery()
                .eq(CallRecord::getDelFlag, 0)
                .eq(CallRecord::getTeamId, SecurityUtils.getTeamId())
                .eq(CallRecord::getTaskUuid, callCenterUuid)
                .in(CallRecord::getCallResult,
                        CallResultEnum.CALL_RESULT_ON_THE_LINE.getInfo(),
                        CallResultEnum.CALL_RESULT_NO_ANSWER.getInfo(), CallResultEnum.CALL_RESULT_SHUT_DOWN.getInfo(),
                        CallResultEnum.CALL_RESULT_BE_BUSY.getInfo(), CallResultEnum.CALL_RESULT_POWER_OFF.getInfo())
                .count();
        return callRecordCount;
    }

    /**
     * 查看结果报告-数据列表
     *
     * @param intelligenceTask 搜索条件
     * @return
     */
    public List<CallRecordDataPojo> agCallTaskDataList(IntelligenceTask intelligenceTask) {
//        查询任务详情
//        PageUtils.clearPage();
        IntelligenceTask selectById = intelligenceTaskService.selectById(intelligenceTask.getId(), intelligenceTask.getTeamId());
        if (ObjectUtils.isEmpty(selectById)) throw new GlobalException("任务查询为空");
        if (ObjectUtils.isEmpty(selectById.getCallCenterUuid())) throw new GlobalException("任务uuid查询为空");
//        根据任务uuid查询对应话单
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("taskUuid", selectById.getCallCenterUuid());
        map.put("connectFlag", intelligenceTask.getConnectFlag());

        Set<String> notAnswerResults = new HashSet<>(Arrays.asList(
                CallResultEnum.CALL_RESULT_ON_THE_LINE.getInfo(), CallResultEnum.CALL_RESULT_NO_ANSWER.getInfo(),
                CallResultEnum.CALL_RESULT_SHUT_DOWN.getInfo(), CallResultEnum.CALL_RESULT_BE_BUSY.getInfo(), CallResultEnum.CALL_RESULT_POWER_OFF.getInfo()
        ));

        //通过回铃状态查询 0=未接听 通话中+无人接听+停机+忙线+关机等呼叫到达被叫侧；
        if (intelligenceTask.getConnectFlag2() != null && intelligenceTask.getConnectFlag2() == 0) {
            map.put("callResults", notAnswerResults);
            map.remove("connectFlag");
        }
        //通过回铃状态查询 2=未接通：黑名单＋拦截+无法接通+空号（呼叫未到被叫侧）
        if (intelligenceTask.getConnectFlag() != null && intelligenceTask.getConnectFlag() == 2) {
            map.put("callResults", Arrays.asList(CallResultEnum.CALL_RESULT_UNKNOWN.getInfo(),
                    CallResultEnum.CALL_RESULT_UNABLE_TO_CONNECT.getInfo(), CallResultEnum.CALL_RESULT_VACANT_NUMBER.getInfo()));
            map.remove("connectFlag");
        }

        PageUtils.startPage();
        List<CallRecordDataPojo> callRecordDataPojos = intelligenceTaskService.selectCallRecordList(map);

//        判断是否可以进入案件详情
        if (!ObjectUtils.isEmpty(callRecordDataPojos)) {
            for (CallRecordDataPojo row : callRecordDataPojos) {
                //100=未接听 通话中+无人接听+停机+忙线+关机等呼叫到达被叫侧；将标识改为未接听
                if (notAnswerResults.contains(row.getCallResult()))
                    row.setConnectFlag(CallTaskMarkEnum.NOT_ANSWER.getCode());

                whetherItBelongs(row);
            }
        }
        return callRecordDataPojos;
    }

    /**
     * 预测试外呼话单-判断案件是否属于登录人
     *
     * @param row
     * @return
     */
    public void whetherItBelongs(CallRecordDataPojo row) {
        if (row.getCaseId() == null) {
            row.setButton(0);
        } else {
//            根据案件id查询案件信息
            CaseManage caseManage = intelligenceTaskService.selectCaseManageCaseId(row.getCaseId());
            if (ObjectUtils.isEmpty(caseManage)) {
                row.setButton(0);
                return;
            }
            if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                if (SecurityUtils.getTeamId().equals(caseManage.getOutsourcingTeamId())) {
                    row.setButton(1);
                } else {
                    row.setButton(0);
                }
            } else {
                row.setButton(0);
            }
        }
    }

    /**
     * 修改坐席状态并同步呼叫中心-(预测试外呼坐席)
     *
     * @param sipState 坐席状态（1=置闲，2=置忙）
     * @return
     */
    @Transactional
    public void updateSipState(Integer sipState) {
//        获取当前登录人的sip坐席信息
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("odvId", SecurityUtils.getUserId());
        CallSip callSip = callRecordService.selectSipData(map);
        if (ObjectUtils.isEmpty(callSip)) throw new GlobalException("当前用户坐席信息查询为空");
        if (StringUtils.isEmpty(callSip.getCompanyNum()) || StringUtils.isEmpty(callConfig.getApiFlag()))
            throw new GlobalException("企业api标识/企业编号查询为空");
        if (callSip.getSeatsType() == null || callSip.getSeatsType() != 2)
            throw new GlobalException("该员工没有预测试外呼坐席，无法修改坐席状态");
//        根据sip账号主键id修改坐席状态
        CallSip sip = new CallSip();
        sip.setId(callSip.getId());
        sip.setSipState(sipState);
        callRecordService.updateSipState(sip);
//        调用呼叫中心接口，同步坐席状态给呼叫中心
        CallCenterTaskPojo centerTaskPojo = new CallCenterTaskPojo();
        centerTaskPojo.setApiFlag(callConfig.getApiFlag());  //企业api标识
        centerTaskPojo.setEnterpriseNum(callSip.getCompanyNum());  //企业编号
        centerTaskPojo.setServiceHostUrl(callConfig.getHostUrl());
        centerTaskPojo.setSipState(sipState);
        centerTaskPojo.setSipAccount(callSip.getSipNumber());
        callingTaskDockingApi.updateSipState(centerTaskPojo);
    }

    /**
     * 修改坐席状态并同步呼叫中心-(预测试外呼坐席)
     *
     * @param entityPojo 登录人信息以及案件id集合
     * @return
     */
    public List<Integer> verifyCaseList(ThreadEntityPojo entityPojo) {
//        根据团队id以及案件id集合查询案件对应员工有预测试外呼坐席的员工id集合
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", entityPojo.getCreateId());
        map.put("caseIdList", entityPojo.getCaseIdList());
        List<Integer> list = intelligenceTaskService.selectEmployeeIdList(map);
        if (ObjectUtils.isEmpty(list)) return new ArrayList<>();
//        查询有预测试外呼坐席的催员id集合
        map.put("caseIdList", null);
        map.put("odvIdList", list);
        return intelligenceTaskService.selectSipOdvIdList(map);
    }

    /**
     * 修改坐席状态并同步呼叫中心-(预测试外呼坐席)
     *
     * @param entityPojo 登录人信息以及案件id集合
     * @return
     */
    public List<Integer> verifyMediatorCaseList(ThreadEntityPojo entityPojo) {
//        根据团队id以及案件id集合查询案件对应员工有预测试外呼坐席的员工id集合
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", entityPojo.getCreateId());
        map.put("caseIdList", entityPojo.getCaseIdList());
        List<Integer> list = intelligenceTaskService.selectTsEmployeeIdList(map);
        if (ObjectUtils.isEmpty(list)) return new ArrayList<>();
//        查询有预测试外呼坐席的催员id集合
        map.put("caseIdList", null);
        map.put("odvIdList", list);
        return intelligenceTaskService.selectSipOdvIdList(map);
    }

    /**
     * 修改坐席状态并同步呼叫中心-(预测试外呼坐席)
     *
     * @param entityPojo 登录人信息以及案件id集合
     * @return
     */
    public List<Integer> verifyStageCaseList(ThreadEntityPojo entityPojo) {
//        根据团队id以及案件id集合查询案件对应员工有预测试外呼坐席的员工id集合
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", entityPojo.getCreateId());
        map.put("caseIdList", entityPojo.getCaseIdList());
        List<Integer> list = intelligenceTaskService.selectStEmployeeIdList(map);
        if (ObjectUtils.isEmpty(list)) return new ArrayList<>();
//        查询有预测试外呼坐席的催员id集合
        map.put("caseIdList", null);
        map.put("odvIdList", list);
        return intelligenceTaskService.selectSipOdvIdList(map);
    }

    /**
     * 提交任务-写入数据
     *
     * @param task
     * @param threadEntityPojo
     * @return
     */
    @Transactional
    public Integer caseSubmitTaskData(IntelligenceTask task, ThreadEntityPojo threadEntityPojo) {
//        校验任务名称机构内去重
        if (ObjectUtils.isEmpty(task.getTaskName())) throw new GlobalException("任务名称不能为空");
        Integer count = intelligenceTaskService.selectByCount(task.getTaskName(), threadEntityPojo.getCreateId());
        if (count > 0) throw new GlobalException("任务名称已存在，请重新输入");
//        根据案件id以及团队id查询案件id以及联系人id和手机号
        if (task.getCallRecipient() == null) throw new GlobalException("呼叫对象不能为空");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("teamId", threadEntityPojo.getCreateId());
        objectMap.put("caseIdList", threadEntityPojo.getCaseIdList());
        if (task.getCallRecipient() == 1) {
            objectMap.put("contactRelation", "本人");
        }
        List<CallCustomDetails> list = intelligenceTaskService.selectContactsList(objectMap);
        if (ObjectUtils.isEmpty(list)) throw new GlobalException("号码状态无效或已结清案件不支持创建预测试外呼任务");
//        写入导入客户记录表并返回主键id
        CallCustomRecord record = new CallCustomRecord();
        record.setCreateId(threadEntityPojo.getUserId());
        record.setCreateBy(threadEntityPojo.getUser());
        record.setCreateType(threadEntityPojo.getType());
        record.setTeamId(threadEntityPojo.getCreateId());
        record.setCustomerType(2);
        Long aLong = customRecordService.insert(record);

        List<CallCustomDataPojo> dataPojoList = new ArrayList<>();
//        批量写入客户信息
        for (CallCustomDetails row : list) {
//            生成客户uuid，与呼叫中心保持一致进行话单关联
            //生成的是不带-的字符串，类似于：b17f24ff026d40949c85a24f4f375d42
            String simpleUUID = IdUtil.simpleUUID();
            row.setUuid(simpleUUID);
            row.setRecordId(aLong);
            row.setCreateTime(new Date());
            row.setCreateType(threadEntityPojo.getType());
            row.setTeamId(threadEntityPojo.getCreateId());
            if (row.getUserNo() != null) {
                Map<String, Object> hashMap = new HashMap<>();
                hashMap.put("teamId", threadEntityPojo.getCreateId());
                hashMap.put("employeesWorking", row.getUserNo());
                Integer integer = customRecordService.selectEmployeesId(hashMap);
                row.setMemberId(integer);
            }
            row.setCreateById(threadEntityPojo.getUserId());
            row.setDelFlag(BaseConstant.DelFlag_Being);
            row.setCustomerType(2);
//            处理手机号/客户姓名解密
            row.setCaseName(FieldEncryptUtil.decrypt(row.getCaseName()));
            row.setCasePhone(FieldEncryptUtil.decrypt(row.getCasePhone()));

//            处理推送给呼叫中心的客户信息
            CallCustomDataPojo data = new CallCustomDataPojo();
            data.setPhone(row.getCasePhone());
            if (row.getMemberId() != null) {
//                根据员工id查询该员工的坐席Sip账号
                Map<String, Object> map = new HashMap<>();
                map.put("teamId", threadEntityPojo.getCreateId());
                map.put("odvId", row.getMemberId());
                map.put("seatsType", 2);
                String sip = customRecordService.selectSip(map);
                data.setBelongSip(sip);
            }
            data.setCustomerId(row.getUuid());
            if ("0".equals(row.getCaseState())) {
                data.setUseAllocatedSip(0);
            } else {
                data.setUseAllocatedSip(1);
            }
            data.setCaseId(row.getCaseId().toString());
            dataPojoList.add(data);
        }
        customDetailsService.insertList(list);
//        插入任务信息
        checkTaskData(task);
//        task.setTaskName(dataPojo.getTaskName());
        task.setTeamId(threadEntityPojo.getCreateId());
        task.setEmployeeId(threadEntityPojo.getUserId());
        task.setCreateBy(threadEntityPojo.getUser());
        task.setCreateTime(new Date());
        task.setCreateId(threadEntityPojo.getUserId());
        task.setCreateType(threadEntityPojo.getType());
        task.setCustomId(aLong);
        task.setStatus(CallStateEnum.IN_PROGRESS.getCode());
        task.setOperationType(2);
        task.setDelFlag(BaseConstant.DelFlag_Being);
//        呼叫总数量
        task.setCallCount(list.size());
        Long idLong = intelligenceTaskService.insert(task);

        //校验坐席
        AtomicBoolean hasUseAllocatedSip = new AtomicBoolean(false);
        List<String> belongSipList = new ArrayList<>();
        dataPojoList.forEach(l -> {
            if (l.getUseAllocatedSip().equals(1)) {
                hasUseAllocatedSip.set(true);
            }
            if (!StringUtils.isBlank(l.getBelongSip())) {
                belongSipList.add(l.getBelongSip());
            }
        });

        if (task.getAnswerSettings() != null  && !hasUseAllocatedSip.get() && belongSipList.isEmpty()) {
            throw new GlobalException("选择的预测式外呼坐席不是案件归属坐席，无法提交，请重新选择坐席或修改接听设置");
        }
//        任务信息推送给呼叫中心
        return callCenterDocking(task, dataPojoList, idLong);
    }

    public List<Integer> verifyCaseListAppeal(ThreadEntityPojo entityPojo) {
        //        根据团队id以及案件id集合查询案件对应员工有预测试外呼坐席的员工id集合
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", entityPojo.getCreateId());
        map.put("caseIdList", entityPojo.getCaseIdList());
        List<Integer> list = intelligenceTaskService.selectEmployeeIdListAppeal(map);
        if (ObjectUtils.isEmpty(list)) return new ArrayList<>();
//        查询有预测试外呼坐席的催员id集合
        map.put("caseIdList", null);
        map.put("odvIdList", list);
        return intelligenceTaskService.selectSipOdvIdList(map);
    }
}