package com.zws.call.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * AI语音通知所有模板
 */
@Data
public class AiFieldRecord {

    private String clientName;//客户姓名
    private String clientPhone;//联系电话
    private String clientIdNum;//客户证件号码
    private String reductionState;//减免状态
    private String followUpState;//当前跟进状态
    private String clientAge;//年龄
    private String clientSex;//性别
    private String qq;//QQ
    private String maritalStatus;//婚姻状况
    private String occupation;//职业
    private String weixin;//微信
    private String mailbox;//邮箱
    private String bankName;//开户行
    private String securityName;//担保人/共同还款人
    private String securityIdType;// 担保人/共同还款人证件类型
    private String securityIdNum;//担保人/共同还款人证件号码
    private String securityPhone;//担保人/共同还款人电话
    private String education;//学历
    private String ycFiveLevel;//五级分类
    private String bankCardNumber;//银行卡号
    private String registeredAddress;//户籍地址
    private String placeOfWork;//单位名称
    private String workingAddress;//单位地址
    private String unitProperties;//单位性质
    private String homeAddress;//联系地址
    private String residentialAddress;//居住地址
    private String entrustMoney;//债权总金额
    private String residualPrincipal;//债权本金
    private String interestMoney;//债权利息
    private String serviceFee;//垫付费用
    private String baseOverdueDays;//基准日逾期天数
    private String overdueStart;//逾期日期（末次）
    private String principalInterestTotal;//本息费合计
    private String interestFeesTotal;//息费合计
    private String amountFinalRepayment;//最后还款金额
    private String amountAfterDeductionSun;//减免总金额(催收端专有)
    private String amountAfterDeduction;//减免后应还金额
    private String syyhwsDisbursement; //剩余应还我司垫付费用
    private String syYhPrincipal;//剩余应还本金
    private String syYhInterest;//剩余应还利息
    private String syYhFees;//剩余应还费用
    private String remainingDue;//剩余应还债权金额
    private String amountCalledBack;//累计已还
    private String afterBaseDateInterest;//基准日后利息
    private String afterBaseDateLpr4Interest;//lpr4倍利息
    private String ycContractNo;//合同号
    private String cardIssuanceDate;//发卡日期
    private String repaymentDate;//每月还款日
    private String statementDate;//账单日
    private String firstOverdueDate;//首次逾期日期
    private String baseDate;//基准日
    private String creditLimit;//统计时点信用额度
    private String amountFinalDate;//末次还款日期
    private String ycOverdueDays;//实际逾期天数
    private String ycDisbursement;//我司垫付费用
    private String contractNo;//借据号
    private String ycCurrencies;//币种
    private String ycPurpose;//贷款用途
    private String ycLendingRate;//贷款利率
    private String ycRepaymentMethod;//还款渠道
    private String ycLitigationStatus;//诉讼状态
    private String judgmentRate;//判决利率
    private String judgmentDate;//判决日期
    private String ycIsDishonest;//是否失信被执行人
    private String ycIsLimitConsumption;//是否被限制高消费
    private String ycWriteDate;//核销日期
    private String ycDefaultRate;//罚息利率
    private String loanPrincipal;//贷款本金
    private String repaymentMonthly;//每月应还
    private String caseRegion;//案件地区
    private String courtJurisdiction;//管辖法院
    private String loanPeriods;//贷款期数
    private String alreadyPeriods;//已还期数
    private String notPeriods;//未还期数
    private String loanInstitution;//受让方
    private String accountPeriod;//账期(M1~Mn)
    private String ycLoanBank;//放款分（支）行
    private String ycBusinessType;//业务类型
    private String productType;//产品类型
    private String ycContractMoney;//授信额度
    private String ycLoanTerm;//贷款期限
    private String ycLoanIssuanceDate;//贷款发放日
    private String ycLoanMaturityDate;//贷款到期日
    private String ycAbdRepayment;//过渡期还款金额  基准日后还款金额
    private String ycAbdPrincipal;//过渡期本金还款  基准日后本金还款
    private String ycAbdInterest;//过渡期利息还款  基准日后利息还款
    private String ycAbdExpense;//过渡期费用还款（催收端专有）
    private String latestFollowUpTime;//最近跟进时间
    private String entrustingCaseDate;//委案日期
    private String returnCaseDate;//退案日期
    private String nowDate;//当前日期



}
