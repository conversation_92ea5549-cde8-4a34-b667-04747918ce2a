package com.zws.call.pojo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 出入账通知接收-实体类
 *
 * @author: huang<PERSON>uf
 * @date ：Created in 2024/12/26 16:07
 */
@Data
public class BillNotificationPojo implements Serializable {

    /**
     * 出入账唯一编号
     */
    private String billUuid;

    /**
     * 类型（0代表入账 1代表出账）
     */
    private String type;

    /**
     * 企业编号
     */
    private Long enterpriseNum;
}
