package com.zws.call.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 通话记录
 */
@Data
@ToString
public class CallRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 联系人id
     */
    private Long contactId;
    /**
     * 案件id
     */
    @Excel(name = "案件ID", sort = 1)
    private Long caseId;
    /**
     * 呼叫时间
     */
    @Excel(name = "呼叫时间",dateFormat="yyyy-MM-dd HH:mm:ss", sort = 2)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date callTime;
    /**
     * 通话时长(秒)
     */
    @Excel(name = "通话时长(秒)", sort = 3)
    private Integer agentDuration;
    /**
     * 主叫电话号码（员工SIP号码）
     */
    @Excel(name = "主叫号码", sort = 4)
    private String callFrom;
    /**
     * 被叫号码(客户电话)
     */
    @Excel(name = "被叫号码", sort = 5)
    private String callTo;
    /**
     * 借款人
     */
    @Excel(name = "借款人",sort =6)
    private String borrower;
    /**
     * 中继号码(外显号码、客户手机上显示的号码)
     */
    @Excel(name = "外显号码", sort = 7)
    private String number;
    /**
     * 呼叫标识（手动呼叫：callOutManual，API点击呼叫：clickCallOut，呼入：inbound，呼出：outbound）
     *
     * 呼叫标识，呼叫类型（手动呼叫：callOutManual，API点击呼叫：clickCallOut，呼入：inbound，预测试外呼：newauto）
     */
    @Excel(name = "呼叫类型",readConverterExp="callOutManual=直拨,clickCallOut=点呼,inbound=呼入,outbound=呼出,newauto=预测试外呼",  sort = 8)
    private String callroter;
    /**
     * 通话的唯一编码(呼叫中心)
     */
    private String callid;
    /**
     * 外呼是否被客户接听,y表示接听,n表示没接听
     */
    private String answer;
    /**
     * 通话的录音文件名，
     * 文件名格式为：年月日/called+主叫号码+被叫号码
     */
    private String recording;
    /**
     * 挂机原因
     */
    private String hangupCause;
    /**
     * 中继标识，标识呼出电话使用的哪条中继线路
     */
    private String trunkName;
    /**
     * 外呼前缀，自动加拨的前缀号码，因为有的中继必须要前缀才能拨打电话
     */
    private String callPrefix;
    /**
     *  入网企业编号
     */
    private String companyNum;
    /**
     * 入网企业名称
     */
    private String companyName;
    /**
     * 员工的SIP账号
     */
    private String sipNumber;
    /**
     * 通话双方摘机的时间
     * 没有通话时为null
     */
    private Date answerStart;
    /**
     *通话双方挂机的时间
     * 没有通话时为null
     */
    private Date answerEnd;
    /**
     *  回调时间
     */
    private Date callbackTime;
    /**
     *  团队id
     */
    private Long teamId;
    /**
     * 催员id
     */
    private Long odvId;
    /**
     * 服务器地址
     */
    private String serviceHost;
    /**
     *
     */
    private Date createTime;

    private String delFlag;


    /**
     * 团队名称（机构名称）
     */
    @Excel(name = "机构名称",sort = 9)
    private String teamName;
    /**
     * 催员名称
     */
    @Excel(name = "处置人员",sort = 10)
    private String odvName;

    /**
     * 录音url
     */
    private String recordUrl;

    /**
     * 机构类型（0-自营，1-委外）
     */
    @Excel(name = "机构类型",readConverterExp="0=自营,1=委外", sort = 7)
    private Integer teamType;

    /**
     * 案件id- (多个案件id拼接)
     */
    @Excel(name = "案件ID", sort = 5)
    private String caseIdStr;

    /**
     * 预测试外呼任务uuid
     */
    private String taskUuid;

    /**
     * 预测试外呼任务接通标识：0=待拨打，1=已接听，2=未接通，3=漏接
     */
    private Integer connectFlag;

    /**
     * 客户uuid
     */
    private String customUuid;

    /**
     * 号码状态
     */
    private String callResult;

    public String getCaseIdStr() {
        return caseIdStr;
    }

    public void setCaseIdStr(String caseIdStr) {
        this.caseIdStr = caseIdStr;
    }

    public Integer getTeamType() {
        return teamType;
    }

    public void setTeamType(Integer teamType) {
        this.teamType = teamType;
    }

    public void setRecordUrl(String recordUrl) {
        this.recordUrl = recordUrl;
    }

    public String getOdvName() {
        return odvName;
    }

    public void setOdvName(String odvName) {
        this.odvName = odvName;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getBorrower() {
        return borrower;
    }

    public void setBorrower(String borrower) {
        this.borrower = borrower;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    public Long getCaseId() {
        return caseId;
    }

    public void setCaseId(Long caseId) {
        this.caseId = caseId;
    }

    public Date getCallTime() {
        return callTime;
    }

    public void setCallTime(Date callTime) {
        this.callTime = callTime;
    }

    public Integer getAgentDuration() {
        return agentDuration;
    }

    public void setAgentDuration(Integer agentDuration) {
        this.agentDuration = agentDuration;
    }

    public String getCallFrom() {
        return callFrom;
    }

    public void setCallFrom(String callFrom) {
        this.callFrom = callFrom == null ? null : callFrom.trim();
    }

    public String getCallTo() {
        return callTo;
    }

    public void setCallTo(String callTo) {
        this.callTo = callTo == null ? null : callTo.trim();
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number == null ? null : number.trim();
    }

    public String getCallroter() {
        return callroter;
    }

    public void setCallroter(String callroter) {
        this.callroter = callroter == null ? null : callroter.trim();
    }

    public String getCallid() {
        return callid;
    }

    public void setCallid(String callid) {
        this.callid = callid == null ? null : callid.trim();
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer == null ? null : answer.trim();
    }

    public String getRecording() {
        return recording;
    }

    public void setRecording(String recording) {
        this.recording = recording == null ? null : recording.trim();
    }

    public String getHangupCause() {
        return hangupCause;
    }

    public void setHangupCause(String hangupCause) {
        this.hangupCause = hangupCause == null ? null : hangupCause.trim();
    }

    public String getTrunkName() {
        return trunkName;
    }

    public void setTrunkName(String trunkName) {
        this.trunkName = trunkName == null ? null : trunkName.trim();
    }

    public String getCallPrefix() {
        return callPrefix;
    }

    public void setCallPrefix(String callPrefix) {
        this.callPrefix = callPrefix == null ? null : callPrefix.trim();
    }

    public String getCompanyNum() {
        return companyNum;
    }

    public void setCompanyNum(String companyNum) {
        this.companyNum = companyNum == null ? null : companyNum.trim();
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    public String getSipNumber() {
        return sipNumber;
    }

    public void setSipNumber(String sipNumber) {
        this.sipNumber = sipNumber == null ? null : sipNumber.trim();
    }

    public Date getAnswerStart() {
        return answerStart;
    }

    public void setAnswerStart(Date answerStart) {
        this.answerStart = answerStart;
    }

    public Date getAnswerEnd() {
        return answerEnd;
    }

    public void setAnswerEnd(Date answerEnd) {
        this.answerEnd = answerEnd;
    }

    public Date getCallbackTime() {
        return callbackTime;
    }

    public void setCallbackTime(Date callbackTime) {
        this.callbackTime = callbackTime;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getOdvId() {
        return odvId;
    }

    public void setOdvId(Long odvId) {
        this.odvId = odvId;
    }

    public String getServiceHost() {
        return serviceHost;
    }

    public void setServiceHost(String serviceHost) {
        this.serviceHost = serviceHost == null ? null : serviceHost.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }

    public String getRecordUrl() {
        //国瑞呼叫 不需要这个规则
       /* if(StringUtils.isNotEmpty(recording) && !StringUtils.equals(recording.toLowerCase(),"null")){
            return this.serviceHost+"record/"+recording;
        }*/
        return this.recording;
    }
}
