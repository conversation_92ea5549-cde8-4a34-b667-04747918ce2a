package com.zws.call.api;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.zws.call.pojo.CallCenterTaskPojo;
import com.zws.call.pojo.CallTaskReturnPojo;
import com.zws.common.core.callcenter.pojo.AjaxRequesPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * 预测试外呼对接呼叫中心接口Api
 *
 * @author: 马博新
 * @date ：Created in 2024/09/09 10:22
 */
@Slf4j
@Component
public class CallingTaskDockingApi {

    /**
     * JAVA呼叫中心 预测试外呼-提交任务
     */
    private final String preTestOutboundCallSubmissionTask = "/call/cti/addPredictiveTask";

    /**
     * JAVA呼叫中心 预测试外呼-更新任务
     */
    private final String preTestOutboundCallUpdateTask = "/call/cti/setPredictiveTaskStatus";

    /**
     * JAVA呼叫中心 预测试外呼-查询任务状态
     */
    private final String getTestOutboundCallTaskStatus = "/call/cti/getPredictiveTaskInfo";

    /**
     * JAVA呼叫中心 预测试外呼-未接通重呼
     */
    private final String notConnectedRecallCallTask = "/call/cti/predictiveTaskUnansweredRecall";

    /**
     * JAVA呼叫中心 预测试外呼-修改sip坐席状态
     */
    private final String modifySipSeatStatus = "/call/cti/setEnterpriseSipStatus";


    /**
     * 预测试外呼-提交任务
     *
     * @param param
     */
    public CallTaskReturnPojo submitCallTask(CallCenterTaskPojo param) {
        if (StringUtils.isEmpty(param.getServiceHostUrl())) {
            //未设置呼叫中心
            throw new ServiceException("未配置呼叫中心地址，无法获取");
        }
        String jsonStr = JSONUtil.toJsonStr(param);
        log.info("预测试外呼-提交任务，请求参数:{}", jsonStr);
        log.info("预测试外呼-提交任务，请求路径:{}", param.getServiceHostUrl() + preTestOutboundCallSubmissionTask);
        String result = HttpUtil.post(param.getServiceHostUrl() + preTestOutboundCallSubmissionTask, jsonStr);
        log.info("预测试外呼-提交任务，响应参数:{}", result);
        AjaxRequesPojo ajaxRequesPojo = JSONUtil.toBean(result, AjaxRequesPojo.class);
        if (!ObjectUtils.isEmpty(ajaxRequesPojo)) {
            if (ajaxRequesPojo.getCode().equals("200")) {
                CallTaskReturnPojo returnPojo = JSONUtil.toBean(ajaxRequesPojo.getData(), CallTaskReturnPojo.class);
                if (ObjectUtils.isEmpty(returnPojo)) throw new GlobalException("呼叫中心返回信息为空");
                if (StringUtils.isEmpty(returnPojo.getTaskUuid())) throw new GlobalException("任务uuid返回为空");
                return returnPojo;
            } else {
                throw new ServiceException("呼叫中心请求错误:" + ajaxRequesPojo.getMsg());
            }
        } else {
            log.error("呼叫中心-预测试外呼-提交任务请求错误：返回为空");
            throw new ServiceException("呼叫中心请求错误:返回为空");
        }
    }

    /**
     * 预测试外呼-更新任务（执行/暂停/撤销）
     *
     * @param param
     */
    public void updateCallTask(CallCenterTaskPojo param) {
        if (StringUtils.isEmpty(param.getServiceHostUrl())) {
            //未设置呼叫中心
            throw new ServiceException("未配置呼叫中心地址，无法获取");
        }
        String jsonStr = JSONUtil.toJsonStr(param);
        log.info("预测试外呼-更新任务，请求参数:{}", jsonStr);
        log.info("预测试外呼-更新任务，请求路径:{}", param.getServiceHostUrl() + preTestOutboundCallUpdateTask);
        String result = HttpUtil.post(param.getServiceHostUrl() + preTestOutboundCallUpdateTask, jsonStr);
        log.info("预测试外呼-更新任务，响应参数:{}", result);
        AjaxRequesPojo ajaxRequesPojo = JSONUtil.toBean(result, AjaxRequesPojo.class);
        if (!ObjectUtils.isEmpty(ajaxRequesPojo)) {
            if (ajaxRequesPojo.getCode().equals("200")) {
                log.info("预测试外呼-更新任务请求成功" + ajaxRequesPojo.getMsg());
            } else {
                throw new ServiceException("呼叫中心请求错误:" + ajaxRequesPojo.getMsg());
            }
        } else {
            log.error("呼叫中心-预测试外呼-更新任务请求错误：返回为空");
            throw new ServiceException("呼叫中心请求错误:返回为空");
        }
    }

    /**
     * 预测试外呼-未接通重呼
     *
     * @param param
     */
    public void notConnectedCallTask(CallCenterTaskPojo param) {
        if (StringUtils.isEmpty(param.getServiceHostUrl())) {
            //未设置呼叫中心
            throw new ServiceException("未配置呼叫中心地址，无法获取");
        }
        String jsonStr = JSONUtil.toJsonStr(param);
        log.info("预测试外呼-未接通重呼，请求参数:{}", jsonStr);
        log.info("预测试外呼-未接通重呼，请求路径:{}", param.getServiceHostUrl() + notConnectedRecallCallTask);
        String result = HttpUtil.post(param.getServiceHostUrl() + notConnectedRecallCallTask, jsonStr);
        log.info("预测试外呼-未接通重呼，响应参数:{}", result);
        AjaxRequesPojo ajaxRequesPojo = JSONUtil.toBean(result, AjaxRequesPojo.class);
        if (!ObjectUtils.isEmpty(ajaxRequesPojo)) {
            if (ajaxRequesPojo.getCode().equals("200")) {
                log.info("预测试外呼-未接通重呼请求成功" + ajaxRequesPojo.getMsg());
            } else {
                throw new ServiceException("呼叫中心请求错误:" + ajaxRequesPojo.getMsg());
            }
        } else {
            log.error("呼叫中心-预测试外呼-未接通重呼请求错误：返回为空");
            throw new ServiceException("呼叫中心请求错误:返回为空");
        }
    }

    /**
     * 预测试外呼-更新任务（执行/暂停/撤销）
     *
     * @param param
     */
    public List<CallCenterTaskPojo> getCallTaskStatus(CallCenterTaskPojo param) {
        if (StringUtils.isEmpty(param.getServiceHostUrl())) {
            //未设置呼叫中心
            throw new ServiceException("未配置呼叫中心地址，无法获取");
        }
        String jsonStr = JSONUtil.toJsonStr(param.getMap());
        log.info("预测试外呼-查询任务，请求参数:{}", jsonStr);
        log.info("预测试外呼-查询任务，请求路径:{}", param.getServiceHostUrl() + getTestOutboundCallTaskStatus);
        String result = HttpUtil.post(param.getServiceHostUrl() + getTestOutboundCallTaskStatus, jsonStr);
        log.info("预测试外呼-查询任务，响应参数:{}", result);
        AjaxRequesPojo ajaxRequesPojo = JSONUtil.toBean(result, AjaxRequesPojo.class);
        if (!ObjectUtils.isEmpty(ajaxRequesPojo)) {
            if (ajaxRequesPojo.getCode().equals("200")) {
                log.info("预测试外呼-查询任务请求成功" + ajaxRequesPojo.getMsg());
                String data = ajaxRequesPojo.getData();
                if (StringUtils.isEmpty(data)) throw new ServiceException("呼叫中心请求错误:返回数据为空");
                return JSONUtil.toList(data, CallCenterTaskPojo.class);
            } else {
                throw new ServiceException("呼叫中心请求错误:" + ajaxRequesPojo.getMsg());
            }
        } else {
            log.error("呼叫中心-预测试外呼-查询任务请求错误：返回为空");
            throw new ServiceException("呼叫中心请求错误:返回为空");
        }
    }

    /**
     * 更新坐席状态-（预测试外呼坐席）
     *
     * @param param
     */
    public void updateSipState(CallCenterTaskPojo param) {
        if (StringUtils.isEmpty(param.getServiceHostUrl())) {
            //未设置呼叫中心
            throw new ServiceException("未配置呼叫中心地址，无法获取");
        }
        String jsonStr = JSONUtil.toJsonStr(param);
        log.info("预测试外呼-修改坐席状态，请求参数:{}", jsonStr);
        log.info("预测试外呼-修改坐席状态，请求路径:{}", param.getServiceHostUrl() + modifySipSeatStatus);
        String result = HttpUtil.post(param.getServiceHostUrl() + modifySipSeatStatus, jsonStr);
        log.info("预测试外呼-修改坐席状态，响应参数:{}", result);
        AjaxRequesPojo ajaxRequesPojo = JSONUtil.toBean(result, AjaxRequesPojo.class);
        if (!ObjectUtils.isEmpty(ajaxRequesPojo)) {
            if (ajaxRequesPojo.getCode().equals("200")) {
                log.info("预测试外呼-修改坐席状态请求成功" + ajaxRequesPojo.getMsg());
            } else {
                throw new ServiceException("呼叫中心请求错误:" + ajaxRequesPojo.getMsg());
            }
        } else {
            log.error("呼叫中心-预测试外呼-修改坐席状态请求错误：返回为空");
            throw new ServiceException("呼叫中心请求错误:返回为空");
        }
    }
}
