package com.zws.call.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zws.call.domain.CallRecord;
import com.zws.call.domain.CallSip;
import com.zws.call.mapper.CallRecordMapper;
import com.zws.call.pojo.AiCallCenterTaskPojo;
import com.zws.call.pojo.TeamPojo;
import com.zws.call.service.ICallRecordService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.utils.DateUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/19 17:29
 */
@Service
@Primary
public class CallRecordServiceImpl extends ServiceImpl<CallRecordMapper, CallRecord> implements ICallRecordService {

    @Resource
    private CallRecordMapper mapper;

    @Override
    public long insert(CallRecord record) {
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setCreateTime(DateUtils.getNowDate());
        mapper.insert(record);
        return record.getId();
    }

    /**
     * 根据id修改话单-（预测试外呼）
     *
     * @param record
     * @return
     */
    @Override
    public int updateCallRecord(CallRecord record) {
        return mapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据手机号查询对应案件id
     *
     * @param teamPojo
     * @return
     */
    @Override
    public List<Long> selectCaseIdPhone(TeamPojo teamPojo) {
        return mapper.selectCaseIdPhone(teamPojo);
    }

    /**
     * 根据sip账号查询员工以及团队id
     *
     * @param sipNumber
     * @return
     */
    @Override
    public TeamPojo selectByTeamId(String sipNumber) {
        return mapper.selectByTeamId(sipNumber);
    }

    /**
     * 根据案件id集合查询借款人
     *
     * @param caseIds
     * @return
     */
    @Override
    public List<String> getClientName(List<Long> caseIds) {
        return mapper.getClientName(caseIds);
    }

    /**
     * 根据SIP账号查找
     *
     * @param sipNumber
     * @return
     */
    @Override
    public CallSip selectBySipNumber(String sipNumber) {
        return mapper.selectBySipNumber(sipNumber);
    }

    /**
     * 查询登录人的sip坐席信息
     *
     * @param map
     * @return
     */
    @Override
    public CallSip selectSipData(Map<String, Object> map) {
        return mapper.selectSipData(map);
    }

    /**
     * 根据callid查找通话记录主键id
     *
     * @param callid
     * @return
     */
    @Override
    public Long selectByCallId(String callid) {
        return mapper.selectByCallId(callid);
    }

    /**
     * 根据id修改sip坐席状态
     *
     * @param callSip
     * @return
     */
    @Override
    public int updateSipState(CallSip callSip) {
        return mapper.updateSipState(callSip);
    }

    /**
     * 获取话单表的uuid
     *
     * @param now
     * @param taskPojo
     * @return
     */
    @Override
    public List<String> selectTaskUuidList(Date now, AiCallCenterTaskPojo taskPojo,String callroter) {
        return mapper.selectTaskUuidList(now,taskPojo,callroter);
    }
}
