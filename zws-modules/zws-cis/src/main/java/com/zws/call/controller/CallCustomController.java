package com.zws.call.controller;

import com.zws.call.agservice.AgCallCustomService;
import com.zws.call.controller.request.WorkPhoneCustomDetailsRequest;
import com.zws.call.controller.response.WorkCustomDetailsResponse;
import com.zws.call.domain.CallCustomDetails;
import com.zws.call.domain.WorkPhoneCustomDetails;
import com.zws.call.pojo.ExportCustomPojo;
import com.zws.call.pojo.ImportDataPojo;
import com.zws.call.pojo.TreeTypePojo;
import com.zws.call.service.ICallCustomDetailsService;
import com.zws.common.core.constant.FileConstant;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 客户资料导入（预测试外呼）-接口层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 11:18
 */
@CrossOrigin
@RestController
@RequestMapping("/callCustom")
public class CallCustomController extends BaseController {

    @Autowired
    private AgCallCustomService agCallCustomService;
    @Autowired
    private ICallCustomDetailsService customDetailsService;

    /**
     * 下载导入客户资料模版--（客户资料）
     *
     * @param response
     * @throws Exception
     */
    @PostMapping("/customImportTemplate")
    public void templateMediationStage(HttpServletResponse response) throws Exception {
//        设置下载模板名字
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("导入客户资料模版.xlsx", "utf-8"));
        ExcelUtil<CallCustomDetails> util = new ExcelUtil<CallCustomDetails>(CallCustomDetails.class);
        util.exportExcel(response, new ArrayList<>(), "导入客户资料模版");
    }

    /**
     * excel表格导入客户资料-(校验名单信息)
     *
     * @param dataPojo 名单信息
     * @return
     * @throws Exception
     */
    @PostMapping("/importDataCustom")
    public AjaxResult importDataBack(@Validated @RequestBody ImportDataPojo dataPojo) throws Exception {
//        根据文件路径下载文件
        File tempFile = FileDownloadUtils.downloadTempFile(dataPojo.getFileUrl());
        ExcelUtil<CallCustomDetails> util = new ExcelUtil<CallCustomDetails>(CallCustomDetails.class);
        List<CallCustomDetails> userList = util.importExcel(new FileInputStream(tempFile));
        if (ObjectUtils.isEmpty(userList)) return AjaxResult.error("文件内容不能为空");
//        登录人信息
        ThreadEntityPojo threadEntityPojo = new ThreadEntityPojo();
        threadEntityPojo.setType(SecurityUtils.getAccountType());
        threadEntityPojo.setUser(SecurityUtils.getUsername());
        threadEntityPojo.setUserId(SecurityUtils.getUserId().intValue());
        threadEntityPojo.setCreateId(SecurityUtils.getTeamId().intValue());
        Map<String, Object> map = agCallCustomService.verifyCustomerList(userList, threadEntityPojo, dataPojo.getTaskName());
        return AjaxResult.success("校验成功", map);
    }

    /**
     * 返回团队分配了预测式外呼的人员列表树类型信息
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/DeptTreeType", method = RequestMethod.GET)
    public AjaxResult DeptTreeType() {
        List<TreeTypePojo> treeTypes = agCallCustomService.DeptTreeType();
        return AjaxResult.success("查询成功", treeTypes);
    }

    /**
     * 客户列表-查询
     *
     * @param customDetails 搜索条件
     * @return
     */
    @GetMapping("/listCustomDetails")
    public TableDataInfo listCustomDetails(CallCustomDetails customDetails) {

        if (ObjectUtils.isEmpty(customDetails)) customDetails = new CallCustomDetails();
        List<CallCustomDetails> list = customDetailsService.selectByList(customDetails);
        return getDataTable(list);
    }

    /**
     * 客户列表-导出
     *
     * @param response      导出数据
     * @param customDetails 搜索条件
     * @throws Exception
     */
    @PostMapping("/exportCustomDetails")
    public void exportCustomDetails(HttpServletResponse response, @RequestBody CallCustomDetails customDetails) throws Exception {
        if (ObjectUtils.isEmpty(customDetails)) throw new GlobalException("必要条件不能为空");
        if (ObjectUtils.isEmpty(customDetails.getCondition()))
            throw new GlobalException("是否本页搜索结果全选字段不能为空");
        if (customDetails.getCondition()) {
            customDetails.setIds(null);
        } else {
            if (ObjectUtils.isEmpty(customDetails.getIds()) || customDetails.getIds().size() == 0)
                throw new GlobalException("本页选中，主键id集合不能为空");
            CallCustomDetails customPojo = new CallCustomDetails();
            customPojo.setIds(customDetails.getIds());
            customDetails = customPojo;
        }
        List<ExportCustomPojo> list = customDetailsService.selectByListExport(customDetails);
        if (!ObjectUtils.isEmpty(list)) {
            for (ExportCustomPojo row : list) {
                Integer noteByType = row.getNoteByType();
                if (noteByType != null) {
                    if (noteByType == 0) row.setNoteBy(row.getCname());
                    else if (noteByType == 1) row.setNoteBy(row.getEmployeeName());
                }
                String userNo = row.getUserNo() == null ? "" : row.getUserNo().toString();
                String memberName = row.getMemberName() == null ? "" : "(" + row.getMemberName() + ")";
                row.setEmployeeIdAndName(userNo + memberName);
            }
        }
        ExcelUtil<ExportCustomPojo> util = new ExcelUtil<>(ExportCustomPojo.class);
        String fileName = "客户列表（预测试外呼）" + FileConstant.getExcelSuffix();
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, list, "客户列表（预测试外呼）");
    }

    /**
     * 客户列表-批量删除
     *
     * @param customDetails 搜索条件
     * @return
     */
    @PostMapping("/deleteCustomDetails")
    public AjaxResult deleteCustomDetails(@RequestBody CallCustomDetails customDetails) {
        if (ObjectUtils.isEmpty(customDetails)) return AjaxResult.error("必要条件不能为空");
        if (ObjectUtils.isEmpty(customDetails.getCondition()))
            return AjaxResult.error("是否本页搜索结果全选字段不能为空");
//        删除的主键id
        List<Long> longList = new ArrayList<>();
        if (customDetails.getCondition()) {
            customDetails.setIds(null);
            longList = customDetailsService.selectByListDelete(customDetails);
        } else {
            if (ObjectUtils.isEmpty(customDetails.getIds()) || customDetails.getIds().size() == 0)
                return AjaxResult.error("本页选中，主键id集合不能为空");
            longList = customDetails.getIds();
        }
        if (ObjectUtils.isEmpty(longList)) return AjaxResult.error("删除数量为0");
//        根据客户主键id删除（假）客户数据
        int number = customDetailsService.deleteByIdList(longList);
        return AjaxResult.success("操作成功,删除数量：" + longList.size());
    }

    /**
     * 客户列表-编辑
     *
     * @param customDetails 备注内容/主键id
     * @return
     */
    @PostMapping("/updateCustomDetails")
    public AjaxResult updateCustomDetails(@RequestBody CallCustomDetails customDetails) {
        if (ObjectUtils.isEmpty(customDetails)) throw new GlobalException("必要参数不能为空");
        if (ObjectUtils.isEmpty(customDetails.getId())) throw new GlobalException("主键id不能为空");
        List<Long> ids = new ArrayList<>();
        ids.add(customDetails.getId());
        customDetails.setIds(ids);
        agCallCustomService.updateCustomDetails(customDetails);
        return AjaxResult.success("操作成功");
    }

    /**
     * 来电弹屏
     *
     * @param customDetails 搜索条件
     * @return
     */
    @GetMapping("/popOnScreen")
    public TableDataInfo popOnScreen(CallCustomDetails customDetails) {
        startPage();
        if (ObjectUtils.isEmpty(customDetails)) throw new GlobalException("必要参数不能为空");
        if (StringUtils.isEmpty(customDetails.getCasePhone())) throw new GlobalException("来电手机号不能为空");
        if (StringUtils.isEmpty(customDetails.getTaskUuid())) throw new GlobalException("任务uuid不能为空");
        List<CallCustomDetails> list = customDetailsService.selectPopOnScreen(customDetails);
        return getDataTable(list);
    }

    /**
     * 来电弹屏(工作手机)
     *
     * @param customDetails 搜索条件
     * @return {@link WorkCustomDetailsResponse}
     */
    @GetMapping("/workPhonePopOnScreen")
    public TableDataInfo workPhonePopOnScreen(WorkPhoneCustomDetailsRequest customDetails) {
        startPage();
        List<WorkCustomDetailsResponse> list = customDetailsService.selectWorkPopOnScreen(customDetails);
        return getDataTable(list);
    }

    /**
     * 来电弹屏-编辑备注
     *
     * @param customDetails 备注内容/主键id
     * @return
     */
    @PostMapping("/popOnScreenRemark")
    public AjaxResult popOnScreenRemark(@RequestBody CallCustomDetails customDetails) {
        if (ObjectUtils.isEmpty(customDetails) || ObjectUtils.isEmpty(customDetails.getCasePhone()))
            throw new GlobalException("来电手机号不能为空");
        List<Long> list = customDetailsService.selectPopOnScreenIds(customDetails);
        customDetails.setIds(list);
        agCallCustomService.updateCustomDetails(customDetails);
        return AjaxResult.success("操作成功");
    }
}
