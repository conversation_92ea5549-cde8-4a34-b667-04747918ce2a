package com.zws.call.agservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.zws.call.api.AiVoiceTaskDockingApi;
import com.zws.call.api.CallingTaskDockingApi;
import com.zws.call.config.CallConfig;
import com.zws.call.domain.*;
import com.zws.call.enums.CallStateEnum;
import com.zws.call.enums.CallTaskMarkEnum;
import com.zws.call.mapper.CallRecordMapper;
import com.zws.call.pojo.*;
import com.zws.call.service.*;
import com.zws.common.core.callcenter.enums.CallResultEnum;
import com.zws.common.core.callcenter.enums.CallroterEnum;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.utils.SplitUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.RemoteAppealService;
import com.zws.system.api.RemoteCaseService;
import com.zws.system.api.RemoteDisposeService;
import com.zws.system.api.domain.*;
import net.sf.cglib.beans.BeanMap;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import static cn.hutool.extra.cglib.CglibUtil.toMap;

@Component
public class AiVoiceTaskService {

    private static final Logger log = LoggerFactory.getLogger(AiVoiceTaskService.class);
    @Autowired
    private IAiVoiceTaskService aiVoiceTaskService;

    @Autowired
    private IIntelligenceTaskService intelligenceTaskService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IAiCallCustomRecordService customRecordService;
    @Autowired
    private IAiCallCustomDetailsService customDetailsService;
    @Autowired
    private CallConfig callConfig;
    @Autowired
    private CallingTaskDockingApi callingTaskDockingApi;
    @Autowired
    private AiVoiceTaskDockingApi aiVoiceTaskDockingApi;
    @Autowired
    private ICallRecordService callRecordService;
    @Autowired
    private CallRecordMapper callRecordMapper;
    @Autowired
    private RemoteDisposeService remoteDisposeService;
    @Autowired
    private RemoteAppealService remoteAppealService;


    /**
     * AI智能语音列表 - 列表查询
     * @param aiVoiceList
     * @return
     */
    public List<AiCallIntelligenceTask> findAiVoiceTaskList(AiCallIntelligenceTask aiVoiceList) {
        List<AiCallIntelligenceTask> list = aiVoiceTaskService.selectAllList(aiVoiceList);
        if (Objects.isNull(list)) {
            return list;
        }
        for (AiCallIntelligenceTask aiCallIntelligence : list) {
            List<String> stringList = new ArrayList<>();  //客户uuid
//            根据任务uuid查询统计已接听数量
            Map<String, Object> map = new HashMap<>();
            map.put("teamId", SecurityUtils.getTeamId());
            map.put("taskUuid", aiCallIntelligence.getCallCenterUuid());
            map.put("connectFlag", CallTaskMarkEnum.CONNECTED.getCode());
            int connected = 0;
            List<String> reloadList = intelligenceTaskService.selectCallReloadList(map);
            if (!ObjectUtils.isEmpty(reloadList)) {
                connected = reloadList.size();
                stringList.addAll(reloadList);
            }
//            根据任务uuid查询统计未接通数量
            map.put("customUuidList", stringList);
            map.put("connectFlag", CallTaskMarkEnum.NOT_CONNECTED.getCode());
            int notConnected = 0;
            List<String> strings = intelligenceTaskService.selectCallReloadList(map);
            if (!ObjectUtils.isEmpty(strings)) {
                notConnected = strings.size();
                stringList.addAll(strings);
            }
//
//            AiTaskStatisticsPojo statisticsPojo = getTaskStatisticsPojoCount(aiCallIntelligence.getCallCenterUuid());
//
//            //未接通：黑名单＋拦截+无法接通+空号（呼叫未到被叫侧）。
//            int notConnected2 = statisticsPojo.getOtherQuantity() + statisticsPojo.getVacantNumberCount() + statisticsPojo.getUnableToConnectCount();
//
////            根据任务uuid查询统计漏接数量
//            map.put("customUuidList", stringList);
//            map.put("connectFlag", CallTaskMarkEnum.FUMBLE.getCode());
//            int fumble = 0;
//            List<String> strs = intelligenceTaskService.selectCallReloadList(map);
//            if (!ObjectUtils.isEmpty(strs)) {
//                fumble = strs.size();
//                stringList.addAll(strs);
//            }
//            呼叫总数量
            int total = aiCallIntelligence.getCallCount() == null ? 0 : aiCallIntelligence.getCallCount();

//            已接通数量（已接听+漏接）
//            int connectedTotal = connected + fumble;
//            已呼叫数量
            //int calledAlready = connected + notConnected + fumble;
            int calledAlready = connected + notConnected;

//            //计算接通率
            BigDecimal bigDecimal = BigDecimal.ZERO;
//            if (connectedTotal == 0 || calledAlready == 0) bigDecimal = BigDecimal.ZERO;
//            else
//                bigDecimal = new BigDecimal(connectedTotal).divide(new BigDecimal(calledAlready), 4, BigDecimal.ROUND_HALF_UP);
//            //接通率：接通量÷呼叫总数x100%
//            aiCallIntelligence.setReachability(bigDecimal.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP));

//            计算任务完成进度(任务进度=已呼叫/总数量)
            BigDecimal taskProgress = BigDecimal.ZERO;
            if (total == 0 || calledAlready == 0) bigDecimal = BigDecimal.ZERO;
            else
                taskProgress = new BigDecimal(calledAlready).divide(new BigDecimal(total), 4, BigDecimal.ROUND_HALF_UP);
            aiCallIntelligence.setTaskCompleteSchedule(taskProgress.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP));

            //完成进度100%时，更新任务状态为已完成
            if ((BigDecimal.valueOf(100.00).compareTo(aiCallIntelligence.getTaskCompleteSchedule()) == 0)){
                if (!aiCallIntelligence.getTaskStatus().equals(CallStateEnum.COMPLETED.getCode())){
                    aiCallIntelligence.setTaskStatus(CallStateEnum.COMPLETED.getCode());
                    //更新任务状态到数据库
                    AiCallIntelligenceTask task = new AiCallIntelligenceTask();
                    task.setId(aiCallIntelligence.getId());
                    task.setTaskStatus(CallStateEnum.COMPLETED.getCode());
                    aiVoiceTaskService.updateById(task);
                }
            }


            //接通率：接通量÷已呼叫数量x100%
            if (calledAlready > 0 && connected>0) {
                // 使用 divide 方法时指定舍入模式
                BigDecimal reachability = new BigDecimal(connected)
                        .divide(new BigDecimal(calledAlready), 4, BigDecimal.ROUND_HALF_UP) // 保留 4 位小数
                        .multiply(BigDecimal.valueOf(100)) // 转换为百分比
                        .setScale(2, BigDecimal.ROUND_HALF_UP); // 最终保留 2 位小数

                aiCallIntelligence.setReachability(reachability);
                //跟新数据库数据
                AiCallIntelligenceTask task = new AiCallIntelligenceTask();
                task.setId(aiCallIntelligence.getId());
                task.setReachability(reachability);
                aiVoiceTaskService.updateById(task);
            }


        }

        return list;
    }


    /**
     * 获取ai语音任务话术/短信模板列表
     * @return
     */
    public List<AiVoiceTplListPojo> getAiVoiceTplList() {
        //        根据机构id查询机构企业编码
        EnterpriseDataPojo dataPojo = customRecordService.selectCompanyNum(SecurityUtils.getTeamId().intValue());
        if (ObjectUtils.isEmpty(dataPojo)) throw new GlobalException("企业信息查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(callConfig.getApiFlag()))
            throw new GlobalException("企业api标识查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(dataPojo.getCompanyNum()))
            throw new GlobalException("企业编码查询为空，该机构无法创建任务");

        //推送数据到呼叫中心
        AiCallCenterTaskPojo taskPojo = new AiCallCenterTaskPojo();
        taskPojo.setServiceHostUrl(callConfig.getHostUrl());
        taskPojo.setApiFlag(callConfig.getApiFlag());
        taskPojo.setEnterpriseNum(dataPojo.getCompanyNum());

//        //测试数据
//        taskPojo.setApiFlag("test-api");
//        taskPojo.setEnterpriseNum("2000");

        //调用呼叫中心获取模板
        List<AiVoiceTplListPojo> aiVoiceTplListPojo = aiVoiceTaskDockingApi.getAiVoiceTplList(taskPojo);
//        List<String> list = new ArrayList<>();
//        for (AiVoiceTplListPojo template : aiVoiceTplListPojo) {
//            list.add(template.getVoiceTplUuid());
//            list.add(template.getTempName());
//            list.add(template.getTempContent());
//            list.add(template.getMessageSign());
//            list.add(template.getMessageContent());
//        }
        return aiVoiceTplListPojo;
    }



    /**
     * 校验AI语音通知任务参数
     *
     * @param task
     */
    public void checkAiTaskData(AiCallIntelligenceTask task) {
        if (ObjectUtils.isEmpty(task)) throw new GlobalException("任务配置信息不能为空");
        if (ObjectUtils.isEmpty(task.getExecutionTime())) throw new GlobalException("执行时间不能为空");
        if (ObjectUtils.isEmpty(task.getTaskStartTime())) throw new GlobalException("任务开始时间不能为空");
        if (ObjectUtils.isEmpty(task.getTaskEndTime())) throw new GlobalException("任务结束时间不能为空");
        if (ObjectUtils.isEmpty(task.getExecutionCallTime())) throw new GlobalException("外呼时段不能为空");
        if (task.getRecallCount() == null) throw new GlobalException("重呼次数不能为空");
        if (task.getRecallCount() > 3) throw new GlobalException("针对某号码第一次外呼未接通，重呼的次数，不能超过3次");
        if (task.getRecallCount() != 0) {
            if (task.getRecallMinute() == null) throw new GlobalException("重呼间隔不能为空");
            if (task.getRecallMinute() < 5 || task.getRecallMinute() > 120)
                throw new GlobalException("重呼间隔应在5-120分钟范围内");
        }
    }


    /**
     * AI智能语音列表
     *      状态：执行、暂停、撤销
     */
    @Transactional
    public void updateAiVoiceStatus( AiCallIntelligenceTask aiVoiceList) {
        //        根据任务主键id集合查询任务对应的uuid
        Map<String, Object> map = new HashMap<>();
        map.put("idList", aiVoiceList.getIds());
        map.put("teamId", aiVoiceList.getTeamId());
        List<String> list = aiVoiceTaskService.selectTaskUuidList(map);
        if (ObjectUtils.isEmpty(list)) throw new GlobalException("任务查询为空");
//        根据机构id查询机构企业编码
        EnterpriseDataPojo dataPojo = customRecordService.selectCompanyNum(aiVoiceList.getTeamId());
        if (ObjectUtils.isEmpty(dataPojo)) throw new GlobalException("企业信息查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(callConfig.getApiFlag()))
            throw new GlobalException("企业api标识查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(dataPojo.getCompanyNum()))
            throw new GlobalException("企业编码查询为空，该机构无法创建任务");
        AiCallCenterTaskPojo centerTaskPojo = new AiCallCenterTaskPojo();
        centerTaskPojo.setApiFlag(callConfig.getApiFlag());  //企业api标识
        centerTaskPojo.setEnterpriseNum(dataPojo.getCompanyNum());  //企业编号
        centerTaskPojo.setTaskUuids(list);
        centerTaskPojo.setStatus(aiVoiceList.getTaskStatus());
        centerTaskPojo.setServiceHostUrl(callConfig.getHostUrl());
        //根据任务uuid修改任务状态
        AiCallIntelligenceTask task = new AiCallIntelligenceTask();
        task.setCallCenterUuidList(list);
        task.setTaskStatus(aiVoiceList.getTaskStatus());
        task.setUpdateTime(new Date());
        //重新计算接通率
        BigDecimal reachability = aiVoiceReachability(aiVoiceList);
        task.setReachability(reachability);
        //更新
        aiVoiceTaskService.updateByUuidList(task);
        //调用呼叫中心
        aiVoiceTaskDockingApi.updateCallTask(centerTaskPojo);

    }


    /**
     * 新增AI语音任务-提交任务
     * @param dataPojo
     * @param threadEntityPojo
     * @return
     */

    public Long submitTaskData(ImportAiVoiceDataPojo dataPojo, ThreadEntityPojo threadEntityPojo) {
        if (ObjectUtils.isEmpty(dataPojo.getKey())) throw new GlobalException("正确数据key不能为空");
        if (!redisService.hasKey(dataPojo.getKey())) throw new GlobalException("正确数据缓存不存在或已过期");
        List<AiCallCustomDetails> list = redisService.getCacheList(dataPojo.getKey(), AiCallCustomDetails.class);
        if (ObjectUtils.isEmpty(list)) throw new GlobalException("正确数据缓存查询为空");
//        写入导入客户记录表并返回主键id
        AiCallCustomRecord record = new AiCallCustomRecord();
//        record.setName(dataPojo.getTaskName());
        record.setFileUrl(dataPojo.getFileUrl());
        record.setOriginalFileName(dataPojo.getOriginalFileName());
        record.setCreateId(threadEntityPojo.getUserId());
        record.setCreateBy(threadEntityPojo.getUser());
        record.setCreateType(threadEntityPojo.getType());
        record.setTeamId(threadEntityPojo.getCreateId());
        record.setCustomerType(1);
        Long aLong = customRecordService.insert(record);

        List<AiCallCustomDataPojo> dataPojoList = new ArrayList<>();
//        批量写入客户信息
        for (AiCallCustomDetails row : list) {
//            生成客户uuid，与呼叫中心保持一致进行话单关联
            //生成的是不带-的字符串，类似于：b17f24ff026d40949c85a24f4f375d42
            String simpleUUID = IdUtil.simpleUUID();
            row.setUuid(simpleUUID);
            row.setRecordId(aLong);
            row.setCreateTime(new Date());
            row.setCreateType(threadEntityPojo.getType());
            row.setTeamId(threadEntityPojo.getCreateId());
            if (row.getUserNo() != null) {
                Map<String, Object> hashMap = new HashMap<>();
                hashMap.put("teamId", threadEntityPojo.getCreateId());
                hashMap.put("employeesWorking", row.getUserNo());
                Integer integer = customRecordService.selectEmployeesId(hashMap);
                row.setMemberId(integer);
            }
            row.setCreateById(threadEntityPojo.getUserId());
            row.setDelFlag(BaseConstant.DelFlag_Being);
            row.setCustomerType(1);

//            处理推送给呼叫中心的客户信息
            AiCallCustomDataPojo data = new AiCallCustomDataPojo();
            data.setPhone(row.getCasePhone());
            data.setCaseId(row.getCaseId());
            data.setCustomerId(row.getUuid());
            data.setVoiceTplArgs(dataPojo.getAiCallIntelligenceTask().getHookArgs());
            dataPojoList.add(data);
        }
        //插入客户信息
        customDetailsService.insertList(list);

        //保存任务到数据库中
        AiCallIntelligenceTask task = dataPojo.getAiCallIntelligenceTask();
        //checkAiTaskData(task);
        task.setTaskName(dataPojo.getTaskName());
        task.setTeamId(threadEntityPojo.getCreateId());
        task.setEmployeeId(threadEntityPojo.getUserId());
        task.setCreateBy(threadEntityPojo.getUser());
        task.setCreateTime(new Date());
        task.setCreateId(threadEntityPojo.getUserId());
        task.setCreateType(threadEntityPojo.getType());
        task.setCustomId(aLong);
        task.setTaskStatus(CallStateEnum.IN_PROGRESS.getCode());
        task.setOperationType(1);
        task.setDelFlag(BaseConstant.DelFlag_Being);
        task.setCallCount(list.size());
        Long idLong= aiVoiceTaskService.insert(task);

        //调用添加智能语音任务接口
        return callCenterDocking(task, dataPojoList, idLong);
    }

    /**
     * 任务数据推送给呼叫中心-添加AI语音通知任务
     *
     * @param task
     * @param dataPojoList
     * @param idLong       任务主键id
     */
    public Long callCenterDocking(AiCallIntelligenceTask task, List<AiCallCustomDataPojo> dataPojoList, Long idLong) {
        if (idLong == null) throw new GlobalException("任务主键id为空");
//        根据机构id查询机构企业编码
        EnterpriseDataPojo dataPojo = customRecordService.selectCompanyNum(task.getTeamId());
        if (ObjectUtils.isEmpty(dataPojo)) throw new GlobalException("企业信息查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(callConfig.getApiFlag()))
            throw new GlobalException("企业api标识查询为空，该机构无法创建任务");
        if (StringUtils.isEmpty(dataPojo.getCompanyNum()))
            throw new GlobalException("企业编码查询为空，该机构无法创建任务");
        AiCallCenterTaskPojo centerTaskPojo = new AiCallCenterTaskPojo();
        centerTaskPojo.setCallList(dataPojoList);
        centerTaskPojo.setEnterpriseNum(dataPojo.getCompanyNum());  //企业编号
        //TODO:语音通知模板
        centerTaskPojo.setVoiceTplUuid(task.getVoiceTplUuid());
        centerTaskPojo.setTaskName(task.getTaskName());//任务名称
        centerTaskPojo.setExecutionTime(task.getExecutionTime());//执行时间
        centerTaskPojo.setExecutionCallTime(task.getExecutionCallTime());//执行呼叫时间
        //任务开始时间
        centerTaskPojo.setTaskStartTime(task.getTaskStartTime());
        //任务结束时间
        centerTaskPojo.setTaskEndTime(task.getTaskEndTime());
        centerTaskPojo.setRecall(task.getRecallCount());//重呼次数
        centerTaskPojo.setRecallMinute(task.getRecallMinute());//重呼间隔
        centerTaskPojo.setRemark(task.getRemark());//备注


        centerTaskPojo.setSmsFlag(task.getIsSendMessage());
        centerTaskPojo.setSmsCondition(task.getSenderTarget());
        centerTaskPojo.setCallTarget(task.getCallRecipient());
        centerTaskPojo.setTaskFlag(task.getOperationType());
        centerTaskPojo.setServiceHostUrl(callConfig.getHostUrl());
        centerTaskPojo.setApiFlag(callConfig.getApiFlag());  //企业api标识
        //调用添加智能语音任务接口
        CallTaskReturnPojo returnPojo = aiVoiceTaskDockingApi.submitCallTask(centerTaskPojo);

//        将任务uuid更新写入数据库
        AiCallIntelligenceTask aiTask = new AiCallIntelligenceTask();
        aiTask.setId(idLong);
        aiTask.setCallCenterUuid(returnPojo.getTaskUuid());
        aiTask.setUpdateTime(new Date());
        aiVoiceTaskService.updateById(aiTask);


        System.err.println("呼叫中心返回状态：" + returnPojo.getTaskUuid());
        return idLong;

    }

    /**
     * 提交任务并执行
     * @param dataPojo
     * @param threadEntityPojo
     * @return
     */
    public Long submitTaskAndExecuteAiTask(ImportAiVoiceDataPojo dataPojo, ThreadEntityPojo threadEntityPojo) {
        //提交任务
        Long submitTaskData = submitTaskData(dataPojo, threadEntityPojo);
        ArrayList<Long> list = new ArrayList<>();
        list.add(submitTaskData);

        AiCallIntelligenceTask task = new AiCallIntelligenceTask();
        task.setIds(list);
        task.setTeamId(SecurityUtils.getTeamId().intValue());
        task.setTaskStatus(CallStateEnum.IN_PROGRESS.getCode());
        //执行任务
        updateAiVoiceStatus(task);
        return submitTaskData;
    }

    /**
     * 计算接通率
     */
    private BigDecimal aiVoiceReachability(AiCallIntelligenceTask task) {

        Map<String, Object> map = new HashMap<>();
        //根据任务uuid查询统计已接听数量
        List<String> stringList = new ArrayList<>();  //客户uuid
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("taskUuid", task.getCallCenterUuid());
        map.put("connectFlag", CallTaskMarkEnum.CONNECTED.getCode());
        int connected = 0;
        //根据呼叫状态查询客户uuid
        List<String> reloadList = intelligenceTaskService.selectCallReloadList(map);
        if (!ObjectUtils.isEmpty(reloadList)) {
            connected = reloadList.size();
            stringList.addAll(reloadList);
        }


//        查找已接听的话单数量
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("teamId", SecurityUtils.getTeamId());
        hashMap.put("taskUuid", task.getCallCenterUuid());
        hashMap.put("connectFlag", CallTaskMarkEnum.CONNECTED.getCode());
//        Integer connectedCount = intelligenceTaskService.selectCallRecordCount(hashMap);
        //获取已接听的通话时间
//        Integer allAgentDuration = aiVoiceTaskService.selectCallRecordAgentDuration(hashMap);
        //获取已接听的通话时长平均值=通话总时长/已接听数量
//        int argAgentDuration = allAgentDuration / connectedCount;


//        查找漏接的话单数量
        hashMap.put("connectFlag", CallTaskMarkEnum.FUMBLE.getCode());
        Integer fumbleCount = intelligenceTaskService.selectCallRecordCount(hashMap);

        //未接听  已接通情况下除了（已接听和漏接）的情况： 通话中+无人接听+停机+忙线+关机等呼叫到达被叫侧；
        Long noAnswerCount = getNoAnswerCount(task.getCallCenterUuid());

        AiTaskStatisticsPojo statisticsPojo = getTaskStatisticsPojoCount(task.getCallCenterUuid());

        //未接通：黑名单＋拦截+无法接通+空号（呼叫未到被叫侧）。
        int notConnected = statisticsPojo.getOtherQuantity() + statisticsPojo.getVacantNumberCount() + statisticsPojo.getUnableToConnectCount();


        //总呼叫数
        int actuallyCalledAlready = connected + fumbleCount + notConnected + noAnswerCount.intValue();

        //接通量：接听+漏接＋未接听（呼叫到达被叫侧）
        Integer throughput = connected + fumbleCount + noAnswerCount.intValue();

        //接通率：接通量÷呼叫总数x100%
        BigDecimal reachability;
        if (actuallyCalledAlready <= 0) {
            // 如果总呼叫数为 0 或空，接通率设为 0
            reachability = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            // 使用 divide 方法时指定舍入模式
            reachability = new BigDecimal(throughput)
                    .divide(new BigDecimal(actuallyCalledAlready), 4, BigDecimal.ROUND_HALF_UP) // 保留 4 位小数
                    .multiply(BigDecimal.valueOf(100)) // 转换为百分比
                    .setScale(2, BigDecimal.ROUND_HALF_UP); // 最终保留 2 位小数
        }
        return reachability;

    }

    /**
     * 查看结果报告 -数据统计
     * @param id
     * @param teamId
     * @return
     */
    public AiTaskStatisticsPojo agCallTaskStatistics(Long id, Integer teamId) {
        //        查询列表数据
        //IntelligenceTask intelligenceTask = intelligenceTaskService.selectById(id, teamId);
        AiCallIntelligenceTask task= aiVoiceTaskService.selectById(id, teamId);
        if (ObjectUtils.isEmpty(task)) throw new GlobalException("主键不存在，查询任务信息为空");
        if (ObjectUtils.isEmpty(task.getCallCenterUuid())) throw new GlobalException("uuid为空，数据不存在");

        Map<String, Object> map = new HashMap<>();
        AiTaskStatisticsPojo statisticsPojo = getTaskStatisticsPojoCount(task.getCallCenterUuid());

        //根据任务uuid查询统计已接听数量
        List<String> stringList = new ArrayList<>();  //客户uuid
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("taskUuid", task.getCallCenterUuid());
        map.put("connectFlag", CallTaskMarkEnum.CONNECTED.getCode());
        int connected = 0;
        //根据呼叫状态查询客户uuid
        List<String> reloadList = intelligenceTaskService.selectCallReloadList(map);
        if (!ObjectUtils.isEmpty(reloadList)) {
            connected = reloadList.size();
            stringList.addAll(reloadList);
        }

//            根据任务uuid查询统计未接通数量
        map.put("customUuidList", stringList);
        map.put("connectFlag", CallTaskMarkEnum.NOT_CONNECTED.getCode());
        int notConnected = 0;
        List<String> strings = intelligenceTaskService.selectCallReloadList(map);
        if (!ObjectUtils.isEmpty(strings)) {
            notConnected = strings.size();
            stringList.addAll(strings);
        }

////            根据任务uuid查询统计漏接数量
//        map.put("customUuidList", stringList);
//        map.put("connectFlag", CallTaskMarkEnum.FUMBLE.getCode());
//        int fumble = 0;
//        List<String> list = intelligenceTaskService.selectCallReloadList(map);
//        if (!ObjectUtils.isEmpty(list)) {
//            fumble = list.size();
//            stringList.addAll(list);
//        }

//        //未接通：黑名单＋拦截+无法接通+空号（呼叫未到被叫侧）。
//        int notConnected = statisticsPojo.getOtherQuantity() + statisticsPojo.getVacantNumberCount() + statisticsPojo.getUnableToConnectCount();

//        查找已接听的话单数量
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("teamId", SecurityUtils.getTeamId());
        hashMap.put("taskUuid", task.getCallCenterUuid());
        hashMap.put("connectFlag", CallTaskMarkEnum.CONNECTED.getCode());
        Integer connectedCount = intelligenceTaskService.selectCallRecordCount(hashMap);
        //获取已接听总通话时间
        Integer allAgentDuration = aiVoiceTaskService.selectCallRecordAgentDuration(hashMap);
        if (allAgentDuration==null){
            allAgentDuration = 0;
        }
        //获取已接听的通话时长平均值=通话总时长/已接听数量
        int  argAgentDuration = 0;
        if ( connected == 0){
            argAgentDuration = 0;
        }else {
            argAgentDuration = allAgentDuration / connected;
        }



////        查找漏接的话单数量
//        hashMap.put("connectFlag", CallTaskMarkEnum.FUMBLE.getCode());
//        Integer fumbleCount = intelligenceTaskService.selectCallRecordCount(hashMap);

        //未接听  已接通情况下除了（已接听和漏接）的情况： 通话中+无人接听+停机+忙线+关机等呼叫到达被叫侧；
        Long noAnswerCount = getNoAnswerCount(task.getCallCenterUuid());

//        呼叫总数量
        int total = task.getCallCount() == null ? 0 : task.getCallCount();
        //所有话单数量
//        Long callTotalCount = getCallTotalCount(intelligenceTask.getCallCenterUuid(), null);

        //实际已呼叫数量 =
        // 已接听 + 漏接
        // 未接通（黑名单＋拦截+无法接通+空号（呼叫未到被叫侧）） +
        // 未接听（通话中+无人接听+停机+忙线+关机）
        //int actuallyCalledAlready = connected + fumbleCount + notConnected + noAnswerCount.intValue();
        int actuallyCalledAlready = connected + notConnected;

//        总的接通数量
//        int connectQuantity = connectedCount + fumbleCount;
//        计算接听率（已接听/接通数量）
        BigDecimal answerRate = BigDecimal.ZERO;
        if (connectedCount == 0 || actuallyCalledAlready == 0) answerRate = BigDecimal.ZERO;
        else
//            answerRate = new BigDecimal(connectedCount).divide(new BigDecimal(connectQuantity), 4, BigDecimal.ROUND_HALF_UP);
            answerRate = new BigDecimal(connectedCount).divide(new BigDecimal(actuallyCalledAlready), 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal bigDecimal = answerRate.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);

//        已呼叫任务数量
        int calledAlready = connected + notConnected ;
////        计算接通率
//        BigDecimal bigDecimal = BigDecimal.ZERO;
//        if (connected == 0 || calledAlready == 0) bigDecimal = BigDecimal.ZERO;
//        else bigDecimal = new BigDecimal(connected).divide(new BigDecimal(calledAlready), 4, BigDecimal.ROUND_HALF_UP);
//        已接通数量
        int connectedFumble = connected ;
        //接通量：接听+漏接＋未接听（呼叫到达被叫侧）
//        Integer throughput = connected + fumbleCount + noAnswerCount.intValue();
        //接通率：接通量÷呼叫总数x100%
        BigDecimal reachability;
        if (actuallyCalledAlready <= 0) {
            // 如果总呼叫数为 0 或空，接通率设为 0
            reachability = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            // 使用 divide 方法时指定舍入模式
            reachability = new BigDecimal(connected)
                    .divide(new BigDecimal(actuallyCalledAlready), 4, BigDecimal.ROUND_HALF_UP) // 保留 4 位小数
                    .multiply(BigDecimal.valueOf(100)) // 转换为百分比
                    .setScale(2, BigDecimal.ROUND_HALF_UP); // 最终保留 2 位小数
        }

        //返回的统计数据
        statisticsPojo.setTotal(total);//总数量
        statisticsPojo.setCalledAlready(calledAlready);//已呼叫数量
        statisticsPojo.setConnected(connected);//已接通数量
        statisticsPojo.setNotConnected(notConnected);//未接通数量
        statisticsPojo.setCreateTime(task.getCreateTime());//创建时间
        statisticsPojo.setRecallCount(task.getRecallCount());//重呼次数
        statisticsPojo.setRecallMinute(task.getRecallMinute());//重呼间隔
        statisticsPojo.setStatus(task.getTaskStatus());//状态
        statisticsPojo.setTaskName(task.getTaskName());//任务名称
        statisticsPojo.setExecutionCallTime(task.getExecutionCallTime());//执行通话时长
        statisticsPojo.setExecutionTime(task.getExecutionTime());//执行时间
        statisticsPojo.setTaskStartTime(task.getTaskStartTime());//任务开始时间
        statisticsPojo.setTaskEndTime(task.getTaskEndTime());//任务结束时间
        statisticsPojo.setRemark(task.getRemark());//备注
        statisticsPojo.setReachability(reachability);//接通率
        statisticsPojo.setTemplateName(task.getDialogueTemplateName());//对话模板名称
        statisticsPojo.setRobotCount(task.getRobotCount());//机器人数量
        statisticsPojo.setSenderTarget(task.getSenderTarget());//发送目标
        statisticsPojo.setMessageContent(task.getMessageTemplateContent());
        //任务完成进度=已呼叫数量 / 总数量
        BigDecimal taskCompleteSchedule = BigDecimal.ZERO;
        if (total == 0 || calledAlready == 0) taskCompleteSchedule = BigDecimal.ZERO;
        else
            taskCompleteSchedule = new BigDecimal(calledAlready).divide(new BigDecimal(total), 4, BigDecimal.ROUND_HALF_UP);
        statisticsPojo.setTaskCompleteSchedule(taskCompleteSchedule.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP));

        //总通话时长
        statisticsPojo.setTotalCallTime(allAgentDuration);
        //平均通话时长
        statisticsPojo.setAverageCallTime(argAgentDuration);

        //更新接通率到数据库
        AiCallIntelligenceTask task1 = new AiCallIntelligenceTask();
        task1.setId(task.getId());
        task1.setReachability(statisticsPojo.getReachability());
        aiVoiceTaskService.updateById(task1);

        return statisticsPojo;

    }

    /**
     * 分组查询所有回铃状态数量
     */
    @NotNull
    private AiTaskStatisticsPojo getTaskStatisticsPojoCount(String callCenterUuid) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("taskUuid", callCenterUuid);
        map.put("callResults", Arrays.asList(CallResultEnum.CALL_RESULT_SHUT_DOWN.getInfo(),
                CallResultEnum.CALL_RESULT_POWER_OFF.getInfo(),
                CallResultEnum.CALL_RESULT_BE_BUSY.getInfo(),
                CallResultEnum.CALL_RESULT_ON_THE_LINE.getInfo(),
                CallResultEnum.CALL_RESULT_NO_ANSWER.getInfo(),
                CallResultEnum.CALL_RESULT_VACANT_NUMBER.getInfo(),
                CallResultEnum.CALL_RESULT_UNABLE_TO_CONNECT.getInfo(),
                CallResultEnum.CALL_RESULT_UNKNOWN.getInfo()));
        //分组查询多个外呼效果字段数量
        Map<String, Object> callResultMap = callRecordMapper.selectCountByCallResultGroupBy(map);

        BiConsumer<String, Consumer<Integer>> setCount = (callResult, setter) -> {
            Integer count = Optional.ofNullable(callResultMap.get(callResult))
                    .filter(resultMap -> resultMap instanceof Map)
                    .map(resultMap -> (Map<String, Object>) resultMap)
                    .map(resultMap -> resultMap.get("count"))
                    .filter(cnt -> cnt instanceof Long)
                    .map(cnt -> ((Long) cnt).intValue())
                    .orElse(0);
            setter.accept(count);
        };
        // 使用定义的方法设置各个统计值
        AiTaskStatisticsPojo statisticsPojo = new AiTaskStatisticsPojo();
        setCount.accept(CallResultEnum.CALL_RESULT_SHUT_DOWN.getInfo(), statisticsPojo::setShutDownCount);
        setCount.accept(CallResultEnum.CALL_RESULT_POWER_OFF.getInfo(), statisticsPojo::setPowerOffCount);
        setCount.accept(CallResultEnum.CALL_RESULT_BE_BUSY.getInfo(), statisticsPojo::setBeBusyCount);
        setCount.accept(CallResultEnum.CALL_RESULT_ON_THE_LINE.getInfo(), statisticsPojo::setOnTheLineCount);
        setCount.accept(CallResultEnum.CALL_RESULT_NO_ANSWER.getInfo(), statisticsPojo::setNoAnswerCount);
        setCount.accept(CallResultEnum.CALL_RESULT_VACANT_NUMBER.getInfo(), statisticsPojo::setVacantNumberCount);
        setCount.accept(CallResultEnum.CALL_RESULT_UNABLE_TO_CONNECT.getInfo(), statisticsPojo::setUnableToConnectCount);
        setCount.accept(CallResultEnum.CALL_RESULT_UNKNOWN.getInfo(), statisticsPojo::setOtherQuantity);
        return statisticsPojo;
    }

    /**
     * 计算未接听数量
     * @param callCenterUuid
     * @return
     */
    private Long getNoAnswerCount(String callCenterUuid) {
        Long callRecordCount = callRecordService.lambdaQuery()
                .eq(CallRecord::getDelFlag, 0)
                .eq(CallRecord::getTeamId, SecurityUtils.getTeamId())
                .eq(CallRecord::getTaskUuid, callCenterUuid)
                .in(CallRecord::getCallResult,
                        CallResultEnum.CALL_RESULT_ON_THE_LINE.getInfo(),
                        CallResultEnum.CALL_RESULT_NO_ANSWER.getInfo(), CallResultEnum.CALL_RESULT_SHUT_DOWN.getInfo(),
                        CallResultEnum.CALL_RESULT_BE_BUSY.getInfo(), CallResultEnum.CALL_RESULT_POWER_OFF.getInfo())
                .count();
        return callRecordCount;
    }

    /**
     * 查看报告结果-数据列表
     * @param task
     * @return
     */
    public List<AiCallRecordDataPojo> agCallTaskDataList(AiCallIntelligenceTask task) {
//        查询任务详情
//        PageUtils.clearPage();
        AiCallIntelligenceTask selectById = aiVoiceTaskService.selectById(task.getId(), task.getTeamId());
        if (ObjectUtils.isEmpty(selectById)) throw new GlobalException("任务查询为空");
        if (ObjectUtils.isEmpty(selectById.getCallCenterUuid())) throw new GlobalException("任务uuid查询为空");
//        根据任务uuid查询对应话单
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("taskUuid", selectById.getCallCenterUuid());
        map.put("connectFlag", task.getConnectFlag());

//        Set<String> notAnswerResults = new HashSet<>(Arrays.asList(
//                CallResultEnum.CALL_RESULT_ON_THE_LINE.getInfo(), CallResultEnum.CALL_RESULT_NO_ANSWER.getInfo(),
//                CallResultEnum.CALL_RESULT_SHUT_DOWN.getInfo(), CallResultEnum.CALL_RESULT_BE_BUSY.getInfo(), CallResultEnum.CALL_RESULT_POWER_OFF.getInfo()
//        ));
//
//        //通过回铃状态查询 0=未接听 通话中+无人接听+停机+忙线+关机等呼叫到达被叫侧；
//        if (task.getConnectFlag2() != null && task.getConnectFlag2() == 0) {
//            map.put("callResults", notAnswerResults);
//            map.remove("connectFlag");
//        }
//        //通过回铃状态查询 2=未接通：黑名单＋拦截+无法接通+空号（呼叫未到被叫侧）
//        if (task.getConnectFlag() != null && task.getConnectFlag() == 2) {
//            map.put("callResults", Arrays.asList(CallResultEnum.CALL_RESULT_UNKNOWN.getInfo(),
//                    CallResultEnum.CALL_RESULT_UNABLE_TO_CONNECT.getInfo(), CallResultEnum.CALL_RESULT_VACANT_NUMBER.getInfo()));
//            map.remove("connectFlag");
//        }


        PageUtils.startPage();
        List<AiCallRecordDataPojo> callRecordDataPojos = aiVoiceTaskService.selectCallRecordList(map);

        return callRecordDataPojos;
    }










//-------------------------------------------案件勾选创建AI语音通知任务------------------------------------------
    /**
     * 提交任务-勾选案件（新增）
     * @param aiVoiceTask
     * @param threadEntityPojo
     * @return
     */
    @Transactional
    public Long caseSubmitTaskData(AiVoiceTask aiVoiceTask, ThreadEntityPojo threadEntityPojo) {
        AiCallIntelligenceTask task = BeanUtil.toBean(aiVoiceTask, AiCallIntelligenceTask.class);
//        校验任务名称机构内去重
        if (ObjectUtils.isEmpty(task.getTaskName())) throw new GlobalException("任务名称不能为空");
        Integer count = aiVoiceTaskService.selectByCount(task.getTaskName(), threadEntityPojo.getCreateId());
        if (count > 0) throw new GlobalException("任务名称已存在，请重新输入");
//        根据案件id以及团队id查询案件id以及联系人id和手机号
        if (task.getCallRecipient() == null) throw new GlobalException("呼叫对象不能为空");
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("teamId", threadEntityPojo.getCreateId());
        objectMap.put("caseIdList", threadEntityPojo.getCaseIdList());
        if (task.getCallRecipient() == 1) {
            objectMap.put("contactRelation", "本人");
        }
        List<CallCustomDetails> callList = intelligenceTaskService.selectContactsList(objectMap);
        List<AiCallCustomDetails> list = BeanUtil.copyToList(callList, AiCallCustomDetails.class);
        if (ObjectUtils.isEmpty(list)) throw new GlobalException("号码状态无效或已结清案件不支持创建AI语音通知任务");
//        写入导入客户记录表并返回主键id
        AiCallCustomRecord record = new AiCallCustomRecord();
        record.setCreateId(threadEntityPojo.getUserId());
        record.setCreateBy(threadEntityPojo.getUser());
        record.setCreateType(threadEntityPojo.getType());
        record.setTeamId(threadEntityPojo.getCreateId());
        record.setCustomerType(2);
        Long aLong = customRecordService.insert(record);

        List<AiCallCustomDataPojo> dataPojoList = new ArrayList<>();
//        批量写入客户信息
        for (AiCallCustomDetails row : list) {
//            生成客户uuid，与呼叫中心保持一致进行话单关联
            //生成的是不带-的字符串，类似于：b17f24ff026d40949c85a24f4f375d42
            String simpleUUID = IdUtil.simpleUUID();
            row.setUuid(simpleUUID);
            row.setRecordId(aLong);
            row.setCreateTime(new Date());
            row.setCreateType(threadEntityPojo.getType());
            row.setTeamId(threadEntityPojo.getCreateId());
            if (row.getUserNo() != null) {
                Map<String, Object> hashMap = new HashMap<>();
                hashMap.put("teamId", threadEntityPojo.getCreateId());
                hashMap.put("employeesWorking", row.getUserNo());
                Integer integer = customRecordService.selectEmployeesId(hashMap);
                row.setMemberId(integer);
            }
            row.setCreateById(threadEntityPojo.getUserId());
            row.setDelFlag(BaseConstant.DelFlag_Being);
            row.setCustomerType(2);
            row.setEntrustMoney(BigDecimal.ZERO);
            row.setRemainingDueMoney(BigDecimal.ZERO);
//            处理手机号/客户姓名解密
            row.setCaseName(FieldEncryptUtil.decrypt(row.getCaseName()));
            row.setCasePhone(FieldEncryptUtil.decrypt(row.getCasePhone()));

//            处理推送给呼叫中心的客户信息
            AiCallCustomDataPojo data = new AiCallCustomDataPojo();
            data.setPhone(row.getCasePhone());
            data.setCaseId(row.getCaseId());
            data.setCustomerId(row.getUuid());

            //处理模板参数的数据
            AiFieldRecord aiFieldRecord = new AiFieldRecord();
            AjaxResult ajaxResult = null;
            if (task.getNumber().equals("0")) {
                //todo:催收端模板参数处理
                ajaxResult = remoteDisposeService.selectMapWithCaseDetailsCis(row.getCaseId());
            }else{
                //todo:调诉端模板参数处理
                ajaxResult = remoteAppealService.selectMapWithCaseDetailsCis(row.getCaseId());
            }
            Map<String, Object> map = (Map<String, Object>) ajaxResult.get("data");
            Map<String, Object> infoBase = (Map<String, Object>) map.get("infoBase");
            aiFieldRecord.setReductionState(infoBase.get("state") == null ? "无" : infoBase.get("state").toString());

            for (Map.Entry<String, Object> entry : infoBase.entrySet()) {
                entry.setValue(entry.getValue() == null ? "无" : entry.getValue().toString());
            }
            BeanUtil.fillBeanWithMap(infoBase, aiFieldRecord, false, true);

            Map<String, Object> infoLoan = (Map<String, Object>) map.get("infoLoan");
            //日期格式处理 把infoLoan的发卡日期转换为yyyy-MM-dd
//                infoLoan.put("cardIssuanceDate", DateUtil.format(DateUtil.parse(infoLoan.get("cardIssuanceDate") == null ? "无" : infoLoan.get("cardIssuanceDate").toString()), "yyyy-MM-dd"));

            for (Map.Entry<String, Object> entry : infoLoan.entrySet()) {
                entry.setValue(entry.getValue() == null ? "无" : entry.getValue().toString());
            }
            BeanUtil.fillBeanWithMap(infoLoan, aiFieldRecord, false, true);

            //特殊数据处理
            aiFieldRecord.setNowDate(DateUtil.format(new Date(), "yyyy-MM-dd"));
            log.info("aiFieldRecord:{}", aiFieldRecord);
            BeanMap map1 = toMap(aiFieldRecord);
            //循环查询数据库表ai_field数据，args为key，field为value
            List<AiField> aiFieldList = aiVoiceTaskService.selectAiFieldList();
            HashMap<String, String> hashMap = new HashMap<>();
            String hookArgs = task.getHookArgs();
            //对hookArgs使用，进行分割取出数据
            String[] args = hookArgs.split(",");
            for (AiField aiField : aiFieldList){
                //通过aiField.getField()去匹配map1中的key，如果匹配到就赋值给hashMap
                if (map1.containsKey(aiField.getField())){
                    hashMap.put(aiField.getArgs(),map1.get(aiField.getField())==null?"无":map1.get(aiField.getField()).toString());
                }
            }
            //循环给args中的数据赋值给argsMap，其中key是args的数据，value是map1中的value，key要对于hashMap中的key
            HashMap<String, String> argsMap = new HashMap<>();
            for (String arg : args) {
                if (hashMap.containsKey(arg)){
                    argsMap.put(arg,hashMap.get(arg)==null?"无":hashMap.get(arg));
                }else {
                    argsMap.put(arg,"暂时无法使用该模板");
                }
            }
            String jsonStr = JSONUtil.toJsonStr(argsMap);
            data.setVoiceTplArgs(jsonStr);


            dataPojoList.add(data);
        }
        //插入客户信息
        customDetailsService.insertList(list);


//        插入任务信息
        checkAiTaskData(task);
//        task.setTaskName(dataPojo.getTaskName());
        task.setTeamId(threadEntityPojo.getCreateId());
        task.setEmployeeId(threadEntityPojo.getUserId());
        task.setCreateBy(threadEntityPojo.getUser());
        task.setCreateTime(new Date());
        task.setCreateId(threadEntityPojo.getUserId());
        task.setCreateType(threadEntityPojo.getType());
        task.setCustomId(aLong);
        task.setTaskStatus(CallStateEnum.NOT_STARTED.getCode());
        task.setOperationType(2);
        task.setDelFlag(BaseConstant.DelFlag_Being);
        //计算接通率
        BigDecimal voicereachability = aiVoiceReachability(task);
        task.setReachability(voicereachability);
//        呼叫总数量
        task.setCallCount(list.size());
        Long idLong = aiVoiceTaskService.insert(task);
//        任务信息推送给呼叫中心
        return callCenterDocking(task, dataPojoList, idLong);
    }



    /**
     * 处理模板参数，拼接成josn格式的字符串
     * @param map
     * @param hookArgs
     * @return
     */
    //
    private String generateConnectedString(HashMap<String, String> map, String hookArgs) {
        String[] fields = hookArgs.split(",");
        HashMap<String, String> resultMap = new HashMap<>();

        for (String field : fields) {
            if (field.startsWith("【") && field.endsWith("】")) {
                String keyInTemplate = field;
                String key = field.substring(1, field.length() - 1); // 去掉【】

                if ("当前日期".equals(key)) {
                    resultMap.put("【当前日期】",LocalDate.now().toString());
                } else {
                    String value = map.getOrDefault(key, "");
                    resultMap.put(keyInTemplate,value);
                }
            }
        }
        //把result转为json字符串
        return JSONUtil.toJsonStr(resultMap);
    }


    /**
     * 创建AI语音通知任务并执行-案件管理
     * @param aiVoiceTask
     * @param entityPojo
     * @return
     */
    public Long caseSubmitTaskDataAndExecute(AiVoiceTask aiVoiceTask, ThreadEntityPojo entityPojo) {
        //创建AI语音通知任务
        Long id = caseSubmitTaskData(aiVoiceTask, entityPojo);
        ArrayList<Long> list = new ArrayList<>();
        list.add(id);

        //根据uuid获取任务ids
        AiCallIntelligenceTask task = new AiCallIntelligenceTask();
        task.setTeamId(SecurityUtils.getTeamId().intValue());
        task.setIds(list);
        task.setTaskStatus(CallStateEnum.IN_PROGRESS.getCode());
        //任务执行
        updateAiVoiceStatus(task);
        return id;
    }


    /**
     * 定时任务获取短信回复内容
     */
    public Integer selectSmsContentTask() {

        //获取当前时间-5分钟
        Date now = DateUtil.offsetMinute(new Date(), -5);


        AiCallCenterTaskPojo taskPojo = new AiCallCenterTaskPojo();
        taskPojo.setServiceHostUrl(callConfig.getHostUrl());
            taskPojo.setApiFlag(callConfig.getApiFlag());

        //呼叫类型
        String callroter = CallroterEnum.newvoccall.getCode();

        //根据话单表获取任务uuidList
        List<String> taskUuidList = callRecordService.selectTaskUuidList(now,taskPojo,callroter);
        taskPojo.setTaskUuids(taskUuidList);

        if (taskUuidList==null){
            log.info("无回复内容新增或修改");
            return 0;
        }

        //调用呼叫中心接口获取短信回复内容
        List<SmsContentPojo> smsContents = aiVoiceTaskDockingApi.selectSmsContentTask(taskPojo);


        //查询数据库表根据callId和手机号判断是否存在 循环遍历
        if (ObjectUtil.isNotEmpty(smsContents)){
            for (SmsContentPojo smsContentPojo : smsContents) {
                //根据callId和手机号判断是否存在
                 Integer countByCallIdAndPhone = aiVoiceTaskService.selectByCallIdAndPhoneCount(smsContentPojo);
                if (countByCallIdAndPhone==0){
                    //新增数据
                    aiVoiceTaskService.insertSmsContent(smsContentPojo);
                }
               if (countByCallIdAndPhone>0){
                   //更新数据
                   aiVoiceTaskService.updateSmsContent(smsContentPojo);
               }
            }
        }

        SmsContentPojo smsContentPojo = new SmsContentPojo();
        smsContentPojo.setCallType(CallroterEnum.newvoccall.getCode());
        smsContentPojo.setNowTime(DateUtil.offsetHour(now, -72));
        smsContentPojo.setStatus(3);
        smsContentPojo.setResult("发送短信时间超时了");
        //更新短信回复状态和回复内容 条件是状态为0 呼叫类型nowvoccall 时间在now的72小时以前的时间
        Integer integer= aiVoiceTaskService.updateSmsContentStatusAndContent(smsContentPojo);

        return integer;


    }
}
