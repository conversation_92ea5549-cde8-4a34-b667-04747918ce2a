package com.zws.call.service;

import com.zws.call.domain.CallCustomDetails;
import com.zws.call.pojo.CallRecordDataPojo;
import com.zws.call.pojo.EmployeesPojo;
import com.zws.system.api.domain.CaseManage;
import com.zws.system.api.domain.IntelligenceTask;

import java.util.List;
import java.util.Map;

/**
 * @author: 马博新
 * @date ：Created in 2024/08/30 17:54
 */
public interface IIntelligenceTaskService {

    /**
     * 客户列表查询参数处理
     *
     * @param intelligenceTask
     * @return
     */
    Map<String, Object> verifyParameters(IntelligenceTask intelligenceTask);

    /**
     * 写入预测式外呼任务
     *
     * @param record
     * @return
     */
    Long insert(IntelligenceTask record);

    /**
     * 根据任务名称机构内去重查询
     *
     * @param taskName
     * @param teamId
     * @return
     */
    Integer selectByCount(String taskName, Integer teamId);

    /**
     * 条件查询预测试外呼任务列表
     *
     * @param intelligenceTask
     * @return
     */
    List<IntelligenceTask> selectList(IntelligenceTask intelligenceTask);

    /**
     * id集合查找任务对应uuid
     *
     * @param map
     * @return
     */
    List<String> selectTaskUuidList(Map<String, Object> map);

    /**
     * 根据任务uuid查询已拨通的客户uuid
     *
     * @param map
     * @return
     */
    List<String> selectCustomUuidList(Map<String, Object> map);

    /**
     * 根据条件查询客户uuid
     *
     * @param map
     * @return
     */
    List<String> getNotConnectedCustomUuid(Map<String, Object> map);

    /**
     * 查询机构所有任务名称
     *
     * @param teamId
     * @return
     */
    List<String> selectTaskNameList(Integer teamId);

    /**
     * 根据主键id查询预测试外呼任务信息
     *
     * @param id
     * @return
     */
    IntelligenceTask selectById(Long id, Integer teamId);

    /**
     * 根据任务uuid查询主键id
     *
     * @param callCenterUuid
     * @return
     */
    IntelligenceTask selectByUuid(String callCenterUuid);

    /**
     * 统计话单各状态数量
     *
     * @param map
     * @return
     */
    Integer selectCallRecordCount(Map<String, Object> map);

    /**
     * 根据呼叫状态查询客户uuid
     *
     * @param map
     * @return
     */
    List<String> selectCallReloadList(Map<String, Object> map);

    /**
     * 查询任务对应的话单信息-（预测试外呼）
     *
     * @param map
     * @return
     */
    List<CallRecordDataPojo> selectCallRecordList(Map<String, Object> map);

    /**
     * 根据员工id集合查询员工信息
     *
     * @param map
     * @return
     */
    List<EmployeesPojo> selectEmployeesData(Map<String, Object> map);

    /**
     * 根据案件id集合查询催员id集合信息
     *
     * @param map
     * @return
     */
    List<Integer> selectEmployeeIdList(Map<String, Object> map);

    List<Integer> selectTsEmployeeIdList(Map<String, Object> map);

    List<Integer> selectStEmployeeIdList(Map<String, Object> map);

    /**
     * 查询有预测试外呼坐席的催员id
     *
     * @param map
     * @return
     */
    List<Integer> selectSipOdvIdList(Map<String, Object> map);

    /**
     * 根据案件id集合查询联系人信息
     *
     * @param map
     * @return
     */
    List<CallCustomDetails> selectContactsList(Map<String, Object> map);

    /**
     * 根据案件id查询案件详情
     *
     * @param caseId
     * @return
     */
    CaseManage selectCaseManageCaseId(Long caseId);

    /**
     * 根据id修改
     *
     * @param record
     * @return
     */
    int updateById(IntelligenceTask record);

    /**
     * 根据任务uuid修改
     *
     * @param record
     * @return
     */
    int updateByUuidList(IntelligenceTask record);

    /**
     * 根据案件id集合查询催员id集合信息(调诉端)
     * @param map
     * @return
     */
    List<Integer> selectEmployeeIdListAppeal(Map<String, Object> map);
}
