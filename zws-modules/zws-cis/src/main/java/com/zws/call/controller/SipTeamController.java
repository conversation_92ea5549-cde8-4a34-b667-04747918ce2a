package com.zws.call.controller;

import com.zws.call.config.CallConfig;
import com.zws.call.config.WorkPhoneConfig;
import com.zws.call.service.ICallTeamSipService;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据同步Controller
 *
 * <AUTHOR>
 * @date ：Created in 2024/10/9 11:59
 */
@CrossOrigin
@RestController
@RequestMapping("/teamCall/sip")
public class SipTeamController extends BaseController {

    @Autowired
    private ICallTeamSipService callSipService;
    @Autowired
    private CallConfig callConfig;
    @Autowired
    private WorkPhoneConfig workPhoneConfig;

    /**
     * SIP坐席数据同步 （JAVA呼叫中心）
     *
     * @return
     */
//    @Log(title = "SIP 坐席（数据同步）", businessType = BusinessType.OTHER)
    @PostMapping("/dataSync")
    public AjaxResult dataSyncJava() {
        if (StringUtils.isEmpty(callConfig.getHostUrl())) {
            throw new GlobalException("未配置呼叫中心地址，无法获取");
        }
//        登录人信息
        ThreadEntityPojo threadEntityPojo = new ThreadEntityPojo();
        threadEntityPojo.setType(SecurityUtils.getAccountType());
        threadEntityPojo.setUser(SecurityUtils.getUsername());
        threadEntityPojo.setUserId(SecurityUtils.getUserId().intValue());
        threadEntityPojo.setCreateId(SecurityUtils.getTeamId().intValue());
        callSipService.dataSyncJava(threadEntityPojo);
        return AjaxResult.success();
    }

    /**
     * 工作手机数据同步
     */
//    @Log(title = "SIP 坐席（数据同步）", businessType = BusinessType.OTHER)
    @PostMapping("/workPhoneDataSync")
    public AjaxResult workPhoneDataSync() {
        if (StringUtils.isEmpty(workPhoneConfig.getHostUrl())) {
            throw new GlobalException("未配置云客接口地址，无法获取");
        }
//        登录人信息
        ThreadEntityPojo threadEntityPojo = new ThreadEntityPojo();
        threadEntityPojo.setType(SecurityUtils.getAccountType());
        threadEntityPojo.setUser(SecurityUtils.getUsername());
        threadEntityPojo.setUserId(SecurityUtils.getUserId().intValue());
        threadEntityPojo.setCreateId(SecurityUtils.getTeamId().intValue());
        callSipService.dataSyncJava(threadEntityPojo);
        return AjaxResult.success();
    }
}
