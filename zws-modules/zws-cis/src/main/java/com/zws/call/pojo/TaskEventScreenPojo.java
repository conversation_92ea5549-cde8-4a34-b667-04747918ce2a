package com.zws.call.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 呼叫通知数据-实体类（预测试外呼）
 *
 * @author: 马博新
 * @date ：Created in 2024/09/10 18:36
 */
@Data
public class TaskEventScreenPojo implements Serializable {

    /**
     * 呼叫时间
     */
    private Date callTime;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 接听坐席
     */
    private String answerSip;

    /**
     * 案件id
     */
    private String caseId;

    /**
     * 客户id
     */
    private String customerId;
}
