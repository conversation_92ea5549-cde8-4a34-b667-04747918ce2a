package com.zws.call.enums;

/**
 * 执行状态 1未启动 2进行中 3已完成 4已暂停 5已撤销 6失败
 */
public enum AiCallStateEnum {

    /**
     * 1-未启动
     */
    NOT_STARTED(1, "未启动"),
    /**
     * 2-进行中
     */
    IN_PROGRESS(2, "进行中"),
    /**
     * 3-已完成
     */
    COMPLETED(3, "已完成"),
    /**
     * 4-已暂停
     */
    PAUSED(4, "已暂停"),
    /**
     * 5-已撤销
     */
    REVOKED(5, "已撤销"),
    /**
     * 6-失败
     */
    FAIL(6, "失败"),
    ;


    private final String info;
    private final Integer code;

    AiCallStateEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }


    public String getInfo() {
        return info;
    }

    public Integer getCode() {
        return code;
    }

    public static AiCallStateEnum valueOfCode(Integer code) {
        AiCallStateEnum[] enums = AiCallStateEnum.values();
        for (AiCallStateEnum temp : enums) {
            if (temp.code.equals(code)) {
                return temp;
            }
        }
        return null;
    }
}
