package com.zws.cis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zws.cis.agservice.TeamImportCaseAgService;
import com.zws.cis.controller.request.TeamImportParam;
import com.zws.cis.domain.TeamImportLog;
import com.zws.cis.domain.TeamManage;
import com.zws.cis.domain.TeamProduct;
import com.zws.cis.enums.ImportStartEnum;
import com.zws.cis.pojo.TeamActionRowResp;
import com.zws.cis.pojo.TeamImportCasePojo;
import com.zws.cis.pojo.TeamImportCaseSonTaskResp;
import com.zws.cis.pojo.TeamImportCaseSynContainer;
import com.zws.cis.service.*;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.sms.InfoContact;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.BigExcelUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.reflect.ReflexUtils;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.domain.*;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.sql.SQLSyntaxErrorException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * 导入案件--service层
 *
 * @author: huangqiuf
 * @date ：Created in 2024/10/28 14:08
 */
@Service
@Slf4j
public class TeamImportCaseServiceImpl implements TeamImportCaseService {

    @Autowired
    private TeamManageService assetManageService;
    @Autowired
    private TeamImportLogService importLogService;
    @Autowired
    private RemoteFileService remoteFileService;
    @Autowired
    private TeamProductService productService;
    @Autowired
    private TeamImportCaseAgService importCaseAgservice;
    @Resource
    private ILibraryMpService libraryService;
    @Resource
    private IInfoPlanMpService infoPlanService;
    @Resource
    private IInfoLoanMpService infoLoanService;
    @Resource
    private IInfoExtraMpService infoExtraService;
    @Resource
    private IInfoBaseMpService infoBaseService;
    @Resource
    private IInfoContactMpService infoContactService;
    @Autowired
    private ILibraryService originLibraryService;

    //例外字段，此数组中的字段必填
    private String[] EXCEPTION = new String[]{"securityName", "securityIdType", "securityIdNum", "securityPhone"};
    @Resource
    private ThreadPoolTaskExecutor saasThreadPool;

    private final int LEAST = 100;

    /**
     * 导入案件信息
     *
     * @param asset
     * @param logId
     * @param param
     * @param loginUser
     */
    @Override
    @Async("saasThreadPool")
    public void importCaseInfo(TeamManage asset, long logId, TeamImportParam param, LoginUser loginUser) {

//        TeamImportCaseSynContainer synContainer = new TeamImportCaseSynContainer();
//        final TimeInterval timer = new TimeInterval();//计时
//        TeamImportLog importLog = new TeamImportLog();
//        importLog.setId(logId);
//        ImportStartEnum istart = ImportStartEnum.ING;
//        try {
//
//            TeamProduct product = param.getProduct();
//            if (product == null) {
//                throw new ServiceException("未找到产品，请确定产品id");
//            }
//
//            timer.start("downloadTempFile");//下载文件
//            File tempFile = FileDownloadUtils.downloadTempFile(param.getFileUrl());//下载导入的文件
//            log.info("下载文件耗时:{} ms ", timer.intervalMs("downloadTempFile"));
//
//            timer.start("read");//读取文件
//            BigExcelUtils bigExcelUtils = new BigExcelUtils();
//            List<List<Object>> rows = bigExcelUtils.read(tempFile, 0);
//            log.info("读取文件耗时:{} ms ", timer.intervalMs("read"));
//            log.info("读取文件总记录数:{}", rows.size());
//            if (rows.size() < 1) {
//                throw new ServiceException("上传正确的模版");
//            }
//            //总记录行数（不包括标题行）
//            int rowCount = rows.size() - 1;
//
//            //第一行是标题
//            List<Object> titleRow = rows.get(0);
//            //数据列移除标题行
//            rows.remove(0);
//            int idleWorkTask = saasThreadPool.getCorePoolSize();
//            //每个任务线程的row量
//            int workSize = rowCount % idleWorkTask == 0 ? rowCount / idleWorkTask : (rowCount / idleWorkTask) + 1;
//            if(rowCount < LEAST){
//                idleWorkTask = 1;
//                workSize = rowCount;
//            }
//            log.info("导入案件,每条线程任务数:{}", workSize);
//            log.info("导入案件,线程数:{}", idleWorkTask);
//            //任务开始
//            timer.start("workTask");
//            //总案件数
//            int rowTotalSize = 0;
//            CountDownLatch latch = new CountDownLatch(idleWorkTask);
//            for (int i = 0; i < idleWorkTask; i++) {
//                int[] startEnd = PageUtil.transToStartEnd(i, workSize);
//                int start = startEnd[0];
//                int end = startEnd[1];
//                if (start > rowCount) {
//                    start = rowCount;
//                }
//                if (end > rowCount) {
//                    end = rowCount;
//                }
//                List<List<Object>> workTaskRows = CollUtil.sub(rows, start, end);
//                if (workTaskRows.size() == 0) {
//                    continue;
//                }
//                rowTotalSize += workTaskRows.size();
//
//
//                saasThreadPool.submit(() -> {
//                    try {
//                        doImportCaseInfo(asset, product, synContainer, titleRow, workTaskRows, loginUser);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    } finally {
//                        latch.countDown();
//                    }
//                });
//
//                if (start > rowCount || end > rowCount) {
//                    break;
//                }
//            }
//            latch.await();
//
//            //失败行
//            List<Map<String, Object>> fails = new ArrayList<>();
//            //成功数量
//            long successNum = 0;
//            BigDecimal entrustMoneyTotal = BigDecimal.ZERO;
//            List<TeamImportCaseSonTaskResp> resList = synContainer.getResList();
//            for (TeamImportCaseSonTaskResp res : resList) {
//                successNum += res.getSuccessNum();
//                entrustMoneyTotal = entrustMoneyTotal.add(res.getEntrustMoneyTotal());
//                fails.addAll(res.getFailsRows());
//            }
//            log.info("总数:{} ", rowTotalSize);
//            log.info("总成功数:{} ", successNum);
//            log.info("总失败数:{} ", fails.size());
//            log.info("执行任务耗时:{} ms ", timer.intervalMs("workTask"));
//            String failFileUrl = "";
//
//            //全部成功
//            if (rowTotalSize == successNum) {
//                istart = ImportStartEnum.SUCCESS;
//            } else {
//                //全部失败
//                if (fails.size() == rowTotalSize) {
//                    istart = ImportStartEnum.FAIL;
//                } else { //部分失败
//                    istart = ImportStartEnum.PARTIAL_SUCCESS;
//                }
//                failFileUrl = uploadFailFile(fails);
//            }
//            //案件数量
//            long caseNum = successNum;
//
//            TeamManage assetManage = assetManageService.selectById(asset.getId());
//            long oldCaseNum = assetManage.getCaseNumber() == null ? 0L : assetManage.getCaseNumber();
//            BigDecimal oldEntrustMoneyTotal = assetManage.getEntrustMoneyTotal() == null ? BigDecimal.ZERO : assetManage.getEntrustMoneyTotal();
//
//            caseNum = oldCaseNum + caseNum;
//            entrustMoneyTotal = oldEntrustMoneyTotal.add(entrustMoneyTotal);
//            TeamManage am = new TeamManage();
//            am.setId(asset.getId());
//            am.setImportStart(istart.getCode());
//            am.setCaseNumber(caseNum);
//            am.setEntrustMoneyTotal(entrustMoneyTotal);
//            assetManageService.updateById(am);
//
//            //更新导入日志
//            importLog.setSuccessNumber(successNum);
//            importLog.setFailFileUrl(failFileUrl);
//            FileDownloadUtils.deletedTempFile(tempFile);
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("导入案件失败:", e);
//            log.info("导入案件异常:{}", e.getMessage());
//            istart = ImportStartEnum.FAIL;
//        } finally {
//            importLog.setImportStart(istart.getCode());
//            importLogService.updateById(importLog, loginUser);
//        }
//    }
//
//    public void doImportCaseInfo(TeamManage asset, TeamProduct product, TeamImportCaseSynContainer synContainer, List<Object> titleRow, List<List<Object>> workTaskRows, LoginUser loginUser) {
//        if (workTaskRows == null) {
//            workTaskRows = new ArrayList<>();
//        }
//        //导入案件客户姓名、身份证、电话必须，其他非必填
//        //判断是否符合模板
//        TeamProduct teamProduct = productService.selectByPrimaryKey(asset.getProductId());
//        if (teamProduct == null) {
//            throw new GlobalException("未找到产品，请确定产品id");
//        }
//        String tmplateStr = teamProduct.getTemplate();
//        if (StringUtils.isEmpty(tmplateStr)) {
//            throw new GlobalException("产品未设置导入模板");
//        }
//        ProductTemplate productTemplate = JSONUtil.toBean(tmplateStr, ProductTemplate.class);
//
//        ProductTemplate initTemp = productService.getInitTemplate();
//        initTemp.setAdditionalInfo(productTemplate.getAdditionalInfo());
//        String shortName = teamProduct.getShortName();
//        if (!StringUtils.equals(shortName, "BL")) {
//            //如果导入的不是企业经营贷
//            //担保人、担保人证件类型、担保人证件号码、担保人联系电话，导入不做必填判断，可以为空；
//            EXCEPTION = new String[]{};
//        }
//
//        int successNum = 0;
//        BigDecimal entrustMoneyTotal = BigDecimal.ZERO;
//        List<Map<String, Object>> fails = new ArrayList<>();
//        ThreadLocal<List<TeamActionRowResp>> successResList = ThreadLocal.withInitial(ArrayList::new);
//
//        log.info("案件数量:{}", workTaskRows.size());
//        for (List<Object> row : workTaskRows) {
//            TeamActionRowResp res = actionRow(asset, product, row, productTemplate, loginUser, titleRow);
//            if (res.isSuccess()) {
//                successResList.get().add(res);
//                //成功
//                BigDecimal entrustMoney = res.getEntrustMoney();
//                if (StringUtils.isNotNull(entrustMoney)) {
//                    entrustMoneyTotal = entrustMoneyTotal.add(entrustMoney);
//                }
//                successNum++;
//            } else {//失败
//                try {
//                    //日期转年月日格式
//                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                    int clientBirthday = titleRow.indexOf("出生日期");
//                    int overdueStart = titleRow.indexOf("逾期日期");
//                    int amountFinalDate = titleRow.indexOf("最后还款日期");
//                    int yhDate = titleRow.indexOf("应还日期");
//                    int alreadyHkDate = titleRow.indexOf("已还款日期");
//
//                    if (clientBirthday >= 0) {
//                        row.set(clientBirthday, simpleDateFormat.format(row.get(clientBirthday)));
//                    }
//                    if (overdueStart >= 0) {
//                        row.set(overdueStart, simpleDateFormat.format(row.get(overdueStart)));
//                    }
//                    if (amountFinalDate >= 0) {
//                        row.set(amountFinalDate, simpleDateFormat.format(row.get(amountFinalDate)));
//                    }
//                    if (yhDate >= 0) {
//                        row.set(yhDate, simpleDateFormat.format(row.get(yhDate)));
//                    }
//                    if (alreadyHkDate >= 0) {
//                        row.set(alreadyHkDate, simpleDateFormat.format(row.get(alreadyHkDate)));
//                    }
//                } catch (Exception e) {
//                    log.error("导入失败时日期格式转换错误,", e);
//                }
//                Map<String, Object> fileRows = new LinkedHashMap<>();
//                for (int i = 0; i < titleRow.size(); i++) {
//                    fileRows.put(StrUtil.toString(titleRow.get(i)), row.get(i));
//                }
//                fileRows.put("失败原因", res.getErrorMsg());
//                fails.add(fileRows);
//            }
//        }
//
//        TeamImportCaseSonTaskResp taskRes = new TeamImportCaseSonTaskResp();
//        //将成功数据写入数据库
//        if (successResList.get().size() > 0) {
//            //插入案件信息
//            ThreadLocal<List<Library>> libraries = ThreadLocal.withInitial(ArrayList::new);
//            ThreadLocal<List<InfoBase>> baseInfos = ThreadLocal.withInitial(ArrayList::new);
//            ThreadLocal<List<InfoLoan>> infoLoans = ThreadLocal.withInitial(ArrayList::new);
//            ThreadLocal<List<InfoPlan>> infoPlans = ThreadLocal.withInitial(ArrayList::new);
//            ThreadLocal<List<InfoContact>> infoContacts = ThreadLocal.withInitial(ArrayList::new);
//            ThreadLocal<List<InfoExtra>> infoExtras = ThreadLocal.withInitial(ArrayList::new);
//            ThreadLocal<List<CaseManage>> manages = ThreadLocal.withInitial(ArrayList::new);
//            ThreadLocal<List<Long>> collect = ThreadLocal.withInitial(ArrayList::new);
//            try {
//
//                successResList.get().forEach(res -> {
//                    //聚合案件信息
//                    libraries.get().addAll(res.getLibraries());
//                    baseInfos.get().addAll(res.getBaseInfos());
//                    infoLoans.get().addAll(res.getInfoLoans());
//                    infoPlans.get().addAll(res.getInfoPlans());
//                    infoContacts.get().addAll(res.getInfoContacts());
//                    infoExtras.get().addAll(res.getInfoExtras());
//                    manages.get().addAll(res.getManages());
//                });
//                taskRes.getLibraries().addAll(libraries.get());
//                taskRes.getBaseInfos().addAll(baseInfos.get());
//                taskRes.getInfoLoans().addAll(infoLoans.get());
//                taskRes.getInfoPlans().addAll(infoPlans.get());
//                taskRes.getInfoContacts().addAll(infoContacts.get());
//                taskRes.getInfoExtras().addAll(infoExtras.get());
//                taskRes.getManages().addAll(manages.get());
//
//                //批处理案件信息
//                libraryService.saveBatch(libraries.get());
//                //获取id集合
//                collect.get().addAll(libraries.get().stream().map(Library::getId).collect(Collectors.toList()));
//                //替换临时id
//                //计算替换临时id用时
//                long startTime = System.currentTimeMillis();
//                libraries.get().forEach(library -> {
//                    // 替换baseInfos中的caseId
//                    baseInfos.get().forEach(baseInfo -> {
//                        if (baseInfo.getCaseId().equals(library.getTempId())) {
//                            baseInfo.setCaseId(library.getId());
//                        }
//                    });
//
//                    // 替换infoLoans中的caseId
//                    infoLoans.get().forEach(infoLoan -> {
//                        if (infoLoan.getCaseId().equals(library.getTempId())) {
//                            infoLoan.setCaseId(library.getId());
//                        }
//                    });
//
//                    // 替换infoPlans中的caseId
//                    infoPlans.get().forEach(infoPlan -> {
//                        if (infoPlan.getCaseId().equals(library.getTempId())) {
//                            infoPlan.setCaseId(library.getId());
//                        }
//                    });
//
//                    // 替换infoContacts中的caseId
//                    infoContacts.get().forEach(infoContact -> {
//                        if (infoContact.getCaseId().equals(library.getTempId())) {
//                            infoContact.setCaseId(library.getId());
//                        }
//                    });
//
//                    // 替换infoExtras中的caseId
//                    infoExtras.get().forEach(infoExtra -> {
//                        if (infoExtra.getCaseId().equals(library.getTempId())) {
//                            infoExtra.setCaseId(library.getId());
//                        }
//                    });
//
//                    // 替换manages中的caseId
//                    manages.get().forEach(manage -> {
//                        if (manage.getCaseId().equals(library.getTempId())) {
//                            manage.setCaseId(library.getId());
//                        }
//                    });
//                });
//                long endTime = System.currentTimeMillis();
//                log.info(Thread.currentThread().getName() + "替换临时id耗时：" + (endTime - startTime) / 1000 + "秒");
//                infoBaseService.saveBatch(baseInfos.get());
//                infoLoanService.saveBatch(infoLoans.get());
//                infoPlanService.saveBatch(infoPlans.get());
//                infoContactService.saveBatch(infoContacts.get());
//                infoExtraService.saveBatch(infoExtras.get());
//                manageService.saveBatch(manages.get());
//                originLibraryService.updateAllocateCaseStateByList(collect.get());
//
//            } catch (Exception e) {
//                e.printStackTrace();
//            } finally {
//                //移除所有ThreadLocal
//                libraries.remove();
//                baseInfos.remove();
//                infoLoans.remove();
//                infoPlans.remove();
//                infoContacts.remove();
//                infoExtras.remove();
//                manages.remove();
//                collect.remove();
//            }
//        }
//        taskRes.setFailsRows(fails);
//        taskRes.setSuccessNum(successNum);
//        taskRes.setEntrustMoneyTotal(entrustMoneyTotal);
//        synContainer.submit(taskRes);
//    }
//
//    /**
//     * 处理行
//     *
//     * @param row
//     * @param productTemplate
//     * @return
//     */
//    private TeamActionRowResp actionRow(TeamManage asset, TeamProduct product, List<Object> row, ProductTemplate productTemplate, LoginUser loginUser, List<Object> titleRow) {
//        //是否成功
//        boolean start = false;
//        //失败原因
//        String errorMsg = "";
//        //委托金额
//        BigDecimal entrustMoney = null;
//        TeamActionRowResp res = new TeamActionRowResp();
//        try {
//            TeamImportCasePojo icpojo = new TeamImportCasePojo();
//            icpojo.getLibrary().setBatchNo(asset.getBatchNum());
//            icpojo.getLibrary().setProductId(asset.getProductId());
//            icpojo.getLibrary().setProductName(product.getName());
//            icpojo.getLibrary().setEntrustingPartyId(asset.getOwnerId());
//            icpojo.getLibrary().setEntrustingPartyName(product.getOwnerName());
//            icpojo.getLibrary().setAssetManageId(asset.getId());
//            icpojo.getLoanInfo().setProductId(asset.getProductId());
//            icpojo.getLibrary().setUpdateBy(asset.getUpdateBy());
//            icpojo.getLoanInfo().setProductId(product.getId());
//            icpojo.getLoanInfo().setProductName(product.getName());
//            productTemplate.getTableField("");//用于生成
//            //标题
//            List<Object> keys = titleRow;
//            //判断
//            boolean isProperSub = CollectionUtils.isSubCollection(productTemplate.getRequireds(), keys);
//            if (!isProperSub) {
//                StringBuffer sbuff = new StringBuffer();
//                for (String temp : productTemplate.getRequireds()) {
//                    if (!keys.contains(temp)) {
//                        sbuff.append(temp + "  ");
//                    }
//                }
//                throw new ServiceException("缺少必填字段,请重新下载模板." + sbuff.toString());
//            }
//
//            for (int i = 0; i < keys.size(); i++) {
//                String tempKey = StrUtil.toString(keys.get(i));
//                Object val = row.get(i);
//
//                try {
//                    if (val == null) {
//                        //当execl 表格的表被清除时，会获取到此数值为null,
//                        val = "";
//                    }
//                    TemplateTable tt = productTemplate.getTableField(tempKey);
//                    if (tt != null) {
//                        if (EXCEPTION.length > 0 && Arrays.asList(EXCEPTION).contains(tt.getField())) {
//                            tt.setDef(true);
//                        }
//                        if (tt.isDef()) {
//                            if (StrUtil.isBlankIfStr(val)) {
//                                throw new ServiceException("默认必填，不能为空");
//                            }
//                            if (StringUtils.equals("-", val.toString())) {
//                                val = null;
//                            }
//                        }
//
//                        if (tt.getLimit() != null) {
//                            Object finalVal = val;
//                            boolean anyMatch = Arrays.stream(tt.getLimit()).anyMatch(v -> StrUtil.equals(v, StrUtil.toString(finalVal)));
//                            if (!anyMatch) {
//                                throw new ServiceException("只能填写指定字段");
//                            }
//                        }
//
//                        switch (tt.getTableName()) {
//                            case "baseInfo":
//                                ReflexUtils.setFieldVal(icpojo.getBaseInfo(), tt.getField(), val);
//                                break;
//                            case "caseInfo":
//                                ReflexUtils.setFieldVal(icpojo.getLoanInfo(), tt.getField(), val);
//                                break;
//                            case "repaymentInfo":
//                                ReflexUtils.setFieldVal(icpojo.getPlanInfo(), tt.getField(), val);
//                                break;
//                            case "liaisonInfo":
//                                String str = val.toString();
//                                if (!StringUtils.isEmpty(str)) {
//                                    String[] contact = str.split("\\|");
//                                    if (contact.length < 1) {
//                                        break;
//                                    }
//                                    String tep = contact[0];
//                                    if (ObjectUtils.isEmpty(tep)) {
//                                        break;
//                                    }
//                                    String gx = null;
//                                    if (contact.length >= 2) {
//                                        gx = contact[1];
//                                    }
//                                    String name = null;
//                                    if (contact.length >= 3) {
//                                        name = contact[2];
//                                    }
//                                    if (ObjectUtils.isEmpty(gx) || gx.equals("")) {
//                                        gx = "其他";
//                                    }
//                                    if (ObjectUtils.isEmpty(name) || name.equals("")) {
//                                        name = "未知";
//                                    }
//                                    InfoContact infoContact = new InfoContact();
//                                    infoContact.setContactPhone(tep);
//                                    infoContact.setContactName(name);
//                                    infoContact.setContactRelation(gx);
//                                    icpojo.getContactInfos().add(infoContact);
//                                }
//                                break;
//                        }
//                    } else {
//                        ProductTemplateGroup additionalInfo = productTemplate.getAdditionalInfo();
//                        if (!ObjectUtils.isEmpty(additionalInfo)) {
//                            List<ProductTemplateItem> columns = additionalInfo.getColumns();
//                            if (!ObjectUtils.isEmpty(columns)) {
//                                for (ProductTemplateItem item : columns) {
//                                    if (tempKey.equals(item.getLabel())) {
//                                        InfoExtra infoExtra = new InfoExtra();
//                                        infoExtra.setExtraName(tempKey);
//                                        infoExtra.setExtraValue(ObjectUtil.toString(val));
//                                        icpojo.getExtraInfo().add(infoExtra);
//                                    }
//                                }
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    throw new GlobalException(tempKey + "错误:" + e.getMessage());
//                }
//            }
//            icpojo.getBaseInfo().setBankCardNumber(icpojo.getLoanInfo().getBankCardNumber());
//            icpojo.getBaseInfo().setBankName(icpojo.getLoanInfo().getBankName());
//            icpojo.getLoanInfo().setYcFiveLevel(icpojo.getBaseInfo().getYcFiveLevel());
//            entrustMoney = icpojo.getLoanInfo().getEntrustMoney();
//            long caseId = importCaseAgservice.importCase(icpojo, loginUser, res);
//            //转入case_manage表
//
//            if (caseId > 0) {
//                start = true;
//            } else {
//                start = false;
//                errorMsg = "执行数据库操作异常，请联系管理员处理";
//            }
//        } catch (SQLSyntaxErrorException sqlExc) {
//            sqlExc.printStackTrace();
//            start = false;
//            errorMsg = "执行数据库操作异常，请联系管理员处理";
//        } catch (Exception e) {
//            e.printStackTrace();
//            start = false;
//            errorMsg = e.getMessage();
//        }
//       /* retMap.put("start", start);
//        retMap.put("errorMsg", errorMsg);
//        retMap.put("entrustMoney", entrustMoney);*/
//        res.setSuccess(start);
//        res.setErrorMsg(errorMsg);
//        res.setEntrustMoney(entrustMoney);
//        return res;
//    }
//
//    /**
//     * 上传保存失败的案件
//     *
//     * @param fails
//     * @return
//     * @throws Exception
//     */
//    private String uploadFailFile(List<Map<String, Object>> fails) {
//        try {
//            MultipartFile multipartFile = FileDownloadUtils.generateMultipartFile(fails);
//            R<SysFile> rest = remoteFileService.upload(multipartFile);
//            if (rest.getCode() == R.SUCCESS) {
//                return rest.getData().getUrl();
//            }
//        } catch (Exception e) {
//            log.error(JSONUtil.toJsonStr(fails));
//            log.error("保存失败文件错误", e);
//        }
//        return "";
    }

}
