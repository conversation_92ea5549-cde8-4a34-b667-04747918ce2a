package com.zws.cis.enums;

/**
 * 导入状态
 * <AUTHOR>
 * @date ：Created in 2022/2/24 10:41
 */
public enum ImportStartEnum {

    /**
     * 0-导入中
     */
    ING("0","导入中"),
    /**
     * 1-导入成功
     */
    SUCCESS("1","导入成功"),
    /**
     * 2-导入失败
     */
    FAIL("2","导入失败"),
    /**
     * 3-部分成功
     */
    PARTIAL_SUCCESS("3","部分成功"),
    ;



    private final String info;
    private String code;
    ImportStartEnum(String code, String info){
        this.code=code;
        this.info = info;
    }



    public String getInfo()
    {
        return info;
    }

    public String getCode() {
        return code;
    }

    public static ImportStartEnum valueOfCode(String code){
        ImportStartEnum[] enums=  ImportStartEnum.values();
        for (ImportStartEnum temp:enums) {
            if(temp.code.equals(code)) {
                return temp;
            }
        }
        return null;
    }
}
