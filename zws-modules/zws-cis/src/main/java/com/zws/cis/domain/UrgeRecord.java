package com.zws.cis.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 催收记录
 * <AUTHOR>
 * @date 2024年8月8日19:28:52
 */
@Data
public class UrgeRecord implements Serializable {

    /**
     * 催记ID
     */
    private Long id;

    /**
     * 案件ID
     */
    private Long caseId;

    /**
     * 团队id
     */
    private Integer createId;

    /**
     * 联系人id
     */
    private Long contactId;

    /**
     * 联系人
     */
    private String liaison;

    /**
     * 联系人解密信息
     */
    private String liaisonDecrypt;

    /**
     * 关系
     */
    private String relation;
    /**
     * 联系方式
     */
    private String contactMode;


    /**
     * 跟进状态
     */
    private String followUpState;

    /**
     * 催收状态
     */
    private String urgeState;

    /**
     * 沟通内容
     */
    private String content;
    /**
     * 备注
     */
    private String remarks;

    /**
     * 催收员id
     */
    private Long odvId;
    /**
     * 催收员名
     */
    private String odvName;
    /**
     * 联系渠道
     */
    private String contactMedium;
    /**
     * 承诺还款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date promiseRepaymentTime;

    /**
     * 承诺还款金额
     */
    private BigDecimal promiseRepaymentMoney;

    /**
     * 另约时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date anotherTime;

    /**
     * 承诺分期-分期数
     */
    private Integer promiseByStages;

    /**
     * 承诺分期-每期还款金额
     */
    private BigDecimal promiseEveryMoney;

    /**
     * 承诺分期-还款日
     */
    private Integer promiseRepaymentDay;

    /**
     * 删除标记
     */
    private String delFlag;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 委案批次号
     */
    private String entrustingCaseBatchNum;

    /**
     * 委案日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entrustingCaseDate;

    /**
     * 退案日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date returnCaseDate;

    /**
     * 申请人类型（0-主账号;1-员工账号;2-资产端账号）
     */
    private Integer operationType;

    /**
     * 类型，电催0、催记1
     */
    private String urgeTpye;

    /**
     * web端类型 1催收 2调解 3调诉
     */
    private Integer webSide;
    /**
     * 标签
     */
    private String label;
    /**
     * 是否是导入催记（0:导入;1:写入,null:写入）
     */
    private Integer importReminder;
}
