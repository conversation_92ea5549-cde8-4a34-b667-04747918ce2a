package com.zws.cis.mapper;

import com.zws.cis.domain.TeamProduct;
import com.zws.cis.pojo.TeamLibraryPojo;
import com.zws.cis.pojo.TeamManagePojo;

import java.util.List;
import java.util.Map;

public interface TeamProductMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TeamProduct record);

    int insertSelective(TeamProduct record);

    TeamProduct selectByPrimaryKey(Long id);

    /**
     * 查询名称是否重复
     *
     * @param record
     * @return 返回大于0表示重复
     */
    int selectCheckByName(TeamProduct record);

    int updateByPrimaryKeySelective(TeamProduct record);

    /**
     * 根据产品id修改案件是否展示
     *
     * @param manage
     * @return
     */
    int updateByPrimaryManage(TeamManagePojo manage);

    /**
     * 根据产品id修改案件库是否展示
     *
     * @param library
     * @return
     */
    int updateByPrimaryLibrary(TeamLibraryPojo library);

    int updateByPrimaryKeyWithBLOBs(TeamProduct record);

    int updateByPrimaryKey(TeamProduct record);

    /**
     * 查询列表
     *
     * @param record
     * @return
     */
    List<TeamProduct> selectList(TeamProduct record);

    /**
     * 查询 产品 id 的案件数量
     *
     * @param productId
     * @return
     */
    long selectCountByProductId(Long productId);

    /**
     * 根据产品ID 获取 统计案件数量、委托金额总量、批号数量
     *
     * @param map
     * @return
     */
    Map<String, Object> selectByProductId(Map<String, Object> map);

    /**
     * 检查是否重复
     *
     * @param record
     * @return
     */
    int checkRepeat(TeamProduct record);
}