package com.zws.cis.service;

import com.zws.cis.domain.TeamManage;
import com.zws.common.core.web.domain.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 资产管理信息--service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:06
 */
public interface TeamManageService {

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    List<TeamManage> selectList(Map<String, Object> param);

    /**
     * id查询
     *
     * @param id
     * @return
     */
    TeamManage selectById(Long id);

    /**
     * 编辑
     *
     * @param assetManage
     * @return
     */
    int edit(TeamManage assetManage);

    /**
     * ID删除
     *
     * @param id
     */
    AjaxResult deletedById(Long id);

    /**
     * 查询是否可删除
     *
     * @param id
     * @return true-可删除，false-不可删除
     */
    boolean checkCanDeleted(Long id);

    /**
     * 更新
     *
     * @param entity
     */
    void updateById(TeamManage entity);

    /**
     * 插入数据
     *
     * @param entity
     * @return
     */
    long insert(TeamManage entity);

    /**
     * 查询资产包名称列表
     * @param teamId
     * @return
     */
    List<String> selectPackageNameById(Long teamId);

    /**
     * 廖智套 2022-9-4 16:53:56
     * 查询统计-caseNum:案件总量，totalRemainingClaims:剩余债权总额,residualDebtPrincipal:剩余债权本金
     *
     * @param param
     * @return
     */
    Map<String, Object> selectCount(Map<String, Object> param);

    /**
     * 生成导入批号中唯一序号，
     * 批次号规则：机构ID+转让方代号+年月日+四位顺序数
     * @param productId 产品id
     * @return
     */
    String getImportUniqueNumber(Long productId);
}
