package com.zws.cis.agservice;

import com.alibaba.fastjson.JSONObject;
import com.zws.cis.pojo.*;
import com.zws.cis.service.ICisSettingService;
import com.zws.cis.utils.DesensitizationRedisUtils;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.redis.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 机构系统管理设置--业务处理层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/08 19:39
 */
@Component
public class AgCisSettingService {

    @Autowired
    private ICisSettingService cisSettingService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private DesensitizationRedisUtils redisUtils;

    /**
     * 根据团队id查询对应的团队
     *
     * @param createId
     * @return
     */
    public TeamLabelPojo selectCreateId(int createId) {
//        根据团队id获取状态控制表信息
        StatePojo state = cisSettingService.findState(createId);
//        根据团队id获取数据脱敏详情状态表信息
        DesensitizationPojo desensitization = cisSettingService.selectDesensitization(createId);
//        根据团队id获取水印设置字段
        WatermarkPojo watermark = cisSettingService.selectWatermark(createId);

        TeamLabelPojo createLabel = new TeamLabelPojo();
        createLabel.setState(state);
        createLabel.setDesensitization(desensitization);
        createLabel.setWatermark(watermark);
        return createLabel;
    }

//    /**
//     * 修改设置状态并更新redis中的数据
//     *
//     * @param state
//     */
//    public void modifyState(StatePojo state) {
//        cisSettingService.updateStates(state);
//        StateDesensitizationPojo desensitizationCreate = redisUtils.getDesensitizationCreate(BaseConstant.DESENSITIZATION + state.getCreateId(), state.getCreateId());
//        StateDesensitizationPojo stateDesensitization = new StateDesensitizationPojo();
//        stateDesensitization.setDesensitization(desensitizationCreate.getDesensitization());
//        stateDesensitization.setState(state);
//        redisService.setCacheObject(BaseConstant.DESENSITIZATION + state.getCreateId(), JSONObject.toJSONString(stateDesensitization), CacheConstants.EXPIRATION, TimeUnit.MINUTES);
//        redisService.setCacheObject(UserConstants.SAAS_SECURITY_VERIFICATION_SWITCH + state.getCreateId(), state.getSecurityVerificationStatus());
//    }
//
//    /**
//     * 修改脱敏状态并更新redis中的数据
//     *
//     * @param desensitization
//     */
//    public void modifyDesensitization(DesensitizationPojo desensitization) {
//        cisSettingService.updateDesensitization(desensitization);
//        StateDesensitizationPojo desensitizationCreate = redisUtils.getDesensitizationCreate(BaseConstant.DESENSITIZATION + desensitization.getCreateId(), desensitization.getCreateId());
//        StateDesensitizationPojo stateDesensitization = new StateDesensitizationPojo();
//        stateDesensitization.setDesensitization(desensitization);
//        stateDesensitization.setState(desensitizationCreate.getState());
//        redisService.setCacheObject(BaseConstant.DESENSITIZATION + desensitization.getCreateId(), JSONObject.toJSONString(stateDesensitization), CacheConstants.EXPIRATION, TimeUnit.MINUTES);
//    }
}
