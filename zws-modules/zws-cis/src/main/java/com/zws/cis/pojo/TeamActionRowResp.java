package com.zws.cis.pojo;

import com.zws.common.core.domain.sms.InfoContact;
import com.zws.system.api.domain.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 执行 行 返回
 * <AUTHOR>
 * @date ：Created in 2022/7/6 11:05
 */
@Data
public class TeamActionRowResp {

    /**
     * 执行结果，true-成功，false-失败
     */
    private boolean success;
    /**
     * 错误信息
     */
    private String errorMsg;
    /**
     * 委托金额
     */
    private BigDecimal entrustMoney;

    /**
     * 收集案件信息
     */
    private List<Library> libraries;
    private List<InfoBase> baseInfos;
    private List<InfoLoan> infoLoans;
    private List<InfoPlan> infoPlans;
    private List<InfoContact> infoContacts;
    private List<InfoExtra> infoExtras;
    private List<CaseManage> manages;


    public TeamActionRowResp() {
        this.init();
    }

    private void init() {
        libraries = new ArrayList<>();
        baseInfos = new ArrayList<>();
        infoLoans = new ArrayList<>();
        infoPlans = new ArrayList<>();
        infoContacts = new ArrayList<>();
        infoExtras = new ArrayList<>();
        manages = new ArrayList<>();
    }
}
