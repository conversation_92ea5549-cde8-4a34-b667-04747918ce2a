package com.zws.cis.controller;

import cn.hutool.core.bean.BeanUtil;
import com.zws.cis.controller.request.TeamImportLogParam;
import com.zws.cis.domain.TeamImportLog;
import com.zws.cis.enums.ImportStartEnum;
import com.zws.cis.enums.TeamImportTypeEnum;
import com.zws.cis.pojo.TeamStatePojo;
import com.zws.cis.service.TeamImportLogService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.SplitUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 导入日志
 *
 * <AUTHOR>
 * @date ：Created in 2022/3/18 15:00
 */
@CrossOrigin
@RestController
@EnableTransactionManagement
@RequestMapping("/teamImportLog")
public class TeamImportLogController extends BaseController {

    @Autowired
    private TeamImportLogService importLogService;

    /**
     * 查询导入案件列表
     *
     * @param param 查询参数
     * @return
     */
    @GetMapping("/caseList")
    public TableDataInfo selectListWithCase(TeamImportLogParam param) {
        startPage();
        String packageNameList = param.getPackageNameList();
        if (StringUtils.isNotEmpty(packageNameList)) {
            List<String> packageNameArr = SplitUtils.strSplit(packageNameList, BaseConstant.comma);
            param.setPackageNameArr(packageNameArr);
        }
        Date begin = DateUtils.beginOfDay(param.getImportTime1());
        Date end = DateUtils.endOfDay(param.getImportTime2());
        param.setImportTime1(begin);
        param.setImportTime2(end);
        Map<String, Object> paramMap = BeanUtil.beanToMap(param);
        String[] importTypes = new String[]{TeamImportTypeEnum.IMPORT_CASE.getCode(),
                TeamImportTypeEnum.UPDATE_CASE.getCode(), TeamImportTypeEnum.RE_IMPORT_CASE.getCode()};
        paramMap.put("importTypes", Arrays.asList(importTypes));
        paramMap.put("teamId", SecurityUtils.getTeamId());
        List<TeamImportLog> list = importLogService.selectList(paramMap);
        return getDataTable(list);
    }

    /**
     * 查询导入联系人列表
     *
     * @param param
     * @return
     */
    @GetMapping("/contactList")
    public TableDataInfo selectListWithContact(TeamImportLogParam param) {
        startPage();
        Date begin = DateUtils.beginOfDay(param.getImportTime1());
        Date end = DateUtils.endOfDay(param.getImportTime2());
        param.setImportTime1(begin);
        param.setImportTime2(end);
        Map<String, Object> paramMap = BeanUtil.beanToMap(param);
        paramMap.put("importType", TeamImportTypeEnum.CONTACT.getCode());
        paramMap.put("teamId", SecurityUtils.getTeamId());
        List<TeamImportLog> list = importLogService.selectList(paramMap);
        return getDataTable(list);
    }

    /**
     * 查询导入催记列表
     *
     * @param param
     * @return
     */
    @GetMapping("/urgeList")
    public TableDataInfo selectListWithUrge(TeamImportLogParam param) {
        startPage();
        Date begin = DateUtils.beginOfDay(param.getImportTime1());
        Date end = DateUtils.endOfDay(param.getImportTime2());
        param.setImportTime1(begin);
        param.setImportTime2(end);
        Map<String, Object> paramMap = BeanUtil.beanToMap(param);
        paramMap.put("importType", TeamImportTypeEnum.URGE.getCode());
        paramMap.put("teamId", SecurityUtils.getTeamId());
        List<TeamImportLog> list = importLogService.selectList(paramMap);
        return getDataTable(list);
    }

    /**
     * 查询导入还款记录列表
     *
     * @param param
     * @return
     */
    @GetMapping("/planList")
    public TableDataInfo selectListWithPlan(TeamImportLogParam param) {
        startPage();
        Date begin = DateUtils.beginOfDay(param.getImportTime1());
        Date end = DateUtils.endOfDay(param.getImportTime2());
        param.setImportTime1(begin);
        param.setImportTime2(end);
        Map<String, Object> paramMap = BeanUtil.beanToMap(param);
        paramMap.put("importType", TeamImportTypeEnum.PLAN.getCode());
        paramMap.put("teamId", SecurityUtils.getTeamId());
        List<TeamImportLog> list = importLogService.selectList(paramMap);
        return getDataTable(list);
    }

    /**
     * 查询导入批量操作列表
     *
     * @param param
     * @return
     */
    @GetMapping("/batchList")
    public TableDataInfo selectListWithBatch(TeamImportLogParam param) {
        startPage();
        Date begin = DateUtils.beginOfDay(param.getImportTime1());
        Date end = DateUtils.endOfDay(param.getImportTime2());
        param.setImportTime1(begin);
        param.setImportTime2(end);
        Map<String, Object> paramMap = BeanUtil.beanToMap(param);
        String[] importTypes = new String[]{TeamImportTypeEnum.BATCH_DELETED.getCode(),
                TeamImportTypeEnum.BATCH_STOP_URGING.getCode(), TeamImportTypeEnum.BATCH_STAY_CASE.getCode(),
                TeamImportTypeEnum.BATCH_SEND_BACK.getCode()};

        paramMap.put("importTypes", Arrays.asList(importTypes));
        paramMap.put("teamId", SecurityUtils.getTeamId());
        List<TeamImportLog> list = importLogService.selectList(paramMap);
        return getDataTable(list);
    }


    /**
     * 获取导入状态下拉选项
     *
     * @return
     */
    @GetMapping("/getImportStart")
    public AjaxResult getImportStart() {
        ImportStartEnum[] startS = ImportStartEnum.values();
        List<TeamStatePojo> list = new ArrayList<>();
        for (ImportStartEnum temp : startS) {
            list.add(new TeamStatePojo(temp.getCode(), temp.getInfo()));
        }
        return AjaxResult.success(list);
    }

}
