package com.zws.cis.service.impl;

import cn.hutool.json.JSONUtil;
import com.zws.cis.domain.TeamOwner;
import com.zws.cis.domain.TeamProduct;
import com.zws.cis.mapper.TeamOwnerMapper;
import com.zws.cis.mapper.TeamProductMapper;
import com.zws.cis.pojo.TeamLibraryPojo;
import com.zws.cis.pojo.TeamManagePojo;
import com.zws.cis.service.TeamProductService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.FileConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.poi.ExportUtils;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品信息--service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:05
 */
@Service
public class TeamProductServiceImpl implements TeamProductService {

    @Resource
    private TeamProductMapper mapper;
    @Resource
    private TeamOwnerMapper ownerMapper;

    /**
     * 获取资产方产品
     *
     * @param ownerId
     * @return
     */
    @Override
    public List<TeamProduct> selectListByOwnerId(Long ownerId, Integer teamId) {
        TeamProduct entity = new TeamProduct();
        entity.setOwnerId(ownerId);
        entity.setTeamId(teamId.intValue());
        List<TeamProduct> list = mapper.selectList(entity);
        return list;
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void deleted(Long id) {
        if (!checkCanDeleted(id)) {
            throw new GlobalException("该产品还有批次存在，无法进行批量删除，请先将批次全部删除再进行删除操作！");
        }
        TeamProduct product = new TeamProduct();
        product.setId(id);
        product.setUpdateBy(SecurityUtils.getUsername());
        product.setUpdateTime(DateUtils.getNowDate());
        product.setDelFlag(BaseConstant.DelFlag_Delete);
        mapper.updateByPrimaryKeySelective(product);
    }

    /**
     * 判断是否可以删除产品
     *
     * @param id
     * @return true 可以删除，false 不可删除
     */
    @Override
    public boolean checkCanDeleted(Long id) {
        return mapper.selectCountByProductId(id) == 0;
    }

//    @Override
//    public ProductTemplate getInitTemplate() {
//        ProductTemplate initTemplate = DownloadConstant.getInitTemplate();
//        initTemplate.setAdditionalInfo(new ProductTemplateGroup("additionalInfo", "附加信息", new ArrayList<>()));
//        return initTemplate;
//    }

    /**
     * 查询获取列表
     *
     * @param entity
     * @return
     */
    @Override
    public List<TeamProduct> selectList(TeamProduct entity) {
        List<TeamProduct> list = mapper.selectList(entity);
        for (TeamProduct product : list) {
            //查询产品的累计案件数、累计委托金额、批次号
            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put("teamId", SecurityUtils.getTeamId());
            hashMap.put("productId", product.getId());
            Map<String, Object> map = mapper.selectByProductId(hashMap);
            if (map == null) {
                map = new HashMap<>();
                map.put("caseNum", BigDecimal.ZERO);
                map.put("caseMoney", BigDecimal.ZERO);
                map.put("batchNoNum", 0L);
            }
            BigDecimal caseNum = (BigDecimal) map.get("caseNum");//案件数量
            BigDecimal caseMoney = (BigDecimal) map.get("caseMoney");//委托金额
            Long batchNoNum = (Long) map.get("batchNoNum");//批次数量

            product.setBatchNumber(batchNoNum.intValue());
            product.setCaseTotalMoney(caseMoney == null ? BigDecimal.ZERO : caseMoney);
            product.setCaseTotalNum(caseNum == null ? 0 : caseNum.intValue());

        }
        return list;
    }

    /**
     * 更新数据
     *
     * @param entity
     */
    @Override
    public void update(TeamProduct entity) {
        check(entity);
        TeamProduct product = new TeamProduct();
        product.setId(entity.getId());
        product.setUpdateBy(SecurityUtils.getUsername());
        product.setUpdateTime(DateUtils.getNowDate());
        product.setState(entity.getState());
        product.setName(entity.getName());
        product.setTemplate(entity.getTemplate());
        mapper.updateByPrimaryKeySelective(product);
    }

//    /**
//     * 插入新增
//     *
//     * @param entity
//     * @return
//     */
//    @Override
//    public long insert(TeamProduct entity) {
//        check(entity);
//        //查询资产方ID
//        TeamOwner owner = ownerMapper.selectByPrimaryKey(entity.getOwnerId());
//        if (owner == null || owner.getDelFlag().equals(BaseConstant.DelFlag_Delete)) {
//            throw new GlobalException("请选择转让方");
//        }
//
//        //判断统一转让方下是否有重复产品
//        TeamProduct record = new TeamProduct();
//        record.setShortName(entity.getShortName());
//        //record.setOwnerId(entity.getOwnerId());
//        //record.setName(entity.getName());
//        record.setTeamId(entity.getTeamId());
//        if (mapper.checkRepeat(record) > 0) {
//            throw new ServiceException("产品代号:【" + entity.getShortName() + "】重复");
//        }
//        record.setShortName(null);
//        record.setName(entity.getName());
//        if (mapper.checkRepeat(record) > 0) {
//            throw new ServiceException("产品类型:【" + entity.getName() + "】重复");
//        }
//
//
//        entity.setOwnerName(owner.getName());
//        entity.setDelFlag(BaseConstant.DelFlag_Being);
//        entity.setState(BaseConstant.State_Use);
//        entity.setCreateBy(SecurityUtils.getUsername());
//        entity.setCreateTime(DateUtils.getNowDate());
//        entity.setUpdateTime(DateUtils.getNowDate());
//        entity.setUpdateBy(SecurityUtils.getUsername());
//        mapper.insert(entity);
//        return entity.getId();
//    }

    /**
     * 根据产品id修改案件是否展示
     *
     * @param manage
     * @return
     */
    @Override
    public int updateByPrimaryManage(TeamManagePojo manage) {
        return mapper.updateByPrimaryManage(manage);
    }

    /**
     * 根据产品id修改案件库是否展示
     *
     * @param library
     * @return
     */
    @Override
    public int updateByPrimaryLibrary(TeamLibraryPojo library) {
        return mapper.updateByPrimaryLibrary(library);
    }

//    /**
//     * 下载模板
//     *
//     * @param response
//     * @param id
//     */
//    @Override
//    public void downloadTemplate(HttpServletResponse response, Long id) throws IOException {
//        ProductTemplate productTemplate = productTemplateById(id);
//        TeamProduct product = selectByPrimaryKey(id);
//        List<String> tableHeader = productTemplate.getTableHeader();
//
//        String titlie = "";
//        ExportUtils.guideCaseExcelHeader(response, tableHeader, product.getName() + "模板" + FileConstant.getExcelSuffix(), titlie);
//    }

//    /**
//     * 主键ID获取产品模板
//     *
//     * @param id
//     * @return
//     */
//    @Override
//    public ProductTemplate productTemplateById(Long id) {
//        if (id == null) {
//            throw new GlobalException("主键为null");
//        }
//        TeamProduct product = selectByPrimaryKey(id);
//        if (product == null) {
//            throw new GlobalException("查找不到产品信息");
//        }
//        String templateStr = product.getTemplate();
//        if (StringUtils.isEmpty(templateStr)) {
//            throw new GlobalException("未设置模板");
//        }
//        ProductTemplate temp = JSONUtil.toBean(templateStr, ProductTemplate.class);
//        ProductTemplate productTemplate = ProductTemplateUtil.examine(temp);
//        return productTemplate;
//    }

    /**
     * id 查找
     *
     * @param id
     * @return
     */
    @Override
    public TeamProduct selectByPrimaryKey(Long id) {
        if (id == null) {
            return null;
        }
        return mapper.selectByPrimaryKey(id);
    }

    private void check(TeamProduct entity) {
        //不检查名称重复
        /*if(StringUtils.isNotEmpty(entity.getName())) {
            entity.setName(entity.getName().trim());
            if(productMapper.selectCheckByName(entity)>0) throw  new GlobalException("产品已存在，请重新输入");
        }*/
    }

    @Override
    public boolean checkName(String name) {
        if (StringUtils.isEmpty(name)) {
            throw new GlobalException("产品类型不能为空");
        }
        TeamProduct entity = new TeamProduct();
        entity.setName(name);
        return mapper.selectCheckByName(entity) > 0 ? false : true;
    }
}
