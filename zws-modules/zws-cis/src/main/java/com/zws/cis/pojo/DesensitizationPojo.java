package com.zws.cis.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 信息脱敏表（实体类）
 */
@Data
public class DesensitizationPojo implements Serializable {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 团队主键id
     */
    private Integer createId;

    /**
     * 姓名脱敏开关状态(0:关闭,1:开启)
     */
    private Integer dname;

    /**
     * 手机号脱敏开关状态(0:关闭,1:开启)
     */
    private Integer numbers;

    /**
     * 身份证脱敏开关状态(0:关闭,1:开启)
     */
    private Integer cardId;

    /**
     * 银行卡脱敏开关状态(0:关闭,1:开启)
     */
    private Integer bankCard;

    /**
     * QQ号脱敏开关状态(0:关闭,1:开启)
     */
    private Integer qq;

    /**
     * 微信脱敏开关状态(0:关闭,1:开启)
     */
    private Integer weChat;

    /**
     * 户籍脱敏开关状态(0:关闭,1:开启)
     */
    private Integer households;

    /**
     * 单位地址脱敏开关状态(0:关闭,1:开启)
     */
    private Integer unitAddress;

    /**
     * 居住地址脱敏开关状态(0:关闭,1:开启)
     */
    private Integer residentialAddress;

    /**
     * 家庭地址脱敏开关状态(0:关闭,1:开启)
     */
    private Integer homeAddress;

    /**
     * 单位名称脱敏开关状态(0:关闭,1:开启)
     */
    private Integer entityName;

    /**
     * 创建人
     */
    private String founder;

    /**
     * 创建时间
     */
    private Date creationtime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 删除标志
     */
    private Integer deleteLogo;
}
