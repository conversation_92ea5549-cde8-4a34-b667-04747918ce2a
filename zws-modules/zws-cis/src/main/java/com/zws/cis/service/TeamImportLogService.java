package com.zws.cis.service;

import com.zws.cis.domain.TeamImportLog;
import com.zws.system.api.model.LoginUser;

import java.util.List;
import java.util.Map;

/**
 * 导入案件日志信息--service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:08
 */
public interface TeamImportLogService {

    /**
     * 插入数据
     *
     * @param entity
     * @param loginUser
     * @return
     */
    long insert(TeamImportLog entity,LoginUser loginUser);

    /**
     * 更新
     *
     * @param entity
     * @param loginUser
     */
    void updateById(TeamImportLog entity, LoginUser loginUser);

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    List<TeamImportLog> selectList(Map<String, Object> param);
}
