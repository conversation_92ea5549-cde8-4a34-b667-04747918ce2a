package com.zws.cis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zws.system.api.domain.InfoBase;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InfoBaseMapper extends BaseMapper<InfoBase> {
    int deleteByPrimaryKey(Long id);

    int insert(InfoBase record);

    int insertSelective(InfoBase record);

    InfoBase selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InfoBase record);

    /**
     * 根据案件ID 更改
     * @param record
     * @return
     */
    int updateByCaseIdSelective(InfoBase record);

    int updateByPrimaryKey(InfoBase record);

    List<InfoBase> selectList(InfoBase record);


}
