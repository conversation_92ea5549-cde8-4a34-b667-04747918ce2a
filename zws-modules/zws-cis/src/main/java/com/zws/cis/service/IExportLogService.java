package com.zws.cis.service;

import com.zws.cis.domain.TeamExportLog;

import java.util.List;

/**
 * 导出日志 业务层接口
 *
 * <AUTHOR>
 * @date 2024/1/22 20:16
 */
public interface IExportLogService {

    /**
     * 写入
     * @param record
     * @return
     */
    Long insert(TeamExportLog record);

    /**
     * 更新
     * @param record
     */
    void update(TeamExportLog record);

    /**
     * 查询列表
     * @param record
     * @return
     */
    List<TeamExportLog> selectList(TeamExportLog record);

}
