package com.zws.cis.pojo;

import lombok.Data;

/**
 * 用于返回的各种状态格式
 * <AUTHOR>
 * @date ：Created in 2022/3/1 8:49
 */
@Data
public class TeamStatePojo {


    public TeamStatePojo() {

    }

    public TeamStatePojo(String info) {
        this.code = info;
        this.info = info;
    }
    public TeamStatePojo(Object code, String info) {
        this.code = code;
        this.info = info;
    }


    /**
     * 状态码,  code为null表示查询时返回info字段信息
     */
    private Object code;
    /**
     * 显示的信息
     */
    private String info;

}
