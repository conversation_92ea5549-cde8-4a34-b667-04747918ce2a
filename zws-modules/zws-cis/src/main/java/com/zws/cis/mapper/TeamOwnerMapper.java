package com.zws.cis.mapper;

import com.zws.cis.domain.TeamOwner;

import java.util.List;

public interface TeamOwnerMapper {
    int deleteByPrimaryKey(Long id);

    /**
     * 新增委托方
     *
     * @param record
     * @return
     */
    int insert(TeamOwner record);

    int insertSelective(TeamOwner record);

    TeamOwner selectByPrimaryKey(Long id);

    /**
     * 查询名称是否重复
     *
     * @param record
     * @return 大于0表示重复
     */
    int selectByName(TeamOwner record);

    /**
     * 修改委托方信息
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(TeamOwner record);

    int updateByPrimaryKey(TeamOwner record);

    /**
     * 查询列表
     *
     * @param record
     * @return
     */
    List<TeamOwner> selectList(TeamOwner record);

    /**
     * 查询委托方(资方方)的案件数量
     *
     * @param ownerId
     * @return
     */
    long selectCountByOwnerId(Long ownerId);
}