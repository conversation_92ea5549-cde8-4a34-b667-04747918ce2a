package com.zws.cis.controller;

import cn.hutool.core.bean.BeanUtil;
import com.zws.cis.controller.request.TeamManageParam;
import com.zws.cis.domain.TeamManage;
import com.zws.cis.domain.TeamProduct;
import com.zws.cis.service.TeamManageService;
import com.zws.cis.service.TeamProductService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.utils.SplitUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.log.annotation.Log;
import com.zws.common.log.enums.BusinessType;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 资产管理
 *
 * <AUTHOR>
 * @date ：Created in 2022/2/24 16:56
 */
@CrossOrigin
@RestController
@RequestMapping("/teamManage")
public class TeamManageController extends BaseController {

    @Autowired
    private TeamManageService teamManageService;
    @Autowired
    private TeamProductService productService;


    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo selectList(TeamManageParam param) {
        startPage();
        if (ObjectUtils.isEmpty(param)) param = new TeamManageParam();
        String packageNameList = param.getPackageNameList();
        if (StringUtils.isNotEmpty(packageNameList)) {
            List<String> packageNameArr = SplitUtils.strSplit(packageNameList, BaseConstant.comma);
            param.setPackageNameArr(packageNameArr);
        }
        param.setTeamId(SecurityUtils.getTeamId().intValue());
        List<TeamManage> list = teamManageService.selectList(BeanUtil.beanToMap(param));
        return getDataTable(list);
    }

    /**
     * ID 获取
     *
     * @param id
     * @return
     */
    @GetMapping("/get")
    public AjaxResult info(Long id) {
        TeamManage assetManage = teamManageService.selectById(id);
        if (assetManage == null) {
            return AjaxResult.error("获取失败");
        }
        TeamProduct product = productService.selectByPrimaryKey(assetManage.getProductId());
        if (product != null) {
            assetManage.setProductName(product.getName());
            assetManage.setOwnerName(product.getOwnerName());
        }
        return AjaxResult.success(assetManage);
    }


    /**
     * 编辑
     *
     * @param assetManage
     * @return
     */
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody TeamManage assetManage) {
        if (ObjectUtils.isEmpty(assetManage.getId())) {
            return AjaxResult.error("id不能为空");
        }
        if (ObjectUtils.isEmpty(assetManage.getPackageName())) {
            return AjaxResult.error("资产包名称不能为空");
        }
        if (assetManage.getPackageName().length() > 20) {
            return AjaxResult.error("资产包名称限制字数20位");
        }
        if (ObjectUtils.isEmpty(assetManage.getOpenAccount()) || assetManage.getAccountId() == null) {
            return AjaxResult.error("请选择回款开户账号");
        }
        Integer i = teamManageService.edit(assetManage);
        return AjaxResult.success();
    }

    /**
     * 删除
     *
     * @param entity
     * @return
     */
    @Log(title = "资产管理（删除）", businessType = BusinessType.DELETE)
    @PostMapping("/deleted")
    public AjaxResult remove(@RequestBody TeamManage entity) {
        return teamManageService.deletedById(entity.getId());
    }

    /**
     * 查询统计-案件总量-总委托金额
     * 案件总量-剩余债权总额(剩余应还总额)-剩余债权本金
     *
     * @param param
     * @return
     */
    @GetMapping("/selectCount")
    public AjaxResult selectCount(TeamManageParam param) {
        if (ObjectUtils.isEmpty(param)) param = new TeamManageParam();
        String packageNameList = param.getPackageNameList();
        if (StringUtils.isNotEmpty(packageNameList)) {
            List<String> packageNameArr = SplitUtils.strSplit(packageNameList, BaseConstant.comma);
            param.setPackageNameArr(packageNameArr);
        }
        param.setTeamId(SecurityUtils.getTeamId().intValue());
        Map<String, Object> map = teamManageService.selectCount(BeanUtil.beanToMap(param));
        return AjaxResult.success(map);
    }


//    /**
//     * 设置自动减免
//     *
//     * @param entity
//     * @return
//     */
//    @Log(title = "资产管理（设置自动减免）", businessType = BusinessType.INSERT)
//    @PostMapping("/updateReduction")
//    public AjaxResult updateReduction(@RequestBody TeamManage entity) {
//        TeamManage assetManage = new TeamManage();
//        if (entity.getId() == null) {
//            return AjaxResult.error("id 不能为空");
//        }
//        if (StringUtils.isEmpty(entity.getAutoReduction())) {
//            return AjaxResult.error("减免参数错误");
//        }
//        if (entity.getAutoReduction().equals(BaseConstant.State_Use)) {
//            if (entity.getReductionId() == null) {
//                return AjaxResult.error("开启自动减免,请选择减免公式");
//            }
//        }
//        assetManage.setId(entity.getId());
//        assetManage.setAutoReduction(entity.getAutoReduction());
//        assetManage.setReductionId(entity.getReductionId());
//        teamManageService.updateById(assetManage);
//        return AjaxResult.success();
//    }
//
//    /**
//     * 设置委托金额公式
//     *
//     * @param entity
//     * @return
//     */
//    @Log(title = "资产管理（设置委托金额公式）", businessType = BusinessType.INSERT)
//    @PostMapping("/updateEntrustMoney")
//    public AjaxResult updateEntrustMoney(@RequestBody TeamManage entity) {
//        TeamManage assetManage = new TeamManage();
//        if (entity.getId() == null) {
//            return AjaxResult.error("id 不能为空");
//        }
//        assetManage.setId(entity.getId());
//        assetManage.setEntrustMoneyId(entity.getEntrustMoneyId());
//        teamManageService.updateById(assetManage);
//        return AjaxResult.success();
//    }

}
