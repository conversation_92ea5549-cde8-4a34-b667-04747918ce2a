package com.zws.cis.service.impl;

import cn.hutool.json.JSONUtil;
import com.zws.cis.mapper.InfoLoanMapper;
import com.zws.cis.service.IInfoLoanService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.InfoLoan;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date ：Created in 2022/2/16 10:14
 */
@Slf4j
@Service
@Primary
public class InfoLoanServiceImpl implements IInfoLoanService {

    @Autowired
    private InfoLoanMapper infoLoanMapper;


    @Override
    public long insert(InfoLoan entity) throws Exception {
//        entity.check();
        entity.setDelFlag(BaseConstant.DelFlag_Being);
        if (StringUtils.isEmpty(entity.getCreateBy())){
            entity.setCreateBy(SecurityUtils.getUsername());
        }
        entity.setCreateTime(DateUtils.getNowDate());
        log.info("新增债权信息：{}", JSONUtil.toJsonStr(entity));
        int i=  infoLoanMapper.insert(entity);
        if(i>0) {
            return entity.getId();
        }
        return 0;
    }

    @Override
    public void updateById(InfoLoan entity) throws Exception {
        entity.check();
        entity.setUpdateBy(SecurityUtils.getUsername());
        entity.setUpdateTime(DateUtils.getNowDate());
        log.info("更新债权信息：{}", JSONUtil.toJsonStr(entity));
        infoLoanMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateByCaseId(InfoLoan entity) {
//        entity.check();
        entity.setUpdateBy(SecurityUtils.getUsername());
        entity.setUpdateTime(DateUtils.getNowDate());
        log.info("更新债权信息："+ JSONUtil.toJsonStr(entity));
        infoLoanMapper.updateByCaseIdSelective(entity);
    }

    @Override
    public InfoLoan selectByCaseId(Long caseId) {
        return null;
    }

}
