package com.zws.cis.mapper;

import com.zws.cis.domain.TeamAccount;

import java.util.List;

/**
 * 机构账户管理--（mapper层）
 *
 * @author: 马博新
 * @date ：Created in 2024/08/09 10:29
 */
public interface TeamAccountMapper {

    int deleteByPrimaryKey(Long id);

    int insert(TeamAccount record);

    int insertSelective(TeamAccount record);

    TeamAccount selectByPrimaryKey(Long id);

    /**
     * 查询账户管理列表
     *
     * @return
     */
    List<TeamAccount> selectList(TeamAccount account);

    /**
     * 查询账户管理已开启列表（机构）
     *
     * @return
     */
    List<TeamAccount> selectAccountById(Integer teamId);

    int updateByPrimaryKeySelective(TeamAccount record);

    int updateByPrimaryKey(TeamAccount record);

    /**
     * 根据id修改账户信息
     *
     * @param account
     * @return
     */
    int update(TeamAccount account);

    /**
     * 根据id删除账户信息
     *
     * @param account
     * @return
     */
    int delete(TeamAccount account);

    /**
     * 更新表名
     * @return
     */
    int updateTebleName();
}
