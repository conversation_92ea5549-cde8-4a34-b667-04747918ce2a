package com.zws.cis.controller.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 创建模板表单参数
 *
 * <AUTHOR>
 * @date ：Created in 2022/2/25 9:05
 */
@Data
public class TeamTemplatesForm {

    /**
     * 常用模板ID
     */
    private Long id;
    /**
     * 名称
     */
    @NotEmpty(message = "名称不能为空")
    @Size(max = 30, message = "名称过长")
    private String name;
    /**
     * 状态
     */
    private String state;

//    /**
//     * 产品模板
//     */
//    @NotNull(message = "模板不能为空")
//    private ProductTemplate template;

}
