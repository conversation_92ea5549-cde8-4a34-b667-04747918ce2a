package com.zws.cis.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zws.cis.domain.UrgeRecord;
import com.zws.cis.mapper.UrgeRecordMapper;
import com.zws.cis.service.IUrgeRecordService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 催记
 *
 * <AUTHOR>
 * @date 2024/8/8 19:44
 */
@Service
public class UrgeRecordServiceImpl implements IUrgeRecordService {

    @Resource
    private UrgeRecordMapper mapper;

    @Override
    public long insert(UrgeRecord record, LoginUser loginUser) {
        record.setDelFlag(BaseConstant.DelFlag_Being);
        if (StringUtils.isEmpty(record.getCreateBy())) {
            record.setCreateBy(SecurityUtils.getUsername(loginUser));
        }
        if (ObjectUtil.isNull(record.getCreateTime())) {
            record.setCreateTime(DateUtils.getNowDate());
        }
        record.setUpdateBy(SecurityUtils.getUsername(loginUser));
        record.setUpdateTime(DateUtils.getNowDate());
        int i = mapper.insert(record);
        if (i > 0) {
            return record.getId();
        }
        return 0;
    }

    @Override
    public void updateById(UrgeRecord record) {
        record.setUpdateTime(DateUtils.getNowDate());
        record.setUpdateBy(SecurityUtils.getUsername());
        mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void deletedById(Long id) {
        UrgeRecord record = new UrgeRecord();
        record.setId(id);
        record.setDelFlag(BaseConstant.DelFlag_Delete);
        updateById(record);
    }
}
