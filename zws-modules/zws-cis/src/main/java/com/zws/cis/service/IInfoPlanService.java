package com.zws.cis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zws.system.api.domain.InfoPlan;
import com.zws.system.api.domain.Library;

import java.util.List;

/**
 * 案件信息-还款计划
 * <AUTHOR>
 * @date ：2024年8月7日19:44:55
 */
public interface IInfoPlanService {

    /**
     * 插入保存数据
     * @param entity
     * @return
     */
    long insert(InfoPlan entity) throws Exception;


    long batchInsert(List<InfoPlan> entity);

    /**
     * 写入还款计划最初数据
     *
     * @param entity
     * @return
     */
    long insertInitial(InfoPlan entity);

    /**
     * 根据更改
     * @param entity
     */
    void updateById(InfoPlan entity) throws Exception;

    /**
     * 根据还款计划id修改更新罚息
     * @param entity
     */
    int updateInfoPlanById(InfoPlan entity);

    /**
     *根据 案件id、还款期数 查找还款计划，如果有多个返回最新的一条
     * @param caseId
     * @param hkPeriodsNumber
     * @return
     */
    InfoPlan getPlanByCaseIdAndTerm(Long caseId,Integer hkPeriodsNumber);


}
