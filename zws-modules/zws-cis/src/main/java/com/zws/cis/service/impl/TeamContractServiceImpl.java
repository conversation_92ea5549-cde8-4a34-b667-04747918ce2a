package com.zws.cis.service.impl;

import com.zws.cis.domain.TeamContract;
import com.zws.cis.mapper.TeamContractMapper;
import com.zws.cis.service.ITeamContractService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 委案合同-service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/09 11:43
 */
@Service
public class TeamContractServiceImpl implements ITeamContractService {

    @Resource
    private TeamContractMapper mapper;


    /**
     * 新增委案合同信息
     *
     * @param record
     * @return
     */
    @Override
    public int insert(TeamContract record, LoginUser loginUser) {
        record.setCreateTime(DateUtils.getNowDate());
        record.setCreateBy(loginUser.getUsername());
        record.setTeamId(SecurityUtils.getTeamId(loginUser).intValue());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        return mapper.insert(record);
    }

    @Override
    public int updateById(TeamContract record,LoginUser loginUser) {
        record.setUpdateTime(DateUtils.getNowDate());
        record.setUpdateBy(SecurityUtils.getUsername());
        return this.mapper.updateByPrimaryKeySelective(record);
    }
}
