package com.zws.cis.service.impl;

import com.zws.cis.pojo.CaseBaseInfo;
import com.zws.cis.service.IManageService;
import com.zws.common.core.enums.AllocatedStates;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.SplitUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 委托案件池--service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/08 16:22
 */
@Service
public class ManageServiceImpl implements IManageService {



//    @Override
//    public long insert(CaseManage entity) {
//        check(entity);
//        int row = mapper.insert(entity);
//        if (row > 0) {
//            return entity.getId();
//        }
//        return 0;
//    }
//
//    /**
//     * 根据资产管理id删除案件
//     *
//     * @param assetManageId
//     * @return
//     */
//    @Override
//    public int deletedByAssetManageId(Long assetManageId,Long teamId) {
//        if (assetManageId == null) {
//            return 0;
//        }
//        CaseManage entity = new CaseManage();
//        entity.setAssetManageId(assetManageId);
//        //0是false 1是true
//        entity.setDelFlag(true);
//        entity.setUpdateTime(DateUtils.getNowDate());
//        entity.setUpdateBy(SecurityUtils.getUsername());
//        entity.setTeamId(teamId);
//        return mapper.updateByAssetManageIdSelective(entity);
//    }
//
//    @Override
//    public int updateByCaseId(CaseManage entity) {
//        return this.mapper.updateByCaseId(entity);
//    }
//
//    @Override
//    public void dateDecryptDetails(Object clz) {
//        if (clz == null) {
//            return;
//        }
//
//        if (clz instanceof CaseBaseInfo) {
//            CaseBaseInfo caseBaseInfo = (CaseBaseInfo) clz;
//            caseBaseInfo.setClientName(FieldEncryptUtil.decrypt(caseBaseInfo.getClientName()));
//            caseBaseInfo.setClientIdNum(FieldEncryptUtil.decrypt(caseBaseInfo.getClientIdNum()));
//            caseBaseInfo.setClientPhone(FieldEncryptUtil.decrypt(caseBaseInfo.getClientPhone()));
//            caseBaseInfo.setClientCensusRegister(FieldEncryptUtil.decrypt(caseBaseInfo.getClientCensusRegister()));
//            caseBaseInfo.setQq(FieldEncryptUtil.decrypt(caseBaseInfo.getQq()));
//            caseBaseInfo.setWeixin(FieldEncryptUtil.decrypt(caseBaseInfo.getWeixin()));
//            caseBaseInfo.setMailbox(FieldEncryptUtil.decrypt(caseBaseInfo.getMailbox()));
//            caseBaseInfo.setPlaceOfWork(FieldEncryptUtil.decrypt(caseBaseInfo.getPlaceOfWork()));
//            caseBaseInfo.setWorkingAddress(FieldEncryptUtil.decrypt(caseBaseInfo.getWorkingAddress()));
//            caseBaseInfo.setResidentialAddress(FieldEncryptUtil.decrypt(caseBaseInfo.getResidentialAddress()));
//            caseBaseInfo.setHomeAddress(FieldEncryptUtil.decrypt(caseBaseInfo.getHomeAddress()));
//            //银行卡号 可能会多个加密后逗号拼接
//            List<String> bankCardNumberEns = SplitUtils.strSplitComma(caseBaseInfo.getBankCardNumber());
//            List<String> bankCardNumbers=new ArrayList<>();
//            for (String bankCardNumberEn:bankCardNumberEns) {
//                bankCardNumbers.add(FieldEncryptUtil.decrypt(bankCardNumberEn));
//            }
//            caseBaseInfo.setBankCardNumber(String.join(SplitUtils.regex_comma,bankCardNumbers));
//            // caseBaseInfo.setRegisteredAddress(FieldEncryptUtil.decrypt(caseBaseInfo.getRegisteredAddress()));
//            return;
//        }
//    }
//
//    @Override
//    public CaseManage selectByCaseId(Long caseId) {
//        return this.mapper.selectByCaseId(caseId);
//    }
//
//
//    /**
//     * 检查必填字段
//     *
//     * @param entity
//     */
//    protected void check(CaseManage entity) {
//        if (entity.getDelFlag() == null) {
//            entity.setDelFlag(false);
//        }
//        if (entity.getCreateTime() == null) {
//            entity.setCreateTime(DateUtils.getNowDate());
//        }
//        if (StringUtils.isEmpty(entity.getCreateBy())) {
//            entity.setCreateBy(SecurityUtils.getUsername());
//        }
//        //如果团队ID 为null 则表示未分配
//        /*if (entity.getOutsourcingTeamId() == null) {
//            entity.setCaseState(CaseStates.UNASSIGNED.getCode());
//        } else {
//            entity.setCaseState(CaseStates.ASSIGNED.getCode());
//        }*/
//
//        if (StringUtils.isEmpty(entity.getAllocatedState())) {
//            entity.setAllocatedState(AllocatedStates.UNASSIGNED.getCode());
//        }
//    }

}
