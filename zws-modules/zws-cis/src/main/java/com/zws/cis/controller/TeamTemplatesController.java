package com.zws.cis.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zws.cis.controller.request.TeamTemplatesForm;
import com.zws.cis.domain.TeamTemplates;
import com.zws.cis.service.TeamProductService;
import com.zws.cis.service.TeamTemplatesService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.poi.ExportUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.log.annotation.Log;
import com.zws.common.log.enums.BusinessType;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 常用模板
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:08
 */
@CrossOrigin
@RestController
@RequestMapping("/teamTemplates")
public class TeamTemplatesController extends BaseController {


    @Autowired
    private TeamTemplatesService teamTemplatesService;
    @Autowired
    private TeamProductService productService;


//    /**
//     * 查询列表
//     *
//     * @param param
//     * @return
//     */
//    @GetMapping("/list")
//    public TableDataInfo selectList(@RequestParam Map<String, Object> param) {
//        startPage();
//        if (ObjectUtil.isEmpty(param)) {
//            param = new HashMap<>();
//        }
//        param.put("teamId", SecurityUtils.getTeamId());
//        List<TeamTemplates> list = teamTemplatesService.selectList(param);
//        return getDataTable(list);
//    }
//
//    /**
//     * id获取常用模型信息
//     *
//     * @param id
//     * @return
//     */
//    @GetMapping("/get")
//    public AjaxResult info(Long id) {
//        //查找通用模板
//        TeamTemplates ct = teamTemplatesService.selectById(id);
//        if (ct == null) {
//            return AjaxResult.error("未找到指定ID的通用模板");
//        }
//        if (ObjectUtil.isEmpty(ct.getTemplate())) {
//            ProductTemplate template = productService.getInitTemplate();
//            ct.setTemplateInfo(template);
//        } else {
//            ProductTemplate template = JSONUtil.toBean(ct.getTemplate(), ProductTemplate.class);
//            //与初始化的模板比较、看是否有新增、删除的字段
//            ct.setTemplateInfo(ProductTemplateUtil.examine(template));
//        }
//        return AjaxResult.success(ct);
//    }
//
//    /**
//     * 创建模板
//     *
//     * @param pojo
//     * @return
//     */
//    @Log(title = "常用模板（创建模板）", businessType = BusinessType.INSERT)
//    @PostMapping("/add")
//    public AjaxResult add(@Validated @RequestBody TeamTemplatesForm pojo) {
//        ProductTemplate template = pojo.getTemplate();
//        if (template == null) {
//            return AjaxResult.error("请设置模板");
//        }
//        template.check();
//        TeamTemplates ct = new TeamTemplates();
//        ct.setName(pojo.getName());
//        ct.setTemplate(JSONUtil.toJsonStr(template));
//        long id = teamTemplatesService.insert(ct);
//        return AjaxResult.success(id);
//    }
//
//    /**
//     * 编辑
//     *
//     * @param pojo
//     * @return
//     */
//    @Log(title = "常用模板（编辑模板）", businessType = BusinessType.UPDATE)
//    @PostMapping("/edit")
//    public AjaxResult edit(@Validated @RequestBody TeamTemplatesForm pojo) {
//        ProductTemplate template = pojo.getTemplate();
//        if (template == null) {
//            return AjaxResult.error("请设置产品模板");
//        }
//        template.check();
//        TeamTemplates ct = new TeamTemplates();
//        ct.setId(pojo.getId());
//        ct.setName(pojo.getName());
//        ct.setTemplate(JSONUtil.toJsonStr(template));
//        teamTemplatesService.updateById(ct);
//        return AjaxResult.success();
//    }
//
//    /**
//     * 删除
//     *
//     * @param pojo
//     * @return
//     */
//    @Log(title = "常用模板（删除）", businessType = BusinessType.DELETE)
//    @PostMapping("/deleted")
//    public AjaxResult remove(@RequestBody TeamTemplatesForm pojo) {
//        teamTemplatesService.deletedById(pojo.getId());
//        return AjaxResult.success();
//    }
//
//
//    /**
//     * 修改状态
//     *
//     * @return
//     */
//    @Log(title = "常用模板（修改状态）", businessType = BusinessType.UPDATE)
//    @PostMapping("/updateState")
//    public AjaxResult updateState(@RequestBody String body) {
//        JSONObject jsonObject = JSONUtil.parseObj(body);
//        Long id = jsonObject.getLong("id");
//        String state = jsonObject.getStr("state");
//        TeamTemplates ct = new TeamTemplates();
//        ct.setId(id);
//        ct.setState(state.equals("0") ? BaseConstant.State_Use : BaseConstant.State_Forbidden);
//        teamTemplatesService.updateById(ct);
//        return AjaxResult.success();
//    }
//
//
//    /**
//     * 下载导入模板
//     *
//     * @param id
//     */
//    @PostMapping("/downloadTemplate")
//    public void downloadTemplate(HttpServletResponse response, Long id) throws IOException {
//        System.out.println("id:" + id);
//        if (id == null) {
//            throw new GlobalException("id为空");
//        }
//        TeamTemplates ct = teamTemplatesService.selectById(id);
//        String templateStr = ct.getTemplate();
//        if (StringUtils.isEmpty(templateStr)) {
//            throw new GlobalException("未设置模板");
//        }
//
//        ProductTemplate productTemplate = JSONUtil.toBean(templateStr, ProductTemplate.class);
//        List<String> tableHeader = productTemplate.getTableHeader();
//        String titlie = "";
//        ExportUtils.guideCaseExcelHeader(response, tableHeader, ct.getName() + ".xlsx", titlie);
//
//    }
//
//
//    /**
//     * 获取常用模板-供创建产品使用
//     *
//     * @return
//     */
//    @GetMapping("/getCurrencyTemplates")
//    public AjaxResult getCurrencyTemplates() {
//        Map<String, Object> param = new HashMap<>();
//        param.put("state", BaseConstant.State_Use);
//        param.put("teamId", SecurityUtils.getTeamId());
//        List<TeamTemplates> list = teamTemplatesService.selectList(param);
//        return AjaxResult.success(list);
//    }


}
