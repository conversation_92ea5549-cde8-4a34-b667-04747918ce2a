package com.zws.cis.controller;

import com.zws.cis.domain.TeamSetup;
import com.zws.cis.pojo.TeamStatePojo;
import com.zws.cis.service.ITeamSetupService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 还款方式设置（机构）
 *
 * <AUTHOR>
 * @date ：Created in 2022/3/7 15:55
 */
@CrossOrigin
@RestController
@RequestMapping("/teamSetup")
public class TeamSetupController extends BaseController {

    @Autowired
    private ITeamSetupService setupService;

//    /**
//     * 查询列表
//     *
//     * @return
//     */
//    @GetMapping("/list")
//    public TableDataInfo selectList() {
//        startPage();
//        Map<String, Object> map = new HashMap<>();
//        map.put("teamId", SecurityUtils.getTeamId());
//        List<TeamSetup> list = setupService.selectList(map);
//        return getDataTable(list);
//    }
//
//    /**
//     * id获取
//     *
//     * @param id
//     * @return
//     */
//    @GetMapping("/get")
//    public AjaxResult info(Long id) {
//        TeamSetup entity = setupService.selectById(id);
//        if (entity != null) {
//            entity.setReconciliationArr(entity.getReconciliation().split(entity.splitStr()));
//            entity.setRegisterPaymentArr(entity.getRegisterPayment().split(entity.splitStr()));
//            return AjaxResult.success(entity);
//        }
//        return AjaxResult.error("id错误,获取还款方式信息失败");
//    }

//    /**
//     * 新增还款方式
//     *
//     * @param entity
//     * @return
//     */
//    @PostMapping("add")
//    public AjaxResult add(@Validated @RequestBody TeamSetup entity) {
//        List<String> reconciliationArr = Arrays.asList(entity.getReconciliationArr());
//
//        if (reconciliationArr.contains(BaseConstant.Repayment_Required_Auto[6]) &&
//                reconciliationArr.contains(BaseConstant.Repayment_Required_Auto[7])
//        ) {
//            return AjaxResult.error(BaseConstant.Repayment_Required_Auto[6] + "," + BaseConstant.Repayment_Required_Auto[7] + "不能同时选择");
//        }
//        entity.setReconciliation(StringUtils.join(entity.getReconciliationArr(), entity.splitStr()));
//        entity.setRegisterPayment(StringUtils.join(entity.getRegisterPaymentArr(), entity.splitStr()));
//        long id = setupService.insert(entity);
//        return AjaxResult.success(id);
//    }

//    /**
//     * 编辑
//     *
//     * @param entity
//     * @return
//     */
//    @PostMapping("/edit")
//    public AjaxResult edit(@Validated @RequestBody TeamSetup entity) {
//        List<String> reconciliationArr = Arrays.asList(entity.getReconciliationArr());
//        if (reconciliationArr.contains(BaseConstant.Repayment_Required_Auto[6]) &&
//                reconciliationArr.contains(BaseConstant.Repayment_Required_Auto[7])
//        ) {
//            return AjaxResult.error(BaseConstant.Repayment_Required_Auto[6] + "," + BaseConstant.Repayment_Required_Auto[7] + "不能同时选择");
//        }
//
//        entity.setReconciliation(StringUtils.join(entity.getReconciliationArr(), entity.splitStr()));
//        entity.setRegisterPayment(StringUtils.join(entity.getRegisterPaymentArr(), entity.splitStr()));
//        setupService.updateById(entity);
//        return AjaxResult.success();
//    }

//    /**
//     * 获取还款方式的登记必填字段
//     *
//     * @param id
//     * @return
//     */
//    @GetMapping("/getRegisterPayment")
//    public AjaxResult getRegisterPayment(Long id) {
//        TeamSetup entity = setupService.selectById(id);
//        if (entity == null) {
//            return AjaxResult.error("id错误,获取还款方式信息失败");
//        }
//        String[] strAll = entity.getRegisterPayment().split(entity.splitStr());
//        return AjaxResult.success(strAll);
//    }

//    /**
//     * 获取新增、编辑时的必填字段下拉选项
//     *
//     * @return
//     */
//    @GetMapping("/getRequired")
//    public AjaxResult getRequired() {
//        Map<String, Object> map = new HashMap<>();
//        map.put("reconciliationArr", BaseConstant.Repayment_Required_Auto);//自动对账必填
//        map.put("registerPaymentArr", BaseConstant.Repayment_Required_Register);//登记回款必填
//        return AjaxResult.success(map);
//    }

//
//    /**
//     * 更改状态
//     *
//     * @param entity
//     * @return
//     */
//    @PostMapping("/editState")
//    public AjaxResult editState(@RequestBody TeamSetup entity) {
//        if (entity.getId() == null) {
//            return AjaxResult.error("id不能为空");
//        }
//        if (entity.getState() == null) {
//            return AjaxResult.error("状态不能为空");
//        }
//
//        TeamSetup temp = new TeamSetup();
//        temp.setState(entity.getState());
//        temp.setId(entity.getId());
//        setupService.updateById(temp);
//        return AjaxResult.success();
//    }
//
//    /**
//     * 获取还款方式下拉选项
//     *
//     * @return
//     */
//    @GetMapping("/getRepaymentOptions")
//    public AjaxResult getRepaymentOptions() {
//        Map<String, Object> map = new HashMap<>();
//        map.put("teamId", SecurityUtils.getTeamId());
//        List<TeamSetup> repaymentSetups = setupService.selectList(map);
//        List<TeamStatePojo> list = new ArrayList<>();
//        repaymentSetups.forEach(temp -> {
//            list.add(new TeamStatePojo(temp.getId().toString(), temp.getRepaymentMethod()));
//        });
//        return AjaxResult.success(list);
//    }
}
