package com.zws.cis.task;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.zws.cis.controller.request.TeamImportParam;
import com.zws.cis.domain.TeamImportLog;
import com.zws.cis.enums.ImportStartEnum;
import com.zws.cis.service.*;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.R;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.threadpool.WorkTask;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.SplitUtils;
import com.zws.common.core.utils.SpringUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.domain.InfoPlan;
import com.zws.system.api.domain.Library;
import com.zws.system.api.domain.SysFile;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;

/**
 * 导入还款计划工作线程
 *
 * <AUTHOR>
 * @date ：Created in 2022/3/19 18:14
 */
@Slf4j
public class ImportPlantWorkTask implements WorkTask {

    private TeamProductService productService = SpringUtils.getBean(TeamProductService.class);
    private IInfoBaseService infoBaseService = SpringUtils.getBean(IInfoBaseService.class);//案件基本信息
    private ILibraryService libraryService = SpringUtils.getBean(ILibraryService.class);//
    private IInfoPlanService infoPlanService = SpringUtils.getBean(IInfoPlanService.class);//
    private TeamImportLogService importLogService = SpringUtils.getBean(TeamImportLogService.class);//
    private RemoteFileService fileService = SpringUtils.getBean(RemoteFileService.class);
    private IInfoLoanService infoLoanService = SpringUtils.getBean(IInfoLoanService.class);


    private TeamImportParam param;//导入参数
    private Long importLogId;//导入日志id
    private Set<Long> productIds = new HashSet<>();//多个产品时的产品ID
    private Set<Long> ownerIds = new HashSet<>();//多个资产方时的资产方ID集合
    private Set<Long> caseIds = new HashSet<>();//导入还款计划的案件id集合
    private LoginUser loginUser;
    private Long teamId;
    public ImportPlantWorkTask(TeamImportParam param, Long importLogId, LoginUser loginUser) {
        this.param = param;
        this.importLogId = importLogId;
        this.loginUser = loginUser;
        teamId = SecurityUtils.getTeamId(loginUser);
    }

    @Override
    public void runTask() {
//        TeamImportLog importLog = new TeamImportLog();
//        importLog.setId(importLogId);
//        ImportStartEnum importStart = ImportStartEnum.ING;
//        try {
//            File tempFile = FileDownloadUtils.downloadTempFile(param.getFileUrl());
//            ExcelReader reader = ExcelUtil.getReader(tempFile);
//            List<Map<String, Object>> rows = reader.readAll();
//            ;
//            List<Map<String, Object>> fails = new ArrayList<>();//导入失败
//
//            long successNum = 0;
//            for (Map<String, Object> row : rows) {
//                Map<String, Object> retMap = actionRow(row);
//                boolean start = (boolean) retMap.get("start");
//                if (start) {
//                    successNum++;
//                } else {//失败
//                    String errorMsg = (String) retMap.get("errorMsg");
//                    row.put("失败原因", errorMsg);
//                    fails.add(row);
//                }
//            }
//
////            根据案件id查询案件贷款信息更新案件罚息
//           /* if (!ObjectUtils.isEmpty(caseIds)) {
//                for (Long row : caseIds) {
//                    casePenaltyInterest(row);
//                }
//            }*/
//
//            if (fails.size() == 0) {//全部成功
//                importStart = ImportStartEnum.SUCCESS;
//            } else {
//                if (fails.size() == rows.size()) {//全部失败
//                    importStart = ImportStartEnum.FAIL;
//                } else {
//                    importStart = ImportStartEnum.PARTIAL_SUCCESS;
//                }
//                //上传文件
//                importLog.setFailFileUrl(uploadFailFile(fails));
//            }
//            importLog.setSuccessNumber(successNum);
//            FileDownloadUtils.deletedTempFile(tempFile);
//        } catch (Exception e) {
//            log.error("导入还款计划失败", e);
//            importStart = ImportStartEnum.FAIL;
//        } finally {
//            if (productIds.size() > 0) {
//                importLog.setProductIds(StringUtils.join(productIds, SplitUtils.regex_comma));
//            }
//            if (ownerIds.size() > 0) {
//                importLog.setOwnerIds(StringUtils.join(ownerIds, SplitUtils.regex_comma));
//            }
//
//            importLog.setImportStart(importStart.getCode());
//            importLogService.updateById(importLog,loginUser);
//        }
    }
//
//    /**
//     * 根据案件id查询案件贷款信息更新案件罚息
//     *
//     * @param caseId
//     */
//  /*  public void casePenaltyInterest(Long caseId) {
//        List<InfoPlan> infoPlans = infoPlanService.selectByCaseId(caseId);
//        BigDecimal money = BigDecimal.ZERO;  //剩余应还罚息
//        BigDecimal syPrincipal = BigDecimal.ZERO; //剩余应还本金
//        BigDecimal syInterest = BigDecimal.ZERO; //剩余应还利息
//        if (!ObjectUtils.isEmpty(infoPlans)) {
//            for (InfoPlan row : infoPlans) {
//                BigDecimal syYhPenaltyInterest = row.getSyYhPenaltyInterest() == null ? BigDecimal.ZERO : row.getSyYhPenaltyInterest();
//                money = money.add(syYhPenaltyInterest);
//                BigDecimal syYhPrincipal = row.getSyYhPrincipal() == null ? BigDecimal.ZERO : row.getSyYhPrincipal();
//                syPrincipal = syPrincipal.add(syYhPrincipal);
//                BigDecimal syYhInterest = row.getSyYhInterest() == null ? BigDecimal.ZERO : row.getSyYhInterest();
//                syInterest = syInterest.add(syYhInterest);
//            }
//        }
//        InfoLoan infoLoan = new InfoLoan();
//        infoLoan.setCaseId(caseId);
//        infoLoan.setSyYhDefault(money);
//        infoLoan.setSyYhInterest(syInterest);
//        infoLoan.setSyYhPrincipal(syPrincipal);
//        infoLoan.setRemainingDue(syPrincipal.add(syInterest).add(money));
//        infoLoanService.updateByCaseIdDefaultInterest(infoLoan);
//    }*/
//
//    /**
//     * 处理行
//     *
//     * @param row
//     */
//    private Map<String, Object> actionRow(Map<String, Object> row) {
//        Map<String, Object> map = new HashMap<>();
//        String[] template = DownloadConstant.planTemplate;
//        boolean start = false;
//        String errorMsg = "";
//        try {
//            Long caseId = checkCaeId(row.get(template[0])); //案件ID
////            Integer serialNumber = checkInteger(row.get(template[1]), false, "序号错误");//序号
//            Integer hkPeriodsNumber = checkInteger(row.get(template[1]), false, "还款期数错误");//还款期数
//            Date yhDate = checkDate(row.get(template[2]), false, "应还日期 错误");//应还日期
//            BigDecimal jhYhMoney = checkBigDecimal(row.get(template[3]), false, "应还总额 错误");//计划应还总额
//            BigDecimal jhYhPrincipal = checkBigDecimal(row.get(template[4]), false, "应还本金 错误");//计划应还本金
//
//            BigDecimal jhYhInterest = checkBigDecimal(row.get(template[5]), false, "应还利息 错误");//计划应还利息
//            BigDecimal syYhSurplusInterest = checkBigDecimal(row.get(template[6]), false, "应还费用 错误");//计划应还费用
//
////            BigDecimal syYhMoney = checkBigDecimal(row.get(template[6]), false, "剩余应还金额 错误");//剩余应还金额
////            BigDecimal syYhPrincipal = checkBigDecimal(row.get(template[7]), false, "剩余应还本金 错误");//剩余应还本金
////            BigDecimal syYhInterest = checkBigDecimal(row.get(template[8]), false, "剩余应还利息 错误");//剩余应还利息
//
////            BigDecimal syYhCompoundInterest = checkBigDecimal(row.get(template[9]), false, "剩余应还复利 错误");//剩余应还复利
////            BigDecimal syYhPenaltyInterest = checkBigDecimal(row.get(template[10]), false, "剩余应还罚息 错误");//剩余应还罚息
////            BigDecimal syYhWyj = checkBigDecimal(row.get(template[11]), false, "剩余应还违约金 错误");//剩余应还违约金
////            BigDecimal syYhLateFee = checkBigDecimal(row.get(template[12]), false, "剩余应还滞纳金 错误");//剩余应还滞纳金
//
////            Date alreadyHkDate = checkDate(row.get(template[13]), false, "已还款日期 错误");//已还款日期
//
//            BigDecimal ychMoney = checkBigDecimal(row.get(template[7]), false, "实还总额 错误");//已偿还总额
//            BigDecimal ychPrincipal = checkBigDecimal(row.get(template[8]), false, "实还本金 错误");//已偿还本金
//            BigDecimal ychInterest = checkBigDecimal(row.get(template[9]), false, "实还利息 错误");//已偿还利息
//            BigDecimal ychPenaltyInterest = checkBigDecimal(row.get(template[10]), false, "实还费用 错误");//实还费用
//
////            BigDecimal ychCompoundInterest = checkBigDecimal(row.get(template[16]), false, "实还复利 错误");//已偿还复利
////            BigDecimal ychLateFee = checkBigDecimal(row.get(template[17]), false, "实还滞纳金 错误");//已偿还滞纳金
////            BigDecimal ychWyj = checkBigDecimal(row.get(template[18]), false, "实还违约金 错误");//已偿还违约金
////            BigDecimal ychMoney = checkBigDecimal(row.get(template[19]), false, "实还金额 错误");//已偿还金额
//
//
//            InfoPlan plan = new InfoPlan();
//            plan.setCaseId(caseId);
//            plan.setDelFlag(BaseConstant.DelFlag_Being);
////            plan.setOrderNumber(serialNumber);
//            plan.setHkPeriodsNumber(hkPeriodsNumber);
//            plan.setYhDate(yhDate);
//            plan.setJhYhMoney(jhYhMoney);
//            plan.setJhYhPrincipal(jhYhPrincipal);
//            plan.setJhYhInterest(jhYhInterest);
////            plan.setSyYhMoney(syYhMoney);
////            plan.setSyYhPrincipal(syYhPrincipal);
////            plan.setSyYhInterest(syYhInterest);
////            plan.setSyYhCompoundInterest(syYhCompoundInterest);
////            plan.setSyYhPenaltyInterest(syYhPenaltyInterest);
////            plan.setSyYhWyj(syYhWyj);
////            plan.setSyYhLateFee(syYhLateFee);
////            plan.setAlreadyHkDate(alreadyHkDate);
//            plan.setSyYhSurplusInterest(syYhSurplusInterest);
//            plan.setYchPrincipal(ychPrincipal);
//            plan.setYchInterest(ychInterest);
//            plan.setYchPenaltyInterest(ychPenaltyInterest);
////            plan.setYchCompoundInterest(ychCompoundInterest);
////            plan.setYchLateFee(ychLateFee);
////            plan.setYchWyj(ychWyj);
//            plan.setYchMoney(ychMoney);
//
//            long insert = infoPlanService.insert(plan);
////            写入还款计划最初数据
//            plan.setPlanId(insert);
//            infoPlanService.insertInitial(plan);
//            start = true;
//        } catch (Exception e) {
//            e.printStackTrace();
//            start = false;
//            errorMsg = e.getMessage();
//        }
//        map.put("start", start);
//        map.put("errorMsg", errorMsg);
//        return map;
//    }
//
//
//    /**
//     * 检查案件是否合法
//     *
//     * @param obj
//     */
//    private Long checkCaeId(Object obj) {
//        if (ObjectUtils.isEmpty(obj)) {
//            throw new GlobalException("案件ID 为空");
//        }
//        String caseIdStr = StrUtil.toString(obj);
//        if (!StringUtils.isNumeric(caseIdStr)) {
//            throw new GlobalException("案件ID 错误");
//        }
//        Long caseId = Long.parseLong(caseIdStr);
//        Library library = libraryService.selectById(caseId);
//        if (library == null || !ObjectUtil.equals(library.getTeamId(),teamId)) {
//            throw new GlobalException("案件ID 错误");
//        }
//
//        if (param.getId() != null) {//只能导入指定的id
//            if (!ObjectUtil.equals(library.getProductId(), param.getProductId())) {
//                throw new GlobalException("不是指定的资产方-产品");
//            }
//        } else {
//            this.productIds.add(library.getProductId());
//            this.ownerIds.add(library.getEntrustingPartyId());
//        }
//        return caseId;
//    }
//
//    /**
//     * 检查是否是int数字
//     *
//     * @param obj
//     * @param errMsg
//     * @return
//     */
//    private Integer checkInteger(Object obj, boolean notNull, String errMsg) {
//        String str = null;
//        if (!ObjectUtils.isEmpty(obj)) {
//            str = StrUtil.toString(obj);
//        }
//        if (str == null || str.equals("")) {
//            if (notNull) {
//                new GlobalException(errMsg);
//            } else {
//                return null;
//            }
//        }
//        if (!StringUtils.isNumeric(str)) {
//            throw new GlobalException(errMsg);
//        }
//        return new Integer(str);
//    }
//
//    /**
//     * 检查是否Date类型
//     *
//     * @param obj
//     * @param notNull 不能为空，true必填，false 可以为空
//     * @param errMsg
//     * @return
//     */
//    private Date checkDate(Object obj, boolean notNull, String errMsg) {
//        String str = null;
//        if (!ObjectUtils.isEmpty(obj)) {
//            str = StrUtil.toString(obj);
//        }
//        if (notNull && StringUtils.isEmpty(str)) {
//            throw new GlobalException(errMsg);
//        } else if (StringUtils.isEmpty(str)) {
//            return null;
//        }
//        Date date = DateUtils.parseDate(str);
//        if (date == null) {
//            throw new GlobalException(errMsg);
//        }
//        return date;
//    }
//
//    /**
//     * 检查是否BigDecimal类型
//     *
//     * @param obj
//     * @param errMsg
//     * @return
//     */
//    private BigDecimal checkBigDecimal(Object obj, boolean notNull, String errMsg) {
//        String str = null;
//        if (!ObjectUtils.isEmpty(obj)) {
//            str = StrUtil.toString(obj);
//        }
//        if (notNull && StringUtils.isEmpty(str)) {
//            throw new GlobalException(errMsg);
//        } else if (StringUtils.isEmpty(str)) {
//            return null;
//        }
////        if (!StringUtils.isNumeric(str)) throw new GlobalException(errMsg);
//        if (!NumberUtil.isNumber(str)) {
//            throw new GlobalException(errMsg);
//        }
//        return new BigDecimal(str);
//    }
//
//    /**
//     * 上传保存失败的案件
//     *
//     * @param fails
//     * @return
//     * @throws Exception
//     */
//    private String uploadFailFile(List<Map<String, Object>> fails) {
//        try {
//            MultipartFile multipartFile = FileDownloadUtils.generateMultipartFile(fails);
//            R<SysFile> rest = fileService.upload(multipartFile);
//            if (rest.getCode() == R.SUCCESS) {
//                return rest.getData().getUrl();
//            } else {
//                log.error("保存失败文件错误", rest.getMsg());
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error(JSONUtil.toJsonStr(fails));
//            log.error("保存失败文件错误", e);
//        }
//        return "";
//    }


    @Override
    public void cancelTask() {

    }

    @Override
    public int getProgress() {
        return 0;
    }
}
