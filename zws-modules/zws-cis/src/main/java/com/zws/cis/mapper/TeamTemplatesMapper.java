package com.zws.cis.mapper;

import com.zws.cis.domain.TeamTemplates;

import java.util.List;
import java.util.Map;

public interface TeamTemplatesMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TeamTemplates record);

    int insertSelective(TeamTemplates record);

    TeamTemplates selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TeamTemplates record);

    int updateByPrimaryKeyWithBLOBs(TeamTemplates record);

    int updateByPrimaryKey(TeamTemplates record);

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    List<TeamTemplates> selectList(Map<String, Object> param);
}