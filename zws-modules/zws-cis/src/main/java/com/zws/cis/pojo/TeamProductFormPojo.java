package com.zws.cis.pojo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 产品提交表单
 *
 * <AUTHOR>
 * @date ：Created in 2022/2/14 14:07
 */
@Data
public class TeamProductFormPojo {

    /**
     * 产品ID
     */
    private Long id;
    /**
     * 资产方id
     */
    private Long ownerId;

    /**
     * 产品类型
     */
    @NotBlank(message = "产品类型不能为空")
    private String name;

    /**
     * 产品代号
     */
//    @NotBlank(message = "产品代号不能为空")
    private String productCode;

    /**
     * 状态，0-启用，非0-禁用，true启动，false禁用
     */
    private boolean state;

//    /**
//     * 产品模板
//     */
//    private ProductTemplate template;

}
