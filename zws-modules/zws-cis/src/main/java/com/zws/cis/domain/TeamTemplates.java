package com.zws.cis.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 常用模板记录表(saas系统机构模版)
 */
@Data
public class TeamTemplates {

    /**
     * 常用模板id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态，0启用，非0禁用
     */
    private String state;

    /**
     * 模板
     */
    private String template;

    /**
     * 机构id
     */
    private Integer teamId;

    private String delFlag;

    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

//    /**
//     * 模板对象
//     */
//    private ProductTemplate templateInfo;
}