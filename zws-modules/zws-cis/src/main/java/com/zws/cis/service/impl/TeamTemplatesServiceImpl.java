package com.zws.cis.service.impl;

import com.zws.cis.domain.TeamTemplates;
import com.zws.cis.mapper.TeamTemplatesMapper;
import com.zws.cis.service.TeamTemplatesService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 常用模板--service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:10
 */
@Service
public class TeamTemplatesServiceImpl implements TeamTemplatesService {

    @Resource
    private TeamTemplatesMapper mapper;


    /**
     * 查找通用模板
     *
     * @param id
     * @return
     */
    @Override
    public TeamTemplates selectById(Long id) {
        return mapper.selectByPrimaryKey(id);
    }

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    @Override
    public List<TeamTemplates> selectList(Map<String, Object> param) {
        List<TeamTemplates> list = mapper.selectList(param);
        return list;
    }

    /**
     * 插入新增数据
     *
     * @param entity
     * @return 返回id
     */
    @Override
    public Long insert(TeamTemplates entity) {
        if (entity.getName().isEmpty()) {
            throw new GlobalException("名称不能为空");
        }
        if (entity.getTemplate().isEmpty()) {
            throw new GlobalException("模板不能为空");
        }
        entity.setTeamId(SecurityUtils.getTeamId().intValue());
        entity.setDelFlag(BaseConstant.DelFlag_Being);
//        entity.setState(BaseConstant.State_Use);
        entity.setCreateBy(SecurityUtils.getUsername());
        entity.setCreateTime(DateUtils.getNowDate());
        int i = mapper.insert(entity);
        if (i > 0) {
            return entity.getId();
        }
        return 0L;
    }

    /**
     * 编辑数据
     *
     * @param entity
     * @return
     */
    @Override
    public void updateById(TeamTemplates entity) {
        entity.setUpdateBy(SecurityUtils.getUsername());
        entity.setUpdateTime(DateUtils.getNowDate());
        mapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 删除模版数据
     *
     * @param id
     */
    @Override
    public void deletedById(Long id) {
        TeamTemplates entity = new TeamTemplates();
        entity.setId(id);
        entity.setDelFlag(BaseConstant.DelFlag_Delete);
        updateById(entity);
    }
}
