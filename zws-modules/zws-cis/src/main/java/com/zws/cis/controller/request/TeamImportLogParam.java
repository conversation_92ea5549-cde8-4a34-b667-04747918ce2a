package com.zws.cis.controller.request;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 导入日志查询参数
 * <AUTHOR>
 * @date ：Created in 2022/3/18 15:12
 */
@Data
public class TeamImportLogParam {


    private Long id;
    /**
     * 导入时间-开始
     */
    private Date importTime1;
    /**
     * 导入时间-截止
     */
    private Date importTime2;

    /**
     * 导入批次号
     */
    private String importBatchNum;

    /**
     * 导入状态
     */
    private String importStart;

    /**
     * 资产包名称--集合
     */
    private List<String> packageNameArr;

    /**
     * 资产包名称--前端传参
     */
    private String packageNameList;

}
