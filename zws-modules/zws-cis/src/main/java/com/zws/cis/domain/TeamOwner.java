package com.zws.cis.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * 委托方信息表（saas系统机构委托方）
 */
@Data
public class TeamOwner {

    /**
     * 委托方id
     */
    private Long id;

    /**
     * 委托方名称
     */
    @NotEmpty(message = "委托方名称不能为空")
    private String name;

    /**
     * 简称、委托方ID(字典表中的键值)
     */
    private String shortName;

    /**
     * 社会信用统一代码
     */
    private String unifiedCode;

    /**
     * 机构id
     */
    private Integer teamId;

    private String delFlag;

    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}