package com.zws.cis.controller.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.cis.domain.TeamProduct;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 导入时的参数
 * <AUTHOR>
 * @date ：Created in 2022/2/22 9:11
 */
@Data
@ToString
public class TeamImportParam {


    private Long id;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     *资产方id
     */
    private Long ownerId;
    /**
     * 导入批次号
     */
    private String importBatchNum;

    /**
     * 债权准入日期
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date creditorAccessDate;

    /**
     * 收购成本(元)
     */
    private BigDecimal acquisitionCosts;

    /**
     *回款目标金额(元)
     */
    private BigDecimal targetAmount;
    /**
     * 回款目标周期_开始
     */
    private Date beginPeriod;
    /**
     * 回款目标周期_结束
     */
    private Date endPeriod;
    /**
     * 费率
     */
    private BigDecimal rate;
    /**
     * 预期收益(元)
     */
    private BigDecimal expectedRevenue;
    /**
     * 收购日期
     */
    private Date acquisitionDate;
    /**
     * 交割日期
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date closingDate;
    /**
     * 文件地址URL
     */
    private String fileUrl;

    /**
     * 委托金额公式id
     */
    private Long entrustMoneySetupId;
    /**
     * 是否开启自动减免
     */
    private boolean openAuotReductionSetup;
    /**
     * 自动减免公式id
     */
    private Long reductionSetupId;

    /**
     * 原文件名
     */
    private String originalFilename;

    /**
     * 委案合同id集合
     */
    private List<Long> contractIds;

    private String uuid;
    /**
     * 团队产品
     */
    private TeamProduct product;

    /**
     *  资产包名称
     */
    private String packageNameArr;

    /**
     *  回款开户账号
     */
    private String openAccount;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 债权公告日
     */
    private Date creditorAnnouncementDate;

    /**
     * 债权公告（链接地址）
     */
    private String creditorAnnouncement;

    /**
     * 签订日
     */
    private Date signingDate;

    /**
     * 协议编号
     */
    private String protocolNumber;

    /**
     * 协议名称
     */
    private String protocol;

    /**
     * 结清模版id
     */
    private String settleId;

    /**
     * 结清模版名称
     */
    private String settleName;


}
