package com.zws.cis.service;

import com.zws.cis.domain.TeamContract;
import com.zws.system.api.model.LoginUser;

/**
 * 委案合同-service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/09 11:43
 */
public interface ITeamContractService {


    /**
     * 新增委案合同信息
     *
     * @param record
     * @return
     */
    int insert(TeamContract record, LoginUser loginUser);

    /**
     * 主键更新
     * @param record
     * @return
     */
    int updateById(TeamContract record,LoginUser loginUser);
}
