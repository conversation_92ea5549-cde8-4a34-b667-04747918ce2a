package com.zws.cis.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.PageUtil;
import cn.hutool.json.JSONUtil;
import com.zws.cis.controller.request.TeamImportParam;
import com.zws.cis.domain.TeamImportLog;
import com.zws.cis.domain.TeamManage;
import com.zws.cis.domain.TeamProduct;
import com.zws.cis.enums.ImportStartEnum;
import com.zws.cis.pojo.TeamImportCaseSonTaskResp;
import com.zws.cis.pojo.TeamImportCaseSynContainer;
import com.zws.cis.service.TeamImportLogService;
import com.zws.cis.service.TeamManageService;
import com.zws.common.core.domain.R;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.threadpool.WorkTask;
import com.zws.common.core.utils.BigExcelUtils;
import com.zws.common.core.utils.SpringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.domain.SysFile;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 导入案件工作线程
 *
 * <AUTHOR>
 * @date ：Created in 2022/3/16 15:15
 */
@Slf4j
public class ImportCaseWorkTaskBig implements WorkTask {

    private TeamManageService assetManageService = SpringUtils.getBean(TeamManageService.class);
    private TeamImportLogService importLogService = SpringUtils.getBean(TeamImportLogService.class);
    private RemoteFileService fileService= SpringUtils.getBean(RemoteFileService.class);
    //private ZwsApplicationConfig zwsApplicationConfig=SpringUtils.getBean(ZwsApplicationConfig.class);

    //导入时的参数
    private TeamImportParam param;
    //案件管理对象
    private TeamManage asset;
    private Long logId;//导入日志id
//    private ReductionSetup rs; //减免规则
//    private EntrustMoneySetup ems;//委托金额策略
    private LoginUser loginUser;//登录人信息


    public ImportCaseWorkTaskBig(TeamManage asset, long logId, TeamImportParam param,LoginUser loginUser) {
        this.asset = asset;
        this.logId = logId;
        this.param=param;
        this.loginUser=loginUser;

    }

    @Override
    public void runTask() {
//        TeamImportCaseSynContainer synContainer=new TeamImportCaseSynContainer();
//        final TimeInterval timer = new TimeInterval();//计时
//        TeamImportLog importLog = new TeamImportLog();
//        importLog.setId(logId);
//        ImportStartEnum istart = ImportStartEnum.ING;
//        try {
//
//            TeamProduct product = param.getProduct();
//            if (product == null) {
//                throw new ServiceException("未找到产品，请确定产品id");
//            }
//
//            timer.start("downloadTempFile");//下载文件
//            File tempFile= FileDownloadUtils.downloadTempFile(param.getFileUrl());//下载导入的文件
//            log.info("下载文件耗时:{} ms ",timer.intervalMs("downloadTempFile"));
//
//            timer.start("read");//读取文件
//            BigExcelUtils bigExcelUtils=new BigExcelUtils();
//            List<List<Object>> rows = bigExcelUtils.read(tempFile, 0);
//            log.info("读取文件耗时:{} ms ",timer.intervalMs("read"));
//            log.info("读取文件总记录数:{}",rows.size());
//            if (rows.size()<1){
//                throw new ServiceException("上传正确的模版");
//            }
//            //总记录行数（不包括标题行）
//            int rowCount= rows.size()-1;
//
//            //第一行是标题
//            List<Object> titleRow=rows.get(0);
//            //数据列移除标题行
//            rows.remove(0) ;
//            //int maxThreadSize= zwsApplicationConfig.getMaxThreadSize();//最大同时执行线程数
//            List<Thread> tasks=new ArrayList<>();
//            //int idleWorkTask=zwsApplicationConfig.getIdleWorkTaskSize();//空闲任务线程数
//            int idleWorkTask= 5;
//            int workSize= rowCount%idleWorkTask==0? rowCount/idleWorkTask: (rowCount/idleWorkTask)+1;//每个任务线程的row量
//            log.info("导入案件,每条线程任务数:{}",workSize);
//            log.info("导入案件,线程数:{}",idleWorkTask);
//            timer.start("workTask");//任务开始
//            //总案件数
//            int rowTotalSize=0;
//            for (int i=0;i<idleWorkTask; i++){
//                int[] startEnd = PageUtil.transToStartEnd(i, workSize);
//                int start=startEnd[0];
//                int end=startEnd[1];
//                if(start>rowCount) {
//                    start=rowCount;
//                }
//                if(end>rowCount) {
//                    end=rowCount;
//                }
//                List<List<Object>> workTaskRows = CollUtil.sub(rows, start, end);
//                if(workTaskRows.size()==0) {
//                    continue;
//                }
//                rowTotalSize+=workTaskRows.size();
//                ImportCaseSonWorkTaskBig sonWorkTask=new ImportCaseSonWorkTaskBig(asset,product, synContainer,titleRow, workTaskRows, loginUser);
//                tasks.add(sonWorkTask);
//                sonWorkTask.start();
//                if (start>rowCount||end>rowCount) {
//                    break;
//                }
//            }
//
//
//            // 线程状态监控【如果全部线程都执行完成 或者 未全部执行完成但总耗时超过了最大等待时间，就结束等待】-----
//            for(Thread service: tasks) {
//                Thread.State state = service.getState();
//                while (state != Thread.State.TERMINATED) {
//                    state = service.getState();
//                    //System.out.println("当前线程状态：" + state);
//                    try {Thread.sleep(200);} catch (InterruptedException e) {e.printStackTrace();}
//                }
//            }
//
//            //List<Map<String, Object>> rows = reader.readAll();//读取的文件行
//            List<Map<String, Object>> fails =new ArrayList<>();//失败行
//            long successNum = 0;//成功数量
//            BigDecimal entrustMoneyTotal = BigDecimal.ZERO;
//            List<TeamImportCaseSonTaskResp> resList= synContainer.getResList();
//            for (TeamImportCaseSonTaskResp res: resList) {
//                //System.out.println(res.toString());
//                successNum+=res.getSuccessNum();
//                entrustMoneyTotal=entrustMoneyTotal.add(res.getEntrustMoneyTotal());
//                fails.addAll(res.getFailsRows());
//            }
//            log.info("总数:{} ",rowTotalSize);
//            log.info("总成功数:{} ",successNum);
//            log.info("总失败数:{} ",fails.size());
//            log.info("执行任务耗时:{} ms ",timer.intervalMs("workTask"));
//            String failFileUrl = "";
//
//            if (rowTotalSize==successNum) { //全部成功
//                istart = ImportStartEnum.SUCCESS;
//            } else {
//                if (fails.size() == rowTotalSize) { //全部失败
//                    istart = ImportStartEnum.FAIL;
//                } else { //部分失败
//                    istart = ImportStartEnum.PARTIAL_SUCCESS;
//                }
//                failFileUrl= uploadFailFile(fails);
//            }
//            long caseNum= successNum; //rowCount-fails.size();//案件数量
//
//            TeamManage assetManage=  assetManageService.selectById(asset.getId());
//            long oldCaseNum= assetManage.getCaseNumber()==null? 0L :assetManage.getCaseNumber();
//            BigDecimal oldEntrustMoneyTotal=  assetManage.getEntrustMoneyTotal()==null?BigDecimal.ZERO:assetManage.getEntrustMoneyTotal();
//
//            caseNum=oldCaseNum+caseNum;
//            entrustMoneyTotal=oldEntrustMoneyTotal.add(entrustMoneyTotal);
//            TeamManage am = new TeamManage();
//            am.setId(asset.getId());
//            am.setImportStart(istart.getCode());
//            am.setCaseNumber(caseNum);
//            am.setEntrustMoneyTotal(entrustMoneyTotal);
//            assetManageService.updateById(am);
//
//            //更新导入日志
//            importLog.setSuccessNumber(successNum);
//            importLog.setFailFileUrl(failFileUrl);
//            //importLog.setProductType(asset.getProtocol());
//            FileDownloadUtils.deletedTempFile(tempFile);
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("导入案件失败:",e);
//            log.info("导入案件异常:{}",e.getMessage());
//            istart = ImportStartEnum.FAIL;
//        }finally {
//            importLog.setImportStart(istart.getCode());
//            importLogService.updateById(importLog,loginUser);
//        }
    }


//    /**
//     * 上传保存失败的案件
//     * @param fails
//     * @return
//     * @throws Exception
//     */
//    private String uploadFailFile(List<Map<String,Object>> fails) {
//        try {
//            MultipartFile multipartFile= FileDownloadUtils.generateMultipartFile(fails);
//            R<SysFile> rest = fileService.upload(multipartFile);
//            if (rest.getCode() == R.SUCCESS) {
//                return rest.getData().getUrl();
//            }
//        } catch (Exception e) {
//            log.error(JSONUtil.toJsonStr(fails));
//            log.error("保存失败文件错误",e);
//        }
//        return "";
//    }


    @Override
    public void cancelTask() {

    }

    @Override
    public int getProgress() {
        return 0;
    }
}
