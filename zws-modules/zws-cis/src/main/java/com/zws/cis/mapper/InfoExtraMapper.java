package com.zws.cis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zws.system.api.domain.InfoExtra;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface InfoExtraMapper extends BaseMapper<InfoExtra> {
    int deleteByPrimaryKey(Long id);

    int insert(InfoExtra record);

    /**
     * 批量插入
     * @param records
     * @return
     */
    int batchInsert(@Param("records") List<InfoExtra> records);

    int insertSelective(InfoExtra record);

    InfoExtra selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InfoExtra record);
    int updateByPrimaryKey(InfoExtra record);
    /**
     * 案件ID以及字段key获取案件对应附加信息
     *
     * @param map
     * @return
     */
    List<InfoExtra> selectByCaseIdKey(Map<String,Object> map);


    List<InfoExtra> selectList(InfoExtra record);
}
