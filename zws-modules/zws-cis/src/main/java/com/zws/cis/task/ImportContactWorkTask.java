package com.zws.cis.task;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.zws.cis.controller.request.TeamImportParam;
import com.zws.cis.domain.TeamImportLog;
import com.zws.cis.enums.ImportStartEnum;
import com.zws.cis.service.IInfoContactService;
import com.zws.cis.service.ILibraryService;
import com.zws.cis.service.TeamImportLogService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.sms.InfoContact;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.threadpool.WorkTask;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.SpringUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.domain.SysFile;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;

/**
 * 导入联系人工作线程
 *
 * <AUTHOR>
 * @date ：Created in 2022/3/19 9:08
 */
@Slf4j
public class ImportContactWorkTask implements WorkTask {

    private ILibraryService libraryService = SpringUtils.getBean(ILibraryService.class);//
    private IInfoContactService infoContactService = SpringUtils.getBean(IInfoContactService.class);//
    private TeamImportLogService importLogService = SpringUtils.getBean(TeamImportLogService.class);//
    private RemoteFileService fileService = SpringUtils.getBean(RemoteFileService.class);


    private TeamImportParam param;//导入参数
    private LoginUser loginUser;//登录用户
    private TeamImportLog importLog;//导入日志
    private Set<Long> productIds = new HashSet<>();//多个产品时的产品ID
    private Set<Long> ownerIds = new HashSet<>();//多个资产方时的资产方ID集合

    private Long teamId;

    public ImportContactWorkTask(TeamImportLog importLog, TeamImportParam param, LoginUser loginUser) {
        this.param = param;
        this.importLog = importLog;
        this.loginUser = loginUser;
        teamId = SecurityUtils.getTeamId(loginUser);
    }

    @Override
    public void runTask() {
        TeamImportLog entity = new TeamImportLog();
        entity.setId(importLog.getId());

        ImportStartEnum start = ImportStartEnum.ING;
        long successNumber = 0L;
        try {
            File tempFile = FileDownloadUtils.downloadTempFile(param.getFileUrl());
            ExcelReader reader = ExcelUtil.getReader(tempFile);
            List<Map<String, Object>> list = reader.readAll();
            ;
            List<Map<String, Object>> fails = new ArrayList<>();//导入失败的联系人
            for (Map<String, Object> row : list) {
                if (row.size() == 6) {//竖向模板
                    if (!vertical(row)) {
                        fails.add(row);
                    } else {
                        successNumber++;
                    }
                } else {//横向模板
                    if (!horizontal(row, loginUser)) {
                        fails.add(row);
                    } else {
                        successNumber++;
                    }
                }
            }
            if (fails.size() == 0) {
                start = ImportStartEnum.SUCCESS;
            } else {
                if (list.size() == fails.size()) {
                    start = ImportStartEnum.FAIL;
                } else {
                    start = ImportStartEnum.PARTIAL_SUCCESS;
                }
                String failFileUrl = uploadFailFile(fails);
                entity.setFailFileUrl(failFileUrl);//失败文件
            }
            FileDownloadUtils.deletedTempFile(tempFile);
        } catch (Exception e) {
            log.error("导入联系人异常", e);
            start = ImportStartEnum.FAIL;
        } finally {
            if (StringUtils.isEmpty(this.param.getImportBatchNum())) {
                entity.setProductIds(productIds.toString());
                entity.setOwnerIds(ownerIds.toString());
            }
            entity.setImportStart(start.getCode());
            entity.setSuccessNumber(successNumber);
            importLogService.updateById(entity, loginUser);
        }
    }

    /**
     * 竖向模板
     */
    private boolean vertical(Map<String, Object> row) {
//        String[] template = DownloadConstant.contactTemplateVertical;
//        //List<InfoContact> success=new ArrayList<>();//成功的联系人
//
//        try {
//            String caseIdStr = StrUtil.toString(row.get(template[0]));//案件ID
//            String clientName = StrUtil.toString(row.get(template[1]));//客户姓名
//            String clientIdcard = StrUtil.toString(row.get(template[2]));//客户身份证
//            if (!checkCase(caseIdStr, clientName, clientIdcard)) {
//                throw new GlobalException("客户姓名/身份证 错误");
//            }
//
//            Long caseId = Long.parseLong(caseIdStr);
//            String contactName = StrUtil.toString(row.get(template[3]));//联系人
//            String relation = StrUtil.toString(row.get(template[4]));//关系
//            String contactPhone = StrUtil.toString(row.get(template[5]));//联系人电话
//
//            if (StringUtils.isEmpty(contactName)) {
//                throw new GlobalException("联系人 为空");
//            }
//            if (StringUtils.isEmpty(relation)) {
//                throw new GlobalException("关系 为空");
//            }
//            if (StringUtils.isEmpty(contactPhone)) {
//                throw new GlobalException("联系人电话 为空");
//            }
//
//            InfoContact infoContact = new InfoContact();
//            infoContact.setCaseId(caseId);
//            infoContact.setContactName(contactName);
//            infoContact.setContactPhone(contactPhone);
//            infoContact.setContactRelation(relation);
//            infoContact.setDelFlag(BaseConstant.DelFlag_Being);
//            infoContact.setCreateBy(SecurityUtils.getUsername(loginUser));
//            infoContact.setCreateTime(DateUtils.getNowDate());
//            infoContact.setTeamId(teamId);
//            infoContactService.insert(infoContact, loginUser);
//            //success.add(infoContact);
//            return true;
//        } catch (Exception e) {
//            log.error("导入联系人失败", e);
//            row.put("失败原因", e.getMessage());
//            return false;
//        }
        return false;
    }

    /**
     * 横向模板
     *
     * @param row
     */
    private boolean horizontal(Map<String, Object> row, LoginUser loginUser) {
//        String[] template = DownloadConstant.contactTemplateHorizontal;
//        try {
//            String caseIdStr = StrUtil.toString(row.get(template[0]));//案件ID
//            String clientName = StrUtil.toString(row.get(template[1]));//客户姓名
//            String clientIdcard = StrUtil.toString(row.get(template[2]));//客户身份证
//            if (!checkCase(caseIdStr, clientName, clientIdcard)) {
//                throw new GlobalException("客户姓名/身份证 错误");
//            }
//
//            Long caseId = Long.parseLong(caseIdStr);
//
//            List<InfoContact> contacts = new ArrayList<>();
//            getRowContact(3, row, caseId, contacts);
//            getRowContact(6, row, caseId, contacts);
//            getRowContact(9, row, caseId, contacts);
//            getRowContact(12, row, caseId, contacts);
//            getRowContact(15, row, caseId, contacts);
//            getRowContact(18, row, caseId, contacts);
//            getRowContact(21, row, caseId, contacts);
//            getRowContact(24, row, caseId, contacts);
//            getRowContact(27, row, caseId, contacts);
//            getRowContact(30, row, caseId, contacts);
//            infoContactService.batchInsert(contacts);
//            return true;
//        } catch (Exception e) {
//            log.error("导入联系人失败", e);
//            row.put("失败原因", e.getMessage());
//            return false;
//        }
        return false;
    }

    /**
     * 查询判断案件
     *
     * @param caseIdStr    案件ID 的字符串
     * @param clientName   客户姓名
     * @param clientIdcard 客户身份证
     * @return
     */
    private boolean checkCase(String caseIdStr, String clientName, String clientIdcard) {

//        if (StringUtils.isEmpty(caseIdStr)) {
//            throw new GlobalException("案件ID 为空");
//        }
//        if (!StringUtils.isNumeric(caseIdStr)) {
//            throw new GlobalException("案件ID 错误");
//        }
//        Long caseId = Long.parseLong(caseIdStr);
//        if (StringUtils.isEmpty(clientName)) {
//            throw new GlobalException("客户姓名 为空");
//        }
//        if (StringUtils.isEmpty(clientIdcard)) {
//            throw new GlobalException("客户身份证 为空");
//        }
//        clientName = FieldEncryptUtil.encrypt(clientName);
//        clientIdcard = FieldEncryptUtil.encrypt(clientIdcard);
//        Library library = libraryService.selectById(caseId);
//        if (library == null || !ObjectUtil.equals(library.getTeamId(), teamId)) {
//            throw new GlobalException("案件不存在");
//        }
//
//        //判断是不是指定导入批号
//        if (!StringUtils.isEmpty(this.param.getImportBatchNum())) {
//            //判断案件是否是指定的批次号
//            if (library == null || !StrUtil.equals(this.param.getImportBatchNum(), library.getBatchNo())) {
//                throw new GlobalException("导入批号错误，不是指定的导入批次案件");
//            }
//        } else {
//            productIds.add(library.getProductId());
//            ownerIds.add(library.getEntrustingPartyId());
//        }
//
//        if (StrUtil.equals(library.getClientName(), clientName) &&
//                StrUtil.equals(library.getClientIdNum(), clientIdcard)) {
//            //案件债务人的姓名、身份证 都符合
//            return true;
//        }
        return false;
    }

    /**
     * 生成联系人对象
     *
     * @param caseId       案件ID
     * @param contactName  联系人名称
     * @param relation     关系
     * @param contactPhone 联系人电话
     * @return
     */
    private InfoContact createContact(Long caseId, String contactName, String relation, String contactPhone) {
//        if (StringUtils.isNotEmpty(contactName) &&
//                StringUtils.isNotEmpty(relation) &&
//                StringUtils.isNotEmpty(contactPhone)
//        ) {//联系人、关系、电话都不能为空
//            InfoContact infoContact = new InfoContact();
//            infoContact.setCaseId(caseId);
//            infoContact.setContactName(contactName);
//            infoContact.setContactPhone(contactPhone);
//            infoContact.setContactRelation(relation);
//            infoContact.setDelFlag(BaseConstant.DelFlag_Being);
//            infoContact.setCreateBy(SecurityUtils.getUsername(loginUser));
//            infoContact.setCreateTime(DateUtils.getNowDate());
//            infoContact.setTeamId(teamId);
//            return infoContact;
//        }
        return null;
    }


    /**
     * 上传保存失败的案件
     *
     * @param fails
     * @return
     * @throws Exception
     */
    private String uploadFailFile(List<Map<String, Object>> fails) {
//        try {
//            MultipartFile multipartFile = FileDownloadUtils.generateMultipartFile(fails);
//            R<SysFile> rest = fileService.upload(multipartFile);
//            if (rest.getCode() == R.SUCCESS) {
//                return rest.getData().getUrl();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error(JSONUtil.toJsonStr(fails));
//            log.error("保存失败文件错误", e);
//        }
        return "";
    }


    private void getRowContact(int index, Map<String, Object> row, long caseId, List<InfoContact> contacts) {

//        String[] template = DownloadConstant.contactTemplateHorizontal;
//        if (index + 2 > template.length) {
//            return;
//        }
//        ;
//        String contactName = StrUtil.toString(row.get(template[index]));//联系人1
//        String relation = StrUtil.toString(row.get(template[index + 1]));//关系1
//        String contactPhone = StrUtil.toString(row.get(template[index + 2]));//联系人电话1
//        InfoContact contact = createContact(caseId, contactName, relation, contactPhone);
//        if (contact != null) {
//            contacts.add(contact);
//        }
    }


    @Override
    public void cancelTask() {

    }

    @Override
    public int getProgress() {
        return 0;
    }


}
