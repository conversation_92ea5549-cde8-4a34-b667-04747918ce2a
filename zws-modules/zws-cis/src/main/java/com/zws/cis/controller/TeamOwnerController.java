package com.zws.cis.controller;

import com.zws.cis.domain.TeamOwner;
import com.zws.cis.pojo.TreeNodePojo;
import com.zws.cis.service.TeamOwnerService;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 资产方管理-委托方
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:08
 */
@CrossOrigin
@RestController
@RequestMapping("/teamOwner")
public class TeamOwnerController extends BaseController {

    @Autowired
    private TeamOwnerService teamOwnerService;


    /**
     * 查询列表
     *
     * @param entity
     * @return
     */
    @GetMapping("/list")
    public AjaxResult selectList(TeamOwner entity) {
        if (ObjectUtils.isEmpty(entity)) entity = new TeamOwner();
        entity.setTeamId(SecurityUtils.getTeamId().intValue());
        List<TeamOwner> list = teamOwnerService.selectList(entity);
        return AjaxResult.success(list);
    }


    /**
     * 创建转让方
     *
     * @param entity
     * @return
     */
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody TeamOwner entity) {
        long id = teamOwnerService.insert(entity);
        return AjaxResult.success(id);
    }


    /**
     * 编辑转让方
     *
     * @param entity
     * @return
     */
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody TeamOwner entity) {
        teamOwnerService.update(entity);
        return AjaxResult.success();
    }


    /**
     * 删除转让方
     *
     * @param entity
     * @return
     */
    @PostMapping("/deleted")
    public AjaxResult remove(@RequestBody TeamOwner entity) {
        if (!teamOwnerService.checkCanDeleted(entity.getId())) {
            return AjaxResult.error("该资产方还有批次存在，无法进行批量删除，请先将批次全部删除再进行删除操作！");
        }
        teamOwnerService.deleted(entity.getId());
        return AjaxResult.success();
    }

//    /**
//     * 可关键词搜索的 查询字典表转让方
//     *
//     * @param search
//     * @return
//     */
//    @GetMapping("/getDictTransferor")
//    public AjaxResult getDictTransferor(String search) {
//        List<StatePojo> statePojos = new ArrayList<>();
//        List<SysDictData> datas = dictService.dictType(DictTypeConstant.TRANSFEROR_DICT_TYPE, search);
//        for (SysDictData temp : datas) {
//            statePojos.add(new StatePojo(temp.getDictValue(), temp.getDictLabel()));
//        }
//        return AjaxResult.success(statePojos);
//    }

    /**
     * 返回树结构
     *
     * @return
     */
    @GetMapping("/getTree")
    public AjaxResult getTree() {
        TeamOwner teamOwner = new TeamOwner();
        teamOwner.setTeamId(SecurityUtils.getTeamId().intValue());
        List<TreeNodePojo> treeNodes = teamOwnerService.getTree(teamOwner);
        return AjaxResult.success(treeNodes);
    }

//    /**
//     * 获取资产端的社会统一信用代码
//     *
//     * @param id
//     * @return
//     */
//    @PostMapping("/getUnifiedCode")
//    public AjaxResult getUnifiedCode(Long id) {
//        Owner owner = ownerService.getById(id);
//        return AjaxResult.success("操作成功", owner.getUnifiedCode());
//    }

    /**
     * 判断是否可删除
     *
     * @return true 可以删除，false 不能删除
     */
    @PostMapping("/checkCanDeleted")
    public AjaxResult checkCanDeleted(@RequestBody TeamOwner entity) {
        if (teamOwnerService.checkCanDeleted(entity.getId())) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error("该资产方还有批次存在，无法进行批量删除，请先将批次全部删除再进行删除操作！");
        }
    }
}
