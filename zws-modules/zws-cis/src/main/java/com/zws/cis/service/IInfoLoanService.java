package com.zws.cis.service;

import com.zws.system.api.domain.InfoLoan;

/**
 * 案件信息-贷款信息
 * <AUTHOR>
 * @date ：2024年8月7日19:45:22
 */
public interface IInfoLoanService {

    /**
     * 插入保存数据
     * @param entity
     * @return
     */
    long insert(InfoLoan entity) throws Exception;


    /**
     * 根据更改
     * @param entity
     */
    void updateById(InfoLoan entity) throws Exception;

    /**
     * 根据案件ID更新
     * @param entity
     */
    void updateByCaseId(InfoLoan entity);


    /**
     * 案件ID获取案件贷款信息
     * @param caseId
     * @return
     */
    InfoLoan selectByCaseId(Long caseId);

}
