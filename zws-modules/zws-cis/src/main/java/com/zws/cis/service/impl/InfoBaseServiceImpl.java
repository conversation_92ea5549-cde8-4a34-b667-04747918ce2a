package com.zws.cis.service.impl;

import com.zws.cis.mapper.InfoBaseMapper;
import com.zws.cis.service.IInfoBaseService;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.InfoBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/2/16 9:23
 */
@Service
@Primary
public class InfoBaseServiceImpl implements IInfoBaseService {

    @Autowired
    private InfoBaseMapper mapper;

    @Override
    public long insert(InfoBase entity) throws Exception {
        entity.check();
        entity.setCreateBy(SecurityUtils.getUsername());
        entity.setCreateTime(DateUtils.getNowDate());
        return mapper.insert(entity);
    }

    @Override
    public void updateById(InfoBase entity) throws Exception {
        entity.check();
        entity.setUpdateBy(SecurityUtils.getUsername());
        entity.setUpdateTime(DateUtils.getNowDate());
        mapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateByCaseId(InfoBase entity) {
        //entity.check();
        if (StringUtils.isNotEmpty(entity.getClientName()) ){
            String clientName=entity.getClientName();
            entity.setClientName(FieldEncryptUtil.encrypt(clientName));
            entity.setClientNameEnc(FieldEncryptUtil.nameLike(clientName));
        }
        if (StringUtils.isNotEmpty(entity.getClientPhone()) ){
            String clientPhone=entity.getClientPhone();
            entity.setClientPhone(FieldEncryptUtil.encrypt(clientPhone));
            entity.setClientPhoneEnc(FieldEncryptUtil.phoneLike(clientPhone));
        }
        if (StringUtils.isNotEmpty(entity.getClientIdNum()) ){
            String clientIdNum=entity.getClientIdNum();
            entity.setClientIdNum(FieldEncryptUtil.encrypt(clientIdNum));
            entity.setClientIdNumEnc(FieldEncryptUtil.idLike(clientIdNum));
        }
        if (StringUtils.isNotEmpty(entity.getBankCardNumber()) ){
            String bankCardNumber=entity.getBankCardNumber();
            entity.setBankCardNumber(FieldEncryptUtil.encrypt(bankCardNumber));
        }
        if (StringUtils.isNotEmpty(entity.getClientCensusRegister()) ){
            String clientCensusRegister=entity.getClientCensusRegister();
            entity.setClientCensusRegister(FieldEncryptUtil.encrypt(clientCensusRegister));
            entity.setClientCensusRegisterEnc(FieldEncryptUtil.censusRegisterLike(clientCensusRegister));
        }
        //QQ-加密
        String qqEncStr = FieldEncryptUtil.encrypt(entity.getQq());
        entity.setQq(qqEncStr);
        //微信-加密
        String wxEncStr = FieldEncryptUtil.encrypt(entity.getWeixin());
        entity.setWeixin(wxEncStr);
        //邮箱-加密
        String mailboxEncStr = FieldEncryptUtil.encrypt(entity.getMailbox());
        entity.setMailbox(mailboxEncStr);
        //工作单位-加密
        String placeOfWorkEncStr = FieldEncryptUtil.encrypt(entity.getPlaceOfWork());
        entity.setPlaceOfWork(placeOfWorkEncStr);
        //单位地址-加密
        String workingAddressEncStr = FieldEncryptUtil.encrypt(entity.getWorkingAddress());
        entity.setWorkingAddress(workingAddressEncStr);
        //居住地址-加密
        String residentialAddressEncStr = FieldEncryptUtil.encrypt(entity.getResidentialAddress());
        entity.setResidentialAddress(residentialAddressEncStr);
        //家庭地址-加密
        String homeAddressEncStr = FieldEncryptUtil.encrypt(entity.getHomeAddress());
        entity.setHomeAddress(homeAddressEncStr);

        //担保人-加密
        String securityNameEncStr = FieldEncryptUtil.encrypt(entity.getSecurityName());
        entity.setSecurityName(securityNameEncStr);
        //担保人分词-用于模糊查找
        String securityNameLike = FieldEncryptUtil.nameLike(entity.getSecurityName());
        entity.setSecurityNameEnc(securityNameLike);
        //担保人证件号-加密
        String securityIdNumEncStr = FieldEncryptUtil.encrypt(entity.getSecurityIdNum());
        entity.setSecurityIdNum(securityIdNumEncStr);
        //担保人证件号-用于模糊查找
        String securityIdNumLike = FieldEncryptUtil.idLike(entity.getSecurityIdNum());
        entity.setSecurityIdNumEnc(securityIdNumLike);
        //担保人电话-加密
        String securityPhoneEncStr = FieldEncryptUtil.encrypt(entity.getSecurityPhone());
        entity.setSecurityPhone(securityPhoneEncStr);
        //担保人电话-用于模糊查找
        String securityPhoneLike = FieldEncryptUtil.phoneLike(entity.getSecurityPhone());
        entity.setSecurityPhoneEnc(securityPhoneLike);
        //家庭电话-加密
        String homePhoneEncStr = FieldEncryptUtil.encrypt(entity.getHomePhone());
        entity.setHomePhone(homePhoneEncStr);
        //单位电话-加密
        String unitTelephoneEncStr = FieldEncryptUtil.encrypt(entity.getUnitTelephone());
        entity.setUnitTelephone(unitTelephoneEncStr);


        entity.setUpdateBy(SecurityUtils.getUsername());
        entity.setUpdateTime(DateUtils.getNowDate());
        mapper.updateByCaseIdSelective(entity);
    }

    @Override
    public InfoBase selectByCaseId(Long caseId) {
        InfoBase entity=new InfoBase();
        entity.setCaseId(caseId);
        List<InfoBase> list=  mapper.selectList(entity);
        return list.size()>0?list.get(0):null;
    }


}
