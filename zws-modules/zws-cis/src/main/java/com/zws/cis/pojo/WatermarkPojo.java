package com.zws.cis.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * 水印字段内容表
 */
@Data
public class WatermarkPojo implements Serializable {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 团队id
     */
    private Integer createId;

    /**
     * 水印设置字段一
     */
    private String watermarkOne;

    /**
     * 水印设置字段二
     */
    private String watermarkTwo;

    /**
     * 水印设置字段三
     */
    private String watermarkThree;

    /**
     * 水印设置字段四
     */
    private String watermarkFour;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCreateId() {
        return createId;
    }

    public void setCreateId(Integer createId) {
        this.createId = createId;
    }

    public String getWatermarkOne() {
        return watermarkOne;
    }

    public void setWatermarkOne(String watermarkOne) {
        this.watermarkOne = watermarkOne;
    }

    public String getWatermarkTwo() {
        return watermarkTwo;
    }

    public void setWatermarkTwo(String watermarkTwo) {
        this.watermarkTwo = watermarkTwo;
    }

    public String getWatermarkThree() {
        return watermarkThree;
    }

    public void setWatermarkThree(String watermarkThree) {
        this.watermarkThree = watermarkThree;
    }

    public String getWatermarkFour() {
        return watermarkFour;
    }

    public void setWatermarkFour(String watermarkFour) {
        this.watermarkFour = watermarkFour;
    }
}
