package com.zws.cis.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 导出日志
 *
 * <AUTHOR>
 * @date 2024年1月23日16:33:18
 */
@Data
public class TeamExportLog extends BaseEntity {

    private Long id;
    /**
     * 导出类别
     */
    private String exportClass;
    /**
     * 导出类型，中文
     */
    private List<String> exportTypeList;

    /**
     * 导出类型，中文
     */
    private String exportType;
    /**
     * 文件url
     */
    private String fileUrl;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 导出状态
     * 0-导出中，1-导出成功，2-导出失败
     */
    private String exportStart;
    /**
     * 失败原因
     */
    private String failMsg;
    /**
     * 团队ID
     */
    private Long teamId;
    /**
     * 操作员类型
     * 0-主账号;1-员工账号;2-资产端账号
     */
    private Integer operationType;


    private String delFlag;

    private String createBy;

    private Long createById;

    private String updateBy;

    private Long updateById;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 查询条件
     * 导出时间-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime1;
    /**
     * 查询条件
     * 导出时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime2;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getExportType() {
        return exportType;
    }

    public void setExportType(String exportType) {
        this.exportType = exportType == null ? null : exportType.trim();
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl == null ? null : fileUrl.trim();
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    public String getExportStart() {
        return exportStart;
    }

    public void setExportStart(String exportStart) {
        this.exportStart = exportStart == null ? null : exportStart.trim();
    }

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg == null ? null : failMsg.trim();
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    public Long getCreateById() {
        return createById;
    }

    public void setCreateById(Long createById) {
        this.createById = createById;
    }

    @Override
    public String getUpdateBy() {
        return updateBy;
    }

    @Override
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    public Long getUpdateById() {
        return updateById;
    }

    public void setUpdateById(Long updateById) {
        this.updateById = updateById;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
