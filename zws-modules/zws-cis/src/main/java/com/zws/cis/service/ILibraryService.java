package com.zws.cis.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.zws.cis.pojo.CaseBaseInfo;

import java.util.List;

/**
 * 资产案件库 service
 *
 * <AUTHOR>
 * @date ：Created in 2022/2/15 14:41
 */
public interface ILibraryService  {

//    /**
//     * 插入数据返回id
//     *
//     * @param entity
//     * @return
//     */
//    long insert(Library entity);

//    /**
//     * 更新
//     *
//     * @param entity
//     */
//    void update(Library entity);

//    /**
//     * 查询案件ID
//     *
//     * @param caseId
//     * @param teamId
//     * @return
//     */
//    List<Library> selectListByCaseId(Long caseId, Long teamId);

//    /**
//     * 根据id查询
//     *
//     * @param caseId
//     * @return
//     */
//    Library selectById(Long caseId);


//    /**
//     * 跟进资产管理id删除案件
//     *
//     * @param assetManageId
//     */
//    void deleteByAssetManageId(Long assetManageId, Long teamId);

//    /**
//     * 批号查询案件
//     *
//     * @param batchNo
//     * @return
//     */
//    List<CaseBaseInfo> selectCaseBaseInfoList(String batchNo, Integer teamId);

//    /**
//     * 更新案件分配状态为已分配
//     *
//     * @param caseId
//     */
//    void updateAllocateCaseState(Long caseId);

    void updateAllocateCaseStateByList(List<Long> caseId);
}
