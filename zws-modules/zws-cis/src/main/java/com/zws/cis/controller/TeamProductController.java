package com.zws.cis.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zws.cis.agservice.AgTeamProductService;
import com.zws.cis.domain.TeamProduct;
import com.zws.cis.domain.TeamTemplates;
import com.zws.cis.pojo.TeamProductFormPojo;
import com.zws.cis.service.TeamProductService;
import com.zws.cis.service.TeamTemplatesService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.log.annotation.Log;
import com.zws.common.log.enums.BusinessType;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 资产方管理-产品
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:08
 */
@CrossOrigin
@RestController
@RequestMapping("/teamProduct")
public class TeamProductController extends BaseController {

    @Autowired
    private TeamProductService productService;
    @Autowired
    private AgTeamProductService agTeamProductService;
    @Autowired
    private TeamTemplatesService teamTemplatesService;


//    /**
//     * 查询列表
//     *
//     * @param pojo
//     * @return
//     */
//    @GetMapping("/list")
//    public TableDataInfo selectList(TeamProductFormPojo pojo) {
//        startPage();
//        TeamProduct product = new TeamProduct();
//        product.setId(pojo.getId());
//        product.setName(pojo.getName());
//        product.setOwnerId(pojo.getOwnerId());
//        product.setTeamId(SecurityUtils.getTeamId().intValue());
//        List<TeamProduct> list = productService.selectList(product);
//        return getDataTable(list);
//    }
//
//    /**
//     * 查询
//     *
//     * @param body
//     * @return
//     */
//    @PostMapping("/findProduct")
//    public AjaxResult info(@RequestBody String body) {
//        JSONObject jsonObject = JSONUtil.parseObj(body);
//        Long id = jsonObject.getLong("id");
//        TeamProduct product = productService.selectByPrimaryKey(id);
//        if (product == null) {
//            return AjaxResult.error("产品为空,请重新选择");
//        }
//
//        ProductTemplate template = null;
//        if (ObjectUtils.isEmpty(product.getTemplate())) {
//            template = productService.getInitTemplate();
//        } else {
//            String templ = product.getTemplate();
//            ProductTemplate temp = JSONUtil.toBean(templ, ProductTemplate.class);
//            template = ProductTemplateUtil.examine(temp);
//        }
//
//        TeamProductFormPojo pojo = new TeamProductFormPojo();
//        pojo.setId(product.getId());
//        pojo.setName(product.getName());
//        pojo.setTemplate(template);
//        return AjaxResult.success(pojo);
//    }
//
//    /**
//     * 创建产品
//     *
//     * @param pojo
//     * @return
//     */
//    @Log(title = "资产方管理-产品（创建产品）", businessType = BusinessType.INSERT)
//    @PostMapping("/add")
//    public AjaxResult add(@Validated @RequestBody TeamProductFormPojo pojo) {
//        ProductTemplate template = pojo.getTemplate();
//        if (template == null) {
//            return AjaxResult.error("请设置产品模板");
//        }
//        if (pojo.getOwnerId() == null) {
//            return AjaxResult.error("转让方不能为空");
//        }
//        template.check();
//
//        TeamProduct product = new TeamProduct();
//        product.setOwnerId(pojo.getOwnerId());
//        product.setName(pojo.getName());
//        product.setShortName(pojo.getProductCode());
//        product.setTemplate(JSONUtil.toJsonStr(template));
//        product.setTeamId(SecurityUtils.getTeamId().intValue());
//        long id = productService.insert(product);
//        return AjaxResult.success(id);
//    }
//
//    /**
//     * 编辑
//     *
//     * @param pojo
//     * @return
//     */
//    @PostMapping("/edit")
//    public AjaxResult edit(@Validated @RequestBody TeamProductFormPojo pojo) {
//        ProductTemplate template = pojo.getTemplate();
//        if (template == null) {
//            return AjaxResult.error("请设置产品模板");
//        }
//        template.check();
//        TeamProduct product = new TeamProduct();
//        product.setId(pojo.getId());
//        product.setName(pojo.getName());
//        product.setTemplate(JSONUtil.toJsonStr(template));
//        productService.update(product);
//        return AjaxResult.success();
//    }
//
//    /**
//     * 删除
//     *
//     * @param pojo
//     * @return
//     */
//    @PostMapping("/deleted")
//    public AjaxResult remove(@RequestBody TeamProductFormPojo pojo) {
//        productService.deleted(pojo.getId());
//        return AjaxResult.success();
//    }
//
//    /**
//     * 检查产品名字是否重复
//     *
//     * @param pojo
//     * @return
//     */
//    @GetMapping("/checkName")
//    public AjaxResult checkName(TeamProductFormPojo pojo) {
//        boolean bool = productService.checkName(pojo.getName());
//        return AjaxResult.success(bool);
//    }
//
////    /**
////     * 关键字搜索字典表中的产品类型
////     *
////     * @param search
////     * @return
////     */
////    @GetMapping("/getDictProductType")
////    public AjaxResult getDictProductType(String search) {
////        List<SysDictData> dictDatas = dictService.dictType(DictTypeConstant.PRODUCT_TYPE, search);
////        List<StatePojo> statePojos = new ArrayList<>();
////        for (SysDictData temp : dictDatas) {
////            statePojos.add(new StatePojo(temp.getDictValue(), temp.getDictLabel()));
////        }
////        return AjaxResult.success(statePojos);
////    }
//
//
//    /**
//     * 修改状态
//     *
//     * @return
//     */
//    @PostMapping("/updateState")
//    public AjaxResult updateState(@RequestBody String body) {
//        JSONObject jsonObject = JSONUtil.parseObj(body);
//        Long id = jsonObject.getLong("id");
//        String state = jsonObject.getStr("state");
//        if (ObjectUtils.isEmpty(id) || ObjectUtils.isEmpty(state)) {
//            throw new GlobalException("参数不可为空");
//        }
//        TeamProduct product = new TeamProduct();
//        product.setId(id);
//        product.setState(state.equals("0") ? BaseConstant.State_Use : BaseConstant.State_Forbidden);
////        productService.update(product);
//        agTeamProductService.changeProducts(product);
//        return AjaxResult.success();
//    }
//
//    /**
//     * 判断是否可删除
//     *
//     * @return true 可以删除，false 不能删除
//     */
//    @PostMapping("/checkCanDeleted")
//    public AjaxResult checkCanDeleted(@RequestBody TeamProductFormPojo entity) {
//        if (productService.checkCanDeleted(entity.getId())) {
//            return AjaxResult.success();
//        } else {
//            return AjaxResult.error("该产品还有批次存在，无法进行批量删除，请先将批次全部删除再进行删除操作！");
//        }
//    }
//
//
//    /**
//     * 下载模板
//     *
//     * @param id
//     */
//    @PostMapping("/downloadTemplate")
//    public void downloadTemplate(HttpServletResponse response, Long id) throws IOException {
//        //long id = pojo.getId();
//        productService.downloadTemplate(response, id);
//    }
//
//    /**
//     * 获取创建产品的的初始化模板
//     *
//     * @return
//     */
//    @GetMapping("/getInitTemplate")
//    public AjaxResult getInitTemplate(Long currencyTemplateId) {
//        ProductTemplate template;
//        if (currencyTemplateId == null) {
//            template = productService.getInitTemplate();
//        } else {
//            //查找通用模板
//            TeamTemplates ct = teamTemplatesService.selectById(currencyTemplateId);
//            if (ct == null) {
//                return AjaxResult.error("未找到指定ID的通用模板");
//            }
//            if (ObjectUtils.isEmpty(ct.getTemplate())) {
//                throw new GlobalException("系统模板有变动，请前往常用模板设置后使用！");
//            }
//            template = JSONUtil.toBean(ct.getTemplate(), ProductTemplate.class);
//        }
//        return AjaxResult.success(template);
//    }
}
