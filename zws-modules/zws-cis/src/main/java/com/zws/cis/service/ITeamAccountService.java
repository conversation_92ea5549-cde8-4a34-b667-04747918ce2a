package com.zws.cis.service;

import com.zws.cis.domain.TeamAccount;

import java.util.List;

/**
 * 机构账户管理--（service层）
 *
 * @author: 马博新
 * @date ：Created in 2024/08/09 10:29
 */
public interface ITeamAccountService {

    /**
     * 查询账户管理列表
     *
     * @param account
     * @return
     */
    List<TeamAccount> selectByPrimaryKey(TeamAccount account);

    /**
     * 查询账户管理已开启列表
     * @param teamId
     * @return
     */
    List<TeamAccount> selectAccountById(Integer teamId);

    /**
     * 新增账户
     *
     * @param account
     * @return
     */
    int insert(TeamAccount account);

    /**
     * 根据id修改账户信息
     *
     * @param account
     * @return
     */
    int update(TeamAccount account);

    /**
     * 根据id删除账户信息
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 不要调用这个接口！！！！！！！！！
     * @return
     */
    int updateTebleName();
}
