package com.zws.cis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zws.cis.mapper.InfoBaseMapper;
import com.zws.cis.mapper.InfoExtraMapper;
import com.zws.cis.service.IInfoBaseMpService;
import com.zws.cis.service.IInfoExtraMpService;
import com.zws.system.api.domain.InfoBase;
import com.zws.system.api.domain.InfoExtra;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date ：Created in 2022/2/15 15:31
 */
@Slf4j
@Service
public class InfoBaseMpServiceImpl extends ServiceImpl<InfoBaseMapper, InfoBase> implements IInfoBaseMpService {


}
