package com.zws.cis.task;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.zws.cis.controller.request.TeamImportParam;
import com.zws.cis.domain.TeamImportLog;
import com.zws.cis.domain.UrgeRecord;
import com.zws.cis.enums.ImportStartEnum;
import com.zws.cis.service.ILibraryService;
import com.zws.cis.service.IUrgeRecordService;
import com.zws.cis.service.TeamImportLogService;
import com.zws.common.core.domain.R;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.threadpool.WorkTask;
import com.zws.common.core.utils.ConvertUtils;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.SpringUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.domain.Library;
import com.zws.system.api.domain.SysFile;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;

/**
 * 导入催记工作线程
 *
 * <AUTHOR>
 * @date ：2024年8月8日18:00:38
 */
@Slf4j
public class ImportUrgeRecordWorkTask implements WorkTask {

    private ILibraryService libraryService = SpringUtils.getBean(ILibraryService.class);//
    private TeamImportLogService importLogService = SpringUtils.getBean(TeamImportLogService.class);//
    private IUrgeRecordService urgeRecordService = SpringUtils.getBean(IUrgeRecordService.class);//催记
    private RemoteFileService fileService = SpringUtils.getBean(RemoteFileService.class);


    private TeamImportParam param;//导入参数
    private Long importLogId;//导入日志id
    private Set<Long> productIds = new HashSet<>();//多个产品时的产品ID
    private Set<Long> ownerIds = new HashSet<>();//多个资产方时的资产方ID集合
    private LoginUser loginUser;
    private Long teamId;

    public ImportUrgeRecordWorkTask(TeamImportParam param, Long importLogId, LoginUser loginUser) {
        this.param = param;
        this.importLogId = importLogId;
        this.loginUser = loginUser;
        teamId = SecurityUtils.getTeamId(loginUser);
    }


    @Override
    public void runTask() {
//        TeamImportLog importLog = new TeamImportLog();
//        importLog.setId(importLogId);
//        ImportStartEnum importStart = ImportStartEnum.ING;
//        try {
//            File tempFile = FileDownloadUtils.downloadTempFile(param.getFileUrl());
//            ExcelReader reader = ExcelUtil.getReader(tempFile);
//            List<Map<String, Object>> rows = reader.readAll();
//            List<Map<String, Object>> fails = new ArrayList<>();//导入失败
//
//            long successNum = 0;
//            for (Map<String, Object> row : rows) {
//                if (row.size() == 1) {
//                    continue;
//                }
//                Map<String, Object> retMap = actionRow(row);
//                boolean start = (boolean) retMap.get("start");
//                if (start) {
//                    successNum++;
//                } else {//失败
//                    String errorMsg = (String) retMap.get("errorMsg");
//                    row.put("失败原因", errorMsg);
//                    fails.add(row);
//                }
//            }
//            if (fails.size() == 0) {//全部成功
//                importStart = ImportStartEnum.SUCCESS;
//            } else {//有失败的数据
//                if (fails.size() == rows.size()) {//全部失败
//                    importStart = ImportStartEnum.FAIL;
//                } else {//部分失败
//                    importStart = ImportStartEnum.PARTIAL_SUCCESS;
//                }
//                importLog.setFailFileUrl(uploadFailFile(fails));
//            }
//            importLog.setSuccessNumber(successNum);
//            FileDownloadUtils.deletedTempFile(tempFile);
//        } catch (Exception e) {
//            log.error("导入催记错误:", e);
//            importStart = ImportStartEnum.FAIL;
//        } finally {
//            if (productIds.size() == 1) {
//                importLog.setProductId((Long) productIds.toArray()[0]);
//                importLog.setOwnerId((Long) ownerIds.toArray()[0]);
//            }
//            importLog.setImportStart(importStart.getCode());
//            importLogService.updateById(importLog, loginUser);
//        }
    }

//    /**
//     * 处理行
//     *
//     * @param row
//     * @return
//     */
//    private Map<String, Object> actionRow(Map<String, Object> row) {
//        Map<String, Object> map = new HashMap<>();
//        String[] template = DownloadConstant.urgeRecordTemplate;
//
//        //本人、夫妻、父母、兄弟、姐妹、朋友、同事、同学、其他亲属/家人、其他
//        String[] relations = DownloadConstant.urgeRecordRelations;
//        boolean start = false;
//        String errorMsg = "";
//        try {
//            Long caseId = checkCaeId(row.get(template[0]));//案件ID
//
//            Date createTime = ConvertUtils.checkDate(row.get(template[1]), true, "登记时间 错误");
//            String liaison = ConvertUtils.checkString(row.get(template[2]), true, "联络人 错误");
//            if (!StrUtil.isEmpty(liaison)) {
//                if (liaison.length() > 300) {
//                    throw new GlobalException("联络人 字段限制300字");
//                }
//            }
//            String relation = ConvertUtils.checkString(row.get(template[3]), true, "关系 错误");
//            if (!StrUtil.isEmpty(relation)) {
//                if (relation.length() > 20) {
//                    throw new GlobalException("关系 字段限制20字");
//                }
//            }
//
//            /*if(ArrayUtil.contains(relations,relation)){  //判断关系 是否合法
//                throw  new GlobalException("关系 不是规定的关系选项");
//            }*/
//            String contactMode = ConvertUtils.checkString(row.get(template[4]), true, "联络方式 错误");
//            if (!StrUtil.isEmpty(contactMode)) {
//                if (contactMode.length() > 100) {
//                    throw new GlobalException("联络方式 字段限制100字");
//                }
//            }
//
//            String followUpState = ConvertUtils.checkString(row.get(template[5]), true, "跟进状态 错误");//跟进状态
//            if (!StrUtil.isEmpty(followUpState)) {
//                if (followUpState.length() > 20) {
//                    throw new GlobalException("跟进状态 字段限制20字");
//                }
//            }
//            String urgeState = ConvertUtils.checkString(row.get(template[6]), true, "催收状态 错误");//催收状态
//            if (!StrUtil.isEmpty(urgeState)) {
//                if (urgeState.length() > 20) {
//                    throw new GlobalException("催收状态 字段限制20字");
//                }
//            }
//            //TODO 判断 根据状态 以及催收状态是否合法
//
//            String content = ConvertUtils.checkString(row.get(template[7]), true, "沟通内容 错误");//沟通内容
//            if (!StrUtil.isEmpty(content)) {
//                if (content.length() > 1000) {
//                    throw new GlobalException("沟通内容 字段限制1000字");
//                }
//            }
//            String remarks = ConvertUtils.checkString(row.get(template[8]), true, "承诺还款信息/备注 错误");//承诺还款信息/备注
//            if (!StrUtil.isEmpty(remarks)) {
//                if (remarks.length() > 500) {
//                    throw new GlobalException("承诺还款信息/备注 字段限制500字");
//                }
//            }
//            String odvName = ConvertUtils.checkString(row.get(template[9]), true, "催收员 错误");//催收员
//            if (!StrUtil.isEmpty(odvName)) {
//                if (odvName.length() > 20) {
//                    throw new GlobalException("催收员 字段限制20字");
//                }
//            }
//            String contactMedium = ConvertUtils.checkString(row.get(template[10]), true, "联系渠道 错误");//联系渠道
//            if (!StrUtil.isEmpty(contactMedium)) {
//                if (contactMedium.length() > 50) {
//                    throw new GlobalException("联系渠道 字段限制50字");
//                }
//            }
//
//            //联络人、联系电话 加密
//            String liaisonEns = FieldEncryptUtil.encrypt(liaison);
//            String contactModeEns = FieldEncryptUtil.encrypt(contactMode);
//
//            UrgeRecord urgeRecord = new UrgeRecord();
//            urgeRecord.setCaseId(caseId);
//            urgeRecord.setCreateTime(createTime);
//            urgeRecord.setLiaison(liaisonEns);
//            urgeRecord.setRelation(relation);
//            urgeRecord.setContactMode(contactModeEns);
//            urgeRecord.setFollowUpState(followUpState);
//            urgeRecord.setUrgeState(urgeState);
//            urgeRecord.setContent(content);
//            urgeRecord.setRemarks(remarks);
//            urgeRecord.setOdvName(odvName);
//            //urgeRecord.setOdvId(null);//TODO 根据 员工名称获取 对应的员工id
//            urgeRecord.setContactMedium(contactMedium);
//            urgeRecord.setImportReminder(0);
//            urgeRecord.setOperationType(SecurityUtils.getAccountType(loginUser));
//            urgeRecord.setCreateId(teamId.intValue());
//            urgeRecordService.insert(urgeRecord,loginUser);
//            start = true;
//        } catch (Exception e) {
//            log.error("导入催记异常", e);
//            start = false;
//            errorMsg = e.getMessage();
//        }
//        map.put("start", start);
//        map.put("errorMsg", errorMsg);
//        return map;
//
//    }
//
//    /**
//     * 检查案件是否合法
//     *
//     * @param obj
//     */
//    private Long checkCaeId(Object obj) {
//        if (ObjectUtil.isEmpty(obj)) {
//            throw new GlobalException("案件ID 为空");
//        }
//        if (!StringUtils.isNumeric(obj.toString())) {
//            throw new GlobalException("案件ID 错误");
//        }
//        Long caseId = Long.parseLong(StrUtil.toString(obj));
//        Library library = libraryService.selectById(caseId);
//        if (library == null || !ObjectUtil.equals(library.getTeamId(), teamId)) {
//            throw new GlobalException("案件ID 错误");
//        }
//
//        if (param.getId() != null) {//只能导入指定的id
//            if (!library.getProductId().equals(param.getProductId())) {
//                throw new GlobalException("不是指定的资产方-产品");
//            }
//        } else {
//            this.productIds.add(library.getProductId());
//            this.ownerIds.add(library.getEntrustingPartyId());
//        }
//        return caseId;
//    }
//
//
//    /**
//     * 上传保存失败的案件
//     *
//     * @param fails
//     * @return
//     * @throws Exception
//     */
//    private String uploadFailFile(List<Map<String, Object>> fails) {
//        try {
//            MultipartFile multipartFile = FileDownloadUtils.generateMultipartFile(fails);
//            R<SysFile> rest = fileService.upload(multipartFile);
//            if (rest.getCode() == R.SUCCESS) {
//                return rest.getData().getUrl();
//            } else {
//                log.error("上传失败文件错误", rest.getMsg());
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error(JSONUtil.toJsonStr(fails));
//            log.error("保存失败文件错误", e);
//        }
//        return "";
//    }


    @Override
    public void cancelTask() {

    }

    @Override
    public int getProgress() {
        return 0;
    }
}
