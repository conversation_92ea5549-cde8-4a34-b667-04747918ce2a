package com.zws.cis.agservice;

import com.zws.cis.controller.request.TeamManageParam;
import com.zws.cis.domain.TeamManage;
import com.zws.cis.pojo.CaseBaseInfo;
import com.zws.cis.service.ILibraryService;
import com.zws.cis.service.TeamProductService;
import com.zws.common.core.constant.FileConstant;
import com.zws.common.core.domain.R;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.poi.ExportUtils;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.domain.SysFile;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 下载任务AgService
 *
 * <AUTHOR>
 * @date 2024/1/23 15:12
 */
@Slf4j
@Component
public class TeamDownloadAgService {

    @Autowired
    private RemoteFileService remoteFileService;
    @Autowired
    private ILibraryService libraryService;
    @Autowired
    private TeamProductService productService;



//    /**
//     * 现案件下载
//     * @param param
//     * @return
//     */
//    public String downloadExistingCases(TeamManageParam param) throws Exception {
//        TeamManage assetManage = param.getAssetManage();
//        String batchNum = assetManage.getBatchNum();
//        ProductTemplate template = productService.productTemplateById(assetManage.getProductId());
//        template.setAdditionalInfo(null);
//        template.setRepaymentInfo(null);
//        template.setLiaisonInfo(null);
//        Map<String, String> fieldHeader = new LinkedHashMap<>();
//        fieldHeader.put("id", "ID");
//        Map<String, String> fieldHeader1 = template.getFieldHeader();
//        fieldHeader.putAll(fieldHeader1);
//        List<CaseBaseInfo> data = libraryService.selectCaseBaseInfoList(batchNum,param.getTeamId());
//        fieldHeader.put("syYhPrincipal", "剩余应还本金");
//        fieldHeader.put("syYhInterest", "剩余应还利息");
//        fieldHeader.put("syYhFees", "剩余应还费用");
//        fieldHeader.put("syYhDefault", "剩余应还罚息");
//        fieldHeader.put("remainingDue", "剩余应还债权总额");
//        fieldHeader.put("uid", "UID");
////        com.zws.assets.common.utils.ExportUtils.customExcelHeader(response, fieldHeader, data, batchNum + "现案件" + FileConstant.getExcelSuffix(), "");
//
//
//        File tempFile= FileDownloadUtils.getTempExcelFile();
//        FileOutputStream out = new FileOutputStream(tempFile);
//        String fileName = batchNum + "现案件" + FileConstant.getExcelSuffix();
//        if (ObjectUtils.isEmpty(data)) {
////            List<ExportDataUtils> list = new ArrayList<>();
////            list.add(new ExportDataUtils());
//            data.add(new CaseBaseInfo());
//        }
////        Map<String, String> fieldHeader = ExportUrgeDiaryFields.getFieldHeaderMap(param.getHeaders());
//        ExportUtils.customExcelHeader(out, fieldHeader, data, fileName, "");
//        out.close();
//
//        FileInputStream inputStream = new FileInputStream(tempFile);
//        MultipartFile multipartFile = new MockMultipartFile(tempFile.getName(), tempFile.getName(),
//                ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
//        inputStream.close();
//        FileDownloadUtils.deletedTempFile(tempFile);
//        try {
//            R<SysFile> rest = remoteFileService.upload(multipartFile);
//            if (rest.getCode() == R.SUCCESS) {
//                return rest.getData().getUrl();
//            }
//        }catch (FeignException e){
//            log.error("保存失败文件错误",e);
//            throw new ServiceException("保存失败文件错误,文件服务未启动");
//        }
//        catch (Exception e) {
//            log.error("保存失败文件错误",e);
//            throw new ServiceException("保存失败文件错误");
//        }
//        return null;
//    }


    /**
     * 案源下载
     * @param param
     * @return
     */
    public String downloadCasesSource(TeamManageParam param) throws Exception {
        TeamManage assetManage = param.getAssetManage();
        String fileUrl = assetManage.getSourceFileurl();
        File tempFile = FileDownloadUtils.downloadTempFile(fileUrl);


//        FileOutputStream out = new FileOutputStream(tempFile);
//        out.close();

        FileInputStream inputStream = new FileInputStream(tempFile);
        MultipartFile multipartFile = new MockMultipartFile(tempFile.getName(), tempFile.getName(),
                ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
        inputStream.close();
        FileDownloadUtils.deletedTempFile(tempFile);
        try {
            R<SysFile> rest = remoteFileService.upload(multipartFile);
            if (rest.getCode() == R.SUCCESS) {
                return rest.getData().getUrl();
            }
        } catch (Exception e) {
            log.error("保存失败文件错误",e);
            throw new ServiceException("文件服务异常！");
        }
        return null;
    }

}
