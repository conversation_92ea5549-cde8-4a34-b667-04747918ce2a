package com.zws.cis.service.impl;

import com.zws.cis.mapper.InfoContactMapper;
import com.zws.cis.service.IInfoContactService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.sms.InfoContact;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2024年8月8日10:28:54
 */
@Service
@Primary
public class InfoContactServiceImpl implements IInfoContactService {

    @Autowired
    private InfoContactMapper mapper;

    @Override
    public long insert(InfoContact entity, LoginUser loginUser) {
        if(entity==null) {
            return 0;
        }
        if (entity.getCaseId()==null) {
            throw new GlobalException("案件ID不能为空");
        }
        if (entity.getTeamId()==null){
            throw new ServiceException("所属机构不能为空");
        }
        if(StringUtils.isNotEmpty(entity.getContactPhone()) ){
            String phone=entity.getContactPhone();
            entity.setContactPhoneEnc(FieldEncryptUtil.phoneLike(phone));
            entity.setContactPhone(FieldEncryptUtil.encrypt(phone));
        }
        if(StringUtils.isNotEmpty(entity.getContactName()) ){
            String contactName=entity.getContactName();
            entity.setContactName(FieldEncryptUtil.encrypt(contactName));
        }

//        String geo= PhoneUtil.getGeo(entity.getContactPhone());
//        entity.setPhoneLocation(geo);
        entity.setPhoneState(BaseConstant.DelFlag_Being);
        entity.setDelFlag(BaseConstant.DelFlag_Being);
        if (StringUtils.isEmpty( entity.getCreateBy())) {
            entity.setCreateBy(SecurityUtils.getUsername(loginUser));
        }

        entity.setCreateTime(DateUtils.getNowDate());
        mapper.insert(entity);
        return entity.getId();
    }

    @Override
    public int batchInsert(List<InfoContact> entitys) {
        return mapper.batchInsert(entitys);
    }

    @Override
    public void updateById(InfoContact entity) {
        if(entity.getId()==null) {
            throw new GlobalException("主键为空无法编辑");
        }
        entity.setUpdateBy(SecurityUtils.getUsername());
        entity.setUpdateTime(DateUtils.getNowDate());
        mapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void deleteByCaseId(Long caseId) {
        List<InfoContact> contacts= mapper.selectSearchList(caseId,null);
        for (InfoContact contact:contacts) {
            contact.setDelFlag(BaseConstant.DelFlag_Delete);
            this.updateById(contact);
        }
    }

    @Override
    public InfoContact selectById(Long id) {
        InfoContact infoContact=mapper.selectByPrimaryKey(id);
        return infoContact;
    }

    @Override
    public InfoContact selectInitialInfoContact(Long caseId) {

        return this.mapper.selectInitialInfoContact(caseId);
    }
}
