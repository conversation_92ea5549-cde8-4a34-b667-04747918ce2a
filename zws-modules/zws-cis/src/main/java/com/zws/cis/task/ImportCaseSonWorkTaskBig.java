package com.zws.cis.task;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zws.cis.agservice.TeamImportCaseAgService;
import com.zws.cis.domain.TeamManage;
import com.zws.cis.domain.TeamProduct;
import com.zws.cis.pojo.TeamActionRowResp;
import com.zws.cis.pojo.TeamImportCasePojo;
import com.zws.cis.pojo.TeamImportCaseSonTaskResp;
import com.zws.cis.pojo.TeamImportCaseSynContainer;
import com.zws.cis.service.TeamProductService;
import com.zws.common.core.domain.sms.InfoContact;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.SpringUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.reflect.ReflexUtils;
import com.zws.system.api.domain.InfoExtra;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.sql.SQLSyntaxErrorException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 导入案件子线程-大文件
 *
 * <AUTHOR>
 * @date ：2024年8月8日09:49:29
 */
@Slf4j
public class ImportCaseSonWorkTaskBig extends Thread {

    private TeamImportCaseAgService importCaseAgservice = SpringUtils.getBean(TeamImportCaseAgService.class);
    private TeamProductService productService = SpringUtils.getBean(TeamProductService.class);

    //数据缓存接收区
    private TeamImportCaseSynContainer synContainer;
    //记录行
    private List<List<Object>> workTaskRows;
    //标题行
    private List<Object> titleRow;
    //案件管理对象-此对象在子线程中只做传递参数作用-只读
    private TeamManage asset;
    //产品-只读
    private TeamProduct product;
    private LoginUser loginUser;//登录人信息

    //例外字段，此数组中的字段必填
    private String[] EXCEPTION = new String[]{"securityName", "securityIdType", "securityIdNum", "securityPhone"};


    public ImportCaseSonWorkTaskBig(TeamManage asset, TeamProduct product, TeamImportCaseSynContainer synContainer, List<Object> titleRow, List<List<Object>> workTaskRows,LoginUser loginUser) {
        super();
        this.workTaskRows = workTaskRows;
        this.titleRow=titleRow;
        this.asset = asset;
        this.product = product;
        this.synContainer = synContainer;
        this.loginUser = loginUser;
    }

    @Override
    public void run() {
//        if (workTaskRows == null) {
//            workTaskRows = new ArrayList<>();
//        }
//        //导入案件客户姓名、身份证、电话必须，其他非必填
//        //判断是否符合模板
//        TeamProduct product = productService.selectByPrimaryKey(asset.getProductId());
//        if (product == null) {
//            throw new GlobalException("未找到产品，请确定产品id");
//        }
//        String tmplateStr = product.getTemplate();
//        if (StringUtils.isEmpty(tmplateStr)) {
//            throw new GlobalException("产品未设置导入模板");
//        }
//        ProductTemplate productTemplate = JSONUtil.toBean(tmplateStr, ProductTemplate.class);
//
//        ProductTemplate initTemp = productService.getInitTemplate();
//        initTemp.setAdditionalInfo(productTemplate.getAdditionalInfo());
//        String shortName = product.getShortName();
//        if (!StringUtils.equals(shortName, "BL")) {
//            //如果导入的不是企业经营贷
//            //担保人、担保人证件类型、担保人证件号码、担保人联系电话，导入不做必填判断，可以为空；
//            EXCEPTION = new String[]{};
//        }
//
//        int successNum = 0;
//        BigDecimal entrustMoneyTotal = BigDecimal.ZERO;
//        List<Map<String, Object>> fails = new ArrayList<>();
//
//        log.info("线程:{},案件数量:{}", this.getName(), workTaskRows.size());
//        for (List<Object> row : workTaskRows) {
//            //if (row.size() == 1) continue;
//
//            TeamActionRowResp res = actionRow(row, productTemplate);
//            if (res.isSuccess()) {
//                //成功
//                BigDecimal entrustMoney = res.getEntrustMoney();
//                if (StringUtils.isNotNull(entrustMoney)) {
//                    entrustMoneyTotal = entrustMoneyTotal.add(entrustMoney);
//                }
//                successNum++;
//            } else {//失败
//                try {
//                    //日期转年月日格式
//                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                    int clientBirthday= titleRow.indexOf("出生日期");
//                    int overdueStart = titleRow.indexOf("逾期日期");
//                    int amountFinalDate = titleRow.indexOf("最后还款日期");
//                    int yhDate = titleRow.indexOf("应还日期");
//                    int alreadyHkDate = titleRow.indexOf("已还款日期");
//
//                    if (clientBirthday >=0) {
//                        row.set(clientBirthday, simpleDateFormat.format(row.get(clientBirthday)));
//                    }
//                    if (overdueStart >=0) {
//                        row.set(overdueStart, simpleDateFormat.format(row.get(overdueStart)));
//                    }
//                    if (amountFinalDate >=0) {
//                        row.set(amountFinalDate, simpleDateFormat.format(row.get(amountFinalDate)));
//                    }
//                    if (yhDate >=0) {
//                        row.set(yhDate, simpleDateFormat.format(row.get(yhDate)));
//                    }
//                    if (alreadyHkDate >=0) {
//                        row.set(alreadyHkDate, simpleDateFormat.format(row.get(alreadyHkDate)));
//                    }
//                } catch (Exception e) {
//                    log.error("导入失败时日期格式转换错误,", e);
//                }
//                Map<String,Object> fileRows=new LinkedHashMap<>();
//                for (int i = 0; i < titleRow.size(); i++) {
//                    fileRows.put(StrUtil.toString(titleRow.get(i)),row.get(i));
//                }
//                fileRows.put("失败原因",res.getErrorMsg());
//                fails.add(fileRows);
//            }
//        }
//
//        TeamImportCaseSonTaskResp taskRes = new TeamImportCaseSonTaskResp();
//        taskRes.setFailsRows(fails);
//        taskRes.setSuccessNum(successNum);
//        taskRes.setEntrustMoneyTotal(entrustMoneyTotal);
//        synContainer.submit(taskRes);
    }

//    /**
//     * 处理行
//     * @param row
//     * @param productTemplate
//     * @return
//     */
//    private TeamActionRowResp actionRow(List<Object> row, ProductTemplate productTemplate) {
//        //Map<String, Object> retMap = new HashMap<>();
//        //是否成功
//        boolean start = false;
//        //失败原因
//        String errorMsg = "";
//        //委托金额
//        BigDecimal entrustMoney = null;
//        TeamActionRowResp res = new TeamActionRowResp();
//        try {
//            TeamImportCasePojo icpojo = new TeamImportCasePojo();
//            icpojo.getLibrary().setBatchNo(asset.getBatchNum());
//            icpojo.getLibrary().setProductId(asset.getProductId());
//            icpojo.getLibrary().setProductName(product.getName());
//            icpojo.getLibrary().setEntrustingPartyId(asset.getOwnerId());
//            icpojo.getLibrary().setEntrustingPartyName(product.getOwnerName());
//            icpojo.getLibrary().setAssetManageId(asset.getId());
//            icpojo.getLoanInfo().setProductId(asset.getProductId());
//            icpojo.getLibrary().setUpdateBy(asset.getUpdateBy());
//            icpojo.getLoanInfo().setProductId(product.getId());
//            icpojo.getLoanInfo().setProductName(product.getName());
//            productTemplate.getTableField("");//用于生成
//            //标题
//            List<Object> keys = this.titleRow;
//            //判断
//            //boolean isProperSub= CollectionUtils.isProperSubCollection(productTemplate.getRequireds(),keys);//此判断在导入字段等于必填字段时会判false
//            boolean isProperSub = CollectionUtils.isSubCollection(productTemplate.getRequireds(), keys);
//            if (!isProperSub) {
//                StringBuffer sbuff = new StringBuffer();
//                for (String temp : productTemplate.getRequireds()) {
//                    if (!keys.contains(temp)) {
//                        sbuff.append(temp + "  ");
//                    }
//                }
//                throw new ServiceException("缺少必填字段,请重新下载模板." + sbuff.toString());
//            }
//
//            for (int i = 0; i < keys.size(); i++) {
//                String tempKey=StrUtil.toString(keys.get(i));
//                Object val = row.get(i);
//
//                try {
//                    if (val == null) {
//                        //当execl 表格的表被清除时，会获取到此数值为null,
////                        throw new ServiceException(tempKey + "不能为空");
//                        val = "";
//                    }
//                    TemplateTable tt = productTemplate.getTableField(tempKey);
//                    if (tt != null) {
//                        if (EXCEPTION.length > 0 && Arrays.asList(EXCEPTION).contains(tt.getField())) {
//                            tt.setDef(true);
//                        }
//                        if (tt.isDef()) {
//                            if (StrUtil.isBlankIfStr(val)) {
//                                throw new ServiceException("默认必填，不能为空");
//                            }
//                            if (StringUtils.equals("-", val.toString())) {
//                                val = null;
//                            }
//                        }
//
//                        if (tt.getLimit() != null) {
//                            Object finalVal = val;
//                            boolean anyMatch = Arrays.stream(tt.getLimit()).anyMatch(v -> StrUtil.equals(v, StrUtil.toString(finalVal)));
//                            if (!anyMatch) {
//                                throw new ServiceException("只能填写指定字段");
//                            }
//                        }
//
//                        switch (tt.getTableName()) {
//                            case "baseInfo":
//                                ReflexUtils.setFieldVal(icpojo.getBaseInfo(), tt.getField(), val);
//                                break;
//                            case "caseInfo":
//                                ReflexUtils.setFieldVal(icpojo.getLoanInfo(), tt.getField(), val);
//                                break;
//                            case "repaymentInfo":
//                                ReflexUtils.setFieldVal(icpojo.getPlanInfo(), tt.getField(), val);
//                                break;
//                            case "liaisonInfo":
//                                String str = val.toString();
//                                if (!StringUtils.isEmpty(str)) {
//                                    String[] contact = str.split("\\|");
//                                    if (contact.length < 1) {
//                                        break;
//                                    }
//                                    String tep = contact[0];
//                                    if (ObjectUtils.isEmpty(tep)) {
//                                        break;
//                                    }
//                                    String gx = null;
//                                    if (contact.length >= 2) {
//                                        gx = contact[1];
//                                    }
//                                    String name = null;
//                                    if (contact.length >= 3) {
//                                        name = contact[2];
//                                    }
//                                    if (ObjectUtils.isEmpty(gx) || gx.equals("")) {
//                                        gx = "其他";
//                                    }
//                                    if (ObjectUtils.isEmpty(name) || name.equals("")) {
//                                        name = "未知";
//                                    }
//                                    InfoContact infoContact = new InfoContact();
//                                    infoContact.setContactPhone(tep);
//                                    infoContact.setContactName(name);
//                                    infoContact.setContactRelation(gx);
//                                    icpojo.getContactInfos().add(infoContact);
//                                }
//                                break;
////                            case "additionalInfo":
////                                InfoExtra infoExtra = new InfoExtra();
////                                infoExtra.setExtraName(tempKey);
////                                infoExtra.setExtraValue(ObjectUtil.toString(val));
////                                icpojo.getExtraInfo().add(infoExtra);
////                                break;
//                        }
//                    } else {
//                        ProductTemplateGroup additionalInfo = productTemplate.getAdditionalInfo();
//                        if (!ObjectUtils.isEmpty(additionalInfo)) {
//                            List<ProductTemplateItem> columns = additionalInfo.getColumns();
//                            if (!ObjectUtils.isEmpty(columns)) {
//                                for (ProductTemplateItem item : columns) {
//                                    if (tempKey.equals(item.getLabel())) {
//                                        InfoExtra infoExtra = new InfoExtra();
//                                        infoExtra.setExtraName(tempKey);
//                                        infoExtra.setExtraValue(ObjectUtil.toString(val));
//                                        icpojo.getExtraInfo().add(infoExtra);
//                                    }
//                                }
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    throw new GlobalException(tempKey + "错误:" + e.getMessage());
//                }
//            }
//            icpojo.getBaseInfo().setBankCardNumber(icpojo.getLoanInfo().getBankCardNumber());
//            icpojo.getBaseInfo().setBankName(icpojo.getLoanInfo().getBankName());
//            icpojo.getLoanInfo().setYcFiveLevel(icpojo.getBaseInfo().getYcFiveLevel());
//            entrustMoney = icpojo.getLoanInfo().getEntrustMoney();
//            long caseId = importCaseAgservice.importCase(icpojo,loginUser,res);
//            //转入case_manage表
//
//            if (caseId > 0) {
//                start = true;
//            } else {
//                start = false;
//                errorMsg = "执行数据库操作异常，请联系管理员处理";
//            }
//        } catch (SQLSyntaxErrorException sqlExc) {
//            sqlExc.printStackTrace();
//            start = false;
//            errorMsg = "执行数据库操作异常，请联系管理员处理";
//        } catch (Exception e) {
//            e.printStackTrace();
//            start = false;
//            errorMsg = e.getMessage();
//        }
//       /* retMap.put("start", start);
//        retMap.put("errorMsg", errorMsg);
//        retMap.put("entrustMoney", entrustMoney);*/
//        res.setSuccess(start);
//        res.setErrorMsg(errorMsg);
//        res.setEntrustMoney(entrustMoney);
//        return res;
//    }

}
