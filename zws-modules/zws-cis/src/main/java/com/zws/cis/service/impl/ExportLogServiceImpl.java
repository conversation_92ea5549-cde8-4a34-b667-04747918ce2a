package com.zws.cis.service.impl;

import cn.hutool.core.util.StrUtil;
import com.zws.cis.domain.TeamExportLog;
import com.zws.cis.mapper.ExportLogMapper;
import com.zws.cis.service.IExportLogService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 导出日志 业务层实现类
 *
 * <AUTHOR>
 * @date 2024/1/22 20:20
 */
@Service
public class ExportLogServiceImpl implements IExportLogService {

    @Autowired
    private ExportLogMapper baseMapper;

    @Override
    public Long insert(TeamExportLog record) {
        record.setCreateBy(SecurityUtils.getUsername());
        record.setCreateById(SecurityUtils.getUserId());
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setOperationType(SecurityUtils.getAccountType());
        baseMapper.insert(record);
        return record.getId();
    }

    @Override
    public void update(TeamExportLog record) {
        record.setCreateBy(null);
        record.setCreateById(null);
        record.setCreateTime(null);
        record.setOperationType(null);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        this.baseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<TeamExportLog> selectList(TeamExportLog record) {
        record.setCreateTime1(DateUtils.beginOfDay(record.getCreateTime1()));
        record.setCreateTime2(DateUtils.endOfDay(record.getCreateTime2()));
        String exportType = record.getExportType();
        List<String> exportTypeList = new ArrayList<>();
        if (!StrUtil.isBlankIfStr(exportType)) {
            exportTypeList.addAll(Arrays.asList(exportType.split(BaseConstant.comma)));
        }
        record.setExportTypeList(exportTypeList);
        return this.baseMapper.selectList(record);
    }
}
