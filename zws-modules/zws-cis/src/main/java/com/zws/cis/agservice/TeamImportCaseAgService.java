package com.zws.cis.agservice;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zws.cis.pojo.TeamActionRowResp;
import com.zws.cis.pojo.TeamImportCasePojo;
import com.zws.cis.service.*;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.sms.InfoContact;
import com.zws.common.core.enums.AllocatedStates;
import com.zws.common.core.enums.CaseStates;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.*;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.*;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 导入案件
 *
 * <AUTHOR>
 * @date ：Created in 2022/2/24 11:55
 */
@Slf4j
@Component
@EnableTransactionManagement
public class TeamImportCaseAgService {

    @Autowired
    private ILibraryService libraryService;
    @Autowired
    private ILibraryMpService libraryMpService;
    @Autowired
    private IInfoPlanService infoPlanService;
    @Autowired
    private IInfoLoanService infoLoanService;
    @Autowired
    private IInfoExtraService infoExtraService;
    @Autowired
    private IInfoBaseService infoBaseService;
    @Autowired
    private IInfoContactService infoContactService;
    @Autowired
    private IManageService manageService;


//    /**
//     * 导入案件
//     *
//     * @param pojo
//     * @return 返回案件ID
//     * @throws Exception
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public long importCase(TeamImportCasePojo pojo, LoginUser loginUser, TeamActionRowResp res) throws Exception {
//
//        Library library = pojo.getLibrary();
//        InfoBase baseInfo = pojo.getBaseInfo();
//        InfoLoan loanInfo = pojo.getLoanInfo();
//        InfoPlan planInfo = pojo.getPlanInfo();
//
//        Long teamId=SecurityUtils.getTeamId(loginUser);
//        library.setTeamId(teamId);
//        baseInfo.setTeamId(teamId);
//        loanInfo.setTeamId(teamId);
//        planInfo.setTeamId(teamId);
//        // 校验参数
//        caseParamVerification(pojo);
//        // 构建案件库信息
//        library.setImportSource(ImportSourceEnum.TEAM_IMPORT.getCode());
//        library.setCreateBy(SecurityUtils.getUsername(loginUser));
//        library.setClientName(FieldEncryptUtil.encrypt(baseInfo.getClientName()));
//        library.setClientCensusRegister(FieldEncryptUtil.encrypt(baseInfo.getClientCensusRegister()));
//        library.setClientIdNum(FieldEncryptUtil.encrypt(baseInfo.getClientIdNum()));
//        library.setClientPhone(FieldEncryptUtil.encrypt(baseInfo.getClientPhone()));
//        library.setOverdueStart(loanInfo.getOverdueStart());
//        library.setEntrustMoney(loanInfo.getEntrustMoney());
//        library.setClientIdType(baseInfo.getClientIdType());
//        //固定团队端导入来源
//        library.setImportSource(ImportSourceEnum.TEAM_IMPORT.getCode());
//        if (StringUtils.isEmpty(library.getSettleState())) {
//            library.setSettleState(BaseConstant.SettleState_Not);
//        }
//        library.setAllocateCaseState(BaseConstant.AllocateCaseState_Not);
//        library.setDelFlag(BaseConstant.DelFlag_Being);
//        library.setCreateBy(SecurityUtils.getUsername());
//        library.setCreateTime(DateUtils.getNowDate());
//        //设置临时ID
//        library.setTempId(IdUtil.getSnowflakeNextId());
//
//        // 添加到案件库
//        res.getLibraries().add(library);
//
//        try {
//            long finalCaseId = library.getTempId();
//            // 敏感信息加密
//            encBaseInfo(loginUser, baseInfo, finalCaseId);
//            // 写入基础信息
//            if(ObjectUtil.isNotNull(baseInfo)){
//                res.getBaseInfos().add(baseInfo);
//            }
////            infoBaseService.insert(baseInfo);
//
//            planInfo.setCaseId(finalCaseId);
//            planInfo.setCreateBy(library.getCreateBy());
////                写入还款计划
//            if(ObjectUtil.isNotNull(planInfo)){
//                res.getInfoPlans().add(planInfo);
//            }
//
////            infoPlanService.insert(planInfo);
//            if (pojo.getPlanInfos() != null&&pojo.getPlanInfos().size()>0) {
//                pojo.getPlanInfos().forEach(infoPlan -> {
//                    infoPlan.setTeamId(teamId);
//                    infoPlan.setCaseId(finalCaseId);
//                    infoPlan.setDelFlag(BaseConstant.DelFlag_Being);
//                    infoPlan.setCreateBy(library.getCreateBy());
//                    infoPlan.setCreateTime(DateUtils.getNowDate());
//                });
//                   res.getInfoPlans().addAll(pojo.getPlanInfos());
////                infoPlanService.batchInsert(pojo.getPlanInfos());
//            }
//
//
//            loanInfo.setCaseId(library.getTempId());
//            loanInfo.setAssetManageId(library.getAssetManageId());
//            loanInfo.setSyYhFees(loanInfo.getServiceFee());
//            loanInfo.setSyYhInterest(loanInfo.getInterestMoney());
//            loanInfo.setSyYhPrincipal(loanInfo.getResidualPrincipal());
//            loanInfo.setRemainingDue(loanInfo.getEntrustMoney());
//            loanInfo.setCreateBy(library.getCreateBy());
//            loanInfo.setDelFlag(BaseConstant.DelFlag_Being);
//            loanInfo.setCreateTime(DateUtils.getNowDate());
//
////            写入贷款信息
//            if(ObjectUtil.isNotNull(loanInfo)){
//                res.getInfoLoans().add(loanInfo);
//            }
////            infoLoanService.insert(loanInfo);
//
//            //添加案件联系人库
//            List<InfoContact> contactInfos = pojo.getContactInfos();
//            contactInfos.forEach(infoContact -> {
//                infoContact.setContactPhone(FieldEncryptUtil.encrypt(infoContact.getContactPhone()));
//                infoContact.setContactName(FieldEncryptUtil.encrypt(infoContact.getContactName()));
//                infoContact.setPhoneState(BaseConstant.DelFlag_Being);
//                infoContact.setCaseId(finalCaseId);
//                infoContact.setTeamId(teamId);
//                infoContact.setDelFlag(BaseConstant.DelFlag_Being);
//                infoContact.setCreateBy(library.getCreateBy());
//                infoContact.setCreateTime(DateUtils.getNowDate());
//            });
//            if(contactInfos.size()>0){
//                res.getInfoContacts().addAll(contactInfos);
////                infoContactService.batchInsert(contactInfos);
//            }
//
//            //添加案件附件库
//            List<InfoExtra> extraInfos = pojo.getExtraInfo();
//            extraInfos.forEach(infoExtra -> {
//                infoExtra.setCaseId(finalCaseId);
//                infoExtra.setTeamId(teamId);
//                infoExtra.setDelFlag(BaseConstant.DelFlag_Being);
//                infoExtra.setCreateBy(library.getCreateBy());
//                infoExtra.setCreateTime(DateUtils.getNowDate());
//            });
//            if (extraInfos.size() > 0) {
//                res.getInfoExtras().addAll(extraInfos);
////                infoExtraService.batchInsert(extraInfos);
//            }
//            //转换到case_manage
//            assignToCaseManage(library, baseInfo,loanInfo,res);
//        } catch (Exception e) {
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            log.error("导入案件失败", e);
//        }
//        return library.getTempId();
//    }
//
//    /**
//     *  敏感信息加密
//     * @param loginUser
//     * @param baseInfo
//     * @param caseId
//     */
//    private static void encBaseInfo(LoginUser loginUser, InfoBase baseInfo, long caseId) {
//        //身份证号-加密
//        String idNumEncStr = FieldEncryptUtil.encrypt(baseInfo.getClientIdNum());
//        //电话号码-加密
//        String phoneEncStr = FieldEncryptUtil.encrypt(baseInfo.getClientPhone());
//        //家庭电话-加密
//        String homePhoneEncStr = FieldEncryptUtil.encrypt(baseInfo.getHomePhone());
//        //单位电话-加密
//        String unitTelephoneEncStr = FieldEncryptUtil.encrypt(baseInfo.getUnitTelephone());
//        //姓名-加密
//        String nameEncStr = FieldEncryptUtil.encrypt(baseInfo.getClientName());
//        //户籍地-加密
//        String censusRegisterEncStr = FieldEncryptUtil.encrypt(baseInfo.getClientCensusRegister());
//        //QQ-加密
//        String qqEncStr = FieldEncryptUtil.encrypt(baseInfo.getQq());
//        //微信-加密
//        String wxEncStr = FieldEncryptUtil.encrypt(baseInfo.getWeixin());
//        //邮箱-加密
//        String mailboxEncStr = FieldEncryptUtil.encrypt(baseInfo.getMailbox());
//        //工作单位-加密
//        String placeOfWorkEncStr = FieldEncryptUtil.encrypt(baseInfo.getPlaceOfWork());
//        //单位地址-加密
//        String workingAddressEncStr = FieldEncryptUtil.encrypt(baseInfo.getWorkingAddress());
//        //居住地址-加密
//        String residentialAddressEncStr = FieldEncryptUtil.encrypt(baseInfo.getResidentialAddress());
//        //家庭地址-加密
//        String homeAddressEncStr = FieldEncryptUtil.encrypt(baseInfo.getHomeAddress());
//        //担保人-加密
//        String securityNameEncStr = FieldEncryptUtil.encrypt(baseInfo.getSecurityName());
//        //担保人证件号-加密
//        String securityIdNumEncStr = FieldEncryptUtil.encrypt(baseInfo.getSecurityIdNum());
//        //担保人电话-加密
//        String securityPhoneEncStr = FieldEncryptUtil.encrypt(baseInfo.getSecurityPhone());
//
//        //银行卡号-加密
//        String bankCardNumberEncStr=null;
//        if(StrUtil.isNotEmpty(baseInfo.getBankCardNumber())){
//            if (baseInfo.getBankCardNumber().length()>300){
//                throw new ServiceException("银行卡号太长");
//            }
//            List<String> strings = SplitUtils.strSplitComma(baseInfo.getBankCardNumber());
//
//            List<String>bankCardNumberEncs=new ArrayList<>();
//            for (String bankCardNumber:strings) {
//                bankCardNumberEncs.add(FieldEncryptUtil.encrypt(bankCardNumber));
//            }
//            bankCardNumberEncStr = String.join(SplitUtils.regex_comma, bankCardNumberEncs);
//        }
//
//        baseInfo.setClientName(nameEncStr);
//        baseInfo.setClientIdNum(idNumEncStr);
//        baseInfo.setClientPhone(phoneEncStr);
//        baseInfo.setClientCensusRegister(censusRegisterEncStr);
//        baseInfo.setQq(qqEncStr);
//        baseInfo.setWeixin(wxEncStr);
//        baseInfo.setMailbox(mailboxEncStr);
//        baseInfo.setPlaceOfWork(placeOfWorkEncStr);
//        baseInfo.setResidentialAddress(residentialAddressEncStr);
//        baseInfo.setHomeAddress(homeAddressEncStr);
//        baseInfo.setBankCardNumber(bankCardNumberEncStr);
//        baseInfo.setWorkingAddress(workingAddressEncStr);
//        baseInfo.setSecurityIdNum(securityIdNumEncStr);
//        baseInfo.setSecurityName(securityNameEncStr);
//        baseInfo.setSecurityPhone(securityPhoneEncStr);
//        baseInfo.setCaseId(caseId);
//        baseInfo.setHomePhone(homePhoneEncStr);
//        baseInfo.setUnitTelephone(unitTelephoneEncStr);
//        baseInfo.setCreateBy(SecurityUtils.getUsername(loginUser));
//    }


//    /**
//     * 导入 更新案件
//     *
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public long importUpdateCase(TeamImportCasePojo pojo) throws Exception {
//        //caseParamVerification(pojo);
//        InfoBase baseInfo = pojo.getBaseInfo();
//        InfoLoan loanInfo = pojo.getLoanInfo();
//        if (loanInfo == null) {
//            throw new ServiceException("贷款信息不能为空");
//        }
//        List<InfoContact> contactInfos = pojo.getContactInfos();
//        List<InfoPlan> planInfos = pojo.getPlanInfos();
//        Long caseId = null;
//        String updateBy = null;
//        if (baseInfo != null) {
//            caseId = baseInfo.getCaseId();
//            updateBy = baseInfo.getUpdateBy();
//        }
//        if (caseId == null) {
//            throw new ServiceException("更新案件，案件id不能为空");
//        }
//
//        List<InfoExtra> extraInfos = pojo.getExtraInfo();
//        //更新附加信息
//        if (!ObjectUtil.isEmpty(extraInfos)) {
//            for (InfoExtra item : extraInfos) {
//                Map<String, Object> objectMap = new HashMap<>();
//                objectMap.put("caseId", item.getCaseId());
//                objectMap.put("extraName", item.getExtraName());
//                List<InfoExtra> infoExtras = infoExtraService.selectByCaseIdKey(objectMap);
////                查询为空则新增，不为空则修改
//                if (ObjectUtil.isEmpty(infoExtras) || infoExtras.isEmpty()) {
//                    item.setDelFlag(BaseConstant.DelFlag_Being);
//                    item.setCreateBy(updateBy);
//                    item.setCreateTime(DateUtils.getNowDate());
//                    List<InfoExtra> list = new ArrayList<>();
//                    list.add(item);
//                    infoExtraService.batchInsert(list);
//                } else {
//                    Long aLong = infoExtras.get(0).getId();  //主键id
//                    item.setId(aLong);
//                    item.setUpdateBy(updateBy);
//                    infoExtraService.updateById(item);
//                }
//            }
//        }
//
//        //更新还款计划
//        if (planInfos != null) {
//            for (InfoPlan infoPlan : planInfos) {
//                //根据还款 案件id、还款期数 更新还款计划
//                InfoPlan planTemp = infoPlanService.getPlanByCaseIdAndTerm(caseId, infoPlan.getHkPeriodsNumber());
//                if (planTemp != null) {
//                    //已经存在 相同期数的还款计划则更新
//                    infoPlan.setId(planTemp.getId());
//                    infoPlan.setCaseId(caseId);
//                    infoPlan.setCreateTime(null);
//                    infoPlanService.updateInfoPlanById(infoPlan);
//                } else {
//                    //不存在则新增
//                    infoPlan.setCaseId(caseId);
//                    infoPlanService.insert(infoPlan);
//                }
//            }
//        }
//
//        //loanInfo.setRemainingDue(loanInfo.getEntrustMoney());
//
//        String settleState = null;
//        /*if (loanInfo.getRemainingDue() == null) {
//            loanInfo.setRemainingDue(BigDecimal.ZERO);
//        }*/
//        if (loanInfo.getRemainingDue()!=null && loanInfo.getRemainingDue().compareTo(BigDecimal.ZERO) > 0) {
//            settleState = BaseConstant.SettleState_Not;
//        }
//        //根据身份证更新出生日期
//        String clientIdNum = baseInfo.getClientIdNum();
//        if (!ObjectUtil.isEmpty(clientIdNum)){
//            String substring = clientIdNum.substring(6, 14);
//            DateTime clientBirthday = DateUtil.parse(substring);
//            baseInfo.setClientBirthday(clientBirthday);
//            //修改年龄，性别
//            int age = IdcardUtil.getAgeByIdCard(clientIdNum, new Date());
//            int gender = IdcardUtil.getGenderByIdCard(clientIdNum);
//            baseInfo.setClientAge((long) age);
//            baseInfo.setClientSex(gender == 1 ? "男" : "女");
//
//            //户籍地
//            baseInfo.setClientCensusRegister(IDCardUtils.getNativePlace(clientIdNum));
//        }
//        infoBaseService.updateByCaseId(baseInfo);
//        infoLoanService.updateByCaseId(loanInfo);
//
////        if (contactInfos!=null){
////            //infoContactService.deleteByCaseId(caseId);
////            for (InfoContact contact:contactInfos) {
////                if(StrUtil.equals(contact.getContactRelation(),"本人银行预留手机号")){
////                    contact.setCaseId(caseId);
////                    infoContactService.insert(contact);
////                }
////            }
////        }
//
//        Library library = new Library();
//        library.setId(caseId);
//        library.setClientIdNum(baseInfo.getClientIdNum());
//        library.setClientCensusRegister(baseInfo.getClientCensusRegister());
//        library.setClientName(baseInfo.getClientName());
//        library.setClientPhone(baseInfo.getClientPhone());
//        library.setEntrustMoney(loanInfo.getEntrustMoney());
//        library.setOverdueStart(loanInfo.getOverdueStart());
//        library.setAssetManageId(pojo.getLibrary().getAssetManageId());
//        library.setBatchNo(pojo.getLibrary().getBatchNo());
//        library.setSettleState(settleState);
//        library.setClientIdType(baseInfo.getClientIdType());
//        libraryService.update(library);
//
//
//        CaseManage manage = new CaseManage();
//        manage.setCaseId(caseId);
//        manage.setClientName(baseInfo.getClientName());
//        manage.setClientPhone(baseInfo.getClientPhone());
//        manage.setClientCensusRegister(baseInfo.getClientCensusRegister());
//        manage.setClientIdcard(baseInfo.getClientIdNum());
//        manage.setClientOverdueStart(loanInfo.getOverdueStart());
//        manage.setClientResidualPrincipal(loanInfo.getSyYhPrincipal());
//        manage.setClientMoney(loanInfo.getEntrustMoney());
//        manage.setAssetNo(baseInfo.getAssetNo());
//        manage.setUid(baseInfo.getUid());
//        manage.setClientIdType(baseInfo.getClientIdType());
//        if (settleState != null) {
//            manage.setSettlementStatus(Integer.getInteger(settleState));
//        }
//        manage.setUpdateTime(DateUtils.getNowDate());
//        manageService.updateByCaseId(manage);
//        return caseId;
//    }

//    /**
//     * 导入、更新 案件参数校验
//     *
//     * @param pojo
//     */
//    private void caseParamVerification(TeamImportCasePojo pojo) throws Exception {
//        //身份证号码
//        String clientIdNum = pojo.getBaseInfo().getClientIdNum();
//        if (StringUtils.isNotNull(pojo.getBaseInfo().getClientIdNum())) {
//            if (pojo.getBaseInfo().getClientIdNum().length() != 15 &&
//                    pojo.getBaseInfo().getClientIdNum().length() != 18
//            ) {
//                throw new ServiceException("证件号码不合法");
//            }
//        }
//        if (StrUtil.equals(pojo.getBaseInfo().getClientIdType(), "身份证")) {
////            boolean validCard = IdcardUtil.isValidCard(clientIdNum);
////            if (!validCard){
////                throw new ServiceException("身份证不合法");
////            }
//            //根据身份证号码获取性别、出生年月、年龄
//            //出生日期
//            String birth = IdcardUtil.getBirthByIdCard(clientIdNum);
//            //年龄
//            int age = 0;
//            try {
//                age = IdcardUtil.getAgeByIdCard(clientIdNum, new Date());
//            } catch (Exception e) {
//                throw new ServiceException("身份证出生日期不合法");
//            }
//            int gender = IdcardUtil.getGenderByIdCard(clientIdNum);
//            pojo.getBaseInfo().setClientAge((long) age);
//            try{
//                pojo.getBaseInfo().setClientBirthday(DateUtil.parse(birth, "yyyyMMdd"));
//            }catch (Exception e){
//                throw new ServiceException("身份证出生日期不合法");
//            }
//            pojo.getBaseInfo().setClientSex(gender == 1 ? "男" : "女");
//        }
//
//
//        if (!ObjectUtil.isEmpty(pojo.getLoanInfo().getYcIsDishonest())) {
//            String ycIsDishonest = pojo.getLoanInfo().getYcIsDishonest();
//            if (ycIsDishonest.length() > 2) {
//                throw new GlobalException("[是否失信被执行人]字段长度不能大于2");
//            }
//        }
//        if (!ObjectUtil.isEmpty(pojo.getLoanInfo().getYcIsLimitConsumption())) {
//            String ycIsDishonest = pojo.getLoanInfo().getYcIsLimitConsumption();
//            if (ycIsDishonest.length() > 2) {
//                throw new GlobalException("[是否被限制高消费]字段长度不能大于2");
//            }
//        }
//        pojo.getBaseInfo().setClientCensusRegister(IDCardUtils.getNativePlace(pojo.getBaseInfo().getClientIdNum()));
//
//        pojo.getBaseInfo().setAdministrativeBi(IDCardUtils.getAdministrativeCode(pojo.getBaseInfo().getHomeAddress()));
//
//        pojo.check();
//        if (pojo.getBaseInfo().getClientIdNum().length() != 15 && pojo.getBaseInfo().getClientIdNum().length() != 18) {
//            throw new GlobalException("证件号码不合法");
//        }
//        //计算账期
//        int accountPeriodNum = TimeUtils.calculateAccountPeriod(pojo.getLoanInfo().getOverdueStart());
//        pojo.getLoanInfo().setAccountPeriodNum(accountPeriodNum);
//        pojo.getLoanInfo().setAccountPeriod("M" + accountPeriodNum);
//
//
//        InfoContact myself = new InfoContact();
//        myself.setContactPhone(pojo.getBaseInfo().getClientPhone());
//        myself.setContactName(pojo.getBaseInfo().getClientName());
//        myself.setContactRelation("本人");
//        pojo.getContactInfos().add(0, myself);
//    }
//
//    /**
//     * 分配到案件管理库
//     * @param library
//     * @param baseInfo
//     * @param loanInfo
//     */
//    public void assignToCaseManage(Library library ,InfoBase baseInfo ,InfoLoan loanInfo, TeamActionRowResp res ){
//        CaseManage manage=new CaseManage();
//        manage.setCaseId(library.getTempId());
//        manage.setBatchNum(library.getBatchNo());
//        manage.setEntrustingPartyId(library.getEntrustingPartyId());
//        manage.setEntrustingPartyName(library.getEntrustingPartyName());
//        //产品ID
//        manage.setProductId(library.getProductId());
//        manage.setProductName(library.getProductName());
//        manage.setClientName(baseInfo.getClientName());
//        manage.setClientIdcard(baseInfo.getClientIdNum());
//        manage.setClientCensusRegister(baseInfo.getClientCensusRegister());
//        manage.setClientPhone(baseInfo.getClientPhone());
//        manage.setCaseState(CaseStates.UNASSIGNED.getCode());
//        manage.setAllocatedState(AllocatedStates.UNASSIGNED.getCode());
//        manage.setClientIdType(baseInfo.getClientIdType());
//        manage.setClientOverdueStart(loanInfo.getOverdueStart());
//        //委托金额
//        manage.setClientMoney(loanInfo.getEntrustMoney());
//        manage.setClientIdType(baseInfo.getClientIdType());
//        manage.setUid(baseInfo.getUid());
//        manage.setContractNo(loanInfo.getContractNo());
//        //剩余本金
//        manage.setClientResidualPrincipal(loanInfo.getResidualPrincipal());
//        manage.setAccountPeriod(loanInfo.getAccountPeriod());
//        manage.setEntrustingCaseDate(loanInfo.getEntrustingCaseDate());
//        manage.setArea(loanInfo.getCaseRegion());
//        manage.setTeamId(library.getTeamId());
//        manage.setOutsourcingTeamId(library.getTeamId());
//        manage.setAllocatedState(AllocatedStates.ASSIGNED.getCode());
//        manage.setImportSource(1);
//        manage.setCaseState(CaseStates.UNASSIGNED.getCode());
//        manage.setCaseMediateState(CaseStates.UNASSIGNED.getCode());
//        manage.setDelFlag(false);
//        manage.setCreateBy(library.getCreateBy());
//        manage.setCreateTime(DateUtils.getNowDate());
//        if (loanInfo.getEntrustMoney().compareTo(BigDecimal.ZERO)==0){
//            manage.setSettlementStatus(1);
//        }
//        res.getManages().add(manage);
////        manageService.insert(manage);
////        libraryService.updateAllocateCaseState(library.getId());
//
//    }

}
