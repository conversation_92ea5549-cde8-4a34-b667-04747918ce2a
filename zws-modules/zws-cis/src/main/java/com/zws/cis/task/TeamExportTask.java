package com.zws.cis.task;

import cn.hutool.core.util.ReUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.zws.cis.domain.TeamExportLog;
import com.zws.cis.service.IExportLogService;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.core.domain.ExportTaskData;
import com.zws.common.core.enums.ExportStartEnum;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.threadpool.WorkTask;
import com.zws.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * 导出任务
 *
 * <AUTHOR>
 * @date 2024/1/22 20:28
 */
@Slf4j
public class TeamExportTask implements WorkTask {

    private IExportLogService exportLogService= SpringUtil.getBean(IExportLogService.class);
    private RedisService redisService= SpringUtil.getBean(RedisService.class);
    /**
     * 导出数据
     */
    private ExportTaskData taskData;


    public TeamExportTask(ExportTaskData taskData) {
        this.taskData = taskData;
    }

    @Override
    public void runTask() throws Exception {
        TeamExportLog record=new TeamExportLog();
        record.setId(taskData.getExportLogId());
        try{
            log.info("导出任务{},开始,参数:{}",taskData.getExportLogId(), JSONUtil.toJsonStr(taskData));
            // 获取类的实例
            Class clazz=taskData.getClazz();
            String funName = taskData.getFunName();
            Object instance = SpringUtil.getBean(clazz);
            Object[] params = taskData.getParams();
            if (params==null){
                params=new Object[]{};
            }
            Class[] paramClass=new Class[params.length];
            for (int i = 0; i < params.length; i++) {
                paramClass[i]=params[i].getClass();
            }
            // 获取类中名为"executeMethodWithArgs"的方法，并指定参数类型
            Method method = clazz.getMethod(funName,paramClass);
            // 调用方法并传入参数
            Object invoke = method.invoke(instance, params);
            if ( invoke instanceof  String){
                record.setFileUrl((String) invoke);
            }else {
                record.setFileUrl(invoke.toString());
            }
            record.setExportStart(ExportStartEnum.SUCCESS.getCode());
        }catch (InvocationTargetException | GlobalException | ServiceException e){
            log.error("导出任务抛业务异常：",e);
            record.setExportStart(ExportStartEnum.FAIL.getCode());
            String message = e.getCause().getMessage();
            if (ReUtil.contains("[a-zA-Z]+",message)) {
                message = "网络错误！";
            }
            record.setFailMsg(message);
        } catch (Exception e){
            log.error("导出任务抛出异常：",e);
            record.setExportStart(ExportStartEnum.FAIL.getCode());
            String message = e.getCause().getMessage();
            log.error("导出任务抛出异常（Message）："+message);
            if (ReUtil.contains("[a-zA-Z]+",message)) {
                message = "网络错误！";
            }
            record.setFailMsg(message);
        }finally {
            log.info("导出任务{},完成",taskData.getExportLogId());
            exportLogService.update(record);
            redisService.deleteObject(CacheConstants.EXPORT_TASK_KEY+record.getId());
        }
    }

    @Override
    public void cancelTask() {

    }

    @Override
    public int getProgress() {
        return 0;
    }
}
