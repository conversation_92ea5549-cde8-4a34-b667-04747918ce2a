package com.zws.cis.pojo;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.system.api.domain.Library;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 案件的基本信息
 * case_library、case_info_base、case_info_loan 三表数据
 *
 * <AUTHOR>
 * @date ：Created in 2022/3/22 16:18
 */
@Data
public class CaseBaseInfo extends Library {

    /**
     * 案件ID
     */
    private Long id;

    /**
     * 姓名
     */
    private String clientName;
    /**
     * 姓名-加密
     */
    private String clientNameEnc;
    /**
     * 证件类型
     */
    private String clientIdType;
    /**
     * 身份证号
     */
    private String clientIdNum;

    /**
     * 身份证号-加密
     */
    private String clientIdNumEnc;

    /**
     * 户籍地
     */
    private String clientCensusRegister;
    /**
     * 户籍地-加密
     */
    private String clientCensusRegisterEnc;
    /**
     * 电话
     */
    private String clientPhone;
    /**
     * 电话-加密
     */
    private String clientPhoneEnc;
    /**
     * 性别
     */
    private String clientSex;
    /**
     * 年龄
     */
    private Long clientAge;
    /**
     * 出生日期
     */
    private Date clientBirthday;
    /**
     * 职业
     */
    private String occupation;
    /**
     * 婚姻状况
     */
    private String maritalStatus;
    /**
     * QQ
     */
    private String qq;
    /**
     * 微信
     */
    private String weixin;
    /**
     * 邮箱
     */
    private String mailbox;
    /**
     * 工作单位
     */
    private String placeOfWork;
    /**
     * 单位地址
     */
    private String workingAddress;
    /**
     * 户籍地址
     */
    private String registeredAddress;
    /**
     * 居住地址
     */
    private String residentialAddress;
    /**
     * 家庭地址
     */
    private String homeAddress;
    /**
     * 银行卡号
     */
    private String bankCardNumber;
    /**
     * 开户行-银行名称
     */
    private String bankName;
    /**
     * 资产编号
     */
    private String assetNo;


    private Long caseManageId;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 产品名
     */
    private String productName;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 贷款金额
     */
    private BigDecimal loanMoney;
    /**
     * 逾期开始时间
     */
    private Date overdueStart;

    /**
     * 资产包名称
     *
     * @return
     */
    private String packageName;
    /**
     * 案件区域
     */
    private String caseRegion;

    /**
     * 账期
     */
    private String accountPeriod;
    /**
     * 贷款机构
     */
    private String loanInstitution;
    /**
     * 贷款期数
     */
    private Integer loanPeriods;
    /**
     * 已还期数
     */
    private Integer alreadyPeriods;
    /**
     * 未还期数
     */
    private Integer notPeriods;
    /**
     * 委托金额
     */
    private BigDecimal entrustMoney;
    /**
     * 贷款本金
     */
    private BigDecimal loanPrincipal;
    /**
     * 滞纳金
     */
    private BigDecimal lateFee;
    /**
     * 服务费
     */
    private BigDecimal serviceFee;
    /**
     * 剩余本金
     */
    private BigDecimal residualPrincipal;
    /**
     * 利息
     */
    private BigDecimal interestMoney;
    /**
     * 罚息
     */
    private BigDecimal interestPenalty;
    /**
     * 实际到账金额-到手金额
     */
    private BigDecimal actualAmountReceived;
    /**
     * 逾期保费
     */
    private BigDecimal overduePremium;
    /**
     * 还款日
     */
    private Integer repaymentDate;
    /**
     * 每月还款金额
     */
    private BigDecimal repaymentMonthly;
    /**
     * 扣除后金额
     */
    private BigDecimal amountAfterDeduction;
    /**
     * 已催回金额
     */
    private BigDecimal amountCalledBack;
    /**
     * 应还金额
     */
    private BigDecimal amountDue;
    /**
     * 剩余应还
     */
    private BigDecimal remainingDue;
    /**
     * 借款日期
     */
    private Date loanDate;
    /**
     * 最后还款金额
     */
    private BigDecimal amountFinalRepayment;
    /**
     * 最后还款日期
     */
    private Date amountFinalDate;
    /**
     * 最后跟进时间
     */
    private Date latestFollowUpTime;
    /**
     * 委案日期
     */
    private Date entrustingCaseDate;
    /**
     * 退案日期
     */
    private Date returnCaseDate;
    /**
     * 其他费用
     */
    private BigDecimal otherFees;
    /**
     * 其他费用备注
     */
    private String otherFeesRemarks;
    /**
     * 逾期天数
     */
    private Integer ycOverdueDays;

    /**
     * 合同号
     */
    private String ycContractNo;
    /**
     * 五级分类
     */
    private String ycFiveLevel;
    /**
     * 币种
     */
    private String ycCurrencies;
    /**
     * 贷款意图
     */
    private String ycPurpose;
    /**
     * 贷款利率
     */
    private BigDecimal ycLendingRate;
    /**
     * 还款方式
     */
    private String ycRepaymentMethod;
    /**
     * 初始本息余额
     */
    private BigDecimal ycInterestBalance;
    /**
     * 垫付费用
     */
    private BigDecimal ycDisbursement;
    /**
     * 诉讼状态
     */
    private String ycLitigationStatus;
    /**
     * 是否失信被执行人
     * 0-否，1-是
     */
    private String ycIsDishonest;
    /**
     * 是否被限制高消费，0-否，1-是
     * 0-否，1-是
     */
    private String ycIsLimitConsumption;

    /**
     * 剩余应还本金
     */
    private BigDecimal syYhPrincipal;
    /**
     * 剩余应还利息
     */
    private BigDecimal syYhInterest;
    /**
     * 剩余应还费用
     */
    private BigDecimal syYhFees;
    /**
     * 剩余应还罚息
     */
    private BigDecimal syYhDefault;


    /**
     * 核销日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ycWriteDate;

    /**
     * 结清日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date clearDate;

    /**
     * 罚息利率
     */
    private BigDecimal ycDefaultRate;

    /**
     * UID
     */
    private String uid;

    public Long getYcOverdueDays() {
        if (this.overdueStart == null) {
            return null;
        }
        if (this.overdueStart.getTime() <= System.currentTimeMillis()) {
            long betweenDay = DateUtil.between(this.overdueStart, new Date(), DateUnit.DAY);
            return betweenDay;
        }
        return null;
    }
}
