package com.zws.cis.service.impl;

import com.zws.cis.mapper.CisSettingMapper;
import com.zws.cis.pojo.DesensitizationPojo;
import com.zws.cis.pojo.StatePojo;
import com.zws.cis.pojo.TeamExportPojo;
import com.zws.cis.pojo.WatermarkPojo;
import com.zws.cis.service.ICisSettingService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.enums.CommonExportEnum;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 机构系统管理设置--Service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/08 19:52
 */
@Service
public class CisSettingServiceImpl implements ICisSettingService {

    @Resource
    private CisSettingMapper mapper;
    @Autowired
    private RedisService redisService;

    /**
     * 根据团队id查询团队状态控制表信息
     *
     * @return
     */
    @Override
    public StatePojo findState(int createId) {
        return mapper.selectState(createId);
    }

    /**
     * 根据团队id查询团队脱敏信息状态
     *
     * @return
     */
    @Override
    public DesensitizationPojo selectDesensitization(int createId) {
        return mapper.selectDesensitization(createId);
    }

    /**
     * 根据团队id查询水印设置字段
     *
     * @return
     */
    @Override
    public WatermarkPojo selectWatermark(int createId) {
        return mapper.selectWatermark(createId);
    }

    /**
     * 修改设置状态（0:关闭,1:启用）
     *
     * @param state
     * @return
     */
    @Override
    public int updateStates(StatePojo state) {
        state.setModifier(SecurityUtils.getUsername());
        state.setModifyTime(new Date());
        return mapper.updateStates(state);
    }

    /**
     * 脱敏状态设置修改
     *
     * @param desensitization
     * @return
     */
    @Override
    public int updateDesensitization(DesensitizationPojo desensitization) {
        return mapper.updateDesensitization(desensitization);
    }

    /**
     * 修改水印设置字段
     *
     * @param watermark
     * @return
     */
    @Override
    public int updateWatermark(WatermarkPojo watermark) {
        if (watermark.getWatermarkOne().equals("无") && watermark.getWatermarkTwo().equals("无") &&
                watermark.getWatermarkThree().equals("无") && watermark.getWatermarkFour().equals("无")) {
            throw new GlobalException("水印设置至少选择一个样式");
        }
        return mapper.updateWatermark(watermark);
    }

    /**
     * 查询团队导出设置列表
     *
     * @param createId 团队ID
     * @return
     */
    @Override
    public List<TeamExportPojo> selectTeamExports(Integer createId) {
        if (createId == null) {
            return new ArrayList<>();
        }
        List<TeamExportPojo> teamExports = mapper.selectTeamExport(createId);
        if (teamExports.size() < CommonExportEnum.values().length) {
            Set<String> teamExportPerms = getTeamExportPerms(teamExports, null);
            //如果机构的导出按钮少于 枚举中的 则可能是新增了 枚举 或者是数据库里面被删除了
            for (CommonExportEnum exportEnum : CommonExportEnum.values()) {
                if (!teamExportPerms.contains(exportEnum.getPerms())) {
                    TeamExportPojo temp = new TeamExportPojo();
                    temp.setButtonName(exportEnum.getButtonName());
                    temp.setPerms(exportEnum.getPerms());
                    temp.setMenuName(exportEnum.getMenuName());
                    temp.setRemark(exportEnum.getRemark());
                    temp.setCreateId(createId);
                    insertTeamExportDefault(temp);
                    teamExports.add(temp);
                }
            }
        }
        return teamExports;
    }

    @Override
    public int insertTeamExport(TeamExportPojo teamExport) {
        //创建人
        teamExport.setFounder(SecurityUtils.getUsername());
        //创建时间
        teamExport.setCreationtime(new Date());
        //删除标志
        teamExport.setDeleteLogo(0);
        return mapper.insertTeamExport(teamExport);
    }

//    /**
//     * 编辑机构安全设置-导出设置-按钮开关
//     *
//     * @param teamExport
//     * @return
//     */
//    @Override
//    public int updateTeamExport(TeamExportPojo teamExport) {
//        teamExport.setModifier(SecurityUtils.getUsername());
//        teamExport.setModifyTime(new Date());
//
//        Integer id = teamExport.getId();
//        TeamExportPojo temp = mapper.getTeamExportById(id);
//        if (temp == null) {
//            return 0;
//        }
//        //删除缓存，等待使用的时候再获取
//        String redisKey = BaseConstant.TEAM_EXPORT + temp.getCreateId();
//        System.out.println("删除缓存：" + redisKey);
//        redisService.deleteObject(redisKey);
//        return mapper.updateTeamExport(teamExport);
//    }

    public Set<String> getTeamExportPerms(List<TeamExportPojo> teamExports, Integer exportStatus) {
        Set<String> openSet = new HashSet<>();
        for (TeamExportPojo temp : teamExports) {
            if (temp.getExportStatus() == null) {
                temp.setExportStatus(0);
            }
            if (exportStatus == null) {
                openSet.add(temp.getPerms());
            } else {
                if (exportStatus.equals(temp.getExportStatus())) {
                    openSet.add(temp.getPerms());
                }
            }
        }
        return openSet;
    }

    public int insertTeamExportDefault(TeamExportPojo teamExport) {
        teamExport.setExportStatus(0);
        return insertTeamExport(teamExport);
    }
}
