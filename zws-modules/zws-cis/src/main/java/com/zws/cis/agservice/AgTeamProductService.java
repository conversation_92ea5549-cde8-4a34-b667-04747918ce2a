package com.zws.cis.agservice;

import com.zws.cis.domain.TeamProduct;
import com.zws.cis.pojo.TeamLibraryPojo;
import com.zws.cis.pojo.TeamManagePojo;
import com.zws.cis.service.TeamProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
public class AgTeamProductService {

    @Autowired
    private TeamProductService productService;

    /**
     * 修改产品状态同步案件详情页是否展示数据
     * @param product
     */
    @Transactional(rollbackFor = Exception.class)
    public void changeProducts(TeamProduct product) {
        productService.update(product);
        TeamManagePojo manage = new TeamManagePojo();
        manage.setProductId(product.getId());
        manage.setDisplayData(Integer.parseInt(product.getState()));
        productService.updateByPrimaryManage(manage);
        TeamLibraryPojo library = new TeamLibraryPojo();
        library.setProductId(product.getId());
        library.setDisplayData(Integer.parseInt(product.getState()));
        productService.updateByPrimaryLibrary(library);
    }
}
