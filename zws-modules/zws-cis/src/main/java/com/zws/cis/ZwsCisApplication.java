package com.zws.cis;

import com.zws.common.security.annotation.EnableCustomConfig;
import com.zws.common.security.annotation.EnableZwsFeignClients;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * zws-cis (Case Import Service 案件导入服务)
 * <AUTHOR>
 */
@Slf4j
@EnableCustomConfig
@EnableZwsFeignClients
@EnableScheduling
@SpringBootApplication
@EnableAsync
@ComponentScan({"com.zws.system.api", "com.zws.common.*", "com.zws.*"})
@MapperScan(value = "com.zws.*.mapper")
public class ZwsCisApplication {

    public static void main(String[] args) {
        SpringApplication.run(ZwsCisApplication.class, args);
        log.info("(♥◠‿◠)ﾉﾞ  zws-cis (Case Import Service 案件导入服务) 启动成功   ლ(´ڡ`ლ) ");
    }

}
