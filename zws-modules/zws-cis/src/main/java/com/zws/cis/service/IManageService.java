package com.zws.cis.service;


/**
 * 委托案件池--service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/08 16:22
 */
public interface IManageService {

//    /**
//     * 插入数据
//     * @param entity
//     * @return  返回ID,返回0则表示插入数据失败
//     */
//    long insert(CaseManage entity);
//
//    /**
//     * 根据资产管理id删除案件
//     *
//     * @param assetManageId
//     * @return
//     */
//    int deletedByAssetManageId(Long assetManageId, Long teamId);
//
//    /**
//     * 根据案件id更新
//     * @param entity
//     * @return
//     */
//    int updateByCaseId(CaseManage entity);
//
//    /**
//     * 数据解密(姓名、身份证、电话、户籍、QQ、微信、邮箱、工作单位、工作地址、居住地址、家庭地址、银行卡)
//     * @param clz
//     */
//    void dateDecryptDetails(Object clz);
//
//
//
//    /**
//     * 案件ID获取
//     * @param caseId
//     * @return
//     */
//    CaseManage selectByCaseId(Long caseId);
}
