package com.zws.cis.service;

import com.zws.common.core.domain.sms.InfoContact;
import com.zws.system.api.model.LoginUser;

import java.util.List;

/**
 * 联系人信息
 * <AUTHOR>
 * @date ：2024年8月8日10:22:31
 */
public interface IInfoContactService {

    /**
     * 插入保存数据
     * @param entity
     * @return
     */
    long insert(InfoContact entity, LoginUser loginUser);

    /**
     * 批量插入数据
     * @param entitys
     * @return
     */
    int batchInsert(List<InfoContact> entitys);

    /**
     * 根据更改
     * @param entity
     */
    void updateById(InfoContact entity);

    /**
     * 根据案件ID 删除联系人
     * @param caseId
     */
    void deleteByCaseId(Long caseId);

    /**
     * 主键查询，未脱敏原数据
     * @param id
     * @return
     */
    InfoContact selectById(Long id);

    /**
     * 查询初始案件时的债权人联系信息
     * @param caseId
     * @return
     */
    InfoContact selectInitialInfoContact(Long caseId);

}
