package com.zws.cis.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.zws.cis.controller.request.TeamImportParam;
import com.zws.cis.domain.*;
import com.zws.cis.enums.ImportStartEnum;
import com.zws.cis.enums.TeamImportTypeEnum;
import com.zws.cis.service.*;
import com.zws.cis.task.*;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.core.domain.Option;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.threadpool.PoolManageMent;
import com.zws.common.core.threadpool.TaskManager;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.log.annotation.Log;
import com.zws.common.log.enums.BusinessType;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;

/**
 * 团队端导入
 *
 * <AUTHOR>
 * @date ：2024年8月9日10:17:38
 */
@Slf4j
@CrossOrigin
@RestController
@EnableTransactionManagement
@RequestMapping("/team/import")
public class TeamImportCaseController extends BaseController {

    @Autowired
    private TeamProductService productService;
    @Autowired
    private TeamManageService assetManageService;
    @Autowired
    private ITeamContractService contractService;
    @Autowired
    private TeamImportLogService importLogService;
    @Autowired
    private ITeamAccountService accountService;
    @Autowired
    private TeamImportCaseService  teamImportCaseService;
    @Autowired
    private RedisService redisService;

//    /**
//     * 获取导入批次号
//     *
//     * @param productId 产品ID
//     * @return
//     */
//    @GetMapping("/getImportBatchNum")
//    public AjaxResult getImportBatchNum(Long productId, Long id) {
//        TeamManage assetManage = assetManageService.selectById(id);
//        if (assetManage != null) {
//            return AjaxResult.success(assetManage.getBatchNum());
//        }
//        //转让方+年+j季度+产品类型简称+该类目下的流水序号
//        String importBatchNum = assetManageService.getImportUniqueNumber(productId);
//        return AjaxResult.success(importBatchNum);
//    }


//    /**
//     * 导入案件信息
//     *
//     * @return
//     */
//    @Log(title = "导入案件", businessType = BusinessType.IMPORT)
//    @PostMapping("/caseInfo")
//    public AjaxResult importCaseInfo(@RequestBody TeamImportParam param) throws Exception {
//        TimeInterval timer = DateUtil.timer();
////        限制文件后缀只能是xlsx
//        String originalFilename = param.getOriginalFilename();
//        if (ObjectUtils.isEmpty(originalFilename)) return AjaxResult.error("文件原名称不能为空");
//        String substring = originalFilename.substring(originalFilename.lastIndexOf("."));
//        if (!substring.equals(".xlsx")) return AjaxResult.error("文件格式必须为xlsx");
//
//        if (param.getProductId() == null) {
//            return AjaxResult.error("请选择产品");
//        }
////        if (StringUtils.isEmpty(param.getImportBatchNum())) {
////            return AjaxResult.error("请填写导入批次号");
////        }
//
//        if (param.getCreditorAccessDate() != null) {
//            long betweenDay = DateUtil.between(param.getCreditorAccessDate(), new Date(), DateUnit.DAY);
//            if (betweenDay < 0) {
//                return AjaxResult.error("债权准入日期不可大于本日");
//            }
//        }
//        if (ObjectUtils.isEmpty(param.getPackageNameArr())) {
//            return AjaxResult.error("资产包名称不能为空");
//        }
//        if (param.getPackageNameArr().length() > 20) {
//            return AjaxResult.error("资产包名称限制字数20位");
//        }
////        if (ObjectUtils.isEmpty(param.getOpenAccount()) || param.getAccountId() == null) {
////            return AjaxResult.error("请选择回款开户账号");
////        }
//        if (StringUtils.isEmpty(param.getFileUrl())) {
//            return AjaxResult.error("请上传文件");
//        }
//
//        TeamProduct product = productService.selectByPrimaryKey(param.getProductId());
//        if (product == null) {
//            return AjaxResult.error("未找到产品，请确定产品id");
//        }
//        String tmplateStr = product.getTemplate();
//        if (StringUtils.isEmpty(tmplateStr)) {
//            return AjaxResult.error("产品未设置导入模板");
//        }
//
//        LoginUser loginUser = SecurityUtils.getLoginUser();
//
//        String importBatchNum = assetManageService.getImportUniqueNumber(param.getProductId());
//        param.setImportBatchNum(importBatchNum);
//        param.setProduct(product);
//        ProductTemplate productTemplate = JSONUtil.toBean(tmplateStr, ProductTemplate.class);
//
//        ProductTemplate initTemp = productService.getInitTemplate();
//        initTemp.setAdditionalInfo(productTemplate.getAdditionalInfo());
//
//        ImportStartEnum istart = ImportStartEnum.ING;//导入中
//        BigDecimal entrustMoneyTotal = BigDecimal.ZERO;
//
//        TeamManage asset = new TeamManage();
//        asset.setBatchNum(importBatchNum);
//        asset.setAcquisitionDate(param.getAcquisitionDate());
//        asset.setAcquisitionCosts(param.getAcquisitionCosts());
//        asset.setTargetAmount(param.getTargetAmount());
//        asset.setBeginPeriod(param.getBeginPeriod());
//        asset.setEndPeriod(param.getEndPeriod());
//        asset.setRate(param.getRate());
//        asset.setExpectedRevenue(param.getExpectedRevenue());
//        asset.setSourceFileurl(param.getFileUrl());
//        asset.setSourceFilename(param.getOriginalFilename());
//        asset.setImportStart(istart.getCode());
//        asset.setCreditorAccessDate(param.getCreditorAccessDate());
//        asset.setEntrustMoneyTotal(entrustMoneyTotal);
//        asset.setOperatorId(SecurityUtils.getUserId());
//        asset.setProductId(product.getId());
//        asset.setOwnerId(product.getOwnerId());
//        asset.setEntrustMoneyId(param.getEntrustMoneySetupId());
//        asset.setReductionId(param.getReductionSetupId());
//        asset.setAutoReduction(param.isOpenAuotReductionSetup() ? BaseConstant.State_Use : BaseConstant.State_Forbidden);
//        //增加了字段：资产包名称 回款开户账号
//        asset.setPackageName(param.getPackageNameArr());
//        asset.setOpenAccount(param.getOpenAccount());
//        asset.setAccountId(param.getAccountId());
//        // 国瑞小程序需求，增加字段-债权公告日，债权公告（链接地址），签订日，协议编号，协议名称
//        asset.setCreditorAnnouncementDate(param.getCreditorAnnouncementDate());
//        asset.setCreditorAnnouncement(param.getCreditorAnnouncement());
//        asset.setProtocol(param.getProtocol());
//        asset.setProtocolNumber(param.getProtocolNumber());
//        asset.setSigningDate(param.getSigningDate());
//        asset.setClosingDate(param.getClosingDate());
//        //结清模板信息
//        asset.setSettleId(param.getSettleId());
//        asset.setSettleName(param.getSettleName());
//        asset.setTeamId(SecurityUtils.getTeamId().intValue());
//        long id = assetManageService.insert(asset);
//        if (id > 0) {
//            String key = CacheConstants.ASSET_PACKAGE_NAME;
//            redisService.deleteObject(key);
//        }
//
//        if (param.getContractIds() != null) {
//            for (Long contractId : param.getContractIds()) {
//                TeamContract contract = new TeamContract();
//                contract.setId(contractId);
//                contract.setAssetManageId(id);
//                contractService.updateById(contract, loginUser);
//            }
//        }
//
//
//        TeamImportLog importLog = new TeamImportLog();
//        importLog.setImportType(TeamImportTypeEnum.IMPORT_CASE.getCode());
//        importLog.setImportTime(DateUtils.getNowDate());
//        importLog.setImportStart(istart.getCode());
//        importLog.setOwnerId(asset.getOwnerId());
//        importLog.setProductId(asset.getProductId());
//        importLog.setImportBatchNum(asset.getBatchNum());
//        importLog.setSourceFileUrl(param.getFileUrl());//源文件URL
//        importLog.setOriginalFilename(param.getOriginalFilename());//原文件名
//        long logId = importLogService.insert(importLog, loginUser);
//
//        PoolManageMent pool = PoolManageMent.getInstance();
//        pool.init();
//
////        ImportCaseWorkTaskBig workTask = new ImportCaseWorkTaskBig(asset, logId, param, loginUser);
////        TaskManager.addTask(workTask);
//
//        teamImportCaseService.importCaseInfo(asset, logId, param, loginUser);
//
//        log.warn("累计计时:{} ms", timer.interval());
//        return AjaxResult.success(id);
//    }


    /**
     * 回款开户账号下拉选项
     *
     * @param
     * @return
     */
    @GetMapping("/selectOpenAccount")
    public AjaxResult selectOpenAccount() {
        Long teamId = SecurityUtils.getTeamId();
        List<TeamAccount> accounts = accountService.selectAccountById(teamId.intValue());
        List<Option> list = new ArrayList<>();
        for (TeamAccount account : accounts) {
            Option option = new Option();
            option.setCode(account.getId());
            option.setInfo(account.getAccountNumber());
            list.add(option);
        }
        return AjaxResult.success(list);
    }

    /**
     * 资产包名称下拉选项
     */
    @GetMapping("/selectPackageName")
    public AjaxResult selectPackageName() {
        Long teamId = SecurityUtils.getTeamId();
        List<String> packageName = assetManageService.selectPackageNameById(teamId);
        return AjaxResult.success(packageName);
    }

    /**
     * 导入更新案件
     *
     * @param param
     * @return
     */
    @Log(title = "导入（导入更新案件）", businessType = BusinessType.IMPORT)
    @PostMapping("/updateCase")
    public AjaxResult importUpdateCase(@RequestBody TeamImportParam param) {
        if (StringUtils.isEmpty(param.getFileUrl())) {
            return AjaxResult.error("请上传文件");
        }
//        限制文件后缀只能是xlsx
        String originalFilename = param.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) {
            return AjaxResult.error("缺少参数，源文件名");
        }
        String substring = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!substring.equals(".xlsx")) return AjaxResult.error("文件格式必须为xlsx");

        Long assetManageId = param.getId();
        TeamManage assetManage = assetManageService.selectById(assetManageId);
        if (assetManage == null) {
            return AjaxResult.error("id查询错误");
        }
        //File tempFile = FileDownloadUtils.downloadTempFile(param.getFileUrl());
        LoginUser loginUser = SecurityUtils.getLoginUser();

        param.setProductId(assetManage.getProductId());
        param.setOwnerId(assetManage.getOwnerId());

        TeamImportLog importLog = new TeamImportLog();
        importLog.setImportTime(DateUtils.getNowDate());
        importLog.setImportType(TeamImportTypeEnum.UPDATE_CASE.getCode());
        importLog.setImportStart(ImportStartEnum.ING.getInfo());
        importLog.setOwnerId(assetManage.getOwnerId());
        importLog.setProductId(assetManage.getProductId());
        importLog.setProductType(assetManage.getProductName());
        importLog.setSourceFileUrl(param.getFileUrl());
        importLog.setImportBatchNum(assetManage.getBatchNum());
        importLog.setOriginalFilename(param.getOriginalFilename());
        importLog.setTeamId(SecurityUtils.getTeamId(loginUser));
        long importLogId = importLogService.insert(importLog, loginUser);

        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();

        ImportUpdateCaseWorkTask workTask = new ImportUpdateCaseWorkTask(param, importLogId, importLog, loginUser);
        TaskManager.addTask(workTask);

        return AjaxResult.success();
    }

    /**
     * 重新导入案件
     *
     * @param param
     * @return
     */
    @Log(title = "导入（重新导入案件）", businessType = BusinessType.IMPORT)
    @PostMapping("/anewImportCaseInfo")
    public AjaxResult anewImportCaseInfo(@RequestBody TeamImportParam param) {
        if (StringUtils.isEmpty(param.getFileUrl())) {
            return AjaxResult.error("请上传文件");
        }
//        限制文件后缀只能是xlsx
        String originalFilename = param.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) {
            return AjaxResult.error("缺少参数，源文件名");
        }
        String substring = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!substring.equals(".xlsx")) return AjaxResult.error("文件格式必须为xlsx");

        Long assetManageId = param.getId();
        TeamManage asset = assetManageService.selectById(assetManageId);
        if (asset == null) {
            return AjaxResult.error("id查询错误");
        }
        TeamProduct product = productService.selectByPrimaryKey(asset.getProductId());
        if (product == null) {
            throw new ServiceException("未找到产品，请确定产品id");
        }
        param.setProduct(product);
        //File tempFile = FileDownloadUtils.downloadTempFile(param.getFileUrl());
        LoginUser loginUser = SecurityUtils.getLoginUser();

        ImportStartEnum istart = ImportStartEnum.ING;//导入中

        TeamImportLog importLog = new TeamImportLog();
        importLog.setImportType(TeamImportTypeEnum.RE_IMPORT_CASE.getCode());
        importLog.setImportTime(DateUtils.getNowDate());
        importLog.setImportStart(istart.getCode());
        importLog.setOwnerId(asset.getOwnerId());
        importLog.setProductId(asset.getProductId());
        importLog.setImportBatchNum(asset.getBatchNum());
        importLog.setSourceFileUrl(param.getFileUrl());//源文件URL
        importLog.setOriginalFilename(param.getOriginalFilename());//原文件名
        importLog.setTeamId(SecurityUtils.getTeamId(loginUser));
        long logId = importLogService.insert(importLog, loginUser);

        /*PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();*/

       /* ImportCaseWorkTask workTask = new ImportCaseWorkTask(asset, logId, param, rs, ems);
        TaskManager.addTask(workTask);*/

        ImportCaseWorkTaskBig workTask = new ImportCaseWorkTaskBig(asset, logId, param, loginUser);
        TaskManager.addTask(workTask);
        return AjaxResult.success(asset.getId());
    }


    /**
     * 批次导入联系人
     *
     * @return
     */
    @Log(title = "导入（批次导入联系人）", businessType = BusinessType.IMPORT)
    @PostMapping("/contactBatchNum")
    public AjaxResult importContactBatchNum(@RequestBody TeamImportParam param) {
        log.debug("批次导入联系人:{}", JSONUtil.toJsonStr(param));
        if (StringUtils.isEmpty(param.getFileUrl())) {
            return AjaxResult.error("请上传文件");
        }
//        限制文件后缀只能是xlsx
        String originalFilename = param.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) return AjaxResult.error("缺少参数，源文件名");
        String substring = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!substring.equals(".xlsx")) return AjaxResult.error("文件格式必须为xlsx");

        Long assetManageId = param.getId();

        TeamManage assetManage = assetManageService.selectById(assetManageId);
        if (assetManage == null) {
            return AjaxResult.error("id查询错误");
        }
        param.setImportBatchNum(assetManage.getBatchNum());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //File tempFile = FileDownloadUtils.downloadTempFile(param.getFileUrl());

        TeamImportLog importLog = new TeamImportLog();
        importLog.setImportTime(DateUtils.getNowDate());
        importLog.setImportType(TeamImportTypeEnum.CONTACT.getCode());
        importLog.setImportStart(ImportStartEnum.ING.getInfo());
        importLog.setOwnerId(assetManage.getOwnerId());
        importLog.setProductId(assetManage.getProductId());
        importLog.setFailFileUrl(param.getFileUrl());
        importLog.setOriginalFilename(param.getOriginalFilename());
        importLog.setTeamId(SecurityUtils.getTeamId(loginUser));
        if (StringUtils.isNotEmpty(param.getImportBatchNum())) {
            importLog.setImportBatchNum(param.getImportBatchNum());
        } else {
            importLog.setImportBatchNum(param.getOriginalFilename());
        }
        importLogService.insert(importLog, loginUser);

        /*PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();*/

        ImportContactWorkTask workTask = new ImportContactWorkTask(importLog, param, loginUser);
        TaskManager.addTask(workTask);

        return AjaxResult.success();
    }

    /**
     * 导入联系人-唯一案件ID匹配
     *
     * @return
     */
    @Log(title = "导入（导入联系人-唯一案件ID匹配）", businessType = BusinessType.IMPORT)
    @PostMapping("/contact")
    public AjaxResult importContact(@RequestBody TeamImportParam param) {
        log.debug("批次导入联系人:{}", JSONUtil.toJsonStr(param));
        if (StringUtils.isEmpty(param.getFileUrl())) {
            return AjaxResult.error("请上传文件");
        }
//        限制文件后缀只能是xlsx
        String originalFilename = param.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) return AjaxResult.error("缺少参数，源文件名");
        String substring = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!substring.equals(".xlsx")) return AjaxResult.error("文件格式必须为xlsx");

        if (StringUtils.isNull(param.getProductId())) {
            return AjaxResult.error("请选择产品");
        }
        param.setImportBatchNum(null);
        //File tempFile = FileDownloadUtils.downloadTempFile(param.getFileUrl());
        TeamProduct product = productService.selectByPrimaryKey(param.getProductId());
        if (StringUtils.isNull(param.getProductId())) {
            return AjaxResult.error("请选择产品");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();

        TeamImportLog importLog = new TeamImportLog();
        importLog.setImportTime(DateUtils.getNowDate());
        importLog.setImportType(TeamImportTypeEnum.CONTACT.getCode());
        importLog.setImportStart(ImportStartEnum.ING.getCode());
        importLog.setOwnerId(product.getOwnerId());
        importLog.setProductId(param.getProductId());
        importLog.setSourceFileUrl(param.getFileUrl());
        importLog.setOriginalFilename(param.getOriginalFilename());
        if (StringUtils.isNotEmpty(param.getImportBatchNum())) {
            importLog.setImportBatchNum(param.getImportBatchNum());
        } else {
            importLog.setImportBatchNum(param.getOriginalFilename());
        }
        importLogService.insert(importLog, loginUser);

        /*PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();*/

        ImportContactWorkTask workTask = new ImportContactWorkTask(importLog, param, loginUser);
        TaskManager.addTask(workTask);

        return AjaxResult.success();
    }

    /**
     * 根据资产方导入还款计划--操作栏
     * 只能导入指定的资产方-产品-案件对应的还款计划
     *
     * @param param
     * @return
     */
    @Log(title = "导入（根据资产方导入还款计划）", businessType = BusinessType.IMPORT)
    @PostMapping("/planByAsset")
    public AjaxResult importPlanByAsset(@RequestBody TeamImportParam param) {
        if (StringUtils.isEmpty(param.getFileUrl())) {
            return AjaxResult.error("请上传文件");
        }

//        限制文件后缀只能是xlsx
        String originalFilename = param.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) return AjaxResult.error("缺少参数，源文件名");
        String substring = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!substring.equals(".xlsx")) return AjaxResult.error("文件格式必须为xlsx");

        Long assetManageId = param.getId();
        TeamManage assetManage = assetManageService.selectById(assetManageId);
        if (assetManage == null) {
            return AjaxResult.error("id查询错误");
        }
        //File tempFile = FileDownloadUtils.downloadTempFile(param.getFileUrl());
        LoginUser loginUser = SecurityUtils.getLoginUser();

        param.setProductId(assetManage.getProductId());
        param.setOwnerId(assetManage.getOwnerId());

        TeamImportLog importLog = new TeamImportLog();
        importLog.setImportBatchNum(param.getImportBatchNum());
        importLog.setImportTime(DateUtils.getNowDate());
        importLog.setImportType(TeamImportTypeEnum.PLAN.getCode());
        importLog.setImportStart(ImportStartEnum.ING.getCode());
        importLog.setOwnerId(assetManage.getOwnerId());
        importLog.setProductId(assetManage.getProductId());
        importLog.setSourceFileUrl(param.getFileUrl());
        importLog.setOriginalFilename(param.getOriginalFilename());
        importLog.setImportBatchNum(assetManage.getBatchNum());
        importLog.setTeamId(SecurityUtils.getTeamId(loginUser));
        long importLogId = importLogService.insert(importLog, loginUser);

        /*PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();*/

        ImportPlantWorkTask workTask = new ImportPlantWorkTask(param, importLogId, loginUser);
        TaskManager.addTask(workTask);

        return AjaxResult.success();
    }

//    /**
//     * 批量导入还款计划
//     *
//     * @param param
//     * @return
//     */
//    @Log(title = "导入（批量导入还款计划）", businessType = BusinessType.IMPORT)
//    @PostMapping("/planBatch")
//    public AjaxResult importPlan(@RequestBody TeamImportParam param) {
//        if (StringUtils.isEmpty(param.getFileUrl())) {
//            return AjaxResult.error("请上传文件");
//        }
////        限制文件后缀只能是xlsx
//        String originalFilename = param.getOriginalFilename();
//        if (StringUtils.isEmpty(originalFilename)) return AjaxResult.error("缺少参数，源文件名");
//        String substring = originalFilename.substring(originalFilename.lastIndexOf("."));
//        if (!substring.equals(".xlsx")) return AjaxResult.error("文件格式必须为xlsx");
//
//        //if(StringUtils.isNull(param.getProductId()) ) return  AjaxResult.error("请选择产品id");
//        File tempFile = FileDownloadUtils.downloadTempFile(param.getFileUrl());
//        ExcelReader reader = ExcelUtil.getReader(tempFile);
//        List<Map<String, Object>> rows = reader.readAll();
//        if (ObjectUtils.isEmpty(rows)) {
//            throw new GlobalException("文件内容不能为空，请重新上传");
//        }
//
//        List<Map<String, Object>> readAll = reader.read(0, 0, 1);
//        Map<String, Object> map = readAll.get(0);
////        还款计划模板字段信息
//        String[] planTemplate = DownloadConstant.REPAYMENT_PLAN_TEMPLATE;
//        List<String> list = Arrays.asList(planTemplate);
//        int size = map.size();
//        if (size != planTemplate.length) {
//            return AjaxResult.error("请下载正确模板");
//        }
//        for (String key : map.keySet()) {
//            System.out.println(key);
//            if (!list.contains(key)) {
//                return AjaxResult.error("请下载正确模板");
//            }
//        }
//
//        LoginUser loginUser = SecurityUtils.getLoginUser();
//        param.setId(null);
//        TeamProduct product = productService.selectByPrimaryKey(param.getProductId());
//
//        TeamImportLog importLog = new TeamImportLog();
//        importLog.setImportTime(DateUtils.getNowDate());
//        importLog.setImportType(TeamImportTypeEnum.PLAN.getCode());
//        importLog.setImportStart(ImportStartEnum.ING.getCode());
//        importLog.setOwnerId(product != null ? product.getOwnerId() : null);
//        importLog.setProductId(product != null ? product.getId() : null);
//        importLog.setSourceFileUrl(param.getFileUrl());
//        importLog.setOriginalFilename(param.getOriginalFilename());
//        importLog.setImportBatchNum(param.getOriginalFilename());
//        importLog.setTeamId(SecurityUtils.getTeamId(loginUser));
//        long importLogId = importLogService.insert(importLog, loginUser);
//
//        /*PoolManageMent pool = PoolManageMent.getInstance();
//        pool.init();*/
//
//        ImportPlantWorkTask workTask = new ImportPlantWorkTask(param, importLogId, loginUser);
//        TaskManager.addTask(workTask);
//
//        return AjaxResult.success();
//    }


    /**
     * 导入催记-操作栏
     * 必须有资产管理id
     *
     * @param param
     * @return
     */
    @Log(title = "导入（导入催记）", businessType = BusinessType.IMPORT)
    @PostMapping("/urgeRecordByAsset")
    public AjaxResult importUrgeRecordByAsset(@RequestBody TeamImportParam param) {
        if (StringUtils.isEmpty(param.getFileUrl())) {
            return AjaxResult.error("请上传文件");
        }
//        限制文件后缀只能是xlsx
        String originalFilename = param.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) return AjaxResult.error("缺少参数，源文件名");
        String substring = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!substring.equals(".xlsx")) return AjaxResult.error("文件格式必须为xlsx");

        Long assetManageId = param.getId();
        TeamManage assetManage = assetManageService.selectById(assetManageId);
        if (assetManage == null) {
            return AjaxResult.error("id查询错误");
        }
        //File tempFile = FileDownloadUtils.downloadTempFile(param.getFileUrl());
        LoginUser loginUser = SecurityUtils.getLoginUser();

        param.setProductId(assetManage.getProductId());
        param.setOwnerId(assetManage.getOwnerId());

        TeamImportLog importLog = new TeamImportLog();
        importLog.setImportTime(DateUtils.getNowDate());
        importLog.setImportType(TeamImportTypeEnum.URGE.getCode());
        importLog.setImportStart(ImportStartEnum.ING.getCode());
        importLog.setOwnerId(assetManage.getOwnerId());
        importLog.setProductId(assetManage.getProductId());
        importLog.setSourceFileUrl(param.getFileUrl());
        importLog.setOriginalFilename(param.getOriginalFilename());
        importLog.setImportBatchNum(assetManage.getBatchNum());
        importLog.setTeamId(SecurityUtils.getTeamId(loginUser));
        long importLogId = importLogService.insert(importLog, loginUser);

        /*PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();*/

        ImportUrgeRecordWorkTask workTask = new ImportUrgeRecordWorkTask(param, importLogId, loginUser);
        TaskManager.addTask(workTask);

//        teamImportCaseService.importCaseInfo(param, importLogId, loginUser);

        return AjaxResult.success();
    }

    /**
     * 导入催记-批量操作
     *
     * @param param
     * @return
     */
    @Log(title = "导入（导入催记-批量操作）", businessType = BusinessType.IMPORT)
    @PostMapping("/urgeRecordBatch")
    public AjaxResult importUrgeRecord(@RequestBody TeamImportParam param) {

        if (StringUtils.isEmpty(param.getFileUrl())) {
            return AjaxResult.error("请上传文件");
        }
//        限制文件后缀只能是xlsx
        String originalFilename = param.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename)) return AjaxResult.error("缺少参数，源文件名");
        String substring = originalFilename.substring(originalFilename.lastIndexOf("."));
        if (!substring.equals(".xlsx")) return AjaxResult.error("文件格式必须为xlsx");

        LoginUser loginUser = SecurityUtils.getLoginUser();

        TeamImportLog importLog = new TeamImportLog();
        importLog.setOwnerId(null);
        importLog.setProductId(null);

        //File tempFile= FileDownloadUtils.downloadTempFile(param.getFileUrl());
        if (param.getProductId() != null) {
            TeamProduct product = productService.selectByPrimaryKey(param.getProductId());
            if (!ObjectUtils.isEmpty(product)) {
                importLog.setOwnerId(product.getOwnerId());
                importLog.setProductId(param.getProductId());
            }
        }

        param.setProductId(null);
        param.setOwnerId(null);

        importLog.setImportTime(DateUtils.getNowDate());
        importLog.setImportType(TeamImportTypeEnum.URGE.getCode());
        importLog.setImportStart(ImportStartEnum.ING.getCode());
        importLog.setSourceFileUrl(param.getFileUrl());
        importLog.setOriginalFilename(param.getOriginalFilename());
        importLog.setImportBatchNum(param.getOriginalFilename());
        long importLogId = importLogService.insert(importLog, loginUser);

        /*PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();*/

        ImportUrgeRecordWorkTask workTask = new ImportUrgeRecordWorkTask(param, importLogId, loginUser);
        TaskManager.addTask(workTask);

        return AjaxResult.success();
    }


}
