package com.zws.cis.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品信息表（saas系统机构产品）
 */
@Data
public class TeamProduct {

    /**
     * 产品id
     */
    private Long id;

    /**
     * 委托方id
     */
    private Long ownerId;

    /**
     * 委托方名称
     */
    private String ownerName;

    /**
     * 产品类型
     */
    private String name;

    /**
     * 产品简称
     */
    private String shortName;

    /**
     * 案件总量
     */
    private Integer caseTotalNum;

    /**
     * 委托总金额
     */
    private BigDecimal caseTotalMoney;

    /**
     * 批次数量
     */
    private Integer batchNumber;

    /**
     * 状态，0启用，非0禁用
     */
    private String state;

    /**
     * 模板
     */
    private String template;

    /**
     * 机构id
     */
    private Integer teamId;

    private String delFlag;

    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}