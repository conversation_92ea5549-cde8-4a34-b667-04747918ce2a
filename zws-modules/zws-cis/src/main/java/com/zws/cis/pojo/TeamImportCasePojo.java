package com.zws.cis.pojo;

import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.sms.InfoContact;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.*;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 导入案件
 * <AUTHOR>
 * @date ：Created in 2022/2/23 9:44
 */
@Data
public class TeamImportCasePojo {

    /**
     * 案件库信息
     */
    private Library library;
    /**
     * 基础信息
     */
    private InfoBase baseInfo;
    /**
     * 联系人信息
     */
    private List<InfoContact> contactInfos;
    /**
     * 贷款信息
     */
    private InfoLoan loanInfo=new InfoLoan();
    /**
     * 还款计划-Excel导入时的
     */
    private InfoPlan planInfo=new InfoPlan();
    /**
     * 还款计划列表
     */
    private List<InfoPlan> planInfos;
    /**
     * 附加信息
     */
    private List<InfoExtra> extraInfo=new ArrayList<>();


    public Library getLibrary() {
        if(library==null){
            library=new Library();
            library.setDelFlag(BaseConstant.DelFlag_Being);
            library.setCreateTime(DateUtils.getNowDate());
            library.setCreateBy(SecurityUtils.getUsername());
        }
        return library;
    }

    public InfoBase getBaseInfo() {
        if(baseInfo==null){
            baseInfo=new InfoBase();
            baseInfo.setCreateTime(DateUtils.getNowDate());
            baseInfo.setCreateBy(SecurityUtils.getUsername());
        }
        return baseInfo;
    }

    public List<InfoContact> getContactInfos() {
        if(contactInfos==null){
            contactInfos=new ArrayList<>();
        }
        return contactInfos;
    }

    public InfoLoan getLoanInfo() {
        if(loanInfo==null){
            loanInfo=new InfoLoan();
            loanInfo.setCreateTime(DateUtils.getNowDate());
            loanInfo.setCreateBy(SecurityUtils.getUsername());
        }
        return loanInfo;
    }

    public InfoPlan getPlanInfo() {
        if(planInfo==null){
            planInfo=new InfoPlan();
            planInfo.setCreateTime(DateUtils.getNowDate());
            planInfo.setCreateBy(SecurityUtils.getUsername());
        }
        return planInfo;
    }

    public List<InfoExtra> getExtraInfo() {
        if(extraInfo==null){
            extraInfo=new ArrayList<>();
        }
        return extraInfo;
    }

    /**
     * 检查参数
     */
    public void check() throws Exception {
        this.getLibrary().check();
        this.getBaseInfo().check();
        this.getLoanInfo().check();
        this.getPlanInfo().check();
    }

    @Override
    public String toString() {
        return "ImportCasePojo{" +
                "library=" + library +
                ", baseInfo=" + baseInfo +
                ", contactInfos=" + contactInfos +
                ", loanInfo=" + loanInfo +
                ", planInfo=" + planInfo +
                ", extraInfo=" + extraInfo +
                '}';
    }
}
