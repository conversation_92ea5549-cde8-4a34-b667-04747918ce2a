package com.zws.cis.service;

import com.zws.system.api.domain.InfoExtra;

import java.util.List;
import java.util.Map;

/**
 * 案件信息- 附加信息
 * <AUTHOR>
 * @date ：2024年8月7日19:46:00
 */
public interface IInfoExtraService {

    /**
     * 插入保存数据
     * @param entity
     * @return
     */
    long insert(InfoExtra entity) throws Exception;

    /**
     * 批量插入保存
     * @param extras
     * @return
     */
    int batchInsert(List<InfoExtra> extras);

    /**
     * 根据更改
     * @param entity
     */
    void updateById(InfoExtra entity) throws Exception;

    /**
     * 案件ID获取案件附加信息
     * @param caseId
     * @return
     */
    List<InfoExtra> selectByCaseId(Long caseId);
    /**
     * 案件ID以及字段key获取案件对应附加信息
     *
     * @param map
     * @return
     */
    List<InfoExtra> selectByCaseIdKey(Map<String,Object> map);
    /**
     * 条件查询列表
     * @param entity
     * @return
     */
    List<InfoExtra> selectList(InfoExtra entity);



}
