package com.zws.cis.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 资产案件库
 */
@Data
public class TeamLibraryPojo extends BaseEntity {
    /**
     * 案件ID
     */
    private Long id;
    /**
     * 资产管理id
     */
    private Long assetManageId;

    /**
     * 结清状态
     */
    private String settleState;

    /**
     * 案件分配状态
     */
    private String allocateCaseState;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 委托方ID
     */
    private Long entrustingPartyId;

    /**
     * 委托方名称
     */
    private String entrustingPartyName;

    private String delFlag;

    /**
     * 是否展示(0-展示；1-不展示)
     */
    private Integer displayData;

    /**
     * 姓名
     */
    private String clientName;
    /**
     * 证件类型
     */
    private String clientIdType;
    /**
     * 身份证号
     */
    private String clientIdNum;
    /**
     * 户籍地
     */
    private String clientCensusRegister;

    /**
     * 电话
     */
    private String clientPhone;

    /**
     * 逾期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date overdueStart;
    /**
     * 委托金额
     */
    private BigDecimal entrustMoney;

    /**
     * 和解协议
     */
    private String settleAgreementUrl;
    /**
     * 和解协议生成时间
     */
    private Date settleAgreementTime;

    public Date getSettleAgreementTime() {
        return settleAgreementTime;
    }

    public void setSettleAgreementTime(Date settleAgreementTime) {
        this.settleAgreementTime = settleAgreementTime;
    }

    public String getSettleAgreementUrl() {
        return settleAgreementUrl;
    }

    public void setSettleAgreementUrl(String settleAgreementUrl) {
        this.settleAgreementUrl = settleAgreementUrl;
    }



    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Integer getDisplayData() {
        return displayData;
    }

    public void setDisplayData(Integer displayData) {
        this.displayData = displayData;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSettleState() {
        return settleState;
    }

    public void setSettleState(String settleState) {
        this.settleState = settleState == null ? null : settleState.trim();
    }

    public String getAllocateCaseState() {
        return allocateCaseState;
    }

    public void setAllocateCaseState(String allocateCaseState) {
        this.allocateCaseState = allocateCaseState == null ? null : allocateCaseState.trim();
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo == null ? null : batchNo.trim();
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public Long getEntrustingPartyId() {
        return entrustingPartyId;
    }

    public void setEntrustingPartyId(Long entrustingPartyId) {
        this.entrustingPartyId = entrustingPartyId;
    }

    public String getEntrustingPartyName() {
        return entrustingPartyName;
    }

    public void setEntrustingPartyName(String entrustingPartyName) {
        this.entrustingPartyName = entrustingPartyName == null ? null : entrustingPartyName.trim();
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }


    public void check(){
        if(StringUtils.isEmpty(batchNo)) {
            throw new GlobalException("批次号不能为空");
        }
        if(this.productId==null) {
            throw new GlobalException("产品不能为空");
        }
        if(this.entrustingPartyId==null) {
            throw new GlobalException("委托方不能为空");
        }
    }

}
