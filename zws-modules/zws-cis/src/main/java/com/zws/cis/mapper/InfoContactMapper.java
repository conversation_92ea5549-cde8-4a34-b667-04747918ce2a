package com.zws.cis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zws.common.core.domain.sms.InfoContact;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InfoContactMapper extends BaseMapper<InfoContact> {

    int insert(InfoContact record);

    int batchInsert(@Param("records")List<InfoContact> record);

    int insertSelective(InfoContact record);

    InfoContact selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InfoContact record);

    List<InfoContact> selectList(InfoContact record);

    List<InfoContact> selectSearchList(@Param("caseId") Long caseId, @Param("search")String search);

    /**
     * 查询导入案件时初始的债权人联系信息
     * @param caseId
     * @return
     */
    InfoContact selectInitialInfoContact(@Param("caseId") Long caseId);
}
