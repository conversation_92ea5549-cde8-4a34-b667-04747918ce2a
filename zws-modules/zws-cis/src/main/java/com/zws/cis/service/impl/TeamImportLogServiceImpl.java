package com.zws.cis.service.impl;

import com.zws.cis.domain.TeamImportLog;
import com.zws.cis.mapper.TeamImportLogMapper;
import com.zws.cis.service.TeamImportLogService;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 导入案件日志信息--service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:08
 */
@Service
public class TeamImportLogServiceImpl implements TeamImportLogService {

    @Resource
    private TeamImportLogMapper mapper;

    /**
     * 插入数据
     *
     * @param entity
     * @return
     */
    @Override
    public long insert(TeamImportLog entity,LoginUser loginUser) {
        entity.setImportTime(DateUtils.getNowDate());
        entity.setOperatorId(SecurityUtils.getUserId(loginUser));
        entity.setOperatorName(SecurityUtils.getUsername(loginUser));
        entity.setTeamId(SecurityUtils.getTeamId(loginUser));
        int i = mapper.insert(entity);
        if (i > 0) {
            return entity.getId();
        }
        return 0;
    }

    /**
     * 更新
     *
     * @param entity
     */
    @Override
    public void updateById(TeamImportLog entity, LoginUser loginUser) {
        entity.setUpdateTime(DateUtils.getNowDate());
        entity.setUpdateBy(SecurityUtils.getUsername(loginUser));
        mapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    @Override
    public List<TeamImportLog> selectList(Map<String, Object> param) {
        List<TeamImportLog> list= mapper.selectList(param);
        return list;
    }
}
