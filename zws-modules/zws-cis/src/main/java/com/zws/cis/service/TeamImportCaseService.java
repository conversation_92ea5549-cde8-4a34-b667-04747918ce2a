package com.zws.cis.service;

import com.zws.cis.controller.request.TeamImportParam;
import com.zws.cis.domain.TeamImportLog;
import com.zws.cis.domain.TeamManage;
import com.zws.system.api.model.LoginUser;

import java.util.List;
import java.util.Map;

/**
 * 导入案件--service层
 *
 * @author: huang<PERSON>uf
 * @date ：Created in 2024/10/28 14:08
 */
public interface TeamImportCaseService {

    /**
     * 导入案件信息
     *
     * @param asset
     * @param logId
     * @param param
     * @param loginUser
     */
    void importCaseInfo(TeamManage asset, long logId, TeamImportParam param, LoginUser loginUser);

}
