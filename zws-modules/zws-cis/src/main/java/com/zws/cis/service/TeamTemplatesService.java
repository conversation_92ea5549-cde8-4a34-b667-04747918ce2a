package com.zws.cis.service;

import com.zws.cis.domain.TeamTemplates;

import java.util.List;
import java.util.Map;

/**
 * 常用模板--service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:10
 */
public interface TeamTemplatesService {


    /**
     * 查找通用模板
     *
     * @param id
     * @return
     */
    TeamTemplates selectById(Long id);

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    List<TeamTemplates> selectList(Map<String, Object> param);

    /**
     * 插入新增数据
     *
     * @param entity
     * @return 返回id
     */
    Long insert(TeamTemplates entity);

    /**
     * 编辑模版数据
     *
     * @param entity
     */
    void updateById(TeamTemplates entity);

    /**
     * 删除模版数据
     *
     * @param id
     */
    void deletedById(Long id);
}
