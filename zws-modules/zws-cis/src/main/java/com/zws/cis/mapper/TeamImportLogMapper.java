package com.zws.cis.mapper;

import com.zws.cis.domain.TeamImportLog;

import java.util.List;
import java.util.Map;

public interface TeamImportLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TeamImportLog record);

    int insertSelective(TeamImportLog record);

    TeamImportLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TeamImportLog record);

    int updateByPrimaryKey(TeamImportLog record);

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    List<TeamImportLog> selectList(Map<String, Object> param);
}