package com.zws.cis.service;

import com.zws.cis.pojo.DesensitizationPojo;
import com.zws.cis.pojo.StatePojo;
import com.zws.cis.pojo.TeamExportPojo;
import com.zws.cis.pojo.WatermarkPojo;

import java.util.List;

/**
 * 机构系统管理设置--Service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/08 19:52
 */
public interface ICisSettingService {

    /**
     * 根据团队id查询团队状态控制表信息
     *
     * @return
     */
    StatePojo findState(int createId);

    /**
     * 根据团队id查询团队脱敏信息状态
     *
     * @return
     */
    DesensitizationPojo selectDesensitization(int createId);

    /**
     * 根据团队id查询水印设置字段
     *
     * @return
     */
    WatermarkPojo selectWatermark(int createId);

    /**
     * 修改设置状态（0:关闭,1:启用）
     *
     * @param state
     * @return
     */
    int updateStates(StatePojo state);

    /**
     * 脱敏状态设置修改
     *
     * @param desensitization
     * @return
     */
    int updateDesensitization(DesensitizationPojo desensitization);

    /**
     * 修改水印设置字段
     *
     * @param watermark
     * @return
     */
    int updateWatermark(WatermarkPojo watermark);

    /**
     * 查询团队导出设置列表
     *
     * @param createId 团队ID
     * @return
     */
    List<TeamExportPojo> selectTeamExports(Integer createId);

    int insertTeamExport(TeamExportPojo teamExport);

//    /**
//     * 编辑机构安全设置-导出设置-按钮开关
//     *
//     * @param teamExport
//     * @return
//     */
//    int updateTeamExport(TeamExportPojo teamExport);
}
