package com.zws.cis.mapper;

import com.zws.cis.pojo.DesensitizationPojo;
import com.zws.cis.pojo.StatePojo;
import com.zws.cis.pojo.TeamExportPojo;
import com.zws.cis.pojo.WatermarkPojo;

import java.util.List;

/**
 * 机构系统管理设置--Dao层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/08 19:57
 */
public interface CisSettingMapper {

    /**
     * 根据团队id查询团队状态控制表信息
     *
     * @return
     */
    StatePojo selectState(int createId);

    /**
     * 根据团队id查询团队脱敏信息状态
     *
     * @return
     */
    DesensitizationPojo selectDesensitization(int createId);

    /**
     * 根据团队id查询水印设置字段
     *
     * @return
     */
    WatermarkPojo selectWatermark(int createId);

    /**
     * 修改设置状态（0:关闭,1:启用）
     *
     * @param state
     * @return
     */
    int updateStates(StatePojo state);

    /**
     * 脱敏状态设置修改
     *
     * @param desensitization
     * @return
     */
    int updateDesensitization(DesensitizationPojo desensitization);

    /**
     * 修改水印设置字段
     *
     * @param watermark
     * @return
     */
    int updateWatermark(WatermarkPojo watermark);

    /**
     * 根据团队id查询导出状态
     *
     * @return
     */
    List<TeamExportPojo> selectTeamExport(int createId);

    /**
     * 写入导出开关状态
     *
     * @return
     */
    int insertTeamExport(TeamExportPojo teamExport);

    TeamExportPojo getTeamExportById(Integer id);

    /**
     * 修改导出状态
     *
     * @return
     */
    int updateTeamExport(TeamExportPojo teamExport);
}
