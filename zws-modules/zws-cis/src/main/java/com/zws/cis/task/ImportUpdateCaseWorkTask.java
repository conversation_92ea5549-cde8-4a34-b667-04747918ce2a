package com.zws.cis.task;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.zws.cis.agservice.TeamImportCaseAgService;
import com.zws.cis.controller.request.TeamImportParam;
import com.zws.cis.domain.TeamImportLog;
import com.zws.cis.enums.ImportStartEnum;
import com.zws.cis.pojo.TeamImportCasePojo;
import com.zws.cis.service.IInfoContactService;
import com.zws.cis.service.ILibraryService;
import com.zws.cis.service.TeamImportLogService;
import com.zws.cis.service.TeamProductService;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.sms.InfoContact;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.threadpool.WorkTask;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.SpringUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.reflect.ReflexUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.domain.*;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 更新案件工作线程
 *
 * <AUTHOR>
 * @date ：Created in 2022/3/22 18:59
 */
@Slf4j
public class ImportUpdateCaseWorkTask implements WorkTask {

    private TeamImportCaseAgService importCaseAgservice = SpringUtils.getBean(TeamImportCaseAgService.class);
    private TeamProductService productService = SpringUtils.getBean(TeamProductService.class);
    private TeamImportLogService importLogService = SpringUtils.getBean(TeamImportLogService.class);
    private RemoteFileService fileService = SpringUtils.getBean(RemoteFileService.class);
    private ILibraryService libraryService = SpringUtils.getBean(ILibraryService.class);
    private IInfoContactService contactService = SpringUtils.getBean(IInfoContactService.class);



    private TeamImportParam param;//导入参数
    private Long importLogId;//导入日志id
    private TeamImportLog importLog;
    private LoginUser loginUser;//登录人信息

    public ImportUpdateCaseWorkTask(TeamImportParam param, Long importLogId, TeamImportLog importLog,LoginUser loginUser) {
        this.param = param;
        this.importLogId = importLogId;
        this.importLog = importLog;
        this.loginUser=loginUser;
    }

    @Override
    public void runTask() {
//        TeamImportLog importLog = new TeamImportLog();
//        importLog.setId(importLogId);
//        ImportStartEnum importStart = ImportStartEnum.ING;
//        try {
//            File tempFile = FileDownloadUtils.downloadTempFile(param.getFileUrl());
//            ExcelReader reader = ExcelUtil.getReader(tempFile);
//            List<Map<String, Object>> rows = reader.readAll();
//
//            //导入失败
//            List<Map<String, Object>> fails = new ArrayList<>();
//            //产品ID
//            Long productId = param.getProductId();
//            ProductTemplate template = productService.productTemplateById(productId);
//            //List<String> header = template.getTableHeader();
//            BigDecimal entrustMoneyTotal = BigDecimal.ZERO;
//            long successNum = 0;
//            for (Map<String, Object> row : rows) {
//                Map<String, Object> retMap = actionRow(row, template);
//                boolean start = (boolean) retMap.get("start");
//                if (start) {
//                    successNum++;
//                    BigDecimal entrustMoney = (BigDecimal) retMap.get("entrustMoney");
//                    if (entrustMoney != null) {
//                        entrustMoneyTotal = entrustMoneyTotal.add(entrustMoney);
//                    }
//                } else {//失败
//                    String errorMsg = (String) retMap.get("errorMsg");
//                    row.put("失败原因", errorMsg);
//                    fails.add(row);
//                }
//            }
//
//            if (fails.size() == 0) {
//                //全部成功
//                importStart = ImportStartEnum.SUCCESS;
//            } else {//有失败的数据
//                if (fails.size() == rows.size()) {
//                    //全部失败
//                    importStart = ImportStartEnum.FAIL;
//                } else {//部分失败
//                    importStart = ImportStartEnum.PARTIAL_SUCCESS;
//                }
//                importLog.setFailFileUrl(uploadFailFile(fails));
//            }
//            importLog.setSuccessNumber(successNum);
//            Long assetManageId = param.getId();
//
//            FileDownloadUtils.deletedTempFile(tempFile);
//        } catch (Exception e) {
//            e.printStackTrace();
//            importStart = ImportStartEnum.FAIL;
//        } finally {
//            importLog.setImportStart(importStart.getCode());
//            importLogService.updateById(importLog,loginUser);
//        }
    }

//    /**
//     * 执行 行
//     *
//     * @param row
//     * @param template
//     * @return
//     */
//    private Map<String, Object> actionRow(Map<String, Object> row, ProductTemplate template) {
//        Map<String, Object> map = new HashMap<>();
//        boolean start = false;
//        try {
//            String caseIdKey = "ID";
//            String phoneKey = "手机号码";
//            String nameKey = "姓名";
//            //获取模板
//            String caseIdStr = StrUtil.toString(row.get(caseIdKey));//案件ID
//            String phoneStr = StrUtil.toString(row.get(phoneKey));//手机号码
//            String nameStr = StrUtil.toString(row.get(nameKey));// 姓名
//            Long caseId = checkCaeId(caseIdStr);
//
//            InfoBase baseInfo = new InfoBase();
//            baseInfo.setCaseId(caseId);
//            baseInfo.setUpdateBy(importLog.getOperatorName());
//
//            InfoLoan loanInfo = new InfoLoan();
//            loanInfo.setCaseId(caseId);
//
//            //查询初始联系人本人信息、更新联系人信息
//            InfoContact infoContact = contactService.selectInitialInfoContact(caseId);
//            infoContact.setContactPhone(FieldEncryptUtil.encrypt(phoneStr));
//            infoContact.setContactName(FieldEncryptUtil.encrypt(nameStr));
//            contactService.updateById(infoContact);
//            List<InfoExtra> infoExtraList = new ArrayList<>();
//
//            for (String tempKey : row.keySet()) {
//                if (caseIdKey.equals(tempKey)) {
//                    continue;
//                }
//                Object val = row.get(tempKey);
//                TemplateTable tt = template.getTableField(tempKey);
//                if (tt != null) {
//                    switch (tt.getTableName()) {
//                        case "baseInfo"://案件基本信息
//                            ReflexUtils.setFieldVal(baseInfo, tt.getField(), val);
//                            break;
//                        case "caseInfo"://案件
//                            ReflexUtils.setFieldVal(loanInfo, tt.getField(), val);
//                            break;
//                        default:
//                            break;
//                    }
//                } else {
//                    ProductTemplateGroup additionalInfo = template.getAdditionalInfo();
//                    if (!ObjectUtils.isEmpty(additionalInfo)) {
//                        List<ProductTemplateItem> columns = additionalInfo.getColumns();
//                        if (!ObjectUtils.isEmpty(columns)) {
//                            for (ProductTemplateItem item : columns) {
//                                if (tempKey.equals(item.getLabel())) {
//                                    InfoExtra infoExtra = new InfoExtra();
//                                    infoExtra.setCaseId(caseId);
//                                    infoExtra.setExtraName(tempKey);
//                                    infoExtra.setExtraValue(ObjectUtil.toString(val));
//                                    infoExtraList.add(infoExtra);
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//
//
//            baseInfo.setBankCardNumber(loanInfo.getBankCardNumber());
//            baseInfo.setBankName(loanInfo.getBankName());
//            loanInfo.setYcFiveLevel(baseInfo.getYcFiveLevel());
//            TeamImportCasePojo pojo=new TeamImportCasePojo() ;
//            pojo.setBaseInfo(baseInfo);
//            pojo.setLoanInfo(loanInfo);
//            pojo.setExtraInfo(infoExtraList);
//
//            importCaseAgservice.importUpdateCase(pojo);
//
//            BigDecimal entrustMoney = loanInfo.getEntrustMoney();
//            map.put("entrustMoney", entrustMoney);
//            start = true;
//        } catch (Exception e) {
//            e.printStackTrace();
//            start = false;
//            String errorMsg = e.getMessage();
//            map.put("errorMsg", errorMsg);
//        }
//        map.put("start", start);
//        return map;
//    }

//    /**
//     * 检查案件是否合法
//     *
//     * @param caseIdStr
//     */
//    private Long checkCaeId(String caseIdStr) {
//        if (StringUtils.isEmpty(caseIdStr)) {
//            throw new GlobalException("案件ID 为空");
//        }
//        if (!StringUtils.isNumeric(caseIdStr)) {
//            throw new GlobalException("案件ID 错误");
//        }
//        Long caseId = Long.parseLong(caseIdStr);
//        Long teamId= SecurityUtils.getTeamId(loginUser);
//        List<Library> libraries = libraryService.selectListByCaseId(caseId,teamId);
//        if (libraries.size() == 0) {
//            throw new GlobalException("案件ID 错误");
//        }
//        for (Library temp : libraries) {
//            if (param.getId() != null) {//只能导入指定的id
//                System.out.println(temp.getProductId() + "----" + param.getProductId());
//                if (!temp.getProductId().equals(param.getProductId())) {
//                    throw new GlobalException("不是指定的资产方-产品");
//                }
//            } else {
//                // this.productIds.add(temp.getProductId());
//                //this.ownerIds.add(temp.getEntrustingPartyId());
//            }
//        }
//        return caseId;
//    }

//    /**
//     * 上传保存失败的案件
//     *
//     * @param fails 表格文件数据
//     * @return
//     * @throws Exception
//     */
//    private String uploadFailFile(List<Map<String, Object>> fails) {
//        try {
//            MultipartFile multipartFile = FileDownloadUtils.generateMultipartFile(fails);
//            R<SysFile> rest = fileService.upload(multipartFile);
//            if (rest.getCode() == R.SUCCESS) {
//                return rest.getData().getUrl();
//            }
//        } catch (Exception e) {
//            log.error(JSONUtil.toJsonStr(fails));
//            log.error("保存失败文件错误", e);
//        }
//        return "";
//    }


    @Override
    public void cancelTask() {

    }

    @Override
    public int getProgress() {
        return 0;
    }
}
