package com.zws.cis.service;

import com.zws.cis.domain.TeamProduct;
import com.zws.cis.pojo.TeamLibraryPojo;
import com.zws.cis.pojo.TeamManagePojo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 产品信息--service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:05
 */
public interface TeamProductService {


    /**
     * 获取资产方产品
     *
     * @param ownerId
     * @return
     */
    List<TeamProduct> selectListByOwnerId(Long ownerId, Integer teamId);

    /**
     * 删除
     *
     * @param id
     */
    void deleted(Long id);

    /**
     * 判断是否可以删除产品
     *
     * @param id
     * @return true 可以删除，false 不可删除
     */
    boolean checkCanDeleted(Long id);

    /**
     * 查询获取列表
     *
     * @param entity
     * @return
     */
    List<TeamProduct> selectList(TeamProduct entity);

    /**
     * 更新数据
     *
     * @param entity
     */
    void update(TeamProduct entity);

//    /**
//     * 插入新增
//     *
//     * @param entity
//     * @return
//     */
//    long insert(TeamProduct entity);

    /**
     * 根据产品id修改案件是否展示
     *
     * @param manage
     * @return
     */
    int updateByPrimaryManage(TeamManagePojo manage);

    /**
     * 根据产品id修改案件库是否展示
     *
     * @param library
     * @return
     */
    int updateByPrimaryLibrary(TeamLibraryPojo library);

//    /**
//     * 下载模板
//     *
//     * @param response
//     * @param id
//     */
//    void downloadTemplate(HttpServletResponse response, Long id) throws IOException;

//    /**
//     * 主键ID获取产品模板
//     *
//     * @param id
//     * @return
//     */
//    ProductTemplate productTemplateById(Long id);

    /**
     * id 查找
     *
     * @param id
     * @return
     */
    TeamProduct selectByPrimaryKey(Long id);

//    /**
//     * 获取初始模板信息
//     *
//     * @return
//     */
//    ProductTemplate getInitTemplate();

    /**
     * 查询产品名是否重复
     *
     * @param name
     * @return
     */
    boolean checkName(String name);
}
