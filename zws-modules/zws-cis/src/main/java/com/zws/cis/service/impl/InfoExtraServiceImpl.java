package com.zws.cis.service.impl;

import com.zws.cis.mapper.InfoExtraMapper;
import com.zws.cis.service.IInfoExtraService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.InfoExtra;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/2/16 9:59
 */
@Service
@Primary
public class InfoExtraServiceImpl implements IInfoExtraService {

    @Autowired
    private InfoExtraMapper infoExtraMapper;

    @Override
    public long insert(InfoExtra entity) throws Exception {
        if (StringUtils.isEmpty(entity.getExtraName())) {
            return 0;
        }
        entity.check();
        entity.setDelFlag(BaseConstant.DelFlag_Being);
        entity.setCreateBy(SecurityUtils.getUsername());
        entity.setCreateTime(DateUtils.getNowDate());
        int i = infoExtraMapper.insert(entity);
        if (i >= 0) {
            return entity.getId();
        }
        return 0;
    }

    @Override
    public int batchInsert(List<InfoExtra> extras) {
        return infoExtraMapper.batchInsert(extras);
    }

    @Override
    public void updateById(InfoExtra entity) throws Exception {
        entity.check();
//        entity.setUpdateBy(SecurityUtils.getUsername());
        entity.setUpdateTime(DateUtils.getNowDate());
        infoExtraMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public List<InfoExtra> selectByCaseId(Long caseId) {
        InfoExtra entity = new InfoExtra();
        entity.setCaseId(caseId);
        List<InfoExtra> list = infoExtraMapper.selectList(entity);
        return list;
    }

    /**
     * 案件ID以及字段key获取案件对应附加信息
     *
     * @param map
     * @return
     */
    @Override
    public List<InfoExtra> selectByCaseIdKey(Map<String, Object> map) {
        return infoExtraMapper.selectByCaseIdKey(map);
    }

    @Override
    public List<InfoExtra> selectList(InfoExtra entity) {
        List<InfoExtra> list = infoExtraMapper.selectList(entity);
        return list;
    }
}
