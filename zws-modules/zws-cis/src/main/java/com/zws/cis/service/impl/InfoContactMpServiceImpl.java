package com.zws.cis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zws.cis.mapper.InfoBaseMapper;
import com.zws.cis.mapper.InfoContactMapper;
import com.zws.cis.service.IInfoBaseMpService;
import com.zws.cis.service.IInfoContactMpService;
import com.zws.common.core.domain.sms.InfoContact;
import com.zws.system.api.domain.InfoBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date ：Created in 2022/2/15 15:31
 */
@Slf4j
@Service
public class InfoContactMpServiceImpl extends ServiceImpl<InfoContactMapper, InfoContact> implements IInfoContactMpService {


}
