package com.zws.cis.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.cis.enums.ImportStartEnum;
import com.zws.cis.enums.TeamImportTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * 导入案件日志记录表（saas系统机构导入日志）
 */
@Data
public class TeamImportLog {

    /**
     * 导入日志id
     */
    private Long id;

    /**
     * 导入批次号
     */
    private String importBatchNum;

    /**
     * 导入类型 0-导入案件，1-更新案件，2-重新导案，3-联系人，4-催记，5-还款计划，6-批量删除，7-批量停催，8-批量留案，9-批量退案
     */
    private String importType;

    /**
     * 成功数量
     */
    private Long successNumber;

    /**
     * 资产方id
     */
    private Long ownerId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 操作员id
     */
    private Long operatorId;

    /**
     * 操作员
     */
    private String operatorName;

    /**
     * 导入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date importTime;

    /**
     * 导入状态（0-导入中，1-导入成功，2-导入失败，3-部分成功）
     */
    private String importStart;

    /**
     * 原文件名
     */
    private String originalFilename;

    /**
     * 源文件地址
     */
    private String sourceFileUrl;

    /**
     * 失败文件地址
     */
    private String failFileUrl;

    /**
     * 多个资产方
     */
    private String ownerIds;

    /**
     * 多个产品
     */
    private String productIds;

    /**
     * 机构id
     */
    private Long teamId;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String updateBy;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 转让方名称
     */
    private String ownerName;

    /**
     * 资产包名称
     */
    private String packageName;

    /**
     * 导入状态-信息
     */
    private String importStartInfo;

    /**
     * 导入类型
     */
    private String importTypeInfo;

    public String getImportStartInfo() {
        ImportStartEnum startEnum = ImportStartEnum.valueOfCode(this.importStart);
        if (startEnum == null) {
            return "";
        }
        return startEnum.getInfo();
    }

    public String getImportTypeInfo() {
        TeamImportTypeEnum typeEnum = TeamImportTypeEnum.valueOfCode(this.importType);
        if (typeEnum == null) {
            return "";
        }
        return typeEnum.getInfo();
    }
}
