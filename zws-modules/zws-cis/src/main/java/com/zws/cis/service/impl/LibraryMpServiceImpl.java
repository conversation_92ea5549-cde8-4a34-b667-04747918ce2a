package com.zws.cis.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zws.cis.mapper.LibraryMapper;
import com.zws.cis.pojo.CaseBaseInfo;
import com.zws.cis.service.ILibraryMpService;
import com.zws.cis.service.ILibraryService;
import com.zws.cis.service.IManageService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.Library;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/2/15 15:31
 */
@Slf4j
@Service
public class LibraryMpServiceImpl extends ServiceImpl<LibraryMapper,Library> implements ILibraryMpService {


}
