package com.zws.cis.controller;

import com.zws.cis.agservice.TeamDownloadAgService;
import com.zws.cis.agservice.TeamExportAgService;
import com.zws.cis.controller.request.TeamManageParam;
import com.zws.cis.domain.TeamManage;
import com.zws.cis.service.TeamManageService;
import com.zws.common.core.constant.FileConstant;
import com.zws.common.core.enums.ExportClassEnum;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.poi.ExportUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.log.annotation.Log;
import com.zws.common.log.enums.BusinessType;
import com.zws.common.security.utils.SecurityUtils;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 下载导出
 *
 * <AUTHOR>
 * @date ：Created in 2022/1/27 17:56
 */
@CrossOrigin
@RestController
@RequestMapping("/team/download")
public class DownloadController extends BaseController {


    @Autowired
    private TeamManageService assetManageService;
    @Autowired
    private TeamExportAgService exportAgService;

//    /**
//     * 下载-导入联系人模板(竖向)
//     */
//    @Log(title = "下载导入联系人模板(竖向)", businessType = BusinessType.EXPORT)
//    @SneakyThrows
//    @PostMapping("/contactTemplateVertical")
//    public void downloadContactTemplateVertical(HttpServletResponse response) {
//        List<String> tableHeader = new ArrayList<>();
//        Collections.addAll(tableHeader, DownloadConstant.contactTemplateVertical);
//        ExportUtils.customExcelHeader(response, tableHeader, "导入联系人模板(竖向)" + FileConstant.getExcelSuffix(), "");
//    }
//
//    /**
//     * 下载-导入联系人模板(横向)
//     *
//     * @param response
//     */
//    @Log(title = "下载导入联系人模板(横向)", businessType = BusinessType.EXPORT)
//    @SneakyThrows
//    @PostMapping("/contactTemplateHorizontal")
//    public void downloadContactTemplateHorizontal(HttpServletResponse response) {
//        List<String> tableHeader = new ArrayList<>();
//        Collections.addAll(tableHeader, DownloadConstant.contactTemplateHorizontal);
//        ExportUtils.customExcelHeader(response, tableHeader, "导入联系人模板(横向)" + FileConstant.getExcelSuffix(), "");
//    }
//
//    /**
//     * 下载-导入还款计划模板
//     *
//     * @param response
//     */
//    @Log(title = "下载导入还款计划模板", businessType = BusinessType.EXPORT)
//    @SneakyThrows
//    @PostMapping("/downloadPlanTemplate")
//    public void downloadPlanTemplate(HttpServletResponse response) {
//        List<String> tableHeader = new ArrayList<>();
//        Collections.addAll(tableHeader, DownloadConstant.planTemplate);
//        ExportUtils.customExcelHeader(response, tableHeader, "导入还款计划模板" + FileConstant.getExcelSuffix(), "");
//    }
//
//    /**
//     * 下载-导入催记模板
//     *
//     * @param response
//     */
//    @Log(title = "下载导入催记模板", businessType = BusinessType.EXPORT)
//    @SneakyThrows
//    @PostMapping("/downloadUrgeRecordTemplate")
//    public void downloadUrgeRecordTemplate(HttpServletResponse response) {
//        List<String> tableHeader = new ArrayList<>();
//        Collections.addAll(tableHeader, DownloadConstant.urgeRecordTemplate);
//        ExportUtils.customExcelHeader(response, tableHeader, "导入催记模板" + FileConstant.getExcelSuffix(), "");
//    }
//
//    /**
//     * 资产管理- 下载现案件
//     *
//     * @param response
//     * @param param
//     */
//    @Log(title = "资产管理- 下载现案件", businessType = BusinessType.EXPORT)
//    @PostMapping("/existingCases")
//    public AjaxResult downloadExistingCases(HttpServletResponse response, TeamManageParam param) throws Exception {
//        Long assetManageId = param.getId();
//        TeamManage assetManage = assetManageService.selectById(assetManageId);
//        if (assetManage == null) {
//            throw new GlobalException("参数错误，查询失败");
//        }
//        if (ObjectUtils.isEmpty(assetManage.getTeamId())) throw new GlobalException("参数错误，查询失败");
//        if (assetManage.getTeamId() != SecurityUtils.getTeamId().intValue())
//            throw new GlobalException("参数错误，查询失败");
//        long totalCaeNum = 0;
//        if (assetManage.getCaseNumber() != null) {
//            totalCaeNum = assetManage.getCaseNumber();
//        }
//        /*if (totalCaeNum > 10000) {
//            throw new ServiceException("数据过大不支持下载，请联系管理员升级服务器配置");
//        }*/
//        param.setAssetManage(assetManage);
//        param.setTeamId(SecurityUtils.getTeamId().intValue());
//        //使用后台导出
//        Object[] params = new Object[]{param};
//        String fileName = exportAgService.exportTask(ExportClassEnum.CASE_EXPORT,
//                TeamDownloadAgService.class,
//                "downloadExistingCases", params, "现案件下载");
//        return AjaxResult.success("操作成功",fileName);
//    }
//
//    /**
//     * 资产管理-下载案源文件
//     *
//     * @param response
//     * @param param
//     */
//    @Log(title = "资产管理-下载案源文件", businessType = BusinessType.EXPORT)
//    @PostMapping("/casesSource")
//    public AjaxResult downloadCasesSource(HttpServletResponse response, TeamManageParam param) throws IOException {
//        Long assetManageId = param.getId();
//        TeamManage assetManage = assetManageService.selectById(assetManageId);
//        if (assetManage == null) {
//            throw new GlobalException("参数错误，查询失败");
//        }
//        if (ObjectUtils.isEmpty(assetManage.getTeamId())) throw new GlobalException("参数错误，查询失败");
//        if (assetManage.getTeamId() != SecurityUtils.getTeamId().intValue())
//            throw new GlobalException("参数错误，查询失败");
//
//        param.setAssetManage(assetManage);
//
//        //使用后台导出
//        Object[] params = new Object[]{param};
//        String fileName = exportAgService.exportTask(ExportClassEnum.CASE_EXPORT,
//                TeamDownloadAgService.class,
//                "downloadCasesSource", params, "案源下载");
//        return AjaxResult.success("操作成功",fileName);
//    }
//
//
//    //===财务管理=========================================================================
//
//    /**
//     * 财务管理-下载对账单模板
//     *
//     * @param response
//     */
//    @Log(title = "财务管理-下载对账单模板", businessType = BusinessType.EXPORT)
//    @PostMapping("/downloadBillTemplate")
//    public void downloadFinancialBillTemplate(HttpServletResponse response) throws IOException {
//        List<String> tableHeader = new ArrayList<>();
//        Collections.addAll(tableHeader, DownloadConstant.financialBillTemplate);
//        ExportUtils.customExcelHeader(response, tableHeader, "对账单模板" + FileConstant.getExcelSuffix(), "");
//    }
}
