package com.zws.cis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zws.system.api.domain.InfoPlan;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InfoPlanMapper extends BaseMapper<InfoPlan>{

    int deleteByPrimaryKey(Long id);

    int insert(InfoPlan record);


    int batchInsert(List<InfoPlan> record);

    /**
     * 写入还款计划最初数据
     *
     * @param record
     * @return
     */
    int insertInitial(InfoPlan record);

    int insertSelective(InfoPlan record);

    InfoPlan selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InfoPlan record);

    /**
     * 根据还款计划id修改更新罚息
     *
     * @param entity
     */
    int updateInfoPlanById(InfoPlan entity);

    List<InfoPlan> selectList(InfoPlan record);
}
