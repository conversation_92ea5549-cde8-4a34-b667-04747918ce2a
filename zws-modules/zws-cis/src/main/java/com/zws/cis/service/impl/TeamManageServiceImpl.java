package com.zws.cis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zws.cis.constant.CisCacheConstants;
import com.zws.cis.domain.TeamManage;
import com.zws.cis.enums.ImportStartEnum;
import com.zws.cis.mapper.TeamManageMapper;
import com.zws.cis.service.ILibraryService;
import com.zws.cis.service.IManageService;
import com.zws.cis.service.TeamManageService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 资产管理信息--service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:06
 */
@Service
public class TeamManageServiceImpl implements TeamManageService {

    @Resource
    private TeamManageMapper mapper;
    @Resource
    private ILibraryService libraryService;
    @Resource
    private IManageService manageService;
    @Resource
    private RedisService redisService;

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    @Override
    public List<TeamManage> selectList(Map<String, Object> param) {
        List<TeamManage> list = mapper.selectList(param);
        return list;
    }

    /**
     * id查询
     *
     * @param id
     * @return
     */
    @Override
    public TeamManage selectById(Long id) {
        return mapper.selectByPrimaryKey(id);
    }

    /**
     * 编辑
     *
     * @param assetManage
     * @return
     */
    @Override
    public int edit(TeamManage assetManage) {
        assetManage.setUpdateBy(SecurityUtils.getUsername());
        assetManage.setUpdateTime(DateUtils.getNowDate());
        int i = mapper.updateAssetManage(assetManage);
//        if (i > 0) {
//            String key = CacheConstants.ASSET_PACKAGE_NAME;
//            redisService.deleteObject(key);
//        }
        return i;
    }

    /**
     * ID删除
     *
     * @param id
     */
    @Override
    public AjaxResult deletedById(Long id) {
        if (!checkCanDeleted(id)) {
            AjaxResult.error("该批次还有案件在催收员名下，无法进行批量删除，请先将案件全部回收再进行删除操作");
        }
        TeamManage entity = new TeamManage();
        entity.setId(id);
        entity.setDelFlag(BaseConstant.DelFlag_Delete);
        updateById(entity);

        //删除 case_library、case_info_contact、case_info_extra、case_info_loan、case_info_plan、case_manage
//        libraryService.deleteByAssetManageId(id, SecurityUtils.getTeamId());
//        manageService.deletedByAssetManageId(id, SecurityUtils.getTeamId());

//        //更新缓存
//        String key=CacheConstants.ASSET_PACKAGE_NAME;
//        redisService.deleteObject(key);
        return AjaxResult.success();
    }

    /**
     * 查询是否可删除
     *
     * @param id
     * @return true-可删除，false-不可删除
     */
    @Override
    public boolean checkCanDeleted(Long id) {
        if (id == null) {
            return false;
        }
        TeamManage assetManage = mapper.selectByPrimaryKey(id);
        if (assetManage == null) {
            return false;
        }
        //查询是否还有批次案件未回收
        long num = mapper.selectCaseNumByBatchNum(assetManage.getBatchNum());
        return num > 0 ? false : true;
    }

    /**
     * 更新
     *
     * @param entity
     */
    @Override
    public void updateById(TeamManage entity) {
        entity.setBatchNum(null);
        entity.setCreateTime(null);
        entity.setUpdateBy(SecurityUtils.getUsername());
        entity.setUpdateTime(DateUtils.getNowDate());
        mapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 插入数据
     *
     * @param entity
     * @return
     */
    @Override
    public long insert(TeamManage entity) {
        if (StringUtils.isEmpty(entity.getImportStart())) {
            entity.setImportStart(ImportStartEnum.ING.getInfo());
        }
        if (StringUtils.isEmpty(entity.getBatchNum())) {
            throw new GlobalException("批次号不能为空");
        }
        if (entity.getProductId() == null) {
            throw new GlobalException("请选择导入的产品");
        }

        entity.setDelFlag(BaseConstant.DelFlag_Being);
        entity.setCreateBy(SecurityUtils.getUsername());
        entity.setCreateTime(DateUtils.getNowDate());
        entity.setOperatorId(SecurityUtils.getUserId());
        entity.setUpdateTime(DateUtils.getNowDate());
        entity.setUpdateBy(SecurityUtils.getUsername());
        int i = mapper.insert(entity);
        if (i > 0) {
            return entity.getId();
        }
        return 0;
    }

    @Override
    public List<String> selectPackageNameById(Long teamId) {
        String key= CisCacheConstants.CIS_TEAM_ASSET_PACKAGE_NAME+teamId;
//        if ( redisService.hasKey(key)){
//            return redisService.getCacheList(key);
//        }
        List<String> list= mapper.selectPackageNameById(teamId);
        if(CollectionUtil.isNotEmpty(list)){
            redisService.setCacheList(key,list);
            redisService.expire(key,30L, TimeUnit.MINUTES);
        }
        return list;
    }

    /**
     * 廖智套 2022-9-4 16:53:56
     * 查询统计-caseNum:案件总量，totalRemainingClaims:剩余债权总额,residualDebtPrincipal:剩余债权本金
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> selectCount(Map<String, Object> param) {
        Map<String, Object> map = mapper.selectCount(param);
        if (map == null) {
            map = new HashMap<>();
        }
        Long caseNum = ObjectUtil.isNull(map.get("caseNum")) ? 0L : (Long) map.get("caseNum");


        //BigDecimal caseMoney=ObjectUtil.isNull( map.get("caseMoney"))? BigDecimal.ZERO:(BigDecimal) map.get("caseMoney");
        //剩余债权总额
        BigDecimal remainingDue = ObjectUtil.isNull(map.get("remainingDue")) ? BigDecimal.ZERO : (BigDecimal) map.get("remainingDue");
        //剩余债权本金
        BigDecimal syYhPrincipal = ObjectUtil.isNull(map.get("syYhPrincipal")) ? BigDecimal.ZERO : (BigDecimal) map.get("syYhPrincipal");

        //map.put("caseMoney",caseMoney);
        map.put("caseNum", caseNum);
        map.put("totalRemainingClaims", remainingDue);//剩余债权总额
        map.put("residualDebtPrincipal", syYhPrincipal);  //剩余债权本金

        return map;
    }

    @Override
    public String getImportUniqueNumber(Long productId) {
        //规则：机构ID+转让方代号+年月日+四位顺序数
        Long teamId = SecurityUtils.getTeamId();
        String dateTime = DateUtils.dateTime();
        String ownerShortName = this.mapper.getOwnerShortNameByProductId(productId);
        String batch=StrUtil.format("{}{}{}", teamId, ownerShortName, dateTime);
        String key= StrUtil.format("{}", CisCacheConstants.CIS_TEAM_ASSET_BATCH_NUM, batch);
        Integer num=0;
//        if (redisService.hasKey(key)){
//            num= redisService.getCacheObject(key);
//        }

        while (true){
            num++;
            String numFormat = NumberUtil.decimalFormat("0000", num);
            String batchNum=StrUtil.format("{}{}",batch,numFormat);
            if (checkBatchNum(batchNum)){
                redisService.setCacheObject(key,num,24L, TimeUnit.HOURS);
                return batchNum;
            }
        }
    }

    /**
     * 检查批次号是否唯一
     * @param batchNum
     * @return true-不重复-可以用，false-已存在重复-不可用
     */
    private boolean checkBatchNum(String batchNum){
        return mapper.selectBatchNumCount(batchNum)==0;
    }
}
