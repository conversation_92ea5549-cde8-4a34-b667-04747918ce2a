package com.zws.cis.domain;

import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.web.domain.BaseEntity;
import com.zws.common.security.utils.SecurityUtils;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * 还款方式设置（机构）-实体类
 */
@Data
public class TeamSetup extends BaseEntity {

    /**
     * 机构id
     */
    private Integer teamId;

    /**
     * 还款方式id
     */
    private Long id;

    /**
     * 账户id
     */
    private Long accountId;
    /**
     * 还款方式（账户名称）
     */
    @NotEmpty(message = "还款方式不能空")
    @Size(max = 20, message = "还款方式名称过长")
    private String repaymentMethod;
    /**
     * 登记还款必填字段
     * 分号";"分隔
     */
    private String registerPayment;
    @NotEmpty(message = "登记还款必填字段不能为空")
    private String[] registerPaymentArr;


    /**
     * 自动对账必填字段
     * <p>
     * 分号";"分隔
     */
    private String reconciliation;

//    @NotEmpty(message = "自动对账必填字段不能为空")
    private String[] reconciliationArr;

    /**
     * 备注
     */
    @Size(max = 300, message = "备注内容过长")
    private String remark;
    /**
     * 状态，0启用，1禁用
     */
    private Integer state;

    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRepaymentMethod() {
        return repaymentMethod;
    }

    public void setRepaymentMethod(String repaymentMethod) {
        this.repaymentMethod = repaymentMethod == null ? null : repaymentMethod.trim();
    }

    public String getRegisterPayment() {
        return registerPayment;
    }

    public void setRegisterPayment(String registerPayment) {
        this.registerPayment = registerPayment == null ? null : registerPayment.trim();
    }

    public String getReconciliation() {
        return reconciliation;
    }

    public void setReconciliation(String reconciliation) {
        this.reconciliation = reconciliation == null ? null : reconciliation.trim();
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }

    public String splitStr() {
        return ";";
    }

    public void check() {
        //if(StringUtils.isEmpty(this.getRepaymentMethod()) ) throw new GlobalException("还款方式不能为空");
        //if(StringUtils.isEmpty(this.getReconciliation()) ) throw new GlobalException("自动对账字段不能为空");
        //if(StringUtils.isEmpty(this.getRegisterPayment()) ) throw new GlobalException("登记还款字段不能为空");
        if (this.getId() == null) {
            this.setDelFlag(BaseConstant.DelFlag_Being);
            this.setCreateBy(SecurityUtils.getUsername());
            this.setCreateTime(DateUtils.getNowDate());
        }
        this.setUpdateBy(SecurityUtils.getUsername());
        this.setUpdateTime(DateUtils.getNowDate());
    }

}