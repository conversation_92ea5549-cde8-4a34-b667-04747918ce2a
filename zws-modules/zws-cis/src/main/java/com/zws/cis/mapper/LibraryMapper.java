package com.zws.cis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zws.cis.pojo.CaseBaseInfo;
import com.zws.system.api.domain.Library;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface LibraryMapper  extends BaseMapper<Library>{

    int deleteByPrimaryKey(Long id);

    int insert(Library record);

    int insertSelective(Library record);

    Library selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Library record);

    int updateAllocateCaseStateByIdList(@Param("ids")List<Long> ids);

    /**
     * 根据案件id查询案件库案件
     * @param caseId
     * @param teamId
     * @return
     */
    List<Library> selectListByCaseId(@Param("caseId") Long caseId, @Param("teamId")Long teamId);

    /**
     * 根据资产管id更新案件库案件
     *
     * @param record
     * @return
     */
    int updateByAssetManageIdSelective(Library record);

    int updateByPrimaryKey(Library record);

    /**
     * 查询案件基本信息
     * @param param
     * @return
     */
    List<CaseBaseInfo> selectCaseBaseInfoList(Map<String,Object> param);
}
