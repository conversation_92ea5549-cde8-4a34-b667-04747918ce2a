package com.zws.cis.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 导出字段表
 */
@Data
public class TeamExportPojo {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 团队id
     */
    private Integer createId;

    /**
     * 状态
     * 0：关闭,1：开启
     */
    private Integer exportStatus;

    /**
     * 删除标志（0:未删除,1:已删除）
     */
    private Integer deleteLogo;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date creationtime;


    /**
     * 创建人
     */
    private String founder;

    /**
     * 权限标识
     */
    private String perms;
    /**
     * 菜单名称
     */
    private String menuName;
    /**
     * 按钮名称
     */
    private String buttonName;

    /**
     * 说明
     */
    private String remark;

}
