package com.zws.cis.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class StatePojo implements Serializable {

    /**
     * 状态表主键
     */
    private Integer id;

    /**
     * 团队表id
     */
    private Integer createId;

    /**
     * 白名单状态
     */
    private Integer whitelistStatus;

    /**
     * 信息脱敏状态
     */
    private Integer informationStatus;

    /**
     * 页面限制状态
     */
    private Integer restrictedState;

    /**
     * 水印设置状态
     */
    private Integer settingStatus;

    /**
     * 外访授权状态
     */
    private Integer authorizationStatus;

    /**
     * 实名认证状态
     */
    private Integer authenticationStatus;

    /**
     * 双重验证状态
     */
    private Integer securityVerificationStatus;

    /**
     * 导出设置状态
     */
    private Integer exportSettingStatus;
    /**
     * 推送小程序状态
     */
    private Integer pushAppStatus;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    public Integer getPushAppStatus() {
        return pushAppStatus;
    }

    public void setPushAppStatus(Integer pushAppStatus) {
        this.pushAppStatus = pushAppStatus;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCreateId() {
        return createId;
    }

    public void setCreateId(Integer createId) {
        this.createId = createId;
    }

    public Integer getWhitelistStatus() {
        return whitelistStatus;
    }

    public void setWhitelistStatus(Integer whitelistStatus) {
        this.whitelistStatus = whitelistStatus;
    }

    public Integer getInformationStatus() {
        return informationStatus;
    }

    public void setInformationStatus(Integer informationStatus) {
        this.informationStatus = informationStatus;
    }

    public Integer getRestrictedState() {
        return restrictedState;
    }

    public void setRestrictedState(Integer restrictedState) {
        this.restrictedState = restrictedState;
    }

    public Integer getSettingStatus() {
        return settingStatus;
    }

    public void setSettingStatus(Integer settingStatus) {
        this.settingStatus = settingStatus;
    }

    public Integer getAuthorizationStatus() {
        return authorizationStatus;
    }

    public void setAuthorizationStatus(Integer authorizationStatus) {
        this.authorizationStatus = authorizationStatus;
    }

    public Integer getAuthenticationStatus() {
        return authenticationStatus;
    }

    public void setAuthenticationStatus(Integer authenticationStatus) {
        this.authenticationStatus = authenticationStatus;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    //    public Integer getRestrictedState() {
//        return restrictedState == null ? 0 : restrictedState;
//    }
}
