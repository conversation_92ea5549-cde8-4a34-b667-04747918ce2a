package com.zws.cis.mapper;


import com.zws.cis.domain.TeamExportLog;

import java.util.List;

public interface ExportLogMapper {

    int deleteByPrimaryKey(Long id);

    int insert(TeamExportLog record);


    TeamExportLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TeamExportLog record);

    /**
     * 查询列表
     * @param record
     * @return
     */
    List<TeamExportLog> selectList(TeamExportLog record);

}
