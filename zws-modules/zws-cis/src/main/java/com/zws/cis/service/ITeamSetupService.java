package com.zws.cis.service;

import com.zws.cis.domain.TeamSetup;

import java.util.List;
import java.util.Map;

/**
 * 还款方式设置（机构）-service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/09 16:19
 */
public interface ITeamSetupService {

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    List<TeamSetup> selectList(Map<String, Object> param);

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    TeamSetup selectById(Long id);

    /**
     * 插入保存数据
     *
     * @param entity
     * @return
     */
    long insert(TeamSetup entity);

    /**
     * 编辑
     *
     * @param entity
     */
    void updateById(TeamSetup entity);
}
