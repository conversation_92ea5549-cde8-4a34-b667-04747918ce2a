package com.zws.cis.pojo;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.enums.AllocatedStates;
import com.zws.common.core.enums.CaseStates;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 案件管理库
 */

@Data
public class TeamManagePojo extends BaseEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 案件ID
     */
    @NotNull(message = "案件ID不能为空")
    private Long caseId;
    /**
     * 资产管理id
     */
    private Long assetManageId;
    /**
     * 合同编号(借据号)
     */
    private String contractNo;

    /**
     * 分配状态
     */
    private String allocatedState;
    /**
     * 案件状态
     */
    private String caseState;

    /**
     * (调解流程)案件状态  0-未分配，1-已分配，2-停催，3-留案，4-退案 5-回收案件，6-案件结清
     */
    private String caseMediateState;

    /**
     * 案件结清状态 （0-未结清，1-已结清）
     */
    private Integer settlementStatus;
    /**
     * 委托方ID
     */
    private Long entrustingPartyId;
    /**
     * 委托方名称
     */
    private String entrustingPartyName;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 批次号
     */
    @NotBlank(message = "批次号不能为空")
    private String batchNum;

    /**
     * 委案批号
     */
    private String entrustingCaseBatchNum;

    /**
     * 委外团队ID
     */
    private Long outsourcingTeamId;

    /**
     * 委外团队名称
     */
    private String outsourcingTeamName;

    /**
     * 客户名称
     */
    @NotBlank(message = "姓名不能为空")
    private String clientName;

    /**
     * 证件类型
     */
    private String clientIdType;

    /**
     * 客户身份号
     */
    @NotBlank(message = "身份证号不能为空")
    private String clientIdcard;

    /**
     * 客户户籍
     */
    @NotBlank(message = "户籍不能不能为空")
    private String clientCensusRegister;

    /**
     * 客户电话
     */
    @NotBlank(message = "手机号不能为空")
    private String clientPhone;

    /**
     * 委托金额
     */
    private BigDecimal clientMoney;
    /**
     * 剩余本金
     */
    private BigDecimal clientResidualPrincipal;
    /**
     * 逾期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date clientOverdueStart;
    /**
     * 账期
     */
    private String accountPeriod;
    /**
     * 账期-数字
     */
    private Integer accountPeriodNum;
    /**
     * 跟进状态
     */
    private String followUpState;
    /**
     * 开始跟进时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date followUpStart;
    /**
     * 最后跟进时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date followUpAst;

    /**
     * 未跟进天数，非数据库字段，动态生成
     */
    private long notFollowUpDay;


    /**
     * 委案日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entrustingCaseDate;
    /**
     * 委案日期--(yyyy-MM-dd HH:mm:ss)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date entrustingCaseDateHms;
    /**
     * 退案日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date returnCaseDate;
    /**
     * 地区
     */
    private String area;
    /**
     * 标签
     */
    private String label;
    /**
     * 资产端标签
     */
    private String labelAsset;
    /**
     * 资产端标签信息
     */
    private String labelAssetInfo;

    /**
     * 催收员ID
     */
    private Long odvId;
    /**
     * 催收员名称
     */
    private String odvName;
    /**
     * 催收状态
     */
    private String urgeState;

    /**
     * 调解员名称
     */
    private String mediatorName;
    /**
     * 调解员ID
     */
    private Long mediatorId;

    /**
     * 分配时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date allocatedTime;

    /**
     * 催记权限，0-不能查看历史催记，1-查看全部历史催记，2-只能查看本团队历史催记
     */
    private String urgePower;

    /**
     * 1.催收记录类别（包括催收记录、协催记录、外访记录）
     * 2.调执记录类别（包括调执记录、保全记录、收费记录、电话记录、物流记录）
     * 3.调诉执记录类别（包括调诉执记录、保全记录、收费记录、电话记录、物流记录）
     * 4.公共记录类别（回款记录、减免记录、投诉记录、便签记录、通话记录、短信记录）
     *
     * 历史权限
     * A1催收记录类别-本机构、A2调执记录类别-本机构、A3调诉执记录类别-本机构、
     * B1催收记录类别-全部机构、B2调执记录类别-全部机构、B3调诉执记录类别-全部机构
     *
     * 可多选记录类别，则拼接使用，例："A1A3"、"B1B2B3"
     *
     */
    private String historyPower;

    /**
     * 删除标记，0表示存在，非0表示删除
     */
    private Boolean delFlag;

    /**
     * 逾期天数，非数据库字段，动态生成
     */
    private Long clientOverdueDays;

    /**
     * 分配状态
     */
    private String allocatedStateStr;

    /**
     * 案件状态显示信息,非数据库字段，动态匹配
     */
    private String caseStateStr;

    /**
     * 是否展示(0-展示；1-不展示)
     */
    private Integer displayData;

    /**
     * 工单状态(null 表示未提交过工单)
     */
    private Integer orderStatusCode;

    /**
     * 合同号
     */
    private String ycContractNo;

    /**
     * 资产编号
     */
    private String assetNo;

    /**
     * 案件文件路径
     */
    private List<String> fileInformation;

    /**
     * 数据解密key
     */
    private String decryptKey;

    /**
     * 剩余应还债权总额(case_info_loan表字段)
     *
     * @return
     */
    private BigDecimal remainingDue;

    /**
     * 剩余债权本金(case_info_loan表字段)
     *
     * @return
     */
    private BigDecimal syYhPrincipal;

    /**
     * 委案天数
     */
    private Integer commissionedDays;

    /**
     * 结清日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date clearDate;

    /**
     * 分案时的回款目标%(非数据库字段，用于查询、传值)
     * 目标回款率
     */
    private BigDecimal targetBackMoney;

    /**
     * 资产包名称
     */
    private String packageName;

    /**
     * UID
     */
    private String uid;

    /**
     * 团队类别(从字典表获取)
     */
    private String category;

    /**
     * 机构一级类别（催收类/调诉类）
     */
    private String teamLevelType;


    /**
     * 上手机构ID
     * 分配给上一个机构的机构ID
     * --不用
     */
    private Long agencySourceId;

    /**
     * 调诉阶段
     * 调诉端分案到催员后默认值 =>未提交立案
     */
    private String disposeStage;

    /**
     * 调解阶段
     * 调诉端分案到催员后默认值 => 调解材料
     */
    private String mediatedStage;

    /**
     * 是否诉保（0未保全、1已保全）
     * 调诉端分案到催员后默认值 => 未申请诉保0
     */
    private Integer isFreeze;


    public Integer getDisplayData() {
        return displayData;
    }

    public void setDisplayData(Integer displayData) {
        this.displayData = displayData;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCaseId() {
        return caseId;
    }

    public void setCaseId(Long caseId) {
        this.caseId = caseId;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo == null ? null : contractNo.trim();
    }

    public String getAllocatedState() {
        return allocatedState;
    }

    public void setAllocatedState(String allocatedState) {
        this.allocatedState = allocatedState == null ? null : allocatedState.trim();
    }

    public String getCaseState() {
        return caseState;
    }

    public void setCaseState(String caseState) {
        this.caseState = caseState == null ? null : caseState.trim();
    }

    public Long getEntrustingPartyId() {
        return entrustingPartyId;
    }

    public void setEntrustingPartyId(Long entrustingPartyId) {
        this.entrustingPartyId = entrustingPartyId;
    }

    public String getEntrustingPartyName() {
        return entrustingPartyName;
    }

    public void setEntrustingPartyName(String entrustingPartyName) {
        this.entrustingPartyName = entrustingPartyName == null ? null : entrustingPartyName.trim();
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getBatchNum() {
        return batchNum;
    }

    public void setBatchNum(String batchNum) {
        this.batchNum = batchNum == null ? null : batchNum.trim();
    }

    public String getEntrustingCaseBatchNum() {
        return entrustingCaseBatchNum;
    }

    public void setEntrustingCaseBatchNum(String entrustingCaseBatchNum) {
        this.entrustingCaseBatchNum = entrustingCaseBatchNum == null ? null : entrustingCaseBatchNum.trim();
    }

    public Long getOutsourcingTeamId() {
        return outsourcingTeamId;
    }

    public void setOutsourcingTeamId(Long outsourcingTeamId) {
        this.outsourcingTeamId = outsourcingTeamId;
    }

    public String getOutsourcingTeamName() {
        if (!StringUtils.equals(this.allocatedState, AllocatedStates.ASSIGNED.getCode())) {
            return null;
        }
        return outsourcingTeamName;
    }

    public void setOutsourcingTeamName(String outsourcingTeamName) {
        this.outsourcingTeamName = outsourcingTeamName == null ? null : outsourcingTeamName.trim();
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName == null ? null : clientName.trim();
    }

    public String getClientIdcard() {
        return clientIdcard;
    }


    public void setClientIdcard(String clientIdcard) {
        this.clientIdcard = clientIdcard == null ? null : clientIdcard.trim();
    }

    public String getClientCensusRegister() {
        return clientCensusRegister;
    }

    public void setClientCensusRegister(String clientCensusRegister) {
        this.clientCensusRegister = clientCensusRegister == null ? null : clientCensusRegister.trim();
    }

    public String getClientPhone() {
        return clientPhone;
    }

    public void setClientPhone(String clientPhone) {
        this.clientPhone = clientPhone == null ? null : clientPhone.trim();
    }

    public BigDecimal getClientMoney() {
        return clientMoney;
    }

    public void setClientMoney(BigDecimal clientMoney) {
        this.clientMoney = clientMoney;
    }

    public BigDecimal getClientResidualPrincipal() {
        return clientResidualPrincipal;
    }

    public void setClientResidualPrincipal(BigDecimal clientResidualPrincipal) {
        this.clientResidualPrincipal = clientResidualPrincipal;
    }

    public Date getClientOverdueStart() {
        return clientOverdueStart;
    }

    public void setClientOverdueStart(Date clientOverdueStart) {
        this.clientOverdueStart = clientOverdueStart;
    }

    public String getAccountPeriod() {
        return accountPeriod;
    }

    public void setAccountPeriod(String accountPeriod) {
        this.accountPeriod = accountPeriod;
    }

    public String getFollowUpState() {
        return followUpState;
    }

    public void setFollowUpState(String followUpState) {
        this.followUpState = followUpState == null ? null : followUpState.trim();
    }

    public Date getFollowUpStart() {
        return followUpStart;
    }

    public void setFollowUpStart(Date followUpStart) {
        this.followUpStart = followUpStart;
    }

    public Date getFollowUpAst() {
        return followUpAst;
    }

    public void setFollowUpAst(Date followUpAst) {
        this.followUpAst = followUpAst;
    }

    public Date getEntrustingCaseDate() {
        return entrustingCaseDate;
    }

    public void setEntrustingCaseDate(Date entrustingCaseDate) {
        this.entrustingCaseDate = entrustingCaseDate;
    }

    public Date getReturnCaseDate() {
        return returnCaseDate;
    }

    public void setReturnCaseDate(Date returnCaseDate) {
        this.returnCaseDate = returnCaseDate;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area == null ? null : area.trim();
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label == null ? null : label.trim();
    }

    public Long getOdvId() {
        return odvId;
    }

    public void setOdvId(Long odvId) {
        this.odvId = odvId;
    }

    public String getOdvName() {
        return odvName;
    }

    public void setOdvName(String odvName) {
        this.odvName = odvName == null ? null : odvName.trim();
    }

    public Date getAllocatedTime() {
        return allocatedTime;
    }

    public void setAllocatedTime(Date allocatedTime) {
        this.allocatedTime = allocatedTime;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }


    public String getUrgePower() {
        return urgePower;
    }

    public void setUrgePower(String urgePower) {
        this.urgePower = urgePower;
    }

    public Long getClientOverdueDays() {
//        if (this.clientOverdueStart == null) return 0L;
//        if (this.settlementStatus != null) {
//            if (this.settlementStatus == 1) {
//                if(this.clearDate!=null) {
//                    if (this.clientOverdueStart.getTime() <= this.clearDate.getTime()) {
//                        return DateUtil.between(this.clientOverdueStart, this.clearDate, DateUnit.DAY);
//                    } else {
//                        return null;
//                    }
//                }
//
//            }  //案件已结清逾期天数为空
//        }
//        if (this.clientOverdueStart.getTime() <= new Date().getTime()) {
//            long betweenDay = DateUtil.between(this.clientOverdueStart, new Date(), DateUnit.DAY);
//            return betweenDay;
//        }
        return clientOverdueDays == null ? 0L : clientOverdueDays;
    }


    public String getCaseStateStr() {
        CaseStates caseStateS = CaseStates.valueOfCode(this.caseState);
        if (caseStateS == null) {
            return "";
        }
        return caseStateS.getInfo();
    }

    public String getAllocatedStateStr() {
        AllocatedStates caseStateS = AllocatedStates.valueOfCode(this.allocatedState);
        if (caseStateS == null) {
            return "";
        }
        return caseStateS.getInfo();
    }

    public long getNotFollowUpDay() {
        //未跟进天数
        if (this.followUpAst == null) {
            return 0;
        }
        long betweenDay = DateUtil.between(this.followUpAst, new Date(), DateUnit.DAY);
        if (betweenDay < 0) {
            return 0;//小于0表示还未跟进
        }
        return betweenDay;

    }

    public Long getCommissionedDays() {
        //委案天数 案件结清状态为未结清（0） 委案天数=当前时间-委案时间  （0点之前算一天过0点天数加一）
        if (this.getSettlementStatus() == null) {
            return 0L;
        }
        if (this.getSettlementStatus() == 0) {
            if (this.entrustingCaseDate == null) {
                return 0L;
            }
            String format = DateUtil.format(this.entrustingCaseDate, "yyyy-MM-dd");
            DateTime parse = DateUtil.parse(format);
            long betweenDay = DateUtil.between(parse, new Date(), DateUnit.DAY);
            if (betweenDay < 0) {
                return 0L;
            }
            return betweenDay + 1;
        } else {
            //案件结清状态为已结清（1） 委案天数=结清时间-委案时间  （0点之前算一天过0点天数加一）
            if (this.clearDate == null || this.entrustingCaseDate == null) {
                return null;
            }
            String format = DateUtil.format(this.clearDate, "yyyy-MM-dd");
            DateTime parse = DateUtil.parse(format);
            String format1 = DateUtil.format(this.entrustingCaseDate, "yyyy-MM-dd");
            DateTime parse1 = DateUtil.parse(format1);
            long betweenDay = DateUtil.between(parse1, parse, DateUnit.DAY);
            if (betweenDay < 0) {
                return 0L;
            }
            return betweenDay + 1;
        }

    }
}
