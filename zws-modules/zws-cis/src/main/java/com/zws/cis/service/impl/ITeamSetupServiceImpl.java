package com.zws.cis.service.impl;

import com.zws.cis.domain.TeamSetup;
import com.zws.cis.mapper.TeamSetupMapper;
import com.zws.cis.service.ITeamSetupService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 还款方式设置（机构）-service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/09 16:19
 */
@Service
public class ITeamSetupServiceImpl implements ITeamSetupService {

    @Resource
    private TeamSetupMapper mapper;

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    @Override
    public List<TeamSetup> selectList(Map<String, Object> param) {
        List<TeamSetup> list = mapper.selectList(param);
        return list;
    }

    /**
     * 主键查询
     *
     * @param id
     * @return
     */
    @Override
    public TeamSetup selectById(Long id) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("teamId", SecurityUtils.getTeamId());
        return mapper.selectByPrimaryKey(map);
    }

    /**
     * 插入保存数据
     *
     * @param entity
     * @return
     */
    @Override
    public long insert(TeamSetup entity) {
        entity.check();
//        entity.setState(Integer.parseInt(BaseConstant.State_Use));
        entity.setTeamId(SecurityUtils.getTeamId().intValue());
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("repaymentMethod", entity.getRepaymentMethod());
        int size = mapper.checkRepaymentMethodSize(map);
        if (size > 0) {
            throw new GlobalException("还款方式不能重复");
        }
        int i = mapper.insert(entity);
        if (i > 0) {
            return entity.getId();
        }
        return 0;
    }

    /**
     * 编辑
     *
     * @param entity
     */
    @Override
    public void updateById(TeamSetup entity) {
        if (entity.getId() == null) {
            throw new GlobalException("id不能为空");
        }
        entity.check();
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", SecurityUtils.getTeamId());
        map.put("repaymentMethod", entity.getRepaymentMethod());
        map.put("id", entity.getId());
        int size = mapper.checkRepaymentMethodSize(map);
        if (size > 0) {
            throw new GlobalException("还款方式不能重复");
        }
        mapper.updateByPrimaryKeySelective(entity);
    }
}
