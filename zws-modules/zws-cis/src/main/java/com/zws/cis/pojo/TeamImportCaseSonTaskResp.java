package com.zws.cis.pojo;

import com.zws.common.core.domain.sms.InfoContact;
import com.zws.system.api.domain.*;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 导入案件子任务 返回数据
 * <AUTHOR>
 * @date ：Created in 2022/7/6 11:20
 */
@Data
@ToString
public class TeamImportCaseSonTaskResp implements Future {

    /**
     * 成功数量
     */
    private int successNum;
    /**
     * 总委案金额
     */
    private BigDecimal entrustMoneyTotal;
    /**
     * 失败的行-包含失败信息
     */
    private List<Map<String, Object>> failsRows;

    /**
     * 导入成功的案件
     */
    private List<TeamImportCasePojo> importCaseList;

    /**
     * 收集案件信息
     */
    private List<Library> libraries;
    private List<InfoBase> baseInfos;
    private List<InfoLoan> infoLoans;
    private List<InfoPlan> infoPlans;
    private List<InfoContact> infoContacts;
    private List<InfoExtra> infoExtras;
    private List<CaseManage> manages;

    public TeamImportCaseSonTaskResp() {
        this.init();
    }

    private void init() {
        libraries = new ArrayList<>();
        baseInfos = new ArrayList<>();
        infoLoans = new ArrayList<>();
        infoPlans = new ArrayList<>();
        infoContacts = new ArrayList<>();
        infoExtras = new ArrayList<>();
        manages = new ArrayList<>();
    }

    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return false;
    }

    @Override
    public boolean isCancelled() {
        return false;
    }

    @Override
    public boolean isDone() {
        return false;
    }

    @Override
    public Object get() throws InterruptedException, ExecutionException {
        return null;
    }

    @Override
    public Object get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        return null;
    }
}
