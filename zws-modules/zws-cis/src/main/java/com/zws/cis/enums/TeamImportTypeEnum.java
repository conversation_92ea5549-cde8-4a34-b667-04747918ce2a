package com.zws.cis.enums;

/**
 * 导入类型
 * <AUTHOR>
 * @date ：Created in 2022/3/17 15:50
 */
public enum TeamImportTypeEnum {
    //导入类型 0-导入案件，1-更新案件，2-重新导案，3-联系人，4-催记，5-还款计划，6-批量删除，7-批量停催，8-批量留案，9-批量退案
    /**
     * 0-导入案件
     */
    IMPORT_CASE("0","导入案件"),
    /**
     * 1-更新案件
     */
    UPDATE_CASE("1","更新案件"),
    /**
     *2-重新导案
     */
    RE_IMPORT_CASE("2","重新导案"),
    /**
     * 3-联系人
     */
    CONTACT("3","联系人"),
    /**
     * 4-催记
     */
    URGE("4","催记"),
    /**
     * 5-还款计划
     */
    PLAN("5","还款计划"),
    /**
     * 6-批量删除
     */
    BATCH_DELETED("6","批量删除"),
    /**
     * 7-批量停催
     */
    BATCH_STOP_URGING("7","批量停催"),
    /**
     * 8-批量留案
     */
    BATCH_STAY_CASE("8","批量留案"),
    /**
     * 9-批量退案
     */
    BATCH_SEND_BACK("9","批量退案"),
    /**
     * 10-批量回收
     */
    BATCH_RECYCLE_BACK("10","批量回收"),
    ;

    private  String code;
    private  String info;
    TeamImportTypeEnum(String code, String info){
        this.code=code;
        this.info = info;
    }


    public String getCode() {return code;}
    public String getInfo()
    {
        return info;
    }

    public static TeamImportTypeEnum valueOfCode(String code){
        TeamImportTypeEnum[] enums=  TeamImportTypeEnum.values();
        for (TeamImportTypeEnum temp:enums) {
            if(temp.code.equals(code)) {
                return temp;
            }
        }
        return null;
    }

}
