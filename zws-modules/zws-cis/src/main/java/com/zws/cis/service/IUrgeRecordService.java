package com.zws.cis.service;

import com.zws.cis.domain.UrgeRecord;
import com.zws.system.api.model.LoginUser;

/**
 * 催记
 *
 * <AUTHOR>
 * @date 2024/8/8 19:33
 */
public interface IUrgeRecordService {

    /**
     * 新增插入催记
     * @param record
     * @return
     */
    long insert(UrgeRecord record, LoginUser loginUser);

    void updateById(UrgeRecord record);
    /**
     * 删除
     * @param id
     */
    void deletedById(Long id);
}
