package com.zws.cis.pojo;

import com.zws.common.core.domain.sms.InfoContact;
import com.zws.system.api.domain.InfoBase;
import com.zws.system.api.domain.InfoExtra;
import com.zws.system.api.domain.InfoLoan;
import com.zws.system.api.domain.InfoPlan;

import java.util.List;

/**
 * 导入的案件信息
 * <AUTHOR>
 * @date ：Created in 2023/10/7 20:10
 */
public class TeamImportCaseInfo {

    /**
     * 案件基本信息
     */
    private InfoBase infoBase;
    /**
     * 贷款信息
     */
    private InfoLoan infoLoan;
    /**
     * 联系人信息
     */
    private List<InfoContact> infoContacts;
    /**
     * 附加信息
     */
    private List<InfoExtra> infoExtras;
    /**
     * 还款计划
     */
    private List<InfoPlan> infoPlans;


}
