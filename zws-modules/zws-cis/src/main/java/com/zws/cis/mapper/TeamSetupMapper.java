package com.zws.cis.mapper;

import com.zws.cis.domain.TeamSetup;

import java.util.List;
import java.util.Map;

/**
 * 还款方式设置（机构）-Dao层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/09 16:19
 */
public interface TeamSetupMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TeamSetup record);

    int insertSelective(TeamSetup record);

    /**
     * 主键查询
     *
     * @param param
     * @return
     */
    TeamSetup selectByPrimaryKey(Map<String, Object> param);

    int updateByPrimaryKeySelective(TeamSetup record);

    int updateByPrimaryKey(TeamSetup record);

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    List<TeamSetup> selectList(Map<String, Object> param);

    /**
     * 查询还款方式 数量
     *
     * @param map
     * @return
     */
    int checkRepaymentMethodSize(Map<String, Object> map);
}