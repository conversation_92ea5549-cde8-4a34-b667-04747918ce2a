package com.zws.cis.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.cis.enums.ImportStartEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 资产管理表(saas系统机构资产)
 */
@Data
public class TeamManage {

    /**
     * 资产管理id
     */
    private Long id;

    /**
     * 导入批次号
     */
    private String batchNum;

    /**
     * 债权准入日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date creditorAccessDate;

    /**
     * 合同文件名
     */
    private String contractFilename;

    /**
     * 委案合同url
     */
    private String contractUrl;

    /**
     * 源文件url
     */
    private String sourceFileurl;

    /**
     * 源文件名
     */
    private String sourceFilename;

    /**
     * 案件数量
     */
    private Long caseNumber;

    /**
     * 委托总额
     */
    private BigDecimal entrustMoneyTotal;

    /**
     * 资产方id
     */
    private Long ownerId;

    /**
     * 收购成本
     */
    private BigDecimal acquisitionCosts;

    /**
     * 回款目标金额
     */
    private BigDecimal targetAmount;

    /**
     * 周期开始
     */
    private Date beginPeriod;

    /**
     * 周期结束
     */
    private Date endPeriod;

    /**
     * 费率
     */
    private BigDecimal rate;

    /**
     * 预期收益
     */
    private BigDecimal expectedRevenue;

    /**
     * 收购日期
     */
    private Date acquisitionDate;

    /**
     * 交割日期
     */
    private Date closingDate;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 资产方名称
     */
    private String ownerName;

    /**
     * 导入状态 0-导入中，1-导入成功，2-导入失败，3-部分成功
     */
    private String importStart;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 是否开启自动减免，0-是，1-否
     */
    private String autoReduction;

    /**
     * 减免规则id
     */
    private Long reductionId;

    /**
     * 委托金额id
     */
    private Long entrustMoneyId;

    /**
     * 债权公告日
     */
    private Date creditorAnnouncementDate;

    /**
     * 债权公告（链接地址）
     */
    private String creditorAnnouncement;

    /**
     * 签订日
     */
    private Date signingDate;

    /**
     * 协议编号
     */
    private String protocolNumber;

    /**
     * 协议名称
     */
    private String protocol;

    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String delFlag;

    /**
     * 资产包名称
     */
    private String packageName;

    /**
     * 回款开户账号
     */
    private String openAccount;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 冲减顺序，逗号分隔(如:本金,利息,费用)
     */
    private String offsetOrder;

    /**
     * 结清证明模版id
     */
    private String settleId;

    /**
     * 结清证明模版名称
     */
    private String settleName;

    /**
     * 机构id
     */
    private Integer teamId;

    /**
     * 导入状态
     */
    private String importStartInfo;

    public String getImportStartInfo() {
        ImportStartEnum start = ImportStartEnum.valueOfCode(this.importStart);
        if (start == null) {
            return "";
        }
        return start.getInfo();
    }
}