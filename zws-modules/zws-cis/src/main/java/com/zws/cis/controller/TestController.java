package com.zws.cis.controller;

import com.zws.cis.service.ITeamAccountService;
import com.zws.common.core.web.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试接口
 *
 * <AUTHOR>
 * @date 2024/8/22 18:10
 */
@CrossOrigin
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private ITeamAccountService teamAccountService;

    /**
     *
     * @return
     */
    @RequestMapping("/updateTableName")
    public AjaxResult updateTableName() {
        teamAccountService.updateTebleName();
        return AjaxResult.success("测试接口");
    }
}
