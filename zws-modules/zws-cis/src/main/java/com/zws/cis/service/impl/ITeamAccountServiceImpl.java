package com.zws.cis.service.impl;

import com.zws.cis.domain.TeamAccount;
import com.zws.cis.mapper.TeamAccountMapper;
import com.zws.cis.service.ITeamAccountService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 机构账户管理--（service层）
 *
 * @author: 马博新
 * @date ：Created in 2024/08/09 10:29
 */
@Service
public class ITeamAccountServiceImpl implements ITeamAccountService {

    @Resource
    private TeamAccountMapper mapper;

    /**
     * 查询账户管理列表
     *
     * @param account
     * @return
     */
    @Override
    public List<TeamAccount> selectByPrimaryKey(TeamAccount account) {
        return mapper.selectList(account);
    }

    /**
     * 查询账户管理已开启列表
     *
     * @return
     */
    @Override
    public List<TeamAccount> selectAccountById(Integer teamId) {
        return mapper.selectAccountById(teamId);
    }

    /**
     * 新增账户
     *
     * @param account
     * @return
     */
    @Override
    public int insert(TeamAccount account) {
        verification(account);
        account.setCreatedById(SecurityUtils.getUserId());
        account.setCreatedBy(SecurityUtils.getUsername());
        account.setCreatedTime(new Date());
        account.setDelFlag(BaseConstant.DelFlag_Being);
        return mapper.insert(account);
    }

    /**
     * 根据id修改账户信息
     *
     * @param account
     * @return
     */
    @Override
    public int update(TeamAccount account) {
        verification(account);
        account.setUpdateBy(SecurityUtils.getUsername());
        account.setUpdateTime(new Date());
        return mapper.update(account);
    }

    /**
     * 根据id删除账户信息
     *
     * @param id
     * @return
     */
    @Override
    public int delete(Long id) {
        TeamAccount account = new TeamAccount();
        account.setDelFlag(BaseConstant.DelFlag_Delete);
        account.setUpdateBy(SecurityUtils.getUsername());
        account.setUpdateTime(new Date());
        account.setId(id);
        account.setTeamId(SecurityUtils.getTeamId().intValue());
        return mapper.delete(account);
    }

    @Override
    public int updateTebleName() {
        return this.mapper.updateTebleName();
    }

    /**
     * 新增/修改账户信息验证
     *
     * @param account
     */
    public void verification(TeamAccount account) {
        if (ObjectUtils.isEmpty(account.getAccountName())) {
            throw new GlobalException("账户名称不能为空");
        }
        if (ObjectUtils.isEmpty(account.getBankName())) {
            throw new GlobalException("开户行名称不能为空");
        }
        if (ObjectUtils.isEmpty(account.getAccountNumber())) {
            throw new GlobalException("开户账户不能为空");
        }
        if (ObjectUtils.isEmpty(account.getAccountStatus())) {
            throw new GlobalException("账户状态不能为空");
        }

        if (!ObjectUtils.isEmpty(account.getAccountName())) {
            if (account.getAccountName().length() > 30) {
                throw new GlobalException("账户名称输入限制30个字符");
            }
        }
        if (!ObjectUtils.isEmpty(account.getBankName())) {
            if (account.getBankName().length() > 30) {
                throw new GlobalException("开户行名称输入限制30个字符");
            }
        }
        if (!ObjectUtils.isEmpty(account.getAccountNumber())) {
            if (account.getAccountNumber().length() > 30) {
                throw new GlobalException("开户账户输入限制30个字符");
            }
           /* if (!StringUtils.isNumeric(account.getAccountNumber()))
                throw new GlobalException("开户账户必须全部为数字");*/
        }
    }
}
