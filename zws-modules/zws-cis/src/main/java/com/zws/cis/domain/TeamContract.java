package com.zws.cis.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 委案合同表(机构)
 * <AUTHOR>
 */
@Data
public class TeamContract {

    /**
     * 委案合同表id
     */
    private Long id;

    /**
     * 资产管理id
     */
    private Long assetManageId;

    /**
     * 合同名
     */
    private String fileName;

    /**
     * 合同地址
     */
    private String fileUrl;

    /**
     * 文件格式
     */
    private String fileFormat;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 机构id
     */
    private Integer teamId;

    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String delFlag;

    /**
     * 备注
     */
    private String remarks;
}
