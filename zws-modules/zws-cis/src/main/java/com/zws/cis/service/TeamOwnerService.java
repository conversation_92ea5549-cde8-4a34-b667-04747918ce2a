package com.zws.cis.service;

import com.zws.cis.domain.TeamOwner;
import com.zws.cis.pojo.TreeNodePojo;

import java.util.List;

/**
 * 委托方信息--service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:03
 */
public interface TeamOwnerService {

    /**
     * 新增委托方
     *
     * @param record
     * @return
     */
    int insert(TeamOwner record);

    /**
     * 修改委托方信息
     *
     * @param record
     * @return
     */
    int update(TeamOwner record);

    /**
     * 删除委托方信息
     *
     * @param id
     */
    void deleted(Long id);

    /**
     * 获取 资方-产品 树结构
     *
     * @return
     */
    List<TreeNodePojo> getTree(TeamOwner entity);

    /**
     * 查询列表
     *
     * @param entity
     * @return
     */
    List<TeamOwner> selectList(TeamOwner entity);

    /**
     * 判断是否可删除
     *
     * @param id
     * @return true 可以删除，false 不能删除
     */
    boolean checkCanDeleted(Long id);
}
