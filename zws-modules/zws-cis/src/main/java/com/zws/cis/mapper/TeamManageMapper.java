package com.zws.cis.mapper;

import com.zws.cis.domain.TeamManage;

import java.util.List;
import java.util.Map;

public interface TeamManageMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TeamManage record);

    int insertSelective(TeamManage record);

    TeamManage selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TeamManage record);

    int updateByPrimaryKey(TeamManage record);

    /**
     * 主键id选择性修改--可以为null
     *
     * @param record
     * @return
     */
    int updateAssetManage(TeamManage record);

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    List<TeamManage> selectList(Map<String, Object> param);

    /**
     * 查询资产管理下的案件数量根，根据此返回判断是否可删除
     *
     * @param batchNum
     * @return
     */
    long selectCaseNumByBatchNum(String batchNum);

    /**
     * 查询批次号 统计，判断是否唯一
     * @param batchNum
     * @return
     */
    long selectBatchNumCount(String batchNum);

    /**
     * 查询资产包名称列表
     * @param teamId
     * @return
     */
    List<String> selectPackageNameById(Long teamId);

    /**
     * 查询统计
     *
     * @param param
     * @return
     */
    Map<String, Object> selectCount(Map<String, Object> param);

    /**
     * 产品ID获取 转让方 简称 owner ShortName
     * @param productId
     * @return
     */
    String getOwnerShortNameByProductId(Long productId);
}
