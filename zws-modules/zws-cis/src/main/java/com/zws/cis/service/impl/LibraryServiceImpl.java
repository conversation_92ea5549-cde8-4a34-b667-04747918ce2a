package com.zws.cis.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zws.cis.mapper.LibraryMapper;
import com.zws.cis.pojo.CaseBaseInfo;
import com.zws.cis.service.ILibraryService;
import com.zws.cis.service.IManageService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/2/15 15:31
 */
@Slf4j
@Service
@Primary
public class LibraryServiceImpl  implements ILibraryService {

    @Autowired
    private LibraryMapper mapper;
    @Autowired
    private IManageService manageService;


//    @Override
//    public long insert(Library entity) {
//        if (StringUtils.isEmpty(entity.getBatchNo())) {
//            throw new GlobalException("批次号不能为空");
//        }
//        if (ObjectUtil.isNull(entity.getProductId())) {
//            throw new GlobalException("产品不能为空");
//        }
//        if (ObjectUtil.isNull(entity.getEntrustingPartyId())) {
//            throw new GlobalException("委托方不能为空");
//        }
//        if (ObjectUtil.isNull(entity.getTeamId())) {
//            throw new GlobalException("所属机构不能为空");
//        }
//        //这里固定是团队端导入来源
//        entity.setImportSource(ImportSourceEnum.TEAM_IMPORT.getCode());
//        if (StringUtils.isEmpty(entity.getSettleState())) {
//            entity.setSettleState(BaseConstant.SettleState_Not);
//        }
//        entity.setAllocateCaseState(BaseConstant.AllocateCaseState_Not);
//        entity.setDelFlag(BaseConstant.DelFlag_Being);
//        entity.setCreateBy(SecurityUtils.getUsername());
//        entity.setCreateTime(DateUtils.getNowDate());
//        int i = mapper.insert(entity);
//        if (i > 0) {
//            return entity.getId();
//        }
//        return 0;
//    }

//    @Override
//    public void update(Library entity) {
//        entity.setUpdateBy(SecurityUtils.getUsername());
//        entity.setUpdateTime(DateUtils.getNowDate());
//        mapper.updateByPrimaryKeySelective(entity);
//    }

//    @Override
//    public List<Library> selectListByCaseId(Long caseId, Long teamId) {
//        return this.mapper.selectListByCaseId(caseId, teamId);
//    }

//    /**
//     * 跟进资产管理id删除案件
//     *
//     * @param assetManageId
//     */
//    @Override
//    public void deleteByAssetManageId(Long assetManageId, Long teamId) {
//        if (assetManageId == null) {
//            return;
//        }
//        Library entity = new Library();
//        entity.setAssetManageId(assetManageId);
//        entity.setDelFlag(BaseConstant.DelFlag_Delete);
//        entity.setUpdateBy(SecurityUtils.getUsername());
//        entity.setUpdateTime(DateUtils.getNowDate());
//        entity.setAllocateCaseState("未分配");
//        entity.setTeamId(teamId);
//        mapper.updateByAssetManageIdSelective(entity);
//    }

//    @Override
//    public List<CaseBaseInfo> selectCaseBaseInfoList(String batchNo, Integer teamId) {
//        Map<String, Object> param = new HashMap<>();
//        param.put("batchNo", batchNo);
//        param.put("teamId", teamId);
//        List<CaseBaseInfo> list = mapper.selectCaseBaseInfoList(param);
//        for (CaseBaseInfo temp : list) {
//            manageService.dateDecryptDetails(temp);
//        }
//        return list;
//    }

//    @Override
//    public void updateAllocateCaseState(Long caseId) {
//        Library entity = new Library();
//        entity.setId(caseId);
//        entity.setAllocateCaseState(BaseConstant.AllocateCaseState_Already);
//        //entity.setUpdateBy(SecurityUtils.getUsername());
//        entity.setUpdateTime(DateUtils.getNowDate());
//        mapper.updateByPrimaryKeySelective(entity);
//    }

    @Override
    public void updateAllocateCaseStateByList(List<Long> caseId) {

        mapper.updateAllocateCaseStateByIdList(caseId);
    }

//    @Override
//    public Library selectById(Long caseId) {
//        if (caseId == null) {
//            return null;
//        }
//        return this.mapper.selectByPrimaryKey(caseId);
//    }
}
