package com.zws.cis.agservice;

import com.zws.common.core.threadpool.PoolManageMent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * CIS 服务 启动初始化
 *
 * <AUTHOR>
 * @date 2024/8/13 16:57
 */
@Slf4j
@Component
public class CisInitAgService {

    @PostConstruct
    private void init(){
        try {
            PoolManageMent pool = PoolManageMent.getInstance();
            pool.init();
        }catch (Exception e){
            log.error("CIS 服务 启动初始化失败",e);
        }

    }

}
