package com.zws.cis.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zws.system.api.domain.InfoLoan;

/**
 * <AUTHOR>
 */
public interface InfoLoanMapper extends BaseMapper<InfoLoan> {

    int deleteByPrimaryKey(Long id);

    int insert(InfoLoan record);


    InfoLoan selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InfoLoan record);

    /**
     * 根据案件ID 更新
     * @param record
     * @return
     */
    int updateByCaseIdSelective(InfoLoan record);

    /**
     * 根据案件ID 更新案件罚息
     * @param record
     * @return
     */
    int updateByCaseIdDefaultInterest(InfoLoan record);



}
