package com.zws.cis.service;

import com.zws.system.api.domain.InfoBase;

/**
 * 案件信息-基本信息
 * <AUTHOR>
 * @date ：Created in 2022/2/16 9:22
 */
public interface IInfoBaseService {

    /**
     * 插入保存数据
     * @param entity
     * @return
     */
    long insert(InfoBase entity) throws Exception;


    /**
     * 根据更改
     * @param entity
     */
    void updateById(InfoBase entity) throws Exception;

    /**
     * 根据案件ID更改
     * @param entity
     */
    void updateByCaseId(InfoBase entity);

    /**
     * 案件ID获取案件基本信息
     * @param caseId
     * @return
     */
    InfoBase selectByCaseId(Long caseId);


}
