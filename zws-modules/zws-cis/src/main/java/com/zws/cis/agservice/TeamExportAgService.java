package com.zws.cis.agservice;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.zws.cis.domain.TeamExportLog;
import com.zws.cis.service.IExportLogService;
import com.zws.cis.task.TeamExportTask;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.core.domain.ExportTaskData;
import com.zws.common.core.enums.ExportClassEnum;
import com.zws.common.core.enums.ExportStartEnum;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.threadpool.PoolManageMent;
import com.zws.common.core.threadpool.TaskManager;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 导出任务的 agservice
 *
 * <AUTHOR>
 * @date 2024/1/22 20:32
 */
@Component
public class TeamExportAgService {

    @Autowired
    private IExportLogService exportLogService;
    @Autowired
    private RedisService redisService;

    /**
     * 后台执行导出任务
     *
     * @param exportClass 导出类别
     * @param clazz       bean类       例如：IExportLogService.class
     * @param funName     函数名        例如：“insert”
     * @param params      参数          例如：new Object[]{exportLog}
     * @param exportType  导出类型       列如：“导出案件”
     * @return 返回文件名称
     */
    public String exportTask(ExportClassEnum exportClass, Class clazz, String funName, Object[] params, String exportType) {
        if (exportClass == null) {
            throw new ServiceException("导出类别 不能为空");
        }
        if (StrUtil.isEmpty(exportType)) {
            throw new ServiceException("导出类型 不能为空");
        }
        if (clazz == null) {
            throw new ServiceException("bean 不能为空");
        }
        if (funName == null) {
            throw new ServiceException("函数名 不能为空");
        }
        String key = CacheConstants.EXPORT_ORDER_KEY + DateUtils.getDate() + ":" + exportClass.name();
        long orderNum = 0;
        if (redisService.hasKey(key)) {
            orderNum = redisService.getCacheObject(key, Long.class);
        }
        orderNum++;
        String orderNumStr = NumberUtil.decimalFormat("000", orderNum);
        String fileNme = StrUtil.format("{}{}", exportType, DateUtils.dateTimeNow() + orderNumStr);

        TeamExportLog record = new TeamExportLog();
        record.setCreateBy(SecurityUtils.getUsername());
        record.setCreateById(SecurityUtils.getUserId());
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setOperationType(SecurityUtils.getAccountType());
        record.setExportType(exportType);
        record.setExportClass(exportClass.getCode());
        record.setFileName(funName);
        record.setFileName(fileNme);
        record.setExportStart(ExportStartEnum.ING.getCode());
        record.setTeamId(SecurityUtils.getTeamId());
        Long exportLogId = exportLogService.insert(record);
        record.setId(exportLogId);

        ExportTaskData taskData = new ExportTaskData();
        taskData.setExportLogId(exportLogId);
        taskData.setClazz(clazz);
        taskData.setExportClass(exportClass);
        taskData.setFileNme(fileNme);
        taskData.setParams(params);
        taskData.setExportType(exportType);
        taskData.setFunName(funName);

        redisService.setCacheObject(CacheConstants.EXPORT_TASK_KEY + exportLogId, taskData);
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        TeamExportTask workTask = new TeamExportTask(taskData);
        TaskManager.addTask(workTask);
        redisService.setCacheObject(key, orderNum, 24L, TimeUnit.HOURS);
        return fileNme;
    }


    /**
     * 导出任务
     * 后台执行导出任务
     *
     * @param exportClass 导出类别
     * @param clazz       bean类       例如：IExportLogService.class
     * @param funName     函数名        例如：“insert”
     * @param params      参数          例如：new Object[]{exportLog}
     * @param exportType  导出类型       列如：“导出案件”
     * @param fileName    文件名         例如： “文件名称”
     * @return 返回文件名称
     */
    public String exportTask(ExportClassEnum exportClass, Class clazz, String funName, Object[] params, String exportType, String fileName) {
        if (exportClass == null) {
            throw new ServiceException("导出类别 不能为空");
        }
        if (StrUtil.isEmpty(exportType)) {
            throw new ServiceException("导出类型 不能为空");
        }
        if (clazz == null) {
            throw new ServiceException("bean 不能为空");
        }
        if (funName == null) {
            throw new ServiceException("函数名 不能为空");
        }
        String key = CacheConstants.EXPORT_ORDER_KEY + DateUtils.getDate() + ":" + exportClass.name();
        long orderNum = 0;
        if (redisService.hasKey(key)) {
            orderNum = redisService.getCacheObject(key, Long.class);
        }
        orderNum++;
        String orderNumStr = NumberUtil.decimalFormat("000", orderNum);
        String fileNme = StrUtil.format("{}{}", fileName, DateUtils.dateTimeNow() + orderNumStr);

        TeamExportLog record = new TeamExportLog();
        record.setCreateBy(SecurityUtils.getUsername());
        record.setCreateById(SecurityUtils.getUserId());
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setOperationType(SecurityUtils.getAccountType());
        record.setExportType(exportType);
        record.setExportClass(exportClass.getCode());
        record.setFileName(funName);
        record.setFileName(fileNme);
        record.setExportStart(ExportStartEnum.ING.getCode());
        Long exportLogId = exportLogService.insert(record);
        record.setId(exportLogId);

        ExportTaskData taskData = new ExportTaskData();
        taskData.setExportLogId(exportLogId);
        taskData.setClazz(clazz);
        taskData.setExportClass(exportClass);
        taskData.setFileNme(fileNme);
        taskData.setParams(params);
        taskData.setExportType(exportType);
        taskData.setFunName(funName);

        redisService.setCacheObject(CacheConstants.EXPORT_TASK_KEY + exportLogId, taskData);
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        TeamExportTask workTask = new TeamExportTask(taskData);
        TaskManager.addTask(workTask);
        redisService.setCacheObject(key, orderNum, 24L, TimeUnit.HOURS);
        return fileNme;
    }


}
