package com.zws.cis.utils;

import com.alibaba.fastjson.JSONObject;
import com.zws.cis.pojo.DesensitizationPojo;
import com.zws.cis.pojo.StateDesensitizationPojo;
import com.zws.cis.pojo.StatePojo;
import com.zws.cis.service.ICisSettingService;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.redis.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.concurrent.TimeUnit;

/**
 * 获取登录人团队的设置信息
 */
@Component
public class DesensitizationRedisUtils {

    @Autowired
    private RedisService redisService;
    @Autowired
    private ICisSettingService cisSettingService;

    /**
     * 从redis中获取脱敏状态数据-(资产端用)
     *
     * @param key
     */
    public StateDesensitizationPojo getDesensitizationCreate(String key, int createId) {
        Object obj = redisService.getCacheObject(key, Object.class);
        StateDesensitizationPojo stateDesensitization = JSONObject.parseObject(obj.toString(), StateDesensitizationPojo.class);
        if (ObjectUtils.isEmpty(stateDesensitization)) {
            StateDesensitizationPojo stateDesensitization1 = new StateDesensitizationPojo();
//           根据团队id获取状态控制表信息
            StatePojo state = cisSettingService.findState(createId);
//           根据团队id获取数据脱敏详情状态表信息
            DesensitizationPojo desensitization = cisSettingService.selectDesensitization(createId);
            stateDesensitization1.setDesensitization(desensitization);
            stateDesensitization1.setState(state);
            redisService.setCacheObject(key, JSONObject.toJSONString(stateDesensitization1), CacheConstants.EXPIRATION, TimeUnit.MINUTES);
            return stateDesensitization1;
        } else {
            return stateDesensitization;
        }
    }
}
