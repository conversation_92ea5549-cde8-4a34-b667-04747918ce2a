package com.zws.cis.service.impl;

import com.zws.cis.domain.TeamOwner;
import com.zws.cis.domain.TeamProduct;
import com.zws.cis.mapper.TeamOwnerMapper;
import com.zws.cis.pojo.TreeNodePojo;
import com.zws.cis.service.TeamOwnerService;
import com.zws.cis.service.TeamProductService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 委托方信息--service层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/06 15:03
 */
@Service
public class TeamOwnerServiceImpl implements TeamOwnerService {

    @Resource
    private TeamOwnerMapper mapper;
    @Autowired
    private TeamProductService productService;

    /**
     * 新增委托方
     *
     * @param record
     * @return
     */
    @Override
    public int insert(TeamOwner record) {
        record.setTeamId(SecurityUtils.getTeamId().intValue());
        check(record);
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setCreateTime(DateUtils.getNowDate());
        record.setCreateBy(SecurityUtils.getUsername());
        return mapper.insert(record);
    }

    /**
     * 修改委托方信息
     *
     * @param record
     * @return
     */
    @Override
    public int update(TeamOwner record) {
        if (record == null || record.getId() == null) {
            throw new GlobalException("主键不能为空");
        }
        record.setTeamId(SecurityUtils.getTeamId().intValue());
        check(record);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateTime(DateUtils.getNowDate());
        return mapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 删除委托方信息
     *
     * @param id
     */
    @Override
    public void deleted(Long id) {
        //查询资方下面还有没有案件未删除
        if (!checkCanDeleted(id)) {
            throw new GlobalException("该资产方还有批次存在，无法进行批量删除，请先将批次全部删除再进行删除操作！");
        }
        TeamOwner entity = new TeamOwner();
        entity.setId(id);
        entity.setDelFlag(BaseConstant.DelFlag_Delete);
        entity.setUpdateBy(SecurityUtils.getUsername());
        entity.setUpdateTime(DateUtils.getNowDate());

        //找到资产方下的产品，判断产品是否可删除
        List<TeamProduct> products = productService.selectListByOwnerId(id, SecurityUtils.getTeamId().intValue());
        for (TeamProduct product : products) {
            productService.deleted(product.getId());
        }
        mapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 获取 资方-产品 树结构
     *
     * @return
     */
    @Override
    public List<TreeNodePojo> getTree(TeamOwner entity) {
        List<TeamOwner> owners = selectList(entity);
        List<TreeNodePojo> treeNodes = new ArrayList<>();
        for (TeamOwner temp : owners) {
            TreeNodePojo node = new TreeNodePojo();
            node.setId("owner:" + temp.getId());
            node.setLabel(temp.getName());
            node.setUnifiedCode(temp.getUnifiedCode());
            node.setShortName(temp.getShortName());
            List<TreeNodePojo> childrens = new ArrayList<>();
            List<TeamProduct> products = productService.selectListByOwnerId(temp.getId(), entity.getTeamId());
            for (TeamProduct pr : products) {
                TreeNodePojo children = new TreeNodePojo();
                children.setId("proud:" + pr.getId());
                children.setLabel(pr.getName());
                childrens.add(children);
            }
            node.setChildren(childrens);
            treeNodes.add(node);
        }
        return treeNodes;
    }

    /**
     * 查询列表
     *
     * @param entity
     * @return
     */
    @Override
    public List<TeamOwner> selectList(TeamOwner entity) {
        List<TeamOwner> list = mapper.selectList(entity);
        return list;
    }

    /**
     * 判断是否可删除
     *
     * @param id
     * @return true 可以删除，false 不能删除
     */
    @Override
    public boolean checkCanDeleted(Long id) {
        return mapper.selectCountByOwnerId(id) == 0;
    }

    private void check(TeamOwner entity) {
        if (StringUtils.isEmpty(entity.getName())) {
            throw new GlobalException("请输入转让方名称");
        }
        TeamOwner teamOwner = new TeamOwner();
        teamOwner.setTeamId(entity.getTeamId());
        teamOwner.setId(entity.getId());
        teamOwner.setName(entity.getName());
        //检查名称是否重复
        if (mapper.selectByName(teamOwner) > 0) {
            throw new GlobalException("转让方名称重复请重新输入");
        }
        teamOwner.setName(null);
        teamOwner.setShortName(entity.getShortName());
        //检查代号是否重复
        if (mapper.selectByName(teamOwner) > 0) {
            throw new GlobalException("转让方代号重复请重新输入");
        }
    }
}
