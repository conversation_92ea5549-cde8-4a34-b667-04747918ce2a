package com.zws.cis.controller.request;

import com.zws.cis.domain.TeamManage;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 资产管理查询参数
 *
 * <AUTHOR>
 * @date ：Created in 2022/3/19 9:16
 */
@Data
public class TeamManageParam {

    private Long id;

    /**
     * 资产方id
     */
    private Long ownerId;
    /**
     * 产品id
     */
    private Long productId;

    /**
     * 导入批号
     */
    private String batchNum;
    /**
     * 导入状态
     */
    private String importStart;

    /**
     * 委托总额-开始
     */
    private BigDecimal entrustMoneyTotal1;
    /**
     * 委托总额-截止
     */
    private BigDecimal entrustMoneyTotal2;


    /**
     * 案件数量-开始
     */

    private Long caseNumber1;
    /**
     * 案件数量-截止
     */
    private Long caseNumber2;

    /**
     * 债权准入日期-开始
     */

    private Date creditorAccessDate1;

    /**
     * 债权准入日期-截止
     */
    private Date creditorAccessDate2;

    /**
     * 资产包名称
     */
    private List<String> packageNameArr;

    /**
     * 资产包名称
     */
    private String packageNameList;

    /**
     * 资产包名称
     */
    private TeamManage assetManage;

    /**
     * 排序字段
     * 1:案件量，2:债权总金额
     */
    private Integer orderBy;
    /**
     * 排序（1-正序；2-倒序）
     */
    private Integer sortOrder;

    /**
     * 机构id
     */
    private Integer teamId;
}
