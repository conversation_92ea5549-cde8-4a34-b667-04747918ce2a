package com.zws.cis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zws.cis.mapper.InfoPlanMapper;
import com.zws.cis.mapper.LibraryMapper;
import com.zws.cis.service.IInfoPlanService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.InfoPlan;
import com.zws.system.api.domain.Library;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/2/16 11:10
 */
@Service
@Primary
public class InfoPlanServiceImpl  implements IInfoPlanService {

    @Autowired
    private InfoPlanMapper infoPlanMapper;

    @Override
    public long insert(InfoPlan entity) throws Exception {
        if (entity.isEmpty()) {
            //为空不执行
            return 0;
        }
        entity.setDelFlag(BaseConstant.DelFlag_Being);
        if (StringUtils.isEmpty( entity.getCreateBy())) {
            entity.setCreateBy(SecurityUtils.getUsername());
        }
        entity.setCreateTime(DateUtils.getNowDate());
        int i= infoPlanMapper.insert(entity);
        if(i>0) {
            return entity.getId();
        }
        return 0;
    }

    @Override
    public long batchInsert(List<InfoPlan> entity) {
        return infoPlanMapper.batchInsert(entity);
    }

    /**
     * 写入还款计划最初数据
     *
     * @param entity
     * @return
     */
    @Override
    public long insertInitial(InfoPlan entity) {
        if (entity.isEmpty()) {
            //为空不执行
            return 0;
        }
        entity.check();
        entity.setDelFlag(BaseConstant.DelFlag_Being);
        if (StringUtils.isEmpty( entity.getCreateBy())) {
            entity.setCreateBy(SecurityUtils.getUsername());
        }
        entity.setCreateTime(DateUtils.getNowDate());
        entity.setUpdateTime(DateUtils.getNowDate());
        entity.setUpdateBy(SecurityUtils.getUsername());
        int i= infoPlanMapper.insertInitial(entity);
        if(i>0) {
            return entity.getId();
        }
        return 0;
    }

    @Override
    public void updateById(InfoPlan entity) throws Exception {
        entity.check();
        entity.setUpdateBy(SecurityUtils.getUsername());
        entity.setUpdateTime(DateUtils.getNowDate());
        infoPlanMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 根据还款计划id修改更新罚息
     * @param entity
     */
    @Override
    public int updateInfoPlanById(InfoPlan entity) {
        return infoPlanMapper.updateInfoPlanById(entity);
    }

    @Override
    public InfoPlan getPlanByCaseIdAndTerm(Long caseId, Integer hkPeriodsNumber) {
        InfoPlan infoPlan=new InfoPlan();
        infoPlan.setCaseId(caseId);
        infoPlan.setHkPeriodsNumber(hkPeriodsNumber);
        List<InfoPlan> list= infoPlanMapper.selectList(infoPlan);
        if (list.size()>0){
            return list.get(0);
        }
        return null;
    }



}
