package com.zws.cis.controller;

import com.zws.cis.domain.TeamAccount;
import com.zws.cis.service.ITeamAccountService;
import com.zws.common.core.constant.FileConstant;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.log.annotation.Log;
import com.zws.common.log.enums.BusinessType;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 账户管理
 *
 * <AUTHOR>
 * @date ：Created in 2022/3/24 18:17
 */
@CrossOrigin
@RestController
@RequestMapping("/teamAccount")
public class TeamAccountController extends BaseController {

    @Autowired
    private ITeamAccountService accountService;


    /**
     * 查询账户管理列表
     *
     * @param account
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo selectList(TeamAccount account) {
        startPage();
        if (ObjectUtils.isEmpty(account)) account = new TeamAccount();
        account.setTeamId(SecurityUtils.getTeamId().intValue());
        List<TeamAccount> accounts = accountService.selectByPrimaryKey(account);
        return getDataTable(accounts);
    }

    /**
     * 新增账户
     *
     * @param account
     * @return
     */
    @Log(title = "账户管理（新增账户）", businessType = BusinessType.INSERT)
    @PostMapping("/insert")
    public AjaxResult add(@RequestBody TeamAccount account) {
        if (ObjectUtils.isEmpty(account)) {
            return AjaxResult.error("新增数据不能为空");
        }
        account.setTeamId(SecurityUtils.getTeamId().intValue());
        accountService.insert(account);
        return AjaxResult.success("操作成功");
    }

    /**
     * 根据id修改账户信息
     *
     * @param account
     * @return
     */
    @Log(title = "账户管理（修改账户信息）", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody TeamAccount account) {
        if (ObjectUtils.isEmpty(account)) {
            return AjaxResult.error("修改数据不能为空");
        }
        if (ObjectUtils.isEmpty(account.getId())) {
            return AjaxResult.error("账户信息id不能为空");
        }
        account.setTeamId(SecurityUtils.getTeamId().intValue());
        accountService.update(account);
        return AjaxResult.success("操作成功");
    }

    /**
     * 根据id删除账户信息
     *
     * @param id
     * @return
     */
    @Log(title = "账户管理（删除账户信息）", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult remove(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            return AjaxResult.error("账户信息id不能为空");
        }
        accountService.delete(id);
        return AjaxResult.success("操作成功");
    }

    /**
     * 根据条件导出查询到的账户信息
     *
     * @param account
     * @return
     */
    @Log(title = "账户管理（导出账户信息）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void delete(HttpServletResponse response, TeamAccount account) throws UnsupportedEncodingException {
        if (ObjectUtils.isEmpty(account)) account = new TeamAccount();
        account.setTeamId(SecurityUtils.getTeamId().intValue());
        List<TeamAccount> accounts = accountService.selectByPrimaryKey(account);
        ExcelUtil<TeamAccount> util = new ExcelUtil<TeamAccount>(TeamAccount.class);
        String fileName = "账户管理" + FileConstant.getExcelSuffix();
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, accounts, "账户管理");
    }


}
