package com.zws.cis.pojo;

import lombok.Data;

@Data
public class StateDesensitizationPojo {

    private StatePojo state;
    private DesensitizationPojo desensitization;

    public StatePojo getState() {
        return state;
    }

    public void setState(StatePojo state) {
        this.state = state;
    }

    public DesensitizationPojo getDesensitization() {
        return desensitization;
    }

    public void setDesensitization(DesensitizationPojo desensitization) {
        this.desensitization = desensitization;
    }
}
