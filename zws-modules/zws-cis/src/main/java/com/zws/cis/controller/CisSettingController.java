package com.zws.cis.controller;

import com.zws.cis.agservice.AgCisSettingService;
import com.zws.cis.pojo.*;
import com.zws.cis.service.ICisSettingService;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机构系统管理设置--接口层
 *
 * @author: 马博新
 * @date ：Created in 2024/08/08 19:35
 */
@CrossOrigin
@RestController
@RequestMapping("/cisSetting")
public class CisSettingController extends BaseController {

    @Autowired
    private AgCisSettingService agCisSettingService;
    @Autowired
    private ICisSettingService cisSettingService;

    /**
     * 根据团队id查询对应的状态控制表/数据脱敏详情状态表/水印设置字段表
     *
     * @return
     */
    @RequestMapping(value = "/selectId", method = RequestMethod.GET)
    public AjaxResult info() {
        TeamLabelPojo createLabel = agCisSettingService.selectCreateId(SecurityUtils.getTeamId().intValue());
        return AjaxResult.success(createLabel);
    }

//    /**
//     * 修改设置状态（0:关闭,1:启用）
//     *
//     * @param state
//     * @return
//     */
//    @RequestMapping(value = "/updateStates", method = RequestMethod.PUT)
//    public AjaxResult editState(@RequestBody StatePojo state) {
//        if (ObjectUtils.isEmpty(state)) throw new GlobalException("参数不能为空");
//        state.setCreateId(SecurityUtils.getTeamId().intValue());
//        agCisSettingService.modifyState(state);
//        return AjaxResult.success("修改成功");
//    }
//
//    /**
//     * 脱敏状态设置修改
//     *
//     * @param desensitization
//     * @return
//     */
//    @RequestMapping(value = "/updateDesensitization", method = RequestMethod.PUT)
//    public AjaxResult editDesensitization(@RequestBody DesensitizationPojo desensitization) {
//        if (ObjectUtils.isEmpty(desensitization)) throw new GlobalException("参数不能为空");
//        desensitization.setCreateId(SecurityUtils.getTeamId().intValue());
//        agCisSettingService.modifyDesensitization(desensitization);
//        return AjaxResult.success("修改成功");
//    }

    /**
     * 修改水印设置字段
     *
     * @param watermark
     * @return
     */
    @RequestMapping(value = "/updateWatermark", method = RequestMethod.PUT)
    public AjaxResult editWatermark(@RequestBody WatermarkPojo watermark) {
        if (ObjectUtils.isEmpty(watermark)) throw new GlobalException("参数不能为空");
        watermark.setCreateId(SecurityUtils.getTeamId().intValue());
        cisSettingService.updateWatermark(watermark);
        return AjaxResult.success("修改成功");
    }

    /**
     * 获取团队 导出设置 列表
     *
     * @return
     */
    @GetMapping("/getTeamExportList")
    public TableDataInfo selectListWithTeamExport() {
        startPage();
        List<TeamExportPojo> teamExports = cisSettingService.selectTeamExports(SecurityUtils.getTeamId().intValue());
        return getDataTable(teamExports);
    }

//    /**
//     * 编辑机构安全设置-导出设置-按钮开关
//     *
//     * @param teamExport
//     * @return
//     */
//    @RequestMapping(value = "/updateTeamExport", method = RequestMethod.PUT)
//    public AjaxResult editTeamExport(@RequestBody TeamExportPojo teamExport) {
//        if (ObjectUtils.isEmpty(teamExport)) throw new GlobalException("参数不能为空");
//        teamExport.setCreateId(SecurityUtils.getTeamId().intValue());
//        cisSettingService.updateTeamExport(teamExport);
//        return AjaxResult.success("修改成功");
//    }
}
