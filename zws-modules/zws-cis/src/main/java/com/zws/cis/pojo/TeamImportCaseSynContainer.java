package com.zws.cis.pojo;


import java.util.ArrayList;

/**
 * 导入案件 数据缓冲区
 * <AUTHOR>
 * @date ：Created in 2022/7/6 11:16
 */
public class TeamImportCaseSynContainer {

    //子任务执行结果返回汇总
    private ArrayList<TeamImportCaseSonTaskResp> resList=new ArrayList<>();


    // 业务执行完毕提交方法【同步锁】
    public synchronized void submit(TeamImportCaseSonTaskResp res) {
        resList.add(res);
    }

    public ArrayList<TeamImportCaseSonTaskResp> getResList(){
        return resList;
    }




}
