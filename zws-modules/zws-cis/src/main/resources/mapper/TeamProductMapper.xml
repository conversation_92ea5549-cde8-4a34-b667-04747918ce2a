<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.TeamProductMapper">
    <resultMap id="BaseResultMap" type="com.zws.cis.domain.TeamProduct">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="owner_id" jdbcType="BIGINT" property="ownerId"/>
        <result column="owner_name" jdbcType="VARCHAR" property="ownerName"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="case_total_num" jdbcType="INTEGER" property="caseTotalNum"/>
        <result column="case_total_money" jdbcType="DECIMAL" property="caseTotalMoney"/>
        <result column="batch_number" jdbcType="INTEGER" property="batchNumber"/>
        <result column="state" jdbcType="CHAR" property="state"/>
        <result column="team_id" jdbcType="INTEGER" property="teamId"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.zws.cis.domain.TeamProduct">
        <result column="template" jdbcType="LONGVARCHAR" property="template"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , owner_id, owner_name, name, short_name, case_total_num, case_total_money, batch_number,
    state, team_id, del_flag, create_by, create_time, update_by, update_time
    </sql>
    <sql id="Base_Column_Join_List">
        ap
        .
        id
        , ap.owner_id, ao.name as owner_name, ao.short_name as ownerShortName, ap.name, ap.case_total_num, ap.case_total_money, ap.batch_number, ap.state, ap.template,
    ap.del_flag, ap.create_by, ap.create_time, ap.update_by, ap.update_time
    </sql>
    <sql id="Blob_Column_List">
        template
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from team_asset_product
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectCheckByName" resultType="java.lang.Integer">
        select count(id)
        from team_asset_product
        where del_flag = 0
        <if test="id!=null">
            and id &lt;&gt; #{id}
        </if>
        <if test="name!=null and name!='' ">
            and name=#{name}
        </if>
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_Join_List"/>
        from team_asset_product as ap
        left join team_asset_owner as ao on(ap.owner_id=ao.id)
        where ap.del_flag = 0 and ap.team_id = #{teamId}
        <if test="id !=null">
            and ap.id = #{id,jdbcType=BIGINT}
        </if>
        <if test="name!=null and name!='' ">
            and ap.name = #{name}
        </if>
        <if test="shortName!=null">
            and ap.short_name=#{shortName}
        </if>
        <if test="ownerId !=null">
            and ap.owner_id = #{ownerId,jdbcType=BIGINT}
        </if>

    </select>

    <select id="selectCountByProductId" resultType="java.lang.Long">
        select count(id)
        from case_library
        where del_flag = 0
          and product_id = #{productId}
    </select>

    <select id="selectByProductId" resultType="java.util.Map">
        select SUM(am.case_number)         as caseNum,
               SUM(am.entrust_money_total) as caseMoney,
               count(am.batch_num)         as batchNoNum
        from team_asset_manage as am
        where am.del_flag = 0
          and am.import_start in (1, 3)
          and am.team_id = #{teamId}
          and product_id = #{productId}
    </select>

    <select id="checkRepeat" resultType="java.lang.Integer">
        select count(1)
        from team_asset_product as ap
        where ap.del_flag = 0 and ap.team_id = #{teamId}
        <if test="name!=null and name!='' ">
            and ap.name = #{name}
        </if>
        <if test="shortName!=null and shortName!='' ">
            and ap.short_name = #{shortName}
        </if>
        <if test="ownerId !=null">
            and ap.owner_id = #{ownerId,jdbcType=BIGINT}
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from team_asset_product
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.cis.domain.TeamProduct" useGeneratedKeys="true"
            keyProperty="id">
        insert into team_asset_product (id, owner_id, owner_name,
                                        name, short_name, case_total_num,
                                        case_total_money, batch_number, state,
                                        team_id, del_flag, create_by,
                                        create_time, update_by, update_time,
                                        template)
        values (#{id,jdbcType=BIGINT}, #{ownerId,jdbcType=BIGINT}, #{ownerName,jdbcType=VARCHAR},
                #{name,jdbcType=VARCHAR}, #{shortName,jdbcType=VARCHAR}, #{caseTotalNum,jdbcType=INTEGER},
                #{caseTotalMoney,jdbcType=DECIMAL}, #{batchNumber,jdbcType=INTEGER}, #{state,jdbcType=CHAR},
                #{teamId,jdbcType=INTEGER}, #{delFlag,jdbcType=CHAR}, #{createBy,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{template,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.cis.domain.TeamProduct">
        insert into team_asset_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="ownerId != null">
                owner_id,
            </if>
            <if test="ownerName != null">
                owner_name,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="shortName != null">
                short_name,
            </if>
            <if test="caseTotalNum != null">
                case_total_num,
            </if>
            <if test="caseTotalMoney != null">
                case_total_money,
            </if>
            <if test="batchNumber != null">
                batch_number,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="template != null">
                template,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="ownerName != null">
                #{ownerName,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="caseTotalNum != null">
                #{caseTotalNum,jdbcType=INTEGER},
            </if>
            <if test="caseTotalMoney != null">
                #{caseTotalMoney,jdbcType=DECIMAL},
            </if>
            <if test="batchNumber != null">
                #{batchNumber,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                #{state,jdbcType=CHAR},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="template != null">
                #{template,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.cis.domain.TeamProduct">
        update team_asset_product
        <set>
            <if test="ownerId != null">
                owner_id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="ownerName != null">
                owner_name = #{ownerName,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                short_name = #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="caseTotalNum != null">
                case_total_num = #{caseTotalNum,jdbcType=INTEGER},
            </if>
            <if test="caseTotalMoney != null">
                case_total_money = #{caseTotalMoney,jdbcType=DECIMAL},
            </if>
            <if test="batchNumber != null">
                batch_number = #{batchNumber,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=CHAR},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="template != null">
                template = #{template,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryManage" parameterType="com.zws.cis.pojo.TeamManagePojo">
        update case_manage
        <set>
            <if test="displayData != null">
                display_data = #{displayData},
            </if>
        </set>
        where product_id = #{productId}
    </update>
    <update id="updateByPrimaryLibrary" parameterType="com.zws.cis.pojo.TeamLibraryPojo">
        update case_library
        <set>
            <if test="displayData != null">
                display_data = #{displayData},
            </if>
        </set>
        where product_id = #{productId}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.zws.cis.domain.TeamProduct">
        update team_asset_product
        set owner_id         = #{ownerId,jdbcType=BIGINT},
            owner_name       = #{ownerName,jdbcType=VARCHAR},
            name             = #{name,jdbcType=VARCHAR},
            short_name       = #{shortName,jdbcType=VARCHAR},
            case_total_num   = #{caseTotalNum,jdbcType=INTEGER},
            case_total_money = #{caseTotalMoney,jdbcType=DECIMAL},
            batch_number     = #{batchNumber,jdbcType=INTEGER},
            state            = #{state,jdbcType=CHAR},
            team_id          = #{teamId,jdbcType=INTEGER},
            del_flag         = #{delFlag,jdbcType=CHAR},
            create_by        = #{createBy,jdbcType=VARCHAR},
            create_time      = #{createTime,jdbcType=TIMESTAMP},
            update_by        = #{updateBy,jdbcType=VARCHAR},
            update_time      = #{updateTime,jdbcType=TIMESTAMP},
            template         = #{template,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.cis.domain.TeamProduct">
        update team_asset_product
        set owner_id         = #{ownerId,jdbcType=BIGINT},
            owner_name       = #{ownerName,jdbcType=VARCHAR},
            name             = #{name,jdbcType=VARCHAR},
            short_name       = #{shortName,jdbcType=VARCHAR},
            case_total_num   = #{caseTotalNum,jdbcType=INTEGER},
            case_total_money = #{caseTotalMoney,jdbcType=DECIMAL},
            batch_number     = #{batchNumber,jdbcType=INTEGER},
            state            = #{state,jdbcType=CHAR},
            team_id          = #{teamId,jdbcType=INTEGER},
            del_flag         = #{delFlag,jdbcType=CHAR},
            create_by        = #{createBy,jdbcType=VARCHAR},
            create_time      = #{createTime,jdbcType=TIMESTAMP},
            update_by        = #{updateBy,jdbcType=VARCHAR},
            update_time      = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>