<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.call.mapper.CallCustomRecordMapper">
    <resultMap id="BaseResultMap" type="com.zws.call.domain.CallCustomRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="team_id" jdbcType="INTEGER" property="teamId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_id" jdbcType="INTEGER" property="createId"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_type" jdbcType="INTEGER" property="createType"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>

    <resultMap id="EmployeesTreeResult" type="com.zws.call.pojo.EmployeesPojo">
        <result property="id" column="id"/>
        <result property="createId" column="create_id"/>
        <result property="departmentId" column="department_id"/>
        <result property="roleId" column="role_id"/>
        <result property="employeeName" column="employee_name"/>
        <result property="departments" column="departments"/>
        <result property="loginAccount" column="login_account"/>
        <result property="password" column="password"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="theRole" column="the_role"/>
        <result property="accountStatus" column="account_status"/>
        <result property="workingState" column="working_state"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="founder" column="founder"/>
        <result property="creationtime" column="creationtime"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="employeesWorking" column="employees_working"/>
        <result property="loginDate" column="login_date"/>
        <result property="sipNumber" column="sip_number"/>
        <result property="sipPassword" column="sip_password"/>
        <result property="updatePasswordTime" column="update_password_time"/>
        <result property="bindingPhoneState" column="binding_phone_state"/>
    </resultMap>

    <resultMap id="DeptTreeResult" type="com.zws.call.pojo.DeptPojo">
        <result property="id" column="id"/>
        <result property="createId" column="create_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="status" column="status"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="founder" column="founder"/>
        <result property="creationtime" column="creationtime"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , team_id, name, file_url, create_time, create_id, create_by, create_type, del_flag
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from call_custom_record
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectEmployeesId" resultType="java.lang.Integer">
        select id
        from team_employees
        where delete_logo = 0
          and create_id = #{teamId}
          and employees_working = #{employeesWorking} LIMIT 1
    </select>

    <select id="selectCompanyNum" resultType="com.zws.call.pojo.EnterpriseDataPojo">
        select company_num AS companyNum,
               api_flag    AS apiFlag
        from team_create
        where delete_logo = 0
          and id = #{id}
    </select>

    <select id="selectSip" resultType="java.lang.String">
        select sip_number
        from call_sip
        where del_flag = '0'
        and team_id = #{teamId}
        and odv_id = #{odvId}
        <if test="seatsType != null">
            and seats_type = #{seatsType}
        </if>
        LIMIT 1
    </select>

    <select id="selectDept" resultType="com.zws.call.pojo.DeptPojo">
        select id,
               create_id   AS createId,
               parent_id   AS parentId,
               ancestors,
               dept_name   AS deptName,
               order_num   AS orderNum,
               status,
               delete_logo AS deleteLogo,
               founder,
               creationtime,
               modifier,
               modify_time AS modifyTime
        from team_dept
        where delete_logo = 0
          and create_id = #{createId}
    </select>

    <select id="selectEmployeesParentId" resultType="com.zws.call.pojo.EmployeesPojo" resultMap="EmployeesTreeResult">
        select emp.id,
               emp.create_id,
               emp.department_id,
               emp.role_id,
               emp.employee_name,
               emp.departments,
               emp.login_account,
               emp.password,
               emp.phone_number,
               emp.the_role,
               emp.account_status,
               emp.working_state,
               emp.founder,
               emp.creationtime,
               emp.modifier,
               emp.modify_time,
               emp.employees_working,
               emp.login_date,
               sip.sip_number,
               emp.sip_password,
               emp.update_password_time
        from team_employees AS emp
                 left join call_sip AS sip ON (emp.id = sip.odv_id and sip.del_flag = '0')
        where emp.delete_logo = 0
          and emp.department_id = #{parentId}
          and emp.create_id = #{createId}
    </select>

    <select id="selectDeptParentId" resultType="com.zws.call.pojo.DeptPojo" resultMap="DeptTreeResult">
        select id,
               create_id,
               parent_id,
               ancestors,
               dept_name,
               order_num,
               status,
               delete_logo,
               founder,
               creationtime,
               modifier,
               modify_time
        from team_dept
        where delete_logo = 0
          and parent_id = #{parentId}
    </select>

    <select id="selectCallSip" resultType="java.lang.Long">
        select odv_id AS odvId
        from call_sip
        where del_flag = '0'
          and team_id = #{teamId}
          and odv_id is not null
          and bind_status = '1'
          and seats_type = 2
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from call_custom_record
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.call.domain.CallCustomRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into call_custom_record (id, team_id, name,
                                        file_url, original_file_name, create_time, create_id,
                                        create_by, create_type, del_flag,customer_type)
        values (#{id,jdbcType=BIGINT}, #{teamId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR},
                #{fileUrl,jdbcType=VARCHAR}, #{originalFileName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{createId,jdbcType=INTEGER}, #{createBy,jdbcType=VARCHAR}, #{createType,jdbcType=INTEGER},
                #{delFlag,jdbcType=CHAR},#{customerType})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.call.domain.CallCustomRecord">
        insert into call_custom_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="fileUrl != null">
                file_url,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createId != null">
                create_id,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createType != null">
                create_type,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="fileUrl != null">
                #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createId != null">
                #{createId,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createType != null">
                #{createType,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.call.domain.CallCustomRecord">
        update call_custom_record
        <set>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="fileUrl != null">
                file_url = #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createType != null">
                create_type = #{createType,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.call.domain.CallCustomRecord">
        update call_custom_record
        set team_id     = #{teamId,jdbcType=INTEGER},
            name        = #{name,jdbcType=VARCHAR},
            file_url    = #{fileUrl,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            create_id   = #{createId,jdbcType=INTEGER},
            create_by   = #{createBy,jdbcType=VARCHAR},
            create_type = #{createType,jdbcType=INTEGER},
            del_flag    = #{delFlag,jdbcType=CHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>