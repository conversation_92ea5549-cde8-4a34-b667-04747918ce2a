<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zws.call.mapper.AiCallCustomDetailsMapper">
        <resultMap id="BaseResultMap" type="com.zws.call.domain.AiCallCustomDetails">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="team_id" jdbcType="INTEGER" property="teamId"/>
            <result column="member_id" jdbcType="INTEGER" property="memberId"/>
            <result column="user_no" jdbcType="INTEGER" property="userNo"/>
            <result column="record_id" jdbcType="BIGINT" property="recordId"/>
            <result column="case_name" jdbcType="VARCHAR" property="caseName"/>
            <result column="case_phone" jdbcType="VARCHAR" property="casePhone"/>
            <result column="product_name" jdbcType="VARCHAR" property="productName"/>
            <result column="entrust_money" jdbcType="DECIMAL" property="entrustMoney"/>
            <result column="remaining_due_money" jdbcType="DECIMAL" property="remainingDueMoney"/>
            <result column="remain_money" jdbcType="DECIMAL" property="remainMoney"/>
            <result column="loan_time" jdbcType="DATE" property="loanTime"/>
            <result column="over_time" jdbcType="DATE" property="overTime"/>
            <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="create_type" jdbcType="INTEGER" property="createType"/>
            <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
            <result column="note_time" jdbcType="TIMESTAMP" property="noteTime"/>
            <result column="note_by" jdbcType="VARCHAR" property="noteBy"/>
            <result column="note_by_id" jdbcType="INTEGER" property="noteById"/>
            <result column="remark" jdbcType="VARCHAR" property="remark"/>
            <result column="note_by_type" jdbcType="INTEGER" property="noteByType"/>
        </resultMap>
        <resultMap id="InfoContactResultMap" type="com.zws.common.core.domain.sms.InfoContact">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="case_id" jdbcType="BIGINT" property="caseId"/>
            <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
            <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
            <result column="phone_location" jdbcType="VARCHAR" property="phoneLocation"/>
            <result column="phone_state" jdbcType="VARCHAR" property="phoneState"/>
            <result column="contact_relation" jdbcType="VARCHAR" property="contactRelation"/>
            <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
            <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
            <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
            <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
            <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
            <result column="app_flag" jdbcType="CHAR" property="appFlag"/>
        </resultMap>
        <sql id="Base_Column_List">
            id
            , team_id, member_id, user_no, record_id, case_name, case_phone, product_name,
    entrust_money,remaining_due_money, remain_money, loan_time, over_time, create_time, create_type, del_flag,
    note_time, note_by, note_by_id, remark, note_by_type
        </sql>

        <sql id="selectCallCustomVo">
            <!--        <if test="memberId != null">-->
            <!--            and det.member_id = #{memberId}-->
            <!--        </if>-->
            <if test="ids != null and ids.size() > 0">
                and det.id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="employeeIdAndName != null and employeeIdAndName != ''">
                and (emp.employees_working like concat('%',#{employeeIdAndName},'%')
                or emp.employee_name like concat('%',#{employeeIdAndName},'%'))
            </if>
            <if test="originalFileName != null and originalFileName != ''">
                and rec.original_file_name like concat('%',#{originalFileName},'%')
            </if>
            <if test="casePhone != null and casePhone != ''">
                and det.case_phone = #{casePhone}
            </if>
            <if test="caseName != null and caseName != ''">
                and det.case_name like concat('%',#{caseName},'%')
            </if>
            <if test="memberIdList != null and memberIdList.size() > 0">
                and det.member_id in
                <foreach collection="memberIdList" item="memberId" separator="," open="(" close=")">
                    #{memberId}
                </foreach>
            </if>
            <if test="noteTime1 != null">
                and det.note_time &gt;= #{noteTime1}
            </if>
            <if test="noteTime2 != null">
                and det.note_time &lt;= #{noteTime2}
            </if>
            <if test="createTime1 != null">
                and det.create_time &gt;= #{createTime1}
            </if>
            <if test="createTime2 != null">
                and det.create_time &lt;= #{createTime2}
            </if>
        </sql>

        <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
            select
            <include refid="Base_Column_List"/>
            from ai_call_custom_record_details
            where id = #{id,jdbcType=BIGINT}
        </select>

        <select id="selectByList" resultType="com.zws.call.domain.AiCallCustomDetails">
            select det.id AS id,
            rec.original_file_name AS originalFileName,
            rec.name AS name,
            emp.employees_working AS userNo,
            emp.employee_name AS memberName,
            det.case_name AS caseName,
            det.case_phone AS casePhone,
            det.product_name AS productName,
            det.entrust_money AS entrustMoney,
            det.remaining_due_money AS remainingDueMoney,
            det.remain_money AS remainMoney,
            det.loan_time AS loanTime,
            det.over_time AS overTime,
            det.create_time AS createTime,
            det.remark AS remark,
            det.note_time AS noteTime,
            det.note_by_type AS noteByType,
            ees.employee_name AS employeeName,
            cre.cname AS cname
            from ai_call_custom_record_details AS det
            left join call_custom_record AS rec ON (det.record_id = rec.id and rec.del_flag = '0')
            left join team_employees AS emp ON (emp.id = det.member_id)
            left join team_employees AS ees ON (ees.id = det.note_by_id)
            left join team_create AS cre ON (cre.id = det.note_by_id)
            where det.del_flag = '0'
            and det.team_id = #{teamId}
            and det.customer_type = 1
            <include refid="selectCallCustomVo"/>
            order by det.id desc
        </select>

        <select id="selectPopOnScreen" resultType="com.zws.call.domain.AiCallCustomDetails">
            select det.id                  AS id,
                   det.case_name           AS caseName,
                   det.case_phone          AS casePhone,
                   det.product_name        AS productName,
                   det.entrust_money       AS entrustMoney,
                   det.remaining_due_money AS remainingDueMoney,
                   det.remain_money        AS remainMoney,
                   det.loan_time           AS loanTime,
                   det.over_time           AS overTime,
                   det.create_time         AS createTime,
                   det.remark              AS remark,
                   det.note_time           AS noteTime
            from ai_call_custom_record_details AS det
                     left join call_custom_record AS rec ON (det.record_id = rec.id and rec.del_flag = '0')
                     left join call_intelligence_task AS tas ON (tas.custom_id = rec.id and tas.del_flag = '0')
            where det.del_flag = '0'
              and det.team_id = #{teamId}
              and det.case_phone = #{casePhone}
              and tas.call_center_uuid = #{taskUuid}
        </select>

        <select id="selectWorkPopOnScreen" resultType="com.zws.call.controller.response.WorkCustomDetailsResponse">
            SELECT
                cl.id as caseId,
                AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) as caseName,
                AES_DECRYPT(UNHEX(cl.client_phone), #{decryptKey}) as casePhone,
                cl.product_name as productName,
                cil.entrust_money as entrustMoney,
                cil.remaining_due as remainMoney,
                cil.residual_principal as residualPrincipal,
                cil.overdue_start as overdueStart
            FROM
                case_library cl
                    LEFT JOIN case_info_loan cil ON cl.id = cil.case_id
            where cl.del_flag = '0' and cil.del_flag = '0'
              and   AES_DECRYPT(UNHEX(cl.client_phone), #{decryptKey}) LIKE concat ('%',#{phone},'%')
        </select>

        <select id="selectPopOnScreenIds" resultType="java.lang.Long">
            select det.id AS id
            from ai_call_custom_record_details AS det
            where det.del_flag = '0'
              and det.team_id = #{teamId}
              and det.case_phone = #{casePhone}
              and det.id = #{id}
        </select>

        <select id="selectByListExport" resultType="com.zws.call.pojo.ExportCustomPojo">
            select det.id AS id,
            rec.original_file_name AS originalFileName,
            rec.name AS name,
            emp.employees_working AS userNo,
            emp.employee_name AS memberName,
            det.case_name AS caseName,
            det.case_phone AS casePhone,
            det.product_name AS productName,
            det.entrust_money AS entrustMoney,
            det.remaining_due_money AS remainingDueMoney,
            det.remain_money AS remainMoney,
            det.loan_time AS loanTime,
            det.over_time AS overTime,
            det.create_time AS createTime,
            det.remark AS remark,
            det.note_time AS noteTime,
            det.note_by_type AS noteByType,
            ees.employee_name AS employeeName,
            cre.cname AS cname
            from ai_call_custom_record_details AS det
            left join call_custom_record AS rec ON (det.record_id = rec.id and rec.del_flag = '0')
            left join team_employees AS emp ON (emp.id = det.member_id)
            left join team_employees AS ees ON (ees.id = det.note_by_id)
            left join team_create AS cre ON (cre.id = det.team_id)
            where det.del_flag = '0'
            and det.team_id = #{teamId}
            and det.customer_type = 1
            <include refid="selectCallCustomVo"/>
            order by det.id desc
        </select>

        <select id="selectByListDelete" resultType="java.lang.Long">
            select det.id AS id
            from ai_call_custom_record_details AS det
            left join call_custom_record AS rec ON (det.record_id = rec.id and rec.del_flag = '0')
            left join team_employees AS emp ON (emp.id = det.member_id)
            where det.del_flag = '0'
            and det.team_id = #{teamId}
            and det.customer_type = 1
            <include refid="selectCallCustomVo"/>
        </select>

        <select id="selectProgenyDept" resultType="java.lang.Integer">
            SELECT id
            FROM team_dept
            WHERE FIND_IN_SET(#{deptId}, ancestors) > 0
              AND delete_logo = 0
              and create_id = #{teamId}
        </select>

        <select id="selectEmployeesIdList" resultType="java.lang.Integer">
            SELECT id
            FROM team_employees
            WHERE delete_logo = 0
            and create_id = #{teamId}
            and department_id in
            <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
        </select>

        <select id="selectCount" resultType="java.lang.Integer">
            SELECT count(det.id)
            from ai_call_custom_record_details AS det
            left join call_custom_record AS rec ON (det.record_id = rec.id and rec.del_flag = '0')
            left join call_intelligence_task AS tas ON (rec.id = tas.custom_id and tas.del_flag = '0')
            WHERE det.del_flag = '0'
            and det.team_id = #{teamId}
            and det.id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
            and tas.status != 3
        </select>

        <select id="selectByCustomUuid" resultType="java.lang.Integer">
            SELECT member_id
            from ai_call_custom_record_details
            WHERE del_flag = '0'
              and uuid = #{uuid} LIMIT 1
        </select>

        <select id="selectCaseIdOrContactsId" resultType="com.zws.call.domain.AiCallCustomDetails">
            SELECT id          AS id,
                   case_id     AS caseId,
                   contacts_id AS contactsId
            from ai_call_custom_record_details
            WHERE del_flag = '0'
              and uuid = #{uuid} LIMIT 1
        </select>

        <select id="selectInfoContactById" resultMap="InfoContactResultMap">
            SELECT cic.id,
                   cic.case_id,
                   cic.contact_name,
                   cic.contact_phone,
                   cic.phone_location,
                   cic.phone_state,
                   cic.contact_relation,
                   cic.remarks,
                   cic.del_flag,
                   cic.create_by,
                   cic.create_time,
                   cic.update_by,
                   cic.update_time
            FROM case_info_contact cic
            where cic.del_flag = 0
              and cic.id = #{id}
              and cic.phone_state = 0
        </select>

        <select id="selectOdvName" resultType="java.lang.String">
            SELECT employee_name
            FROM team_employees
            where delete_logo = 0
              and id = #{id}
        </select>

        <select id="selectTeamLevelType" resultType="java.lang.String">
            SELECT team_level_type
            FROM team_create
            where delete_logo = 0
              and id = #{id}
        </select>

        <insert id="insertUrgeRecord" parameterType="com.zws.cis.domain.UrgeRecord">
            insert into case_urge_record(case_id, create_id, contact_id, liaison, relation, contact_mode, follow_up_state,
                                         urge_state,
                                         content, remarks, odv_id, odv_name,
                                         contact_medium, promise_repayment_time, promise_repayment_money, another_time,
                                         promise_by_stages, promise_every_money,
                                         promise_repayment_day, del_flag, create_by, create_time, entrusting_case_batch_num,
                                         entrusting_case_date, return_case_date, operation_type, urge_tpye, web_side)
            values (#{caseId}, #{createId}, #{contactId}, #{liaison}, #{relation}, #{contactMode}, #{followUpState},
                    #{urgeState},
                    #{content},
                    #{remarks}, #{odvId}, #{odvName}, #{contactMedium}, #{promiseRepaymentTime},
                    #{promiseRepaymentMoney}, #{anotherTime}, #{promiseByStages},
                    #{promiseEveryMoney}, #{promiseRepaymentDay}, #{delFlag}, #{createBy}, #{createTime},
                    #{entrustingCaseBatchNum}, #{entrustingCaseDate}, #{returnCaseDate}, #{operationType}, #{urgeTpye},
                    #{webSide})
        </insert>

        <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
            delete
            from ai_call_custom_record_details
            where id = #{id,jdbcType=BIGINT}
        </delete>
        <insert id="insert" parameterType="com.zws.call.domain.AiCallCustomDetails">
            insert into ai_call_custom_record_details (id, team_id, member_id,
                                                    user_no, record_id, case_name,
                                                    case_phone, product_name, entrust_money, remaining_due_money,
                                                    remain_money, loan_time, over_time,
                                                    create_by_id, create_time, create_type, del_flag, note_time, note_by,
                                                    note_by_id, remark, note_by_type)
            values (#{id,jdbcType=BIGINT}, #{teamId,jdbcType=INTEGER}, #{memberId,jdbcType=INTEGER},
                    #{userNo,jdbcType=INTEGER}, #{recordId,jdbcType=BIGINT}, #{caseName,jdbcType=VARCHAR},
                    #{casePhone,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{entrustMoney,jdbcType=DECIMAL},
                    #{remainingDueMoney,jdbcType=DECIMAL}, #{remainMoney,jdbcType=DECIMAL}, #{loanTime,jdbcType=DATE},
                    #{overTime,jdbcType=DATE}, #{createById,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
                    #{createType,jdbcType=INTEGER}, #{delFlag,jdbcType=CHAR}, #{noteTime}, #{noteBy}, #{noteById},
                    #{remark}, #{noteByType})
        </insert>

        <insert id="insertList" parameterType="com.zws.call.domain.AiCallCustomDetails">
            insert into ai_call_custom_record_details(team_id, member_id,
            user_no, record_id, case_name,
            case_phone, product_name, entrust_money, remaining_due_money,
            remain_money, loan_time, over_time,
            create_by_id, create_time, create_type, del_flag, note_time, note_by, note_by_id, remark, note_by_type,
            uuid,customer_type,case_id,contacts_id)
            values
            <foreach collection="customDetails" item="list" index="index" separator=",">
                (#{list.teamId,jdbcType=INTEGER}, #{list.memberId,jdbcType=INTEGER},
                #{list.userNo,jdbcType=INTEGER}, #{list.recordId,jdbcType=BIGINT}, #{list.caseName,jdbcType=VARCHAR},
                #{list.casePhone,jdbcType=VARCHAR}, #{list.productName,jdbcType=VARCHAR},
                #{list.entrustMoney,jdbcType=DECIMAL},
                #{list.remainingDueMoney,jdbcType=DECIMAL}, #{list.remainMoney,jdbcType=DECIMAL},
                #{list.loanTime,jdbcType=DATE},
                #{list.overTime,jdbcType=DATE}, #{list.createById,jdbcType=INTEGER}, #{list.createTime,jdbcType=TIMESTAMP},
                #{list.createType,jdbcType=INTEGER}, #{list.delFlag,jdbcType=CHAR}, #{list.noteTime}, #{list.noteBy},
                #{list.noteById}, #{list.remark}, #{list.noteByType},
                #{list.uuid},#{list.customerType},#{list.caseId},#{list.contactsId})
            </foreach>
        </insert>

        <insert id="insertSelective" parameterType="com.zws.call.domain.AiCallCustomDetails">
            insert into ai_call_custom_record_details
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="teamId != null">
                    team_id,
                </if>
                <if test="memberId != null">
                    member_id,
                </if>
                <if test="userNo != null">
                    user_no,
                </if>
                <if test="recordId != null">
                    record_id,
                </if>
                <if test="caseName != null">
                    case_name,
                </if>
                <if test="casePhone != null">
                    case_phone,
                </if>
                <if test="productName != null">
                    product_name,
                </if>
                <if test="entrustMoney != null">
                    entrust_money,
                </if>
                <if test="remainMoney != null">
                    remain_money,
                </if>
                <if test="loanTime != null">
                    loan_time,
                </if>
                <if test="overTime != null">
                    over_time,
                </if>
                <if test="createTime != null">
                    create_time,
                </if>
                <if test="createType != null">
                    create_type,
                </if>
                <if test="delFlag != null">
                    del_flag,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    #{id,jdbcType=BIGINT},
                </if>
                <if test="teamId != null">
                    #{teamId,jdbcType=INTEGER},
                </if>
                <if test="memberId != null">
                    #{memberId,jdbcType=INTEGER},
                </if>
                <if test="userNo != null">
                    #{userNo,jdbcType=INTEGER},
                </if>
                <if test="recordId != null">
                    #{recordId,jdbcType=BIGINT},
                </if>
                <if test="caseName != null">
                    #{caseName,jdbcType=VARCHAR},
                </if>
                <if test="casePhone != null">
                    #{casePhone,jdbcType=VARCHAR},
                </if>
                <if test="productName != null">
                    #{productName,jdbcType=VARCHAR},
                </if>
                <if test="entrustMoney != null">
                    #{entrustMoney,jdbcType=DECIMAL},
                </if>
                <if test="remainMoney != null">
                    #{remainMoney,jdbcType=DECIMAL},
                </if>
                <if test="loanTime != null">
                    #{loanTime,jdbcType=DATE},
                </if>
                <if test="overTime != null">
                    #{overTime,jdbcType=DATE},
                </if>
                <if test="createTime != null">
                    #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createType != null">
                    #{createType,jdbcType=INTEGER},
                </if>
                <if test="delFlag != null">
                    #{delFlag,jdbcType=CHAR},
                </if>
            </trim>
        </insert>
        <update id="updateByPrimaryKeySelective" parameterType="com.zws.call.domain.AiCallCustomDetails">
            update ai_call_custom_record_details
            <set>
                <if test="teamId != null">
                    team_id = #{teamId,jdbcType=INTEGER},
                </if>
                <if test="memberId != null">
                    member_id = #{memberId,jdbcType=INTEGER},
                </if>
                <if test="userNo != null">
                    user_no = #{userNo,jdbcType=INTEGER},
                </if>
                <if test="recordId != null">
                    record_id = #{recordId,jdbcType=BIGINT},
                </if>
                <if test="caseName != null">
                    case_name = #{caseName,jdbcType=VARCHAR},
                </if>
                <if test="casePhone != null">
                    case_phone = #{casePhone,jdbcType=VARCHAR},
                </if>
                <if test="productName != null">
                    product_name = #{productName,jdbcType=VARCHAR},
                </if>
                <if test="entrustMoney != null">
                    entrust_money = #{entrustMoney,jdbcType=DECIMAL},
                </if>
                <if test="remainingDueMoney != null">
                    remaining_due_money = #{remainingDueMoney,jdbcType=DECIMAL},
                </if>
                <if test="remainMoney != null">
                    remain_money = #{remainMoney,jdbcType=DECIMAL},
                </if>
                <if test="loanTime != null">
                    loan_time = #{loanTime,jdbcType=DATE},
                </if>
                <if test="overTime != null">
                    over_time = #{overTime,jdbcType=DATE},
                </if>
                <if test="createById != null">
                    create_by_id = #{createById},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createType != null">
                    create_type = #{createType,jdbcType=INTEGER},
                </if>
                <if test="delFlag != null">
                    del_flag = #{delFlag,jdbcType=CHAR},
                </if>
                <if test="noteTime != null">
                    note_time = #{noteTime},
                </if>
                <if test="noteBy != null">
                    note_by = #{noteBy},
                </if>
                <if test="noteById != null">
                    note_by_id = #{noteById},
                </if>
                <if test="remark != null">
                    remark = #{remark},
                </if>
                <if test="noteByType != null">
                    note_by_type = #{noteByType},
                </if>
            </set>
            where id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </update>
        <update id="updateByPrimaryKey" parameterType="com.zws.call.domain.AiCallCustomDetails">
            update ai_call_custom_record_details
            set team_id       = #{teamId,jdbcType=INTEGER},
                member_id     = #{memberId,jdbcType=INTEGER},
                user_no       = #{userNo,jdbcType=INTEGER},
                record_id     = #{recordId,jdbcType=BIGINT},
                case_name     = #{caseName,jdbcType=VARCHAR},
                case_phone    = #{casePhone,jdbcType=VARCHAR},
                product_name  = #{productName,jdbcType=VARCHAR},
                entrust_money = #{entrustMoney,jdbcType=DECIMAL},
                remain_money  = #{remainMoney,jdbcType=DECIMAL},
                loan_time     = #{loanTime,jdbcType=DATE},
                over_time     = #{overTime,jdbcType=DATE},
                create_time   = #{createTime,jdbcType=TIMESTAMP},
                create_type   = #{createType,jdbcType=INTEGER},
                del_flag      = #{delFlag,jdbcType=CHAR}
            where id = #{id,jdbcType=BIGINT}
        </update>

        <update id="deleteByIdList">
            update ai_call_custom_record_details
            set del_flag = '1'
            where del_flag = '0'
            and id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </update>
</mapper>