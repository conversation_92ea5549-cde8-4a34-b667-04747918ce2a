<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.call.mapper.CallTeamSipMapper">
    <resultMap id="BaseResultMap" type="com.zws.call.domain.CallSip">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="sip_number" jdbcType="VARCHAR" property="sipNumber"/>
        <result column="sip_password" jdbcType="VARCHAR" property="sipPassword"/>

        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="company_num" jdbcType="VARCHAR" property="companyNum"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="bind_status" jdbcType="CHAR" property="bindStatus"/>
        <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>

    <resultMap id="BaseResultMapTeam" type="com.zws.call.pojo.TeamPojo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cname" jdbcType="VARCHAR" property="cname"/>
        <result column="category" property="category"/>
        <result column="delete_logo" property="deleteLogo"/>
        <result column="company_num" property="companyNum"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , sip_number,sip_password, team_id, odv_id, company_num, company_name, bind_status, bind_time,
    create_by, create_time, update_by, update_time, del_flag
    </sql>

    <sql id="Base_Column_Join_List">
        cs
        .
        id
        , cs.sip_number, cs.sip_password,cs.team_id, cs.odv_id, cs.company_num, cs.company_name, cs.bind_status, cs.bind_time,
    cs.create_by, cs.create_time, cs.update_by, cs.update_time, cs.del_flag,t.cname AS teamName,te.employee_name as odvName
    </sql>

    <sql id="Base_Column_List_sip">
        id
        , sip_number,sip_password, team_id, odv_id, company_num, company_name, bind_status, bind_time,
    create_by, create_time, update_by, update_time, del_flag
    </sql>


    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from call_sip
        where id = #{id,jdbcType=BIGINT} and del_flag='0'
    </select>
    <select id="selectBySipNumber" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from call_sip
        where del_flag = 0 and sip_number=#{sipNumber}
    </select>
    <select id="selectTeamNumber" resultType="java.lang.String">
        select company_num
        from team_create
        where delete_logo = 0
          and company_num is not null
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from call_sip
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.call.domain.CallSip">
        insert into call_sip (id, sip_number, sip_password, team_id,
                              odv_id, company_num, company_name,
                              bind_status, bind_time, create_by,
                              create_time, update_by, update_time,
                              del_flag, seats_type)
        values (#{id,jdbcType=BIGINT}, #{sipNumber,jdbcType=VARCHAR}, #{sipPassword}, #{teamId,jdbcType=BIGINT},
                #{odvId,jdbcType=BIGINT}, #{companyNum,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR},
                #{bindStatus,jdbcType=CHAR}, #{bindTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{delFlag,jdbcType=CHAR}, #{seatsType})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.call.domain.CallSip">
        insert into call_sip
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="sipNumber != null">
                sip_number,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="odvId != null">
                odv_id,
            </if>
            <if test="companyNum != null">
                company_num,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="bindStatus != null">
                bind_status,
            </if>
            <if test="bindTime != null">
                bind_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="sipNumber != null">
                #{sipNumber,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=BIGINT},
            </if>
            <if test="odvId != null">
                #{odvId,jdbcType=BIGINT},
            </if>
            <if test="companyNum != null">
                #{companyNum,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="bindStatus != null">
                #{bindStatus,jdbcType=CHAR},
            </if>
            <if test="bindTime != null">
                #{bindTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.call.domain.CallSip">
        update call_sip
        <set>
            <if test="sipNumber != null">
                sip_number = #{sipNumber,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
            <if test="odvId != null">
                odv_id = #{odvId,jdbcType=BIGINT},
            </if>
            <if test="companyNum != null">
                company_num = #{companyNum,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="bindStatus != null">
                bind_status = #{bindStatus,jdbcType=CHAR},
            </if>
            <if test="bindTime != null">
                bind_time = #{bindTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.call.domain.CallSip">
        update call_sip
        set sip_number   = #{sipNumber,jdbcType=VARCHAR},
            team_id      = #{teamId,jdbcType=BIGINT},
            odv_id       = #{odvId,jdbcType=BIGINT},
            company_num  = #{companyNum,jdbcType=VARCHAR},
            company_name = #{companyName,jdbcType=VARCHAR},
            bind_status  = #{bindStatus,jdbcType=CHAR},
            bind_time    = #{bindTime,jdbcType=TIMESTAMP},
            create_by    = #{createBy,jdbcType=VARCHAR},
            create_time  = #{createTime,jdbcType=TIMESTAMP},
            update_by    = #{updateBy,jdbcType=VARCHAR},
            update_time  = #{updateTime,jdbcType=TIMESTAMP},
            del_flag     = #{delFlag,jdbcType=CHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBySipNumberSelective">
        update call_sip
        <set>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
            <if test="sipPassword!=null">
                sip_password=#{sipPassword},
            </if>
            <if test="odvId != null">
                odv_id = #{odvId,jdbcType=BIGINT},
            </if>
            <if test="companyNum != null">
                company_num = #{companyNum,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="bindStatus != null">
                bind_status = #{bindStatus,jdbcType=CHAR},
            </if>
            <if test="bindTime != null">
                bind_time = #{bindTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="seatsType != null">
                seats_type = #{seatsType},
            </if>
        </set>
        where sip_number = #{sipNumber,jdbcType=VARCHAR}
    </update>


    <select id="selectByTeamId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_sip"/>
        from call_sip
        where del_flag = 0 and team_id = #{teamId}
    </select>

    <update id="updateById" parameterType="java.lang.Long">
        update call_sip
        set team_id      = null,
            odv_id       = null,
            bind_time    = null,
            company_num  = null,
            company_name = null
        where id = #{id,jdbcType=BIGINT}
          and del_flag = 0
    </update>

    <update id="updateEmployees" parameterType="java.lang.String">
        update team_employees
        set sip_number = null,
        sip_password = null
        <!--        ,seat_number = null-->
        where sip_number = #{sipNumber}
    </update>

    <select id="selectByCompanyNum" resultMap="BaseResultMapTeam">
        select
        <include refid="Base_Column_List"/>
        from team_create
        where delete_logo=0 and company_num=#{companyNum}
    </select>


    <select id="selectEmployees" resultType="java.lang.Long">
        select create_id AS createId
        from team_employees
        where delete_logo = 0
          and sip_number = #{sipNumber}
    </select>

</mapper>
