<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.call.mapper.IntelligenceTaskMapper">
    <resultMap id="BaseResultMap" type="com.zws.system.api.domain.IntelligenceTask">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="execution_time" jdbcType="VARCHAR" property="executionTime"/>
        <result column="execution_call_time" jdbcType="VARCHAR" property="executionCallTime"/>
        <result column="recall_count" jdbcType="INTEGER" property="recallCount"/>
        <result column="recall_minute" jdbcType="INTEGER" property="recallMinute"/>
        <result column="is_screen" jdbcType="INTEGER" property="isScreen"/>
        <result column="single_call_number" jdbcType="INTEGER" property="singleCallNumber"/>
        <result column="team_id" jdbcType="INTEGER" property="teamId"/>
        <result column="employee_id" jdbcType="INTEGER" property="employeeId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="call_count" jdbcType="INTEGER" property="callCount"/>
        <result column="create_id" jdbcType="INTEGER" property="createId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_type" jdbcType="INTEGER" property="createType"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="custom_name_montage" jdbcType="VARCHAR" property="customNameMontage"/>
        <result column="custom_id_montage" jdbcType="VARCHAR" property="customIdMontage"/>
        <result column="task_allocation_personnel_id" jdbcType="VARCHAR" property="taskAllocationPersonnelId"/>
        <result column="task_allocation_personnel_seating" jdbcType="VARCHAR"
                property="taskAllocationPersonnelSeating"/>
        <result column="answer_settings" jdbcType="INTEGER" property="answerSettings"/>
        <result column="custom_id" jdbcType="BIGINT" property="customId"/>
        <result column="call_center_uuid" jdbcType="CHAR" property="callCenterUuid"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.zws.system.api.domain.IntelligenceTask">
        <result column="remark" jdbcType="LONGVARCHAR" property="remark"/>
    </resultMap>

    <resultMap id="BaseCaseResultMap" type="com.zws.system.api.domain.CaseManage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="phone_state" jdbcType="INTEGER" property="phoneState"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="allocated_state" jdbcType="VARCHAR" property="allocatedState"/>
        <result column="case_state" jdbcType="VARCHAR" property="caseState"/>
        <result column="entrusting_party_id" jdbcType="BIGINT" property="entrustingPartyId"/>
        <result column="entrusting_party_name" jdbcType="VARCHAR" property="entrustingPartyName"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="batch_num" jdbcType="VARCHAR" property="batchNum"/>
        <result column="entrusting_case_batch_num" jdbcType="VARCHAR" property="entrustingCaseBatchNum"/>
        <result column="outsourcing_team_id" jdbcType="BIGINT" property="outsourcingTeamId"/>
        <result column="outsourcing_team_name" jdbcType="VARCHAR" property="outsourcingTeamName"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="client_sex" jdbcType="VARCHAR" property="clientSex"/>
        <result column="client_idcard" jdbcType="VARCHAR" property="clientIdcard"/>
        <result column="client_census_register" jdbcType="VARCHAR" property="clientCensusRegister"/>
        <result column="client_phone" jdbcType="VARCHAR" property="clientPhone"/>
        <result column="client_money" jdbcType="DECIMAL" property="clientMoney"/>
        <result column="client_residual_principal" jdbcType="DECIMAL" property="clientResidualPrincipal"/>
        <result column="client_overdue_start" jdbcType="TIMESTAMP" property="clientOverdueStart"/>
        <result column="account_period" jdbcType="INTEGER" property="accountPeriod"/>
        <result column="follow_up_state" jdbcType="VARCHAR" property="followUpState"/>
        <result column="follow_up_start" jdbcType="TIMESTAMP" property="followUpStart"/>
        <result column="follow_up_ast" jdbcType="TIMESTAMP" property="followUpAst"/>
        <result column="entrusting_case_date" jdbcType="TIMESTAMP" property="entrustingCaseDate"/>
        <result column="return_case_date" jdbcType="TIMESTAMP" property="returnCaseDate"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="odv_name" jdbcType="VARCHAR" property="odvName"/>
        <result column="urge_state" jdbcType="VARCHAR" property="urgeState"/>
        <result column="allocated_time" jdbcType="TIMESTAMP" property="allocatedTime"/>
        <result column="urge_power" jdbcType="VARCHAR" property="urgePower"/>
        <result column="del_flag" jdbcType="BIT" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="client_id_type" property="clientIdType"/>
        <result column="settlement_status" property="settlementStatus"/>
        <result column="remaining_due" property="remainingDue"/>
        <result column="remaining_due" property="remainingDue"/>
        <result column="sy_yh_principal" property="syYhPrincipal"/>
        <result column="client_birthday" property="clientBirthday"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , task_name, execution_time, execution_call_time, recall_count, recall_minute,
    is_screen, single_call_number, team_id, employee_id, status, call_count, create_id, 
    create_time, create_by, create_type, fee, custom_name_montage, custom_id_montage, 
    task_allocation_personnel_id, task_allocation_personnel_seating, answer_settings, 
    custom_id, call_center_uuid
    </sql>
    <sql id="Blob_Column_List">
        remark
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from call_intelligence_task
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectById" resultType="com.zws.system.api.domain.IntelligenceTask">
        select id                                AS id,
               task_name                         AS taskName,
               execution_time                    AS executionTime,
               execution_call_time               AS executionCallTime,
               recall_count                      AS recallCount,
               recall_minute                     AS recallMinute,
               is_screen                         AS isScreen,
               single_call_number                AS singleCallNumber,
               status                            AS status,
               call_count                        AS callCount,
               create_time                       AS createTime,
               fee                               AS fee,
               task_allocation_personnel_id      AS taskAllocationPersonnelId,
               task_allocation_personnel_seating AS taskAllocationPersonnelSeating,
               answer_settings                   AS answerSettings,
               call_center_uuid                  AS callCenterUuid,
               remark                            AS remark
        from call_intelligence_task
        where del_flag = '0'
          and id = #{id}
          and team_id = #{teamId}
    </select>

    <select id="selectByUuid" resultType="com.zws.system.api.domain.IntelligenceTask">
        select tas.id               AS id,
               tas.is_screen        AS isScreen,
               tas.call_center_uuid AS callCenterUuid,
               tas.operation_type   AS operationType,
               tas.team_id          AS teamId,
               tas.operation_type   AS operationType,
               cre.company_num      AS enterpriseNum,
               cre.api_flag         AS apiFlag,
               cre.team_type        AS teamType
        from call_intelligence_task AS tas
                 left join team_create AS cre ON (tas.team_id = cre.id and cre.delete_logo = 0)
        where tas.del_flag = '0'
          and tas.call_center_uuid = #{callCenterUuid} LIMIT 1
    </select>

    <select id="selectByCount" resultType="java.lang.Integer">
        select count(1)
        from call_intelligence_task
        where del_flag = '0'
          and task_name = #{taskName}
          and team_id = #{teamId}
    </select>

    <select id="selectList" resultType="com.zws.system.api.domain.IntelligenceTask">
        select tas.id AS id,
        tas.task_name AS taskName,
        tas.call_count AS callCount,
        tas.create_time AS createTime,
        tas.create_type AS createType,
        tas.status AS status,
        tas.remark AS remark,
        tas.call_center_uuid AS callCenterUuid,
        cre.cname AS cname,
        emp.employee_name AS employeeName,
        tas.execution_time as executionTime,
        tas.execution_call_time as executionCallTime
        from call_intelligence_task AS tas
        <!--        left join (select a.record_id,a.member_id from call_custom_record_details AS a where a.del_flag = '0'-->
        <!--        and a.team_id = #{teamId}-->
        <!--        <if test="memberIdList != null and memberIdList.size() > 0">-->
        <!--            and a.member_id in-->
        <!--            <foreach collection="memberIdList" item="memberId" separator="," open="(" close=")">-->
        <!--                #{memberId}-->
        <!--            </foreach>-->
        <!--        </if>-->
        <!--        group by a.record_id,a.member_id-->
        <!--        ) AS det ON (tas.custom_id = det.record_id)-->
        left join team_create AS cre ON (tas.create_id = cre.id)
        left join team_employees AS emp ON (tas.create_id = emp.id)
        where tas.del_flag = '0'
        and tas.team_id = #{teamId}
        <if test="memberIdList != null and memberIdList.size() > 0">
            and (
            tas.employee_id in
            <foreach collection="memberIdList" item="memberId" separator="," open="(" close=")">
                #{memberId}
            </foreach>
            or
            <foreach collection="memberIdList" item="memberId" separator="or" open="(" close=")">
                FIND_IN_SET(#{memberId},tas.task_allocation_personnel_id)
            </foreach>
            <if test="customReloadIdList != null and customReloadIdList.size() > 0">
                or tas.custom_id in
                <foreach collection="customReloadIdList" item="customReloadId" separator="," open="(" close=")">
                    #{customReloadId}
                </foreach>
            </if>
            )
        </if>
        <if test="taskNameList != null and taskNameList.size() > 0">
            and tas.task_name in
            <foreach collection="taskNameList" item="taskName" separator="," open="(" close=")">
                #{taskName}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and tas.status in
            <foreach collection="statusList" item="status" separator="," open="(" close=")">
                #{status}
            </foreach>
        </if>
        <if test="createTime1 != null">
            and tas.create_time &gt;= #{createTime1}
        </if>
        <if test="createTime2 != null">
            and tas.create_time &lt;= #{createTime2}
        </if>
        order by tas.id desc
    </select>

    <select id="selectRecordId" resultType="java.lang.Long">
        select record_id
        from call_custom_record_details
        where del_flag = '0'
        and team_id = #{teamId}
        <if test="memberIdList != null and memberIdList.size() > 0">
            and member_id in
            <foreach collection="memberIdList" item="memberId" separator="," open="(" close=")">
                #{memberId}
            </foreach>
        </if>
        group by record_id
    </select>

    <select id="selectTaskUuidList" resultType="java.lang.String">
        select call_center_uuid
        from call_intelligence_task
        where del_flag = '0'
        and team_id = #{teamId}
        and call_center_uuid is not null
        and id in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectCustomUuidList" resultType="java.lang.String">
        select DISTINCT det.uuid
        from call_custom_record_details AS det
                 left join call_record AS rec ON (det.uuid = rec.custom_uuid and rec.del_flag = '0')
        where det.del_flag = '0'
          and det.team_id = #{teamId}
          and rec.task_uuid = #{taskUuid}
          and rec.agent_duration > 0
    </select>

    <select id="getNotConnectedCustomUuid" resultType="java.lang.String">
        select DISTINCT det.uuid
        from call_custom_record_details AS det
        left join call_custom_record AS rec ON (det.record_id = rec.id and rec.del_flag = '0')
        left join call_intelligence_task AS tas ON (tas.custom_id = rec.id and tas.del_flag = '0')
        where det.del_flag = '0'
        and det.team_id = #{teamId}
        and tas.call_center_uuid = #{taskUuid}
        <if test="customUuidList != null and customUuidList.size > 0">
            and det.uuid not in
            <foreach collection="customUuidList" item="customUuid" separator="," open="(" close=")">
                #{customUuid}
            </foreach>
        </if>
    </select>

    <select id="selectTaskNameList" resultType="java.lang.String">
        select DISTINCT task_name
        from call_intelligence_task
        where del_flag = '0'
          and team_id = #{teamId}
    </select>

    <select id="selectCallRecordCount" resultType="java.lang.Integer">
        select count(custom_uuid)
        from call_record
        where del_flag = '0'
          and team_id = #{teamId}
          and task_uuid = #{taskUuid}
          and connect_flag = #{connectFlag}
    </select>

    <select id="selectCallReloadList" resultType="java.lang.String">
        select DISTINCT custom_uuid
        from call_record
        where del_flag = '0'
        and team_id = #{teamId}
        and task_uuid = #{taskUuid}
        and connect_flag = #{connectFlag}
        <if test="customUuidList != null and customUuidList.size > 0">
            and custom_uuid not in
            <foreach collection="customUuidList" item="customUuid" separator="," open="(" close=")">
                #{customUuid}
            </foreach>
        </if>
    </select>

    <select id="selectCallRecordList" resultType="com.zws.call.pojo.CallRecordDataPojo">
        select rec.id AS id,
        rec.call_from AS callFrom,
        rec.call_to AS callTo,
        det.case_name AS name,
        rec.call_time AS callTime,
        rec.sip_number AS sipNumber,
        emp.employee_name AS employeeName,
        rec.connect_flag AS connectFlag,
        rec.agent_duration AS agentDuration,
        rec.recording AS recording,
        rec.service_host AS serviceHost,
        rec.call_result AS callResult,
        det.case_id AS caseId
        from call_record AS rec
        left join call_custom_record_details AS det ON (rec.custom_uuid = det.uuid)
        left join team_employees AS emp ON (emp.id = rec.odv_id)
        where rec.del_flag = '0'
        and rec.team_id = #{teamId}
        and rec.task_uuid = #{taskUuid}
        <if test="connectFlag != null">
            and rec.connect_flag = #{connectFlag}
        </if>
        <if test="callResults != null">
            and rec.call_result in
            <foreach collection="callResults" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectEmployeesData" resultType="com.zws.call.pojo.EmployeesPojo">
        select id AS id,
        employee_name AS employeeName,
        employees_working AS employeesWorking
        from team_employees
        where delete_logo = 0
        and create_id = #{teamId}
        and id in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectEmployeeIdList" resultType="java.lang.Integer">
        select
            DISTINCT
            cs.odv_id AS odvId
        FROM
            call_sip AS cs
            LEFT JOIN case_manage AS cm ON ( cm.odv_id = cs.odv_id OR cm.mediator_id = cs.odv_id and cm.del_flag = 0)
        WHERE
        cs.del_flag = 0 and cs.seats_type = 2
        and outsourcing_team_id = #{teamId}
        and case_id in
        <foreach collection="caseIdList" item="caseId" separator="," open="(" close=")">
            #{caseId}
        </foreach>
    </select>

    <select id="selectTsEmployeeIdList" resultType="java.lang.Integer">
        select DISTINCT(mediator_id) AS mediatorId
        from case_manage
        where del_flag = 0
        and outsourcing_team_id = #{teamId}
        and mediator_id is not null
        and case_id in
        <foreach collection="caseIdList" item="caseId" separator="," open="(" close=")">
            #{caseId}
        </foreach>
    </select>

    <select id="selectStEmployeeIdList" resultType="java.lang.Integer">
        select DISTINCT(IFNULL(mediator_id,odv_id)) AS mediatorId
        from case_manage
        where del_flag = 0
        and outsourcing_team_id = #{teamId}
        and case_id in
        <foreach collection="caseIdList" item="caseId" separator="," open="(" close=")">
            #{caseId}
        </foreach>
    </select>

    <select id="selectSipOdvIdList" resultType="java.lang.Integer">
        select DISTINCT(odv_id) AS odvId
        from call_sip
        where del_flag = '0'
        and seats_type = 2
        and bind_status = 1
        and team_id = #{teamId}
        and odv_id in
        <foreach collection="odvIdList" item="odvId" separator="," open="(" close=")">
            #{odvId}
        </foreach>
    </select>

    <select id="selectContactsList" resultType="com.zws.call.domain.CallCustomDetails">
        select cas.case_id AS caseId,
        cas.case_state AS caseState,
        con.id AS contactsId,
        con.contact_name AS caseName,
        con.contact_phone AS casePhone,
        emp.employees_working AS userNo
        from case_manage AS cas
        left join case_info_contact AS con ON (cas.case_id = con.case_id and con.del_flag = '0' and con.phone_state = 0)
        left join team_employees AS emp ON ( IFNULL(cas.mediator_id,cas.odv_id) = emp.id  and emp.delete_logo = 0)
        where cas.del_flag = 0 and cas.settlement_status = 0
        and cas.outsourcing_team_id = #{teamId}
        and cas.case_id in
        <foreach collection="caseIdList" item="caseId" separator="," open="(" close=")">
            #{caseId}
        </foreach>
        <if test="contactRelation != null">
            and con.contact_relation = #{contactRelation}
        </if>
    </select>

    <select id="selectCaseManageCaseId" resultMap="BaseCaseResultMap">
        select id,
               case_id,
               contract_no,
               allocated_state,
               case_state,
               settlement_status,
               entrusting_party_id,
               entrusting_party_name,
               product_id,
               product_name,
               batch_num,
               entrusting_case_batch_num,
               outsourcing_team_id,
               outsourcing_team_name,
               client_name,
               client_sex,
               client_idcard,
               client_census_register,
               client_phone,
               client_money,
               client_residual_principal,
               client_overdue_start,
               account_period,
               follow_up_state,
               follow_up_start,
               follow_up_ast,
               entrusting_case_date,
               return_case_date,
               area,
               label,
               odv_id,
               odv_name,
               urge_state,
               allocated_time,
               urge_power,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from case_manage
        where del_flag = 0 and case_id = #{caseId}
    </select>
    <select id="selectEmployeeIdListAppeal" resultType="java.lang.Integer">
        select
        DISTINCT
        cs.odv_id AS odvId
        FROM
        call_sip AS cs
        LEFT JOIN case_manage AS cm ON (cm.mediator_id = cs.odv_id and cm.del_flag = 0)
        WHERE
        cs.del_flag = 0 and cs.seats_type = 2
        and outsourcing_team_id = #{teamId}
        and case_id in
        <foreach collection="caseIdList" item="caseId" separator="," open="(" close=")">
            #{caseId}
        </foreach>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from call_intelligence_task
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.system.api.domain.IntelligenceTask" useGeneratedKeys="true"
            keyProperty="id">
        insert into call_intelligence_task (id, task_name, execution_time,
                                            execution_call_time, recall_count, recall_minute,
                                            is_screen, single_call_number, team_id,
                                            employee_id, status, call_count,
                                            create_id, create_time, create_by,
                                            create_type, fee, custom_name_montage,
                                            custom_id_montage, task_allocation_personnel_id,
                                            task_allocation_personnel_seating, answer_settings,
                                            custom_id, call_center_uuid, remark, del_flag, operation_type,
                                            call_recipient)
        values (#{id,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, #{executionTime,jdbcType=VARCHAR},
                #{executionCallTime,jdbcType=VARCHAR}, #{recallCount,jdbcType=INTEGER},
                #{recallMinute,jdbcType=INTEGER},
                #{isScreen,jdbcType=INTEGER}, #{singleCallNumber,jdbcType=INTEGER}, #{teamId,jdbcType=INTEGER},
                #{employeeId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{callCount,jdbcType=INTEGER},
                #{createId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR},
                #{createType,jdbcType=INTEGER}, #{fee,jdbcType=DECIMAL}, #{customNameMontage,jdbcType=VARCHAR},
                #{customIdMontage,jdbcType=VARCHAR}, #{taskAllocationPersonnelId,jdbcType=VARCHAR},
                #{taskAllocationPersonnelSeating,jdbcType=VARCHAR}, #{answerSettings,jdbcType=INTEGER},
                #{customId,jdbcType=BIGINT}, #{callCenterUuid,jdbcType=CHAR}, #{remark,jdbcType=LONGVARCHAR},
                #{delFlag}, #{operationType}, #{callRecipient})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.system.api.domain.IntelligenceTask">
        insert into call_intelligence_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskName != null">
                task_name,
            </if>
            <if test="executionTime != null">
                execution_time,
            </if>
            <if test="executionCallTime != null">
                execution_call_time,
            </if>
            <if test="recallCount != null">
                recall_count,
            </if>
            <if test="recallMinute != null">
                recall_minute,
            </if>
            <if test="isScreen != null">
                is_screen,
            </if>
            <if test="singleCallNumber != null">
                single_call_number,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="employeeId != null">
                employee_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="callCount != null">
                call_count,
            </if>
            <if test="createId != null">
                create_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createType != null">
                create_type,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="customNameMontage != null">
                custom_name_montage,
            </if>
            <if test="customIdMontage != null">
                custom_id_montage,
            </if>
            <if test="taskAllocationPersonnelId != null">
                task_allocation_personnel_id,
            </if>
            <if test="taskAllocationPersonnelSeating != null">
                task_allocation_personnel_seating,
            </if>
            <if test="answerSettings != null">
                answer_settings,
            </if>
            <if test="customId != null">
                custom_id,
            </if>
            <if test="callCenterUuid != null">
                call_center_uuid,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="taskName != null">
                #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="executionTime != null">
                #{executionTime,jdbcType=VARCHAR},
            </if>
            <if test="executionCallTime != null">
                #{executionCallTime,jdbcType=VARCHAR},
            </if>
            <if test="recallCount != null">
                #{recallCount,jdbcType=INTEGER},
            </if>
            <if test="recallMinute != null">
                #{recallMinute,jdbcType=INTEGER},
            </if>
            <if test="isScreen != null">
                #{isScreen,jdbcType=INTEGER},
            </if>
            <if test="singleCallNumber != null">
                #{singleCallNumber,jdbcType=INTEGER},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=INTEGER},
            </if>
            <if test="employeeId != null">
                #{employeeId,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="callCount != null">
                #{callCount,jdbcType=INTEGER},
            </if>
            <if test="createId != null">
                #{createId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createType != null">
                #{createType,jdbcType=INTEGER},
            </if>
            <if test="fee != null">
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="customNameMontage != null">
                #{customNameMontage,jdbcType=VARCHAR},
            </if>
            <if test="customIdMontage != null">
                #{customIdMontage,jdbcType=VARCHAR},
            </if>
            <if test="taskAllocationPersonnelId != null">
                #{taskAllocationPersonnelId,jdbcType=VARCHAR},
            </if>
            <if test="taskAllocationPersonnelSeating != null">
                #{taskAllocationPersonnelSeating,jdbcType=VARCHAR},
            </if>
            <if test="answerSettings != null">
                #{answerSettings,jdbcType=INTEGER},
            </if>
            <if test="customId != null">
                #{customId,jdbcType=BIGINT},
            </if>
            <if test="callCenterUuid != null">
                #{callCenterUuid,jdbcType=CHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.system.api.domain.IntelligenceTask">
        update call_intelligence_task
        <set>
            <if test="taskName != null">
                task_name = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="executionTime != null">
                execution_time = #{executionTime,jdbcType=VARCHAR},
            </if>
            <if test="executionCallTime != null">
                execution_call_time = #{executionCallTime,jdbcType=VARCHAR},
            </if>
            <if test="recallCount != null">
                recall_count = #{recallCount,jdbcType=INTEGER},
            </if>
            <if test="recallMinute != null">
                recall_minute = #{recallMinute,jdbcType=INTEGER},
            </if>
            <if test="isScreen != null">
                is_screen = #{isScreen,jdbcType=INTEGER},
            </if>
            <if test="singleCallNumber != null">
                single_call_number = #{singleCallNumber,jdbcType=INTEGER},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=INTEGER},
            </if>
            <if test="employeeId != null">
                employee_id = #{employeeId,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="callCount != null">
                call_count = #{callCount,jdbcType=INTEGER},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createType != null">
                create_type = #{createType,jdbcType=INTEGER},
            </if>
            <if test="fee != null">
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="customNameMontage != null">
                custom_name_montage = #{customNameMontage,jdbcType=VARCHAR},
            </if>
            <if test="customIdMontage != null">
                custom_id_montage = #{customIdMontage,jdbcType=VARCHAR},
            </if>
            <if test="taskAllocationPersonnelId != null">
                task_allocation_personnel_id = #{taskAllocationPersonnelId,jdbcType=VARCHAR},
            </if>
            <if test="taskAllocationPersonnelSeating != null">
                task_allocation_personnel_seating = #{taskAllocationPersonnelSeating,jdbcType=VARCHAR},
            </if>
            <if test="answerSettings != null">
                answer_settings = #{answerSettings,jdbcType=INTEGER},
            </if>
            <if test="customId != null">
                custom_id = #{customId,jdbcType=BIGINT},
            </if>
            <if test="callCenterUuid != null">
                call_center_uuid = #{callCenterUuid,jdbcType=CHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=LONGVARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByUuidList" parameterType="com.zws.system.api.domain.IntelligenceTask">
        update call_intelligence_task
        <set>
            <if test="taskName != null">
                task_name = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="executionTime != null">
                execution_time = #{executionTime,jdbcType=VARCHAR},
            </if>
            <if test="executionCallTime != null">
                execution_call_time = #{executionCallTime,jdbcType=VARCHAR},
            </if>
            <if test="recallCount != null">
                recall_count = #{recallCount,jdbcType=INTEGER},
            </if>
            <if test="recallMinute != null">
                recall_minute = #{recallMinute,jdbcType=INTEGER},
            </if>
            <if test="isScreen != null">
                is_screen = #{isScreen,jdbcType=INTEGER},
            </if>
            <if test="singleCallNumber != null">
                single_call_number = #{singleCallNumber,jdbcType=INTEGER},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=INTEGER},
            </if>
            <if test="employeeId != null">
                employee_id = #{employeeId,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="callCount != null">
                call_count = #{callCount,jdbcType=INTEGER},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createType != null">
                create_type = #{createType,jdbcType=INTEGER},
            </if>
            <if test="fee != null">
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="customNameMontage != null">
                custom_name_montage = #{customNameMontage,jdbcType=VARCHAR},
            </if>
            <if test="customIdMontage != null">
                custom_id_montage = #{customIdMontage,jdbcType=VARCHAR},
            </if>
            <if test="taskAllocationPersonnelId != null">
                task_allocation_personnel_id = #{taskAllocationPersonnelId,jdbcType=VARCHAR},
            </if>
            <if test="taskAllocationPersonnelSeating != null">
                task_allocation_personnel_seating = #{taskAllocationPersonnelSeating,jdbcType=VARCHAR},
            </if>
            <if test="answerSettings != null">
                answer_settings = #{answerSettings,jdbcType=INTEGER},
            </if>
            <if test="customId != null">
                custom_id = #{customId,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=LONGVARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where call_center_uuid in
        <foreach collection="callCenterUuidList" item="callCenterUuid" separator="," open="(" close=")">
            #{callCenterUuid}
        </foreach>
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.zws.system.api.domain.IntelligenceTask">
        update call_intelligence_task
        set task_name                         = #{taskName,jdbcType=VARCHAR},
            execution_time                    = #{executionTime,jdbcType=VARCHAR},
            execution_call_time               = #{executionCallTime,jdbcType=VARCHAR},
            recall_count                      = #{recallCount,jdbcType=INTEGER},
            recall_minute                     = #{recallMinute,jdbcType=INTEGER},
            is_screen                         = #{isScreen,jdbcType=INTEGER},
            single_call_number                = #{singleCallNumber,jdbcType=INTEGER},
            team_id                           = #{teamId,jdbcType=INTEGER},
            employee_id                       = #{employeeId,jdbcType=INTEGER},
            status                            = #{status,jdbcType=INTEGER},
            call_count                        = #{callCount,jdbcType=INTEGER},
            create_id                         = #{createId,jdbcType=INTEGER},
            create_time                       = #{createTime,jdbcType=TIMESTAMP},
            create_by                         = #{createBy,jdbcType=VARCHAR},
            create_type                       = #{createType,jdbcType=INTEGER},
            fee                               = #{fee,jdbcType=DECIMAL},
            custom_name_montage               = #{customNameMontage,jdbcType=VARCHAR},
            custom_id_montage                 = #{customIdMontage,jdbcType=VARCHAR},
            task_allocation_personnel_id      = #{taskAllocationPersonnelId,jdbcType=VARCHAR},
            task_allocation_personnel_seating = #{taskAllocationPersonnelSeating,jdbcType=VARCHAR},
            answer_settings                   = #{answerSettings,jdbcType=INTEGER},
            custom_id                         = #{customId,jdbcType=BIGINT},
            call_center_uuid                  = #{callCenterUuid,jdbcType=CHAR},
            remark                            = #{remark,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.system.api.domain.IntelligenceTask">
        update call_intelligence_task
        set task_name                         = #{taskName,jdbcType=VARCHAR},
            execution_time                    = #{executionTime,jdbcType=VARCHAR},
            execution_call_time               = #{executionCallTime,jdbcType=VARCHAR},
            recall_count                      = #{recallCount,jdbcType=INTEGER},
            recall_minute                     = #{recallMinute,jdbcType=INTEGER},
            is_screen                         = #{isScreen,jdbcType=INTEGER},
            single_call_number                = #{singleCallNumber,jdbcType=INTEGER},
            team_id                           = #{teamId,jdbcType=INTEGER},
            employee_id                       = #{employeeId,jdbcType=INTEGER},
            status                            = #{status,jdbcType=INTEGER},
            call_count                        = #{callCount,jdbcType=INTEGER},
            create_id                         = #{createId,jdbcType=INTEGER},
            create_time                       = #{createTime,jdbcType=TIMESTAMP},
            create_by                         = #{createBy,jdbcType=VARCHAR},
            create_type                       = #{createType,jdbcType=INTEGER},
            fee                               = #{fee,jdbcType=DECIMAL},
            custom_name_montage               = #{customNameMontage,jdbcType=VARCHAR},
            custom_id_montage                 = #{customIdMontage,jdbcType=VARCHAR},
            task_allocation_personnel_id      = #{taskAllocationPersonnelId,jdbcType=VARCHAR},
            task_allocation_personnel_seating = #{taskAllocationPersonnelSeating,jdbcType=VARCHAR},
            answer_settings                   = #{answerSettings,jdbcType=INTEGER},
            custom_id                         = #{customId,jdbcType=BIGINT},
            call_center_uuid                  = #{callCenterUuid,jdbcType=CHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>