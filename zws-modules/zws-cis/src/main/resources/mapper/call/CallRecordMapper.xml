<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.call.mapper.CallRecordMapper">
    <resultMap id="BaseResultMap" type="com.zws.call.domain.CallRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="contact_id" jdbcType="BIGINT" property="contactId"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="call_time" jdbcType="TIMESTAMP" property="callTime"/>
        <result column="agent_duration" jdbcType="INTEGER" property="agentDuration"/>
        <result column="call_from" jdbcType="VARCHAR" property="callFrom"/>
        <result column="call_to" jdbcType="VARCHAR" property="callTo"/>
        <result column="number" jdbcType="VARCHAR" property="number"/>
        <result column="callroter" jdbcType="VARCHAR" property="callroter"/>
        <result column="callid" jdbcType="VARCHAR" property="callid"/>
        <result column="answer" jdbcType="CHAR" property="answer"/>
        <result column="recording" jdbcType="VARCHAR" property="recording"/>
        <result column="hangup_cause" jdbcType="VARCHAR" property="hangupCause"/>
        <result column="trunk_name" jdbcType="VARCHAR" property="trunkName"/>
        <result column="call_prefix" jdbcType="VARCHAR" property="callPrefix"/>
        <result column="company_num" jdbcType="VARCHAR" property="companyNum"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="sip_number" jdbcType="VARCHAR" property="sipNumber"/>
        <result column="answer_start" jdbcType="TIMESTAMP" property="answerStart"/>
        <result column="answer_end" jdbcType="TIMESTAMP" property="answerEnd"/>
        <result column="callback_time" jdbcType="TIMESTAMP" property="callbackTime"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="borrower" jdbcType="VARCHAR" property="borrower"/>
        <result column="service_host" jdbcType="VARCHAR" property="serviceHost"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>

    <resultMap id="BaseResultCallSipMap" type="com.zws.call.domain.CallSip">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="sip_number" jdbcType="VARCHAR" property="sipNumber"/>
        <result column="sip_password" jdbcType="VARCHAR" property="sipPassword"/>

        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="company_num" jdbcType="VARCHAR" property="companyNum"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="bind_status" jdbcType="CHAR" property="bindStatus"/>
        <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="team_type" jdbcType="INTEGER" property="teamType"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , contact_id, case_id, call_time, agent_duration, call_from, call_to, number, callroter,
    callid, answer, recording, hangup_cause, trunk_name, call_prefix, company_num, company_name,
    sip_number, answer_start, answer_end, callback_time, team_id, odv_id, service_host,
    create_time, del_flag
    </sql>
    <sql id="Base_Column_Join_List">
        cr
        .
        id
        , cr.contact_id, cr.case_id, cr.call_time, cr.agent_duration, cr.call_from, cr.call_to, cr.number, cr.callroter,
    cr.callid, cr.answer, cr.recording, cr.hangup_cause, cr.trunk_name, cr.call_prefix, cr.company_num, cr.company_name,
    cr.sip_number, cr.answer_start, cr.answer_end, cr.callback_time, cr.team_id, cr.odv_id, cr.service_host,cr.borrower,
    cr.create_time, cr.del_flag,t.cname as teamName,tu.employee_name as odvName
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from call_record
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_Join_List"/>
        from call_record as cr
        left join team_create as t on(cr.team_id=t.id)
        left join team_employees as tu on(cr.odv_id= tu.id)
        where cr.del_flag=0
        <if test="answer!=null">
            and cr.answer=#{answer}
        </if>
        <if test="callFrom!=null and callFrom!=''">
            and cr.call_from like concat('%',#{callFrom},'%')
        </if>
        <if test="callTo!=null and callTo!=''">
            and cr.call_to like concat('%',#{callTo},'%')
        </if>
        <if test="teamName!=null and teamName!=''">
            and t.cname like concat('%',#{teamName},'%')
        </if>
        <if test="odvName!=null and odvName!=''">
            and tu.employee_name like concat('%',#{odvName},'%')
        </if>
        <if test="callroter!=null and callroter!=''">
            and cr.callroter like concat('%',#{callroter},'%')
        </if>
        <if test="callroter!=null and callroter!=''">
            and cr.callroter like concat('%',#{callroter},'%')
        </if>

        <if test="callTime1!=null ">
            and cr.call_time &gt;= #{callTime1}
        </if>
        <if test="callTime2!=null ">
            and cr.call_time &lt;= #{callTime2}
        </if>
        <if test="agentDuration1!=null ">
            and cr.agent_duration &gt;= #{agentDuration1}
        </if>
        <if test="agentDuration2!=null ">
            and cr.agent_duration &lt;= #{agentDuration2}
        </if>
        <if test="ids!=null and ids.size()>0">
            and cr.id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        order by cr.create_time desc
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from call_record
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.call.domain.CallRecord" keyProperty="id" useGeneratedKeys="true">
        insert into call_record (id, contact_id, case_id,
                                 call_time, agent_duration, call_from,
                                 call_to, number, callroter,
                                 callid, answer, recording,
                                 hangup_cause, trunk_name, call_prefix,
                                 company_num, company_name, sip_number,
                                 answer_start, answer_end, callback_time,
                                 team_id, odv_id, service_host,
                                 create_time, del_flag, borrower, team_type, task_uuid, connect_flag,
                                 custom_uuid)
        values (#{id,jdbcType=BIGINT}, #{contactId,jdbcType=BIGINT}, #{caseId,jdbcType=BIGINT},
                #{callTime,jdbcType=TIMESTAMP}, #{agentDuration,jdbcType=INTEGER}, #{callFrom,jdbcType=VARCHAR},
                #{callTo,jdbcType=VARCHAR}, #{number,jdbcType=VARCHAR}, #{callroter,jdbcType=VARCHAR},
                #{callid,jdbcType=VARCHAR}, #{answer,jdbcType=CHAR}, #{recording,jdbcType=VARCHAR},
                #{hangupCause,jdbcType=VARCHAR}, #{trunkName,jdbcType=VARCHAR}, #{callPrefix,jdbcType=VARCHAR},
                #{companyNum,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{sipNumber,jdbcType=VARCHAR},
                #{answerStart,jdbcType=TIMESTAMP}, #{answerEnd,jdbcType=TIMESTAMP}, #{callbackTime,jdbcType=TIMESTAMP},
                #{teamId,jdbcType=BIGINT}, #{odvId,jdbcType=BIGINT}, #{serviceHost,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR}, #{borrower}, #{teamType}, #{taskUuid},
                #{connectFlag}, #{customUuid})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.call.domain.CallRecord">
        insert into call_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="contactId != null">
                contact_id,
            </if>
            <if test="caseId != null">
                case_id,
            </if>
            <if test="callTime != null">
                call_time,
            </if>
            <if test="agentDuration != null">
                agent_duration,
            </if>
            <if test="callFrom != null">
                call_from,
            </if>
            <if test="callTo != null">
                call_to,
            </if>
            <if test="number != null">
                number,
            </if>
            <if test="callroter != null">
                callroter,
            </if>
            <if test="callid != null">
                callid,
            </if>
            <if test="answer != null">
                answer,
            </if>
            <if test="recording != null">
                recording,
            </if>
            <if test="hangupCause != null">
                hangup_cause,
            </if>
            <if test="trunkName != null">
                trunk_name,
            </if>
            <if test="callPrefix != null">
                call_prefix,
            </if>
            <if test="companyNum != null">
                company_num,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="sipNumber != null">
                sip_number,
            </if>
            <if test="answerStart != null">
                answer_start,
            </if>
            <if test="answerEnd != null">
                answer_end,
            </if>
            <if test="callbackTime != null">
                callback_time,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="odvId != null">
                odv_id,
            </if>
            <if test="serviceHost != null">
                service_host,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="contactId != null">
                #{contactId,jdbcType=BIGINT},
            </if>
            <if test="caseId != null">
                #{caseId,jdbcType=BIGINT},
            </if>
            <if test="callTime != null">
                #{callTime,jdbcType=TIMESTAMP},
            </if>
            <if test="agentDuration != null">
                #{agentDuration,jdbcType=INTEGER},
            </if>
            <if test="callFrom != null">
                #{callFrom,jdbcType=VARCHAR},
            </if>
            <if test="callTo != null">
                #{callTo,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                #{number,jdbcType=VARCHAR},
            </if>
            <if test="callroter != null">
                #{callroter,jdbcType=VARCHAR},
            </if>
            <if test="callid != null">
                #{callid,jdbcType=VARCHAR},
            </if>
            <if test="answer != null">
                #{answer,jdbcType=CHAR},
            </if>
            <if test="recording != null">
                #{recording,jdbcType=VARCHAR},
            </if>
            <if test="hangupCause != null">
                #{hangupCause,jdbcType=VARCHAR},
            </if>
            <if test="trunkName != null">
                #{trunkName,jdbcType=VARCHAR},
            </if>
            <if test="callPrefix != null">
                #{callPrefix,jdbcType=VARCHAR},
            </if>
            <if test="companyNum != null">
                #{companyNum,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="sipNumber != null">
                #{sipNumber,jdbcType=VARCHAR},
            </if>
            <if test="answerStart != null">
                #{answerStart,jdbcType=TIMESTAMP},
            </if>
            <if test="answerEnd != null">
                #{answerEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="callbackTime != null">
                #{callbackTime,jdbcType=TIMESTAMP},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=BIGINT},
            </if>
            <if test="odvId != null">
                #{odvId,jdbcType=BIGINT},
            </if>
            <if test="serviceHost != null">
                #{serviceHost,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.call.domain.CallRecord">
        update call_record
        <set>
            <if test="contactId != null">
                contact_id = #{contactId,jdbcType=BIGINT},
            </if>
            <if test="caseId != null">
                case_id = #{caseId,jdbcType=BIGINT},
            </if>
            <if test="callTime != null">
                call_time = #{callTime,jdbcType=TIMESTAMP},
            </if>
            <if test="agentDuration != null">
                agent_duration = #{agentDuration,jdbcType=INTEGER},
            </if>
            <if test="callFrom != null">
                call_from = #{callFrom,jdbcType=VARCHAR},
            </if>
            <if test="callTo != null">
                call_to = #{callTo,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                number = #{number,jdbcType=VARCHAR},
            </if>
            <if test="callroter != null">
                callroter = #{callroter,jdbcType=VARCHAR},
            </if>
            <if test="callid != null">
                callid = #{callid,jdbcType=VARCHAR},
            </if>
            <if test="answer != null">
                answer = #{answer,jdbcType=CHAR},
            </if>
            <if test="recording != null">
                recording = #{recording,jdbcType=VARCHAR},
            </if>
            <if test="hangupCause != null">
                hangup_cause = #{hangupCause,jdbcType=VARCHAR},
            </if>
            <if test="trunkName != null">
                trunk_name = #{trunkName,jdbcType=VARCHAR},
            </if>
            <if test="callPrefix != null">
                call_prefix = #{callPrefix,jdbcType=VARCHAR},
            </if>
            <if test="companyNum != null">
                company_num = #{companyNum,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="sipNumber != null">
                sip_number = #{sipNumber,jdbcType=VARCHAR},
            </if>
            <if test="answerStart != null">
                answer_start = #{answerStart,jdbcType=TIMESTAMP},
            </if>
            <if test="answerEnd != null">
                answer_end = #{answerEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="callbackTime != null">
                callback_time = #{callbackTime,jdbcType=TIMESTAMP},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
            <if test="odvId != null">
                odv_id = #{odvId,jdbcType=BIGINT},
            </if>
            <if test="serviceHost != null">
                service_host = #{serviceHost,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="borrower != null">
                borrower = #{borrower},
            </if>
            <if test="teamType != null">
                team_type = #{teamType},
            </if>
            <if test="taskUuid != null">
                task_uuid = #{taskUuid},
            </if>
            <if test="connectFlag != null">
                connect_flag = #{connectFlag},
            </if>
            <if test="customUuid != null">
                custom_uuid = #{customUuid},
            </if>
            <if test="callResult != null">
                call_result = #{callResult},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.call.domain.CallRecord">
        update call_record
        set contact_id     = #{contactId,jdbcType=BIGINT},
            case_id        = #{caseId,jdbcType=BIGINT},
            call_time      = #{callTime,jdbcType=TIMESTAMP},
            agent_duration = #{agentDuration,jdbcType=INTEGER},
            call_from      = #{callFrom,jdbcType=VARCHAR},
            call_to        = #{callTo,jdbcType=VARCHAR},
            number         = #{number,jdbcType=VARCHAR},
            callroter      = #{callroter,jdbcType=VARCHAR},
            callid         = #{callid,jdbcType=VARCHAR},
            answer         = #{answer,jdbcType=CHAR},
            recording      = #{recording,jdbcType=VARCHAR},
            hangup_cause   = #{hangupCause,jdbcType=VARCHAR},
            trunk_name     = #{trunkName,jdbcType=VARCHAR},
            call_prefix    = #{callPrefix,jdbcType=VARCHAR},
            company_num    = #{companyNum,jdbcType=VARCHAR},
            company_name   = #{companyName,jdbcType=VARCHAR},
            sip_number     = #{sipNumber,jdbcType=VARCHAR},
            answer_start   = #{answerStart,jdbcType=TIMESTAMP},
            answer_end     = #{answerEnd,jdbcType=TIMESTAMP},
            callback_time  = #{callbackTime,jdbcType=TIMESTAMP},
            team_id        = #{teamId,jdbcType=BIGINT},
            odv_id         = #{odvId,jdbcType=BIGINT},
            service_host   = #{serviceHost,jdbcType=VARCHAR},
            create_time    = #{createTime,jdbcType=TIMESTAMP},
            del_flag       = #{delFlag,jdbcType=CHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--根据手机号查询案件id-->
    <select id="selectCaseIdPhone" resultType="java.lang.Long">
        select DISTINCT cm.case_id
        from case_manage AS cm
        LEFT JOIN case_info_contact AS con ON (con.case_id = cm.case_id and con.del_flag = 0)
        where cm.del_flag = 0
        and con.phone_state = 0
        and con.contact_phone = #{contactPhone}
        and cm.outsourcing_team_id = #{teamId}
        <if test="userId != null">and cm.odv_id = #{userId}</if>
        order by cm.case_id asc
    </select>
    <select id="selectByTeamId" resultType="com.zws.call.pojo.TeamPojo">
        select id        AS userId,
               create_id AS teamId
        from team_employees
        where delete_logo = 0
          and sip_number = #{sipNumber}
    </select>

    <select id="getClientName" resultType="java.lang.String">
        select client_name
        from case_manage
        where del_flag=0
        and case_id in
        <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        order by case_id asc
    </select>

    <select id="selectBySipNumber" resultMap="BaseResultCallSipMap">
        select sip.id,
               sip.sip_number,
               sip.sip_password,
               sip.team_id,
               sip.odv_id,
               sip.company_num,
               sip.company_name,
               sip.bind_status,
               sip.bind_time,
               sip.create_by,
               sip.create_time,
               sip.update_by,
               sip.update_time,
               sip.del_flag,
               cre.team_type
        from call_sip AS sip
                 left join team_create AS cre ON (sip.team_id = cre.id)
        where sip.del_flag = '0' and sip.sip_number = #{sipNumber}
    </select>

    <select id="selectSipData" resultType="com.zws.call.domain.CallSip">
        select sip.id           AS id,
               sip.sip_number   AS sipNumber,
               sip.sip_password AS sipPassword,
               sip.seats_type   AS seatsType,
               sip.sip_state    AS sipState,
               cre.company_num  AS companyNum,
               cre.api_flag     AS apiFlag
        from call_sip AS sip
                 left join team_create AS cre ON (sip.team_id = cre.id and cre.delete_logo = 0)
        where sip.del_flag = 0
          and sip.team_id = #{teamId}
          and sip.odv_id = #{odvId}
    </select>

    <select id="selectByCallId" resultType="java.lang.Long">
        select id
        from call_record
        where del_flag = '0'
          and callid = #{callid} LIMIT 1
    </select>

    <select id="selectCountByCallResult" resultType="java.lang.Integer"
            parameterType="java.util.Map">
        select count(1) from call_record
        where del_flag = 0
        and team_id = #{teamId}
        and task_uuid = #{taskUuid}
        and call_result = #{callResult}
    </select>
    <select id="selectCountByCallResultGroupBy" resultType="java.util.Map"
            parameterType="java.util.List">
        select call_result, count(1) as count
        from call_record
        where del_flag = 0
        and team_id = #{teamId}
        and task_uuid = #{taskUuid}
        and call_result in
        <foreach collection="callResults" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by call_result
    </select>
    <select id="selectTaskUuidList" resultType="java.lang.String">
        select task_uuid
        from call_record
        where del_flag = 0
        and create_time >= DATE_SUB(#{now}, INTERVAL 72 HOUR)
        and callroter  = #{callroter}
        group by task_uuid
    </select>

    <update id="updateSipState" parameterType="com.zws.call.domain.CallSip">
        update call_sip
        set sip_state = #{sipState}
        where id = #{id}
    </update>
</mapper>
