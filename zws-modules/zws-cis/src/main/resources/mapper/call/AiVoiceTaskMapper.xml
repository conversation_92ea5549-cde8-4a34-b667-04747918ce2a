<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zws.call.mapper.AiVoiceTaskMapper">

    <resultMap id="BaseResultMap" type="com.zws.call.domain.AiCallIntelligenceTask">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="dialogue_template_id" jdbcType="VARCHAR" property="dialogueTemplateName"/>
        <result column="dialogue_template_content" jdbcType="VARCHAR" property="dialogueTemplateContent"/>
        <result column="execution_time" jdbcType="VARCHAR" property="executionTime"/>
        <result column="task_start_time" jdbcType="VARCHAR" property="taskStartTime"/>
        <result column="task_end_time" jdbcType="VARCHAR" property="taskEndTime"/>
        <result column="execution_call_time" jdbcType="VARCHAR" property="executionCallTime"/>
        <result column="recall_count" jdbcType="INTEGER" property="recallCount"/>
        <result column="recall_minute" jdbcType="INTEGER" property="recallMinute"/>
        <result column="is_send_message" jdbcType="INTEGER" property="isSendMessage"/>
        <result column="message_template_id" jdbcType="VARCHAR" property="messageTemplateName"/>
        <result column="message_template_content" jdbcType="VARCHAR" property="messageTemplateContent"/>
        <result column="sender_target" jdbcType="VARCHAR" property="senderTarget"/>
        <result column="team_id" jdbcType="INTEGER" property="teamId"/>
        <result column="employee_id" jdbcType="INTEGER" property="employeeId"/>
        <result column="call_count" jdbcType="INTEGER" property="callCount"/>
        <result column="robot_count" jdbcType="INTEGER" property="robotCount"/>
        <result column="task_status" jdbcType="INTEGER" property="taskStatus"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="create_id" jdbcType="INTEGER" property="createId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_type" jdbcType="INTEGER" property="createType"/>
        <result column="custom_name_montage" jdbcType="VARCHAR" property="customNameMontage"/>
        <result column="custom_id_montage" jdbcType="VARCHAR" property="customIdMontage"/>
        <result column="task_allocation_personnel_id" jdbcType="VARCHAR" property="taskAllocationPersonnelId"/>
        <result column="task_allocation_personnel_seating" jdbcType="VARCHAR" property="taskAllocationPersonnelSeating"/>
        <result column="custom_id" jdbcType="BIGINT" property="customId"/>
        <result column="call_center_uuid" jdbcType="CHAR" property="callCenterUuid"/>
        <result column="del_flag" jdbcType="VARCHAR" property="delFlag"/>
        <result column="operation_type" jdbcType="INTEGER" property="operationType"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="call_recipient" jdbcType="INTEGER" property="callRecipient"/>
        <result column="voice_tpl_uuid" jdbcType="VARCHAR" property="voiceTplUuid"/>
        <result column="hook_args" jdbcType="VARCHAR" property="hookArgs"/>
        <result column="reachability" jdbcType="DECIMAL" property="reachability"/>
    </resultMap>

    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.zws.system.api.domain.IntelligenceTask">
        <result column="remark" jdbcType="LONGVARCHAR" property="remark"/>
    </resultMap>

    <resultMap id="BaseCaseResultMap" type="com.zws.system.api.domain.CaseManage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="phone_state" jdbcType="INTEGER" property="phoneState"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="allocated_state" jdbcType="VARCHAR" property="allocatedState"/>
        <result column="case_state" jdbcType="VARCHAR" property="caseState"/>
        <result column="entrusting_party_id" jdbcType="BIGINT" property="entrustingPartyId"/>
        <result column="entrusting_party_name" jdbcType="VARCHAR" property="entrustingPartyName"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="batch_num" jdbcType="VARCHAR" property="batchNum"/>
        <result column="entrusting_case_batch_num" jdbcType="VARCHAR" property="entrustingCaseBatchNum"/>
        <result column="outsourcing_team_id" jdbcType="BIGINT" property="outsourcingTeamId"/>
        <result column="outsourcing_team_name" jdbcType="VARCHAR" property="outsourcingTeamName"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="client_sex" jdbcType="VARCHAR" property="clientSex"/>
        <result column="client_idcard" jdbcType="VARCHAR" property="clientIdcard"/>
        <result column="client_census_register" jdbcType="VARCHAR" property="clientCensusRegister"/>
        <result column="client_phone" jdbcType="VARCHAR" property="clientPhone"/>
        <result column="client_money" jdbcType="DECIMAL" property="clientMoney"/>
        <result column="client_residual_principal" jdbcType="DECIMAL" property="clientResidualPrincipal"/>
        <result column="client_overdue_start" jdbcType="TIMESTAMP" property="clientOverdueStart"/>
        <result column="account_period" jdbcType="INTEGER" property="accountPeriod"/>
        <result column="follow_up_state" jdbcType="VARCHAR" property="followUpState"/>
        <result column="follow_up_start" jdbcType="TIMESTAMP" property="followUpStart"/>
        <result column="follow_up_ast" jdbcType="TIMESTAMP" property="followUpAst"/>
        <result column="entrusting_case_date" jdbcType="TIMESTAMP" property="entrustingCaseDate"/>
        <result column="return_case_date" jdbcType="TIMESTAMP" property="returnCaseDate"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="odv_name" jdbcType="VARCHAR" property="odvName"/>
        <result column="urge_state" jdbcType="VARCHAR" property="urgeState"/>
        <result column="allocated_time" jdbcType="TIMESTAMP" property="allocatedTime"/>
        <result column="urge_power" jdbcType="VARCHAR" property="urgePower"/>
        <result column="del_flag" jdbcType="BIT" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="client_id_type" property="clientIdType"/>
        <result column="settlement_status" property="settlementStatus"/>
        <result column="remaining_due" property="remainingDue"/>
        <result column="remaining_due" property="remainingDue"/>
        <result column="sy_yh_principal" property="syYhPrincipal"/>
        <result column="client_birthday" property="clientBirthday"/>
    </resultMap>
    <select id="selectAiTaskNameList" resultType="java.lang.String">
        select DISTINCT task_name
        from ai_call_intelligence_task
        where team_id = #{teamId}
        and del_flag = '0'
    </select>


    <sql id="Base_Column_List">
        id, task_name, dialogue_template_id, dialogue_template_content, execution_time,
    execution_call_time, recall_count, recall_minute, is_send_message, message_template_id,
    message_template_content, sender_target, team_id, employee_id, call_count, robot_count,
    task_status, task_complete_schedule, connection_rate, fee, create_id, create_time,
    create_by, create_type, custom_name_montage, custom_id_montage, task_allocation_personnel_id,
    task_allocation_personnel_seating, custom_id, call_center_uuid, del_flag, operation_type,
    update_time, call_recipient, task_start_time, task_end_time, voice_tpl_uuid, hook_args,
    reachability
    </sql>
    <sql id="Blob_Column_List">
        remark
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from ai_call_intelligence_task
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from ai_call_intelligence_task
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.call.domain.AiCallIntelligenceTask" useGeneratedKeys="true" keyProperty="id">
        insert into ai_call_intelligence_task ( task_name, dialogue_template_name, dialogue_template_content, execution_time,
                                               execution_call_time, recall_count, recall_minute, is_send_message, message_template_name,
                                               message_template_content, sender_target, team_id, employee_id, call_count, robot_count,
                                               task_status,  fee, create_id, create_time,
                                               create_by, create_type, custom_name_montage, custom_id_montage, task_allocation_personnel_id,
                                               task_allocation_personnel_seating, custom_id, call_center_uuid, del_flag, operation_type,
                                               update_time, call_recipient, task_start_time, task_end_time, voice_tpl_uuid, hook_args,remark,
                                               reachability
        )
        values ( #{taskName,jdbcType=VARCHAR}, #{dialogueTemplateName,jdbcType=VARCHAR},
                #{dialogueTemplateContent,jdbcType=VARCHAR}, #{executionTime,jdbcType=VARCHAR},
                #{executionCallTime,jdbcType=VARCHAR}, #{recallCount,jdbcType=INTEGER},
                #{recallMinute,jdbcType=INTEGER}, #{isSendMessage,jdbcType=INTEGER}, #{messageTemplateName,jdbcType=VARCHAR},
                #{messageTemplateContent,jdbcType=VARCHAR}, #{senderTarget,jdbcType=VARCHAR}, #{teamId,jdbcType=INTEGER},
                #{employeeId,jdbcType=INTEGER}, #{callCount,jdbcType=INTEGER}, #{robotCount,jdbcType=INTEGER},
                #{taskStatus,jdbcType=INTEGER},
                #{fee,jdbcType=DECIMAL}, #{createId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
                #{createBy,jdbcType=VARCHAR}, #{createType,jdbcType=INTEGER}, #{customNameMontage,jdbcType=VARCHAR},
                #{customIdMontage,jdbcType=VARCHAR}, #{taskAllocationPersonnelId,jdbcType=VARCHAR},
                #{taskAllocationPersonnelSeating,jdbcType=VARCHAR}, #{customId,jdbcType=BIGINT},
                #{callCenterUuid,jdbcType=CHAR}, #{delFlag,jdbcType=CHAR}, #{operationType,jdbcType=INTEGER},
                #{updateTime,jdbcType=TIMESTAMP}, #{callRecipient,jdbcType=INTEGER},
                #{taskStartTime,jdbcType=VARCHAR},
                #{taskEndTime,jdbcType=VARCHAR},
                #{voiceTplUuid,jdbcType=VARCHAR},
                #{hookArgs,jdbcType=VARCHAR},
                #{remark,jdbcType=LONGVARCHAR},
                #{reachability,jdbcType=DECIMAL}
               )
    </insert>
    <insert id="insertSelective" parameterType="com.zws.call.domain.AiCallIntelligenceTask">
        insert into ai_call_intelligence_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskName != null">
                task_name,
            </if>
            <if test="dialogueTemplateId != null">
                dialogue_template_id,
            </if>
            <if test="dialogueTemplateContent != null">
                dialogue_template_content,
            </if>
            <if test="executionTime != null">
                execution_time,
            </if>
            <if test="executionCallTime != null">
                execution_call_time,
            </if>
            <if test="recallCount != null">
                recall_count,
            </if>
            <if test="recallMinute != null">
                recall_minute,
            </if>
            <if test="isSendMessage != null">
                is_send_message,
            </if>
            <if test="messageTemplateId != null">
                message_template_id,
            </if>
            <if test="messageTemplateContent != null">
                message_template_content,
            </if>
            <if test="senderTarget != null">
                sender_target,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="employeeId != null">
                employee_id,
            </if>
            <if test="callCount != null">
                call_count,
            </if>
            <if test="robotCount != null">
                robot_count,
            </if>
            <if test="taskStatus != null">
                task_status,
            </if>
            <if test="taskCompleteSchedule != null">
                task_complete_schedule,
            </if>
            <if test="connectionRate != null">
                connection_rate,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="createId != null">
                create_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createType != null">
                create_type,
            </if>
            <if test="customNameMontage != null">
                custom_name_montage,
            </if>
            <if test="customIdMontage != null">
                custom_id_montage,
            </if>
            <if test="taskAllocationPersonnelId != null">
                task_allocation_personnel_id,
            </if>
            <if test="taskAllocationPersonnelSeating != null">
                task_allocation_personnel_seating,
            </if>
            <if test="customId != null">
                custom_id,
            </if>
            <if test="callCenterUuid != null">
                call_center_uuid,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="operationType != null">
                operation_type,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="callRecipient != null">
                call_recipient,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="taskName != null">
                #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="dialogueTemplateId != null">
                #{dialogueTemplateId,jdbcType=VARCHAR},
            </if>
            <if test="dialogueTemplateContent != null">
                #{dialogueTemplateContent,jdbcType=VARCHAR},
            </if>
            <if test="executionTime != null">
                #{executionTime,jdbcType=VARCHAR},
            </if>
            <if test="executionCallTime != null">
                #{executionCallTime,jdbcType=VARCHAR},
            </if>
            <if test="recallCount != null">
                #{recallCount,jdbcType=INTEGER},
            </if>
            <if test="recallMinute != null">
                #{recallMinute,jdbcType=INTEGER},
            </if>
            <if test="isSendMessage != null">
                #{isSendMessage,jdbcType=INTEGER},
            </if>
            <if test="messageTemplateId != null">
                #{messageTemplateId,jdbcType=VARCHAR},
            </if>
            <if test="messageTemplateContent != null">
                #{messageTemplateContent,jdbcType=VARCHAR},
            </if>
            <if test="senderTarget != null">
                #{senderTarget,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=INTEGER},
            </if>
            <if test="employeeId != null">
                #{employeeId,jdbcType=INTEGER},
            </if>
            <if test="callCount != null">
                #{callCount,jdbcType=INTEGER},
            </if>
            <if test="robotCount != null">
                #{robotCount,jdbcType=INTEGER},
            </if>
            <if test="taskStatus != null">
                #{taskStatus,jdbcType=INTEGER},
            </if>
            <if test="taskCompleteSchedule != null">
                #{taskCompleteSchedule,jdbcType=CHAR},
            </if>
            <if test="connectionRate != null">
                #{connectionRate,jdbcType=CHAR},
            </if>
            <if test="fee != null">
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="createId != null">
                #{createId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createType != null">
                #{createType,jdbcType=INTEGER},
            </if>
            <if test="customNameMontage != null">
                #{customNameMontage,jdbcType=VARCHAR},
            </if>
            <if test="customIdMontage != null">
                #{customIdMontage,jdbcType=VARCHAR},
            </if>
            <if test="taskAllocationPersonnelId != null">
                #{taskAllocationPersonnelId,jdbcType=VARCHAR},
            </if>
            <if test="taskAllocationPersonnelSeating != null">
                #{taskAllocationPersonnelSeating,jdbcType=VARCHAR},
            </if>
            <if test="customId != null">
                #{customId,jdbcType=BIGINT},
            </if>
            <if test="callCenterUuid != null">
                #{callCenterUuid,jdbcType=CHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="operationType != null">
                #{operationType,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="callRecipient != null">
                #{callRecipient,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertSmsContent">
        insert into ai_task_sms_content
        (phone,content,number,create_time,status,result,call_id)
        values
        (#{phone},#{content},#{number},#{createTime},#{status},#{result},#{callId})



    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.call.domain.AiCallIntelligenceTask">
    update ai_call_intelligence_task
    <set>
        <if test="taskName != null">
            task_name = #{taskName,jdbcType=VARCHAR},
        </if>
        <if test="dialogueTemplateName != null">
            dialogue_template_name = #{dialogueTemplateName,jdbcType=VARCHAR},
        </if>
        <if test="dialogueTemplateContent != null">
            dialogue_template_content = #{dialogueTemplateContent,jdbcType=VARCHAR},
        </if>
        <if test="executionTime != null">
            execution_time = #{executionTime,jdbcType=VARCHAR},
        </if>
        <if test="taskStartTime != null">
            task_start_time = #{taskStartTime,jdbcType=VARCHAR},
        </if>
        <if test="taskEndTime != null">
            task_end_time = #{taskEndTime,jdbcType=VARCHAR},
        </if>
        <if test="executionCallTime != null">
            execution_call_time = #{executionCallTime,jdbcType=VARCHAR},
        </if>
        <if test="recallCount != null">
            recall_count = #{recallCount,jdbcType=INTEGER},
        </if>
        <if test="recallMinute != null">
            recall_minute = #{recallMinute,jdbcType=INTEGER},
        </if>
        <if test="isSendMessage != null">
            is_send_message = #{isSendMessage,jdbcType=INTEGER},
        </if>
        <if test="messageTemplateName != null">
            message_template_name = #{messageTemplateName,jdbcType=VARCHAR},
        </if>
        <if test="messageTemplateContent != null">
            message_template_content = #{messageTemplateContent,jdbcType=VARCHAR},
        </if>
        <if test="senderTarget != null">
            sender_target = #{senderTarget,jdbcType=VARCHAR},
        </if>
        <if test="teamId != null">
            team_id = #{teamId,jdbcType=INTEGER},
        </if>
        <if test="employeeId != null">
            employee_id = #{employeeId,jdbcType=INTEGER},
        </if>
        <if test="callCount != null">
            call_count = #{callCount,jdbcType=INTEGER},
        </if>
        <if test="robotCount != null">
            robot_count = #{robotCount,jdbcType=INTEGER},
        </if>
        <if test="taskStatus != null">
            task_status = #{taskStatus,jdbcType=INTEGER},
        </if>
        <if test="fee != null">
            fee = #{fee,jdbcType=DECIMAL},
        </if>
        <if test="createId != null">
            create_id = #{createId,jdbcType=INTEGER},
        </if>
        <if test="createTime != null">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="createBy != null">
            create_by = #{createBy,jdbcType=VARCHAR},
        </if>
        <if test="createType != null">
            create_type = #{createType,jdbcType=INTEGER},
        </if>
        <if test="customNameMontage != null">
            custom_name_montage = #{customNameMontage,jdbcType=VARCHAR},
        </if>
        <if test="customIdMontage != null">
            custom_id_montage = #{customIdMontage,jdbcType=VARCHAR},
        </if>
        <if test="taskAllocationPersonnelId != null">
            task_allocation_personnel_id = #{taskAllocationPersonnelId,jdbcType=VARCHAR},
        </if>
        <if test="taskAllocationPersonnelSeating != null">
            task_allocation_personnel_seating = #{taskAllocationPersonnelSeating,jdbcType=VARCHAR},
        </if>
        <if test="customId != null">
            custom_id = #{customId,jdbcType=BIGINT},
        </if>
        <if test="callCenterUuid != null">
            call_center_uuid = #{callCenterUuid,jdbcType=CHAR},
        </if>
        <if test="delFlag != null">
            del_flag = #{delFlag,jdbcType=CHAR},
        </if>
        <if test="operationType != null">
            operation_type = #{operationType,jdbcType=INTEGER},
        </if>
        <if test="updateTime != null">
            update_time = #{updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="callRecipient != null">
            call_recipient = #{callRecipient,jdbcType=INTEGER},
        </if>
        <if test="remark != null">
            remark = #{remark,jdbcType=LONGVARCHAR},
        </if>
        <if test="voiceTplUuid!=null">
            voice_tpl_uuid = #{voiceTplUuid,jdbcType=VARCHAR},
        </if>
        <if test="hookArgs!=null">
            hook_args = #{hookArgs,jdbcType=VARCHAR},
        </if>
        <if test="reachability!=null">
            reachability = #{reachability,jdbcType=DECIMAL},
        </if>
    </set>
    where id= #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.zws.call.domain.AiCallIntelligenceTask">
        update ai_call_intelligence_task
        set task_name = #{taskName,jdbcType=VARCHAR},
            dialogue_template_id = #{dialogueTemplateId,jdbcType=VARCHAR},
            dialogue_template_content = #{dialogueTemplateContent,jdbcType=VARCHAR},
            execution_time = #{executionTime,jdbcType=VARCHAR},
            execution_call_time = #{executionCallTime,jdbcType=VARCHAR},
            recall_count = #{recallCount,jdbcType=INTEGER},
            recall_minute = #{recallMinute,jdbcType=INTEGER},
            is_send_message = #{isSendMessage,jdbcType=INTEGER},
            message_template_id = #{messageTemplateId,jdbcType=VARCHAR},
            message_template_content = #{messageTemplateContent,jdbcType=VARCHAR},
            sender_target = #{senderTarget,jdbcType=VARCHAR},
            team_id = #{teamId,jdbcType=INTEGER},
            employee_id = #{employeeId,jdbcType=INTEGER},
            call_count = #{callCount,jdbcType=INTEGER},
            robot_count = #{robotCount,jdbcType=INTEGER},
            task_status = #{taskStatus,jdbcType=INTEGER},
            task_complete_schedule = #{taskCompleteSchedule,jdbcType=CHAR},
            connection_rate = #{connectionRate,jdbcType=CHAR},
            fee = #{fee,jdbcType=DECIMAL},
            create_id = #{createId,jdbcType=INTEGER},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            create_by = #{createBy,jdbcType=VARCHAR},
            create_type = #{createType,jdbcType=INTEGER},
            custom_name_montage = #{customNameMontage,jdbcType=VARCHAR},
            custom_id_montage = #{customIdMontage,jdbcType=VARCHAR},
            task_allocation_personnel_id = #{taskAllocationPersonnelId,jdbcType=VARCHAR},
            task_allocation_personnel_seating = #{taskAllocationPersonnelSeating,jdbcType=VARCHAR},
            custom_id = #{customId,jdbcType=BIGINT},
            call_center_uuid = #{callCenterUuid,jdbcType=CHAR},
            del_flag = #{delFlag,jdbcType=CHAR},
            operation_type = #{operationType,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            call_recipient = #{callRecipient,jdbcType=INTEGER},
            remark = #{remark,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.call.domain.AiCallIntelligenceTask">
        update ai_call_intelligence_task
        set task_name = #{taskName,jdbcType=VARCHAR},
            dialogue_template_id = #{dialogueTemplateId,jdbcType=VARCHAR},
            dialogue_template_content = #{dialogueTemplateContent,jdbcType=VARCHAR},
            execution_time = #{executionTime,jdbcType=VARCHAR},
            execution_call_time = #{executionCallTime,jdbcType=VARCHAR},
            recall_count = #{recallCount,jdbcType=INTEGER},
            recall_minute = #{recallMinute,jdbcType=INTEGER},
            is_send_message = #{isSendMessage,jdbcType=INTEGER},
            message_template_id = #{messageTemplateId,jdbcType=VARCHAR},
            message_template_content = #{messageTemplateContent,jdbcType=VARCHAR},
            sender_target = #{senderTarget,jdbcType=VARCHAR},
            team_id = #{teamId,jdbcType=INTEGER},
            employee_id = #{employeeId,jdbcType=INTEGER},
            call_count = #{callCount,jdbcType=INTEGER},
            robot_count = #{robotCount,jdbcType=INTEGER},
            task_status = #{taskStatus,jdbcType=INTEGER},
            task_complete_schedule = #{taskCompleteSchedule,jdbcType=CHAR},
            connection_rate = #{connectionRate,jdbcType=CHAR},
            fee = #{fee,jdbcType=DECIMAL},
            create_id = #{createId,jdbcType=INTEGER},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            create_by = #{createBy,jdbcType=VARCHAR},
            create_type = #{createType,jdbcType=INTEGER},
            custom_name_montage = #{customNameMontage,jdbcType=VARCHAR},
            custom_id_montage = #{customIdMontage,jdbcType=VARCHAR},
            task_allocation_personnel_id = #{taskAllocationPersonnelId,jdbcType=VARCHAR},
            task_allocation_personnel_seating = #{taskAllocationPersonnelSeating,jdbcType=VARCHAR},
            custom_id = #{customId,jdbcType=BIGINT},
            call_center_uuid = #{callCenterUuid,jdbcType=CHAR},
            del_flag = #{delFlag,jdbcType=CHAR},
            operation_type = #{operationType,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            call_recipient = #{callRecipient,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>


    <update id="updateByUuidList" parameterType="com.zws.call.domain.AiCallIntelligenceTask">
        update ai_call_intelligence_task
        <set>
            <if test="taskName != null">
                task_name = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="dialogueTemplateName != null">
                dialogue_template_name = #{dialogueTemplateName,jdbcType=VARCHAR},
            </if>
            <if test="dialogueTemplateContent != null">
                dialogue_template_content = #{dialogueTemplateContent,jdbcType=VARCHAR},
            </if>
            <if test="executionTime !=null">
                execution_time = #{executionTime,jdbcType=VARCHAR},
            </if>
            <if test="executionCallTime != null">
                execution_call_time = #{executionCallTime,jdbcType=VARCHAR},
            </if>
            <if test="recallCount != null">
                recall_count = #{recallCount,jdbcType=INTEGER},
            </if>
            <if test="recallMinute != null">
                recall_minute = #{recallMinute,jdbcType=INTEGER},
            </if>
            <if test="isSendMessage != null">
                is_send_message = #{isSendMessage,jdbcType=INTEGER},
            </if>
            <if test="messageTemplateName != null">
                message_template_name = #{messageTemplateName,jdbcType=VARCHAR},
            </if>
            <if test="messageTemplateContent != null">
                message_template_content = #{messageTemplateContent,jdbcType=VARCHAR},
            </if>
            <if test="senderTarget != null">
                sender_target = #{senderTarget,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=INTEGER},
            </if>
            <if test="employeeId != null">
                employee_id = #{employeeId,jdbcType=INTEGER},
            </if>
            <if test="callCount != null">
                call_count = #{callCount,jdbcType=INTEGER},
            </if>
            <if test="robotCount != null">
                robot_count = #{robotCount,jdbcType=INTEGER},
            </if>
            <if test="taskStatus != null">
                task_status = #{taskStatus,jdbcType=INTEGER},
            </if>
            <if test="fee != null">
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createType != null">
                create_type = #{createType,jdbcType=INTEGER},
            </if>
            <if test="customNameMontage != null">
                custom_name_montage = #{customNameMontage,jdbcType=VARCHAR},
            </if>
            <if test="customIdMontage != null">
                custom_id_montage = #{customIdMontage,jdbcType=VARCHAR},
            </if>
            <if test="taskAllocationPersonnelId != null">
                task_allocation_personnel_id = #{taskAllocationPersonnelId,jdbcType=VARCHAR},
            </if>
            <if test="taskAllocationPersonnelSeating != null">
                task_allocation_personnel_seating = #{taskAllocationPersonnelSeating,jdbcType=VARCHAR},
            </if>
            <if test="customId != null">
                custom_id = #{customId,jdbcType=BIGINT},
            </if>
            <if test="callCenterUuid != null">
                call_center_uuid = #{callCenterUuid,jdbcType=CHAR},
            </if>
            <if test="operationType != null">
                operation_type = #{operationType,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=LONGVARCHAR},
            </if>
            <if test="callRecipient != null">
                call_recipient = #{callRecipient,jdbcType=INTEGER},
            </if>
            <if test="taskStartTime!=null">
                task_start_time = #{taskStartTime,jdbcType=VARCHAR},
            </if>
            <if test="taskEndTime!=null">
                task_end_time = #{taskEndTime,jdbcType=VARCHAR},
            </if>
            <if test="voiceTplUuid!=null">
                voice_tpl_uuid = #{voiceTplUuid,jdbcType=VARCHAR},
            </if>
            <if test="hookArgs!=null">
                hook_args = #{hookArgs,jdbcType=VARCHAR},
            </if>
            <if test="reachability">
                reachability = #{reachability,jdbcType=INTEGER},
            </if>
        </set>
        where call_center_uuid in
        <foreach collection="callCenterUuidList" item="callCenterUuid" separator="," open="(" close=")">
            #{callCenterUuid}
        </foreach>
    </update>
    <update id="updateByAiCallRecordDataPojo">
        update call_record
        set del_flag = #{delFlag}
        where id = #{id}
    </update>
    <update id="updateSmsContent">
        update ai_task_sms_content AS sms
        <set>
            <if test="phone!=null">
                sms.phone  = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="content!=null">
                sms.content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="number!=null">
                sms.number = #{number,jdbcType=VARCHAR},
            </if>
            <if test="createTime!=null">
                sms.create_time = #{createTime,jdbcType=DATE},
            </if>
            <if test="status!=null">
                sms.status = #{status,jdbcType=INTEGER},
            </if>
            <if test="result!=null">
                sms.result = #{result,jdbcType=VARCHAR},
            </if>
        </set>
        where sms.call_id =  #{callId,jdbcType=VARCHAR}
    </update>
    <update id="updateSmsContentStatusAndContent">
        update ai_task_sms_content AS sms
        left join call_record AS cr on cr.callid = sms.call_id
        set sms.status = #{status,jdbcType=INTEGER},
            sms.result = #{result,jdbcType=VARCHAR}
        where
            sms.status = 0 and cr.callroter = #{callType,jdbcType=VARCHAR}
        and cr.create_time &lt; #{nowTime,jdbcType=TIMESTAMP}

    </update>


    <select id="selectTaskUuidList" resultType="java.lang.String">
        select call_center_uuid
        from ai_call_intelligence_task
        where del_flag = '0'
        and team_id = #{teamId}
        and call_center_uuid is not null
        and id in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectCallReloadList" resultType="java.lang.String">

    </select>
    <select id="selectAllList" resultType="com.zws.call.domain.AiCallIntelligenceTask">
        select tas.id AS id,
        tas.task_name AS taskName,
        tas.dialogue_template_name AS dialogueTemplateName,
        tas.call_count AS callCount,
        tas.robot_count AS robotCount,
        tas.create_time AS createTime,
        tas.create_type AS createType,
        tas.task_status AS taskStatus,
        tas.reachability AS reachability,
        tas.remark AS remark,
        tas.fee AS fee,
        tas.call_center_uuid AS callCenterUuid,
        cre.cname AS cname,
        emp.employee_name AS employeeName,
        tas.execution_time as executionTime,
        tas.execution_call_time as executionCallTime
        from ai_call_intelligence_task AS tas
        left join team_create AS cre ON (tas.create_id = cre.id)
        left join team_employees AS emp ON (tas.create_id = emp.id)
        where tas.del_flag = '0'
        and tas.team_id = #{teamId}
        <if test="memberIdList != null and memberIdList.size() > 0">
            and (
            tas.employee_id in
            <foreach collection="memberIdList" item="memberId" separator="," open="(" close=")">
                #{memberId}
            </foreach>
            or
            <foreach collection="memberIdList" item="memberId" separator="or" open="(" close=")">
                FIND_IN_SET(#{memberId},tas.task_allocation_personnel_id)
            </foreach>
            <if test="customReloadIdList != null and customReloadIdList.size() > 0">
                or tas.custom_id in
                <foreach collection="customReloadIdList" item="customReloadId" separator="," open="(" close=")">
                    #{customReloadId}
                </foreach>
            </if>
            )
        </if>
        <if test="taskNameList != null and taskNameList.size() > 0">
            and tas.task_name in
            <foreach collection="taskNameList" item="taskName" separator="," open="(" close=")">
                #{taskName}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and tas.task_status in
            <foreach collection="statusList" item="taskStatus" separator="," open="(" close=")">
                #{taskStatus}
            </foreach>
        </if>
        <if test="createTime1 != null">
            and tas.create_time &gt;= #{createTime1}
        </if>
        <if test="createTime2 != null">
            and tas.create_time &lt;= #{createTime2}
        </if>
        <if test="reachability1 != null">
            and tas.reachability &gt;= #{reachability1}
        </if>
        <if test="reachability2 != null">
            and tas.reachability &lt;= #{reachability2}
        </if>
        <if test="dialogueTemplateNameList != null and dialogueTemplateNameList.size() > 0">
            and tas.dialogue_template_name in
            <foreach collection="dialogueTemplateNameList" item="dialogueTemplateName" separator="," open="(" close=")">
                #{dialogueTemplateName}
            </foreach>
        </if>
        order by tas.id desc
    </select>


    <select id="selectRecordId" resultType="java.lang.Long">
        select record_id
        from ai_call_custom_record_details
        where del_flag = '0'
        and team_id = #{teamId}
        <if test="memberIdList != null and memberIdList.size() > 0">
            and member_id in
            <foreach collection="memberIdList" item="memberId" separator="," open="(" close=")">
                #{memberId}
            </foreach>
        </if>
        group by record_id
    </select>

    <select id="selectById" resultType="com.zws.call.domain.AiCallIntelligenceTask">
        select
            id,
            task_name,
            dialogue_template_name,
            execution_time,
            task_start_time,
            task_end_time,
            execution_call_time,
            recall_count,
            recall_minute,
            is_send_message,
            message_template_name,
            message_template_content,
            sender_target,
            call_count,
            robot_count,
            task_status,
            remark,
            call_center_uuid,
            hook_args,
            create_time
        from ai_call_intelligence_task
        where del_flag = '0'
          and id = #{id}
          and team_id = #{teamId}

    </select>

    <select id="selectCallRecordAgentDuration" resultType="java.lang.Integer">
        select sum(agent_duration)
        from call_record
        where del_flag = '0'
          and team_id = #{teamId}
          and task_uuid = #{taskUuid}
          and connect_flag = #{connectFlag}
    </select>
    <select id="selectCallRecordList" resultType="com.zws.call.pojo.AiCallRecordDataPojo">
        select rec.id AS id,
        rec.call_from AS callFrom,
        rec.call_to AS callTo,
        det.case_name AS name,
        rec.call_time AS callTime,
        rec.sip_number AS sipNumber,
        emp.employee_name AS employeeName,
        rec.connect_flag AS connectFlag,
        rec.agent_duration AS agentDuration,
        rec.recording AS recording,
        rec.service_host AS serviceHost,
        rec.call_result AS callResult,
        det.case_id AS caseId,
        sms.content AS smsContent,
        sms.status AS smsStatus,
        sms.create_time AS smsCreateTime,
        sms.result AS result
        from call_record AS rec
        left join ai_call_custom_record_details AS det ON (rec.custom_uuid = det.uuid)
        left join team_employees AS emp ON (emp.id = rec.odv_id)
        left join ai_task_sms_content AS sms ON (rec.callid = sms.call_id)
        where rec.del_flag = '0'
        and rec.team_id = #{teamId}
        and rec.task_uuid = #{taskUuid}
          <if test="connectFlag!=null">
              and rec.connect_flag = #{connectFlag}
          </if>
        <if test="callResults != null">
            and rec.call_result in
            <foreach collection="callResults" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectAiFieldList" resultType="com.zws.call.domain.AiField">
        select id,
        args,
        field
        from ai_field
    </select>
    <select id="selectByCount" resultType="java.lang.Integer">
        select count(1)
        from ai_call_intelligence_task
        where del_flag = '0'
          and team_id = #{teamId}
          and task_name = #{taskName}
    </select>
    <select id="selectByUuid" resultType="com.zws.call.domain.AiCallIntelligenceTask">
        select tas.id               AS id,
               tas.call_center_uuid AS callCenterUuid,
               tas.operation_type   AS operationType,
               tas.team_id          AS teamId,
               cre.company_num      AS enterpriseNum,
               cre.api_flag         AS apiFlag,
               cre.team_type        AS teamType
        from ai_call_intelligence_task AS tas
                 left join team_create AS cre ON (tas.team_id = cre.id and cre.delete_logo = 0)
        where tas.del_flag = '0'
          and tas.call_center_uuid = #{callCenterUuid} LIMIT 1
    </select>
    <select id="selectByCallIdAndPhoneCount" resultType="java.lang.Integer">
        select count(1)
        from ai_task_sms_content
        where call_id = #{callId}
    </select>


</mapper>