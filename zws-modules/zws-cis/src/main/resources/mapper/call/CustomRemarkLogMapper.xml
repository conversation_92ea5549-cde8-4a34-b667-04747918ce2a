<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.call.mapper.CustomRemarkLogMapper">
  <resultMap id="BaseResultMap" type="com.zws.call.domain.CustomRemarkLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="note_time" jdbcType="TIMESTAMP" property="noteTime" />
    <result column="note_by" jdbcType="VARCHAR" property="noteBy" />
    <result column="note_by_id" jdbcType="INTEGER" property="noteById" />
    <result column="note_by_type" jdbcType="INTEGER" property="noteByType" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="team_id" jdbcType="INTEGER" property="teamId" />
    <result column="custom_id" jdbcType="BIGINT" property="customId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, remark, note_time, note_by, note_by_id, note_by_type, del_flag, team_id, custom_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from call_custom_remark_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from call_custom_remark_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.zws.call.domain.CustomRemarkLog">
    insert into call_custom_remark_log (id, remark, note_time, 
      note_by, note_by_id, note_by_type, 
      del_flag, team_id, custom_id
      )
    values (#{id,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, #{noteTime,jdbcType=TIMESTAMP}, 
      #{noteBy,jdbcType=VARCHAR}, #{noteById,jdbcType=INTEGER}, #{noteByType,jdbcType=INTEGER}, 
      #{delFlag,jdbcType=CHAR}, #{teamId,jdbcType=INTEGER}, #{customId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zws.call.domain.CustomRemarkLog">
    insert into call_custom_remark_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="noteTime != null">
        note_time,
      </if>
      <if test="noteBy != null">
        note_by,
      </if>
      <if test="noteById != null">
        note_by_id,
      </if>
      <if test="noteByType != null">
        note_by_type,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
      <if test="customId != null">
        custom_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="noteTime != null">
        #{noteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="noteBy != null">
        #{noteBy,jdbcType=VARCHAR},
      </if>
      <if test="noteById != null">
        #{noteById,jdbcType=INTEGER},
      </if>
      <if test="noteByType != null">
        #{noteByType,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=INTEGER},
      </if>
      <if test="customId != null">
        #{customId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.call.domain.CustomRemarkLog">
    update call_custom_remark_log
    <set>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="noteTime != null">
        note_time = #{noteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="noteBy != null">
        note_by = #{noteBy,jdbcType=VARCHAR},
      </if>
      <if test="noteById != null">
        note_by_id = #{noteById,jdbcType=INTEGER},
      </if>
      <if test="noteByType != null">
        note_by_type = #{noteByType,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=INTEGER},
      </if>
      <if test="customId != null">
        custom_id = #{customId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zws.call.domain.CustomRemarkLog">
    update call_custom_remark_log
    set remark = #{remark,jdbcType=VARCHAR},
      note_time = #{noteTime,jdbcType=TIMESTAMP},
      note_by = #{noteBy,jdbcType=VARCHAR},
      note_by_id = #{noteById,jdbcType=INTEGER},
      note_by_type = #{noteByType,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=CHAR},
      team_id = #{teamId,jdbcType=INTEGER},
      custom_id = #{customId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>