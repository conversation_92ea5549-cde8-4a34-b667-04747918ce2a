<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.InfoExtraMapper">
    <resultMap id="BaseResultMap" type="com.zws.system.api.domain.InfoExtra">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="extra_name" jdbcType="VARCHAR" property="extraName"/>
        <result column="extra_value" jdbcType="VARCHAR" property="extraValue"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="team_id" property="teamId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , case_id, extra_name, extra_value, del_flag, create_by, create_time, update_by,
    update_time,team_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from case_info_extra
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByCaseIdKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from case_info_extra
        where del_flag = 0 and case_id = #{caseId} and extra_name = #{extraName}
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from case_info_extra
        where del_flag=0
        <if test="caseId!=null">
            and case_id = #{caseId}
        </if>
        <if test="extraName!=null and extraName!=''">
            and extra_name=#{extraName}
        </if>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from case_info_extra
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.system.api.domain.InfoExtra" keyProperty="id" useGeneratedKeys="true">
        insert into case_info_extra (id, case_id, extra_name,
                                     extra_value, del_flag, create_by,
                                     create_time, update_by, update_time, team_id)
        values (#{id,jdbcType=BIGINT}, #{caseId,jdbcType=BIGINT}, #{extraName,jdbcType=VARCHAR},
                #{extraValue,jdbcType=VARCHAR}, #{delFlag,jdbcType=CHAR}, #{createBy,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{teamId})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.system.api.domain.InfoExtra">
        insert into case_info_extra
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="caseId != null">
                case_id,
            </if>
            <if test="extraName != null">
                extra_name,
            </if>
            <if test="extraValue != null">
                extra_value,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="caseId != null">
                #{caseId,jdbcType=BIGINT},
            </if>
            <if test="extraName != null">
                #{extraName,jdbcType=VARCHAR},
            </if>
            <if test="extraValue != null">
                #{extraValue,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert">
        insert into case_info_extra (id, case_id, extra_name,
        extra_value, del_flag, create_by,
        create_time, update_by, update_time,team_id
        )
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.id,jdbcType=BIGINT}, #{record.caseId,jdbcType=BIGINT}, #{record.extraName,jdbcType=VARCHAR},
            #{record.extraValue,jdbcType=VARCHAR}, 0, #{record.createBy,jdbcType=VARCHAR},
            #{record.createTime,jdbcType=TIMESTAMP}, #{record.updateBy,jdbcType=VARCHAR},
            #{record.updateTime,jdbcType=TIMESTAMP},#{record.teamId}
            )
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.system.api.domain.InfoExtra">
        update case_info_extra
        <set>
            <if test="caseId != null">
                case_id = #{caseId,jdbcType=BIGINT},
            </if>
            <if test="extraName != null">
                extra_name = #{extraName,jdbcType=VARCHAR},
            </if>
            <if test="extraValue != null">
                extra_value = #{extraValue,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.system.api.domain.InfoExtra">
        update case_info_extra
        set case_id     = #{caseId,jdbcType=BIGINT},
            extra_name  = #{extraName,jdbcType=VARCHAR},
            extra_value = #{extraValue,jdbcType=VARCHAR},
            del_flag    = #{delFlag,jdbcType=CHAR},
            create_by   = #{createBy,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_by   = #{updateBy,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
