<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.TeamImportLogMapper">
    <resultMap id="BaseResultMap" type="com.zws.cis.domain.TeamImportLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="import_batch_num" jdbcType="VARCHAR" property="importBatchNum"/>
        <result column="import_type" jdbcType="VARCHAR" property="importType"/>
        <result column="success_number" jdbcType="BIGINT" property="successNumber"/>
        <result column="owner_id" jdbcType="BIGINT" property="ownerId"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName"/>
        <result column="import_time" jdbcType="TIMESTAMP" property="importTime"/>
        <result column="import_start" jdbcType="VARCHAR" property="importStart"/>
        <result column="original_filename" jdbcType="VARCHAR" property="originalFilename"/>
        <result column="source_file_url" jdbcType="VARCHAR" property="sourceFileUrl"/>
        <result column="fail_file_url" jdbcType="VARCHAR" property="failFileUrl"/>
        <result column="owner_ids" jdbcType="VARCHAR" property="ownerIds"/>
        <result column="product_ids" jdbcType="VARCHAR" property="productIds"/>
        <result column="team_id" jdbcType="INTEGER" property="teamId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="team_id"  property="teamId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , import_batch_num, import_type, success_number, owner_id, product_id, product_type,
    operator_id, operator_name, import_time, import_start, original_filename, source_file_url,
    fail_file_url, owner_ids, product_ids, team_id, update_time, update_by,team_id
    </sql>
    <sql id="Base_Column_List_Join">
        ail
        .
        id
        , ail.import_type, ail.import_batch_num,ail.success_number, ail.owner_id, ail.product_id, ap.name as product_type, ail.operator_id,
    ail.operator_name, ail.import_time, ail.import_start, ail.source_file_url, ail.fail_file_url,am.package_name as packageName,ap.name as productName, ao.name as ownerName
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from team_asset_import_log
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Join"/>,
        b.productNames,c.ownerNames
        from team_asset_import_log as ail
        left join team_asset_product as ap on (ap.id=ail.product_id)
        left join team_asset_owner as ao on (ao.id=ail.owner_id)
        left join team_asset_manage as am on(am.batch_num = ail.import_batch_num and am.del_flag=0)
        LEFT JOIN (
        SELECT ilog.id, GROUP_CONCAT(ap.name) AS productNames FROM team_asset_import_log AS ilog
        LEFT JOIN team_asset_product AS ap ON FIND_IN_SET(ap.id, ilog.product_ids)
        GROUP BY ilog.id
        ) AS b ON(ail.id=b.id)
        LEFT JOIN (
        SELECT ilog.id, GROUP_CONCAT(ao.name) AS ownerNames FROM team_asset_import_log AS ilog
        LEFT JOIN team_asset_owner AS ao ON FIND_IN_SET(ao.id, ilog.owner_ids)
        GROUP BY ilog.id
        ) AS c ON(ail.id=c.id)
        <where>
            ail.team_id = #{teamId}
            <if test="importTypes!=null">
                and ail.import_type in
                <foreach collection="importTypes" item="importType" open="(" close=")" separator=",">
                    #{importType}
                </foreach>
            </if>
            <if test="importType!=null">
                and ail.import_type= #{importType}
            </if>
            <if test="importTime1!=null">
                and ail.import_time >= #{importTime1}
            </if>
            <if test="importTime2 !=null ">
                and ail.import_time &lt;= #{importTime2}
            </if>
            <if test="importStart!=null and importStart!=''">
                and ail.import_start =#{importStart}
            </if>
            <if test="importBatchNum!=null and importBatchNum!='' ">
                and ail.import_batch_num =#{importBatchNum}
            </if>
            <if test="packageNameArr!=null and packageNameArr.size()>0 ">
                and am.package_name in
                <foreach collection="packageNameArr" item="packageName" separator="," open="(" close=")">
                    #{packageName}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from team_asset_import_log
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.cis.domain.TeamImportLog" useGeneratedKeys="true"
            keyProperty="id">
        insert into team_asset_import_log (id, import_batch_num, import_type,
                                           success_number, owner_id, product_id,
                                           product_type, operator_id, operator_name,
                                           import_time, import_start, original_filename,
                                           source_file_url, fail_file_url, owner_ids,
                                           product_ids, team_id, update_time,
                                           update_by)
        values (#{id,jdbcType=BIGINT}, #{importBatchNum,jdbcType=VARCHAR}, #{importType,jdbcType=VARCHAR},
                #{successNumber,jdbcType=BIGINT}, #{ownerId,jdbcType=BIGINT}, #{productId,jdbcType=BIGINT},
                #{productType,jdbcType=VARCHAR}, #{operatorId,jdbcType=BIGINT}, #{operatorName,jdbcType=VARCHAR},
                #{importTime,jdbcType=TIMESTAMP}, #{importStart,jdbcType=VARCHAR}, #{originalFilename,jdbcType=VARCHAR},
                #{sourceFileUrl,jdbcType=VARCHAR}, #{failFileUrl,jdbcType=VARCHAR}, #{ownerIds,jdbcType=VARCHAR},
                #{productIds,jdbcType=VARCHAR}, #{teamId,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.cis.domain.TeamImportLog">
        insert into team_asset_import_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="importBatchNum != null">
                import_batch_num,
            </if>
            <if test="importType != null">
                import_type,
            </if>
            <if test="successNumber != null">
                success_number,
            </if>
            <if test="ownerId != null">
                owner_id,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="productType != null">
                product_type,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="operatorName != null">
                operator_name,
            </if>
            <if test="importTime != null">
                import_time,
            </if>
            <if test="importStart != null">
                import_start,
            </if>
            <if test="originalFilename != null">
                original_filename,
            </if>
            <if test="sourceFileUrl != null">
                source_file_url,
            </if>
            <if test="failFileUrl != null">
                fail_file_url,
            </if>
            <if test="ownerIds != null">
                owner_ids,
            </if>
            <if test="productIds != null">
                product_ids,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="importBatchNum != null">
                #{importBatchNum,jdbcType=VARCHAR},
            </if>
            <if test="importType != null">
                #{importType,jdbcType=VARCHAR},
            </if>
            <if test="successNumber != null">
                #{successNumber,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="productId != null">
                #{productId,jdbcType=BIGINT},
            </if>
            <if test="productType != null">
                #{productType,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                #{operatorId,jdbcType=BIGINT},
            </if>
            <if test="operatorName != null">
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="importTime != null">
                #{importTime,jdbcType=TIMESTAMP},
            </if>
            <if test="importStart != null">
                #{importStart,jdbcType=VARCHAR},
            </if>
            <if test="originalFilename != null">
                #{originalFilename,jdbcType=VARCHAR},
            </if>
            <if test="sourceFileUrl != null">
                #{sourceFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="failFileUrl != null">
                #{failFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="ownerIds != null">
                #{ownerIds,jdbcType=VARCHAR},
            </if>
            <if test="productIds != null">
                #{productIds,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.cis.domain.TeamImportLog">
        update team_asset_import_log
        <set>
            <if test="importBatchNum != null">
                import_batch_num = #{importBatchNum,jdbcType=VARCHAR},
            </if>
            <if test="importType != null">
                import_type = #{importType,jdbcType=VARCHAR},
            </if>
            <if test="successNumber != null">
                success_number = #{successNumber,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="productId != null">
                product_id = #{productId,jdbcType=BIGINT},
            </if>
            <if test="productType != null">
                product_type = #{productType,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId,jdbcType=BIGINT},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="importTime != null">
                import_time = #{importTime,jdbcType=TIMESTAMP},
            </if>
            <if test="importStart != null">
                import_start = #{importStart,jdbcType=VARCHAR},
            </if>
            <if test="originalFilename != null">
                original_filename = #{originalFilename,jdbcType=VARCHAR},
            </if>
            <if test="sourceFileUrl != null">
                source_file_url = #{sourceFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="failFileUrl != null">
                fail_file_url = #{failFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="ownerIds != null">
                owner_ids = #{ownerIds,jdbcType=VARCHAR},
            </if>
            <if test="productIds != null">
                product_ids = #{productIds,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.cis.domain.TeamImportLog">
        update team_asset_import_log
        set import_batch_num  = #{importBatchNum,jdbcType=VARCHAR},
            import_type       = #{importType,jdbcType=VARCHAR},
            success_number    = #{successNumber,jdbcType=BIGINT},
            owner_id          = #{ownerId,jdbcType=BIGINT},
            product_id        = #{productId,jdbcType=BIGINT},
            product_type      = #{productType,jdbcType=VARCHAR},
            operator_id       = #{operatorId,jdbcType=BIGINT},
            operator_name     = #{operatorName,jdbcType=VARCHAR},
            import_time       = #{importTime,jdbcType=TIMESTAMP},
            import_start      = #{importStart,jdbcType=VARCHAR},
            original_filename = #{originalFilename,jdbcType=VARCHAR},
            source_file_url   = #{sourceFileUrl,jdbcType=VARCHAR},
            fail_file_url     = #{failFileUrl,jdbcType=VARCHAR},
            owner_ids         = #{ownerIds,jdbcType=VARCHAR},
            product_ids       = #{productIds,jdbcType=VARCHAR},
            team_id           = #{teamId,jdbcType=INTEGER},
            update_time       = #{updateTime,jdbcType=TIMESTAMP},
            update_by         = #{updateBy,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
