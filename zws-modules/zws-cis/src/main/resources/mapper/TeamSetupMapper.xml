<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.TeamSetupMapper">
    <resultMap id="BaseResultMap" type="com.zws.cis.domain.TeamSetup">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="account_id" jdbcType="BIGINT" property="accountId"/>
        <result column="repayment_method" jdbcType="VARCHAR" property="repaymentMethod"/>
        <result column="register_payment" jdbcType="VARCHAR" property="registerPayment"/>
        <result column="reconciliation" jdbcType="VARCHAR" property="reconciliation"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="team_id" jdbcType="INTEGER" property="teamId"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , account_id, repayment_method, register_payment, reconciliation, remark, state,
    team_id, del_flag, create_by, create_time, update_by, update_time
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from team_repayment_setup
        where id = #{id,jdbcType=BIGINT} and team_id = #{teamId}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from team_repayment_setup
        where del_flag = 0 and team_id = #{teamId}
        <if test="repaymentMethod != null and repaymentMethod != ''">
            and repayment_method=#{repaymentMethod}
        </if>
        <if test="state!=null">
            and state=#{state}
        </if>
        order by id desc
    </select>

    <select id="checkRepaymentMethodSize" resultType="java.lang.Integer">
        select count(id)
        from team_repayment_setup
        where del_flag = 0
        and team_id = #{teamId}
        and repayment_method = #{repaymentMethod}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from team_repayment_setup
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.cis.domain.TeamSetup" useGeneratedKeys="true"
            keyProperty="id">
        insert into team_repayment_setup (id, account_id, repayment_method,
                                          register_payment, reconciliation, remark,
                                          state, team_id, del_flag,
                                          create_by, create_time, update_by,
                                          update_time)
        values (#{id,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{repaymentMethod,jdbcType=VARCHAR},
                #{registerPayment,jdbcType=VARCHAR}, #{reconciliation,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
                #{state,jdbcType=INTEGER}, #{teamId,jdbcType=INTEGER}, #{delFlag,jdbcType=CHAR},
                #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
                #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.cis.domain.TeamSetup">
        insert into team_repayment_setup
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="repaymentMethod != null">
                repayment_method,
            </if>
            <if test="registerPayment != null">
                register_payment,
            </if>
            <if test="reconciliation != null">
                reconciliation,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="accountId != null">
                #{accountId,jdbcType=BIGINT},
            </if>
            <if test="repaymentMethod != null">
                #{repaymentMethod,jdbcType=VARCHAR},
            </if>
            <if test="registerPayment != null">
                #{registerPayment,jdbcType=VARCHAR},
            </if>
            <if test="reconciliation != null">
                #{reconciliation,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.cis.domain.TeamSetup">
        update team_repayment_setup
        <set>
            <if test="accountId != null">
                account_id = #{accountId,jdbcType=BIGINT},
            </if>
            <if test="repaymentMethod != null">
                repayment_method = #{repaymentMethod,jdbcType=VARCHAR},
            </if>
            <if test="registerPayment != null">
                register_payment = #{registerPayment,jdbcType=VARCHAR},
            </if>
            <if test="reconciliation != null">
                reconciliation = #{reconciliation,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=INTEGER},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.cis.domain.TeamSetup">
        update team_repayment_setup
        set account_id       = #{accountId,jdbcType=BIGINT},
            repayment_method = #{repaymentMethod,jdbcType=VARCHAR},
            register_payment = #{registerPayment,jdbcType=VARCHAR},
            reconciliation   = #{reconciliation,jdbcType=VARCHAR},
            remark           = #{remark,jdbcType=VARCHAR},
            state            = #{state,jdbcType=INTEGER},
            team_id          = #{teamId,jdbcType=INTEGER},
            del_flag         = #{delFlag,jdbcType=CHAR},
            create_by        = #{createBy,jdbcType=VARCHAR},
            create_time      = #{createTime,jdbcType=TIMESTAMP},
            update_by        = #{updateBy,jdbcType=VARCHAR},
            update_time      = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>