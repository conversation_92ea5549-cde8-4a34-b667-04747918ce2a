<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.TeamOwnerMapper">
    <resultMap id="BaseResultMap" type="com.zws.cis.domain.TeamOwner">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="unified_code" jdbcType="VARCHAR" property="unifiedCode"/>
        <result column="team_id" jdbcType="INTEGER" property="teamId"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , name, short_name, unified_code, team_id, del_flag, create_by, create_time, update_by,
    update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from team_asset_owner
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByName" resultType="java.lang.Integer">
        select count(id)
        from team_asset_owner
        where del_flag = '0'
        <if test="teamId != null">
            and team_id = #{teamId}
        </if>
        <if test="id != null">
            and id != #{id}
        </if>
        <if test="name != null and name != ''">
            and name = #{name}
        </if>
        <if test="shortName != null and shortName != ''">
            and short_name = #{shortName}
        </if>
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from team_asset_owner
        where del_flag = '0' and team_id = #{teamId}
        <if test="id != null">
            and id = #{id}
        </if>
        <if test="name != null and name != ''">
            and name = #{name}
        </if>
    </select>

    <select id="selectCountByOwnerId" resultType="java.lang.Long">
        select count(id)
        from case_library
        where del_flag = 0
          and entrusting_party_id = #{ownerId}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from team_asset_owner
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.cis.domain.TeamOwner">
        insert into team_asset_owner (id, name, short_name,
                                      unified_code, team_id, del_flag,
                                      create_by, create_time, update_by,
                                      update_time)
        values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{shortName,jdbcType=VARCHAR},
                #{unifiedCode,jdbcType=VARCHAR}, #{teamId,jdbcType=INTEGER}, #{delFlag,jdbcType=CHAR},
                #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
                #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.cis.domain.TeamOwner">
        insert into team_asset_owner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="shortName != null">
                short_name,
            </if>
            <if test="unifiedCode != null">
                unified_code,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="unifiedCode != null">
                #{unifiedCode,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.cis.domain.TeamOwner">
        update team_asset_owner
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                short_name = #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="unifiedCode != null">
                unified_code = #{unifiedCode,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.cis.domain.TeamOwner">
        update team_asset_owner
        set name         = #{name,jdbcType=VARCHAR},
            short_name   = #{shortName,jdbcType=VARCHAR},
            unified_code = #{unifiedCode,jdbcType=VARCHAR},
            team_id      = #{teamId,jdbcType=INTEGER},
            del_flag     = #{delFlag,jdbcType=CHAR},
            create_by    = #{createBy,jdbcType=VARCHAR},
            create_time  = #{createTime,jdbcType=TIMESTAMP},
            update_by    = #{updateBy,jdbcType=VARCHAR},
            update_time  = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>