<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.LibraryMapper">
    <resultMap id="BaseResultMap" type="com.zws.system.api.domain.Library">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="asset_manage_id" property="assetManageId"/>
        <result column="settle_state" jdbcType="VARCHAR" property="settleState"/>
        <result column="allocate_case_state" jdbcType="VARCHAR" property="allocateCaseState"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="entrusting_party_id" jdbcType="BIGINT" property="entrustingPartyId"/>
        <result column="entrusting_party_name" jdbcType="VARCHAR" property="entrustingPartyName"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="client_name" property="clientName"/>
        <result column="client_id_num" property="clientIdNum"/>
        <result column="client_census_register" property="clientCensusRegister"/>
        <result column="client_phone" property="clientPhone"/>
        <result column="entrust_money" property="entrustMoney"/>
        <result column="overdue_start" property="overdueStart"/>
        <result column="settle_agreement_time" property="settleAgreementTime"/>
        <result column="settle_agreement_url" jdbcType="VARCHAR" property="settleAgreementUrl"/>
        <result column="team_id" property="teamId"/>
        <result column="import_source" property="importSource"/>
    </resultMap>
    <!--案件基本信息-->
    <resultMap id="BaseResultMap_CaseBaseInfo" type="com.zws.cis.pojo.CaseBaseInfo">
        <id column="id" property="id"/>
        <result column="settle_state" property="settleState"/>
        <result column="allocate_case_state" property="allocateCaseState"/>
        <result column="batch_no" property="batchNo"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="entrusting_party_id" property="entrustingPartyId"/>
        <result column="entrusting_party_name" property="entrustingPartyName"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>

        <result column="client_name" property="clientName"/>
        <result column="client_name_enc" property="clientNameEnc"/>
        <result column="client_id_type" property="clientIdType"/>

        <result column="client_id_num" property="clientIdNum"/>
        <result column="client_id_num_enc" property="clientIdNumEnc"/>
        <result column="client_census_register" property="clientCensusRegister"/>
        <result column="client_census_register_enc" property="clientCensusRegisterEnc"/>
        <result column="client_phone" property="clientPhone"/>
        <result column="client_phone_enc" property="clientPhoneEnc"/>
        <result column="client_sex" property="clientSex"/>
        <result column="client_age" property="clientAge"/>
        <result column="client_birthday" jdbcType="DATE" property="clientBirthday"/>
        <result column="occupation" property="occupation"/>
        <result column="marital_status" property="maritalStatus"/>
        <result column="qq" property="qq"/>
        <result column="weixin" property="weixin"/>
        <result column="mailbox" property="mailbox"/>
        <result column="place_of_work" property="placeOfWork"/>
        <result column="working_address" property="workingAddress"/>
        <result column="residential_address" property="residentialAddress"/>
        <result column="registered_address" property="registeredAddress"/>
        <result column="home_address" property="homeAddress"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_card_number" property="bankCardNumber"/>
        <result column="asset_no" property="assetNo"/>
        <result column="uid" property="uid"/>


        <result column="contract_no" property="contractNo"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="product_type" property="productType"/>
        <result column="loan_money" property="loanMoney"/>
        <result column="overdue_start" property="overdueStart"/>
        <result column="package_name" property="packageName"/>

        <result column="account_period" property="accountPeriod"/>
        <result column="loan_institution" property="loanInstitution"/>
        <result column="loan_periods" property="loanPeriods"/>
        <result column="already_periods" property="alreadyPeriods"/>
        <result column="not_periods" property="notPeriods"/>
        <result column="entrust_money" property="entrustMoney"/>
        <result column="loan_principal" property="loanPrincipal"/>
        <result column="late_fee" property="lateFee"/>
        <result column="service_fee" property="serviceFee"/>
        <result column="residual_principal" property="residualPrincipal"/>
        <result column="interest_money" property="interestMoney"/>
        <result column="interest_penalty" property="interestPenalty"/>
        <result column="actual_amount_received" property="actualAmountReceived"/>
        <result column="overdue_premium" property="overduePremium"/>
        <result column="repayment_date" property="repaymentDate"/>
        <result column="repayment_monthly" property="repaymentMonthly"/>
        <result column="amount_after_deduction" property="amountAfterDeduction"/>
        <result column="amount_called_back" property="amountCalledBack"/>
        <result column="amount_due" property="amountDue"/>
        <result column="remaining_due" property="remainingDue"/>
        <result column="loan_date" property="loanDate"/>
        <result column="amount_final_repayment" property="amountFinalRepayment"/>
        <result column="amount_final_date" property="amountFinalDate"/>
        <result column="latest_follow_up_time" property="latestFollowUpTime"/>
        <result column="entrusting_case_date" property="entrustingCaseDate"/>
        <result column="return_case_date" property="returnCaseDate"/>
        <result column="other_fees" property="otherFees"/>
        <result column="other_fees_remarks" property="otherFeesRemarks"/>
        <result column="case_region" property="caseRegion"/>

        <result column="yc_overdue_days" property="ycOverdueDays"/>
        <result column="yc_contract_no" property="ycContractNo"/>
        <result column="yc_five_level" property="ycFiveLevel"/>
        <result column="yc_currencies" property="ycCurrencies"/>
        <result column="yc_purpose" property="ycPurpose"/>
        <result column="yc_lending_rate" property="ycLendingRate"/>
        <result column="yc_repayment_method" property="ycRepaymentMethod"/>
        <result column="yc_interest_balance" property="ycInterestBalance"/>
        <result column="yc_disbursement" property="ycDisbursement"/>
        <result column="yc_litigation_status" property="ycLitigationStatus"/>
        <result column="yc_is_dishonest" property="ycIsDishonest"/>
        <result column="yc_is_limit_consumption" property="ycIsLimitConsumption"/>
        <result column="sy_yh_principal" property="syYhPrincipal"/>
        <result column="sy_yh_interest" property="syYhInterest"/>
        <result column="sy_yh_fees" property="syYhFees"/>
        <result column="sy_yh_default" property="syYhDefault"/>
        <result column="yc_write_date" property="ycWriteDate"/>
        <result column="yc_default_rate" property="ycDefaultRate"/>
    </resultMap>
    <sql id="Base_Column_Case_Base_Info_List">
        cl
        .
        id
        , cl.settle_state, cl.allocate_case_state, cl.batch_no,cl.product_id, cl.product_name, cl.entrusting_party_id,
    cl.entrusting_party_name, cl.del_flag, cl.create_by, cl.create_time, cl.update_by, cl.update_time,
    cib.client_name, cib.client_name_enc, cib.client_id_num, cib.client_id_num_enc, cib.client_census_register,
    cib.client_census_register_enc, cib.client_phone, cib.client_phone_enc, cib.client_sex, cib.client_age,
    cib.client_birthday, cib.occupation, cib.marital_status, cib.qq, cib.weixin, cib.mailbox, cib.place_of_work,
    cib.working_address, cib.residential_address, cib.home_address, cib.bank_name,cib.bank_card_number,cib.registered_address,cib.asset_no,
    cib.uid,cil.contract_no, cil.product_id, cil.product_name, cil.product_type,
    cil.loan_money, cil.overdue_start, cil.account_period, cil.loan_institution, cil.loan_periods, cil.already_periods,
    cil.not_periods, cil.entrust_money, cil.loan_principal, cil.late_fee, cil.service_fee, cil.residual_principal,
    cil.interest_money, cil.interest_penalty, cil.actual_amount_received, cil.overdue_premium, cil.repayment_date,
    cil.repayment_monthly, cil.amount_after_deduction, cil.amount_called_back, cil.amount_due, cil.remaining_due,
    cil.loan_date, cil.amount_final_repayment, cil.amount_final_date, cil.latest_follow_up_time, cil.entrusting_case_date,
    cil.return_case_date, cil.other_fees, cil.other_fees_remarks,cil.case_region,
    cil.yc_overdue_days, cil.yc_contract_no, cil.yc_five_level, cil.yc_currencies,
    cil.yc_purpose, cil.yc_lending_rate, cil.yc_repayment_method, cil.yc_interest_balance,
    cil.yc_disbursement, cil.yc_litigation_status, cil.yc_is_dishonest, cil.yc_is_limit_consumption,
    cil.sy_yh_principal, cil.sy_yh_interest, cil.sy_yh_fees,cil.sy_yh_default,cl.client_id_type,cil.yc_write_date,cil.yc_default_rate
    </sql>
    <sql id="Base_Column_List">
        id
        ,asset_manage_id
        , settle_state, allocate_case_state, batch_no,product_id, product_name, entrusting_party_id,
    entrusting_party_name, del_flag, create_by, create_time, update_by, update_time,client_name,client_id_num,client_census_register,client_phone,entrust_money,overdue_start,
    client_id_type,team_id,import_source
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from case_library
        where del_flag = '0' and id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from case_library
        where id = #{id,jdbcType=BIGINT}
    </delete>


    <insert id="insert" parameterType="com.zws.system.api.domain.Library" keyProperty="id"
            useGeneratedKeys="true">
        insert into case_library (id, asset_manage_id, settle_state, allocate_case_state,
                                  batch_no, product_id, product_name, entrusting_party_id,
                                  entrusting_party_name, del_flag, create_by,
                                  create_time, update_by, update_time, client_name, client_id_num,
                                  client_census_register, client_phone, entrust_money, overdue_start, client_id_type,
                                  team_id, import_source)
        values (#{id,jdbcType=BIGINT}, #{assetManageId}, #{settleState,jdbcType=VARCHAR},
                #{allocateCaseState,jdbcType=VARCHAR},
                #{batchNo,jdbcType=VARCHAR}, #{productId}, #{productName,jdbcType=VARCHAR},
                #{entrustingPartyId,jdbcType=BIGINT},
                #{entrustingPartyName,jdbcType=VARCHAR}, #{delFlag,jdbcType=CHAR}, #{createBy,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{clientName}, #{clientIdNum}, #{clientCensusRegister}, #{clientPhone}, #{entrustMoney},
                #{overdueStart}, #{clientIdType}, #{teamId}, #{importSource})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.system.api.domain.Library">
        insert into case_library
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="settleState != null">
                settle_state,
            </if>
            <if test="allocateCaseState != null">
                allocate_case_state,
            </if>
            <if test="batchNo != null">
                batch_no,
            </if>
            <if test="productId!=null">
                product_id,
            </if>
            <if test="productName != null">
                product_name,
            </if>
            <if test="entrustingPartyId != null">
                entrusting_party_id,
            </if>
            <if test="entrustingPartyName != null">
                entrusting_party_name,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="clientName!=null">
                client_name,
            </if>
            <if test="clientIdNum">
                client_id_num,
            </if>
            <if test="clientCensusRegister">
                client_census_register,
            </if>
            <if test="clientPhone">
                client_phone,
            </if>
            <if test="entrustMoney">
                entrust_money ,
            </if>
            <if test="overdueStart">
                overdue_start,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="settleState != null">
                #{settleState,jdbcType=VARCHAR},
            </if>
            <if test="allocateCaseState != null">
                #{allocateCaseState,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null">
                #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="productId != null">
                #{productId,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="entrustingPartyId != null">
                #{entrustingPartyId,jdbcType=BIGINT},
            </if>
            <if test="entrustingPartyName != null">
                #{entrustingPartyName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="clientName!=null">
                #{clientName},
            </if>
            <if test="clientIdNum">
                #{clientIdNum},
            </if>
            <if test="clientCensusRegister">
                #{clientCensusRegister},
            </if>
            <if test="clientPhone">
                #{clientPhone},
            </if>
            <if test="entrustMoney">
                #{clientPhone} ,
            </if>
            <if test="overdueStart">
                #{overdueStart},
            </if>
        </trim>
    </insert>
    <sql id="updateSelective">
        <set>
            <if test="assetManageId != null">
                asset_manage_id = #{assetManageId},
            </if>
            <if test="settleState != null">
                settle_state = #{settleState,jdbcType=VARCHAR},
            </if>
            <if test="allocateCaseState != null">
                allocate_case_state = #{allocateCaseState,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null">
                batch_no = #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="entrustingPartyId != null">
                entrusting_party_id = #{entrustingPartyId,jdbcType=BIGINT},
            </if>
            <if test="entrustingPartyName != null">
                entrusting_party_name = #{entrustingPartyName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="clientName!=null">
                client_name=#{clientName},
            </if>
            <if test="clientIdNum!=null">
                client_id_num=#{clientIdNum},
            </if>
            <if test="clientIdType!=null">
                client_id_type=#{clientIdType},
            </if>
            <if test="clientCensusRegister!=null">
                client_census_register=#{clientCensusRegister},
            </if>
            <if test="clientPhone!=null">
                client_phone=#{clientPhone},
            </if>
            <if test="entrustMoney!=null">
                entrust_money=#{entrustMoney},
            </if>
            <if test="overdueStart!=null">
                overdue_start=#{overdueStart},
            </if>
            <if test="settleAgreementUrl!=null">
                settle_agreement_url=#{settleAgreementUrl},
            </if>
            <if test="settleAgreementTime!=null">
                settle_agreement_time=#{settleAgreementTime},
            </if>
        </set>
    </sql>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.system.api.domain.Library">
        update case_library
        <include refid="updateSelective"></include>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateAllocateCaseStateByIdList" >
        update case_library
        set allocate_case_state = '已分配'
        where id in
        <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>
    <update id="updateByAssetManageIdSelective" parameterType="com.zws.system.api.domain.Library">
        update case_library
        <include refid="updateSelective"></include>
        where asset_manage_id = #{assetManageId} and import_source = 1 and team_id = #{teamId}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.system.api.domain.Library">
        update case_library
        set settle_state          = #{settleState,jdbcType=VARCHAR},
            allocate_case_state   = #{allocateCaseState,jdbcType=VARCHAR},
            batch_no              = #{batchNo,jdbcType=VARCHAR},
            product_name          = #{productName,jdbcType=VARCHAR},
            entrusting_party_id   = #{entrustingPartyId,jdbcType=BIGINT},
            entrusting_party_name = #{entrustingPartyName,jdbcType=VARCHAR},
            del_flag              = #{delFlag,jdbcType=CHAR},
            create_by             = #{createBy,jdbcType=VARCHAR},
            create_time           = #{createTime,jdbcType=TIMESTAMP},
            update_by             = #{updateBy,jdbcType=VARCHAR},
            update_time           = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectCaseBaseInfoList" resultMap="BaseResultMap_CaseBaseInfo">
        select
        <include refid="Base_Column_Case_Base_Info_List"></include>
        from case_library as cl
        left join case_info_base as cib on (cib.case_id=cl.id)
        left join case_info_loan as cil on (cil.case_id=cl.id)
        where cl.del_flag='0' and cl.import_source = 1 and cl.team_id = #{teamId}
        <if test="batchNo!=null and batchNo!=''">
            and cl.batch_no=#{batchNo}
        </if>
        order by cl.id desc
    </select>
    <select id="selectListByCaseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from case_library
        where del_flag='0' and import_source = 1 and team_id = #{teamId} and id=#{caseId}
    </select>
</mapper>
