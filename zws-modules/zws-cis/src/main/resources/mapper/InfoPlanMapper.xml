<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.InfoPlanMapper">
    <resultMap id="BaseResultMap" type="com.zws.system.api.domain.InfoPlan">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="order_number" jdbcType="BIGINT" property="orderNumber"/>
        <result column="hk_periods_number" jdbcType="INTEGER" property="hkPeriodsNumber"/>
        <result column="yh_date" jdbcType="DATE" property="yhDate"/>
        <result column="jh_yh_principal" jdbcType="DECIMAL" property="jhYhPrincipal"/>
        <result column="jh_yh_interest" jdbcType="DECIMAL" property="jhYhInterest"/>
        <result column="sy_yh_money" jdbcType="DECIMAL" property="syYhMoney"/>
        <result column="sy_yh_principal" jdbcType="DECIMAL" property="syYhPrincipal"/>
        <result column="sy_yh_interest" jdbcType="DECIMAL" property="syYhInterest"/>
        <result column="sy_yh_compound_interest" jdbcType="DECIMAL" property="syYhCompoundInterest"/>
        <result column="sy_yh_penalty_interest" jdbcType="DECIMAL" property="syYhPenaltyInterest"/>
        <result column="sy_yh_wyj" jdbcType="DECIMAL" property="syYhWyj"/>
        <result column="sy_yh_late_fee" jdbcType="DECIMAL" property="syYhLateFee"/>
        <result column="sy_yh_update_time" jdbcType="DATE" property="syYhUpdateTime"/>
        <result column="sy_yh_surplus_interest" jdbcType="DECIMAL" property="syYhSurplusInterest"/>
        <result column="already_hk_date" jdbcType="DATE" property="alreadyHkDate"/>
        <result column="ych_principal" jdbcType="DECIMAL" property="ychPrincipal"/>
        <result column="ych_interest" jdbcType="DECIMAL" property="ychInterest"/>
        <result column="ych_penalty_interest" jdbcType="DECIMAL" property="ychPenaltyInterest"/>
        <result column="ych_compound_interest" jdbcType="DECIMAL" property="ychCompoundInterest"/>
        <result column="ych_late_fee" jdbcType="DECIMAL" property="ychLateFee"/>
        <result column="ych_wyj" jdbcType="DECIMAL" property="ychWyj"/>
        <result column="ych_money" jdbcType="DECIMAL" property="ychMoney"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>

        <result column="jh_yh_advice"  property="jhYhAdvice"/>
        <result column="jh_yh_guarantee"  property="jhYhGuarantee"/>
        <result column="ych_advice"  property="ychAdvice"/>
        <result column="ych_guarantee"  property="ychGuarantee"/>
        <result column="sy_yh_advice"  property="syYhAdvice"/>
        <result column="sy_yh_guarantee"  property="syYhGuarantee"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , case_id,order_number, hk_periods_number, yh_date, jh_yh_principal, jh_yh_interest, sy_yh_money,
    sy_yh_principal, sy_yh_interest, sy_yh_compound_interest, sy_yh_penalty_interest,
    sy_yh_wyj, sy_yh_late_fee,sy_yh_update_time,sy_yh_surplus_interest, already_hk_date, ych_principal, ych_interest, ych_penalty_interest,
    ych_compound_interest, ych_late_fee, ych_wyj, ych_money, del_flag, create_by, create_time,
    update_by, update_time,jh_yh_advice,jh_yh_guarantee,ych_advice,ych_guarantee,sy_yh_advice,sy_yh_guarantee
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from case_info_plan
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from case_info_plan
        where del_flag=0
        <if test="caseId!=null">
            and case_id=#{caseId}
        </if>
        <if test="id != null">
            and id=#{id}
        </if>
        <if test="hkPeriodsNumber!=null">
            and hk_periods_number=#{hkPeriodsNumber}
        </if>
        <choose>
            <when test="sortOrder != null">
                <if test="sortOrder == 1">
                    order by ISNULL(hk_periods_number),hk_periods_number asc,yh_date asc
                </if>
                <if test="sortOrder == 2">
                    order by ISNULL(hk_periods_number),hk_periods_number desc,yh_date desc
                </if>
            </when>
            <otherwise>
                order by ISNULL(hk_periods_number),hk_periods_number asc,yh_date asc
            </otherwise>
        </choose>
        <!--  order by ISNULL(hk_periods_number),hk_periods_number desc,create_time desc LIMIT 100  -->
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from case_info_plan
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.system.api.domain.InfoPlan" keyProperty="id"
            useGeneratedKeys="true">
        insert into case_info_plan (id, case_id, order_number, hk_periods_number,
                                    yh_date, jh_yh_principal, jh_yh_interest,jh_yh_money,
                                    sy_yh_money, sy_yh_principal, sy_yh_interest,
                                    sy_yh_compound_interest, sy_yh_penalty_interest,
                                    sy_yh_wyj, sy_yh_late_fee, sy_yh_update_time, sy_yh_surplus_interest,
                                    already_hk_date,
                                    ych_principal, ych_interest, ych_penalty_interest,
                                    ych_compound_interest, ych_late_fee, ych_wyj,
                                    ych_money, del_flag, create_by,
                                    create_time, update_by, update_time,
                                    ych_advice,ych_guarantee,sy_yh_advice,sy_yh_guarantee,jh_yh_advice,jh_yh_guarantee)
        values (#{id,jdbcType=BIGINT}, #{caseId,jdbcType=BIGINT}, #{orderNumber,jdbcType=INTEGER},
                #{hkPeriodsNumber,jdbcType=INTEGER},
                #{yhDate,jdbcType=DATE}, #{jhYhPrincipal,jdbcType=DECIMAL}, #{jhYhInterest,jdbcType=DECIMAL},#{jhYhMoney,jdbcType=DECIMAL},
                #{syYhMoney,jdbcType=DECIMAL}, #{syYhPrincipal,jdbcType=DECIMAL}, #{syYhInterest,jdbcType=DECIMAL},
                #{syYhCompoundInterest,jdbcType=DECIMAL}, #{syYhPenaltyInterest,jdbcType=DECIMAL},
                #{syYhWyj,jdbcType=DECIMAL}, #{syYhLateFee,jdbcType=DECIMAL}, #{syYhUpdateTime},
                #{syYhSurplusInterest,jdbcType=DECIMAL},
                #{alreadyHkDate,jdbcType=DATE},
                #{ychPrincipal,jdbcType=DECIMAL}, #{ychInterest,jdbcType=DECIMAL},
                #{ychPenaltyInterest,jdbcType=DECIMAL},
                #{ychCompoundInterest,jdbcType=DECIMAL}, #{ychLateFee,jdbcType=DECIMAL}, #{ychWyj,jdbcType=DECIMAL},
                #{ychMoney,jdbcType=DECIMAL}, #{delFlag,jdbcType=CHAR}, #{createBy,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{ychAdvice},#{ychGuarantee},#{syYhAdvice},#{syYhGuarantee},#{jhYhAdvice},#{jhYhGuarantee})
    </insert>

    <insert id="batchInsert">
        insert into case_info_plan ( case_id, order_number, hk_periods_number,
                                    yh_date, jh_yh_principal, jh_yh_interest,jh_yh_money,
                                    sy_yh_money, sy_yh_principal, sy_yh_interest,
                                    sy_yh_compound_interest, sy_yh_penalty_interest,
                                    sy_yh_wyj, sy_yh_late_fee, sy_yh_update_time, sy_yh_surplus_interest,
                                    already_hk_date,
                                    ych_principal, ych_interest, ych_penalty_interest,
                                    ych_compound_interest, ych_late_fee, ych_wyj,
                                    ych_money, del_flag, create_by,
                                    create_time, update_by, update_time,
                                    ych_advice,ych_guarantee,sy_yh_advice,sy_yh_guarantee,jh_yh_advice,jh_yh_guarantee)
        values
        <foreach collection="records" item="record" separator=",">
            ( #{record.caseId,jdbcType=BIGINT}, #{record.orderNumber,jdbcType=INTEGER},
                #{record.hkPeriodsNumber,jdbcType=INTEGER},
                #{record.yhDate,jdbcType=DATE}, #{record.jhYhPrincipal,jdbcType=DECIMAL}, #{record.jhYhInterest,jdbcType=DECIMAL},#{record.jhYhMoney,jdbcType=DECIMAL},
                #{record.syYhMoney,jdbcType=DECIMAL}, #{record.syYhPrincipal,jdbcType=DECIMAL}, #{record.syYhInterest,jdbcType=DECIMAL},
                #{record.syYhCompoundInterest,jdbcType=DECIMAL}, #{record.syYhPenaltyInterest,jdbcType=DECIMAL},
                #{record.syYhWyj,jdbcType=DECIMAL}, #{record.syYhLateFee,jdbcType=DECIMAL}, #{record.syYhUpdateTime},
                #{record.syYhSurplusInterest,jdbcType=DECIMAL},
                #{record.alreadyHkDate,jdbcType=DATE},
                #{record.ychPrincipal,jdbcType=DECIMAL}, #{record.ychInterest,jdbcType=DECIMAL},
                #{record.ychPenaltyInterest,jdbcType=DECIMAL},
                #{record.ychCompoundInterest,jdbcType=DECIMAL}, #{record.ychLateFee,jdbcType=DECIMAL}, #{record.ychWyj,jdbcType=DECIMAL},
                #{record.ychMoney,jdbcType=DECIMAL}, #{record.delFlag,jdbcType=CHAR}, #{record.createBy,jdbcType=VARCHAR},
                #{record.createTime,jdbcType=TIMESTAMP}, #{record.updateBy,jdbcType=VARCHAR}, #{record.updateTime,jdbcType=TIMESTAMP},
                #{record.ychAdvice},#{record.ychGuarantee},#{record.syYhAdvice},#{record.syYhGuarantee},#{record.jhYhAdvice},#{record.jhYhGuarantee})
        </foreach>
    </insert>
    <insert id="insertInitial" parameterType="com.zws.system.api.domain.InfoPlan" keyProperty="id"
            useGeneratedKeys="true">
        insert into case_info_plan_initial (id, plan_id, case_id, order_number, hk_periods_number,
                                            yh_date, jh_yh_principal, jh_yh_interest,jh_yh_money,
                                            sy_yh_money, sy_yh_principal, sy_yh_interest,
                                            sy_yh_compound_interest, sy_yh_penalty_interest,
                                            sy_yh_wyj, sy_yh_late_fee, sy_yh_update_time, sy_yh_surplus_interest,
                                            already_hk_date,
                                            ych_principal, ych_interest, ych_penalty_interest,
                                            ych_compound_interest, ych_late_fee, ych_wyj,
                                            ych_money, del_flag, create_by,
                                            create_time, update_by, update_time,
                                            ych_advice,ych_guarantee,sy_yh_advice,sy_yh_guarantee,jh_yh_advice,jh_yh_guarantee)
        values (#{id,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, #{caseId,jdbcType=BIGINT},
                #{orderNumber,jdbcType=INTEGER},
                #{hkPeriodsNumber,jdbcType=INTEGER},
                #{yhDate,jdbcType=DATE}, #{jhYhPrincipal,jdbcType=DECIMAL}, #{jhYhInterest,jdbcType=DECIMAL},#{jhYhMoney,jdbcType=DECIMAL},
                #{syYhMoney,jdbcType=DECIMAL}, #{syYhPrincipal,jdbcType=DECIMAL}, #{syYhInterest,jdbcType=DECIMAL},
                #{syYhCompoundInterest,jdbcType=DECIMAL}, #{syYhPenaltyInterest,jdbcType=DECIMAL},
                #{syYhWyj,jdbcType=DECIMAL}, #{syYhLateFee,jdbcType=DECIMAL}, #{syYhUpdateTime},
                #{syYhSurplusInterest,jdbcType=DECIMAL},
                #{alreadyHkDate,jdbcType=DATE},
                #{ychPrincipal,jdbcType=DECIMAL}, #{ychInterest,jdbcType=DECIMAL},
                #{ychPenaltyInterest,jdbcType=DECIMAL},
                #{ychCompoundInterest,jdbcType=DECIMAL}, #{ychLateFee,jdbcType=DECIMAL}, #{ychWyj,jdbcType=DECIMAL},
                #{ychMoney,jdbcType=DECIMAL}, #{delFlag,jdbcType=CHAR}, #{createBy,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{ychAdvice},#{ychGuarantee},#{syYhAdvice},#{syYhGuarantee},#{jhYhAdvice},#{jhYhGuarantee})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.system.api.domain.InfoPlan">
        insert into case_info_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="caseId != null">
                case_id,
            </if>
            <if test="hkPeriodsNumber != null">
                hk_periods_number,
            </if>
            <if test="yhDate != null">
                yh_date,
            </if>
            <if test="jhYhPrincipal != null">
                jh_yh_principal,
            </if>
            <if test="jhYhInterest != null">
                jh_yh_interest,
            </if>
            <if test="syYhMoney != null">
                sy_yh_money,
            </if>
            <if test="syYhPrincipal != null">
                sy_yh_principal,
            </if>
            <if test="syYhInterest != null">
                sy_yh_interest,
            </if>
            <if test="syYhCompoundInterest != null">
                sy_yh_compound_interest,
            </if>
            <if test="syYhPenaltyInterest != null">
                sy_yh_penalty_interest,
            </if>
            <if test="syYhWyj != null">
                sy_yh_wyj,
            </if>
            <if test="syYhLateFee != null">
                sy_yh_late_fee,
            </if>
            <if test="alreadyHkDate != null">
                already_hk_date,
            </if>
            <if test="ychPrincipal != null">
                ych_principal,
            </if>
            <if test="ychInterest != null">
                ych_interest,
            </if>
            <if test="ychPenaltyInterest != null">
                ych_penalty_interest,
            </if>
            <if test="ychCompoundInterest != null">
                ych_compound_interest,
            </if>
            <if test="ychLateFee != null">
                ych_late_fee,
            </if>
            <if test="ychWyj != null">
                ych_wyj,
            </if>
            <if test="ychMoney != null">
                ych_money,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="caseId != null">
                #{caseId,jdbcType=BIGINT},
            </if>
            <if test="hkPeriodsNumber != null">
                #{hkPeriodsNumber,jdbcType=INTEGER},
            </if>
            <if test="yhDate != null">
                #{yhDate,jdbcType=DATE},
            </if>
            <if test="jhYhPrincipal != null">
                #{jhYhPrincipal,jdbcType=DECIMAL},
            </if>
            <if test="jhYhInterest != null">
                #{jhYhInterest,jdbcType=DECIMAL},
            </if>
            <if test="syYhMoney != null">
                #{syYhMoney,jdbcType=DECIMAL},
            </if>
            <if test="syYhPrincipal != null">
                #{syYhPrincipal,jdbcType=DECIMAL},
            </if>
            <if test="syYhInterest != null">
                #{syYhInterest,jdbcType=DECIMAL},
            </if>
            <if test="syYhCompoundInterest != null">
                #{syYhCompoundInterest,jdbcType=DECIMAL},
            </if>
            <if test="syYhPenaltyInterest != null">
                #{syYhPenaltyInterest,jdbcType=DECIMAL},
            </if>
            <if test="syYhWyj != null">
                #{syYhWyj,jdbcType=DECIMAL},
            </if>
            <if test="syYhLateFee != null">
                #{syYhLateFee,jdbcType=DECIMAL},
            </if>
            <if test="alreadyHkDate != null">
                #{alreadyHkDate,jdbcType=DATE},
            </if>
            <if test="ychPrincipal != null">
                #{ychPrincipal,jdbcType=DECIMAL},
            </if>
            <if test="ychInterest != null">
                #{ychInterest,jdbcType=DECIMAL},
            </if>
            <if test="ychPenaltyInterest != null">
                #{ychPenaltyInterest,jdbcType=DECIMAL},
            </if>
            <if test="ychCompoundInterest != null">
                #{ychCompoundInterest,jdbcType=DECIMAL},
            </if>
            <if test="ychLateFee != null">
                #{ychLateFee,jdbcType=DECIMAL},
            </if>
            <if test="ychWyj != null">
                #{ychWyj,jdbcType=DECIMAL},
            </if>
            <if test="ychMoney != null">
                #{ychMoney,jdbcType=DECIMAL},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.system.api.domain.InfoPlan">
        update case_info_plan
        <set>
            <if test="caseId != null">
                case_id = #{caseId,jdbcType=BIGINT},
            </if>
            <if test="hkPeriodsNumber != null">
                hk_periods_number = #{hkPeriodsNumber,jdbcType=INTEGER},
            </if>
            <if test="yhDate != null">
                yh_date = #{yhDate,jdbcType=DATE},
            </if>
            <if test="jhYhPrincipal != null">
                jh_yh_principal = #{jhYhPrincipal,jdbcType=DECIMAL},
            </if>
            <if test="jhYhInterest != null">
                jh_yh_interest = #{jhYhInterest,jdbcType=DECIMAL},
            </if>
            <if test="syYhMoney != null">
                sy_yh_money = #{syYhMoney,jdbcType=DECIMAL},
            </if>
            <if test="syYhPrincipal != null">
                sy_yh_principal = #{syYhPrincipal,jdbcType=DECIMAL},
            </if>
            <if test="syYhInterest != null">
                sy_yh_interest = #{syYhInterest,jdbcType=DECIMAL},
            </if>
            <if test="syYhCompoundInterest != null">
                sy_yh_compound_interest = #{syYhCompoundInterest,jdbcType=DECIMAL},
            </if>
            <if test="syYhPenaltyInterest != null">
                sy_yh_penalty_interest = #{syYhPenaltyInterest,jdbcType=DECIMAL},
            </if>
            <if test="syYhWyj != null">
                sy_yh_wyj = #{syYhWyj,jdbcType=DECIMAL},
            </if>
            <if test="syYhLateFee != null">
                sy_yh_late_fee = #{syYhLateFee,jdbcType=DECIMAL},
            </if>
            <if test="alreadyHkDate != null">
                already_hk_date = #{alreadyHkDate,jdbcType=DATE},
            </if>
            <if test="ychPrincipal != null">
                ych_principal = #{ychPrincipal,jdbcType=DECIMAL},
            </if>
            <if test="ychInterest != null">
                ych_interest = #{ychInterest,jdbcType=DECIMAL},
            </if>
            <if test="ychPenaltyInterest != null">
                ych_penalty_interest = #{ychPenaltyInterest,jdbcType=DECIMAL},
            </if>
            <if test="ychCompoundInterest != null">
                ych_compound_interest = #{ychCompoundInterest,jdbcType=DECIMAL},
            </if>
            <if test="ychLateFee != null">
                ych_late_fee = #{ychLateFee,jdbcType=DECIMAL},
            </if>
            <if test="ychWyj != null">
                ych_wyj = #{ychWyj,jdbcType=DECIMAL},
            </if>
            <if test="ychMoney != null">
                ych_money = #{ychMoney,jdbcType=DECIMAL},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="jhYhAdvice != null">
                jh_yh_advice = #{jhYhAdvice},
            </if>
            <if test="jhYhGuarantee != null">
                jh_yh_guarantee = #{jhYhGuarantee},
            </if>
            <if test="ychAdvice != null">
                ych_advice = #{ychAdvice},
            </if>
            <if test="ychGuarantee != null">
                ych_guarantee = #{ychGuarantee},
            </if>
            <if test="syYhAdvice != null">
                sy_yh_advice = #{syYhAdvice},
            </if>
            <if test="syYhGuarantee != null">
                sy_yh_guarantee = #{syYhGuarantee},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateInfoPlanById" parameterType="com.zws.system.api.domain.InfoPlan">
        update case_info_plan
        <set>
            <if test="syYhUpdateTime != null">
                sy_yh_update_time = #{syYhUpdateTime},
            </if>
            <if test="syYhPenaltyInterest != null">
                sy_yh_penalty_interest = #{syYhPenaltyInterest},
            </if>
            <if test="syYhPrincipal != null">
                sy_yh_principal = #{syYhPrincipal},
            </if>
            <if test="syYhInterest != null">
                sy_yh_interest = #{syYhInterest},
            </if>
            <if test="syYhMoney != null">
                sy_yh_money = #{syYhMoney},
            </if>

            <if test="ychPrincipal != null">
                ych_principal = #{ychPrincipal},
            </if>
            <if test="ychInterest != null">
                ych_interest = #{ychInterest},
            </if>
            <if test="ychPenaltyInterest != null">
                ych_penalty_interest = #{ychPenaltyInterest},
            </if>
            <if test="ychMoney != null">
                ych_money = #{ychMoney},
            </if>
            <if test="syYhSurplusInterest != null">
                sy_yh_surplus_interest = #{syYhSurplusInterest},
            </if>
            <if test="jhYhAdvice != null">
                jh_yh_advice = #{jhYhAdvice},
            </if>
            <if test="jhYhGuarantee != null">
                jh_yh_guarantee = #{jhYhGuarantee},
            </if>
            <if test="ychAdvice != null">
                ych_advice = #{ychAdvice},
            </if>
            <if test="ychGuarantee != null">
                ych_guarantee = #{ychGuarantee},
            </if>
            <if test="syYhAdvice != null">
                sy_yh_advice = #{syYhAdvice},
            </if>
            <if test="syYhGuarantee != null">
                sy_yh_guarantee = #{syYhGuarantee},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>
