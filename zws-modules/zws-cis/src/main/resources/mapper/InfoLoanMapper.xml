<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.InfoLoanMapper">
    <resultMap id="BaseResultMap" type="com.zws.system.api.domain.InfoLoan">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="asset_manage_id" jdbcType="BIGINT" property="assetManageId"/>
        <result column="case_manage_id" jdbcType="BIGINT" property="caseManageId"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="loan_money" jdbcType="DECIMAL" property="loanMoney"/>
        <result column="overdue_start" jdbcType="TIMESTAMP" property="overdueStart"/>
        <result column="case_region" jdbcType="VARCHAR" property="caseRegion"/>
        <result column="account_period" jdbcType="VARCHAR" property="accountPeriod"/>
        <result column="loan_institution" jdbcType="VARCHAR" property="loanInstitution"/>
        <result column="loan_periods" jdbcType="INTEGER" property="loanPeriods"/>
        <result column="already_periods" jdbcType="INTEGER" property="alreadyPeriods"/>
        <result column="not_periods" jdbcType="INTEGER" property="notPeriods"/>
        <result column="entrust_money" jdbcType="DECIMAL" property="entrustMoney"/>
        <result column="loan_principal" jdbcType="DECIMAL" property="loanPrincipal"/>
        <result column="late_fee" jdbcType="DECIMAL" property="lateFee"/>
        <result column="service_fee" jdbcType="DECIMAL" property="serviceFee"/>
        <result column="residual_principal" jdbcType="DECIMAL" property="residualPrincipal"/>
        <result column="interest_money" jdbcType="DECIMAL" property="interestMoney"/>
        <result column="interest_penalty" jdbcType="DECIMAL" property="interestPenalty"/>
        <result column="actual_amount_received" jdbcType="DECIMAL" property="actualAmountReceived"/>
        <result column="overdue_premium" jdbcType="DECIMAL" property="overduePremium"/>
        <result column="repayment_date" jdbcType="DATE" property="repaymentDate"/>
        <result column="repayment_monthly" jdbcType="DECIMAL" property="repaymentMonthly"/>
        <result column="amount_after_deduction" jdbcType="DECIMAL" property="amountAfterDeduction"/>
        <result column="amount_called_back" jdbcType="DECIMAL" property="amountCalledBack"/>
        <result column="amount_due" jdbcType="DECIMAL" property="amountDue"/>
        <result column="remaining_due" jdbcType="DECIMAL" property="remainingDue"/>
        <result column="loan_date" jdbcType="TIMESTAMP" property="loanDate"/>
        <result column="amount_final_repayment" jdbcType="DECIMAL" property="amountFinalRepayment"/>
        <result column="amount_final_date" jdbcType="TIMESTAMP" property="amountFinalDate"/>
        <result column="latest_follow_up_time" jdbcType="TIMESTAMP" property="latestFollowUpTime"/>
        <result column="entrusting_case_date" jdbcType="TIMESTAMP" property="entrustingCaseDate"/>
        <result column="return_case_date" jdbcType="TIMESTAMP" property="returnCaseDate"/>
        <result column="other_fees" jdbcType="DECIMAL" property="otherFees"/>
        <result column="other_fees_remarks" jdbcType="VARCHAR" property="otherFeesRemarks"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="yc_overdue_days" jdbcType="INTEGER" property="ycOverdueDays"/>
        <result column="yc_contract_no" jdbcType="VARCHAR" property="ycContractNo"/>
        <result column="yc_five_level" jdbcType="VARCHAR" property="ycFiveLevel"/>
        <result column="yc_currencies" jdbcType="VARCHAR" property="ycCurrencies"/>
        <result column="yc_purpose" jdbcType="VARCHAR" property="ycPurpose"/>
        <result column="yc_lending_rate" jdbcType="DECIMAL" property="ycLendingRate"/>
        <result column="yc_repayment_method" jdbcType="VARCHAR" property="ycRepaymentMethod"/>
        <result column="yc_interest_balance" jdbcType="DECIMAL" property="ycInterestBalance"/>
        <result column="yc_disbursement" jdbcType="DECIMAL" property="ycDisbursement"/>
        <result column="yc_litigation_status" jdbcType="VARCHAR" property="ycLitigationStatus"/>
        <result column="yc_is_dishonest" jdbcType="CHAR" property="ycIsDishonest"/>
        <result column="yc_is_limit_consumption" jdbcType="CHAR" property="ycIsLimitConsumption"/>
        <result column="sy_yh_principal" jdbcType="DECIMAL" property="syYhPrincipal"/>
        <result column="sy_yh_interest" jdbcType="DECIMAL" property="syYhInterest"/>
        <result column="sy_yh_fees" jdbcType="DECIMAL" property="syYhFees"/>
        <result column="sy_yh_default" property="syYhDefault"/>
        <result column="yc_loan_bank" property="ycLoanBank"/>
        <result column="yc_loan_term" property="ycLoanTerm"/>
        <result column="yc_business_type" property="ycBusinessType"/>
        <result column="yc_contract_money" property="ycContractMoney"/>
        <result column="yc_loan_issuance_date" property="ycLoanIssuanceDate"/>
        <result column="yc_loan_maturity_date" property="ycLoanMaturityDate"/>
        <result column="yc_abd_repayment" property="ycAbdRepayment"/>
        <result column="yc_abd_principal" property="ycAbdPrincipal"/>
        <result column="yc_abd_interest" property="ycAbdInterest"/>
        <result column="yc_write_date" property="ycWriteDate"/>
        <result column="yc_default_rate" property="ycDefaultRate"/>
        <result column="account_period_num" property="accountPeriodNum"/>
        <result column="interest_guarantee" property="interestGuarantee"/>
        <result column="interest_advice" property="interestAdvice"/>
        <result column="number_of_periods" property="numberOfPeriods"/>
        <result column="clear_date" property="clearDate"/>

        <result column="base_overdue_days" property="baseOverdueDays"/>
        <result column="card_issuance_date" property="cardIssuanceDate"/>
        <result column="statement_date" property="statementDate"/>
        <result column="first_overdue_date" property="firstOverdueDate"/>
        <result column="base_date" property="baseDate"/>
        <result column="credit_limit" property="creditLimit"/>
        <result column="principal_interest_total" property="principalInterestTotal"/>
        <result column="interest_fees_total" property="interestFeesTotal"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , case_id, asset_manage_id, case_manage_id, contract_no, product_id, product_name,
    product_type, loan_money, overdue_start, case_region, account_period, loan_institution,
    loan_periods, already_periods, not_periods, entrust_money, loan_principal, late_fee,
    service_fee, residual_principal, interest_money, interest_penalty, actual_amount_received,
    overdue_premium, repayment_date, repayment_monthly, amount_after_deduction, amount_called_back,
    amount_due, remaining_due, loan_date, amount_final_repayment, amount_final_date,
    latest_follow_up_time, entrusting_case_date, return_case_date, other_fees, other_fees_remarks,
    del_flag, create_by, create_time, update_by, update_time, yc_overdue_days, yc_contract_no,
    yc_five_level, yc_currencies, yc_purpose, yc_lending_rate, yc_repayment_method, yc_interest_balance,
    yc_disbursement, yc_litigation_status, yc_is_dishonest, yc_is_limit_consumption,
    sy_yh_principal, sy_yh_interest, sy_yh_fees,sy_yh_default,
    yc_loan_bank,yc_business_type,yc_contract_money,yc_loan_term,yc_loan_issuance_date,yc_loan_maturity_date,
    yc_abd_repayment,yc_abd_principal,yc_abd_interest,yc_write_date,yc_default_rate,account_period_num,interest_guarantee,interest_advice,number_of_periods,
    base_overdue_days,card_issuance_date,statement_date,first_overdue_date,base_date,credit_limit,principal_interest_total,interest_fees_total
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from case_info_loan
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from case_info_loan
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.zws.system.api.domain.InfoLoan" useGeneratedKeys="true"
            keyProperty="id">
        insert into case_info_loan (id, case_id, asset_manage_id,
                                    case_manage_id, contract_no, product_id,
                                    product_name, product_type, loan_money,
                                    overdue_start, case_region, account_period, account_period_num,
                                    loan_institution, loan_periods, already_periods,
                                    not_periods, entrust_money, loan_principal,
                                    late_fee, service_fee, residual_principal,
                                    interest_money, interest_penalty, actual_amount_received,
                                    overdue_premium, repayment_date, repayment_monthly,
                                    amount_after_deduction, amount_called_back,
                                    amount_due, remaining_due, loan_date,
                                    amount_final_repayment, amount_final_date,
                                    latest_follow_up_time, entrusting_case_date,
                                    return_case_date, other_fees, other_fees_remarks,
                                    del_flag, create_by, create_time,
                                    update_by, update_time, yc_overdue_days,
                                    yc_contract_no, yc_five_level, yc_currencies,
                                    yc_purpose, yc_lending_rate, yc_repayment_method,
                                    yc_interest_balance, yc_disbursement, yc_litigation_status,
                                    yc_is_dishonest, yc_is_limit_consumption, sy_yh_principal,
                                    sy_yh_interest, sy_yh_fees, sy_yh_default,
                                    yc_loan_bank, yc_business_type, yc_contract_money, yc_loan_term,
                                    yc_loan_issuance_date, yc_loan_maturity_date, yc_abd_repayment, yc_abd_principal,
                                    yc_abd_interest,
                                    yc_write_date, yc_default_rate,interest_guarantee,interest_advice,clear_date,number_of_periods,
                                    base_overdue_days,card_issuance_date,statement_date,first_overdue_date,base_date,credit_limit,
                                    principal_interest_total,interest_fees_total)
        values (#{id,jdbcType=BIGINT}, #{caseId,jdbcType=BIGINT}, #{assetManageId,jdbcType=BIGINT},
                #{caseManageId,jdbcType=BIGINT}, #{contractNo,jdbcType=VARCHAR}, #{productId,jdbcType=BIGINT},
                #{productName,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR}, #{loanMoney,jdbcType=DECIMAL},
                #{overdueStart,jdbcType=TIMESTAMP}, #{caseRegion,jdbcType=VARCHAR}, #{accountPeriod,jdbcType=VARCHAR},
                #{accountPeriodNum},
                #{loanInstitution,jdbcType=VARCHAR}, #{loanPeriods,jdbcType=INTEGER},
                #{alreadyPeriods,jdbcType=INTEGER},
                #{notPeriods,jdbcType=INTEGER}, #{entrustMoney,jdbcType=DECIMAL}, #{loanPrincipal,jdbcType=DECIMAL},
                #{lateFee,jdbcType=DECIMAL}, #{serviceFee,jdbcType=DECIMAL}, #{residualPrincipal,jdbcType=DECIMAL},
                #{interestMoney,jdbcType=DECIMAL}, #{interestPenalty,jdbcType=DECIMAL},
                #{actualAmountReceived,jdbcType=DECIMAL},
                #{overduePremium,jdbcType=DECIMAL}, #{repaymentDate,jdbcType=DATE},
                #{repaymentMonthly,jdbcType=DECIMAL},
                #{amountAfterDeduction,jdbcType=DECIMAL}, #{amountCalledBack,jdbcType=DECIMAL},
                #{amountDue,jdbcType=DECIMAL}, #{remainingDue,jdbcType=DECIMAL}, #{loanDate,jdbcType=TIMESTAMP},
                #{amountFinalRepayment,jdbcType=DECIMAL}, #{amountFinalDate,jdbcType=TIMESTAMP},
                #{latestFollowUpTime,jdbcType=TIMESTAMP}, #{entrustingCaseDate,jdbcType=TIMESTAMP},
                #{returnCaseDate,jdbcType=TIMESTAMP}, #{otherFees,jdbcType=DECIMAL},
                #{otherFeesRemarks,jdbcType=VARCHAR},
                #{delFlag,jdbcType=CHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{ycOverdueDays,jdbcType=INTEGER},
                #{ycContractNo,jdbcType=VARCHAR}, #{ycFiveLevel,jdbcType=VARCHAR}, #{ycCurrencies,jdbcType=VARCHAR},
                #{ycPurpose,jdbcType=VARCHAR}, #{ycLendingRate,jdbcType=DECIMAL}, #{ycRepaymentMethod,jdbcType=VARCHAR},
                #{ycInterestBalance,jdbcType=DECIMAL}, #{ycDisbursement,jdbcType=DECIMAL},
                #{ycLitigationStatus,jdbcType=VARCHAR},
                #{ycIsDishonest,jdbcType=CHAR}, #{ycIsLimitConsumption,jdbcType=CHAR},
                #{syYhPrincipal,jdbcType=DECIMAL},
                #{syYhInterest,jdbcType=DECIMAL}, #{syYhFees,jdbcType=DECIMAL}, #{syYhDefault},
                #{ycLoanBank}, #{ycBusinessType}, #{ycContractMoney}, #{ycLoanTerm}, #{ycLoanIssuanceDate},
                #{ycLoanMaturityDate}, #{ycAbdRepayment}, #{ycAbdPrincipal}, #{ycAbdInterest}, #{ycWriteDate},
                #{ycDefaultRate},#{interestGuarantee},#{interestAdvice},#{clearDate},#{numberOfPeriods},
                #{baseOverdueDays},#{cardIssuanceDate},#{statementDate},#{firstOverdueDate},#{baseDate},#{creditLimit},
                #{principalInterestTotal},#{interestFeesTotal})
    </insert>

    <sql id="UpdateSelectiveSet">
        <set>
            <if test="caseId != null">
                case_id = #{caseId,jdbcType=BIGINT},
            </if>
            <if test="assetManageId != null">
                asset_manage_id = #{assetManageId,jdbcType=BIGINT},
            </if>
            <if test="caseManageId != null">
                case_manage_id = #{caseManageId,jdbcType=BIGINT},
            </if>
            <if test="contractNo != null">
                contract_no = #{contractNo,jdbcType=VARCHAR},
            </if>
            <if test="productId != null">
                product_id = #{productId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productType != null">
                product_type = #{productType,jdbcType=VARCHAR},
            </if>
            <if test="loanMoney != null">
                loan_money = #{loanMoney,jdbcType=DECIMAL},
            </if>
            <if test="overdueStart != null">
                overdue_start = #{overdueStart,jdbcType=TIMESTAMP},
            </if>
            <if test="caseRegion != null">
                case_region = #{caseRegion,jdbcType=VARCHAR},
            </if>
            <if test="accountPeriod != null">
                account_period = #{accountPeriod,jdbcType=VARCHAR},
            </if>
            <if test="accountPeriodNum != null">
                account_period_num = #{accountPeriodNum},
            </if>
            <if test="loanInstitution != null">
                loan_institution = #{loanInstitution,jdbcType=VARCHAR},
            </if>
            <if test="loanPeriods != null">
                loan_periods = #{loanPeriods,jdbcType=INTEGER},
            </if>
            <if test="alreadyPeriods != null">
                already_periods = #{alreadyPeriods,jdbcType=INTEGER},
            </if>
            <if test="notPeriods != null">
                not_periods = #{notPeriods,jdbcType=INTEGER},
            </if>
            <if test="entrustMoney != null">
                entrust_money = #{entrustMoney,jdbcType=DECIMAL},
            </if>
            <if test="loanPrincipal != null">
                loan_principal = #{loanPrincipal,jdbcType=DECIMAL},
            </if>
            <if test="lateFee != null">
                late_fee = #{lateFee,jdbcType=DECIMAL},
            </if>
            <if test="serviceFee != null">
                service_fee = #{serviceFee,jdbcType=DECIMAL},
            </if>
            <if test="residualPrincipal != null">
                residual_principal = #{residualPrincipal,jdbcType=DECIMAL},
            </if>
            <if test="interestMoney != null">
                interest_money = #{interestMoney,jdbcType=DECIMAL},
            </if>
            <if test="interestPenalty != null">
                interest_penalty = #{interestPenalty,jdbcType=DECIMAL},
            </if>
            <if test="actualAmountReceived != null">
                actual_amount_received = #{actualAmountReceived,jdbcType=DECIMAL},
            </if>
            <if test="overduePremium != null">
                overdue_premium = #{overduePremium,jdbcType=DECIMAL},
            </if>
            <if test="repaymentDate != null">
                repayment_date = #{repaymentDate,jdbcType=DATE},
            </if>
            <if test="repaymentMonthly != null">
                repayment_monthly = #{repaymentMonthly,jdbcType=DECIMAL},
            </if>
            <if test="amountAfterDeduction != null">
                amount_after_deduction = #{amountAfterDeduction,jdbcType=DECIMAL},
            </if>
            <if test="amountCalledBack != null">
                amount_called_back = #{amountCalledBack,jdbcType=DECIMAL},
            </if>
            <if test="amountDue != null">
                amount_due = #{amountDue,jdbcType=DECIMAL},
            </if>
            <if test="remainingDue != null">
                remaining_due = #{remainingDue,jdbcType=DECIMAL},
            </if>
            <if test="loanDate != null">
                loan_date = #{loanDate,jdbcType=TIMESTAMP},
            </if>
            <if test="amountFinalRepayment != null">
                amount_final_repayment = #{amountFinalRepayment,jdbcType=DECIMAL},
            </if>
            <if test="amountFinalDate != null">
                amount_final_date = #{amountFinalDate,jdbcType=TIMESTAMP},
            </if>
            <if test="latestFollowUpTime != null">
                latest_follow_up_time = #{latestFollowUpTime,jdbcType=TIMESTAMP},
            </if>
            <if test="entrustingCaseDate != null">
                entrusting_case_date = #{entrustingCaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="returnCaseDate != null">
                return_case_date = #{returnCaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="otherFees != null">
                other_fees = #{otherFees,jdbcType=DECIMAL},
            </if>
            <if test="otherFeesRemarks != null">
                other_fees_remarks = #{otherFeesRemarks,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ycOverdueDays != null">
                yc_overdue_days = #{ycOverdueDays,jdbcType=INTEGER},
            </if>
            <if test="ycContractNo != null">
                yc_contract_no = #{ycContractNo,jdbcType=VARCHAR},
            </if>
            <if test="ycFiveLevel != null">
                yc_five_level = #{ycFiveLevel,jdbcType=VARCHAR},
            </if>
            <if test="ycCurrencies != null">
                yc_currencies = #{ycCurrencies,jdbcType=VARCHAR},
            </if>
            <if test="ycPurpose != null">
                yc_purpose = #{ycPurpose,jdbcType=VARCHAR},
            </if>
            <if test="ycLendingRate != null">
                yc_lending_rate = #{ycLendingRate,jdbcType=DECIMAL},
            </if>
            <if test="ycRepaymentMethod != null">
                yc_repayment_method = #{ycRepaymentMethod,jdbcType=VARCHAR},
            </if>
            <if test="ycInterestBalance != null">
                yc_interest_balance = #{ycInterestBalance,jdbcType=DECIMAL},
            </if>
            <if test="ycDisbursement != null">
                yc_disbursement = #{ycDisbursement,jdbcType=DECIMAL},
            </if>
            <if test="ycLitigationStatus != null">
                yc_litigation_status = #{ycLitigationStatus,jdbcType=VARCHAR},
            </if>
            <if test="ycIsDishonest != null">
                yc_is_dishonest = #{ycIsDishonest,jdbcType=CHAR},
            </if>
            <if test="ycIsLimitConsumption != null">
                yc_is_limit_consumption = #{ycIsLimitConsumption,jdbcType=CHAR},
            </if>
            <if test="syYhPrincipal != null">
                sy_yh_principal = #{syYhPrincipal,jdbcType=DECIMAL},
            </if>
            <if test="syYhInterest != null">
                sy_yh_interest = #{syYhInterest,jdbcType=DECIMAL},
            </if>
            <if test="syYhFees != null">
                sy_yh_fees = #{syYhFees,jdbcType=DECIMAL},
            </if>
            <if test="syYhDefault != null">
                sy_yh_default = #{syYhDefault},
            </if>

            <if test="ycLoanBank != null">
                yc_loan_bank = #{ycLoanBank},
            </if>
            <if test="ycBusinessType != null">
                yc_business_type = #{ycBusinessType},
            </if>
            <if test="ycContractMoney != null">
                yc_contract_money = #{ycContractMoney},
            </if>
            <if test="ycLoanTerm != null">
                yc_loan_term = #{ycLoanTerm},
            </if>
            <if test="ycLoanIssuanceDate != null">
                yc_loan_issuance_date = #{ycLoanIssuanceDate},
            </if>
            <if test="ycLoanMaturityDate != null">
                yc_loan_maturity_date = #{ycLoanMaturityDate},
            </if>
            <if test="ycAbdRepayment != null">
                yc_abd_repayment = #{ycAbdRepayment},
            </if>
            <if test="ycAbdPrincipal != null">
                yc_abd_principal = #{ycAbdPrincipal},
            </if>
            <if test="ycAbdInterest != null">
                yc_abd_interest = #{ycAbdInterest},
            </if>


            <if test="params.setAmountFinalDateNull != null">
                amount_final_date = null,
            </if>
            <if test="params.setAmountFinalRepaymentNull != null">
                amount_final_repayment = null,
            </if>
            <if test="ycWriteDate != null">
                yc_write_date = #{ycWriteDate},
            </if>
            <if test="ycDefaultRate != null">
                yc_default_rate = #{ycDefaultRate},
            </if>
            <if test="interestGuarantee != null">
                interest_guarantee = #{interestGuarantee},
            </if>
            <if test="interestAdvice != null">
                interest_advice = #{interestAdvice},
            </if>
            <if test="clearDate != null">
                clear_date = #{clearDate},
            </if>
            <if test="numberOfPeriods != null">
                number_of_periods = #{numberOfPeriods},
            </if>

            <if test="baseOverdueDays != null">
                base_overdue_days = #{baseOverdueDays},
            </if>
            <if test="cardIssuanceDate != null">
                card_issuance_date = #{cardIssuanceDate},
            </if>
            <if test="statementDate != null">
                statement_date = #{statementDate},
            </if>
            <if test="firstOverdueDate != null">
                first_overdue_date = #{firstOverdueDate},
            </if>
            <if test="baseDate != null">
                base_date = #{baseDate},
            </if>
            <if test="creditLimit != null">
                credit_limit = #{creditLimit},
            </if>
            <if test="principalInterestTotal != null">
                principal_interest_total = #{principalInterestTotal},
            </if>
            <if test="interestFeesTotal != null">
                interest_fees_total = #{interestFeesTotal},
            </if>
        </set>
    </sql>

    <update id="updateByPrimaryKeySelective" parameterType="com.zws.system.api.domain.InfoLoan">
        update case_info_loan
        <include refid="UpdateSelectiveSet"></include>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <update id="updateByCaseIdSelective">
        update case_info_loan
        <include refid="UpdateSelectiveSet"></include>
        where case_id = #{caseId}
    </update>

    <update id="updateByCaseIdDefaultInterest" parameterType="com.zws.system.api.domain.InfoLoan">
        update case_info_loan
        set sy_yh_default   = #{syYhDefault},
            sy_yh_principal = #{syYhPrincipal},
            sy_yh_interest  = #{syYhInterest},
            remaining_due   = #{remainingDue},
            update_by       = #{updateBy},
            update_time     = #{updateTime}
        where del_flag = 0
          and case_id = #{caseId}
    </update>


</mapper>
