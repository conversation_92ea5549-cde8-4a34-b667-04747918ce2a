<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.UrgeRecordMapper">
    <resultMap id="BaseResultMap" type="com.zws.cis.domain.UrgeRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="liaison" jdbcType="VARCHAR" property="liaison"/>
        <result column="relation" jdbcType="VARCHAR" property="relation"/>
        <result column="contact_mode" jdbcType="VARCHAR" property="contactMode"/>
        <result column="follow_up_state" jdbcType="VARCHAR" property="followUpState"/>
        <result column="urge_state" jdbcType="VARCHAR" property="urgeState"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="odv_name" jdbcType="VARCHAR" property="odvName"/>
        <result column="contact_medium" jdbcType="VARCHAR" property="contactMedium"/>
        <result column="promise_repayment_time" jdbcType="TIMESTAMP" property="promiseRepaymentTime"/>
        <result column="promise_repayment_money" jdbcType="DECIMAL" property="promiseRepaymentMoney"/>
        <result column="another_time" jdbcType="TIMESTAMP" property="anotherTime"/>
        <result column="promise_by_stages" jdbcType="INTEGER" property="promiseByStages"/>
        <result column="promise_every_money" jdbcType="DECIMAL" property="promiseEveryMoney"/>
        <result column="promise_repayment_day" jdbcType="INTEGER" property="promiseRepaymentDay"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="contact_id" property="contactId"/>
        <result column="create_id" property="createId"/>
        <result column="import_reminder" property="importReminder"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , case_id, liaison, relation,contact_mode, follow_up_state, urge_state,
    content, remarks, odv_id, odv_name, contact_medium, promise_repayment_time, promise_repayment_money,
    another_time, promise_by_stages, promise_every_money, promise_repayment_day, del_flag,
    create_by, create_time, update_by, update_time,label,contact_id,import_reminder
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from case_urge_record
        where id = #{id,jdbcType=BIGINT}
    </select>
     <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from case_urge_record
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.cis.domain.UrgeRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into case_urge_record (id, case_id, liaison, relation, contact_id,
                                      contact_mode, follow_up_state,
                                      urge_state, content, remarks,
                                      odv_id, odv_name, contact_medium,
                                      promise_repayment_time, promise_repayment_money,
                                      another_time, promise_by_stages, promise_every_money,
                                      promise_repayment_day, del_flag, create_by,
                                      create_time, update_by, update_time, label, operation_type, import_reminder)
        values (#{id,jdbcType=BIGINT}, #{caseId,jdbcType=BIGINT}, #{liaison,jdbcType=VARCHAR},
                #{relation,jdbcType=VARCHAR}, #{contactId},
                #{contactMode,jdbcType=VARCHAR}, #{followUpState,jdbcType=VARCHAR},
                #{urgeState,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR},
                #{odvId,jdbcType=BIGINT}, #{odvName,jdbcType=VARCHAR}, #{contactMedium,jdbcType=VARCHAR},
                #{promiseRepaymentTime,jdbcType=TIMESTAMP}, #{promiseRepaymentMoney,jdbcType=DECIMAL},
                #{anotherTime,jdbcType=TIMESTAMP}, #{promiseByStages,jdbcType=INTEGER},
                #{promiseEveryMoney,jdbcType=DECIMAL},
                #{promiseRepaymentDay,jdbcType=DATE}, #{delFlag,jdbcType=CHAR}, #{createBy,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{label},
                #{operationType}, #{importReminder})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.cis.domain.UrgeRecord">
        insert into case_urge_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="caseId != null">
                case_id,
            </if>
            <if test="liaison != null">
                liaison,
            </if>
            <if test="relation != null">
                relation,
            </if>
            <if test="contactMode != null">
                contact_mode,
            </if>
            <if test="contactModeEnc != null">
                contact_mode_enc,
            </if>
            <if test="followUpState != null">
                follow_up_state,
            </if>
            <if test="urgeState != null">
                urge_state,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="odvId != null">
                odv_id,
            </if>
            <if test="odvName != null">
                odv_name,
            </if>
            <if test="contactMedium != null">
                contact_medium,
            </if>
            <if test="promiseRepaymentTime != null">
                promise_repayment_time,
            </if>
            <if test="promiseRepaymentMoney != null">
                promise_repayment_money,
            </if>
            <if test="anotherTime != null">
                another_time,
            </if>
            <if test="promiseByStages != null">
                promise_by_stages,
            </if>
            <if test="promiseEveryMoney != null">
                promise_every_money,
            </if>
            <if test="promiseRepaymentDay != null">
                promise_repayment_day,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="caseId != null">
                #{caseId,jdbcType=BIGINT},
            </if>
            <if test="liaison != null">
                #{liaison,jdbcType=VARCHAR},
            </if>
            <if test="relation != null">
                #{relation,jdbcType=VARCHAR},
            </if>
            <if test="contactMode != null">
                #{contactMode,jdbcType=VARCHAR},
            </if>
            <if test="contactModeEnc != null">
                #{contactModeEnc,jdbcType=VARCHAR},
            </if>
            <if test="followUpState != null">
                #{followUpState,jdbcType=VARCHAR},
            </if>
            <if test="urgeState != null">
                #{urgeState,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="odvId != null">
                #{odvId,jdbcType=BIGINT},
            </if>
            <if test="odvName != null">
                #{odvName,jdbcType=VARCHAR},
            </if>
            <if test="contactMedium != null">
                #{contactMedium,jdbcType=VARCHAR},
            </if>
            <if test="promiseRepaymentTime != null">
                #{promiseRepaymentTime,jdbcType=TIMESTAMP},
            </if>
            <if test="promiseRepaymentMoney != null">
                #{promiseRepaymentMoney,jdbcType=DECIMAL},
            </if>
            <if test="anotherTime != null">
                #{anotherTime,jdbcType=TIMESTAMP},
            </if>
            <if test="promiseByStages != null">
                #{promiseByStages,jdbcType=INTEGER},
            </if>
            <if test="promiseEveryMoney != null">
                #{promiseEveryMoney,jdbcType=DECIMAL},
            </if>
            <if test="promiseRepaymentDay != null">
                #{promiseRepaymentDay,jdbcType=DATE},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.cis.domain.UrgeRecord">
        update case_urge_record
        <set>
            <if test="caseId != null">
                case_id = #{caseId,jdbcType=BIGINT},
            </if>
            <if test="liaison != null">
                liaison = #{liaison,jdbcType=VARCHAR},
            </if>
            <if test="relation != null">
                relation = #{relation,jdbcType=VARCHAR},
            </if>
            <if test="contactId!=null">
                contact_id=#{contactId},
            </if>
            <if test="contactMode != null">
                contact_mode = #{contactMode,jdbcType=VARCHAR},
            </if>
            <if test="followUpState != null">
                follow_up_state = #{followUpState,jdbcType=VARCHAR},
            </if>
            <if test="urgeState != null">
                urge_state = #{urgeState,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="odvId != null">
                odv_id = #{odvId,jdbcType=BIGINT},
            </if>
            <if test="odvName != null">
                odv_name = #{odvName,jdbcType=VARCHAR},
            </if>
            <if test="contactMedium != null">
                contact_medium = #{contactMedium,jdbcType=VARCHAR},
            </if>
            <if test="promiseRepaymentTime != null">
                promise_repayment_time = #{promiseRepaymentTime,jdbcType=TIMESTAMP},
            </if>
            <if test="promiseRepaymentMoney != null">
                promise_repayment_money = #{promiseRepaymentMoney,jdbcType=DECIMAL},
            </if>
            <if test="anotherTime != null">
                another_time = #{anotherTime,jdbcType=TIMESTAMP},
            </if>
            <if test="promiseByStages != null">
                promise_by_stages = #{promiseByStages,jdbcType=INTEGER},
            </if>
            <if test="promiseEveryMoney != null">
                promise_every_money = #{promiseEveryMoney,jdbcType=DECIMAL},
            </if>
            <if test="promiseRepaymentDay != null">
                promise_repayment_day = #{promiseRepaymentDay,jdbcType=DATE},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="label!=null">
                label=#{label}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.cis.domain.UrgeRecord">
        update case_urge_record
        set case_id                 = #{caseId,jdbcType=BIGINT},
            liaison                 = #{liaison,jdbcType=VARCHAR},
            relation                = #{relation,jdbcType=VARCHAR},
            contact_mode            = #{contactMode,jdbcType=VARCHAR},
            contact_mode_enc        = #{contactModeEnc,jdbcType=VARCHAR},
            follow_up_state         = #{followUpState,jdbcType=VARCHAR},
            urge_state              = #{urgeState,jdbcType=VARCHAR},
            content                 = #{content,jdbcType=VARCHAR},
            remarks                 = #{remarks,jdbcType=VARCHAR},
            odv_id                  = #{odvId,jdbcType=BIGINT},
            odv_name                = #{odvName,jdbcType=VARCHAR},
            contact_medium          = #{contactMedium,jdbcType=VARCHAR},
            promise_repayment_time  = #{promiseRepaymentTime,jdbcType=TIMESTAMP},
            promise_repayment_money = #{promiseRepaymentMoney,jdbcType=DECIMAL},
            another_time            = #{anotherTime,jdbcType=TIMESTAMP},
            promise_by_stages       = #{promiseByStages,jdbcType=INTEGER},
            promise_every_money     = #{promiseEveryMoney,jdbcType=DECIMAL},
            promise_repayment_day   = #{promiseRepaymentDay,jdbcType=DATE},
            del_flag                = #{delFlag,jdbcType=CHAR},
            create_by               = #{createBy,jdbcType=VARCHAR},
            create_time             = #{createTime,jdbcType=TIMESTAMP},
            update_by               = #{updateBy,jdbcType=VARCHAR},
            update_time             = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
