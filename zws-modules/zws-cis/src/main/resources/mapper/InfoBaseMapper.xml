<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.InfoBaseMapper">
    <resultMap id="BaseResultMap" type="com.zws.system.api.domain.InfoBase">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="client_name_enc" jdbcType="VARCHAR" property="clientNameEnc"/>
        <result column="client_id_num" jdbcType="VARCHAR" property="clientIdNum"/>
        <result column="client_id_num_enc" jdbcType="VARCHAR" property="clientIdNumEnc"/>
        <result column="client_census_register" jdbcType="VARCHAR" property="clientCensusRegister"/>
        <result column="client_census_register_enc" jdbcType="VARCHAR" property="clientCensusRegisterEnc"/>
        <result column="client_phone" jdbcType="VARCHAR" property="clientPhone"/>
        <result column="client_phone_enc" jdbcType="VARCHAR" property="clientPhoneEnc"/>
        <result column="client_sex" jdbcType="VARCHAR" property="clientSex"/>
        <result column="client_age" jdbcType="INTEGER" property="clientAge"/>
        <result column="client_birthday" jdbcType="TIMESTAMP" property="clientBirthday"/>
        <result column="occupation" jdbcType="VARCHAR" property="occupation"/>
        <result column="marital_status" jdbcType="VARCHAR" property="maritalStatus"/>
        <result column="qq" jdbcType="VARCHAR" property="qq"/>
        <result column="weixin" jdbcType="VARCHAR" property="weixin"/>
        <result column="mailbox" jdbcType="VARCHAR" property="mailbox"/>
        <result column="place_of_work" jdbcType="VARCHAR" property="placeOfWork"/>
        <result column="working_address" jdbcType="VARCHAR" property="workingAddress"/>
        <result column="registered_address" jdbcType="VARCHAR" property="registeredAddress"/>
        <result column="residential_address" jdbcType="VARCHAR" property="residentialAddress"/>
        <result column="home_address" jdbcType="VARCHAR" property="homeAddress"/>
        <result column="bank_name" jdbcType="VARBINARY" property="bankName"/>
        <result column="bank_card_number" jdbcType="VARCHAR" property="bankCardNumber"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="client_id_type" property="clientIdType"/>
        <result column="security_name" property="securityName"/>
        <result column="security_name_enc" property="securityNameEnc"/>
        <result column="security_id_type" property="securityIdType"/>
        <result column="security_id_num" property="securityIdNum"/>
        <result column="security_id_num_enc" property="securityIdNumEnc"/>
        <result column="security_phone" property="securityPhone"/>
        <result column="security_phone_enc" property="securityPhoneEnc"/>
        <result column="client_id_type" property="clientIdType"/>
        <result column="asset_no" property="assetNo"/>
        <result column="uid" property="uid"/>
        <result column="education" property="education"/>
        <result column="academic_degree" property="academicDegree"/>
        <result column="employment_status" property="employmentStatus"/>
        <result column="residential_status" property="residentialStatus"/>
        <result column="home_phone" property="homePhone"/>
        <result column="duties" property="duties"/>
        <result column="title" property="title"/>
        <result column="unit_start_year" property="unitStartYear"/>
        <result column="unit_industry" property="unitIndustry"/>
        <result column="unit_postal_code" property="unitPostalCode"/>
        <result column="unit_telephone" property="unitTelephone"/>
        <result column="residence_postal_code" property="residencePostalCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , case_id, client_name, client_id_num, client_census_register,
     client_phone, client_sex, client_age,
    client_birthday, occupation, marital_status, qq, weixin, mailbox, place_of_work,
    working_address,registered_address, residential_address, home_address, bank_name,bank_card_number, create_by,
    create_time, update_by, update_time,del_flag,
    client_id_type,security_name,security_id_type,security_id_num,security_phone,client_id_type,asset_no,uid,education,
    academic_degree,employment_status,residential_status,home_phone,duties,title,unit_start_year,unit_industry,
    unit_postal_code,unit_telephone,residence_postal_code
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from case_info_base
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from case_info_base
        <where>1=1
            <if test="caseId!=null">
                and case_id=#{caseId}
            </if>
            <if test="clientNameEnc!=null and clientNameEnc!=''">
                and client_name_enc=#{clientNameEnc}
            </if>
            <if test="clientPhoneEnc!=null and clientPhoneEnc!=''">
                and client_phone_enc=#{clientPhoneEnc}
            </if>
            <if test="clientName!=null and clientName!=''">
                and client_name=#{clientName}
            </if>
            <if test="clientIdNum!=null and clientIdNum!=''">
                and client_id_num=#{clientIdNum}
            </if>
        </where>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from case_info_base
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.system.api.domain.InfoBase" useGeneratedKeys="true"
            keyProperty="id">
        insert into case_info_base (id, case_id, client_name,
                                    client_name_enc, client_id_num, client_id_num_enc,
                                    client_census_register, client_census_register_enc,
                                    client_phone, client_phone_enc, client_sex,
                                    client_age, client_birthday, occupation,
                                    marital_status, qq, weixin,
                                    mailbox, place_of_work, working_address, registered_address,
                                    residential_address, home_address, bank_card_number,
                                    create_by, create_time, update_by, update_time, bank_name,
                                    client_id_type, security_name, security_name_enc, security_id_type, security_id_num,
                                    security_id_num_enc, security_phone, security_phone_enc, administrative_bi,asset_no,
                                    uid,education,academic_degree,employment_status,residential_status,home_phone,duties,
                                    title,unit_start_year,unit_industry,unit_postal_code,unit_telephone,residence_postal_code)
        values (#{id,jdbcType=BIGINT}, #{caseId,jdbcType=BIGINT}, #{clientName,jdbcType=VARCHAR},
                #{clientNameEnc,jdbcType=VARCHAR}, #{clientIdNum,jdbcType=VARCHAR}, #{clientIdNumEnc,jdbcType=VARCHAR},
                #{clientCensusRegister,jdbcType=VARCHAR}, #{clientCensusRegisterEnc,jdbcType=VARCHAR},
                #{clientPhone,jdbcType=VARCHAR}, #{clientPhoneEnc,jdbcType=VARCHAR}, #{clientSex,jdbcType=VARCHAR},
                #{clientAge,jdbcType=INTEGER}, #{clientBirthday,jdbcType=TIMESTAMP}, #{occupation,jdbcType=VARCHAR},
                #{maritalStatus,jdbcType=VARCHAR}, #{qq,jdbcType=VARCHAR}, #{weixin,jdbcType=VARCHAR},
                #{mailbox,jdbcType=VARCHAR}, #{placeOfWork,jdbcType=VARCHAR}, #{workingAddress,jdbcType=VARCHAR},
                #{registeredAddress},
                #{residentialAddress,jdbcType=VARCHAR}, #{homeAddress,jdbcType=VARCHAR},
                #{bankCardNumber,jdbcType=VARCHAR},
                #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
                #{updateTime,jdbcType=TIMESTAMP}, #{bankName,jdbcType=VARBINARY},
                #{clientIdType}, #{securityName}, #{securityNameEnc}, #{securityIdType}, #{securityIdNum},
                #{securityIdNumEnc}, #{securityPhone}, #{securityPhoneEnc}, #{administrativeBi},#{assetNo},#{uid},#{education},
                #{academicDegree},#{employmentStatus},#{residentialStatus},#{homePhone},#{duties},#{title},#{unitStartYear},
                #{unitIndustry},#{unitPostalCode},#{unitTelephone},#{residencePostalCode})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.system.api.domain.InfoBase">
        insert into case_info_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="caseId != null">
                case_id,
            </if>
            <if test="clientName != null">
                client_name,
            </if>
            <if test="clientNameEnc != null">
                client_name_enc,
            </if>
            <if test="clientIdNum != null">
                client_id_num,
            </if>
            <if test="clientIdNumEnc != null">
                client_id_num_enc,
            </if>
            <if test="clientCensusRegister != null">
                client_census_register,
            </if>
            <if test="clientCensusRegisterEnc != null">
                client_census_register_enc,
            </if>
            <if test="clientPhone != null">
                client_phone,
            </if>
            <if test="clientPhoneEnc != null">
                client_phone_enc,
            </if>
            <if test="clientSex != null">
                client_sex,
            </if>
            <if test="clientAge != null">
                client_age,
            </if>
            <if test="clientBirthday != null">
                client_birthday,
            </if>
            <if test="occupation != null">
                occupation,
            </if>
            <if test="maritalStatus != null">
                marital_status,
            </if>
            <if test="qq != null">
                qq,
            </if>
            <if test="weixin != null">
                weixin,
            </if>
            <if test="mailbox != null">
                mailbox,
            </if>
            <if test="placeOfWork != null">
                place_of_work,
            </if>
            <if test="workingAddress != null">
                working_address,
            </if>
            <if test="residentialAddress != null">
                residential_address,
            </if>
            <if test="homeAddress != null">
                home_address,
            </if>
            <if test="bankCardNumber != null">
                bank_card_number,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="bankName != null">
                bank_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="caseId != null">
                #{caseId,jdbcType=BIGINT},
            </if>
            <if test="clientName != null">
                #{clientName,jdbcType=VARCHAR},
            </if>
            <if test="clientNameEnc != null">
                #{clientNameEnc,jdbcType=VARCHAR},
            </if>
            <if test="clientIdNum != null">
                #{clientIdNum,jdbcType=VARCHAR},
            </if>
            <if test="clientIdNumEnc != null">
                #{clientIdNumEnc,jdbcType=VARCHAR},
            </if>
            <if test="clientCensusRegister != null">
                #{clientCensusRegister,jdbcType=VARCHAR},
            </if>
            <if test="clientCensusRegisterEnc != null">
                #{clientCensusRegisterEnc,jdbcType=VARCHAR},
            </if>
            <if test="clientPhone != null">
                #{clientPhone,jdbcType=VARCHAR},
            </if>
            <if test="clientPhoneEnc != null">
                #{clientPhoneEnc,jdbcType=VARCHAR},
            </if>
            <if test="clientSex != null">
                #{clientSex,jdbcType=VARCHAR},
            </if>
            <if test="clientAge != null">
                #{clientAge,jdbcType=INTEGER},
            </if>
            <if test="clientBirthday != null">
                #{clientBirthday,jdbcType=TIMESTAMP},
            </if>
            <if test="occupation != null">
                #{occupation,jdbcType=VARCHAR},
            </if>
            <if test="maritalStatus != null">
                #{maritalStatus,jdbcType=VARCHAR},
            </if>
            <if test="qq != null">
                #{qq,jdbcType=VARCHAR},
            </if>
            <if test="weixin != null">
                #{weixin,jdbcType=VARCHAR},
            </if>
            <if test="mailbox != null">
                #{mailbox,jdbcType=VARCHAR},
            </if>
            <if test="placeOfWork != null">
                #{placeOfWork,jdbcType=VARCHAR},
            </if>
            <if test="workingAddress != null">
                #{workingAddress,jdbcType=VARCHAR},
            </if>
            <if test="residentialAddress != null">
                #{residentialAddress,jdbcType=VARCHAR},
            </if>
            <if test="homeAddress != null">
                #{homeAddress,jdbcType=VARCHAR},
            </if>
            <if test="bankCardNumber != null">
                #{bankCardNumber,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="bankName != null">
                #{bankName,jdbcType=VARBINARY},
            </if>
        </trim>
    </insert>
    <sql id="UpdateSelectiveSet">
        <set>
            <if test="clientName != null">
                client_name = #{clientName,jdbcType=VARCHAR},
            </if>
            <if test="clientNameEnc != null">
                client_name_enc = #{clientNameEnc,jdbcType=VARCHAR},
            </if>
            <if test="clientIdType != null">
                client_id_type = #{clientIdType},
            </if>
            <if test="clientIdNum != null">
                client_id_num = #{clientIdNum,jdbcType=VARCHAR},
            </if>
            <if test="clientIdNumEnc != null">
                client_id_num_enc = #{clientIdNumEnc,jdbcType=VARCHAR},
            </if>
            <if test="clientCensusRegister != null">
                client_census_register = #{clientCensusRegister,jdbcType=VARCHAR},
            </if>
            <if test="clientCensusRegisterEnc != null">
                client_census_register_enc = #{clientCensusRegisterEnc,jdbcType=VARCHAR},
            </if>
            <if test="clientPhone != null">
                client_phone = #{clientPhone,jdbcType=VARCHAR},
            </if>
            <if test="clientPhoneEnc != null">
                client_phone_enc = #{clientPhoneEnc,jdbcType=VARCHAR},
            </if>
            <if test="clientSex != null">
                client_sex = #{clientSex,jdbcType=VARCHAR},
            </if>
            <if test="clientAge != null">
                client_age = #{clientAge,jdbcType=INTEGER},
            </if>
            <if test="clientBirthday != null">
                client_birthday = #{clientBirthday,jdbcType=TIMESTAMP},
            </if>
            <if test="occupation != null">
                occupation = #{occupation,jdbcType=VARCHAR},
            </if>
            <if test="maritalStatus != null">
                marital_status = #{maritalStatus,jdbcType=VARCHAR},
            </if>
            <if test="qq != null">
                qq = #{qq,jdbcType=VARCHAR},
            </if>
            <if test="weixin != null">
                weixin = #{weixin,jdbcType=VARCHAR},
            </if>
            <if test="mailbox != null">
                mailbox = #{mailbox,jdbcType=VARCHAR},
            </if>
            <if test="placeOfWork != null">
                place_of_work = #{placeOfWork,jdbcType=VARCHAR},
            </if>
            <if test="workingAddress != null">
                working_address = #{workingAddress,jdbcType=VARCHAR},
            </if>
            <if test="registeredAddress != null">
                registered_address = #{registeredAddress,jdbcType=VARCHAR},
            </if>
            <if test="residentialAddress != null">
                residential_address = #{residentialAddress,jdbcType=VARCHAR},
            </if>
            <if test="homeAddress != null">
                home_address = #{homeAddress,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null">
                bank_name = #{bankName,jdbcType=VARBINARY},
            </if>
            <if test="bankCardNumber != null">
                bank_card_number = #{bankCardNumber,jdbcType=VARCHAR},
            </if>


            <if test="securityName != null">
                security_name = #{securityName},
            </if>
            <if test="securityNameEnc != null">
                security_name_enc = #{securityNameEnc},
            </if>
            <if test="securityIdType != null">
                security_id_type = #{securityIdType},
            </if>
            <if test="securityIdNum != null">
                security_id_num = #{securityIdNum},
            </if>
            <if test="securityIdNumEnc != null">
                security_id_num_enc = #{securityIdNumEnc},
            </if>
            <if test="securityPhone != null">
                security_phone = #{securityPhone},
            </if>
            <if test="securityPhoneEnc != null">
                security_phone_enc = #{securityPhoneEnc},
            </if>
            <if test="administrativeBi != null">
                administrative_bi = #{administrativeBi},
            </if>
            <if test="assetNo != null">
                asset_no = #{assetNo,jdbcType=VARBINARY},
            </if>
            <if test="uid != null">
                uid = #{uid},
            </if>
            <if test="education != null">
                education = #{education},
            </if>
            <if test="academicDegree != null">
                academic_degree = #{academicDegree},
            </if>
            <if test="employmentStatus != null">
                employment_status = #{employmentStatus},
            </if>
            <if test="residentialStatus != null">
                residential_status = #{residentialStatus},
            </if>
            <if test="homePhone != null">
                home_phone = #{homePhone},
            </if>
            <if test="duties != null">
                duties = #{duties},
            </if>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="unitStartYear != null">
                unit_start_year = #{unitStartYear},
            </if>
            <if test="unitIndustry != null">
                unit_industry = #{unitIndustry},
            </if>
            <if test="unitPostalCode != null">
                unit_postal_code = #{unitPostalCode},
            </if>
            <if test="unitTelephone != null">
                unit_telephone = #{unitTelephone},
            </if>
            <if test="residencePostalCode != null">
                residence_postal_code = #{residencePostalCode},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
    </sql>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.system.api.domain.InfoBase">
        update case_info_base
        <include refid="UpdateSelectiveSet"></include>
        where id =#{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.system.api.domain.InfoBase">
        update case_info_base
        set case_id                    = #{caseId,jdbcType=BIGINT},
            client_name                = #{clientName,jdbcType=VARCHAR},
            client_name_enc            = #{clientNameEnc,jdbcType=VARCHAR},
            client_id_num              = #{clientIdNum,jdbcType=VARCHAR},
            client_id_num_enc          = #{clientIdNumEnc,jdbcType=VARCHAR},
            client_census_register     = #{clientCensusRegister,jdbcType=VARCHAR},
            client_census_register_enc = #{clientCensusRegisterEnc,jdbcType=VARCHAR},
            client_phone               = #{clientPhone,jdbcType=VARCHAR},
            client_phone_enc           = #{clientPhoneEnc,jdbcType=VARCHAR},
            client_sex                 = #{clientSex,jdbcType=VARCHAR},
            client_age                 = #{clientAge,jdbcType=INTEGER},
            client_birthday            = #{clientBirthday,jdbcType=TIMESTAMP},
            occupation                 = #{occupation,jdbcType=VARCHAR},
            marital_status             = #{maritalStatus,jdbcType=VARCHAR},
            qq                         = #{qq,jdbcType=VARCHAR},
            weixin                     = #{weixin,jdbcType=VARCHAR},
            mailbox                    = #{mailbox,jdbcType=VARCHAR},
            place_of_work              = #{placeOfWork,jdbcType=VARCHAR},
            working_address            = #{workingAddress,jdbcType=VARCHAR},
            residential_address        = #{residentialAddress,jdbcType=VARCHAR},
            home_address               = #{homeAddress,jdbcType=VARCHAR},
            bank_card_number           = #{bankCardNumber,jdbcType=VARCHAR},
            create_by                  = #{createBy,jdbcType=VARCHAR},
            create_time                = #{createTime,jdbcType=TIMESTAMP},
            update_by                  = #{updateBy,jdbcType=VARCHAR},
            update_time                = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByCaseIdSelective">
        update case_info_base
        <include refid="UpdateSelectiveSet"></include>
        where case_id =#{caseId,jdbcType=BIGINT}
    </update>
</mapper>
