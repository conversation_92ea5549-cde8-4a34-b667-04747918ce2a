<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.TeamContractMapper">
  <resultMap id="BaseResultMap" type="com.zws.cis.domain.TeamContract">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="asset_manage_id" jdbcType="BIGINT" property="assetManageId" />
    <result column="file_Name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file_format" jdbcType="VARCHAR" property="fileFormat" />
    <result column="file_size" jdbcType="BIGINT" property="fileSize" />
    <result column="team_id" jdbcType="INTEGER" property="teamId" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  <sql id="Base_Column_List">
    id, asset_manage_id, file_Name, file_url, file_format, file_size, team_id, create_by, 
    create_time, update_by, update_time, del_flag, remarks
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from team_asset_contract
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from team_asset_contract
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.zws.cis.domain.TeamContract">
    insert into team_asset_contract (id, asset_manage_id, file_Name, 
      file_url, file_format, file_size, 
      team_id, create_by, create_time, 
      update_by, update_time, del_flag, 
      remarks)
    values (#{id,jdbcType=BIGINT}, #{assetManageId,jdbcType=BIGINT}, #{fileName,jdbcType=VARCHAR}, 
      #{fileUrl,jdbcType=VARCHAR}, #{fileFormat,jdbcType=VARCHAR}, #{fileSize,jdbcType=BIGINT}, 
      #{teamId,jdbcType=INTEGER}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR}, 
      #{remarks,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zws.cis.domain.TeamContract">
    insert into team_asset_contract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="assetManageId != null">
        asset_manage_id,
      </if>
      <if test="fileName != null">
        file_Name,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="fileFormat != null">
        file_format,
      </if>
      <if test="fileSize != null">
        file_size,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="assetManageId != null">
        #{assetManageId,jdbcType=BIGINT},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileFormat != null">
        #{fileFormat,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        #{fileSize,jdbcType=BIGINT},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.cis.domain.TeamContract">
    update team_asset_contract
    <set>
      <if test="assetManageId != null">
        asset_manage_id = #{assetManageId,jdbcType=BIGINT},
      </if>
      <if test="fileName != null">
        file_Name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileFormat != null">
        file_format = #{fileFormat,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        file_size = #{fileSize,jdbcType=BIGINT},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zws.cis.domain.TeamContract">
    update team_asset_contract
    set asset_manage_id = #{assetManageId,jdbcType=BIGINT},
      file_Name = #{fileName,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      file_format = #{fileFormat,jdbcType=VARCHAR},
      file_size = #{fileSize,jdbcType=BIGINT},
      team_id = #{teamId,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del_flag = #{delFlag,jdbcType=CHAR},
      remarks = #{remarks,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>