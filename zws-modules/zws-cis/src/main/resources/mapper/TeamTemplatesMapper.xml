<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.TeamTemplatesMapper">
    <resultMap id="BaseResultMap" type="com.zws.cis.domain.TeamTemplates">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="team_id" jdbcType="INTEGER" property="teamId"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.zws.cis.domain.TeamTemplates">
        <result column="template" jdbcType="LONGVARCHAR" property="template"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , name, state, team_id, del_flag, create_by, create_time, update_by, update_time
    </sql>
    <sql id="Blob_Column_List">
        template
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from team_asset_currency_templates
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectList" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from team_asset_currency_templates
        where del_flag = 0 and team_id = #{teamId}
        <if test="name!=null and name!=''">
            and name = #{name}
        </if>
        <if test="state!=null and state!=''">
            and state = #{state}
        </if>
        order by id desc
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from team_asset_currency_templates
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.cis.domain.TeamTemplates" keyProperty="id" useGeneratedKeys="true">
        insert into team_asset_currency_templates (id, name, state,
                                                   team_id, del_flag, create_by,
                                                   create_time, update_by, update_time,
                                                   template)
        values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR},
                #{teamId,jdbcType=INTEGER}, #{delFlag,jdbcType=CHAR}, #{createBy,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{template,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.cis.domain.TeamTemplates">
        insert into team_asset_currency_templates
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="template != null">
                template,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="template != null">
                #{template,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.cis.domain.TeamTemplates">
        update team_asset_currency_templates
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="template != null">
                template = #{template,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.zws.cis.domain.TeamTemplates">
        update team_asset_currency_templates
        set name        = #{name,jdbcType=VARCHAR},
            state       = #{state,jdbcType=VARCHAR},
            team_id     = #{teamId,jdbcType=INTEGER},
            del_flag    = #{delFlag,jdbcType=CHAR},
            create_by   = #{createBy,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_by   = #{updateBy,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            template    = #{template,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.cis.domain.TeamTemplates">
        update team_asset_currency_templates
        set name        = #{name,jdbcType=VARCHAR},
            state       = #{state,jdbcType=VARCHAR},
            team_id     = #{teamId,jdbcType=INTEGER},
            del_flag    = #{delFlag,jdbcType=CHAR},
            create_by   = #{createBy,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_by   = #{updateBy,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
