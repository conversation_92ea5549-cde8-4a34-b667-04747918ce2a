<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.InfoContactMapper">
  <resultMap id="BaseResultMap" type="com.zws.common.core.domain.sms.InfoContact">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_id" jdbcType="BIGINT" property="caseId" />

    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <!--<result column="contact_phone_enc" jdbcType="VARCHAR" property="contactPhoneEnc" />-->
    <result column="phone_location" jdbcType="VARCHAR" property="phoneLocation" />
    <result column="phone_state" jdbcType="VARCHAR" property="phoneState" />
    <result column="contact_relation" jdbcType="VARCHAR" property="contactRelation" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <!--<result column="contact_state" jdbcType="VARCHAR" property="contactState" />-->
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="app_flag" jdbcType="CHAR" property="appFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, case_id, contact_name, contact_phone, phone_location, phone_state,
    contact_relation, remarks, del_flag, create_by, create_time, update_by,
    update_time,app_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from case_info_contact
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from case_info_contact
    where del_flag=0
    <if test="caseId!=null">
      and case_id =#{caseId}
    </if>
    <if test="contactName!=null and contactName!=''">
      and contact_name = #{contactName}
    </if>
    <if test="contactPhoneEnc!=null and contactPhoneEnc!=''">
      and contact_phone_enc = #{contactPhoneEnc}
    </if>

  </select>
    <select id="selectSearchList" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"></include>
      from case_info_contact
      where del_flag=0
      <if test="caseId!=null">
        and case_id =#{caseId}
      </if>
      <if test="search!=null and search!=''">
        and (contact_name = #{search} or  contact_phone = #{search})
      </if>
    </select>
    <select id="selectInitialInfoContact" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List"></include>
      FROM case_info_contact
      WHERE contact_relation = '本人' AND case_id = #{caseId} and del_flag='0' ORDER BY create_time LIMIT 1
    </select>


    <insert id="insert" parameterType="com.zws.common.core.domain.sms.InfoContact" keyProperty="id" useGeneratedKeys="true">
    insert into case_info_contact (id, case_id,
      contact_name, contact_phone, phone_location,
      phone_state, contact_relation, remarks,
       del_flag, create_by,
      create_time, update_by, update_time
      )
    values (#{id,jdbcType=BIGINT},#{caseId,jdbcType=BIGINT},
      #{contactName,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR},  #{phoneLocation,jdbcType=VARCHAR},
      #{phoneState,jdbcType=VARCHAR}, #{contactRelation,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR},
     #{delFlag,jdbcType=CHAR}, #{createBy,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>

  <insert id="batchInsert" >
    insert into case_info_contact ( case_id,
                                   contact_name, contact_phone, phone_location,
                                   phone_state, contact_relation, remarks,
                                   del_flag, create_by,
                                   create_time
    )
    values
    <foreach collection="records" item="record" separator=",">
        (#{record.caseId,jdbcType=BIGINT},
            #{record.contactName,jdbcType=VARCHAR}, #{record.contactPhone,jdbcType=VARCHAR},  #{record.phoneLocation,jdbcType=VARCHAR},
            #{record.phoneState,jdbcType=VARCHAR}, #{record.contactRelation,jdbcType=VARCHAR}, #{record.remarks,jdbcType=VARCHAR},
            #{record.delFlag,jdbcType=CHAR}, #{record.createBy,jdbcType=VARCHAR},
            #{record.createTime,jdbcType=TIMESTAMP}
           )
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.zws.common.core.domain.sms.InfoContact">
    insert into case_info_contact
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="caseId != null">
        case_id,
      </if>
      <if test="caseManageId != null">
        case_manage_id,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="contactPhoneEnc != null">
        contact_phone_enc,
      </if>
      <if test="phoneLocation != null">
        phone_location,
      </if>
      <if test="phoneState != null">
        phone_state,
      </if>
      <if test="contactRelation != null">
        contact_relation,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="contactState != null">
        contact_state,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="caseId != null">
        #{caseId,jdbcType=BIGINT},
      </if>
      <if test="caseManageId != null">
        #{caseManageId,jdbcType=BIGINT},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactPhoneEnc != null">
        #{contactPhoneEnc,jdbcType=VARCHAR},
      </if>
      <if test="phoneLocation != null">
        #{phoneLocation,jdbcType=VARCHAR},
      </if>
      <if test="phoneState != null">
        #{phoneState,jdbcType=VARCHAR},
      </if>
      <if test="contactRelation != null">
        #{contactRelation,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="contactState != null">
        #{contactState,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.common.core.domain.sms.InfoContact">
    update case_info_contact
    <set>
      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>

      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="phoneLocation != null">
        phone_location = #{phoneLocation,jdbcType=VARCHAR},
      </if>
      <if test="phoneState != null">
        phone_state = #{phoneState,jdbcType=VARCHAR},
      </if>
      <if test="contactRelation != null">
        contact_relation = #{contactRelation,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>

      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>
