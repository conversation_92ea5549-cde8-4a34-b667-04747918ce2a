<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.TeamManageMapper">
    <resultMap id="BaseResultMap" type="com.zws.cis.domain.TeamManage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="batch_num" jdbcType="VARCHAR" property="batchNum"/>
        <result column="creditor_access_date" jdbcType="TIMESTAMP" property="creditorAccessDate"/>
        <result column="contract_filename" jdbcType="VARCHAR" property="contractFilename"/>
        <result column="contract_url" jdbcType="VARCHAR" property="contractUrl"/>
        <result column="source_fileurl" jdbcType="VARCHAR" property="sourceFileurl"/>
        <result column="source_filename" jdbcType="VARCHAR" property="sourceFilename"/>
        <result column="case_number" jdbcType="BIGINT" property="caseNumber"/>
        <result column="entrust_money_total" jdbcType="DECIMAL" property="entrustMoneyTotal"/>
        <result column="owner_id" jdbcType="BIGINT" property="ownerId"/>
        <result column="acquisition_costs" jdbcType="DECIMAL" property="acquisitionCosts"/>
        <result column="target_amount" jdbcType="DECIMAL" property="targetAmount"/>
        <result column="begin_period" jdbcType="DATE" property="beginPeriod"/>
        <result column="end_period" jdbcType="DATE" property="endPeriod"/>
        <result column="rate" jdbcType="DECIMAL" property="rate"/>
        <result column="expected_revenue" jdbcType="DECIMAL" property="expectedRevenue"/>
        <result column="acquisition_date" jdbcType="DATE" property="acquisitionDate"/>
        <result column="closing_date" jdbcType="DATE" property="closingDate"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="import_start" jdbcType="VARCHAR" property="importStart"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="auto_reduction" jdbcType="CHAR" property="autoReduction"/>
        <result column="reduction_id" jdbcType="BIGINT" property="reductionId"/>
        <result column="entrust_money_id" jdbcType="BIGINT" property="entrustMoneyId"/>
        <result column="creditor_announcement_date" jdbcType="DATE" property="creditorAnnouncementDate"/>
        <result column="creditor_announcement" jdbcType="VARCHAR" property="creditorAnnouncement"/>
        <result column="signing_date" jdbcType="DATE" property="signingDate"/>
        <result column="protocol_number" jdbcType="VARCHAR" property="protocolNumber"/>
        <result column="protocol" jdbcType="VARCHAR" property="protocol"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="package_name" jdbcType="VARCHAR" property="packageName"/>
        <result column="open_account" jdbcType="VARCHAR" property="openAccount"/>
        <result column="account_id" jdbcType="BIGINT" property="accountId"/>
        <result column="offset_order" jdbcType="VARCHAR" property="offsetOrder"/>
        <result column="settle_id" jdbcType="VARCHAR" property="settleId"/>
        <result column="settle_name" jdbcType="VARCHAR" property="settleName"/>
        <result column="team_id" jdbcType="INTEGER" property="teamId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , batch_num, creditor_access_date, contract_filename, contract_url, source_fileurl,
    source_filename, case_number, entrust_money_total, owner_id, acquisition_costs, target_amount,
    begin_period, end_period, rate, expected_revenue, acquisition_date, closing_date,
    product_id, import_start, operator_id, auto_reduction, reduction_id, entrust_money_id,
    creditor_announcement_date, creditor_announcement, signing_date, protocol_number,
    protocol, create_by, create_time, update_by, update_time, del_flag, package_name,
    open_account, account_id, offset_order, settle_id, settle_name, team_id
    </sql>
    <sql id="Base_Where_Join">
        <if test="batchNum!=null and batchNum!=''">
            and am.batch_num=#{batchNum}
        </if>
        <if test="entrustMoneyTotal1!=null ">
            and am.entrust_money_total >= #{entrustMoneyTotal1}
        </if>
        <if test="entrustMoneyTotal2!=null ">
            and am.entrust_money_total &lt;= #{entrustMoneyTotal2}
        </if>
        <if test="caseNumber1!=null ">
            and am.case_number >= #{caseNumber1}
        </if>
        <if test="caseNumber2!=null ">
            and am.case_number &lt;= #{caseNumber2}
        </if>

        <if test="creditorAccessDate1!=null ">
            and am.creditor_access_date >= #{creditorAccessDate1}
        </if>
        <if test="creditorAccessDate2!=null ">
            and am.creditor_access_date &lt;= #{creditorAccessDate2}
        </if>
        <if test="importStart!=null and importStart!='' ">
            and am.import_start = #{importStart}
        </if>
        <if test="ownerId!=null">
            and am.owner_id = #{ownerId}
        </if>
        <if test="productId!=null">
            and am.product_id = #{productId}
        </if>
        <if test="packageNameArr!=null and packageNameArr.size()>0 ">
            and am.package_name in
            <foreach collection="packageNameArr" item="packageName" separator="," open="(" close=")">
                #{packageName}
            </foreach>
        </if>
    </sql>
    <sql id="Base_Where_Join_Count">
        <if test="batchNum!=null and batchNum!=''">
            and am.batch_num=#{batchNum}
        </if>
        <if test="entrustMoneyTotal1!=null ">
            and am.entrust_money_total >= #{entrustMoneyTotal1}
        </if>
        <if test="entrustMoneyTotal2!=null ">
            and am.entrust_money_total &lt;= #{entrustMoneyTotal2}
        </if>
        <if test="caseNumber1!=null ">
            and am.case_number >= #{caseNumber1}
        </if>
        <if test="caseNumber2!=null ">
            and am.case_number &lt;= #{caseNumber2}
        </if>

        <if test="creditorAccessDate1!=null ">
            and am.creditor_access_date >= #{creditorAccessDate1}
        </if>
        <if test="creditorAccessDate2!=null ">
            and am.creditor_access_date &lt;= #{creditorAccessDate2}
        </if>
        <if test="importStart!=null and importStart!='' ">
            and am.import_start = #{importStart}
        </if>
        <if test="ownerId!=null">
            and tap.owner_id = #{ownerId}
        </if>
        <if test="productId!=null">
            and lib.product_id = #{productId}
        </if>
        <if test="packageNameArr!=null and packageNameArr.size()>0 ">
            and am.package_name in
            <foreach collection="packageNameArr" item="packageName" separator="," open="(" close=")">
                #{packageName}
            </foreach>
        </if>
    </sql>
    <sql id="orderBy">
        <choose>
            <when test="sortOrder != null and orderBy != null ">
                <if test="sortOrder == 1 and orderBy==1">
                    order by am.case_number asc
                </if>
                <if test="sortOrder == 1 and orderBy==2">
                    order by am.entrust_money_total asc
                </if>

                <if test="sortOrder == 2 and orderBy==1">
                    order by am.case_number desc
                </if>
                <if test="sortOrder == 2 and orderBy==2">
                    order by am.entrust_money_total desc
                </if>
            </when>
            <otherwise>
                order by am.id desc
            </otherwise>
        </choose>
    </sql>
    <sql id="Base_Column_List_Join">
        am
        .
        id
        , am.batch_num, am.creditor_access_date, am.contract_url, contract_filename,source_fileurl,source_filename, am.case_number, am.entrust_money_total,
    am.owner_id, am.product_id, am.import_start, am.operator_id,am.auto_reduction, am.reduction_id,am.entrust_money_id,am.del_flag, am.create_by, am.create_time,
    am.update_by, am.update_time,am.acquisition_costs,am.target_amount,am.begin_period,am.end_period,am.rate,am.expected_revenue,am.acquisition_date,
    am.package_name, am.open_account, am.account_id, ao.name as ownerName, ap.name as productName
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from team_asset_manage
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Join"/>
        from team_asset_manage as am
        left join team_asset_owner as ao on(ao.id = am.owner_id)
        left join team_asset_product as ap on (ap.id = am.product_id)
        where am.del_flag = 0 and am.team_id = #{teamId}
        <include refid="Base_Where_Join"></include>
        <include refid="orderBy"/>
    </select>

    <select id="selectCaseNumByBatchNum" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM case_manage
        WHERE batch_num = #{batchNum}
          AND (
                odv_id IS NOT NULL
                AND odv_id != 0
            )
          AND del_flag = 0 AND import_source = 1
    </select>

    <select id="selectCount" resultType="java.util.Map">
        SELECT COUNT(1) AS caseNum,SUM(cil.remaining_due) AS remainingDue,SUM(cil.sy_yh_principal) AS syYhPrincipal
        FROM
        case_manage AS lib
        LEFT JOIN case_info_loan AS cil ON (  lib.case_id = cil.case_id  AND cil.del_flag = 0 )
        LEFT JOIN team_asset_product AS tap ON ( lib.product_id = tap.id and tap.state = 0)
        LEFT JOIN team_asset_manage AS am ON ( lib.batch_num = am.batch_num AND am.del_flag = 0 )
        WHERE lib.del_flag = 0
        and lib.team_id = #{teamId}
        and am.import_start in (1,3)
        <include refid="Base_Where_Join_Count"></include>
        <include refid="orderBy"/>
    </select>
    <select id="getOwnerShortNameByProductId" resultType="java.lang.String">
        select tao.short_name
        from team_asset_product as tap
                 left join team_asset_owner as tao on (tap.owner_id = tao.id)
        where tap.id = #{productId}
          and tap.del_flag = '0' LIMIT 1
    </select>
    <select id="selectBatchNumCount" resultType="java.lang.Long">
        select count(1)
        from team_asset_manage
        where batch_num = #{batchNum}
         -- and del_flag = 0
    </select>
    <select id="selectPackageNameById" resultType="java.lang.String">
        select package_name
        from team_asset_manage
        where team_id = #{teamId}
          and del_flag = 0
          and package_name is not null
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from team_asset_manage
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.cis.domain.TeamManage" useGeneratedKeys="true"
            keyProperty="id">
        insert into team_asset_manage (id, batch_num, creditor_access_date,
                                       contract_filename, contract_url, source_fileurl,
                                       source_filename, case_number, entrust_money_total,
                                       owner_id, acquisition_costs, target_amount,
                                       begin_period, end_period, rate,
                                       expected_revenue, acquisition_date, closing_date,
                                       product_id, import_start, operator_id,
                                       auto_reduction, reduction_id, entrust_money_id,
                                       creditor_announcement_date, creditor_announcement,
                                       signing_date, protocol_number, protocol,
                                       create_by, create_time, update_by,
                                       update_time, del_flag, package_name,
                                       open_account, account_id, offset_order,
                                       settle_id, settle_name, team_id)
        values (#{id,jdbcType=BIGINT}, #{batchNum,jdbcType=VARCHAR}, #{creditorAccessDate,jdbcType=TIMESTAMP},
                #{contractFilename,jdbcType=VARCHAR}, #{contractUrl,jdbcType=VARCHAR},
                #{sourceFileurl,jdbcType=VARCHAR},
                #{sourceFilename,jdbcType=VARCHAR}, #{caseNumber,jdbcType=BIGINT},
                #{entrustMoneyTotal,jdbcType=DECIMAL},
                #{ownerId,jdbcType=BIGINT}, #{acquisitionCosts,jdbcType=DECIMAL}, #{targetAmount,jdbcType=DECIMAL},
                #{beginPeriod,jdbcType=DATE}, #{endPeriod,jdbcType=DATE}, #{rate,jdbcType=DECIMAL},
                #{expectedRevenue,jdbcType=DECIMAL}, #{acquisitionDate,jdbcType=DATE}, #{closingDate,jdbcType=DATE},
                #{productId,jdbcType=BIGINT}, #{importStart,jdbcType=VARCHAR}, #{operatorId,jdbcType=BIGINT},
                #{autoReduction,jdbcType=CHAR}, #{reductionId,jdbcType=BIGINT}, #{entrustMoneyId,jdbcType=BIGINT},
                #{creditorAnnouncementDate,jdbcType=DATE}, #{creditorAnnouncement,jdbcType=VARCHAR},
                #{signingDate,jdbcType=DATE}, #{protocolNumber,jdbcType=VARCHAR}, #{protocol,jdbcType=VARCHAR},
                #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
                #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR}, #{packageName,jdbcType=VARCHAR},
                #{openAccount,jdbcType=VARCHAR}, #{accountId,jdbcType=BIGINT}, #{offsetOrder,jdbcType=VARCHAR},
                #{settleId,jdbcType=VARCHAR}, #{settleName,jdbcType=VARCHAR}, #{teamId,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.cis.domain.TeamManage">
        insert into team_asset_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="batchNum != null">
                batch_num,
            </if>
            <if test="creditorAccessDate != null">
                creditor_access_date,
            </if>
            <if test="contractFilename != null">
                contract_filename,
            </if>
            <if test="contractUrl != null">
                contract_url,
            </if>
            <if test="sourceFileurl != null">
                source_fileurl,
            </if>
            <if test="sourceFilename != null">
                source_filename,
            </if>
            <if test="caseNumber != null">
                case_number,
            </if>
            <if test="entrustMoneyTotal != null">
                entrust_money_total,
            </if>
            <if test="ownerId != null">
                owner_id,
            </if>
            <if test="acquisitionCosts != null">
                acquisition_costs,
            </if>
            <if test="targetAmount != null">
                target_amount,
            </if>
            <if test="beginPeriod != null">
                begin_period,
            </if>
            <if test="endPeriod != null">
                end_period,
            </if>
            <if test="rate != null">
                rate,
            </if>
            <if test="expectedRevenue != null">
                expected_revenue,
            </if>
            <if test="acquisitionDate != null">
                acquisition_date,
            </if>
            <if test="closingDate != null">
                closing_date,
            </if>
            <if test="productId != null">
                product_id,
            </if>
            <if test="importStart != null">
                import_start,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="autoReduction != null">
                auto_reduction,
            </if>
            <if test="reductionId != null">
                reduction_id,
            </if>
            <if test="entrustMoneyId != null">
                entrust_money_id,
            </if>
            <if test="creditorAnnouncementDate != null">
                creditor_announcement_date,
            </if>
            <if test="creditorAnnouncement != null">
                creditor_announcement,
            </if>
            <if test="signingDate != null">
                signing_date,
            </if>
            <if test="protocolNumber != null">
                protocol_number,
            </if>
            <if test="protocol != null">
                protocol,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="packageName != null">
                package_name,
            </if>
            <if test="openAccount != null">
                open_account,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="offsetOrder != null">
                offset_order,
            </if>
            <if test="settleId != null">
                settle_id,
            </if>
            <if test="settleName != null">
                settle_name,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="batchNum != null">
                #{batchNum,jdbcType=VARCHAR},
            </if>
            <if test="creditorAccessDate != null">
                #{creditorAccessDate,jdbcType=TIMESTAMP},
            </if>
            <if test="contractFilename != null">
                #{contractFilename,jdbcType=VARCHAR},
            </if>
            <if test="contractUrl != null">
                #{contractUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceFileurl != null">
                #{sourceFileurl,jdbcType=VARCHAR},
            </if>
            <if test="sourceFilename != null">
                #{sourceFilename,jdbcType=VARCHAR},
            </if>
            <if test="caseNumber != null">
                #{caseNumber,jdbcType=BIGINT},
            </if>
            <if test="entrustMoneyTotal != null">
                #{entrustMoneyTotal,jdbcType=DECIMAL},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="acquisitionCosts != null">
                #{acquisitionCosts,jdbcType=DECIMAL},
            </if>
            <if test="targetAmount != null">
                #{targetAmount,jdbcType=DECIMAL},
            </if>
            <if test="beginPeriod != null">
                #{beginPeriod,jdbcType=DATE},
            </if>
            <if test="endPeriod != null">
                #{endPeriod,jdbcType=DATE},
            </if>
            <if test="rate != null">
                #{rate,jdbcType=DECIMAL},
            </if>
            <if test="expectedRevenue != null">
                #{expectedRevenue,jdbcType=DECIMAL},
            </if>
            <if test="acquisitionDate != null">
                #{acquisitionDate,jdbcType=DATE},
            </if>
            <if test="closingDate != null">
                #{closingDate,jdbcType=DATE},
            </if>
            <if test="productId != null">
                #{productId,jdbcType=BIGINT},
            </if>
            <if test="importStart != null">
                #{importStart,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                #{operatorId,jdbcType=BIGINT},
            </if>
            <if test="autoReduction != null">
                #{autoReduction,jdbcType=CHAR},
            </if>
            <if test="reductionId != null">
                #{reductionId,jdbcType=BIGINT},
            </if>
            <if test="entrustMoneyId != null">
                #{entrustMoneyId,jdbcType=BIGINT},
            </if>
            <if test="creditorAnnouncementDate != null">
                #{creditorAnnouncementDate,jdbcType=DATE},
            </if>
            <if test="creditorAnnouncement != null">
                #{creditorAnnouncement,jdbcType=VARCHAR},
            </if>
            <if test="signingDate != null">
                #{signingDate,jdbcType=DATE},
            </if>
            <if test="protocolNumber != null">
                #{protocolNumber,jdbcType=VARCHAR},
            </if>
            <if test="protocol != null">
                #{protocol,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="packageName != null">
                #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="accountId != null">
                #{accountId,jdbcType=BIGINT},
            </if>
            <if test="offsetOrder != null">
                #{offsetOrder,jdbcType=VARCHAR},
            </if>
            <if test="settleId != null">
                #{settleId,jdbcType=VARCHAR},
            </if>
            <if test="settleName != null">
                #{settleName,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.cis.domain.TeamManage">
        update team_asset_manage
        <set>
            <if test="batchNum != null">
                batch_num = #{batchNum,jdbcType=VARCHAR},
            </if>
            <if test="creditorAccessDate != null">
                creditor_access_date = #{creditorAccessDate,jdbcType=TIMESTAMP},
            </if>
            <if test="contractFilename != null">
                contract_filename = #{contractFilename,jdbcType=VARCHAR},
            </if>
            <if test="contractUrl != null">
                contract_url = #{contractUrl,jdbcType=VARCHAR},
            </if>
            <if test="sourceFileurl != null">
                source_fileurl = #{sourceFileurl,jdbcType=VARCHAR},
            </if>
            <if test="sourceFilename != null">
                source_filename = #{sourceFilename,jdbcType=VARCHAR},
            </if>
            <if test="caseNumber != null">
                case_number = #{caseNumber,jdbcType=BIGINT},
            </if>
            <if test="entrustMoneyTotal != null">
                entrust_money_total = #{entrustMoneyTotal,jdbcType=DECIMAL},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="acquisitionCosts != null">
                acquisition_costs = #{acquisitionCosts,jdbcType=DECIMAL},
            </if>
            <if test="targetAmount != null">
                target_amount = #{targetAmount,jdbcType=DECIMAL},
            </if>
            <if test="beginPeriod != null">
                begin_period = #{beginPeriod,jdbcType=DATE},
            </if>
            <if test="endPeriod != null">
                end_period = #{endPeriod,jdbcType=DATE},
            </if>
            <if test="rate != null">
                rate = #{rate,jdbcType=DECIMAL},
            </if>
            <if test="expectedRevenue != null">
                expected_revenue = #{expectedRevenue,jdbcType=DECIMAL},
            </if>
            <if test="acquisitionDate != null">
                acquisition_date = #{acquisitionDate,jdbcType=DATE},
            </if>
            <if test="closingDate != null">
                closing_date = #{closingDate,jdbcType=DATE},
            </if>
            <if test="productId != null">
                product_id = #{productId,jdbcType=BIGINT},
            </if>
            <if test="importStart != null">
                import_start = #{importStart,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId,jdbcType=BIGINT},
            </if>
            <if test="autoReduction != null">
                auto_reduction = #{autoReduction,jdbcType=CHAR},
            </if>
            <if test="reductionId != null">
                reduction_id = #{reductionId,jdbcType=BIGINT},
            </if>
            <if test="entrustMoneyId != null">
                entrust_money_id = #{entrustMoneyId,jdbcType=BIGINT},
            </if>
            <if test="creditorAnnouncementDate != null">
                creditor_announcement_date = #{creditorAnnouncementDate,jdbcType=DATE},
            </if>
            <if test="creditorAnnouncement != null">
                creditor_announcement = #{creditorAnnouncement,jdbcType=VARCHAR},
            </if>
            <if test="signingDate != null">
                signing_date = #{signingDate,jdbcType=DATE},
            </if>
            <if test="protocolNumber != null">
                protocol_number = #{protocolNumber,jdbcType=VARCHAR},
            </if>
            <if test="protocol != null">
                protocol = #{protocol,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="packageName != null">
                package_name = #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                open_account = #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="accountId != null">
                account_id = #{accountId,jdbcType=BIGINT},
            </if>
            <if test="offsetOrder != null">
                offset_order = #{offsetOrder,jdbcType=VARCHAR},
            </if>
            <if test="settleId != null">
                settle_id = #{settleId,jdbcType=VARCHAR},
            </if>
            <if test="settleName != null">
                settle_name = #{settleName,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.cis.domain.TeamManage">
        update team_asset_manage
        set batch_num                  = #{batchNum,jdbcType=VARCHAR},
            creditor_access_date       = #{creditorAccessDate,jdbcType=TIMESTAMP},
            contract_filename          = #{contractFilename,jdbcType=VARCHAR},
            contract_url               = #{contractUrl,jdbcType=VARCHAR},
            source_fileurl             = #{sourceFileurl,jdbcType=VARCHAR},
            source_filename            = #{sourceFilename,jdbcType=VARCHAR},
            case_number                = #{caseNumber,jdbcType=BIGINT},
            entrust_money_total        = #{entrustMoneyTotal,jdbcType=DECIMAL},
            owner_id                   = #{ownerId,jdbcType=BIGINT},
            acquisition_costs          = #{acquisitionCosts,jdbcType=DECIMAL},
            target_amount              = #{targetAmount,jdbcType=DECIMAL},
            begin_period               = #{beginPeriod,jdbcType=DATE},
            end_period                 = #{endPeriod,jdbcType=DATE},
            rate                       = #{rate,jdbcType=DECIMAL},
            expected_revenue           = #{expectedRevenue,jdbcType=DECIMAL},
            acquisition_date           = #{acquisitionDate,jdbcType=DATE},
            closing_date               = #{closingDate,jdbcType=DATE},
            product_id                 = #{productId,jdbcType=BIGINT},
            import_start               = #{importStart,jdbcType=VARCHAR},
            operator_id                = #{operatorId,jdbcType=BIGINT},
            auto_reduction             = #{autoReduction,jdbcType=CHAR},
            reduction_id               = #{reductionId,jdbcType=BIGINT},
            entrust_money_id           = #{entrustMoneyId,jdbcType=BIGINT},
            creditor_announcement_date = #{creditorAnnouncementDate,jdbcType=DATE},
            creditor_announcement      = #{creditorAnnouncement,jdbcType=VARCHAR},
            signing_date               = #{signingDate,jdbcType=DATE},
            protocol_number            = #{protocolNumber,jdbcType=VARCHAR},
            protocol                   = #{protocol,jdbcType=VARCHAR},
            create_by                  = #{createBy,jdbcType=VARCHAR},
            create_time                = #{createTime,jdbcType=TIMESTAMP},
            update_by                  = #{updateBy,jdbcType=VARCHAR},
            update_time                = #{updateTime,jdbcType=TIMESTAMP},
            del_flag                   = #{delFlag,jdbcType=CHAR},
            package_name               = #{packageName,jdbcType=VARCHAR},
            open_account               = #{openAccount,jdbcType=VARCHAR},
            account_id                 = #{accountId,jdbcType=BIGINT},
            offset_order               = #{offsetOrder,jdbcType=VARCHAR},
            settle_id                  = #{settleId,jdbcType=VARCHAR},
            settle_name                = #{settleName,jdbcType=VARCHAR},
            team_id                    = #{teamId,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateAssetManage" parameterType="com.zws.cis.domain.TeamManage">
        update team_asset_manage
        <set>
            <if test="batchNum != null">
                batch_num = #{batchNum,jdbcType=VARCHAR},
            </if>
            <if test="creditorAccessDate != null">
                creditor_access_date = #{creditorAccessDate,jdbcType=TIMESTAMP},
            </if>
            <if test="contractUrl != null">
                contract_url = #{contractUrl,jdbcType=VARCHAR},
            </if>
            <if test="caseNumber != null">
                case_number = #{caseNumber,jdbcType=BIGINT},
            </if>
            <if test="entrustMoneyTotal != null">
                entrust_money_total = #{entrustMoneyTotal,jdbcType=DECIMAL},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="productId != null">
                product_id = #{productId,jdbcType=BIGINT},
            </if>
            <if test="importStart != null">
                import_start = #{importStart,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                operator_id = #{operatorId,jdbcType=BIGINT},
            </if>
            <if test="autoReduction!=null">
                auto_reduction =#{autoReduction},
            </if>
            <if test="reductionId!=null">
                reduction_id =#{reductionId},
            </if>
            <if test="entrustMoneyId!=null">
                entrust_money_id =#{entrustMoneyId},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>

            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            acquisition_costs = #{acquisitionCosts,jdbcType=DECIMAL},
            target_amount = #{targetAmount,jdbcType=DECIMAL},
            begin_period = #{beginPeriod,jdbcType=TIMESTAMP},
            end_period = #{endPeriod,jdbcType=TIMESTAMP},
            rate = #{rate,jdbcType=DECIMAL},
            expected_revenue = #{expectedRevenue,jdbcType=DECIMAL},
            acquisition_date = #{acquisitionDate,jdbcType=TIMESTAMP},
            <if test="closingDate != null">
                closing_date = #{closingDate},
            </if>
            <if test="packageName != null">
                package_name = #{packageName,jdbcType=VARCHAR},
            </if>
            <if test="openAccount != null">
                open_account = #{openAccount,jdbcType=VARCHAR},
            </if>
            <if test="accountId != null">
                account_id = #{accountId,jdbcType=BIGINT},
            </if>
            <if test="creditorAnnouncementDate != null">
                creditor_announcement_date = #{creditorAnnouncementDate,jdbcType=TIMESTAMP},
            </if>
            <if test="creditorAnnouncement != null">
                creditor_announcement = #{creditorAnnouncement,jdbcType=VARCHAR},
            </if>
            <if test="signingDate != null">
                signing_date = #{signingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="protocolNumber != null">
                protocol_number = #{protocolNumber,jdbcType=VARCHAR},
            </if>
            <if test="creditorAnnouncement != null">
                protocol = #{protocol,jdbcType=VARCHAR},
            </if>

            <if test="settleId != null">
                settle_id = #{settleId,jdbcType=VARCHAR},
            </if>

            <if test="settleName != null">
                settle_name = #{settleName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
