<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.cis.mapper.CisSettingMapper">

    <resultMap id="StateTreeResult" type="com.zws.cis.pojo.StatePojo">
        <result property="createId" column="create_id"/>
        <result property="whitelistStatus" column="whitelist_status"/>
        <result property="informationStatus" column="information_status"/>
        <result property="restrictedState" column="restricted_state"/>
        <result property="settingStatus" column="setting_status"/>
        <result property="authorizationStatus" column="authorization_status"/>
        <result property="authenticationStatus" column="authentication_status"/>
        <result property="securityVerificationStatus" column="security_verification_status"/>
        <result property="exportSettingStatus" column="export_setting_status"/>
        <result property="pushAppStatus" column="push_app_status"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <resultMap id="DesensitizationTreeResult" type="com.zws.cis.pojo.DesensitizationPojo">
        <result property="createId" column="create_id"/>
        <result property="cardId" column="card_id"/>
        <result property="bankCard" column="bank_card"/>
        <result property="weChat" column="we_chat"/>
        <result property="unitAddress" column="unit_address"/>
        <result property="residentialAddress" column="residential_address"/>
        <result property="homeAddress" column="home_address"/>
        <result property="entityName" column="entity_name"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="deleteLogo" column="delete_logo"/>
    </resultMap>

    <resultMap id="WatermarkTreeResult" type="com.zws.cis.pojo.WatermarkPojo">
        <result property="createId" column="create_id"/>
        <result property="watermarkOne" column="watermark_one"/>
        <result property="watermarkTwo" column="watermark_two"/>
        <result property="watermarkThree" column="watermark_three"/>
        <result property="watermarkFour" column="watermark_four"/>
    </resultMap>

    <resultMap id="TeamExportResult" type="com.zws.cis.pojo.TeamExportPojo">
        <result property="createId" column="create_id"/>
        <result property="exportStatus" column="export_status"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="creationtime" column="creation_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="modifier" column="modifier"/>
        <result property="founder" column="founder"/>

        <result property="perms" column="perms"/>
        <result property="menuName" column="menu_name"/>
        <result property="buttonName" column="button_name"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <select id="selectState" resultType="com.zws.cis.pojo.StatePojo" resultMap="StateTreeResult">
        select id,
               create_id,
               whitelist_status,
               information_status,
               restricted_state,
               setting_status,
               authorization_status,
               authentication_status,
               security_verification_status,
               export_setting_status,
               push_app_status,
               modifier,
               modify_time
        from team_states
        where create_id = #{createId}
    </select>

    <select id="selectDesensitization" resultType="com.zws.cis.pojo.DesensitizationPojo"
            resultMap="DesensitizationTreeResult">
        select id,
               create_id,
               dname,
               numbers,
               card_id,
               bank_card,
               qq,
               we_chat,
               households,
               unit_address,
               residential_address,
               home_address,
               entity_name,
               founder,
               creationtime,
               modifier,
               modify_time
        from team_desensitization
        where delete_logo = 0
          and create_id = #{createId}
    </select>

    <select id="selectWatermark" resultType="com.zws.cis.pojo.WatermarkPojo" resultMap="WatermarkTreeResult">
        select id,
               create_id,
               watermark_one,
               watermark_two,
               watermark_three,
               watermark_four
        from team_watermark
        where create_id = #{createId}
    </select>

    <update id="updateStates" parameterType="com.zws.cis.pojo.StatePojo">
        update team_states
        set whitelist_status=#{whitelistStatus},
            information_status=#{informationStatus},
            restricted_state=#{restrictedState},
            setting_status=#{settingStatus},
            authorization_status=#{authorizationStatus},
            authentication_status=#{authenticationStatus},
            security_verification_status = #{securityVerificationStatus},
            export_setting_status=#{exportSettingStatus},
            push_app_status=#{pushAppStatus},
            modifier=#{modifier},
            modify_time=#{modifyTime}
        where create_id = #{createId}
          and id = #{id}
    </update>

    <update id="updateDesensitization" parameterType="com.zws.cis.pojo.DesensitizationPojo">
        update team_desensitization
        <trim prefix="SET" suffixOverrides=",">
            <if test="dname != null">dname = #{dname},</if>
            <if test="numbers != null">numbers = #{numbers},</if>
            <if test="cardId != null">card_id = #{cardId},</if>
            <if test="bankCard != null">bank_card = #{bankCard},</if>
            <if test="qq != null">qq = #{qq},</if>
            <if test="weChat != null">we_chat = #{weChat},</if>
            <if test="households != null">households = #{households},</if>
            <if test="unitAddress != null">unit_address = #{unitAddress},</if>
            <if test="residentialAddress != null">residential_address = #{residentialAddress},</if>
            <if test="homeAddress != null">home_address = #{homeAddress},</if>
            <if test="entityName != null">entity_name = #{entityName},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where create_id=#{createId}
    </update>

    <update id="updateWatermark" parameterType="com.zws.cis.pojo.WatermarkPojo">
        update team_watermark
        <trim prefix="SET" suffixOverrides=",">
            <if test="watermarkOne != null">watermark_one = #{watermarkOne},</if>
            <if test="watermarkTwo != null">watermark_two = #{watermarkTwo},</if>
            <if test="watermarkThree != null">watermark_three = #{watermarkThree},</if>
            <if test="watermarkFour != null">watermark_four = #{watermarkFour}</if>
        </trim>
        where create_id=#{createId}
    </update>

    <select id="selectTeamExport" resultType="com.zws.cis.pojo.TeamExportPojo" resultMap="TeamExportResult">
        select id,
               create_id,
               export_status,
               modify_time,
               modifier,
               creation_time,
               founder,
               perms,
               menu_name,
               button_name,
               remark
        from team_export
        where create_id = #{createId}
          and delete_logo = 0
    </select>

    <insert id="insertTeamExport" parameterType="com.zws.cis.pojo.TeamExportPojo" useGeneratedKeys="true"
            keyProperty="id">
        insert into team_export(create_id, export_status, founder, creation_time, delete_logo,
                                perms, menu_name, button_name, remark)
        values (#{createId}, #{exportStatus}, #{founder}, #{creationtime}, #{deleteLogo},
                #{perms}, #{menuName}, #{buttonName}, #{remark})
    </insert>

    <select id="getTeamExportById" resultMap="TeamExportResult">
        select id,
               create_id,
               export_status,
               modify_time,
               modifier,
               creation_time,
               founder,
               perms,
               menu_name,
               button_name,
               remark
        from team_export
        where id = #{id}
    </select>

    <update id="updateTeamExport" parameterType="com.zws.cis.pojo.TeamExportPojo">
        update team_export
        <trim prefix="SET" suffixOverrides=",">
            <if test="exportStatus != null">export_status = #{exportStatus},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where id = #{id} and create_id = #{createId}
    </update>
</mapper>
