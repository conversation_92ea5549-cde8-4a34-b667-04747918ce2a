server:
  port: 9205

spring:
  application:
    # 应用名称
    name: zws-cis
  profiles:
    # 环境配置
    active: @active@
  #    active: test
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB

  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: @nacos.server-addr@
        namespace: @nacos.namespace@
        #        server-addr: localhost:8848
        username: nacos    # 新增项，Nacos账号
        password: zwsCX#Nacos    # 新增项，Nacos密码
      config:
        # 配置中心地址
        server-addr: @nacos.server-addr@
        namespace: @nacos.namespace@
        #        server-addr: localhost:8848
        username: nacos    # 新增项，Nacos账号
        password: zwsCX#Nacos    # 新增项，Nacos密码
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}


