package com.zws.system.api.factory;

import com.zws.common.core.constant.Constants;
import com.zws.common.core.domain.R;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.system.api.RemoteMessageService;
import com.zws.system.api.domain.Notice;
import com.zws.system.api.domain.TeamMessageCenter;
import com.zws.system.api.domain.TeamUserMessage;
import com.zws.system.api.domain.UserMessage;
import com.zws.system.api.domain.dto.MessageDeleteDto;
import com.zws.system.api.pojo.CallMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/5/31 18:10
 */
@Component
public class RemoteMessageFallbackFactory implements FallbackFactory<RemoteMessageService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteMessageFallbackFactory.class);

    @Override
    public RemoteMessageService create(Throwable throwable) {
        log.error("消息中心服务调用失败:{}", throwable.getMessage());
        return new RemoteMessageService()
        {
            @Override
            public R<Long> createMessageCenter(TeamMessageCenter teamMessageCenter, String source) {
                return R.fail("创建消息失败:" + throwable.getMessage());
            }

            @Override
            public R publishTeamNotice(Long noticeId) {
                return R.fail("发布公告失败失败:" + throwable.getMessage());
            }

            @Override
            public R pussTeamUserMessage(List<TeamUserMessage> teamUserMessages, String source) {
                return R.fail("调用消息中心失败:" + throwable.getMessage());
            }

            @Override
            public R pushAssetUserMessage(List<UserMessage> userMessages, String source) {
                return R.fail("调用资产端业务进度提醒失败:" + throwable.getMessage());
            }

            @Override
            public R<Long> createNotice(Notice notice, String source) {
                return R.fail("创建系统消息失败:" + throwable.getMessage());
            }

            @Override
            public TableDataInfo getList(TeamUserMessage param, Integer pageNum, Integer pageSize) {
                TableDataInfo tableDataInfo=new TableDataInfo();
                tableDataInfo.setCode(Constants.FAIL);
                tableDataInfo.setMsg("获取消息列表失败:" + throwable.getMessage());
                return tableDataInfo;
            }

            @Override
            public R publishAssetNotice(Long noticeId) {
                return R.fail("发布公告失败失败:" + throwable.getMessage());
            }

            @Override
            public R publish(Long noticeId) {
                return R.fail("系统消息发布失败:" + throwable.getMessage());
            }

            @Override
            public R pushAssetUserMessage(UserMessage userMessage, String source) {
                return R.fail("调用消息中心失败:" + throwable.getMessage());
            }

            @Override
            public R pushCallEnd(CallMessage callMessage, String source) {
                return R.fail("调用消息中心失败:" + throwable.getMessage());
            }

            @Override
            public R pushOnCall(CallMessage callMessage, String source) {
                return R.fail("调用消息中心失败:" + throwable.getMessage());
            }

            @Override
            public R pushCallIng(CallMessage callMessage, String source) {
                return R.fail("调用消息中心失败:" + throwable.getMessage());
            }

            @Override
            public R pushCallPopUpScreen(CallMessage callMessage, String source) {
                return R.fail("调用消息中心失败:" + throwable.getMessage());
            }

            @Override
            public R pushCaseCallPopUpScreen(CallMessage callMessage, String source) {
                return R.fail("调用消息中心失败:" + throwable.getMessage());
            }

            @Override
            public R pushWorkPhoneCaseCallPopUpScreen(String userId, String phone) {
                return R.fail("调用消息中心失败:" + throwable.getMessage());
            }

            @Override
            public R insertUserMessage(List<TeamUserMessage> teamUserMessages, String source) {
                return R.fail("添加用户消息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<TeamUserMessage>> getNavMessage() {
                return R.fail("获取导航信息失败:" + throwable.getMessage());
            }

            @Override
            public R markRead(List<Long> ids, String source) {
                return R.fail("信息标记已读失败:" + throwable.getMessage());
            }

            @Override
            public R delete(List<Long> ids, String source) {
                return R.fail("删除信息失败:" + throwable.getMessage());
            }

            /**
             * 删除信息
             *
             * @param messageDeleteDto
             * @param source
             * @return
             */
            @Override
            public R delete(MessageDeleteDto messageDeleteDto, String source) {
                return R.fail("删除信息失败:" + throwable.getMessage());
            }

            @Override
            public R<TeamMessageCenter> getUserNotice(Long id, String source) {
                return R.fail("获取用户信息-系统公告详情失败:" + throwable.getMessage());
            }

            @Override
            public R<Integer> getUnreadQuantity() {
                return R.fail("获取未读信息数量失败:" + throwable.getMessage());
            }

        };
    }
}
