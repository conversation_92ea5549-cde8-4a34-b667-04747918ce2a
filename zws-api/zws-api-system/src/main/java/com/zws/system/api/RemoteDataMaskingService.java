package com.zws.system.api;

import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.system.api.factory.RemoteDataMaskingFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 脱敏信息查询服务
 *
 * <AUTHOR>
 * @date 2024/1/19 11:37
 */
@FeignClient(contextId = "remoteDataMaskingService", value = ServiceNameConstants.ZWS_DATAMASK, fallbackFactory = RemoteDataMaskingFallbackFactory.class)
public interface RemoteDataMaskingService {
    /**
     * 获取催收端脱敏开关是否开启
     *
     * @param param
     * @return
     */
    @GetMapping("/setup/legal/queryDataMaskingInfo")
    R<Boolean> autoReduction(@RequestParam("param") String param);
}
