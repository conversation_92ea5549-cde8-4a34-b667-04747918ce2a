package com.zws.system.api;

import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.SmsPhoneAndMsg;
import com.zws.common.core.domain.TimeManage;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.domain.*;
import com.zws.system.api.factory.RemoteCaseFallbackFactory;
import com.zws.system.api.pojo.YcCenterLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 案件相关远程服务
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/7 14:36
 */
@FeignClient(contextId = "remoteCaseService", value = ServiceNameConstants.CASE_SERVICE, fallbackFactory = RemoteCaseFallbackFactory.class)
public interface RemoteCaseService {

    /**
     * 案件到期自动回收案件
     *
     * @return
     */
    @PostMapping("/case/manage/autoReclaimCase")
    R<Long> autoReclaimCase();

    /**
     * 获取实名认证开关
     * //0 开启，1 关闭
     *
     * @return
     */
    @GetMapping("/setup/legal/getVerifiedSwitch")
    R<Map<String, Object>> getVerifiedSwitch();

    /**
     * 获取资产端第一位审核人员
     *
     * @param approveCode 审核code 0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
     * @return
     */
    @GetMapping("/approve/setup/findFirstApprove")
    R<Long> findFirstApprove(@RequestParam("approveCode") Integer approveCode);

    /**
     * 提交减免
     *
     * @param recordIds
     * @return
     */
    @PostMapping("/approve/reduction/auto")
    R autoReduction(@RequestBody List<Long> recordIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 执行录音下载任务
     *
     * @param taskId
     * @param source
     * @return
     */
    @PostMapping("/call/downloadTask/executeWorkTask")
    R executeWorkTask(@RequestParam("taskId") Long taskId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 写入结佣数据--（催收端用）
     *
     * @param insert     佣金表主键id
     * @param teamId     团队id
     * @param monthBegin 上个月月初
     * @param monthEnd   上个月月末
     * @param detailsId  上个月月末
     * @param source     佣金详情表id
     * @return
     */
    @PostMapping("/financial/teamBrokerage/calculateTeamCommission")
    R calculateTeamCommission(@RequestParam("insert") Long insert, @RequestParam("teamId") Long teamId, @RequestParam("monthBegin") Date monthBegin, @RequestParam("monthEnd") Date monthEnd, @RequestParam("detailsId") Long detailsId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 验证码发送短信
     *
     * @param smsPhoneAndMsg
     * @param source
     * @return
     */
    @PostMapping("/verification/sendSms")
    R sendSmsCommission(@RequestBody SmsPhoneAndMsg smsPhoneAndMsg, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 中央认证入参信息日志写入
     *
     * @param ycCenterLog
     * @param source
     * @return
     */
    @PostMapping("/centerLog/insertYcCenterLog")
    R insertYcCenterLog(@RequestBody YcCenterLog ycCenterLog, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询短信标签下拉框
     */
    @GetMapping("/management/selectAllSmsSignature")
    R selectAllSmsSignature();

    /**
     * 查询短信模板下拉框
     */
    @GetMapping("/management/selectAllSmsTemplate")
    R selectAllSmsTemplate();

    /**
     * 查询短信模板下拉框
     */
    @PostMapping("/management/sendCuiShouMessage")
    R sendCuiShouMessage(@RequestParam("phone") String phone, @RequestParam("autographName") String autographName, @RequestParam("templateId") Long templateId, @RequestParam("smsType") Integer smsType, @RequestParam("username") String username, @RequestParam("userId") Long userId, @RequestParam("caseId") Long caseId, @RequestParam("clientName") String clientName, @RequestParam("overdueDays") Long overdueDays, @RequestParam("remainingDue") BigDecimal remainingDue);

    /**
     * 获取案件详情
     */
    @GetMapping("/case/library/getCaseDetails")
    R<CaseDetails> getCaseDetails(@RequestParam("caseId") Long caseId);

    /**
     * 写进案件跟进记录表
     *
     * @param timeManage
     * @param source
     * @return
     */
    @PostMapping("/time/manage/insertTimeRecord")
    R insertTimeRecord(@RequestBody TimeManage timeManage, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 新增签章审批记录
     */
    @GetMapping("/approve/case/sign/insert")
    R<Long> addCaseSignRecord(@RequestBody CaseSignRecord caseSignRecord);

    /**
     * 获取文件在线预览服务地址
     *
     * @return
     */
    @GetMapping("/management/getFileOnlinePreviewHost")
    AjaxResult getFileOnlinePreviewHost();

    /**
     * 获取对应机构的短信模板列表
     *
     * @param createId 当前用户所属机构id
     * @return
     */
    @GetMapping("/management/selectSmsListByCreateId")
    public AjaxResult selectSmsListByCreateId(@RequestParam("createId") Integer createId);

    /**
     * 通过用户id获取用户资产批次号
     *
     * @param userId
     * @return
     */
    @GetMapping("/investorConfig/info")
    R<List<String>> selectAssetBatchNums(@RequestParam("userId") Long userId);

    @DeleteMapping("/investorConfig/remove")
    R deleteByUserId(@RequestParam("userId") Long userId);

    /**
     * 内部接口-重新计算案件金额
     *
     * @return
     */
    @PostMapping("/inner/api/recalculateOffsetByCaseId")
    R innerRecalculateOffsetByCaseId(@RequestBody InnerRecalculate innerRecalculate);


    /**
     * 合作关闭/黑名单-案件回收
     *
     * @param teamIds
     * @return
     */
    @PostMapping("/case/manage/caseRecovery")
    R<Long> caseRecovery(@RequestBody List<Long> teamIds);

    /**
     * 新增团队费用管理
     */
    @GetMapping("/call/cost/add")
    R<Long> addCost(@RequestParam("teamId") Long teamId,  @RequestParam("cname") String cname);
    /**
     * 获取案件标签信息列表
     */
    @GetMapping("/setup/legal/getLabel")
    R<List<Legal>> getLabel();

    /**
     * 获取案件标签下拉框
     */
    @GetMapping("/case/start/getLabels")
    R<List<StatePojo>> getLabels();
}
