package com.zws.system.api.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * 基本信息实体类
 *
 * <AUTHOR>
 * @since 2024-04-10 17:32:05
 */
@Data
public class CaseInfoBaseVo implements Serializable {
    private Long id ;
    /**
     * 姓名(借款人)
     */
    private String clientName;

    /**
     * 身份证号(证件号码)
     */
    private String clientIdNum;
    /**
     * 学历
     */
    private String education;
    /**
     * 学位
     */
    private String academicDegree;
    /**
     * 就业状态
     */
    private String employmentStatus;
    /**
     * 居住状态
     */
    private String residentialStatus;
    /**
     * 职务
     */
    private String duties;
    /**
     * 职称
     */
    private String title;
    /**
     * 职业
     */
    private String occupation;
    /**
     * 单位所属行业
     */
    private String unitIndustry;
    /**
     * 户籍地址
     */
    private String registeredAddress;
    /**
     * 户籍地划分显示
     */
    private String hhDivide;
    /**
     * 户籍所在地行政区划
     */
    private String hhDist;
    /**
     * 居住所在地行政区划
     */
    private String resiDist;
    /**
     * 居住地划分显示
     */
    private String resiDivide;
    /**
     * 婚姻状况
     */
    private String maritalStatus;
    /**
     * 工作单位
     */
    private String placeOfWork;
    /**
     * 居住地址
     */
    private String residentialAddress;
    /**
     * 邮箱
     */
    private String mailbox;
    /**
     * 联系人姓名
     */
    private String contactName;
    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人身份证
     */
    private String contactIdNum;
    /**
     * 联系人工作单位
     */
    private String contactPlaceOfWork;



}
