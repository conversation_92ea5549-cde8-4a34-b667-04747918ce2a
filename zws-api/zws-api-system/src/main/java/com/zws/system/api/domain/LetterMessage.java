package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zws.system.api.model.LoginUser;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 律函批次
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-04 20:38
 */
@Getter
@Setter
@Accessors(chain = true)
public class LetterMessage {
    private LoginUser loginUser;

    /**
     * 当前记录起始索引
     */
    private Integer pageNum;

    /**
     * 每页显示记录数
     */
    private Integer pageSize;

    /**
     * 律函id
     */
    private Integer id;

    /**
     * 发函批次号
     */
    @NotBlank(message = "发函批次号不能为空")
    private String batchNum;

    /**
     * 模板id
     */
    @NotNull(message = "请选择模板")
    private Integer templateId;

    /**
     * 签章状态，0-失败，1-进行中，2-完成
     */
    private Integer status;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 函件量
     */
    private Integer quantity;

    /**
     * 审核进程 0-待审核，1-审核中，2-已通过，3未通过
     */
    private Integer proce;

    /**
     * 审核顺序
     */
    private Integer proceSort;

    /**
     * 原文件地址
     */
    private String sourceFileUrl;

    /**
     * 寄送方式
     * 初始默认：电子版本、邮寄版本(数据字典获取)
     */
    private String deliveryWay;

    /**
     * 审核之后的压缩包文件地址
     */
    @JsonIgnore
    private String zipFileUrl;
    /**
     * 未审核的压缩包文件地址
     */
    @JsonIgnore
    private String notZipFileUrl;
    /***
     * 0:Excel类型  1:Zip类型
     * 批量导入的源文件类型
     */
    private Integer sourceFileType;


    @JsonIgnore
    private Integer tenantId;

    private String createBy;

    @JsonSerialize(using = ToStringSerializer.class)
    private Integer createById;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;

    @JsonSerialize(using = ToStringSerializer.class)
    private Integer updateById;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String delFlag;


    /**
     * 模板名称
     * 查询参数、返回
     */
    private String templateName;
    /**
     * 模板类型
     * 查询参数、返回
     */
    private String classifyName;
    /**
     * 模板类型id
     * 返回
     */
    private String classifyLabel;
    /**
     * 查询参数-模板类型
     */
    @JsonIgnore
    private String classifyId;

    /**
     * 查询参数-创建时间-开始
     */
    @JsonIgnore
    private String createTime1;
    /**
     * 查询参数-创建时间-截止
     */
    @JsonIgnore
    private String createTime2;

    /**
     * 查询参数-处理时间-开始
     */
    @JsonIgnore
    private Date updateTime1;
    /**
     * 查询参数-处理时间-截止
     */
    @JsonIgnore
    private Date updateTime2;


    /**
     * 校验文件id
     * 传值参数
     */
    //@JsonIgnore
    private String verifyId;

    /**
     * 审核进程集合，审核进程 0-待审核，1-审核中，2-已结束
     * 查询参数
     */
    private List<Integer> proceList;

    /**
     * 律函批次id集合
     * 查询参数
     */
    //@JsonIgnore
    private List<Integer> ids;

    /**
     * 拒绝理由
     * 传值参数
     */
    private String refuseReason;

    /**
     * 查询参数-模板类型
     */
    private List<String> classifyIds;

    /**
     * 导入状态：0导入中、1导入失败、2导入成功
     */
    private Integer importStatus;

    /**
     * 是否催收端发起审批
     */
    private boolean disposePort;

    /**
     * 审核状态
     */
    private String examineState;


    @Override
    public String toString() {
        return "LetterMessage{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", id=" + id +
                ", batchNum='" + batchNum + '\'' +
                ", templateId=" + templateId +
                ", status=" + status +
                ", quantity=" + quantity +
                ", proce=" + proce +
                ", proceSort=" + proceSort +
                ", sourceFileUrl='" + sourceFileUrl + '\'' +
                ", zipFileUrl='" + zipFileUrl + '\'' +
                ", sourceFileType=" + sourceFileType +
                ", tenantId=" + tenantId +
                ", createBy='" + createBy + '\'' +
                ", createById=" + createById +
                ", createTime=" + createTime +
                ", updateBy='" + updateBy + '\'' +
                ", updateById=" + updateById +
                ", updateTime=" + updateTime +
                ", delFlag='" + delFlag + '\'' +
                ", templateName='" + templateName + '\'' +
                ", classifyName='" + classifyName + '\'' +
                ", classifyLabel='" + classifyLabel + '\'' +
                ", classifyId='" + classifyId + '\'' +
                ", createTime1=" + createTime1 +
                ", createTime2=" + createTime2 +
                ", updateTime1=" + updateTime1 +
                ", updateTime2=" + updateTime2 +
                ", verifyId='" + verifyId + '\'' +
                ", proceList=" + proceList +
                ", ids=" + ids +
                ", refuseReason='" + refuseReason + '\'' +
                ", classifyIds=" + classifyIds +
                ", importStatus=" + importStatus +
                '}';
    }
}
