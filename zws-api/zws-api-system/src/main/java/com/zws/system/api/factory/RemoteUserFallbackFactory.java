package com.zws.system.api.factory;

import com.zws.system.api.RemoteUserService;
import com.zws.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import com.zws.common.core.domain.R;
import com.zws.system.api.domain.SysUser;

import java.util.List;

/**
 * 用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public RemoteUserService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserService()
        {
            @Override
            public R<LoginUser> getUserInfo(String username, String source)
            {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> registerUserInfo(SysUser sysUser, String source)
            {
                return R.fail("注册用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> updateUserLogin(SysUser sysUser, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> getUserFirstLogin(Long userId, String source) {
                return R.ok(false);
            }

            @Override
            public R<List<SysUser>> selectUserByRole(Long roleId, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<SysUser> getUserById(Long uesrId) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<Long>> selectListByInvestorAll() {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

        };
    }
}
