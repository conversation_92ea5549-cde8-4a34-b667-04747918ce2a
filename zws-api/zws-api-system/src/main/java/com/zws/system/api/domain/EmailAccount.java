package com.zws.system.api.domain;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/4/29 下午7:24
 */
@Data
public class EmailAccount {
    private Long id;
    private String emailName;
    private String smtpHost;
    private String smtpPort;
    private String imapHost;
    private String imapPort;
    private String mailbox;
    private String password;
    /**
     * 每半小时发送上限
     */
    private int halfHourNum;

    /**
     * 每日发送上限
     */
    private int dayNum;
}
