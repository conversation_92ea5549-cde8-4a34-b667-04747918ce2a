package com.zws.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 案件信息-附加信息
 */
@Data
public class InfoExtra extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 所属机构ID
     */
    private Long teamId;
    /**
     * 案件ID
     */
    private Long caseId;
    /**
     * 附加信息名称
     */
    private String extraName;
    /**
     * 附加信息值
     */
    private String extraValue;

    private String delFlag;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCaseId() {
        return caseId;
    }

    public void setCaseId(Long caseId) {
        this.caseId = caseId;
    }

    public String getExtraName() {
        return extraName;
    }

    public void setExtraName(String extraName) {
        this.extraName = extraName == null ? null : extraName.trim();
    }

    public String getExtraValue() {
        return extraValue;
    }

    public void setExtraValue(String extraValue) {
        this.extraValue = extraValue == null ? null : extraValue.trim();
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }

    public boolean check(){
        if(this.caseId==null) {
            throw new GlobalException("案件ID不能为空");
        }
        return true;
    }

}
