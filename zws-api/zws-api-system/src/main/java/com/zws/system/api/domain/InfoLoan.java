package com.zws.system.api.domain;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 案件信息-贷款信息
 */
@Data
public class InfoLoan extends BaseEntity {

    private Long id;
    /**
     * 案件ID
     */
    private Long caseId;
    /**
     * 资产管理id
     */
    private Long assetManageId;
    /**
     * 此属性已废弃
     */
    @Deprecated
    private Long caseManageId;
    /**
     * 合同编号(借据号)
     */
    private String contractNo;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 产品名(产品类型)
     */
    private String productName;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 贷款金额
     */
    private BigDecimal loanMoney;
    /**
     * 逾期开始时间（逾期日期）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date overdueStart;
    /**
     * 案件区域
     */
    private String caseRegion;

    /**
     * 账期
     */
    private String accountPeriod;
    /**
     * 账期数字部分
     */
    private int accountPeriodNum;

    /**
     * 贷款机构
     */
    private String loanInstitution;
    /**
     * 贷款期数
     */
    private Integer loanPeriods;
    /**
     * 已还期数
     */
    private Integer alreadyPeriods;
    /**
     * 未还期数
     */
    private Integer notPeriods;
    /**
     * 委托金额（初始债权总额）
     */
    private BigDecimal entrustMoney;
    /**
     * 贷款本金
     */
    private BigDecimal loanPrincipal;
    /**
     * 滞纳金
     */
    private BigDecimal lateFee;
    /**
     * 服务费（初始费用）
     */
    private BigDecimal serviceFee;
    /**
     * 剩余本金(初始债权本金)
     */
    private BigDecimal residualPrincipal;
    /**
     * 利息（初始利息余额）
     */
    private BigDecimal interestMoney;
    /**
     * 担保费（初始担保费余额）
     */
    private BigDecimal interestGuarantee;
    /**
     * 咨询费（初始咨询费余额）
     */
    private BigDecimal interestAdvice;

    /**
     * 罚息
     */
    private BigDecimal interestPenalty;
    /**
     * 实际到账金额-到手金额
     */
    private BigDecimal actualAmountReceived;
    /**
     * 逾期保费
     */
    private BigDecimal overduePremium;
    /**
     * 还款日（每月还款日）
     */
    //@JsonFormat(pattern = "yyyy-MM-dd")
    //private Date repaymentDate;
    private Integer repaymentDate; //廖智涛 2022年9月29日16:44:16 每月还款日由日期改为 1-31 的数字
    /**
     * 每月还款金额（每月应还）
     */
    private BigDecimal repaymentMonthly;
    /**
     * 扣除后金额-减免后金额
     */
    private BigDecimal amountAfterDeduction;
    /**
     * 已催回金额
     */
    private BigDecimal amountCalledBack;
    /**
     * 应还金额
     */
    private BigDecimal amountDue;
    /**
     * 剩余应还（剩余应还债权总额）
     */
    private BigDecimal remainingDue;
    /**
     * 借款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date loanDate;
    /**
     * 最后还款金额
     */
    private BigDecimal amountFinalRepayment;
    /**
     * 最后还款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date amountFinalDate;

    /**
     * 首次还款金额
     */
    private BigDecimal amountFirstRepayment;
    /**
     * 首次还款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date amountFirstDate;
    /**
     * 最后跟进时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date latestFollowUpTime;
    /**
     * 委案日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrustingCaseDate;
    /**
     * 退案日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date returnCaseDate;
    /**
     * 其他费用
     */
    private BigDecimal otherFees;
    /**
     * 其他费用备注
     */
    private String otherFeesRemarks;

    private String delFlag;


    //yc_contract_no,yc_five_level,yc_currencies,yc_purpose,yc_lending_rate,yc_repayment_method,yc_interest_balance,
    //yc_disbursement,yc_litigation_status,yc_is_dishonest,yc_is_limit_consumption
    /**
     * 逾期天数
     */
    private Integer ycOverdueDays;

    /**
     * 合同号
     */
    private String ycContractNo;
    /**
     * 五级分类
     */
    private String ycFiveLevel;
    /**
     * 币种
     */
    private String ycCurrencies;
    /**
     * 贷款意图
     */
    private String ycPurpose;
    /**
     * 贷款利率
     */
    private BigDecimal ycLendingRate;
    /**
     * 还款方式
     */
    private String ycRepaymentMethod;
    /**
     * 初始本息余额
     */
    private BigDecimal ycInterestBalance;
    /**
     * 垫付费用
     */
    private BigDecimal ycDisbursement;
    /**
     * 诉讼状态
     */
    private String ycLitigationStatus;
    /**
     * 是否失信被执行人
     * 0-否，1-是
     */
    private String ycIsDishonest;
    /**
     * 是否被限制高消费，0-否，1-是
     * 0-否，1-是
     */
    private String ycIsLimitConsumption;

    /**
     * 剩余应还本金
     */
    private BigDecimal syYhPrincipal;
    /**
     * 剩余应还利息
     */
    private BigDecimal syYhInterest;
    /**
     * 剩余应还费用
     */
    private BigDecimal syYhFees;

    /**
     * 剩余应还罚息
     */
    private BigDecimal syYhDefault;

    /**
     * 放款分(支)行
     */
    private String ycLoanBank;
    /**
     * 业务类型
     */
    private String ycBusinessType;
    /**
     * 合同金额
     */
    private BigDecimal ycContractMoney;
    /**
     * 贷款期限
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ycLoanTerm;
    /**
     * 贷款发放日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ycLoanIssuanceDate;
    /**
     * 贷款到期日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ycLoanMaturityDate;
    /**
     * 基准日后还款金额
     */
    private BigDecimal ycAbdRepayment;
    /**
     * 基准日后本金还款
     */
    private BigDecimal ycAbdPrincipal;
    /**
     * 基准日后利息还款
     */
    private BigDecimal ycAbdInterest;

    /**
     * 核销日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ycWriteDate;

    /**
     * 结清日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clearDate;

    /**
     * 罚息利率
     */
    private BigDecimal ycDefaultRate;


    /**
     * 产品简称
     *  产品code值
     */
    private String productShortName;

    /**
     * 逾期期次 逗号拼接(众利)
     */
    private String numberOfPeriods;

    /**
     * 基准日逾期天数
     */
    private Integer baseOverdueDays;

    /**
     * 发卡日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date cardIssuanceDate;

    /**
     * 账单日
     */
    private Integer statementDate;

    /**
     * 首次逾期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date firstOverdueDate;

    /**
     * 基准日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date baseDate;

    /**
     * 统计时点信用额度
     */
    private BigDecimal creditLimit;

    /**
     * 本息费合计
     */
    private BigDecimal principalInterestTotal;

    /**
     * 息费合计
     */
    private BigDecimal interestFeesTotal;

    /**
     * 银行卡号（国瑞暂用字段）
     */
    private String bankCardNumber;

    /**
     * 开户行-银行名称（国瑞暂用字段）
     */
    private String bankName;


    public String getNumberOfPeriods() {
        return numberOfPeriods;
    }

    public void setNumberOfPeriods(String numberOfPeriods) {
        this.numberOfPeriods = numberOfPeriods;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCaseId() {
        return caseId;
    }

    public void setCaseId(Long caseId) {
        this.caseId = caseId;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo == null ? null : contractNo.trim();
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType == null ? null : productType.trim();
    }

    public BigDecimal getLoanMoney() {
        return loanMoney;
    }

    public void setLoanMoney(BigDecimal loanMoney) {
        this.loanMoney = loanMoney;
    }

    public Date getOverdueStart() {
        return overdueStart;
    }

    public void setOverdueStart(Date overdueStart) {
        this.overdueStart = overdueStart;
    }

    public String getAccountPeriod() {
        return accountPeriod;
    }

    public void setAccountPeriod(String accountPeriod) {
        this.accountPeriod = accountPeriod == null ? null : accountPeriod.trim();
    }

    public String getLoanInstitution() {
        return loanInstitution;
    }

    public void setLoanInstitution(String loanInstitution) {
        this.loanInstitution = loanInstitution == null ? null : loanInstitution.trim();
    }

    public Integer getLoanPeriods() {
        return loanPeriods;
    }

    public void setLoanPeriods(Integer loanPeriods) {
        this.loanPeriods = loanPeriods;
    }

    public Integer getAlreadyPeriods() {
        return alreadyPeriods;
    }

    public void setAlreadyPeriods(Integer alreadyPeriods) {
        this.alreadyPeriods = alreadyPeriods;
    }

    public Integer getNotPeriods() {
        return notPeriods;
    }

    public void setNotPeriods(Integer notPeriods) {
        this.notPeriods = notPeriods;
    }

    public BigDecimal getEntrustMoney() {
        return entrustMoney;
    }

    public void setEntrustMoney(BigDecimal entrustMoney) {
        this.entrustMoney = entrustMoney;
    }

    public BigDecimal getLoanPrincipal() {
        return loanPrincipal;
    }

    public void setLoanPrincipal(BigDecimal loanPrincipal) {
        this.loanPrincipal = loanPrincipal;
    }

    public BigDecimal getLateFee() {
        return lateFee;
    }

    public void setLateFee(BigDecimal lateFee) {
        this.lateFee = lateFee;
    }

    public BigDecimal getServiceFee() {
        return serviceFee;
    }

    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }

    public BigDecimal getResidualPrincipal() {
        return residualPrincipal;
    }

    public void setResidualPrincipal(BigDecimal residualPrincipal) {
        this.residualPrincipal = residualPrincipal;
    }

    public BigDecimal getInterestMoney() {
        return interestMoney;
    }

    public void setInterestMoney(BigDecimal interestMoney) {
        this.interestMoney = interestMoney;
    }

    public BigDecimal getInterestPenalty() {
        return interestPenalty;
    }

    public void setInterestPenalty(BigDecimal interestPenalty) {
        this.interestPenalty = interestPenalty;
    }

    public BigDecimal getActualAmountReceived() {
        return actualAmountReceived;
    }

    public void setActualAmountReceived(BigDecimal actualAmountReceived) {
        this.actualAmountReceived = actualAmountReceived;
    }

    public BigDecimal getOverduePremium() {
        return overduePremium;
    }

    public void setOverduePremium(BigDecimal overduePremium) {
        this.overduePremium = overduePremium;
    }

    public Integer getRepaymentDate() {
        return repaymentDate;
    }

    public void setRepaymentDate(Integer repaymentDate) {
        this.repaymentDate = repaymentDate;
    }

    public BigDecimal getRepaymentMonthly() {
        return repaymentMonthly;
    }

    public void setRepaymentMonthly(BigDecimal repaymentMonthly) {
        this.repaymentMonthly = repaymentMonthly;
    }

    public BigDecimal getAmountAfterDeduction() {
        return amountAfterDeduction;
    }

    public void setAmountAfterDeduction(BigDecimal amountAfterDeduction) {
        this.amountAfterDeduction = amountAfterDeduction;
    }

    public BigDecimal getAmountCalledBack() {
        return amountCalledBack;
    }

    public void setAmountCalledBack(BigDecimal amountCalledBack) {
        this.amountCalledBack = amountCalledBack;
    }

    public BigDecimal getAmountDue() {
        return amountDue;
    }

    public void setAmountDue(BigDecimal amountDue) {
        this.amountDue = amountDue;
    }

    public BigDecimal getRemainingDue() {
        return remainingDue;
    }

    public void setRemainingDue(BigDecimal remainingDue) {
        this.remainingDue = remainingDue;
    }

    public Date getLoanDate() {
        return loanDate;
    }

    public void setLoanDate(Date loanDate) {
        this.loanDate = loanDate;
    }

    public BigDecimal getAmountFinalRepayment() {
        return amountFinalRepayment;
    }

    public void setAmountFinalRepayment(BigDecimal amountFinalRepayment) {
        this.amountFinalRepayment = amountFinalRepayment;
    }

    public Date getAmountFinalDate() {
        return amountFinalDate;
    }

    public void setAmountFinalDate(Date amountFinalDate) {
        this.amountFinalDate = amountFinalDate;
    }

    public Date getLatestFollowUpTime() {
        return latestFollowUpTime;
    }

    public void setLatestFollowUpTime(Date latestFollowUpTime) {
        this.latestFollowUpTime = latestFollowUpTime;
    }

    public Date getEntrustingCaseDate() {
        return entrustingCaseDate;
    }

    public void setEntrustingCaseDate(Date entrustingCaseDate) {
        this.entrustingCaseDate = entrustingCaseDate;
    }

    public Date getReturnCaseDate() {
        return returnCaseDate;
    }

    public void setReturnCaseDate(Date returnCaseDate) {
        this.returnCaseDate = returnCaseDate;
    }

    public BigDecimal getOtherFees() {
        return otherFees;
    }

    public void setOtherFees(BigDecimal otherFees) {
        this.otherFees = otherFees;
    }

    public String getOtherFeesRemarks() {
        return otherFeesRemarks;
    }

    public void setOtherFeesRemarks(String otherFeesRemarks) {
        this.otherFeesRemarks = otherFeesRemarks == null ? null : otherFeesRemarks.trim();
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }

    public boolean check(){
        //判断初始债权总额是否等于 初始本金+初始利息+初始费用+初始担保费+初始咨询费
        BigDecimal entrustMoney= this.getEntrustMoney()==null?BigDecimal.ZERO:this.getEntrustMoney();//初始债权总额
        BigDecimal residualPrincipal= this.getResidualPrincipal()==null?BigDecimal.ZERO:this.getResidualPrincipal();//初始本金余额
        BigDecimal interestMoney= this.getInterestMoney()==null?BigDecimal.ZERO:this.getInterestMoney();//初始利息余额
        BigDecimal serviceFee= this.getServiceFee()==null?BigDecimal.ZERO:this.getServiceFee();//初始费用
        BigDecimal guarantee=this.getInterestGuarantee()==null?BigDecimal.ZERO:this.getInterestGuarantee();//担保费
        BigDecimal advice=this.getInterestAdvice()==null?BigDecimal.ZERO:this.getInterestAdvice();//咨询费


        //初始本金+初始利息+初始费用
        BigDecimal tal=residualPrincipal.add(interestMoney).add(serviceFee).add(guarantee).add(advice);
        //if (entrustMoney.compareTo(tal)!=0) throw new ServiceException("初始债权总额 不等于 (初始本金+初始利息+初始费用) 之和");

        return true;
    }

    public Integer getYcOverdueDays() {
        if(this.overdueStart==null) {
            return null;
        }
        if(this.overdueStart.getTime()<=System.currentTimeMillis()){
            long betweenDay = DateUtil.between(this.overdueStart, new Date(), DateUnit.DAY);
            return Integer.parseInt(betweenDay+"") ;
        }
        return null;
    }
}
