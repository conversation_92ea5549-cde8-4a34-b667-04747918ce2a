package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 共债信息
 * <AUTHOR>
 * @date ：Created in 2022/2/17 11:24
 */
@Data
public class JointDebtInfo {

    /**
     * 案件ID
     */
    private Long caseId;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 客户名称
     */
    private String clientName;
    /**
     * 身份证号
     */
    private String clientIdNum;
    /**
     * 催收员
     */
    private String odvName;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 贷款金额
     */
    private BigDecimal loanMoney;
    /**
     * 逾期天数
     */
    private int overdueDays;
    /**
     * 账期
     */
    private String accountPeriod;

    /**
     *  贷款机构
     */
    private String loanInstitution;

    /**
     * 贷款期数
     */
    private Integer loanPeriods;
    /**
     * 已还期数
     */
    private Integer alreadyPeriods;
    /**
     * 待还期数
     */
    private Integer notPeriods;

    /**
     * 案件地区
     */
    private String caseRegion;
    /**
     * 委托金额
     */
    private BigDecimal entrustMoney;

    /**
     * 贷款本金
     */
    private BigDecimal loanPrincipal;
    /**
     * 利息
     */
    private BigDecimal interestMoney;
    /**
     * 贷款日期
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date loanDate;
    /**
     * 逾期开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private Date overdueStart;

    /**
     * 剩余本金
     */
    private BigDecimal syYhPrincipal;

    /**
     * 剩余利息
     */
    private BigDecimal syYhInterest;

    /**
     * 剩余费用
     */
    private BigDecimal syYhFees;

    /**
     * 剩余应还罚息
     */
    private BigDecimal syYhDefault;

    /**
     * 剩余应还债权总额
     */
    private BigDecimal remainingDue;


}
