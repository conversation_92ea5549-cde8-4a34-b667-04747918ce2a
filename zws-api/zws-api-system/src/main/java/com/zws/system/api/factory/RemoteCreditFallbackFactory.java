package com.zws.system.api.factory;

import com.zws.common.core.domain.R;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.RemoteCreditService;
import com.zws.system.api.domain.CreditProtocolVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 征信服务
 *
 * <AUTHOR>
 * @date 2024/1/17 11:40
 */
@Component
public class RemoteCreditFallbackFactory implements FallbackFactory<RemoteCreditService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteSmsFallbackFactory.class);

    @Override
    public RemoteCreditService create(Throwable throwable) {
        log.error("征信服务调用失败:{}", throwable.getMessage());
        return new RemoteCreditService() {

            @Override
            public R autoReduction(List<Long> caseIds, String source) {
                return R.fail("征信报送失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult add(CreditProtocolVo creditProtocolVo) {
                return AjaxResult.error("新增征信失败:" + throwable.getMessage());
            }
        };
    }
}
