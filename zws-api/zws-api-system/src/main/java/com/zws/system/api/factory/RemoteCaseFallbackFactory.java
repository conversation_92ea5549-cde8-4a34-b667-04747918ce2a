package com.zws.system.api.factory;

import com.zws.common.core.domain.R;
import com.zws.common.core.domain.SmsPhoneAndMsg;
import com.zws.common.core.domain.TimeManage;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.RemoteCaseService;
import com.zws.system.api.domain.*;
import com.zws.system.api.pojo.YcCenterLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/7 14:38
 */
@Component
public class RemoteCaseFallbackFactory implements FallbackFactory<RemoteCaseService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteCaseFallbackFactory.class);

    @Override
    public RemoteCaseService create(Throwable cause) {
        log.error("案件服务调用失败:{}", cause.getMessage());
        return new RemoteCaseService() {
            @Override
            public R<Long> autoReclaimCase() {
                return R.fail("自动退案失败:" + cause.getMessage());
            }

            @Override
            public R<Map<String, Object>> getVerifiedSwitch() {
                return R.fail("资产服务调用失败:" + cause.getMessage());
            }

            @Override
            public R<Long> findFirstApprove(Integer approveCode) {
                return R.fail("资产服务调用失败:" + cause.getMessage());
            }

            @Override
            public R autoReduction(List<Long> recordIds, String source) {
                return R.fail("资产服务调用失败:" + cause.getMessage());
            }

            @Override
            public R executeWorkTask(Long taskId, String source) {
                return R.fail("资产服务调用失败:" + cause.getMessage());
            }

            @Override
            public R calculateTeamCommission(Long insert, Long teamId, Date monthBegin, Date monthEnd, Long detailsId, String source) {
                return R.fail("资产服务调用失败:" + cause.getMessage());
            }

            @Override
            public R sendSmsCommission(SmsPhoneAndMsg smsPhoneAndMsg, String source) {
                return R.fail("资产服务发送短信验证码调用失败:" + cause.getMessage());
            }

            @Override
            public R insertYcCenterLog(YcCenterLog ycCenterLog, String source) {
                return R.fail("中央认证日志写入调用失败:" + cause.getMessage());
            }

            @Override
            public R selectAllSmsSignature() {
                return R.fail("资产服务查询调用失败:" + cause.getMessage());
            }

            @Override
            public R selectAllSmsTemplate() {
                return R.fail("资产服务查询调用失败:" + cause.getMessage());
            }

            @Override
            public R sendCuiShouMessage(String phone,String autographName,Long templateId,Integer smsType,String username,Long userId, Long caseId, String clientName,Long overdueDays,BigDecimal remainingDue) {
                return R.fail("资产服务查询调用失败:" + cause.getMessage());
            }

            @Override
            public R<CaseDetails> getCaseDetails(Long caseId) {
                return R.fail("查询案件详情调用失败:" + cause.getMessage());
            }

            @Override
            public  R<Long> addCaseSignRecord(CaseSignRecord caseSignRecord) {
                return  R.fail("新增签章记录调用失败:" + cause.getMessage());
            }
            @Override
            public AjaxResult getFileOnlinePreviewHost() {
                return AjaxResult.error("获取文件预览地址调用失败:" + cause.getMessage());
            }

            @Override
            public R insertTimeRecord(TimeManage timeManage, String source) {
                return R.fail("资产服务调用失败:" + cause.getMessage());
            }
            @Override
            public AjaxResult selectSmsListByCreateId(Integer createId) {
                return AjaxResult.error("获取对应机构的短信模板列表失败："+ cause.getMessage());
            }

            @Override
            public R<List<String>> selectAssetBatchNums(Long userId) {
                return R.fail("获取用户资产批次号失败:" + cause.getMessage());
            }

            @Override
            public R deleteByUserId(Long userId) {
                return R.fail("删除用户资产批次号失败:" + cause.getMessage());
            }

            /**
             * 内部接口-重新计算案件金额
             *
             * @param innerRecalculate
             * @return
             */
            @Override
            public R innerRecalculateOffsetByCaseId(InnerRecalculate innerRecalculate) {
                return R.fail("重新计算案件金额失败:" + cause.getMessage());
            }

            @Override
            public R<Long> caseRecovery(List<Long> teamId) {
                return R.fail("合作关闭/黑名单 案件回收失败:" + cause.getMessage());
            }

            @Override
            public R<Long> addCost(Long teamId,  String cname) {
                return R.fail("调用增加机构费用管理数据失败:" + cause.getMessage());
            }
            @Override
            public R<List<Legal>> getLabel() {
                return R.fail("获取案件标签列表失败:" + cause.getMessage());
            }

            @Override
            public R<List<StatePojo>> getLabels() {
                return R.fail("获取案件标签下拉框失败:" + cause.getMessage());
            }

        };
    }
}
