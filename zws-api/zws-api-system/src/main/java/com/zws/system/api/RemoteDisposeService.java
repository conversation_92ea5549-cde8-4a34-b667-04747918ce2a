package com.zws.system.api;

import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.factory.RemoteDictFallbackFactory;
import com.zws.system.api.factory.RemoteDisposeFallbackFactory;
import com.zws.system.api.model.LoginUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.*;

/**
 * 催收端的远程调用服务
 *
 * <AUTHOR>
 * @date 2024/5/8 23:25
 */
@FeignClient(contextId = "remoteDisposeService", value = ServiceNameConstants.ZWS_DISPOSE, fallbackFactory = RemoteDisposeFallbackFactory.class)
public interface RemoteDisposeService {

    /**
     * 远程调用函件审批操作
     * @param id 审核记录ID
     * @return
     */
    @PostMapping("/dispose/updateSignRecordPass")
    public R updateSignRecord(@RequestParam("id")  Long id,@RequestBody LoginUser loginUser);

    @GetMapping("/getTeamState")
    public Integer getTeamState(String username);

    /**
     * 根据案件id查询案件所有信息以及共债信息
     * @param caseId
     * @return
     */
    @RequestMapping(value = "/collection/cis/selectCase/{caseId}", method = RequestMethod.GET)
    public AjaxResult selectMapWithCaseDetailsCis(@PathVariable("caseId") Long caseId);

}
