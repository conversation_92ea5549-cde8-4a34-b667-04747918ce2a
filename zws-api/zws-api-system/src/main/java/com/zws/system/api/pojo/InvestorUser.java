package com.zws.system.api.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 投资人用户
 * <AUTHOR>
 */
@Data
public class InvestorUser {
    /**
     * 用户序号
     */
    private Long userId;
    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 工号
     */
    private Long employeeId;
    /**
     * 用户资产批次号
     */
    private String assetBatchNums;
    /**
     * 用户资产批次号集合
     */
    private List<String> assetBatchNumsList;
    /**
     * 用户资产包
     */
    private String assetBatchNames;
    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
