package com.zws.system.api.pojo;

import lombok.Data;
import lombok.ToString;

/**
 * 推送呼叫状态信息
 * <AUTHOR>
 * @date ：Created in 2022/9/6 17:54
 */
@Data
@ToString
public class CallMessage {

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 团队/用户标识（0-团队;1-用户）
     */
    private Integer identification;

    /**
     * 呼叫状态
     * 0-初始(挂机状态)，1-呼叫中，2-通话中
     */
    private Integer callStatus;
    /**
     * 呼叫电话
     */
    private String phone;
    /**
     * 开始通话时间戳
     */
    private Long callStartTime;
    /**
     * 其他提示信息
     */
    private String msg;

//--------------------------------------------------预测试外呼来电弹屏参数---------------------------------------------------

    /**
     * 任务uuid（预测试外呼）
     */
    private String taskUuid;

    /**
     * 客户uuid（预测试外呼）
     */
    private String customUuid;

    /**
     * 案件id
     */
    private Long caseId;

    /**
     * 联系人id
     */
    private Long contactsId;
}
