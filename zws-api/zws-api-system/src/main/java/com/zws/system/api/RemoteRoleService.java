package com.zws.system.api;

import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.system.api.domain.SysRole;
import com.zws.system.api.factory.RemoteUserFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 角色服务
 * <AUTHOR>
 * @date ：Created in 2022/5/5 10:17
 */
@FeignClient(contextId = "remoteRoleService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteRoleService {

    /**
     * 获取全部角色
     * @return 结果
     */
    @GetMapping("/role/optionselect")
    public R<List<SysRole>> selectRoleAll();

    /**
     * 获取催收端主账号角色
     * @return
     */
    @GetMapping("/role/getCsdAdminRole")
    public R<SysRole> getCsdAdminRole();

    /**
     * 获取资产端主账号角色
     * @return
     */
    @GetMapping("/role/getAdminRole")
    public R<SysRole> getAdminRole();


}
