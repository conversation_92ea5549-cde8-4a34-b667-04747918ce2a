package com.zws.system.api.factory;

import com.zws.common.core.domain.R;
import com.zws.system.api.RemoteDictService;
import com.zws.system.api.domain.SysDictData;
import com.zws.system.api.domain.SysDictType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/3/1 11:44
 */
@Component
public class RemoteDictFallbackFactory implements FallbackFactory<RemoteDictService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteDictFallbackFactory.class);

    @Override
    public RemoteDictService create(Throwable throwable) {
        log.error("系统服务调用失败:{}", throwable.getMessage());
        return new RemoteDictService()
        {
            @Override
            public R<List<SysDictData>> dictType(String dictType) {
                return R.fail("获取字典数据失败:" + throwable.getMessage());
            }

            @Override
            public R<Long> addDictType(SysDictType sysDictType, String source) {
                return R.fail("新增字典失败:" + throwable.getMessage());
            }
        };
    }
}
