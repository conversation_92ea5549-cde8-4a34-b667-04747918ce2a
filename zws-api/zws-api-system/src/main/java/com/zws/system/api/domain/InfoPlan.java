package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 案件信息- 还款计划
 */
@Data
public class InfoPlan extends BaseEntity {

    private Long id;

    /**
     * 关联的还款计划表id
     */
    private Long planId;
    /**
     * 案件ID
     */
    private Long caseId;
    /**
     * 序号
     */
    private Integer orderNumber;
    /**
     * 还款期数
     */
    @Excel(name = "还款期数")
    private Integer hkPeriodsNumber;
    /**
     * 应还日期
     */
    @Excel(name = "应还日期", dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date yhDate;
    /**
     * 计划应还本金
     */
    @Excel(name = "应还本金")
    private BigDecimal jhYhPrincipal;
    /**
     * 计划应还利息
     */
    @Excel(name = "应还利息")
    private BigDecimal jhYhInterest;

    /**
     * 应还罚息
     */
    @Excel(name = "应还罚息")
    private BigDecimal syYhSurplusInterest;

    /**
     * 计划应还咨询费
     */
    private BigDecimal jhYhAdvice;
    /**
     * 计划应还担保费
     */
    private BigDecimal jhYhGuarantee;
    /**
     * 剩余应还金额
     */
    @Excel(name = "剩余应还金额")
    private BigDecimal syYhMoney;
    /**
     * 剩余应还本金
     */
    @Excel(name = "剩余应还本金")
    private BigDecimal syYhPrincipal;
    /**
     * 剩余应还利息
     */
    @Excel(name = "剩余应还利息")
    private BigDecimal syYhInterest;
    /**
     * 剩余应还复利
     */
    @Excel(name = "剩余应还复利")
    private BigDecimal syYhCompoundInterest;
    /**
     * 剩余应还罚息
     */
    @Excel(name = "剩余应还罚息")
    private BigDecimal syYhPenaltyInterest;
    /**
     * 剩余应还咨询费
     */
    private BigDecimal syYhAdvice;
    /**
     *剩余应还担保费
     */
    private BigDecimal syYhGuarantee;
    /**
     * 更新剩余应还罚息时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date syYhUpdateTime;

    /**
     * 剩余应还违约金
     */
    @Excel(name = "剩余应还违约金")
    private BigDecimal syYhWyj;
    /**
     * 剩余应还滞纳金
     */
    @Excel(name = "剩余应还滞纳金")
    private BigDecimal syYhLateFee;

    /**
     * 已还款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date alreadyHkDate;
    /**
     * 已偿还本金
     */
    @Excel(name = "实还本金")
    private BigDecimal ychPrincipal;
    /**
     * 已偿还利息
     */
    @Excel(name = "实还利息")
    private BigDecimal ychInterest;
    /**
     * 已偿还罚息
     */
    @Excel(name = "实还罚息")
    private BigDecimal ychPenaltyInterest;
    /**
     * 已偿还复利
     */
    @Excel(name = "实还复利")
    private BigDecimal ychCompoundInterest;
    /**
     * 已偿还滞纳金
     */
    @Excel(name = "实还滞纳金")
    private BigDecimal ychLateFee;
    /**
     * 已偿还违约金
     */
    @Excel(name = "实还违约金")
    private BigDecimal ychWyj;
    /**
     * 已偿还咨询费
     */
    private BigDecimal ychAdvice;
    /**
     *已偿还担保费
     */
    private BigDecimal ychGuarantee;
    /**
     * 已偿还金额
     */
    @Excel(name = "实还金额")
    private BigDecimal ychMoney;

    private String delFlag;

    /**
     * 排序（1-正序；2-倒序）
     */
    private Integer sortOrder;

    /**
     * 判断是否为空
     *
     * @return
     */
    public boolean isEmpty() {
        if (this.hkPeriodsNumber != null) {
            return false;
        }
        if (this.yhDate != null) {
            return false;
        }
        if (this.jhYhPrincipal != null) {
            return false;
        }
        if (this.jhYhInterest != null) {
            return false;
        }
        if (this.syYhMoney != null) {
            return false;
        }
        if (this.syYhPrincipal != null) {
            return false;
        }
        if (this.syYhInterest != null) {
            return false;
        }
        if (this.syYhCompoundInterest != null) {
            return false;
        }
        if (this.syYhPenaltyInterest != null) {
            return false;
        }
        if (this.syYhWyj != null) {
            return false;
        }
        if (this.syYhLateFee != null) {
            return false;
        }
        if (this.alreadyHkDate != null) {
            return false;
        }
        if (this.ychPrincipal != null) {
            return false;
        }
        if (this.ychInterest != null) {
            return false;
        }
        if (this.ychPenaltyInterest != null) {
            return false;
        }
        if (this.ychCompoundInterest != null) {
            return false;
        }
        if (this.ychLateFee != null) {
            return false;
        }
        if (this.ychWyj != null) {
            return false;
        }
        if (this.ychMoney != null) {
            return false;
        }
        return true;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCaseId() {
        return caseId;
    }

    public void setCaseId(Long caseId) {
        this.caseId = caseId;
    }

    public Integer getHkPeriodsNumber() {
        return hkPeriodsNumber;
    }

    public void setHkPeriodsNumber(Integer hkPeriodsNumber) {
        this.hkPeriodsNumber = hkPeriodsNumber;
    }


    public BigDecimal getJhYhPrincipal() {
        return jhYhPrincipal;
    }

    public void setJhYhPrincipal(BigDecimal jhYhPrincipal) {
        this.jhYhPrincipal = jhYhPrincipal;
    }

    public BigDecimal getJhYhInterest() {
        return jhYhInterest;
    }

    public void setJhYhInterest(BigDecimal jhYhInterest) {
        this.jhYhInterest = jhYhInterest;
    }

    public BigDecimal getSyYhMoney() {
        return syYhMoney;
    }

    public void setSyYhMoney(BigDecimal syYhMoney) {
        this.syYhMoney = syYhMoney;
    }

    public BigDecimal getSyYhPrincipal() {
        return syYhPrincipal;
    }

    public void setSyYhPrincipal(BigDecimal syYhPrincipal) {
        this.syYhPrincipal = syYhPrincipal;
    }

    public BigDecimal getSyYhInterest() {
        return syYhInterest;
    }

    public void setSyYhInterest(BigDecimal syYhInterest) {
        this.syYhInterest = syYhInterest;
    }

    public BigDecimal getSyYhCompoundInterest() {
        return syYhCompoundInterest;
    }

    public void setSyYhCompoundInterest(BigDecimal syYhCompoundInterest) {
        this.syYhCompoundInterest = syYhCompoundInterest;
    }

    public BigDecimal getSyYhPenaltyInterest() {
        return syYhPenaltyInterest;
    }

    public void setSyYhPenaltyInterest(BigDecimal syYhPenaltyInterest) {
        this.syYhPenaltyInterest = syYhPenaltyInterest;
    }

    public BigDecimal getSyYhWyj() {
        return syYhWyj;
    }

    public void setSyYhWyj(BigDecimal syYhWyj) {
        this.syYhWyj = syYhWyj;
    }

    public BigDecimal getSyYhLateFee() {
        return syYhLateFee;
    }

    public void setSyYhLateFee(BigDecimal syYhLateFee) {
        this.syYhLateFee = syYhLateFee;
    }


    public BigDecimal getYchPrincipal() {
        return ychPrincipal;
    }

    public void setYchPrincipal(BigDecimal ychPrincipal) {
        this.ychPrincipal = ychPrincipal;
    }

    public BigDecimal getYchInterest() {
        return ychInterest;
    }

    public void setYchInterest(BigDecimal ychInterest) {
        this.ychInterest = ychInterest;
    }

    public BigDecimal getYchPenaltyInterest() {
        return ychPenaltyInterest;
    }

    public void setYchPenaltyInterest(BigDecimal ychPenaltyInterest) {
        this.ychPenaltyInterest = ychPenaltyInterest;
    }

    public BigDecimal getYchCompoundInterest() {
        return ychCompoundInterest;
    }

    public void setYchCompoundInterest(BigDecimal ychCompoundInterest) {
        this.ychCompoundInterest = ychCompoundInterest;
    }

    public BigDecimal getYchLateFee() {
        return ychLateFee;
    }

    public void setYchLateFee(BigDecimal ychLateFee) {
        this.ychLateFee = ychLateFee;
    }

    public BigDecimal getYchWyj() {
        return ychWyj;
    }

    public void setYchWyj(BigDecimal ychWyj) {
        this.ychWyj = ychWyj;
    }

    public BigDecimal getYchMoney() {
        return ychMoney;
    }

    public void setYchMoney(BigDecimal ychMoney) {
        this.ychMoney = ychMoney;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }

    public boolean check() {

        return true;
    }

}
