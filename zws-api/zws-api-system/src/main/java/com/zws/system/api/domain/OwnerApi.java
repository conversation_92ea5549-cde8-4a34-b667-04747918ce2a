package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 资产方管理- 资产方(转让方)
 * 财项目所有中 资产方 就是 转让方
 */
@Data
public class OwnerApi extends BaseEntity {

    /**
     * 转让方id
     */
    private Long id;
    /**
     * 转让方名称，公司全称（工商登记营业执照全称）
     */
    @NotEmpty(message = "转让方名称不能为空")
    private String name;
    /**
     * 社会统一信用代码
     */
    private String unifiedCode;
    /**
     * 转让方简称(转让方ID)
     */
    @NotEmpty(message = "请选择转让方")
    private String shortName;

    @JsonIgnore
    private String delFlag;

    /**
     * 原转让方ID
     */
    private String originName;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }




}
