package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 团队用户信息
 */
@Data
public class TeamUserMessage {
    /**
     * 团队用户信息主键id
     */
    private Long id;
    /**
     * 团队id
     */
    private Long createId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 团队/用户标识（0-团队;1-用户）
     */
    private Integer identification;
    /**
     * 消息中心id
     */
    private Long messageId;
    /**
     * 消息类型,0-系统信息(公告) 1-业务进度提醒,2-审批提醒,3-工单反馈
     */
    private Integer messageType;
    /**
     * 消息标题
     */
    private String messageTitle;
    /**
     * 创建时间-发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 阅读公告时间,null表示未读
     */
    private Date updateTime;
    /**
     *
     */
    private Integer delFlag;
    /**
     * 消息内容
     */
    private String messageContent;
    /**
     * 0-未读，1-已读
     */
    private Integer read;
    /**
     * 信息来源(0-资产端，1-催收端)
     */
    private Integer messageFrom;

    /**
     * 提醒方式(非数据库字段)，0-红点，1-弹窗
     */
    private Integer reminderMode;




    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getIdentification() {
        return identification;
    }

    public void setIdentification(Integer identification) {
        this.identification = identification;
    }

    public Integer getMessageType() {
        return messageType;
    }

    public void setMessageType(Integer messageType) {
        this.messageType = messageType;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle == null ? null : messageTitle.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            read = 1;
        } else {
            read = 0;
        }
        this.updateTime = updateTime;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent == null ? null : messageContent.trim();
    }

    /*public Integer getRead() {
        if(this.updateTime==null) return 0;
        else return 1;
    }*/

    @Override
    public String toString() {
        return "TeamUserMessage{" +
                "id=" + id +
                ", createId=" + createId +
                ", userId=" + userId +
                ", identification=" + identification +
                ", messageId=" + messageId +
                ", messageType=" + messageType +
                ", messageTitle='" + messageTitle + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", delFlag=" + delFlag +
                ", messageContent='" + messageContent + '\'' +
                ", read=" + read +
                ", messageFrom=" + messageFrom +
                ", reminderMode=" + reminderMode +
                '}';
    }
}
