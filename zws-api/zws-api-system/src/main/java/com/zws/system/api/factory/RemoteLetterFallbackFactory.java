package com.zws.system.api.factory;

import com.github.pagehelper.PageInfo;
import com.zws.common.core.constant.Constants;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.seal.CalculateSealCountParam;
import com.zws.common.core.domain.seal.SignatureParam;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.system.api.RemoteLetterService;
import com.zws.system.api.domain.DocumentTemplate;
import com.zws.system.api.domain.Letter;
import com.zws.system.api.domain.LetterMessage;
import com.zws.system.api.domain.LetterTemplate;
import feign.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * 签章服务
 *
 * <AUTHOR>
 * @date 2024/4/23 19:40
 */
@Component
public class RemoteLetterFallbackFactory implements FallbackFactory<RemoteLetterService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteLetterFallbackFactory.class);

    @Override
    public RemoteLetterService create(Throwable throwable) {
        log.error("签章服务调用失败:{}", throwable.getMessage());
        return new RemoteLetterService() {

            @Override
            public String doSignature(SignatureParam signatureParam) {
                log.error("签章接口执行失败");
                return null;
            }

            @Override
            public R<AjaxResult> calculateSignatureCount(CalculateSealCountParam calculateSealCountParam) {
                return R.fail("签章日志统计接口调用失败:" + throwable.getMessage());
            }

            @Override
            public R<AjaxResult> signSettleRecord(Long caseId) {
                return R.fail("签章信息详情接口调整失败:" + throwable.getMessage());
            }

            @Override
            public R<AjaxResult> createSignFile(List<Integer> ids, String type) {
                return R.fail("生成签章文件接口调整失败:" + throwable.getMessage());
            }

            @Override
            public R<List<Integer>> list(String batchNum ) {
                return R.fail("查询函件接口调整失败:" + throwable.getMessage());
            }

            @Override
            public R<LetterTemplate> getByBatchNum(String batchNum) {
                return R.fail("通过批次号查询函件批次接口调整失败:" + throwable.getMessage());
            }

            @Override
            public R<AjaxResult> updateProce(List<Integer> ids) {
                return R.fail("更新函件批次状态:" + throwable.getMessage());
            }

            @Override
            public TableDataInfo disposeList( LetterMessage message) {
                TableDataInfo tableDataInfo=new TableDataInfo();
                tableDataInfo.setCode(Constants.FAIL);
                tableDataInfo.setMsg("查询签章文件失败:" + throwable.getMessage());
                return tableDataInfo;
            }

            @Override
            public AjaxResult verifyFile(LetterMessage message) {
                return AjaxResult.error("校验文件失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult add(LetterMessage message) {
                return AjaxResult.error("新建发函失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult verifyZipFile(LetterMessage message) {
                return AjaxResult.error("校验zip压缩包文件失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult addZip(LetterMessage message) {
                return AjaxResult.error("新建发函(压缩包pdf)失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult checkUniqueName(LetterMessage template) {
                return AjaxResult.error("判断模板名称是否唯一失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult uploadZip(MultipartFile[] file) {
                return AjaxResult.error("上传zip压缩文件失败:" + throwable.getMessage());
            }

            @Override
            public R<AjaxResult> createOldSourceFile() {
                return R.fail("重新生成以前的源文件失败:" + throwable.getMessage());
            }

            @Override
            public R<AjaxResult> getByBatchNum2(String batchNum) {
                return R.fail("校验文件失败:" + throwable.getMessage());
            }

            @Override
            public TableDataInfo disposeItemList(@SpringQueryMap Letter letter) {
                TableDataInfo tableDataInfo=new TableDataInfo();
                tableDataInfo.setCode(Constants.FAIL);
                tableDataInfo.setMsg("查询签章文件失败:" + throwable.getMessage());
                return tableDataInfo;
//                return R.fail("查询签章文件失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult getOptions(Integer type) {
                return AjaxResult.error("获取模板选项失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult uploadCase(@RequestPart(value = "file") MultipartFile file) {
                return AjaxResult.error("上传excel文件到minio失败:" + throwable.getMessage());
            }

            @Override
            public Response downloadTemplateVariable( @RequestParam("id")Integer id) {
                  throw  new RuntimeException("下载模板变量失败");
            }

            @Override
            public AjaxResult getPreview(@RequestParam("id")Integer id){
                return AjaxResult.error("新建发函失败:" + throwable.getMessage());
            }
            @Override
            public AjaxResult getPreviewUrl(@RequestParam("id")Integer id){
                return AjaxResult.error("获取预览文件失败:" + throwable.getMessage());
            }

            @Override
            public R getSignRecordId(Long letterId) {
                return R.fail();
            }

            @Override
            public R<AjaxResult> updateLetterMessage(String batchNum,String status) {
                return R.fail("更新函件批次状态失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult disposeExport(@SpringQueryMap LetterMessage message) {
                return AjaxResult.error("下载函件批次压缩文件失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult getOptions() {
                return AjaxResult.error("获取变量选项失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult add(LetterTemplate record) {
                return AjaxResult.error("新增电子用印模板失败:" + throwable.getMessage());
            }

            @Override
            public R<LetterTemplate> getLetterTemplateByApproveId(Long approveId) {
                return R.fail("根据邮件模板审批id获取模板信息失败:" + throwable.getMessage());
            }

            @Override
            public R<LetterTemplate> getLetterById(Integer id) {
                return R.fail("根据邮件模板id获取模板信息失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult examineApprove(LetterTemplate record) {
                return AjaxResult.error("审批电子用印模板失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult edit(LetterTemplate record) {
                return AjaxResult.error("编辑电子用印模板失败:" + throwable.getMessage());
            }

            @Override
            public R<LetterTemplate> getById(Integer id) {
                return R.fail("根据id查询签章模板失败:" + throwable.getMessage());
            }

            @Override
            public R<String> personSignSettle(Long caseId) {
                return R.fail("个人征信授权签章失败失败:" + throwable.getMessage());
            }

            @Override
            public AjaxResult add(DocumentTemplate template) {
                return null;
            }

            @Override
            public TableDataInfo listNotPaging(DocumentTemplate template) {
                return null;
            }

            @Override
            public AjaxResult edit(DocumentTemplate template) {
                return null;
            }

            @Override
            public AjaxResult getSelectTree() {
                return null;
            }
        };
    }
}
