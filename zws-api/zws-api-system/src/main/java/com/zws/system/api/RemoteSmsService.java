package com.zws.system.api;

import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.SmsRequest;
import com.zws.common.core.domain.TemplateSms;
import com.zws.common.core.domain.sms.*;
import com.zws.common.core.enums.ZcOrCsEnum;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.factory.RemoteSmsFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

@FeignClient(contextId = "remoteSmsService", value = ServiceNameConstants.ZWS_SMS, fallbackFactory = RemoteSmsFallbackFactory.class)
public interface RemoteSmsService {

    /**
     * 远程调用短信接口（传递短信类型（短信通知，会员营销））
     *
     * @param smsRequest 短信请求
     * @return {@link R}
     */
    @PostMapping("/distributorApi/sendSmsMarketing")
    R sendSmsMarketing(@RequestBody SmsRequest smsRequest);

    @PostMapping("/distributorApi/sendDanMiSms")
    R sendMessage(@RequestBody SendDanMiVo sendDanMiVo);
    @PostMapping("/distributorApi/sendCuiShouMessage")
    R sendCuiShouMessage(@RequestParam("phone") String phone, @RequestParam("autographName") String autographName, @RequestParam("templateId") Long templateId, @RequestParam("smsType") Integer smsType, @RequestParam("username") String username, @RequestParam("userId") Long userId, @RequestParam("caseId") Long caseId, @RequestParam("clientName") String clientName, @RequestParam("overdueDays") Long overdueDays, @RequestParam("remainingDue") BigDecimal remainingDue);
    @PostMapping(value = "/distributorApi/createTemplate")
    R createTemplate(@RequestBody SmsRequest smsRequest);
    @PostMapping(value = "/distributorApi/updateTemplate")
    R updateTemplate(@RequestBody SmsRequest smsRequest);
    @PostMapping(value = "/distributorApi/deleteTemplate")
    R deleteTemplate(@RequestBody SmsRequest smsRequest);

    /**
     * 资产端发送单个短信
     *
     * @return
     */
    @PostMapping(value = "/distributorApi/sendZcMessage")
    R sendZcMessage(@RequestBody SendZcMessageVo sendZcMessage);
    @PostMapping("/distributorApi/sendChuWuSms")
    R sendChuWuMessage(SendChuWuVo sendChuWuVo);


    /**
     * 调诉端/催收端批量发送短信
     * @param sendMessagePojo
     * @return
     */
    @PostMapping("/distributorApi/sendAppealMessage")
    R sendAppealMessages(@RequestBody SendMessagePojo sendMessagePojo);
}
