package com.zws.system.api.factory;

import com.zws.common.core.domain.R;
import com.zws.system.api.RemoteJobService;
import com.zws.system.api.domain.Job;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/7 17:05
 */
@Component
public class RemoteJobFallbackFactory implements FallbackFactory<RemoteJobService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteJobFallbackFactory.class);


    @Override
    public RemoteJobService create(Throwable cause) {
        log.error("消息中心服务调用失败:{}", cause.getMessage());

        return new RemoteJobService(){
            @Override
            public R addJob(Job job, String source) {
                return R.fail("创建定时任务失败:" + cause.getMessage());
            }
        };
    }
}
