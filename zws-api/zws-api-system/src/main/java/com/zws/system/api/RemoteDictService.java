package com.zws.system.api;

/**
 * <AUTHOR>
 * @date ：Created in 2022/3/1 11:42
 */

import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.domain.SysDictData;
import com.zws.system.api.domain.SysDictType;
import com.zws.system.api.factory.RemoteDictFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统字典-服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteDictService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteDictFallbackFactory.class)
public interface RemoteDictService {

    /**
     * 根据字典类型查询字典数据信息
     */
    @GetMapping(value = "/dict/data/type/{dictType}")
    public R<List<SysDictData>> dictType(@PathVariable("dictType") String dictType);

    /**
     * 新增字典类型
     * @param sysDictType
     * @param source
     * @return
     */
    @PostMapping("/dict/type")
    R<Long> addDictType(@RequestBody SysDictType sysDictType, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}
