package com.zws.system.api.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import com.zws.common.core.annotation.Excel.ColumnType;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 系统访问记录表 sys_logininfor
 *
 * <AUTHOR>
 */
@Data
public class SysLogininfor extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @Excel(name = "序号", cellType = ColumnType.NUMERIC)
    private Long infoId;

    /** 用户账号 */
    @Excel(name = "用户账号")
    private String userName;

    /** 状态 0成功 1失败 */
    @Excel(name = "状态", readConverterExp = "0=成功,1=失败")
    private String status;

    /** 地址 */
    @Excel(name = "地址")
    private String ipaddr;

    /** 描述 */
    @Excel(name = "描述")
    private String msg;

    /** 访问时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "访问时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date accessTime;

    /** 所属公司 */
    @Excel(name = "所属公司")
    private String deptName;

    /** 勾选的id集合 */
    private List<Long> infoIds;

    /** 勾选的主键id拼接字段 */
    private String operIds;
    /**
     * 内网IP
     */
    @Excel(name = "内网IP")
    private String internalIp;
    /**
     * 浏览器指纹
     */
    @Excel(name = "浏览器指纹")
    private String browserFingerprint;

    public List<Long> getInfoIds() {
        return infoIds;
    }

    public void setInfoIds(List<Long> infoIds) {
        this.infoIds = infoIds;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getInfoId()
    {
        return infoId;
    }

    public void setInfoId(Long infoId)
    {
        this.infoId = infoId;
    }

    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getIpaddr()
    {
        return ipaddr;
    }

    public void setIpaddr(String ipaddr)
    {
        this.ipaddr = ipaddr;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    public Date getAccessTime()
    {
        return accessTime;
    }

    public void setAccessTime(Date accessTime)
    {
        this.accessTime = accessTime;
    }

    public String getOperIds() {
        return operIds;
    }

    public void setOperIds(String operIds) {
        this.operIds = operIds;
    }
}
