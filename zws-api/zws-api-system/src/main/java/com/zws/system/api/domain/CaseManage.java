package com.zws.system.api.domain;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 案件管理-资产分案
 * <AUTHOR>
 * @date 2024年8月8日15:27:28
 */
@Data
public class CaseManage {

    /**
     * 案件管理ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 案件ID
     */
    @Excel(name = "案件ID", sort = 1)
    private Long caseId;

    /**
     * 资产管理id
     */
    private Long assetManageId;

    /**
     * 机构id（条件用）
     */
    private Long teamId;

    /**
     * 催收员登录账号
     */
    @Excel(name = "催收员登录账号", sort = 2)
    private String loginAccount;

    /**
     * 分配状态
     */
    private String allocatedState;

    /**
     * 案件状态(0-未分配，1-已分配，2-停催，3-留案，4-退案 5-回收案件，6-案件结清)
     */
    private String caseState;

    /**
     * 委托方ID
     */
    private Long entrustingPartyId;

    /**
     * 委托方名称
     */
    private String entrustingPartyName;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 资产包名
     */
    private String packageName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 批次号
     */
    private String batchNum;

    /**
     * 委案批号
     */
    private String entrustingCaseBatchNum;

    /**
     * 委外团队id
     */
    private Long outsourcingTeamId;

    /**
     * 委外团队名称
     */
    private String outsourcingTeamName;

    /**
     * 客户姓名
     */
    @Excel(name = "姓名", sort = 3)
    private String clientName;

    /**
     * 性别
     */
    private String clientSex;

    /**
     * 证件类型
     */
    private String clientIdType;


    /**
     * 客户电话
     */
    private String clientPhone;

    /**
     * 委托金额
     */
    @Excel(name = "委案金额", sort = 4)
    private BigDecimal clientMoney;

    /**
     * 剩余本金
     */
    private BigDecimal clientResidualPrincipal;

    /**
     * 逾期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clientOverdueStart;

    /**
     * 账期
     */
    private String accountPeriod;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 跟进状态
     */
    private String followUpState;

    /**
     * 开始跟进时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date followUpStart;

    /**
     * 最后跟进时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date followUpAst;

    /**
     * 退按日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date returnCaseDate;

    /**
     * 地区
     */
    private String area;

    /**
     * 标签
     */
    private String label;

    /**
     * 催收员id
     */
    private Long odvId;

    /**
     * 催收员名称
     */
    private String odvName;

    /**
     * 催收员工号
     */
    @Excel(name = "员工工号", type = Excel.Type.IMPORT)
    private Integer employeesWorking;

    /**
     * 催员账号
     */
    private String employeesAccount;

    /**
     * 催收状态
     */
    private String urgeState;

    /**
     * 分配时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date allocatedTime;

    /**
     * 催记权限，0-不能查看历史催记，1-查看全部历史催记，2-只能查看本团队历史催记
     */
    private String urgePower;

    /**
     * 1.催收记录类别（包括催收记录、协催记录、外访记录）
     * 2.调执记录类别（包括调执记录、保全记录、收费记录、电话记录、物流记录）
     * 3.调诉执记录类别（包括调诉执记录、保全记录、收费记录、电话记录、物流记录）
     * 4.公共记录类别（回款记录、减免记录、投诉记录、便签记录、通话记录、短信记录）
     *
     * 历史权限
     * A1催收记录类别-本机构、A2调执记录类别-本机构、A3调诉执记录类别-本机构、
     * B1催收记录类别-全部机构、B2调执记录类别-全部机构、B3调诉执记录类别-全部机构
     *
     * 可多选记录类别，则拼接使用，例："A1A3"、"B1B2B3"
     *
     */
    private String historyPower;

    /**
     * 删除标记，0表示存在
     */
    private Boolean delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
//------------------------------------------------------
    /**
     * 操作
     */
    private String operation;

    /**
     * 逾期天数
     */
    private Long overdueDays;

    /**
     * yc逾期天数
     */
    private Long ycOverdueDays;


    /**
     * 结清日期
     */
    private Date clearDate;

    /**
     * UID
     */
    private String uid;
    /**
     * 未跟进天数
     */
    private Long notFollowed;
    /**
     * 标签内容
     */
    private String labelContent;
    /**
     * 标签表id
     */
    private Integer labelId;


//-----------------------------------------------------------
    /**
     * 标签颜色（0-黑色，1-粉色，2-橙色，3-绿色，4-蓝色，5-紫色，6-黄色）
     */
    private Integer code;
    /**
     * 统计数量
     */
    private Integer quantity;
    /**
     * 共债案件总金额
     */
    private BigDecimal amount;
    /**
     * 非公债人员身份证号集合
     */
    private List<String> clientIdcards;


//-----------------------------------------------------------------------
    /**
     * 案件分配状态集合
     */
    private List<String> caseStates;
    /**
     * 电话状态
     */
    private Integer phoneState;
    /**
     * 债权总金额
     */
    @Excel(name = "债权总金额", sort = 5)
    private BigDecimal entrustMoney;
    /**
     * 债权本金
     */
    @Excel(name = "债权本金", sort = 6)
    private BigDecimal residualPrincipal;
    /**
     * 债权利息
     */
    @Excel(name = "债权利息", sort = 7)
    private BigDecimal interestMoney;
    /**
     * 债权费用
     */
    @Excel(name = "债权费用", sort = 8)
    private BigDecimal serviceFee;
    /**
     * 剩余应还本金
     */
    @Excel(name = "剩余应还本金", sort = 9)
    private BigDecimal syYhPrincipal;
    /**
     * 剩余应还利息
     */
    @Excel(name = "剩余应还利息", sort = 10)
    private BigDecimal syYhInterest;
    /**
     * 剩余应还费用
     */
    @Excel(name = "剩余应还费用", sort = 11)
    private BigDecimal syYhFees;
    /**
     * 剩余应还债权总额
     */
    @Excel(name = "剩余应还债权总额", sort = 12)
    private BigDecimal remainingDue;
    /**
     * 委按日期
     */
    @Excel(name = "委案日期", dateFormat = "yyyy-MM-dd", sort = 13)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entrustingCaseDate;
    /**
     * 基准日逾期天数
     */
    @Excel(name = "基准日逾期天数", sort = 14)
    private Integer baseOverdueDays;
    /**
     * 结清状态（0-未结清，1-已结清）
     */
    @Excel(name = "结清状态", readConverterExp = "0=未结清,1=已结清", sort = 15)
    private Integer settlementStatus;
    /**
     * 客户户籍
     */
    @Excel(name = "户籍地", sort = 16)
    private String clientCensusRegister;
    /**
     * 客户身份证号
     */
    @Excel(name = "证件号码", sort = 17)
    private String clientIdcard;
    /**
     * 出生日期
     */
    @Excel(name = "出生日期", dateFormat = "yyyy-MM-dd", sort = 18)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clientBirthday;
    /**
     * 末次还款日期
     */
    private Date amountFinalDate;
    /**
     * 户籍地址
     */
    private String registeredAddress;
    /**
     * 家庭住址
     */
    private String homeAddress;
    /**
     * 单位名称
     */
    private String placeOfWork;

    /**
     * 调诉阶段
     * 调诉端分案到催员后默认值 =>未提交立案
     */
    private String disposeStage;

    /**
     * 调解阶段
     * 调诉端分案到催员后默认值 => 调解材料
     */
    private String mediatedStage;


    /**
     * 是否诉保（0未保全、1已保全）
     * 调诉端分案到催员后默认值 => 未申请诉保0
     */
    private Integer isFreeze;

    /**
     * 调诉员
     */
    private String prosecutor;

    /**
     * (调解流程)案件状态  0-未分配，1-已分配，2-停催，3-留案，4-退案 5-回收案件，6-案件结清
     */
    private String caseMediateState;
    /**
     * 调解员名称
     */
    private String mediatorName;
    /**
     * 调解员ID
     */
    private Long mediatorId;
    /**
     * 资产编号
     */
    private String assetNo;

    /**
     * 导入来源（0/null-资产端；1-催收/调诉机构）
     */
    private Integer importSource;

    public Long getOverdueDays() {
        if (this.getClientOverdueStart() == null) {
            return 0L;
        }
        if (this.getSettlementStatus() == null) {
            return 0L;
        } else if (this.getSettlementStatus() == 1) {
            if (this.getYcOverdueDays() != null) {
                return this.getYcOverdueDays();
            } else {
                return 0L;
            }
        }

        if (this.getClientOverdueStart().getTime() < System.currentTimeMillis()) {
            return DateUtil.between(this.getClientOverdueStart(), new Date(), DateUnit.DAY);
        } else {
            return 0L;
        }

    }

    public Long getNotFollowed() {
        if (this.getFollowUpAst() == null) {
            return null;
        } else {
            if (this.getFollowUpAst().getTime() <System.currentTimeMillis()) {
                return DateUtil.between(this.getFollowUpAst(), new Date(), DateUnit.DAY);
            } else {
                return 0L;
            }
        }

        /*if (this.getFollowUpAst() == null && this.getAllocatedTime() == null) {
            return null;
        } else {
            if (this.getFollowUpAst() == null) {
                if (this.getAllocatedTime().getTime() < new Date().getTime())
                    return DateUtil.between(this.getAllocatedTime(), new Date(), DateUnit.DAY);
                else
                    return 0l;
            } else {
                if (this.getFollowUpAst().getTime() < new Date().getTime()) {
                    return DateUtil.between(this.getFollowUpAst(), new Date(), DateUnit.DAY);
                } else {
                    return 0L;
                }
            }
        }*/
    }

}
