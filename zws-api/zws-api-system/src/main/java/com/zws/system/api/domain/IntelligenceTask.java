package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 预测式外呼任务表-实体类
 *
 * @author: 马博新
 * @date ：Created in 2024/08/29 10:57
 */
@Data
public class IntelligenceTask {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 外呼日期，1-7，多个用逗号隔开
     */
    private String executionTime;

    /**
     * 外呼时段
     */
    private String executionCallTime;

    /**
     * 重呼次数
     */
    private Integer recallCount;

    /**
     * 重呼间隔
     */
    private Integer recallMinute;

    /**
     * 是否弹屏 1是 2否
     */
    private Integer isScreen;

    /**
     * 单次批量外呼数量
     */
    private Integer singleCallNumber;

    /**
     * 机构id
     */
    private Integer teamId;

    /**
     * 员工id
     */
    private Integer employeeId;

    /**
     * 状态 1未启动 2进行中 3已完成 4已暂停 5已撤销
     */
    private Integer status;

    /**
     * 任务呼叫总量
     */
    private Integer callCount;

    /**
     * 创建人id
     */
    private Integer createId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人类型（0-主账号;1-员工账号;2-资产端账号）
     */
    private Integer createType;

    /**
     * 费用
     */
    private BigDecimal fee;

    /**
     * 客户资料批次名称
     */
    private String customNameMontage;

    /**
     * 客户资料批次id，多个用逗号隔开
     */
    private String customIdMontage;

    /**
     * 任务分配人员id，多个逗号隔开
     */
    private String taskAllocationPersonnelId;

    /**
     * 任务分配人员坐席账号，多个逗号隔开
     */
    private String taskAllocationPersonnelSeating;

    /**
     * 接听设置（1-已分配案件只允许归属坐席接听；2-随机接听）
     */
    private Integer answerSettings;

    /**
     * 导入客户记录表id
     */
    private Long customId;

    /**
     * 呼叫中心uuid
     */
    private String callCenterUuid;

    /**
     * 呼叫中心uuid集合List
     */
    private List<String> callCenterUuidList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 操作类型（1-导入客户，2-案件生成）
     */
    private Integer operationType;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 呼叫对象（1-本人，2-全部号码）
     */
    private Integer callRecipient;

    /**
     * 任务进度
     */
    private BigDecimal taskProgress;

    /**
     * 接通率
     */
    private BigDecimal callCompletionRate;
//-------------------------------------------------------搜索条件---------------------------------------------------------

    /**
     * 员工id集合
     */
    private List<Integer> memberIdList;

    /**
     * 客户记录id集合
     */
    private List<Long> customReloadIdList;

    /**
     * 多选任务名称搜索
     */
    private List<String> taskNameList;

    /**
     * 创建时间-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime1;

    /**
     * 创建时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime2;

    /**
     * 任务状态(多选逗号拼接)
     */
    private String statusStr;

    /**
     * 多选任务状态搜索
     */
    private List<Integer> statusList;

    /**
     * 主键id集合
     */
    private List<Long> ids;

    /**
     * 通话状态 呼叫中心回写
     * 0=待拨打，1=已接通，2=未接通，3=漏接
     */
    private Integer connectFlag;

    /**
     * 通话状态2
     * 0=未接听 挂断+通话中+无人接听+停机+忙线+关机等呼叫到达被叫侧；
     */
    private Integer connectFlag2;

//--------------------------------------------------创建人姓名参数处理------------------------------------------------------

    /**
     * 员工名称
     */
    private String employeeName;

    /**
     * 机构名称
     */
    private String cname;

    public String getCreateBy() {
        if (createType != null) {
            if (createType == 0) createBy = cname;
            else if (createType == 1) createBy = employeeName;
        }
        return createBy;
    }

//-----------------------------------------------------机构企业信息--------------------------------------------------------

    /**
     * 企业API标识
     */
    private String apiFlag;

    /**
     * 企业编号
     */
    private String enterpriseNum;

    /**
     * 机构类型（0-自营，1-委外）
     */
    private Integer teamType;

    /**
     * 登录人信息
     */
    private ThreadEntityPojo entityPojo;
}