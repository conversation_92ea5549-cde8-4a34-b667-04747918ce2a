package com.zws.system.api.domain;

import com.zws.common.core.annotation.Excel;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 签章审批记录对象 case_sign_record
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
@Data
public class CaseSignRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 签章审批表id */
    private Long id;

    /** 申请id */
    private Long approveId;

    /** 团队id */
    @Excel(name = "团队id")
    private Long teamId;

    /** 案件id */
    @Excel(name = "案件id")
    private Long caseId;

    /** 审核状态 */
    @Excel(name = "审核状态")
    private String examineState;

    /** 登记人Id */
    @Excel(name = "登记人Id")
    private Long registrarId;

    /** 登记人 */
    @Excel(name = "登记人")
    private String registrar;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNum;

    /** 模版类型 */
    @Excel(name = "模版类型")
    private String modelType;

    /** 模版名称 */
    @Excel(name = "模版名称")
    private String modelName;

    /** 审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束，6-已撤销 */
    @Excel(name = "审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束，6-已撤销")
    private Long proce;

    /** 审核进程顺序 */
    @Excel(name = "审核进程顺序")
    private Long proceSort;

    /** 删除标记,0表示存在 */
    private String delFlag;

    /** 审核人id */
    @Excel(name = "审核人id")
    private Long updateById;

    /** 是否有效（0-有效，1-无效） */
    @Excel(name = "是否有效", readConverterExp = "0=-有效，1-无效")
    private Long invalid;

    /** 催收员id */
    @Excel(name = "催收员id")
    private Long odvId;

    /** 催收员名字 */
    @Excel(name = "催收员名字")
    private String odvName;

    /** 关联的财务账单id */
    @Excel(name = "关联的财务账单id")
    private Long billId;

    /** 申请人类型（0-主账号;1-员工账号;2-资产端账号） */
    @Excel(name = "申请人类型", readConverterExp = "0=-主账号;1-员工账号;2-资产端账号")
    private Long operationType;

    /** 对方机构 */
    @Excel(name = "对方机构")
    private String ycAccountAgency;

    /** 是否可以取消关联（null-可以，1-不可以） */
    @Excel(name = "是否可以取消关联", readConverterExp = "n=ull-可以，1-不可以")
    private Long whetherOperate;

    /** 是否为导入数据（null-不是，1-是） */
    @Excel(name = "是否为导入数据", readConverterExp = "n=ull-不是，1-是")
    private Long importNot;

    /**
     * 模版类型id
     */
    private String modelTypeId;

    /**
     * 函件批次id
     */
    private Integer letterId;

    public String getModelTypeId() {
        return modelTypeId;
    }

    public void setModelTypeId(String modelTypeId) {
        this.modelTypeId = modelTypeId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setTeamId(Long teamId)
    {
        this.teamId = teamId;
    }

    public Long getTeamId()
    {
        return teamId;
    }
    public void setCaseId(Long caseId)
    {
        this.caseId = caseId;
    }

    public Long getCaseId()
    {
        return caseId;
    }
    public void setExamineState(String examineState)
    {
        this.examineState = examineState;
    }

    public String getExamineState()
    {
        return examineState;
    }
    public void setRegistrarId(Long registrarId)
    {
        this.registrarId = registrarId;
    }

    public Long getRegistrarId()
    {
        return registrarId;
    }
    public void setRegistrar(String registrar)
    {
        this.registrar = registrar;
    }

    public String getRegistrar()
    {
        return registrar;
    }
    public void setBatchNum(String batchNum)
    {
        this.batchNum = batchNum;
    }

    public String getBatchNum()
    {
        return batchNum;
    }
    public void setModelType(String modelType)
    {
        this.modelType = modelType;
    }

    public String getModelType()
    {
        return modelType;
    }
    public void setModelName(String modelName)
    {
        this.modelName = modelName;
    }

    public String getModelName()
    {
        return modelName;
    }
    public void setProce(Long proce)
    {
        this.proce = proce;
    }

    public Long getProce()
    {
        return proce;
    }
    public void setProceSort(Long proceSort)
    {
        this.proceSort = proceSort;
    }

    public Long getProceSort()
    {
        return proceSort;
    }
    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }
    public void setUpdateById(Long updateById)
    {
        this.updateById = updateById;
    }

    public Long getUpdateById()
    {
        return updateById;
    }
    public void setInvalid(Long invalid)
    {
        this.invalid = invalid;
    }

    public Long getInvalid()
    {
        return invalid;
    }
    public void setOdvId(Long odvId)
    {
        this.odvId = odvId;
    }

    public Long getOdvId()
    {
        return odvId;
    }
    public void setOdvName(String odvName)
    {
        this.odvName = odvName;
    }

    public String getOdvName()
    {
        return odvName;
    }
    public void setBillId(Long billId)
    {
        this.billId = billId;
    }

    public Long getBillId()
    {
        return billId;
    }
    public void setOperationType(Long operationType)
    {
        this.operationType = operationType;
    }

    public Long getOperationType()
    {
        return operationType;
    }
    public void setYcAccountAgency(String ycAccountAgency)
    {
        this.ycAccountAgency = ycAccountAgency;
    }

    public String getYcAccountAgency()
    {
        return ycAccountAgency;
    }
    public void setWhetherOperate(Long whetherOperate)
    {
        this.whetherOperate = whetherOperate;
    }

    public Long getWhetherOperate()
    {
        return whetherOperate;
    }
    public void setImportNot(Long importNot)
    {
        this.importNot = importNot;
    }

    public Long getImportNot()
    {
        return importNot;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teamId", getTeamId())
            .append("caseId", getCaseId())
            .append("examineState", getExamineState())
            .append("registrarId", getRegistrarId())
            .append("registrar", getRegistrar())
            .append("batchNum", getBatchNum())
            .append("modelType", getModelType())
            .append("modelName", getModelName())
            .append("createTime", getCreateTime())
            .append("proce", getProce())
            .append("proceSort", getProceSort())
            .append("delFlag", getDelFlag())
            .append("updateById", getUpdateById())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("invalid", getInvalid())
            .append("odvId", getOdvId())
            .append("odvName", getOdvName())
            .append("billId", getBillId())
            .append("operationType", getOperationType())
            .append("ycAccountAgency", getYcAccountAgency())
            .append("whetherOperate", getWhetherOperate())
            .append("importNot", getImportNot())
            .toString();
    }
}
