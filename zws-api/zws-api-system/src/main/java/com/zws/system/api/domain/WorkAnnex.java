package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 工单进度上传附件实体类
 *
 * @Author: 马博新
 * @DATE: Created in 2022/9/29 14:25
 */
@Data
public class WorkAnnex {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 工单id
     */
    private Long workId;

    /**
     * 工单进度id
     */
    private Long followId;

    /**
     * 文件url路径
     */
    private String fileUrl;

    /**
     * 原文件名
     */
    private String fileName;

    /**
     * 跟进人id
     */
    private Long createdById;

    /**
     * 跟进人
     */
    private String createdBy;

    /**
     * 跟进时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**
     * 删除标志 (0-未删除；1-已删除)
     */
    private String delFlag;
}
