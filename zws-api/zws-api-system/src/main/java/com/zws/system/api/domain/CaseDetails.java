package com.zws.system.api.domain;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 案件详情
 * <AUTHOR>
 * @date ：Created in 2022/2/17 9:10
 */
@Data
public class CaseDetails {

    /**
     * 案件基础信息
     */
    private Map<String,Object> infoBase;
    /**
     * 贷款信息
     */
    private InfoLoan infoLoan;
    /**
     * 共债信息
     */
    private List<JointDebtInfo> jointDebtInfo;
    /**
     * 还款计划
     */
    private List<InfoPlan>  infoPlan;
    /**
     * 附加信息
     */
    private List<Map<String,Object>> infoExtras;

    /**
     * 账户信息
     */
    private List<Account>  accounts;
    /**
     * 共债案件数量
     */
    private Integer count;


}
