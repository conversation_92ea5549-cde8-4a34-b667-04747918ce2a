package com.zws.system.api.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 案件信息-基本信息
 */
@Data
public class InfoBase extends BaseEntity {

    private Long id;
    /**
     * 案件ID
     */
    private Long caseId;
    /**
     * 姓名
     */
    private String clientName;
    /**
     * 姓名-加密
     */
    private String clientNameEnc;
    /**
     * 证件类型
     */
    private String clientIdType;
    /**
     * 身份证号
     */
    private String clientIdNum;

    /**
     * 身份证号-加密
     */
    private String clientIdNumEnc;

    /**
     * 户籍地
     */
    private String clientCensusRegister;
    /**
     * 户籍地-加密
     */
    private String clientCensusRegisterEnc;
    /**
     * 电话
     */
    private String clientPhone;
    /**
     * 电话-加密
     */
    private String clientPhoneEnc;
    /**
     * 性别
     */
    private String clientSex;
    /**
     * 年龄
     */
    private Long clientAge;
    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date clientBirthday;
    /**
     * 职业
     */
    private String occupation;
    /**
     * 婚姻状况
     */
    private String maritalStatus;
    /**
     * QQ
     */
    private String qq;
    /**
     * 微信
     */
    private String weixin;
    /**
     * 邮箱
     */
    private String mailbox;
    /**
     * 工作单位
     */
    private String placeOfWork;
    /**
     * 单位地址
     */
    private String workingAddress;
    /**
     * 户籍地址
     */
    private String registeredAddress;
    /**
     * 居住地址
     */
    private String residentialAddress;
    /**
     * 家庭地址
     */
    private String homeAddress;
    /**
     * 银行卡号
     */
    private String bankCardNumber;
    /**
     * 开户行-银行名称
     */
    private String bankName;

    /**
     * 担保人
     */
    private String securityName;
    /**
     * 担保人-加密
     */
    private String securityNameEnc;
    /**
     * 担保人证件类型
     */
    private String securityIdType;
    /**
     * 担保人证件号码
     */
    private String securityIdNum;
    /**
     * 担保人证件号码-加密
     */
    private String securityIdNumEnc;
    /**
     * 担保人电话
     */
    private String securityPhone;
    /**
     * 担保人电话-加密
     */
    private String securityPhoneEnc;

    /**
     * 行政编码
     */
    private String administrativeBi;

    /**
     * 资产编号
     */
    private String assetNo;

    /**
     * UID
     */
    private String uid;

    /**
     * 学历
     */
    private String education;

    /**
     * 学位
     */
    private String academicDegree;

    /**
     * 就业状态
     */
    private String employmentStatus;

    /**
     * 居住状态
     */
    private String residentialStatus;

    /**
     * 家庭电话
     */
    private String homePhone;

    /**
     * 职务
     */
    private String duties;

    /**
     * 职称
     */
    private String title;

    /**
     * 本单位工作起始年份
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date unitStartYear;

    /**
     * 单位所属行业
     */
    private String unitIndustry;

    /**
     * 单位所在地邮编
     */
    private String unitPostalCode;

    /**
     * 单位电话
     */
    private String unitTelephone;

    /**
     * 居住地邮编
     */
    private String residencePostalCode;

    private String delFlag;

    /**
     * 五级分类（国瑞暂用字段）
     */
    private String ycFiveLevel;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCaseId() {
        return caseId;
    }

    public void setCaseId(Long caseId) {
        this.caseId = caseId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName == null ? null : clientName.trim();
    }

    public String getClientNameEnc() {
        return clientNameEnc;
    }

    public void setClientNameEnc(String clientNameEnc) {
        this.clientNameEnc = clientNameEnc == null ? null : clientNameEnc.trim();
    }

    public String getClientIdNum() {
        return clientIdNum;
    }

    public void setClientIdNum(String clientIdNum) {
        this.clientIdNum = clientIdNum == null ? null : clientIdNum.trim();
    }

    public String getClientIdNumEnc() {
        return clientIdNumEnc;
    }

    public void setClientIdNumEnc(String clientIdNumEnc) {
        this.clientIdNumEnc = clientIdNumEnc == null ? null : clientIdNumEnc.trim();
    }

    public String getClientCensusRegister() {
        return clientCensusRegister;
    }

    public void setClientCensusRegister(String clientCensusRegister) {
        this.clientCensusRegister = clientCensusRegister == null ? null : clientCensusRegister.trim();
    }

    public String getClientCensusRegisterEnc() {
        return clientCensusRegisterEnc;
    }

    public void setClientCensusRegisterEnc(String clientCensusRegisterEnc) {
        this.clientCensusRegisterEnc = clientCensusRegisterEnc == null ? null : clientCensusRegisterEnc.trim();
    }

    public String getClientPhone() {
        return clientPhone;
    }

    public void setClientPhone(String clientPhone) {
        this.clientPhone = clientPhone == null ? null : clientPhone.trim();
    }

    public String getClientPhoneEnc() {
        return clientPhoneEnc;
    }

    public void setClientPhoneEnc(String clientPhoneEnc) {
        this.clientPhoneEnc = clientPhoneEnc == null ? null : clientPhoneEnc.trim();
    }

    public String getClientSex() {
        return clientSex;
    }

    public void setClientSex(String clientSex) {
        this.clientSex = clientSex == null ? null : clientSex.trim();
    }

    public Long getClientAge() {
        return clientAge;
    }

    public void setClientAge(Long clientAge) {
        this.clientAge = clientAge;
    }

    public Date getClientBirthday() {
        return clientBirthday;
    }

    public void setClientBirthday(Date clientBirthday) {
        this.clientBirthday = clientBirthday;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation == null ? null : occupation.trim();
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus == null ? null : maritalStatus.trim();
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq == null ? null : qq.trim();
    }

    public String getWeixin() {
        return weixin;
    }

    public void setWeixin(String weixin) {
        this.weixin = weixin == null ? null : weixin.trim();
    }

    public String getMailbox() {
        return mailbox;
    }

    public void setMailbox(String mailbox) {
        this.mailbox = mailbox == null ? null : mailbox.trim();
    }

    public String getPlaceOfWork() {
        return placeOfWork;
    }

    public void setPlaceOfWork(String placeOfWork) {
        this.placeOfWork = placeOfWork == null ? null : placeOfWork.trim();
    }

    public String getWorkingAddress() {
        return workingAddress;
    }

    public void setWorkingAddress(String workingAddress) {
        this.workingAddress = workingAddress == null ? null : workingAddress.trim();
    }

    public String getResidentialAddress() {
        return residentialAddress;
    }

    public void setResidentialAddress(String residentialAddress) {
        this.residentialAddress = residentialAddress == null ? null : residentialAddress.trim();
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress == null ? null : homeAddress.trim();
    }

    public String getBankCardNumber() {
        return bankCardNumber;
    }

    public void setBankCardNumber(String bankCardNumber) {
        this.bankCardNumber = bankCardNumber == null ? null : bankCardNumber.trim();
    }

    /**
     * 检查必要字段
     *
     * @return
     */
    public boolean check() {
        if (StringUtils.isEmpty(this.clientName)) {
            throw new GlobalException("姓名不能为空");
        }
        if (StringUtils.isEmpty(this.clientIdNum)) {
            throw new GlobalException("证件号码不能为空");
        }
        if (StringUtils.isEmpty(this.clientPhone)) {
            throw new GlobalException("手机号码不能为空");
        }
        if (StringUtils.isNotEmpty(this.clientSex)) {
            if (this.clientSex.length() > 2) {
                throw new GlobalException("性别错误");
            }
        }
        return true;
    }

    @Override
    public String toString() {
        return "InfoBase{" +
                "id=" + id +
                ", caseId=" + caseId +
                ", clientName='" + clientName + '\'' +
                ", clientNameEnc='" + clientNameEnc + '\'' +
                ", clientIdNum='" + clientIdNum + '\'' +
                ", clientIdNumEnc='" + clientIdNumEnc + '\'' +
                ", clientCensusRegister='" + clientCensusRegister + '\'' +
                ", clientCensusRegisterEnc='" + clientCensusRegisterEnc + '\'' +
                ", clientPhone='" + clientPhone + '\'' +
                ", clientPhoneEnc='" + clientPhoneEnc + '\'' +
                ", clientSex='" + clientSex + '\'' +
                ", clientAge=" + clientAge +
                ", clientBirthday=" + clientBirthday +
                ", occupation='" + occupation + '\'' +
                ", maritalStatus='" + maritalStatus + '\'' +
                ", qq='" + qq + '\'' +
                ", weixin='" + weixin + '\'' +
                ", mailbox='" + mailbox + '\'' +
                ", placeOfWork='" + placeOfWork + '\'' +
                ", workingAddress='" + workingAddress + '\'' +
                ", residentialAddress='" + residentialAddress + '\'' +
                ", homeAddress='" + homeAddress + '\'' +
                ", bankCardNumber='" + bankCardNumber + '\'' +
                ", bankName='" + bankName + '\'' +
                '}';
    }
}
