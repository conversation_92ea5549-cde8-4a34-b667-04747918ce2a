package com.zws.system.api.factory;

import com.zws.common.core.domain.R;
import com.zws.system.api.RemoteQualityService;
import com.zws.system.api.domain.EmailAccount;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/5/6 下午2:43
 */
@Component
public class RemoteQualityFallbackFactory implements FallbackFactory<RemoteQualityService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteQualityFallbackFactory.class);
    @Override
    public RemoteQualityService create(Throwable throwable) {
        log.error("质检服务调用失败:{}", throwable.getMessage());
        return new RemoteQualityService(){

            @Override
            public R<EmailAccount> getByPrimaryKey(Long id) {
                return R.fail("根据id获取质检邮箱失败:" + throwable.getMessage());
            }

            @Override
            public R<List<EmailAccount>> getListByUseType(Integer useType) {
                return R.fail("根据类型获取质检邮箱失败:" + throwable.getMessage());
            }
        };
    }
}
