package com.zws.system.api;

import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.domain.SysUser;
import com.zws.system.api.domain.dto.ReviewProgressDto;
import com.zws.system.api.domain.dto.ReviewProgressRequestDto;
import com.zws.system.api.factory.RemoteUserFallbackFactory;
import com.zws.system.api.model.LoginUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 审批服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteProceService", value = ServiceNameConstants.CASE_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteProceService {
    /**
     * 获取审批进度信息
     *
     * @param approveCode 审核类型
     * @param applyId 申请ID
     */
    @GetMapping(value = "/approve/getReviewProgress/{approveCode}/{applyId}")
    public R<List<ReviewProgressDto>> getReviewProgress(@PathVariable("approveCode") Integer approveCode,
                                                        @PathVariable("applyId")Long applyId);

    /**
     * 批量获取审批进度信息
     *
     * @param reviewProgressRequestDto
     */
    @PostMapping(value = "/approve/getReviewProgressBatch")
    public R<Map<Integer, List<ReviewProgressDto>>> getReviewProgressBatch(@RequestBody ReviewProgressRequestDto reviewProgressRequestDto);

    /**
     * 获取设置的资料链接有效期
     * @return
     */
    @GetMapping("/setup/legal/getDataRetrieval")
    public AjaxResult infoDataRetrieval();


}
