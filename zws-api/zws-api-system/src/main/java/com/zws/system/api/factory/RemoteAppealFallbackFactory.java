package com.zws.system.api.factory;

import com.zws.common.core.domain.R;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.RemoteAppealService;
import com.zws.system.api.RemoteCisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


@Component
public class RemoteAppealFallbackFactory implements FallbackFactory<RemoteAppealService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteAppealFallbackFactory.class);

    @Override
    public RemoteAppealService create(Throwable cause) {
        log.error("saas 调诉服务调用失败:{}", cause.getMessage());
        return new RemoteAppealService() {

            @Override
            public AjaxResult selectMapWithCaseDetailsCis(Long caseId) {
                return AjaxResult.error("调诉端查询案件信息："+ cause.getMessage());
            }
        };
    }
}
