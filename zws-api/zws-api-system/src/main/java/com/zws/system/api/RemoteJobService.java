package com.zws.system.api;

import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.system.api.domain.Job;
import com.zws.system.api.factory.RemoteJobFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 远程定时任务服务
 * <AUTHOR>
 * @date ：Created in 2022/6/7 17:00
 */
@FeignClient(contextId = "remoteJobService", value = ServiceNameConstants.JOB_SERVICE, fallbackFactory = RemoteJobFallbackFactory.class)
public interface RemoteJobService {

    /**
     * 添加定时任务
     * @param job
     * @param source
     * @return
     */
    @PostMapping("/job")
    R  addJob(@RequestBody Job job,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}
