package com.zws.system.api.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 消息删除 DTO
 *
 * @PackageName: com.zws.system.api.domain.dto
 * @ClassName: MessageDeleteDto
 * @Description: //TODO
 * @Author: Aasee
 * @Company: M+科技&深圳市债卫士信息科技有限公司
 * @website: www.amcmj.com
 * @Date: 2025/3/18 20:50
 */

@Data
public class MessageDeleteDto {
    private List<Long> ids;
    private Long userId;
    private Integer type;
}