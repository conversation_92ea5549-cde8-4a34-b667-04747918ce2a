package com.zws.system.api.factory;

import com.zws.common.core.domain.R;
import com.zws.common.core.domain.SmsRequest;
import com.zws.common.core.domain.sms.SendChuWuVo;
import com.zws.common.core.domain.sms.SendDanMiVo;
import com.zws.common.core.domain.sms.SendMessagePojo;
import com.zws.common.core.domain.sms.SendZcMessageVo;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.RemoteSmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 远程短信回退工厂
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
@Component
public class RemoteSmsFallbackFactory implements FallbackFactory<RemoteSmsService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteSmsFallbackFactory.class);

    @Override
    public RemoteSmsService create(Throwable cause) {
        log.error("短信服务调用失败:{}", cause.getMessage());
        return new RemoteSmsService() {
            @Override
            public R sendSmsMarketing(SmsRequest smsRequest) {
                return R.fail("发送短信服务调用失败:" + cause.getMessage());
            }

            @Override
            public R sendMessage(SendDanMiVo sendDanMiVo) {
                return R.fail("资产端发送短信服务调用失败:" + cause.getMessage());
            }

            @Override
            public R sendCuiShouMessage(String phone, String autographName, Long templateId, Integer smsType, String username, Long userId, Long caseId, String clientName, Long overdueDays, BigDecimal remainingDue) {
                return R.fail("催收端发送短信服务调用失败:" + cause.getMessage());
            }

            @Override
            public R createTemplate(SmsRequest smsRequest) {
                return R.fail("创建模板服务调用失败:" + cause.getMessage());
            }

            @Override
            public R updateTemplate(SmsRequest smsRequest) {
                return R.fail("修改模板服务调用失败:" + cause.getMessage());
            }

            @Override
            public R deleteTemplate(SmsRequest smsRequest) {
                return R.fail("删除模板服务调用失败:" + cause.getMessage());
            }

            @Override
            public R sendZcMessage(SendZcMessageVo sendZcMessage) {
                return R.fail("资产端发送单个短信服务调用失败:" + cause.getMessage());
            }

            @Override
            public R sendChuWuMessage(SendChuWuVo sendChuWuVo) {
                return R.fail("资产端发送短信服务调用失败:" + cause.getMessage());
            }

            @Override
            public R sendAppealMessages(SendMessagePojo sendMessagePojo) {
                return R.fail("调诉端发送短信服务调用失败:" + cause.getMessage());
            }
//            @Override
//            public R sendCuiShouMessage(String phone,String autographName,Long templateId,Integer smsType,String username,Long userId, Long caseId, String clientName,Long overdueDays,BigDecimal remainingDue) {
//                return R.fail("资产服务查询调用失败:" + cause.getMessage());
//            }


        };
    }
}
