package com.zws.system.api.factory;

import com.zws.common.core.domain.R;
import com.zws.common.core.domain.seal.SealParam;
import com.zws.system.api.RemoteAppService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;

public class RemoteAppFallbackFactory implements FallbackFactory<RemoteAppService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteAppFallbackFactory.class);

    @Override
    public RemoteAppService create(Throwable cause) {
        log.error("小程序服务调用失败:{}", cause.getMessage());
        return new  RemoteAppService() {
            @Override
            public R<String> officialSealSigned(SealParam sealParam) {
                return R.fail("企业合同签署失败:" + cause.getMessage());
            }
        };
    }
}
