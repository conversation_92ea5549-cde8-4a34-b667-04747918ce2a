package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.domain.seal.SealStamp;
import com.zws.common.core.signature.Stamp;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * 函件模板
 * @date 2023年11月23日15:36:59
 * <AUTHOR>
 */
@Data
public class LetterTemplate extends BaseEntity {

    private Integer id;
    /**
     * 模板编号
     */
    @NotEmpty(message = "模板编号不能为空")
    private String templateCode;
    /**
     * 模板类型id
     */
    private Integer classifyId;
    /**
     * 模板类型标签
     */
    private String classifyLabel;
    /**
     *  模板名称
     */
    @NotEmpty(message = "模板名称 不能为空")
    private String templateName;
    /**
     *  标签，分号(;)分隔
     */
    private String templateLabel;
    /**
     * 状态，0-正常(启用)，1-禁用
     */
    private Integer status;
    /**
     * 模板变量
     */
    private String templateVariable;
    /**
     * 签章机构(律所id)
     */
    private Long lawId;
    /**
     * 代理律师(签章id)
     */
    private Long signId;
    /**
     * zip压缩包导入模板pdf(原路径_未盖章)
     */
    private String originalUrl;
    /**
     *  模板预览url
     */
    private String previewUrl;
    /**
     * 模板预览的PDF页数
     */
    private Integer previewPages;
    /**
     * 页眉图片路径(非必填)
     */
    private String pageHeader;
    /**
     * 页脚图片路径(非必填)
     */
    private String pageFooter;
    /**
     * 裁剪页眉路径(如未裁剪则存原图路径)
     */
    private String cutHeader;
    /**
     * 裁剪页脚路径(如未裁剪则存原图路径)
     */
    private String cutFooter;
    /**
     *  模板类型
     *  0-Excel文件类型;1-Zip文件类型
     */
    private Integer sourceFileType;
    /**
     * 团队、机构id
     */
    private Long teamId;
    /**
     * 正文内容
     */
    private String bodyContent;
    /**
     * 签章定位数据(xy坐标,盖章页码,盖章图片)
     */
    private String positionData;
    /**
     *  类型（0-函件 1-文书,2-结清模板，3-分期模板，4-平台模板）
     *  @see com.zws.common.core.enums.letter.LetterTypeEnum
     */
    private String type;

    private Integer createById;

    private Integer updateById;

    private String delFlag;


    /**
     * 签章定位数据,接收前端传定位数据
     *(非数据库字段)
     */
    private List<Stamp> positionList;

    /**
     * 页眉原图缩放后的路径
     * （非数据库字段）
     */
    private  String scaleHeader;
    /**
     * 页脚原图缩放后的路径
     * （非数据库字段）
     */
    private  String scaleFooter;



    /**
     * 模板类型名称
     */
    private String classifyName;

    /**
     * 查询参数-模板类型
     */
    private List<String> classifyIds;


    /**
     * 查询参数-模板名称
     */
    private List<String> templateNames;
    /**
     * 查询参数-模板id集合
     */
    private List<Long> templateIds;
    /**
     * 查询参数-创建人
     */
    private String createBy;
    /**
     * 查询参数-创建人
     */
    private List<Long> createByIds;

    /**
     * 不包含id
     */
    private Long notId;
    /**
     * 代理律师(签章id) 是否设置为null
     */
    private Boolean signIdNull;

    /**
     * zip模板编辑回显、截取previewUrl 作为文件名
     * (非数据库字段)
     */
    private String fileName;

    /**
     * 底部内容
     */
    private String tailContent;



    private Integer tenantId;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /**
     * 查询参数-创建人
     */
    private List<String> createBys;


    /**
     * 短信模板id
     */
    private String smsTemplateId;

    /**
     * 短信模板内容
     */
    private String smsTemplateContent;

    /**
     * cfca签章坐标
     */
    private List<SealStamp> sealPositionList;

    /**
     * 审批id
     */
    private Long approveId;

    /**
     * 是否通过审核( 1、待审核，2、审核成功，3、审核失败)
     */
    private Integer examine;

    /**
     * 审核不通过原因
     */
    private String reason;
}
