package com.zws.system.api.config;

import feign.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * feign 配置
 *
 * <AUTHOR>
 * @date ：Created in 2022/12/1 16:29
 */
@Configuration
public class FeignConfig {

    /**
     * OpenFeign日志级别：
     *      NONE: 不记录任何日志，是OpenFeign默认日志级别（性能最佳，适用于生产环境）
     *      BASIC: 仅记录请求方法、URL、响应状态码、执行时间（适用于生产环境追踪问题）
     *      HEADERS: 在记录BASIC级别的基础上，记录请求和响应的header头部信息
     *      FULL: 记录请求响应的header、body 和 元数据（适用于开发和测试环境定位问题）
     */
    @Bean
    public Logger.Level feignLoggerLevel(){
        return Logger.Level.FULL;
    }

}
