package com.zws.system.api;

import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.system.api.domain.Notice;
import com.zws.system.api.domain.TeamMessageCenter;
import com.zws.system.api.domain.TeamUserMessage;
import com.zws.system.api.domain.UserMessage;
import com.zws.system.api.domain.dto.MessageDeleteDto;
import com.zws.system.api.factory.RemoteMessageFallbackFactory;
import com.zws.system.api.pojo.CallMessage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/5/31 18:07
 */
@FeignClient(contextId = "remoteMessageService", value = ServiceNameConstants.ZWS_MESSAGE, fallbackFactory = RemoteMessageFallbackFactory.class)
public interface RemoteMessageService {

    /**
     * 创建团队系统消息\公告
     *
     * @return
     */
    @PostMapping("/team/messageCenter/add")
    R<Long> createMessageCenter(@RequestBody TeamMessageCenter teamMessageCenter, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 资产端-发布系统公告
     *
     * @param noticeId
     * @return
     */
    @PostMapping("/team/messageCenter/publishTeamNotice")
    R publishTeamNotice(Long noticeId);

    /**
     * 新增并推送催收端用户消息
     * @param teamUserMessage
     * @return
     */
    @PostMapping("/team/userMessage/push")
    R pussTeamUserMessage(@RequestBody  List<TeamUserMessage> teamUserMessages,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 新增并推送资本端用户消息
     * @param userMessages
     * @return
     */
    @PostMapping("/asset/userMessage/pushs")
    R pushAssetUserMessage(@RequestBody List<UserMessage> userMessages, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 消息列表
     *
     * @param param
     * @return
     */
    @PostMapping("/team/getList")
    TableDataInfo getList(@RequestBody TeamUserMessage param, @RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize);


    /**
     * 添加用户消息
     *
     * @param teamUserMessages
     * @return
     */
    @PostMapping("/team/insert")
    R insertUserMessage(@RequestBody List<TeamUserMessage> teamUserMessages, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取导航信息
     * 获取当前最新的5条信息
     *
     * @return
     */
    @GetMapping("/team/getNavMessage")
    R<List<TeamUserMessage>> getNavMessage();

    /**
     * 标记已读
     *
     * @return
     */
    @PostMapping("/team/markRead")
    R markRead(@RequestBody List<Long> ids, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 删除信息
     *
     * @param ids
     * @return
     */
    @PostMapping("/team/delete")
    R delete(@RequestBody List<Long> ids,  @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 删除信息
     *
     * @param messageDeleteDto
     * @return
     */
    @PostMapping("/team/deleteByDto")
    R delete(@RequestBody MessageDeleteDto messageDeleteDto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取用户信息-系统公告详情
     *
     * @return
     */
    @GetMapping("/team/getUserNotice")
    R<TeamMessageCenter> getUserNotice(@RequestParam("id") Long id, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取未读信息数量
     *
     * @return
     */
    @GetMapping("/team/getUnreadQuantity")
    R<Integer> getUnreadQuantity();




    /**
     * 资产端-创建系统消息\公告
     *
     * @param notice
     * @param source
     * @return
     */
    @PostMapping("/asset/notice/add")
    R<Long> createNotice(@RequestBody Notice notice, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 资产端- 发布系统公告
     *
     * @param noticeId
     * @return
     */
    @PostMapping("/asset/notice/publishAssetNotice")
    R publishAssetNotice(@RequestBody Long noticeId);

    /**
     * 发布消息
     *
     * @param noticeId
     * @return
     */
    @PostMapping("/asset/notice/publish")
    R publish(@RequestBody Long noticeId);

    /**
     * 新增并推送资产端用户消息
     * @param userMessage
     * @param source
     * @return
     */
    @PostMapping("/asset/userMessage/push")
    R pushAssetUserMessage(@RequestBody UserMessage userMessage,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    //----------推送呼叫信息---------------------------------------------------------------------

    /**
     * 推送通话结束
     * @param callMessage
     * @param source
     * @return
     */
    @PostMapping("/team/call/pushCallEnd")
    R pushCallEnd(@RequestBody CallMessage callMessage, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 推送通话呼叫中
     * @param callMessage
     * @param source
     * @return
     */
    @PostMapping("/team/call/pushOnCall")
    R pushOnCall(@RequestBody CallMessage callMessage, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 推送通话中
     * @param callMessage
     * @param source
     * @return
     */
    @PostMapping("/team/call/pushCallIng")
    R pushCallIng(@RequestBody CallMessage callMessage, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 推送预测试外呼呼入弹窗提醒-（导入客户）
     *
     * @param callMessage
     * @param source
     * @return
     */
    @PostMapping("/team/call/pushCallPopUpScreen")
    R pushCallPopUpScreen(@RequestBody CallMessage callMessage, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 推送预测试外呼呼入弹窗提醒-（勾选案件）
     *
     * @param callMessage
     * @param source
     * @return
     */
    @PostMapping("/team/call/pushCaseCallPopUpScreen")
    R pushCaseCallPopUpScreen(@RequestBody CallMessage callMessage, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 推送预测试外呼呼入弹窗提醒-（勾选案件）
     *
     * @param userId
     * @param phone
     * @return
     */
    @PostMapping("/team/call/pushWorkPhoneCaseCallPopUpScreen")
    R pushWorkPhoneCaseCallPopUpScreen(@RequestParam("userid") String userId, @RequestParam("phone")String phone);


}
