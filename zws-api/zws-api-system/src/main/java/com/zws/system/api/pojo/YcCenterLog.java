package com.zws.system.api.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 中央认证接口调用日志信息实体类
 *
 * @Author: 马博新
 * @DATE: Created in 2023/7/24 19:35
 */
@Data
public class YcCenterLog {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 临时票据
     */
    private String ticket;

    /**
     * 登录名/用户名
     */
    private String loginName;

    /**
     * 服务令牌
     */
    private String serviceToken;

    /**
     * 登录用户标识（用户名）CAS广播退出参数
     */
    private String loginId;

    /**
     * 接口类型描述
     */
    private String type;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 调用异常错误信息
     */
    private String error;

    /**
     * 登出是否成功（true-成功；false-失败）
     */
    private String states;
}
