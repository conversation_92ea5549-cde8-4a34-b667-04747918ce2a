package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zws.common.core.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 函件-一个函件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-04 20:38
 */
@Data
@Accessors(chain = true)
public class Letter {
    /**
     * 当前记录起始索引
     */
    private Integer pageNum;

    /**
     * 每页显示记录数
     */
    private Integer pageSize;
    /**
     * 函件项
     */
    private Integer id;

    /**
     * 函件批次id
     */
    private Integer letterId;

    /**
     * 序列号(系统单号)函件单号
     */
    @Excel(name = "函件单号", cellType = Excel.ColumnType.STRING,sort=1)
    private String serialNo;

    /**
     * 发函批次号
     */
    @Excel(name = "批次号", cellType = Excel.ColumnType.STRING,sort=1)
    private String batchNum;

    /**
     * 状态，0-待审核，1-审核中，2-已通过，3-未通过
     */
    @Excel(name = "审核状态", cellType = Excel.ColumnType.STRING,readConverterExp = "0=待审核,1=审核中,2=已签章,3=已驳回",sort=2)
    private Integer status;

    /**
     * 函件项数据
     */
    private String itemData;

    /**
     * 预览url
     */
    private String previewUrl;

    /**
     * 签章之后的预览url
     */
    private String signPreviewUrl;

    /**
     * PDF页数
     */
    private Integer previewPages;

    /**
     * 审核进程 0-待审核，1-审核中，2-已结束
     */
    private Integer proce;

    /**
     * 审核进程顺序
     */
    private Integer proceSort;

    /**
     * 签章时间
     */
    @Excel(name = "最后处理时间",dateFormat = "yyyy/MM/dd HH:mm:ss", cellType = Excel.ColumnType.STRING,sort=5)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date examineTime;
    /**
     * 签章时间查询-开始时间
     */
    private String examineTime1;
    /**
     * 签章时间查询-结束时间
     */
    private String examineTime2;



    /**
     * 最后审核人
     */
    @Excel(name = "最后处理人", cellType = Excel.ColumnType.STRING,sort=4)
    private String examineBy;

    /**
     * 最后审核人id
     */
    private Integer examineById;

    private Integer tenantId;

    private String createBy;

    @JsonSerialize(using = ToStringSerializer.class)
    private Integer createById;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;

    @JsonSerialize(using = ToStringSerializer.class)
    private Integer updateById;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String delFlag;

    /**
     * 签章错误信息
     */
    private String signErrMsg;

    /**
     * 0:Excel类型  1:Zip类型
     * 批量导入的源文件类型
     */
    private Integer sourceFileType;
    /**
     * 邮寄状态:待邮寄0、已邮寄1、已签收2 、已拒收3
     */
    @Excel(name = "物流跟踪", cellType = Excel.ColumnType.STRING,readConverterExp = "0=待邮寄,1=已邮寄,2=已签收,3=已拒收",sort=12)
    private Integer mailingStatus;

    /**
     * 生成签章文件后脱敏文件url
     */
    private String desensitizeUrl;

    /**
     * 辨别真伪网址
     * (脱敏文件链接)
     */
    @Excel(name = "辨真伪网址", cellType = Excel.ColumnType.STRING,sort=7)
    private String fileLink;

    /**
     * 链接访问数量
     */
    @Excel(name = "辨真伪网址访问次数", cellType = Excel.ColumnType.STRING,sort=8)
    private Integer visitNum;

    /**
     * 链接首次访问时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstVisitTime;

    /**
     * 发送状态(0-发送中,1-发送失败,2-发送成功)
     */
    @Excel(name = "短信发送状态", cellType = Excel.ColumnType.STRING, readConverterExp = "0=已发送,1=发送失败,2=发送成功",sort=6)
    private Integer sendStatus;
    /**
     * 短信发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date  sendTime;
    /**
     * 短信提交响应状态码说明(含有发送失败原因等内容)
     */
    private String respdesc;

    @Excel(name = "寄送方式", cellType = Excel.ColumnType.STRING,sort=9)
    private String deliveryWay;

    @Excel(name = "快递单号", cellType = Excel.ColumnType.STRING,sort=10)
    private String expressNumber;

    @Excel(name = "收件地址", cellType = Excel.ColumnType.STRING,sort=11)
    private String address;

    /**
     * 脱敏文件链接_拼接的随机字符串
     */
    private String randomId;

    /**
     * id集合
     * 查询参数
     */
    private List<Integer> ids;

    /**
     * 状态集合
     * 查询参数
     */
    private List<Integer> signStatusList;
    /**
     * 函件批次id集合
     * 查询参数
     */
    private List<Integer> letterIds;
    /**
     * 拒绝理由
     * 传值参数
     */
    @JsonIgnore
    private String refuseReason;

    /**
     * 处理状态
     * 0-待处理，2-已通过，3-不通过，null-全部
     */
    @JsonIgnore
    private Integer proceStatus;

    /**
     * 模板名称
     */
    @Excel(name = "模板名称", cellType = Excel.ColumnType.STRING,sort=3)
    private String templateName;

    /**
     * 短信模版（未发送状态）
     */
    private String smsTemplateContent;

    /**
     * 短信模版（已发送状态）
     */
    private String smsContent;

    /**
     * 签章状态默认0为签章中，1为已签章, 2-签章失败
     */
    private Integer signStatus;

    /**
     * 函件类型
     * 类型（0-函件 1-文书）
     */
    private String type;

    /**
     * 邮寄方式集合
     * 非数据库字段
     */
    private List<String> deliveryWayList;

    /**
     * 短信发送状态集合
     * 非数据库字段
     */
    private List<Integer> sendStatusList;


}
