package com.zws.system.api.factory;

import com.zws.common.core.domain.R;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.system.api.RemoteCisService;
import com.zws.system.api.domain.AiVoiceTask;
import com.zws.system.api.domain.IntelligenceTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/7 14:38
 */
@Component
public class RemoteCisFallbackFactory implements FallbackFactory<RemoteCisService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteCisFallbackFactory.class);

    @Override
    public RemoteCisService create(Throwable cause) {
        log.error(" saas 案件导入服务调用失败:{}", cause.getMessage());
        return new RemoteCisService() {
            @Override
            public R executeWorkTask(Long taskId, String source) {
                return R.fail("执行录音下载任务 失败:" + cause.getMessage());
            }

            @Override
            public R<List<Integer>> verifyCaseList(ThreadEntityPojo entityPojo, String source) {
                return R.fail("匹配预测试外呼坐席员工信息 失败:" + cause.getMessage());
            }

            @Override
            public R<List<Integer>> verifyMediatorCaseList(ThreadEntityPojo entityPojo, String source) {
                return R.fail("匹配调诉端预测试外呼坐席员工信息 失败:" + cause.getMessage());
            }

            @Override
            public R<List<Integer>> verifyStageCaseList(ThreadEntityPojo entityPojo, String source) {
                return R.fail("匹配保全预测试外呼坐席员工信息 失败:" + cause.getMessage());
            }

            @Override
            public R caseSubmitTaskData(IntelligenceTask task, String source) {
                return R.fail("创建预测试外呼任务 失败:" + cause.getMessage());
            }

            @Override
            public R<Integer> AiVoiceCaseSubmitTaskData(AiVoiceTask aiVoiceTask, String inner) {
                return R.fail("创建AI语音通知任务 失败:" + cause.getMessage());
            }

            @Override
            public R<Integer> AiVoiceCaseSubmitTaskDataAndExecute(AiVoiceTask aiVoiceTask, String inner) {
                return R.fail("创建AI语音通知任务并执行 失败:" + cause.getMessage());
            }

            @Override
            public R<List<Integer>> verifyCaseListAppeal(ThreadEntityPojo entityPojo, String source) {
                return R.fail("调诉端匹配预测试外呼坐席员工信息 失败:" + cause.getMessage());
            }
        };
    }
}
