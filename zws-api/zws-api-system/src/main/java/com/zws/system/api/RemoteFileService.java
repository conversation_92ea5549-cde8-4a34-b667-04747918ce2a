package com.zws.system.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.system.api.domain.SysFile;
import com.zws.system.api.factory.RemoteFileFallbackFactory;

/**
 * 文件服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteFileService", value = ServiceNameConstants.FILE_SERVICE, fallbackFactory = RemoteFileFallbackFactory.class)
public interface RemoteFileService {
    /**
     * 上传文件
     *
     * @param file 文件信息
     * @return 结果
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<SysFile> upload(@RequestPart(value = "file") MultipartFile file);

    /**
     * 上传文件，原文件名上传
     *
     * @param file 文件信息
     * @return 结果
     */
    @PostMapping(value = "/uploadOriginalName", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<SysFile> uploadOriginalName(@RequestPart(value = "file") MultipartFile file);


    /**
     * 本地文件上传接口
     *
     * @param file           上传的文件
     * @param identification 唯一标识生成文件夹
     * @param classification 类别（1-本地原文件上传，2-生成水印后文件上传）
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/uploadLocal", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<SysFile> uploadLocal(@RequestPart(value = "file") MultipartFile file, @RequestPart(value = "identification") String identification, @RequestPart(value = "classification") int classification);
}
