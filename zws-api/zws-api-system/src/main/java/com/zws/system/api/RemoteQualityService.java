package com.zws.system.api;

import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.system.api.domain.EmailAccount;
import com.zws.system.api.factory.RemoteQualityFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@FeignClient(contextId = "remoteQualityService", value = ServiceNameConstants.ZWS_QUALITY, fallbackFactory = RemoteQualityFallbackFactory.class)
public interface RemoteQualityService {

    @GetMapping("/email/getByPrimaryKey/{id}")
    R<EmailAccount> getByPrimaryKey(@PathVariable("id") Long id);

    @GetMapping("/email/getListByUseType/{useType}")
    R<List<EmailAccount>> getListByUseType(@PathVariable("useType") Integer useType);
}
