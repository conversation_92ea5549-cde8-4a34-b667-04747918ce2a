package com.zws.system.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 审批流程
 */
@Data
public class ReviewProgressDto {

    /**
     * 审批人名称
     */
    private String userName;
    /**
     * 申请状态 0-通过，1-不通过，4-撤销 -1待处理
     */
    private Integer approveStart;
    /**
     * 拒绝原因
     */
    private String refuseReason;
    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 排序
     */
    private Integer sort;

}
