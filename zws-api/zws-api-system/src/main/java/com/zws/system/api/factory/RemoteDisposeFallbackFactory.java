package com.zws.system.api.factory;

import com.zws.common.core.domain.R;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.RemoteCaseService;
import com.zws.system.api.RemoteDictService;
import com.zws.system.api.RemoteDisposeService;
import com.zws.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 * <AUTHOR>
 * @date 2024/5/8 23:27
 */
public class RemoteDisposeFallbackFactory implements FallbackFactory<RemoteDisposeService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteDisposeFallbackFactory.class);

    @Override
    public RemoteDisposeService create(Throwable cause) {
        log.error("催收服务调用失败:{}", cause.getMessage());
        return new RemoteDisposeService(){

            @Override
            public R updateSignRecord(Long id, LoginUser loginUser) {
                return R.fail();
            }

            @Override
            public Integer getTeamState(String username) {
                return 0;
            }
            @Override
            public AjaxResult selectMapWithCaseDetailsCis(Long caseId) {
                return AjaxResult.error("催收端AI语音查询失败:"+cause.getMessage());
            }
        };
    }
}
