package com.zws.system.api;

import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.system.api.domain.AiVoiceTask;
import com.zws.system.api.domain.IntelligenceTask;
import com.zws.system.api.factory.RemoteCaseFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * saas案件导入服务关远程服务
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/7 14:36
 */
@FeignClient(contextId = "remoteCisService", value = ServiceNameConstants.ZWS_CIS, fallbackFactory = RemoteCaseFallbackFactory.class)
public interface RemoteCisService {


    /**
     * 执行录音下载任务
     *
     * @param taskId
     * @param source
     * @return
     */
    @PostMapping("/call/downloadTask/executeWorkTask")
    R executeWorkTask(@RequestParam("taskId") Long taskId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据案件id集合查询有预测试外呼坐席的催员id-（勾选案件创建预测试外呼）
     *
     * @param entityPojo 登录人信息
     * @param source
     * @return
     */
    @PostMapping("/intelligenceTask/verifyCaseList")
    R<List<Integer>> verifyCaseList(@RequestBody ThreadEntityPojo entityPojo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据案件id集合查询有预测试外呼坐席的催员id-（勾选案件创建预测试外呼）
     *
     * @param entityPojo 登录人信息
     * @param source
     * @return
     */
    @PostMapping("/intelligenceTask/verifyCaseListAppeal")
    R<List<Integer>> verifyCaseListAppeal(@RequestBody ThreadEntityPojo entityPojo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 根据案件id集合查询有预测试外呼坐席的催员id-（勾选案件创建预测试外呼）
     *
     * @param entityPojo 登录人信息
     * @param source
     * @return
     */
    @PostMapping("/intelligenceTask/verifyMediatorCaseList")
    R<List<Integer>> verifyMediatorCaseList(@RequestBody ThreadEntityPojo entityPojo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据案件id集合查询有预测试外呼坐席的催员id-（勾选案件创建预测试外呼）
     *
     * @param entityPojo 登录人信息
     * @param source
     * @return
     */
    @PostMapping("/intelligenceTask/verifyStageCaseList")
    R<List<Integer>> verifyStageCaseList(@RequestBody ThreadEntityPojo entityPojo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 创建预测试外呼任务-（勾选案件）
     *
     * @param task       新建任务实体
     * @param source
     * @return
     */
    @PostMapping("/intelligenceTask/caseSubmitTaskData")
    R<Integer> caseSubmitTaskData(@RequestBody IntelligenceTask task, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 创建AI语音通知任务-（勾选案件）
     * @param inner
     * @return
     */
    @PostMapping("/aiVoiceTask/AiVoiceCaseSubmitTaskData")
    R<Integer> AiVoiceCaseSubmitTaskData(@RequestBody AiVoiceTask aiVoiceTask, @RequestHeader(SecurityConstants.FROM_SOURCE) String inner);

    /**
     * 创建AI语音通知任务并执行-（勾选案件）
     * @param inner
     * @return
     */
    @PostMapping("/aiVoiceTask/AiVoiceCaseSubmitTaskDataAndExecute")
    R<Integer> AiVoiceCaseSubmitTaskDataAndExecute(@RequestBody AiVoiceTask aiVoiceTask, @RequestHeader(SecurityConstants.FROM_SOURCE) String inner);
}
