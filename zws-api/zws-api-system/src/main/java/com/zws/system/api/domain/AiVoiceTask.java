package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * AI语言通知任务实体类
 */
@Data
public class AiVoiceTask {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 话术模板名称
     */
    private String dialogueTemplateName;

    /**
     * 话术模板内容
     */
    private String dialogueTemplateContent;

    /**
     * 执行时间，1-7，多个用逗号隔开
     */
    private String executionTime;

    /**
     * 任务开始时间
     */
    private String taskStartTime;
    /**
     * 任务结束时间
     */
    private String taskEndTime;

    /**
     * 外呼时段
     */
    private String executionCallTime;

    /**
     * 重呼次数
     */
    private Integer recallCount;

    /**
     * 重呼间隔
     */
    private Integer recallMinute;

    /**
     * 是否发送挂机短信
     */
    private Integer isSendMessage;

    /**
     * 短信模板名称
     */
    private String messageTemplateName;

    /**
     * 短信模板内容
     */
    private String messageTemplateContent;

    /**
     * 选择发送对象 1-接通 2未接通 3空号 4停机 5关机 6忙线 7呼叫转移 8未知
     */
    private String senderTarget;

    /**
     * 机构id
     */
    private Integer teamId;

    /**
     * 员工id
     */
    private Integer employeeId;

    /**
     * 呼叫数量
     */
    private Integer callCount;

    /**
     * 机器人数量
     */
    private Integer robotCount;

    /**
     * 任务状态 1未启动 2进行中 3已完成 4已暂停 5已撤销  6失败
     */
    private Integer taskStatus;


    /**
     * 费用
     */
    private BigDecimal fee;

    /**
     * 创建人id
     */
    private Integer createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人类型（0-主账号;1-员工账号;2-资产端账号）
     */
    private Integer createType;

    /**
     * 客户资料批次名称，多个用逗号隔开
     */
    private String customNameMontage;

    /**
     * 客户资料批次id，多个用逗号隔开
     */
    private String customIdMontage;

    /**
     * 任务分配人员id，多个逗号隔开
     */
    private String taskAllocationPersonnelId;

    /**
     * 任务分配人员坐席账号，多个逗号隔开
     */
    private String taskAllocationPersonnelSeating;

    /**
     * 导入客户记录表id
     */
    private Long customId;

    /**
     * 呼叫中心uuid
     */
    private String callCenterUuid;
    /**
     * 呼叫中心uuid集合List
     */
    private List<String> callCenterUuidList;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 操作类型（1-导入客户，2-案件生成）
     */
    private Integer operationType;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 呼叫对象（1-本人，2-全部号码）
     */
    private Integer callRecipient;

    /**
     * 备注
     */
    private String remark;

    /**
     * 语音通知模板uuid
     */
    private String voiceTplUuid;

    /**
     * 语音模板参数json字符串
     */
    private String hookArgs;

    /**
     * 接通率
     */
    private BigDecimal reachability;

//-------------------------------------------------------展示数据参数---------------------------------------------------------

    /**
     * 任务完成进度
     */
    private BigDecimal taskCompleteSchedule;

    /**
     * 判断是哪个端调用  （0-催收端 1-调诉端）
     */
    private String number;

//-------------------------------------------------------搜索条件---------------------------------------------------------
    /**
     * 员工id集合
     */
    private List<Integer> memberIdList;

    /**
     * 客户记录id集合
     */
    private List<Long> customReloadIdList;

    /**
     * 多选任务名称搜索
     */
    private List<String> taskNameList;

    /**
     * 创建时间-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime1;

    /**
     * 创建时间-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime2;

    /**
     * 任务状态(多选逗号拼接)
     */
    private String statusStr;

    /**
     * 多选任务状态搜索
     */
    private List<Integer> statusList;

    /**
     * 主键id集合
     */
    private List<Long> ids;

    /**
     * 通话状态
     * 0=待拨打，1=已接通，2=未接通，3=漏接
     */
    private Integer connectFlag;

    /**
     * 接通率-开始
     */
    private BigDecimal reachability1;

    /**
     * 接通率-结束
     */
    private BigDecimal reachability2;

//--------------------------------------------------创建人姓名参数处理------------------------------------------------------

    /**
     * 员工名称
     */
    private String employeeName;

    /**
     * 机构名称
     */
    private String cname;

    public String getCreateBy() {
        if (createType != null) {
            if (createType == 0) createBy = cname;
            else if (createType == 1) createBy = employeeName;
        }
        return createBy;
    }

//-----------------------------------------------------机构企业信息--------------------------------------------------------

    /**
     * 企业API标识
     */
    private String apiFlag;

    /**
     * 企业编号
     */
    private String enterpriseNum;

    /**
     * 机构类型（0-自营，1-委外）
     */
    private Integer teamType;

    /**
     * 登录人信息
     */
    private ThreadEntityPojo entityPojo;

}