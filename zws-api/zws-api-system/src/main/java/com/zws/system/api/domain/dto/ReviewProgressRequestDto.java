package com.zws.system.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 审批流程
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReviewProgressRequestDto {

    /**
     * 审批人名称
     */
    private Integer approveCode;

//    /**
//     * 是否是催收端查询
//     */
//    private boolean isCs;

    /**
     * 催收端审批人列表,为空做资产端处理
     * <user,昵称></>
     */
    private Map<String,String> csApprovals;

    /**
     * 审批id集合
     */
    private List<Integer> applyIds;
   /**
     * 主账号审批集合审批id集合      applyId：<审核人ID：审核人姓名>
     */
    private Map<String, Map<String, String>> mainMap;

    /**
     * 催收创建的审批id集合
     */
    private List<Integer> isCsList;

    public ReviewProgressRequestDto(Integer approveCode, Map<String, String> csApprovals, List<Integer> applyIds) {
        this.approveCode = approveCode;
        this.csApprovals = csApprovals;
        this.applyIds = applyIds;
    }

    public ReviewProgressRequestDto(Integer approveCode, List<Integer> applyIds) {
        this.approveCode = approveCode;
        this.applyIds = applyIds;
    }
}
