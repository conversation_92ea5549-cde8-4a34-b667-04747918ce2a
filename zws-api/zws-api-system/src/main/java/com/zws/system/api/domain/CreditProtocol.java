package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 征信协议表(CreditProtoco)实体类
 *
 * <AUTHOR>
 * @since 2024-04-09 13:51:07
 */
@Data
public class CreditProtocol implements Serializable {
    private static final long serialVersionUID = 997130808637530844L;
    /**
     * 序号
     */
    private Long id;
    /**
     * 证件号码
     */
    private String clientIdNum;
    /**
     * 姓名
     */
    private String clientName;
    /**
     * 关联案件数
     */
    private Integer relatedCase;
    /**
     * 已结清案件
     */
    private Integer closedCase;
    /**
     * 已开户
     */
    private Integer accountOpened;
    /**
     * 已报送结清
     */
    private Integer submittedSettlement;
    /**
     * 协议签署时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signingTime;
    /**
     * 上传协议
     */
    private String uploadProtocol;
    /**
     * 上传补充信息
     */
    private String uploadSupplementary;
    /**
     * 签署方式
     */
    private String signingMethod;
    /**
     * 解密密钥
     */
    private String decryptKey;
    /**
     * 创建人（上传人）
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 删除标记
     */
    private String delFlag;
    /**
     * 上传协议时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadProtocolTime;
}

