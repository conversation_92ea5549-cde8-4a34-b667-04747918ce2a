package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.enums.MessageTypes;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * sys_notice
 * 公告
 */
@Data
public class Notice extends BaseEntity {
    /**
     * 公告id
     */
    private Integer noticeId;
    /**
     * 公告标题
     */
    private String noticeTitle;
    /**
     * 公告类型（1通知 2公告 3知识库）
     */
    private String noticeType;
    /**
     * 消息类型 中文信息
     */
    private String noticeTypeInfo;
    /**
     * 公告状态（0正常 1关闭）
     */
    private String status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 推送人群 0-所有人，1-资产端账号，2-催收端账号，3-催收端主账号，5-调诉端账号，6-调诉端主账号
     */
    private Integer pushCrowd;
    /**
     * 推送方式,0-实时推送，1-定时推送
     */
    private Integer pushMode;
    /**
     * 定时推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date pushTime;
    /**
     *提醒方式 红点、弹窗 多个时逗号分隔
     */
    private String reminderMode;
    /**
     * 推送状态 (待发送、已发送)
     */
    private String pushStatus;
    /**
     * 阅读量
     */
    private Long readingVolume;
    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 提醒推送时间(业务提醒,审批提醒、工单反馈)
     */
    private String remindPushTime;
    /**
     * 提醒频率(业务提醒,审批提醒、工单反馈)
     */
    private String pushFrequency;

    public Integer getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(Integer noticeId) {
        this.noticeId = noticeId;
    }

    public String getNoticeTitle() {
        return noticeTitle;
    }

    public void setNoticeTitle(String noticeTitle) {
        this.noticeTitle = noticeTitle == null ? null : noticeTitle.trim();
    }

    public String getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType == null ? null : noticeType.trim();
        try{
            if (StringUtils.isNumeric(noticeType)){
                Integer code=Integer.parseInt(noticeType);
                MessageTypes messageTypes=  MessageTypes.valueOfCode(code);
                if (messageTypes==null) {
                    return;
                }
                switch (messageTypes){
                    case BUSINESS_PROGRESS:
                    case APPROVAL:
                    case WORK_ORDER:
                        if(StringUtils.equals(this.noticeTitle,"退案提醒") ){
                            this.remindPushTime= "上午10:00";
                        }else if(StringUtils.equals(this.noticeTitle,"跟案提醒")){
                            this.remindPushTime= "下午14:30";
                        }else if(StringUtils.equals(this.noticeTitle,"承诺还款提醒")){
                            this.remindPushTime= "上午10:00 ";
                        }else if(StringUtils.equals(this.noticeTitle,"另约时间提醒")){
                            this.remindPushTime= "提前5分钟 ";
                        }else{
                            this.remindPushTime="实时";
                        }
                        break;
                }
            }
        }catch (Exception e){}
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Integer getPushCrowd() {
        return pushCrowd;
    }

    public void setPushCrowd(Integer pushCrowd) {
        this.pushCrowd = pushCrowd;
    }

    public Integer getPushMode() {
        return pushMode;
    }

    public void setPushMode(Integer pushMode) {
        this.pushMode = pushMode;
    }

    public Date getPushTime() {
        return pushTime;
    }

    public void setPushTime(Date pushTime) {
        this.pushTime = pushTime;
    }

    public String getReminderMode() {
        return reminderMode;
    }

    public void setReminderMode(String reminderMode) {
        this.reminderMode = reminderMode == null ? null : reminderMode.trim();
    }

    public String getPushStatus() {
        return pushStatus;
    }

    public void setPushStatus(String pushStatus) {
        this.pushStatus = pushStatus == null ? null : pushStatus.trim();
    }



    public Long getReadingVolume() {
        return readingVolume==null?0:readingVolume;
    }

    public void setReadingVolume(Long readingVolume) {
        this.readingVolume = readingVolume;
    }

    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

    public String getNoticeTypeInfo() {
        if (StringUtils.isEmpty(this.noticeType)) {
            return null;
        }
        if (!StringUtils.isNumeric(this.noticeType)) {
            return null;
        }

        MessageTypes messageTypes= MessageTypes.valueOfCode(Integer.parseInt(this.noticeType));
        if (messageTypes==null) {
            return null;
        }
        return messageTypes.getInfo();
    }

    public void setNoticeTypeInfo(String noticeTypeInfo) {
        this.noticeTypeInfo = noticeTypeInfo;
    }

    public String getRemindPushTime() {
        return remindPushTime;
    }

    public void setRemindPushTime(String remindPushTime) {
        this.remindPushTime = remindPushTime;
    }

    public String getPushFrequency() {
        return pushFrequency;
    }

    public void setPushFrequency(String pushFrequency) {
        this.pushFrequency = pushFrequency;
    }
}
