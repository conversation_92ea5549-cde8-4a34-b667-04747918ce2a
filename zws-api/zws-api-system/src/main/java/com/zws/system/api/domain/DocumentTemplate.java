package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zws.common.core.domain.seal.SealStamp;
import com.zws.common.core.signature.Stamp;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 函件模板
 * </p>
 */
@Data
public class DocumentTemplate {

    /**
     * 函件模板
     */
    private Integer id;

    /**
     * 编号，租户唯一
     */
    private Integer templateCode;

    /**
     * 模板类型id
     */
    private Integer classifyId;

    /**
     * 模板类型标签
     */
    private String classifyLabel;

    /**
     * 0:Excel类型  1:Zip类型
     * 批量导入的源文件类型
     */
    private Integer sourceFileType;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    /**
     * 标签，分号(;)分隔
     */
    private String templateLabel;

    /**
     * 状态，0-正常(启用)，1-禁用
     */
    private Integer status;

    /**
     * 模板变量
     */
    private String templateVariable;

    /**
     * 正文内容
     */
    private String bodyContent;

    /**
     * 签章机构(律所id)
     */
    private Integer lawId;

    /**
     * 代理律师(签章id)
     */
    private Integer signId;

    /**
     * 底部内容
     */
    private String tailContent;

    /**
     * pdf压缩包模板原路径(zip导入模板)
     */
    private String originalUrl;

    /**
     * 模板预览url
     */
    private String previewUrl;

    /**
     * 模板预览的总页数
     */
    private Integer previewPages;

    private Integer tenantId;

    private String createBy;

    @JsonSerialize(using = ToStringSerializer.class)
    private Integer createById;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;

    @JsonSerialize(using = ToStringSerializer.class)
    private Integer updateById;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String delFlag;

    /**
     * 页眉、非必填
     */
    private String pageHeader;
    /**
     * 裁剪后的页眉
     */
    private String cutHeader;

    /**
     * 页脚、非必填
     */
    private String pageFooter;
    /**
     * 裁剪后的页脚
     */
    private String cutFooter;

    /**
     * 签章定位数据(xy坐标,盖章页码,盖章图片)
     * (数据库字段)
     */
    private String positionData;

    /**
     * 签章定位数据,接收前端传定位数据
     * (非数据库字段)
     */
    private List<Stamp> positionList;

    /**
     * 页眉原图缩放后的路径
     * （非数据库字段）
     */
    private String scaleHeader;
    /**
     * 页脚原图缩放后的路径
     * （非数据库字段）
     */
    private String scaleFooter;


    /**
     * 模板类型名称
     */
    private String classifyName;

    /**
     * 查询参数-模板类型
     */
    private List<String> classifyIds;


    /**
     * 查询参数-模板名称
     */
    private List<String> templateNames;
    /**
     * 查询参数-模板id集合
     */
    private List<Long> templateIds;
    /**
     * 查询参数-创建人
     */
    private List<String> createBys;
    /**
     * 查询参数-创建人
     */
    private List<Long> createByIds;

    /**
     * 不包含id
     */
    private Integer notId;
    /**
     * 代理律师(签章id) 是否设置为null
     */
    private Boolean signIdNull;

    /**
     * zip模板编辑回显、截取previewUrl 作为文件名
     * (非数据库字段)
     */
    private String fileName;
    /**
     * 类型（0-函件 1-文书）
     */
    private Integer type;

    /**
     * 团队、机构id
     */
    private Long teamId;

    /**
     * cfca签章坐标
     */
    private List<SealStamp> sealPositionList;

//--------------------------文书审批---------------------------

    /**
     * 审批id
     */
    private Long  approveId;

    /**
     * 审核状态
     */
    private Integer examineState;
    private List<Integer> examineStateList;
    private List<String> examineStateArr;

    /**
     * 处理状态
     */
    private Integer handlingStatus;

//----------------------------查询条件--------------------------

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 不通过原因
     */
    private String reason;


    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime1;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime2;

    private Integer pageNum;
    private Integer pageSize;
    private List<Long> approveIds;
    private List<Long> approveProcessIds;
    private Boolean allQuery;

    private Long userId;
}
