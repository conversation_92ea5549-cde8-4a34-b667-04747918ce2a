package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * sys_user_message
 * 资产端- 用户信息
 */
@Data
@ToString
public class UserMessage extends BaseEntity {
    /**
     *用户信息主键id
     */
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 公告id 消息类型为0时
     */
    private Long noticeId;
    /**
     * 消息类型,0-系统信息(公告) 1-业务进度提醒,2-审批提醒,3-工单反馈
     */
    private Integer messageType;
    /**
     * 信息类型
     */
    private String messageTypeInfo;
    /**
     * 消息标题
     */
    private String messageTitle;
    /**
     * 创建时间-发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 阅读公告时间,null表示未读
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 删除标记
     */
    private String delFlag;
    /**
     * 消息内容
     */
    private String messageContent;
    /**
     * 0-未读，1-已读
     */
    private Integer read;

    /**
     * 提醒方式(非数据库字段)，0-红点，1-弹窗
     */
    private Integer reminderMode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
    }

    public Integer getMessageType() {
        return messageType;
    }

    public void setMessageType(Integer messageType) {
        this.messageType = messageType;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle == null ? null : messageTitle.trim();
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getMessageTypeInfo() {
        if(messageType==null) {
            return "";
        }
       switch (messageType){
           case 0:
               return "系统信息";
           case 1:
               return "业务进度提醒";
           case 2:
               return "审批提醒";
           case 3:
               return "工单反馈";
       }
        return "";
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        if(updateTime==null) {
            this.read=0;
        } else {
            this.read=1;
        }
    }
}
