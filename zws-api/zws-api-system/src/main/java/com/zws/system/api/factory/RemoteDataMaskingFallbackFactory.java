package com.zws.system.api.factory;

import com.zws.common.core.domain.R;
import com.zws.system.api.RemoteDataMaskingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 *
 *
 * <AUTHOR> @date 2024/1/17 11:40
 */
public class RemoteDataMaskingFallbackFactory implements FallbackFactory<RemoteDataMaskingService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteDataMaskingService.class);

    @Override
    public RemoteDataMaskingService create(Throwable throwable) {
        log.error("查看脱敏开关信息失败:{}", throwable.getMessage());
        return new RemoteDataMaskingService() {
            @Override
            public R<Boolean> autoReduction(String param) {
                return  R.fail("查看脱敏开关信息失败:" + throwable.getMessage());
            }
        };
    }
}
