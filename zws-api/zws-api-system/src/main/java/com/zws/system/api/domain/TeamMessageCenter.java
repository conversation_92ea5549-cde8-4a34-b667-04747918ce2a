package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 团队消息中心
 */
@Data
public class TeamMessageCenter extends BaseEntity {
    /**
     * 团队消息中心主键id
     */
    private Long id;
    /**
     * 团队id
     */
    private Integer createId;
    /**
     * 消息类型(1-系统通知;2-公告/公示;3-知识库)
     */
    private Integer messageType;
    /**
     * 推送方式(1-实时推送;2-定时推送)
     */
    private Integer pushMode;
    /**
     * 定时推送时间-string类型
     */
    private String pushTimeStr;
    /**
     * 定时推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date pushTime;
    /**
     * 推送人群id(*表示所有人)
     */
    private String pushCrowdId;
    /**
     * 推送人群名字(*表示所有人)
     */
    private String pushCrowdName;
    /**
     * 提醒方式
     */
    private String reminderMode;
    /**
     * 消息标题
     */
    private String messageTitle;
    /**
     * 状态
     */
    private String states;
    /**
     * 阅读量
     */
    private Long readingVolume;
    /**
     * 创建人
     */
    private String founder;
    /**
     *  创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creationtime;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     *  删除标志
     */
    private Integer deleteLogo;
    /**
     * 消息内容
     */
    private String messageContent;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCreateId() {
        return createId;
    }

    public void setCreateId(Integer createId) {
        this.createId = createId;
    }

    public Integer getMessageType() {
        return messageType;
    }

    public void setMessageType(Integer messageType) {
        this.messageType = messageType;
    }

    public Integer getPushMode() {
        return pushMode;
    }

    public void setPushMode(Integer pushMode) {
        this.pushMode = pushMode;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getPushTime() {
        return pushTime;
    }

    public void setPushTime(Date pushTime) {
        this.pushTime = pushTime;
    }

    public String getPushCrowdId() {
        return pushCrowdId;
    }

    public void setPushCrowdId(String pushCrowdId) {
        this.pushCrowdId = pushCrowdId == null ? null : pushCrowdId.trim();
    }

    public String getPushCrowdName() {
        return pushCrowdName;
    }

    public void setPushCrowdName(String pushCrowdName) {
        this.pushCrowdName = pushCrowdName == null ? null : pushCrowdName.trim();
    }

    public String getReminderMode() {
        if(reminderMode==null) {
            this.reminderMode="红点";
        }
        return reminderMode;
    }

    public void setReminderMode(String reminderMode) {
        this.reminderMode = reminderMode == null ? null : reminderMode.trim();
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle == null ? null : messageTitle.trim();
    }

    public String getStates() {
        return states;
    }

    public void setStates(String states) {
        this.states = states == null ? null : states.trim();
    }

    public Long getReadingVolume() {
        return readingVolume;
    }

    public void setReadingVolume(Long readingVolume) {
        this.readingVolume = readingVolume;
    }

    public String getFounder() {
        return founder;
    }

    public void setFounder(String founder) {
        this.founder = founder == null ? null : founder.trim();
    }

    public Date getCreationtime() {
        return creationtime;
    }

    public void setCreationtime(Date creationtime) {
        this.creationtime = creationtime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getDeleteLogo() {
        return deleteLogo;
    }

    public void setDeleteLogo(Integer deleteLogo) {
        this.deleteLogo = deleteLogo;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent == null ? null : messageContent.trim();
    }
}
