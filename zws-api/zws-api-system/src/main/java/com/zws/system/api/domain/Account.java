package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 账户管理实体类
 */
@Data
public class Account {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 开户行名称
     */
    @Excel(name = "开户行名称")
    private String bankName;

    /**
     * 账户名称
     */
    @Excel(name = "账户名称")
    private String accountName;

    /**
     * 开户账户
     */
    @Excel(name = "开户账户")
    private String accountNumber;

    /**
     * 账户状态(0:开启,1:关闭)
     */
    @Excel(name = "账户状态", readConverterExp = "0=开启,1=关闭")
    private Integer accountStatus;

    /**
     * 创建人id
     */
    private Long createdById;

    /**
     * 创建人
     */
    @Excel(name = "添加人")
    private String createdBy;

    /**
     * 创建时间
     */
    @Excel(name = "添加时间", dateFormat = "yyyy/MM/dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标志
     */
    private String delFlag;
}
