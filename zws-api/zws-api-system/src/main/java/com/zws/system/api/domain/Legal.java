package com.zws.system.api.domain;

import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 合规设置
 */
@Data
public class Legal extends BaseEntity {
    /**
     * 合规设置id
     */
    private Long id;
    /**
     * 类型，0-信息安全，1-水印设置，2-页面限制，3-标签设置，4-实名认证,5-合规宣导，6-双重验证,7-资料链接有效期 8-百度秘钥key
     */
    private Integer legalType;
    /**
     * 显示名称
     */
    private String legalName;
    /**
     * 键
     */
    private String legalKey;
    /**
     * 值
     */
    private String legalVal;
    /**
     *状态 0启用，1禁用
     */
    private Integer state;
    /**
     *排序
     */
    private Integer sort;
    /**
     * 备注
     */
    private String remark;

    private String delFlag;
    /**
     * 获取资料链接有效期-申请过期时长
     */
   private Integer duration;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getLegalType() {
        return legalType;
    }

    public void setLegalType(Integer legalType) {
        this.legalType = legalType;
    }

    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName == null ? null : legalName.trim();
    }

    public String getLegalKey() {
        return legalKey;
    }

    public void setLegalKey(String legalKey) {
        this.legalKey = legalKey == null ? null : legalKey.trim();
    }

    public String getLegalVal() {
        return legalVal;
    }

    public void setLegalVal(String legalVal) {
        this.legalVal = legalVal == null ? null : legalVal.trim();
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }


}
