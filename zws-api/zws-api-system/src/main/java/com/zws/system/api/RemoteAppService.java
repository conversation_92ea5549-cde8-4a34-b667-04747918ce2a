package com.zws.system.api;

import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.seal.SealParam;
import com.zws.system.api.factory.RemoteAppFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(contextId = "remoteAppService", value = ServiceNameConstants.ZWS_APP, fallbackFactory = RemoteAppFallbackFactory.class)
public interface RemoteAppService {

    @PostMapping("/app/signed/officialSealSigned")
    R<String> officialSealSigned(@RequestBody SealParam sealParam);
}
