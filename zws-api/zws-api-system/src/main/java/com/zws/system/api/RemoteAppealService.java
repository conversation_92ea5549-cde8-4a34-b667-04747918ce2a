package com.zws.system.api;


import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.factory.RemoteCaseFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(contextId = "remoteAppealService", value = ServiceNameConstants.ZWS_APPEAL, fallbackFactory = RemoteCaseFallbackFactory.class)
public interface RemoteAppealService {

    /**
     * 根据案件id查询案件所有信息以及共债信息
     * @param caseId
     * @return
     */
    @RequestMapping(value = "/collection/cis/selectCase/{caseId}", method = RequestMethod.GET)
    public AjaxResult selectMapWithCaseDetailsCis(@PathVariable("caseId") Long caseId);
}
