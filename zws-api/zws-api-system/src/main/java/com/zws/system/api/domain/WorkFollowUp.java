package com.zws.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.system.api.pojo.WorkAnnexPojo;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class WorkFollowUp implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 工单表id
     */
    @NotNull(message = "工单ID 不能为空")
    private Long orderId;

    /**
     * 跟进状态(0-继续跟进;1-结束跟进)
     */
    private Integer orderState;

    /**
     * 跟进状态(0-继续跟进;1-结束跟进)
     */
    private String orderStateStr;

    /**
     * 跟进内容
     */
    @NotEmpty(message = "跟进内容不能为空")
    @Length(max = 300, message = "跟进内容字数不能超过300")
    private String workFollowContent;

    /**
     * 跟进人id
     */
    private Long createdById;

    /**
     * 跟进人
     */
    private String createdBy;

    /**
     * 跟进时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**
     * 删除标志 (0-未删除；1-已删除)
     */
    private String delFlag;

    /**
     * 工单进度上传附件信息
     */
    private List<WorkAnnexPojo> workAnnex;

    /**
     * 工单进度附件信息集合
     */
    private List<WorkAnnex> workAnnexs;
}
