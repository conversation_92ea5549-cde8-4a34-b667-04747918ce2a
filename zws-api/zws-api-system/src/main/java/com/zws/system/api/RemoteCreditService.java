package com.zws.system.api;

import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.domain.CreditProtocolVo;
import com.zws.system.api.factory.RemoteCreditFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 征信服务
 *
 * <AUTHOR>
 * @date 2024/1/17 11:37
 */
@FeignClient(contextId = "remoteCreditService", value = ServiceNameConstants.ZWS_CREDIT, fallbackFactory = RemoteCreditFallbackFactory.class)
public interface RemoteCreditService {


    /**
     * 提交征信报送
     * @param caseIds
     * @return
     */
    @PostMapping("/sugar/rest/sgmt")
    R autoReduction(@RequestBody List<Long> caseIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     *新增征信
     */
    @PostMapping("/creditRhzx/add")
    AjaxResult add(@RequestBody CreditProtocolVo creditProtocolVo);

}
