package com.zws.system.api;

import com.github.pagehelper.PageInfo;
import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.seal.CalculateSealCountParam;
import com.zws.common.core.domain.seal.SignatureParam;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.system.api.config.FeignConfig;
import com.zws.system.api.domain.DocumentTemplate;
import com.zws.system.api.domain.Letter;
import com.zws.system.api.domain.LetterMessage;
import com.zws.system.api.domain.LetterTemplate;
import com.zws.system.api.factory.RemoteLetterFallbackFactory;
import feign.Response;
import org.apache.ibatis.annotations.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 签章服务
 *
 * <AUTHOR>
 * @date 2024/4/23 19:37
 */
@FeignClient(contextId = "remoteLetterService",configuration = FeignConfig.class, value = ServiceNameConstants.ZWS_LETTER, fallbackFactory = RemoteLetterFallbackFactory.class)
public interface RemoteLetterService {


    @PostMapping("/seal/doSignature")
    public String doSignature(@RequestBody SignatureParam signatureParam);

    @PostMapping("/seal/calculate-signature")
    public R<AjaxResult> calculateSignatureCount(@RequestBody CalculateSealCountParam calculateSealCountParam);

    /**
     * 结清证明生成签章文件
     *
     * @param caseId 案件id
     * @return
     */
    @GetMapping("/letter/message/signSettleRecord")
    public R<AjaxResult> signSettleRecord(@RequestParam("caseId") Long caseId);

    /**
     * 生成签章文件
     *
     * @param ids letterId集合
     * @param type 模版类型
     * @return
     */
    @PostMapping("/letter/message/createSignFile")
    public R<AjaxResult> createSignFile(@RequestBody List<Integer> ids, @RequestParam("type")String type);

    /**
     * 列表
     *
     * @param batchNum
     * @return
     */
    @GetMapping("/letter/item/itemList")
    public R<List<Integer>> list(@RequestParam("batchNum")String  batchNum );

    /**
     * 通过批次号查询函件批次
     *
     * @param batchNum 批次号
     * @return
     */
    @GetMapping("/letter/message/getByBatchNum")
    public R<LetterTemplate> getByBatchNum(@RequestParam("batchNum") String batchNum);


    /**
     * 更新函件批次审核状态
     * @param ids 函件批次ID
     * @return
     */
    @PostMapping("/letter/message/updateProce")
    public R<AjaxResult> updateProce(@RequestBody List<Integer> ids);

    /**
     * 更新函件批次审核状态
     * @param batchNum 函件号
     * @return
     */
    @GetMapping("/letter/message/updateLetterMessage")
    public R<AjaxResult> updateLetterMessage(@RequestParam("batchNum") String batchNum,@RequestParam("examineState") String status);

    /**
     * 签章文件列表
     * @return
     */
    @GetMapping("/letter/message/disposeList")
    public TableDataInfo disposeList(@SpringQueryMap LetterMessage message);

    /**
     * 校验文件
     *
     * @param message 需要模板id、文件路径
     * @return
     */
    @GetMapping("/letter/message/verifyFile")
    public AjaxResult verifyFile( @SpringQueryMap LetterMessage message);

    /**
     * 新建发函
     *
     * @param message
     * @return
     */
    @PostMapping(value = "/letter/message/add",headers = {"isFeign=true"})
    public AjaxResult add( @RequestBody LetterMessage message);

    /**
     * 校验zip压缩包文件
     *
     * @param message
     * @return
     */
    @PostMapping("/letter/message/verifyZipFile")
    public AjaxResult verifyZipFile(@RequestBody LetterMessage message);

    /**
     * 新建发函(压缩包pdf)
     *
     * @param message
     * @return
     */
    @PostMapping(value = "/letter/message/addZip",headers = {"isFeign=true"})
    public AjaxResult addZip( @RequestBody LetterMessage message);

    /**
     * 判断模板名称是否唯一
     *
     * @param template
     * @return
     */
    @GetMapping("/letter/message/checkUniqueName")
    public AjaxResult checkUniqueName(@SpringQueryMap LetterMessage template);

    /**
     * 上传zip压缩文件
     * @param file
     * @return
     */
    @PostMapping(value = "/letter/message/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult uploadZip(@RequestPart(value = "file") MultipartFile[] file);
    /**
     * 重新生成以前的源文件
     *
     * @return
     */
    @GetMapping("/letter/message/createOldSourceFile")
    public R<AjaxResult> createOldSourceFile();

    /**
     * 校验文件
     *
     * @param batchNum 批次号
     * @return
     */
    @GetMapping("/letter/message/getByBatchNum")
    public R<AjaxResult> getByBatchNum2(@RequestParam("batchNum")String batchNum);

    /**
     * 列表
     *
     * @param letter
     * @return
     */
    @GetMapping("/letter/item/disposeItemList")
    public TableDataInfo disposeItemList(@SpringQueryMap Letter letter);

    /**
     * 获取模板选项
     *
     * @param type 模板文件类型  0-Excel类型模板,1-Zip类型模板
     * @return
     */
    @GetMapping("/letter/template/getOptions")
    public AjaxResult getOptions(@RequestParam("type")Integer type);

    /**
     * 上传excel文件到minio
     *
     * @param file
     * @return
     * @throws Exception
     */

    @PostMapping(value = "/letter/template/uploadCase" , consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
            headers = {"isFeign=true"})
    public AjaxResult uploadCase(@RequestPart(value = "file") MultipartFile file);

    /**
     * 下载模板变量
     */
    @GetMapping(value = "/letter/template/downloadTemplateVariable")
    public Response downloadTemplateVariable(@RequestParam("id")Integer id);

    /**
     * 详情 获取预览
     *
     * @param id 模板id
     * @return
     */
    @GetMapping("/letter/template/getPreview")
    public AjaxResult getPreview(@RequestParam("id")Integer id);

    /**
     * 获取签章预览url
     * @param id 签章id
     * @return
     */
    @GetMapping("/letter/item/getPreviewUrl")
    public AjaxResult getPreviewUrl(@RequestParam("id")Integer id);


    /**
     * 获取签章预览url
     * @param letterId 签章批次ID
     * @return
     */
    @GetMapping("/letter/message/getSignRecordId")
    public R<Long> getSignRecordId(@RequestParam("letterId")Long letterId);

    /**
     * 导出
     */
    @PostMapping(value = "/letter/message/disposeExport",headers = {"isFeign=true"})
    public AjaxResult disposeExport(@SpringQueryMap LetterMessage message);

    /**
     * 获取变量 选项
     * @return
     */
    @GetMapping("/letter/variable/getOptions")
    AjaxResult getOptions();

    /**
     * 新增电子用印模板
     * @param record
     * @return
     */
    @PostMapping("/letter/template/add")
    AjaxResult add(@RequestBody @Validated LetterTemplate record);

    /**
     * 获取电子用印模板详情
     * @param approveId
     * @return
     */
    @PostMapping("/letter/template/getLetterTemplateByApproveId")
    R<LetterTemplate> getLetterTemplateByApproveId(@RequestParam("approveId") Long approveId);

    @GetMapping("/letter/template/getLetterById")
    R<LetterTemplate> getLetterById(@RequestParam("id") Integer id);

    /**
     * 审批电子用印模板
     * @param record
     * @return
     */
    @PostMapping("/letter/template/examineApprove")
    AjaxResult examineApprove(@RequestBody LetterTemplate record);


    /**
     * 修改电子用印模板
     * @param record
     * @return
     */
    @PostMapping("/letter/template/edit")
    AjaxResult edit(@RequestBody @Validated LetterTemplate record);


    @GetMapping("/letter/template/getById")
    R<LetterTemplate> getById(@RequestParam("id") Integer id);

    /**
     * 个人征信授权书签章
     * @param caseId
     * @return
     */
    @GetMapping("/letter/message/personSignSettle")
    R<String> personSignSettle(@RequestParam("caseId") Long caseId);
    /**
     * 文书模板添加
     * @param template
     * @return
     */
    @PostMapping("/document/template/add")
    public AjaxResult add(@Validated @RequestBody DocumentTemplate template);

    /**
     * 文书列表
     *
     * @param template
     * @return
     */
    @PostMapping("/document/template/listNotPaging")
    public TableDataInfo  listNotPaging(@RequestBody DocumentTemplate template);

    /**
     * 编辑
     *
     * @param template
     * @return
     */
    @PostMapping("/document/template/edit")
    public AjaxResult edit(@RequestBody DocumentTemplate template);

    /**
     * 获取模板下拉框
     * @return
     */
    @GetMapping("/document/template/classify/getSelectTree")
    public AjaxResult getSelectTree();

}
