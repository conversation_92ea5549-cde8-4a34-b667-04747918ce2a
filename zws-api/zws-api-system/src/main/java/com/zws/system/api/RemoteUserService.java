package com.zws.system.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.ServiceNameConstants;
import com.zws.common.core.domain.R;
import com.zws.system.api.domain.SysUser;
import com.zws.system.api.factory.RemoteUserFallbackFactory;
import com.zws.system.api.model.LoginUser;

import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteUserService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/info/{username}")
    public R<LoginUser> getUserInfo(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    public R<Boolean> registerUserInfo(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 更新用户登录信息
     * @param sysUser
     * @param source
     * @return
     */
    @PostMapping("/user/updateUserLogin")
    public R<Boolean> updateUserLogin(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取用户是不是当天首次登录
     * @param userId
     * @param source
     * @return
     */
    @GetMapping("/user/getUserFirstLogin")
    public R<Boolean> getUserFirstLogin(@RequestParam("userId")Long userId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 角色id获取用户列表
     * @param roleId
     * @param source
     * @return
     */
    @GetMapping("/user/listByRoleId/")
    public R<List<SysUser>> selectUserByRole(@RequestParam("roleId") Long roleId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 通过用户ID查询用户基本信息
     */
    @GetMapping("/user/getUserById/{uesrId}")
    public R<SysUser> getUserById(@PathVariable("uesrId") Long uesrId);

    /**
     * 查询投资人角色下的所有用户
     * @return
     */
    @GetMapping("/user/selectListByInvestorAll")
    R<List<Long>> selectListByInvestorAll();
}
