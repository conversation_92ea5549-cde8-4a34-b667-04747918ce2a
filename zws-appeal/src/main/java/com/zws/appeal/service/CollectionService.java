package com.zws.appeal.service;

import com.zws.appeal.domain.*;
import com.zws.appeal.pojo.Criteria;
import com.zws.appeal.pojo.InfoLoanPojo;
import com.zws.appeal.pojo.Option;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface CollectionService {

    /**
     * 根据标签颜色和团队id查询案件标签信息
     *
     * @param caseId
     * @param createId
     * @return
     */
    Label selectLabelCode(Long caseId, int createId, String code);

    /**
     * 根据案件联系人信息id查询案件联系人信息
     *
     * @param id
     * @return
     */
    InfoContact selectInfoContactById(Long id);

    /**
     * 根据案件id查询案件联系人信息
     *
     * @param caseId
     * @return
     */
    List<InfoContact> selectInfoContactListByCaseId(Long caseId);

    /**
     * 根据案件id查询案件详情
     *
     * @param caseId
     * @return
     */
    CaseManage selectCaseManageId(Long caseId);

    /**
     * 根据证件号码以及团队id查询共债案件id集合
     *
     * @param map
     * @return
     */
    List<Long> selectIdentificationAndTeamId(Map<String, Object> map);

    /**
     * 根据证件号码查询共债案件id集合
     *
     * @param map
     * @return
     */
    List<Long> selectIdentification(Map<String, Object> map);

    /**
     * 根据案件id查询案件基础信息详情
     *
     * @param caseId
     * @return
     */
    InfoBase selectInfoBase(Long caseId);

    /**
     * 根据案件id以及联系人/联系人电话查询案件联系人信息
     *
     * @param criteria
     * @return
     */
    List<InfoContact> selectInfoContact(Criteria criteria);

    /**
     * 根据案件id查询案件附加信息
     *
     * @param caseId
     * @return
     */
    List<InfoExtra> selectInfoExtra(Long caseId);

    /**
     * 根据案件id查询案件贷款信息
     *
     * @param caseId
     * @return
     */
    InfoLoan selectInfoLoan(Long caseId);

    /**
     * 根据案件id查询案件还款计划信息
     *
     * @param infoPlan
     * @return
     */
    List<InfoPlan> selectInfoPlan(InfoPlan infoPlan);

    /**
     * 根据案件id以及客户身份证号查询共债案件
     *
     * @param clientIdcard
     * @return
     */
    List<InfoLoanPojo> selectCaseManage(String clientIdcard, int createId);

    /**
     * 根据案件id以及客户身份证号查询共债案件数量
     *
     * @param clientIdcard
     * @return
     */
    int selectCaseManageCount(String clientIdcard, int createId);

    /**
     * 新增案件联系人信息
     *
     * @param infoContact
     * @return
     */
    int insertInfoContact(InfoContact infoContact);

    /**
     * 修改联系人状态
     *
     * @param infoContact
     * @return
     */
    int updateInfoContact(InfoContact infoContact);

    /**
     * 根据催收员id查询催收员标签
     *
     * @param
     * @return
     */
    List<StaffLabel> selectStaffLabel();

    /**
     * 新增员工案件标签信息
     *
     * @param staffLabel
     * @return
     */
    int insertStaffLabel(StaffLabel staffLabel);

    /**
     * 删除员工案件标签信息
     *
     * @param id
     * @return
     */
    int deleteStaffLabel(int id);

    /**
     * 查询获取产品简称
     *
     * @param productId
     * @return
     */
    String getProductShortName(Long productId);

    /**
     * 统计手机号码无效的数量
     *
     * @param caseId      案例编号
     * @param clientPhone 客户电话
     * @return {@link Long}
     */
    Long selectPhoneIsVoid(Long caseId, String clientPhone);

    /**
     * 获取历史记录携带标识
     * @param caseId
     * @return
     */
    List<Option> selectWithTab(Long caseId);

    BigDecimal selectAmountAfterDeductionSun(Long caseId);

    BigDecimal selectReorganizationCaseIdSum(Long caseId);
}
