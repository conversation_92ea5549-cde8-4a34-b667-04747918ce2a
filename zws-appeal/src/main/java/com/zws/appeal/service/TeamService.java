package com.zws.appeal.service;

import com.zws.appeal.domain.*;
import com.zws.appeal.enums.ApproveEnum;
import com.zws.appeal.pojo.Authentication;
import com.zws.appeal.pojo.Cooperation;
import com.zws.appeal.pojo.TimeIntervalPojo;
import com.zws.system.api.model.LoginUser;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface TeamService {

    /**
     * 根据条件查询团队合作状态修改记录表
     *
     * @param
     * @return
     */
    List<Category> selectCooperation(Cooperation cooperation);

    /**
     * 根据字典表类型查询字典表标签
     *
     * @param
     * @return
     */
    List<String> selectDictData();

    /**
     * 根据字段查询员工信息
     *
     * @param
     * @return
     */
    List<Employees> selectDeptFuzzy(Employees employees);

    /**
     * 人工通过员工实名认证-（新增）
     *
     * @param certification 实名认证表信息
     * @return
     */
    int addCertification(Certification certification);

    /**
     * 人工通过员工实名认证-(修改)
     *
     * @param certification 实名认证表信息
     * @return
     */
    int updateCertification(Certification certification);

    /**
     * 创建团队
     *
     * @param create 团队表信息
     * @return
     */
    int insertCreate(Create create);

    /**
     * 创建团队信息验证
     */
    void yanzheng(Create create);

    /**
     * 创建团队标签
     *
     * @param label 标签表信息
     * @return
     */
    int insertLabel(List<Label> label);

    /**
     * 创建白名单名称以及IP地址
     *
     * @param white
     * @return
     */
    int insertWhite(White white);

    /**
     * 写入状态值
     *
     * @param state
     * @return
     */
    int insertState(State state);

    /**
     * 月回款目标历史记录添加
     *
     * @param targets
     * @return
     */
    int insertTargets(Targets targets);

    /**
     * 写入文件信息
     *
     * @param file
     * @return
     */
    int insertFile(List<Files> file);

    /**
     * 写入脱敏开关状态
     *
     * @param desensitization
     * @return
     */
    int insertDesensitizationDefault(Desensitization desensitization);

    /**
     * 写入脱敏开关状态
     *
     * @param desensitization
     * @return
     */
    int insertDesensitization(Desensitization desensitization);

    /**
     * 写入水印设置字段信息
     *
     * @param watermark
     * @return
     */
    int insertWatermark(Watermark watermark);

    /**
     * 写入审批设置信息
     *
     * @param id
     * @return
     */
    int insertApprovalSettings(int id);

    /**
     * 写入机构评价
     *
     * @param evaluate
     * @return
     */
    int addEvaluate(Evaluate evaluate);

    /**
     * 根据主键id修改机构评价信息
     *
     * @param evaluate
     * @return
     */
    int updateEvaluate(Evaluate evaluate);

    /**
     * 修改审批设置时间以及修改人
     *
     * @param approvalSettings
     * @return
     */
    int updateApprovalSettingsDate(ApprovalSettings approvalSettings);

    /**
     * 写入团队审批步骤信息(默认)
     *
     * @param name
     * @return
     */
    int insertApprovalSteps(int id, String name);

    /**
     * 写入团队审批步骤信息(批量新增)
     *
     * @param approvalSteps
     * @return
     */
    int insertApprovalStepsPojo(List<ApprovalSteps> approvalSteps);

    /**
     * 新增默认团队主账号的审批流程
     *
     * @param approvalSteps
     * @return
     */
    int insertApprovalStepsByTeamId(ApprovalSteps approvalSteps);

    /**
     * 写入修改团队合作状态记录表
     *
     * @param categories
     * @return
     */
    int insertCategory(List<Category> categories);

    /**
     * 查询团队信息（全查）
     *
     * @return
     */
    List<Create> findAllCreate();

    /**
     * 查询团队信息（合作中）
     *
     * @return
     */
    List<Create> selectCreateByType();

    /**
     * 根据团队id查询团队员工实名认证信息
     *
     * @return
     */
    List<Authentication> selectAuthentication(Authentication authentication);

    /**
     * 根据团队id查询团队状态控制表信息
     *
     * @return
     */
    State findState(int createId);

    /**
     * 查询权限字符串集合-(催收员账号)
     *
     * @return
     */
    Set<String> selectRoleMenu(Long roleId);

    /**
     * 根据团队id查询团队脱敏信息状态
     *
     * @return
     */
    Desensitization selectDesensitization(int createId);

    /**
     * 根据团队id查找团队审批设置
     *
     * @return
     */
    List<ApprovalSettings> selectApprovalSettings(int createId);

    /**
     * 根据时间区间分组查询每个团队的评价信息
     *
     * @param timeIntervalPojo
     * @return
     */
    List<Evaluate> selectEvaluate(TimeIntervalPojo timeIntervalPojo);

    /**
     * 根据时间区间以及团队id查询最大月回款率
     *
     * @param timeIntervalPojo
     * @return
     */
    BigDecimal selectTeamIdEvaluate(TimeIntervalPojo timeIntervalPojo);

    /**
     * 根据时间区间以及团队id查询已生成的记录信息
     *
     * @param timeIntervalPojo
     * @return
     */
    List<Long> selectEvaluateTeamId(TimeIntervalPojo timeIntervalPojo);

    /**
     * 根据团队id查找团队审批设置的角色信息
     *
     * @return
     */
    List<Role> selectRole(int createId);

    /**
     * 根据团队id查找团队审批流程默认字段
     *
     * @return
     */
    List<ApprovalSteps> selectApprovalSteps(ApprovalSteps approvalSteps);

    /**
     * 根据登陆人信息查询登陆人的审批流程
     *
     * @return
     */
    ApprovalSteps selectApproval(ApprovalSteps approvalSteps);

    /**
     * 分组查询合作状态数量
     *
     * @return
     */
    Map selectCount();

    /**
     * 根据团队id查询团队白名单信息
     *
     * @return
     */
    List<White> selectWhite(int createId);

    /**
     * 根据团队id/字段模糊查询团队白名单信息
     *
     * @return
     */
    List<White> selectWhiteVague(White white);

    /**
     * 根据团队id查询水印设置字段
     *
     * @return
     */
    Watermark selectWatermark(int createId);

    /**
     * 根据团队id查询对应的团队信息
     *
     * @return
     */
    Create findIdCreate(int id);

    /**
     * 根据团队id查询对应的团队密码
     *
     * @return
     */
    Create selectCreateIdPassword(int id);

    /**
     * 根据团队账号查询对应的团队信息
     *
     * @return
     */
    LoginUser findCreateAccount(String account, String password,String teamLevelType);

    /**
     * 根据团队id查询对应的团队标签
     *
     * @return
     */
    List<Label> findIdLabel(int id);

    /**
     * 根据字段条件查询团队表信息
     *
     * @param query
     * @return
     */
    List<Create> findAccurate(Query query);

    /**
     * 根据团队id查询该团队下所有凭证文件信息
     *
     * @param createId
     * @return
     */
    List<Files> findFiles(int createId);

    /**
     * 根据id查询团队凭证信息
     *
     * @param id
     * @return
     */
    Files findFilesId(int id);

    /**
     * 修改团队信息
     *
     * @param create
     * @return
     */
    int updateCreate(Create create);

    /**
     * 修改团队信息---(修改团队修改密码时间)
     *
     * @param create
     * @return
     */
    int updateCreatePassword(Create create);

    /**
     * 修改团队的呼叫中心入网企业编号
     *
     * @param create
     * @return
     */
    int updateCompanyNum(Create create);

    /**
     * 修改团队信息-最后登录时间
     *
     * @param create
     * @return
     */
    int updateCreateDate(Create create);

    /**
     * 根据团队id修改团队密码
     *
     * @param create
     * @return
     */
    int updateCreatePassWord(Create create);

    /**
     * （批量）重置团队密码
     */
    String updateCreatePassword(List<Create> create);

    /**
     * （批量）修改团队状态
     */
    int updateCreateCooperation(List<Create> create);

    /**
     * 重置密码服务（默认）
     */
    String password();

    /**
     * 修改团队标签
     *
     * @param label
     * @return
     */
    int updateLabel(List<Label> label);

    /**
     * 修改白名单信息
     *
     * @param white
     * @return
     */
    int updateWhite(White white);

    /**
     * 修改白名单状态
     *
     * @param white
     * @return
     */
    int updateState(White white);

    /**
     * 修改团队的月回款目标（最新目标）
     *
     * @param create
     * @return
     */
    int updateCreates(Create create);

    /**
     * 修改设置状态（0:关闭,1:启用）
     *
     * @param state
     * @return
     */
    int updateStates(State state);

    /**
     * 修改水印设置字段
     *
     * @param watermark
     * @return
     */
    int updateWatermark(Watermark watermark);

    /**
     * 脱敏状态设置修改
     *
     * @param desensitization
     * @return
     */
    int updateDesensitization(Desensitization desensitization);

    /**
     * 团队审批设置流程修改（批量）
     *
     * @param approvalSteps
     * @return
     */
    int updateApprovalSteps(@Param("approvalSteps") List<ApprovalSteps> approvalSteps);

    /**
     * 根据id删除白名单
     *
     * @param whites
     * @return
     */
    int deleteWhite(List<White> whites);

    /**
     * 根据id删除凭证文件信息
     *
     * @param id
     * @return
     */
    int deleteFiles(int id);

    /**
     * 根据id删除审批流程信息（批量）
     *
     * @param ids
     * @return
     */
    int deleteApprovalSteps(@Param("ids") List<Integer> ids);

    /**
     * 查询通话量
     *
     * @param timeIntervalPojo
     * @return
     */
    int selectCallRecord(TimeIntervalPojo timeIntervalPojo);

    /**
     * 查询团队导出设置列表
     *
     * @param createId 团队ID
     * @return
     */
    List<TeamExport> selectTeamExports(Integer createId);

    /**
     * 获取开启的团队导出按钮
     *
     * @param createId
     * @return
     */
    Set<String> selectCloseTeamExport(Integer createId);

    int updateTeamExport(TeamExport teamExport);

    int insertTeamExport(TeamExport teamExport);

    /**
     * 写入默认状态
     *
     * @param teamExport
     * @return
     */
    int insertTeamExportDefault(TeamExport teamExport);

    /**
     * 查询当前登录账号的审批流程
     * @param approveEnum 审批枚举
     * @return
     */
    ApprovalSteps getApprovalSteps(ApproveEnum approveEnum);

    /**
     * 判断是不是终审
     * @param approveEnum   审批枚举
     * @param approvalSteps 审批流程
     * @return
     */
    boolean checkUltimateProcedure(ApproveEnum approveEnum,ApprovalSteps approvalSteps);

    Integer getTeamEmployeesCooperation(String username);

    Integer getTeamCreateCooperation(String username);
}
