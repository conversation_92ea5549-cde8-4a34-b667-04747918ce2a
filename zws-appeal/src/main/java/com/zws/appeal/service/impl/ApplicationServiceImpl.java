package com.zws.appeal.service.impl;

import com.zws.appeal.domain.ApplyRecord;
import com.zws.appeal.domain.record.*;
import com.zws.appeal.mapper.ApplicationMapper;
import com.zws.appeal.service.ApplicationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 应用服务实现类
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@Service
public class ApplicationServiceImpl implements ApplicationService {

    @Resource
    private ApplicationMapper applicationMapper;

    /**
     * 修改审批流程判断是否有未走完的退案/留案/停催审核流程的数据
     *
     * @param
     */
    @Override
    public List<ApplyRecord> selectApplyRecord(int applyState, int teamId) {
        Map<String, Object> map = new HashMap<>();
        map.put("applyState", applyState);
        map.put("teamId", teamId);
        return applicationMapper.selectApplyRecord(map);
    }

    /**
     * 修改审批流程判断是否有未走完的回款审核流程的数据
     *
     * @param
     */
    @Override
    public List<RepaymentRecord> selectRepaymentRecord(int teamId) {
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", teamId);
        return applicationMapper.selectRepaymentRecord(map);
    }

    /**
     * 修改审批流程判断是否有未走完的减免审核流程的数据
     *
     * @param
     */
    @Override
    public List<ReductionRecord> selectReductionRecord(int teamId) {
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", teamId);
        return applicationMapper.selectReductionRecord(map);
    }

    /**
     * 修改审批流程判断是否有未走完的分期还款审核流程的数据
     *
     * @param
     */
    @Override
    public List<StagingRecord> selectStagingRecord(int teamId) {
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", teamId);
        return applicationMapper.selectStagingRecord(map);
    }

    /**
     * 修改审批流程判断是否有未走完的外访审核流程的数据
     *
     * @param
     */
    @Override
    public List<OutsideRecord> selectOutsideRecord(int teamId) {
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", teamId);
        return applicationMapper.selectOutsideRecord(map);
    }

    /**
     * 修改审批流程判断是否有未走完的资料审核流程的数据
     *
     * @param
     */
    @Override
    public List<RetrievalRecord> selectRetrievalRecord(int teamId) {
        Map<String, Object> map = new HashMap<>();
        map.put("teamId", teamId);
        return applicationMapper.selectRetrievalRecord(map);
    }
}
