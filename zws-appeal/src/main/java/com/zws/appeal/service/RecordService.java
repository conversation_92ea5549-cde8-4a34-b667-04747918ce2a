package com.zws.appeal.service;

import cn.hutool.core.date.DateTime;
import com.zws.appeal.pojo.appeal.*;
import com.zws.common.core.domain.sms.SmsParameters;
import com.zws.common.core.domain.sms.TemplateSms;
import com.zws.appeal.domain.*;
import com.zws.appeal.domain.record.*;
import com.zws.appeal.pojo.*;
import com.zws.dispose.pojo.ApplicationUtils;
import com.zws.system.api.domain.WorkAnnex;
import com.zws.system.api.domain.WorkFollowUp;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface RecordService {

    /**
     * 根据案件id查询案件详情
     *
     * @param caseId
     * @return
     */
    CaseManage selectCaseManageCaseId(Long caseId);

    /**
     * 根据字段查询该催收员的案件/该催收员案件全查
     *
     * @param manageQueryParam
     * @return
     */
    List<CaseManage> selectCaseManage(ManageQueryParam manageQueryParam);

    /**
     * 根据字段查询该催收员的案件/该催收员案件全查
     *
     * @param manageQueryParam
     * @return
     */
    List<CaseManage> selectNeedCaseManage(ManageQueryParam manageQueryParam);

    /**
     * 根据字段查询该催收员的案件/该催收员案件全查
     *
     * @param manageQueryParam
     * @return
     */
    List<SmsParameters> selectNeedSmsParameters(ManageQueryParam manageQueryParam);

    /**
     * 根据字段查询该催收员的案件/该催收员案件全查可以排除无效号码和超出发送次数的号码
     *
     * @param manageQueryParam
     * @return
     */
    Long selectNeedCaseManageCount(ManageQueryParam manageQueryParam);

    /**
     * 根据案件id查询案件详情
     *
     * @param caseId
     * @return
     */
    CaseManage selectCaseManageByCaseId(int caseId);

    /**
     * 该催收员案件全查（计算案件总量，案件总金额，当月案件回款总金额）
     *
     * @param manageQueryParam
     * @return
     */
    Map<String, Object> selectCaseManageMoney(ManageQueryParam manageQueryParam);

    /**
     * 根据字段查询该催收员的案件/该催收员案件全查
     *
     * @param manageQueryParam
     * @return
     */
    List<CaseManage> selectCaseManages(ManageQueryParam manageQueryParam);

    /**
     * 根据登录人id以及其他条件查询该催收员的所有案件的案件金额以及总数量
     *
     * @param manageQueryParam
     * @return
     */
    Map<String, Object> selectCaseManageMoneySize(ManageQueryParam manageQueryParam);

    /**
     * 根据条件查询统计催员所有案件金额以及总数量
     *
     * @param manageQueryParam
     * @return
     */
    Map<String, Object> selectMoneySizeById(ManageQueryParam manageQueryParam);

    /**
     * 查询上一条/下一条案件信息
     *
     * @param caseBeating
     * @return
     */
    List<CaseManage> selectCaseManagePrevious(CaseBeating caseBeating);

    /**
     * 查询案加总数量
     *
     * @param caseBeating
     * @return
     */
    int selectNeedCaseManageCount(CaseBeating caseBeating);

    /**
     * 根据案件id查询催收记录
     *
     * @param caseIds
     * @return
     */
    List<UrgeRecord> selectUrgeRecord(Long caseId, List<Long> caseIds, CaseManage caseManage1, String urgeTpye,Integer webSide);
    List<UrgeRecord> selectUrgeRecords(Long caseId, List<Long> caseIds, CaseManage caseManage1);

    /**
     * 统计团队每天的催记量
     *
     * @param map
     * @return
     */
    int selectUrgeRecordCount(Map<String, Object> map);

    /**
     * 根据申请人id以及其他条件查询案件详情表和催收承诺户申请表
     *
     * @param applicationUtils
     * @return
     */
    List<InheritanceCollection> selectUrgeRecordId(ApplicationUtils applicationUtils);

    /**
     * 根据案件id查询分期还款记录
     *
     * @param caseId
     * @return
     */
    List<StagingRecord> selectStagingRecord(Long caseId);

    /**
     * 根据案件id查询资料调取记录
     *
     * @param caseId
     * @return
     */
    List<RetrievalRecord> selectRetrievalRecord(Long caseId);

    /**
     * 根据案件id查询外访记录
     *
     * @param caseId
     * @return
     */
    List<OutsideRecord> selectOutsideRecord(Long caseId);

    /**
     * 根据申请人id以及其他条件查询案件详情表和分期申请表
     *
     * @param applicationUtils
     * @return
     */
    List<InheritanceCollection> selectStagingRecordId(ApplicationUtils applicationUtils);

    /**
     * 根据案件id查询回款记录
     *
     * @param caseId
     * @return
     */
    List<RepaymentRecord> selectRepaymentRecord(Long caseId, CaseManage caseManage1);

    /**
     * 根据登录人id以及其他条件查询案件详情表和回款申请表
     *
     * @param applicationUtils
     * @return
     */
    List<InheritanceCollection> selectRepaymentRecordId(ApplicationUtils applicationUtils);

    /**
     * 根据登录人id以及其他条件查询案件详情表和资料调取申请表
     *
     * @param applicationUtils
     * @return
     */
    List<InheritanceCollection> selectRetrievalRecordId(ApplicationUtils applicationUtils);

    /**
     * 查询每个团队当月已通过的回款申请回款金额统计
     *
     * @param map
     * @return
     */
    BigDecimal selectRepaymentByTime(Map<String, Object> map);

    /**
     * 查询每个团队时间区间内委案剩余应还债权总额统计
     *
     * @param map
     * @return
     */
    BigDecimal selectHistoryMoney(Map<String, Object> map);

    /**
     * 查询每个团队当天已通过的回款申请回款金额统计
     *
     * @param map
     * @return
     */
    BigDecimal selectRepaymentRecordIdByTime(Map<String, Object> map);

    /**
     * 根据申请id查询回款申请凭证信息
     *
     * @param id
     * @return
     */
    RepaymentRecord selectRepaymentRecordById(Long id);

    /**
     * 根据案件id和审核状态查询 末次还款时间
     *
     * @return
     */
    RepaymentRecord selectRepaymentByCaseIdAndState(Long caseId);

    /**
     * 根据申请id查询回款申请结清证明的Url
     *
     * @param id
     * @return
     */
    RepaymentRecord selectRepaymentRecordUrl(Long id);

    /**
     * 还款方式下拉查询
     *
     * @param repaymentSetup
     * @return
     */
    List<RepaymentSetup> selectRepaymentSetup(RepaymentSetup repaymentSetup);

    /**
     * 获取还款方式的登记必填字段
     *
     * @param id
     * @return
     */
    RepaymentSetup selectRepaymentSetupId(Long id);

    /**
     * 根据案件id查询减免记录
     *
     * @param caseId
     * @return
     */
    List<ReductionRecord> selectReductionRecord(Long caseId);

    /**
     * 根据案件id查询和解协议信息
     *
     * @param caseId
     * @return
     */
    SettleAgreementInfo selectSettleAgreementInfoById(Long caseId, Long stagingId);

    /**
     * 根据案件id查询和解协议信息
     *
     * @param caseId
     * @return
     */
    SettleAgreementInfo selectReductionSettleAgreementInfoById(Long caseId, Long reductionId);

    /**
     * 根据案件id以及团队id查询减免记录
     *
     * @param caseId
     * @return
     */
    List<ReductionRecord> selectReductionRecordCreateId(Long caseId);

    /**
     * 根据案件id查询剩余债权总额
     *
     * @param caseId
     * @return
     */
    BigDecimal selectInfoLoan(Long caseId);

    /**
     * 根据案件id以及催记权限查询减免记录
     *
     * @param caseId
     * @return
     */
    List<ReductionRecord> selectReductionRecordCaseId(Long caseId, CaseManage caseManage1);

    /**
     * 根据申请人id以及其他条件查询案件详情表和减免申请表
     *
     * @param applicationUtils
     * @return
     */
    List<InheritanceCollection> selectReductionRecordId(ApplicationUtils applicationUtils);

    /**
     * 根据申请id查询减免申请凭证文件信息
     *
     * @param id
     * @return
     */
    List<ReductionFile> selectReductionFile(Long id);

    /**
     * 根据案件id查询外访记录
     *
     * @param caseId
     * @return
     */
    List<OutsideRecord> selectOutsideRecord(Long caseId, CaseManage caseManage1);

    /**
     * 根据申请id外访资源分组数量查询
     *
     * @param outsideId
     * @return
     */
    List<Map<String, Object>> selectOutsideResource(Long outsideId);

    /**
     * 根据申请id以及内容类型查询外访内容
     *
     * @param outsideId
     * @return
     */
    List<OutsideResource> selectOutsideResourceById(Long outsideId, int resourceType);

    /**
     * 根据案件id以及申请id查询外访记录
     *
     * @param caseId
     * @return
     */
    OutsideRecord selectOutsideRecordIdBy(Long caseId, Long id);

    /**
     * 根据申请人id以及其他条件查询案件详情表和外访申请表
     *
     * @param
     * @return
     */
    List<OutsideRecordCollection> selectOutsideRecordId(OutsideRecordUtils outsideRecordUtils);

    /**
     * 根据案件id查询便签记录
     *
     * @param caseId
     * @return
     */
    List<NoteRecord> selectNoteRecord(Long caseId, CaseManage caseManage1);

    /**
     * 根据案件id查询诉讼记录
     *
     * @param caseId
     * @return
     */
    List<LawsuitRecord> selectLawsuitRecord(Long caseId, CaseManage caseManage1);

    /**
     * 根据案件id查询投诉记录
     *
     * @param caseId
     * @return
     */
    List<ComplaintRecord> selectComplaintRecord(Long caseId, CaseManage caseManage1);

    /**
     * 根据案件id查询协催记录
     *
     * @param caseId
     * @return
     */
    List<AssistRecord> selectAssistRecord(Long caseId, CaseManage caseManage1);

    /**
     * 根据申请人id以及其他条件查询案件详情表和协催申请表
     *
     * @param applicationUtils
     * @return
     */
    List<InheritanceCollection> selectAssistRecordId(ApplicationUtils applicationUtils);

    /**
     * 根据协催人id以及其他条件查询案件详情表和协催申请表
     *
     * @param applicationUtils
     * @return
     */
    List<InheritanceCollection> selectAssistRecordHelperId(ApplicationUtils applicationUtils);

    /**
     * 根据协催人id以及其他条件查询案件详情表和协催申请表统计数量
     *
     * @param applicationUtils
     * @return
     */
    List<ExpeditingCount> selectAssistRecordNumber(ApplicationUtils applicationUtils);

    /**
     * 根据协催申请id查询协催记录
     *
     * @param caseId
     * @return
     */
    List<AssistDetails> selectAssistDetails(Long caseId);

    /**
     * 根据案件id查询有效的协催记录
     *
     * @param caseId
     * @return
     */
    List<AssistRecord> selectAssistRecordInvalid(List<Long> caseId, int invalid);

    /**
     * 根据案件id倒序查询该案件最后一条便签
     *
     * @param caseId
     * @return
     */
    NoteRecord selectNoteRecordLast(Long caseId, CaseManage caseManage1);

    /**
     * 根据案件id倒序查询该案件最后一条投诉记录
     *
     * @param caseId
     * @return
     */
    ComplaintRecord selectComplaintRecordLast(Long caseId, CaseManage caseManage1);

    /**
     * 根据申请人id以及其他条件查询案件详情表和停案/留案/停催申请表
     *
     * @param applicationUtils
     * @return
     */
    List<InheritanceCollection> selectApplyRecordId(ApplicationUtils applicationUtils);

    /**
     * 根据登录人id以及其他条件查询我的工单
     *
     * @param workOrderRequest
     * @return
     */
    List<WorkOrderExtends> selectWorkOrder(WorkOrderRequest workOrderRequest);

    /**
     * 根据工单id查询工单详情
     *
     * @param id
     * @return
     */
    WorkOrder selectWorkOrderById(Long id);

    /**
     * 根据字典表键值查询工单问题类型标签以及内容
     *
     * @return
     */
    List<DictDataOrder> selectDictData();

    /**
     * 根据工单id查询工单跟进内容
     *
     * @param id
     * @return
     */
    List<WorkFollowUp> selectWorkFollowUpByWoId(Long id);

    /**
     * 根据工单跟进id查询工单跟进内容文件信息
     *
     * @param followId
     * @return
     */
    List<WorkAnnex> selectWorkAnnex(Long followId);

    /**
     * 根据登录人id以及其他条件查询我的工单统计各状态数量
     *
     * @param workOrderRequest
     * @return
     */
    List<Map<String, Object>> selectWorkOrderNumber(WorkOrderRequest workOrderRequest);

    /**
     * 根据团队id以及案件id查询协催申请
     *
     * @param teamId
     * @return
     */
    List<AssistRecord> selectAssistRecordCaseId(Long teamId, Long caseId);

    /**
     * 我的工单-写入继续跟进内容
     *
     * @param workFollowUp
     * @return
     */
    Long insertWorkOrderFollowUp(WorkFollowUp workFollowUp);

    /**
     * 我的工单-写入继续跟进文件信息
     *
     * @param workAnnex
     * @return
     */
    int insertWorkOrderAnnex(WorkAnnex workAnnex);

    /**
     * 写入催收记录
     *
     * @param urgeRecord
     * @return
     */
    int insertUrgeRecord(UrgeRecord urgeRecord);

    /**
     * 写入便签
     *
     * @param noteRecord
     * @return
     */
    int insertNoteRecord(NoteRecord noteRecord);

    /**
     * 写入投诉记录
     *
     * @param complaintRecord
     * @return
     */
    int insertComplaintRecord(ComplaintRecord complaintRecord);

    /**
     * 写入回款申请表
     *
     * @param repaymentRecord
     * @return 返回ID
     */
    Long insertRepaymentRecord(RepaymentRecord repaymentRecord);

    /**
     * 写入减免记录表
     *
     * @param reductionRecord
     * @return
     */
    int insertReductionRecord(ReductionRecord reductionRecord);

    /**
     * 写入减免上传文件信息
     *
     * @param reductionFiles
     * @return
     */
    int insertReductionFile(List<ReductionFile> reductionFiles);

    /**
     * 写入分期还款申请记录表
     *
     * @param stagingRecord
     * @return
     */
    int insertStagingRecord(StagingRecord stagingRecord);

    /**
     * 写入资料调取申请记录表
     *
     * @param retrievalRecord
     * @return
     */
    int insertRetrievalRecord(RetrievalRecord retrievalRecord);

    /**
     * 写入协催申请记录表
     *
     * @param assistRecord
     * @return
     */
    int insertAssistRecord(List<AssistRecord> assistRecord);

    /**
     * 写入协催申请记录详情表
     *
     * @param assistDetails
     * @return
     */
    int insertAssistDetails(AssistDetails assistDetails);

    /**
     * 写入外访申请记录详情表
     *
     * @param outsideRecord
     * @return
     */
    int insertOutsideRecord(OutsideRecord outsideRecord);

    /**
     * 修改协催申请记录是否有效
     *
     * @param assistRecords
     * @return
     */
    int updateAssistRecord(List<AssistRecord> assistRecords);

    /**
     * 修改案件详情表信息
     *
     * @param caseManage
     * @return
     */
    int updateCaseManage(CaseManage caseManage);

    /**
     * 修改案件贷款信息表信息
     *
     * @param infoLoan
     * @return
     */
    int updateInfoLoan(InfoLoan infoLoan);

    /**
     * 修改协催申请状态
     *
     * @param assistRecords
     * @return
     */
    int updateAssistRecordState(AssistRecord assistRecords);

    /**
     * 修改工单状态以及工单处理人信息
     *
     * @param workOrder
     * @return
     */
    int updateWorkOrderById(WorkOrder workOrder);

    /**
     * 回款申请判断是否是待审核案件
     *
     * @param ids
     * @return
     */
    List<RepaymentRecord> selectRepaymentRecordProce(List<Long> ids);

    /**
     * 减免申请判断是否是待审核案件
     *
     * @param ids
     * @return
     */
    List<ReductionRecord> selectReductionRecordProce(List<Long> ids);

    /**
     * 分期还款申请判断是否是待审核案件
     *
     * @param ids
     * @return
     */
    List<StagingRecord> selectStagingRecordProce(List<Long> ids);

    /**
     * 外访申请判断是否是待审核案件
     *
     * @param ids
     * @return
     */
    List<OutsideRecord> selectOutsideRecordProce(List<Long> ids);

    /**
     * 资料调取申请判断是否是待审核案件
     *
     * @param ids
     * @return
     */
    List<RetrievalRecord> selectRetrievalRecordProce(List<Long> ids);

    /**
     * 协催申请判断是否是待审核案件
     *
     * @param ids
     * @return
     */
    List<AssistRecord> selectAssistRecordProce(List<Long> ids);

    /**
     * 停催/退案/留案申请判断是否是待审核案件
     *
     * @param ids
     * @return
     */
    List<ApplyRecord> selectApplyRecordProce(List<Long> ids);

    /**
     * 根据工单id集合查询工单详情
     *
     * @param ids
     * @return
     */
    List<WorkOrder> selectWorkOrderByIds(List<Long> ids);

    /**
     * 根据申请id批量撤销回款申请
     *
     * @param ids
     * @return
     */
    int updateRepaymentRecord(List<Long> ids);

    /**
     * 根据申请id批量撤销减免申请
     *
     * @param ids
     * @return
     */
    int updateReductionRecord(List<Long> ids);

    /**
     * 根据申请id批量撤销减免申请
     *
     * @param reductionRecord
     * @return
     */
    int updateReductionRecordInfo(ReductionRecord reductionRecord);

    /**
     * 根据申请id批量撤销分期还款申请
     *
     * @param ids
     * @return
     */
    int updateStagingRecord(List<Long> ids);

    /**
     * 根据申请id批量撤销外访申请
     *
     * @param ids
     * @return
     */
    int updateOutsideRecord(List<Long> ids);

    /**
     * 根据申请id批量撤销资料调取申请
     *
     * @param ids
     * @return
     */
    int updateRetrievalRecord(List<Long> ids);

    /**
     * 根据申请id批量撤销协催申请
     *
     * @param ids
     * @return
     */
    int updateAssistRecords(List<Long> ids);

    /**
     * 根据申请id批量撤销停案/留案/停催申请
     *
     * @param ids
     * @return
     */
    int updateApplyRecord(List<Long> ids, int applyState);

    /**
     * 根据主键id集合批量处理工单
     *
     * @param ids
     * @return
     */
    int updateWorkOrder(List<Long> ids);

    /**
     * 完成外访修改状态
     *
     * @param outsideRecord
     * @return
     */
    int updateVisitStatus(OutsideRecord outsideRecord);

    /**
     * 根据id查询分期记录
     *
     * @param id
     * @return
     */
    StagingRecord selectStagingRecordById(Long id);

    /**
     * 根据id查询减免记录
     *
     * @param id
     * @return
     */
    ReductionRecord selectReductionRecordById(Long id);

    /**
     * 根据手机号查询该用户当天是否成功发送短信
     * @param phone
     * @param dateTime
     * @param dateTime1
     * @return
     */
    List<SendRecords> selectFailMessage(String phone, DateTime dateTime, DateTime dateTime1);

    /**
     * 根据联系人id查询联系人信息
     * @param id
     * @return
     */
    InfoContact selectCwContact(Long id);

    /**
     * 查询共债案件
     * @param map
     * @return
     */
    List<String> selectBankCardNumber(Map<String, String> map);

    /**
     * 根据id查询短信模板信息
     * @param id
     * @return
     */
    TemplateSms selectTemplateById(Long id);

    /**
     * 根据案件id查询短信参数
     * @param caseId
     * @return
     */
    SmsParameters selectSmsParameter(Long caseId);

    /**
     * 查询案件信息
     * @param map
     * @return
     */
    List<SendMsg> selectSendMsgByIds(Map<String, Object> map);

    /**
     * 根据案件id查询重组记录
     *
     * @param caseId     案例 ID
     * @param caseManage 案例管理
     * @return {@link List}<{@link ReductionRecord}>
     */
    List<ReorganizationVo> selectReorganizationRecordCaseId(Long caseId, CaseManage caseManage);

    InfoLoan selectInfoLoanByCaseId(Long caseId);
}
