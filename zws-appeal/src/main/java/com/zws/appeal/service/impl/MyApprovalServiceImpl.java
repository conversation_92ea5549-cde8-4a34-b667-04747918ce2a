package com.zws.appeal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.agservice.TeamSysAgService;
import com.zws.appeal.domain.ApplyRecord;
import com.zws.appeal.domain.ApproveProce;
import com.zws.appeal.domain.Create;
import com.zws.appeal.domain.Employees;
import com.zws.appeal.domain.record.*;
import com.zws.appeal.mapper.MyApprovalMapper;
import com.zws.appeal.pojo.OutsideCollectionUtils;
import com.zws.appeal.pojo.PaymentCollectionUtils;
import com.zws.appeal.pojo.QueryCriteria;
import com.zws.appeal.pojo.SignCollectionUtils;
import com.zws.appeal.pojo.myApproval.*;
import com.zws.appeal.service.MyApprovalService;
import com.zws.appeal.service.SettingsService;
import com.zws.appeal.service.TeamService;
import com.zws.appeal.utils.SplitUtils;
import com.zws.appeal.utils.TokenInformation;
import com.zws.system.api.domain.CaseSignRecord;
import com.zws.system.api.domain.SysUser;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;


/**
 * 我的审批服务实现类
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@Service
public class MyApprovalServiceImpl implements MyApprovalService {

    @Resource
    private MyApprovalMapper myApprovalMapper;
    @Resource
    private TeamService teamService;
    @Resource
    private SettingsService settingsService;
    @Autowired
    private TeamSysAgService teamSysAgService;

    /**
     * 处理时间-(留案/退案/停催)
     *
     * @param queryCriteria
     * @return
     */
    public QueryCriteria verificationCaseDetails(QueryCriteria queryCriteria) {
        //        修改结束时间为一天的结束
        if (!ObjectUtils.isEmpty(queryCriteria.getExamineTime2())) {
            DateTime dateTime = DateUtil.endOfDay(queryCriteria.getExamineTime2());  //一天的结束，结果：2017-03-01 23:59:59
            queryCriteria.setExamineTime2(dateTime);
        }
        if (!ObjectUtils.isEmpty(queryCriteria.getEntrustingCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(queryCriteria.getEntrustingCaseDate2());
            queryCriteria.setEntrustingCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(queryCriteria.getReturnCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(queryCriteria.getReturnCaseDate2());
            queryCriteria.setReturnCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(queryCriteria.getApplyDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(queryCriteria.getApplyDate2());
            queryCriteria.setApplyDate2(dateTime1);
        }
        return queryCriteria;
    }

//    /**
//     * 根据条件查询我的审批信息（停案/留案/停催）-通过/未通过案件
//     *
//     * @param queryCriteria
//     * @return
//     */
//    @Override
//    public List<myApplyRecordUtils> selectCaseDetails(QueryCriteria queryCriteria) {
//        String examineState = queryCriteria.getExamineState();
//        if (!ObjectUtils.isEmpty(examineState)) {
//            List<String> list = SplitUtils.strSplitComma(examineState);
//            queryCriteria.setExamineStateList(list);
//        }
//        QueryCriteria queryCriteria1 = verificationCaseDetails(queryCriteria);
//        Map<String, Object> map = BeanUtil.beanToMap(queryCriteria1);
//        return myApprovalMapper.selectCaseDetails(map);
//    }

//    /**
//     * 根据条件查询我的审批信息（停案/留案/停催）-待审核案件
//     *
//     * @param queryCriteria
//     * @return
//     */
//    @Override
//    public List<myApplyRecordUtils> selectApplyRecord(QueryCriteria queryCriteria) {
//        String examineState = queryCriteria.getExamineState();
//        if (!ObjectUtils.isEmpty(examineState)) {
//            List<String> list = SplitUtils.strSplitComma(examineState);
//            queryCriteria.setExamineStateList(list);
//        }
//        QueryCriteria queryCriteria1 = verificationCaseDetails(queryCriteria);
//        Map<String, Object> map = BeanUtil.beanToMap(queryCriteria1);
//        List<myApplyRecordUtils> myApplyRecordUtils = myApprovalMapper.selectApplyRecord(map);
//        for (myApplyRecordUtils myApplyRecordUtils1 : myApplyRecordUtils) {
//            if (myApplyRecordUtils1.getExamineState().equals("待审核") || myApplyRecordUtils1.getExamineState().equals("审核中")) {
//                myApplyRecordUtils1.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
//            }
//        }
//        return myApplyRecordUtils;
//    }

    /**
     * 根据条件查询我的审批信息（停案/留案/停催）-通过/未通过案件/待审核案件
     *
     * @param queryCriteria
     * @return
     */
    @Override
    public List<myApplyRecordUtils> selectApplyRecord(QueryCriteria queryCriteria) {
        String examineState = queryCriteria.getExamineState();
        if (!ObjectUtils.isEmpty(examineState)) {
            List<String> list = SplitUtils.strSplitComma(examineState);
            queryCriteria.setExamineStateList(list);
        }
        QueryCriteria queryCriteria1 = verificationCaseDetails(queryCriteria);
        Map<String, Object> map = BeanUtil.beanToMap(queryCriteria1);

        Map<String, Object> map1 = generateProceApplyRecord(queryCriteria, map);
        PageUtils.startPage();
        List<myApplyRecordUtils> myApplyRecordUtils = myApprovalMapper.selectApplyRecord(map1);

        return myApplyRecordUtils;
    }

    /**
     * 根据条件查询我的资料审批审批信息-通过/未通过案件/待审核案件
     *
     * @param paymentCollectionUtils
     * @return
     */
    @Override
    public List<MyRetrievalRecordUtils> selectRetrievalRecord(PaymentCollectionUtils paymentCollectionUtils) {
        String examineState = paymentCollectionUtils.getExamineState();
        if (!ObjectUtils.isEmpty(examineState)) {
            List<String> list = SplitUtils.strSplitComma(examineState);
            paymentCollectionUtils.setExamineStateList(list);
        }
        PaymentCollectionUtils verification = verification(paymentCollectionUtils);
        Map<String, Object> map = BeanUtil.beanToMap(verification);

        Map<String, Object> map1 = generateProceParam(verification, map);
        PageUtils.startPage();
        List<MyRetrievalRecordUtils> myRetrievalRecordUtils = myApprovalMapper.selectRetrievalRecord(map1);

        return myRetrievalRecordUtils;
    }

    /**
     * 生成审批查询参数
     *
     * @param queryCriteria
     * @param param
     * @return
     */
    private Map<String, Object> generateProceApplyRecord(QueryCriteria queryCriteria, Map<String, Object> param) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        param.put("reviewerId", TokenInformation.getUserid());
        param.remove("selectAll");
        if (queryCriteria.getApproveStarts() == null) {
            param.put("selectAll", "true");
        } else {
            param.put("approveStart", queryCriteria.getApproveStarts());
        }
        param.put("operationType", TokenInformation.getType());
        if (queryCriteria.getApproveStarts() == null || queryCriteria.getApproveStarts() == 2) {
            boolean approvalAuthority = teamSysAgService.checkApprovalAuthority(loginUser);
            if (approvalAuthority) {
                param.put("approveSort", 1);
            } else {
                param.put("approveSort", 999999);
            }
            param.put("approveStart", null);

        }
        //部门是否有负责人
        if (TokenInformation.getType(loginUser) == UserConstants.ACCOUNT_TYPE_0) {
            //团队主账号 只能查看没有 部门负责人的部门
            param.put("haveHead", 0);
        } else {
            //只能查本部门的
            param.put("deptIds", Arrays.asList(TokenInformation.getDeptId()));
        }

        return param;
    }

    /**
     * 处理时间-(回款/减免/分期还款)
     *
     * @param paymentCollectionUtils
     * @return
     */
    public PaymentCollectionUtils verification(PaymentCollectionUtils paymentCollectionUtils) {
        //        修改结束时间为一天的结束
        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getUpdateTime2())) {
            DateTime dateTime = DateUtil.endOfDay(paymentCollectionUtils.getUpdateTime2());  //一天的结束，结果：2017-03-01 23:59:59
            paymentCollectionUtils.setUpdateTime2(dateTime);
        }
        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getEntrustingCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(paymentCollectionUtils.getEntrustingCaseDate2());
            paymentCollectionUtils.setEntrustingCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getReturnCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(paymentCollectionUtils.getReturnCaseDate2());
            paymentCollectionUtils.setReturnCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getApplyDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(paymentCollectionUtils.getApplyDate2());
            paymentCollectionUtils.setApplyDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getCreateTime2())) {
            DateTime dateTime1 = DateUtil.endOfDay(paymentCollectionUtils.getCreateTime2());
            paymentCollectionUtils.setCreateTime2(dateTime1);
        }
        return paymentCollectionUtils;
    }

//    /**
//     * 根据条件查询我的回款审批信息-待审核案件
//     *
//     * @param paymentCollectionUtils
//     * @return
//     */
//    @Override
//    public List<myRepaymentRecordUtils> selectRepaymentRecord(PaymentCollectionUtils paymentCollectionUtils) {
//        String examineState = paymentCollectionUtils.getExamineState();
//        if (!ObjectUtils.isEmpty(examineState)) {
//            List<String> list = SplitUtils.strSplitComma(examineState);
//            paymentCollectionUtils.setExamineStateList(list);
//        }
//        PaymentCollectionUtils verification = verification(paymentCollectionUtils);
//        Map<String, Object> map = BeanUtil.beanToMap(verification);
//        List<myRepaymentRecordUtils> myRepaymentRecordUtils = myApprovalMapper.selectRepaymentRecord(map);
//        for (myRepaymentRecordUtils myRepaymentRecordUtil : myRepaymentRecordUtils) {
//            if (myRepaymentRecordUtil.getExamineState().equals("待审核") || myRepaymentRecordUtil.getExamineState().equals("审核中")) {
//                myRepaymentRecordUtil.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
//            }
//        }
//        return myRepaymentRecordUtils;
//    }
//
//    /**
//     * 根据条件查询我的回款审批信息-通过/未通过案件
//     *
//     * @param paymentCollectionUtils
//     * @return
//     */
//    @Override
//    public List<myRepaymentRecordUtils> selectRepaymentRecordProce(PaymentCollectionUtils paymentCollectionUtils) {
//        String examineState = paymentCollectionUtils.getExamineState();
//        if (!ObjectUtils.isEmpty(examineState)) {
//            List<String> list = SplitUtils.strSplitComma(examineState);
//            paymentCollectionUtils.setExamineStateList(list);
//        }
//        PaymentCollectionUtils verification = verification(paymentCollectionUtils);
//        Map<String, Object> map = BeanUtil.beanToMap(verification);
//        return myApprovalMapper.selectRepaymentRecordProce(map);
//    }

    /**
     * 根据条件查询我的回款审批信息-通过/未通过案件/待审核案件
     *
     * @param paymentCollectionUtils
     * @return
     */
    @Override
    public List<myRepaymentRecordUtils> selectToBeReviewd(PaymentCollectionUtils paymentCollectionUtils) {
        String examineState = paymentCollectionUtils.getExamineState();
        if (!ObjectUtils.isEmpty(examineState)) {
            List<String> list = SplitUtils.strSplitComma(examineState);
            paymentCollectionUtils.setExamineStateList(list);
        }
        PaymentCollectionUtils verification = verification(paymentCollectionUtils);
        Map<String, Object> map = BeanUtil.beanToMap(verification);

        Map<String, Object> map1 = generateProceParam(paymentCollectionUtils, map);
        PageUtils.startPage();
        List<myRepaymentRecordUtils> myRepaymentRecordUtils = myApprovalMapper.selectToBeReviewd(map1);

        return myRepaymentRecordUtils;
    }

    /**
     * 生成审批查询参数
     *
     * @param paymentCollectionUtils
     * @param param
     * @return
     */
    private Map<String, Object> generateProceParam(PaymentCollectionUtils paymentCollectionUtils, Map<String, Object> param) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        param.put("reviewerId", TokenInformation.getUserid());
        param.remove("selectAll");

        if (paymentCollectionUtils.getApproveStarts() == null) {
            param.put("selectAll", "true");
        } else {
            param.put("approveStart", paymentCollectionUtils.getApproveStarts());
        }

        param.put("operationType", TokenInformation.getType());

        List<String> examineStateList = new ArrayList<>();
        if (paymentCollectionUtils.getApproveStarts() != null && paymentCollectionUtils.getApproveStarts() == 4) {
            examineStateList.add("待上传凭证");
        }
        if (paymentCollectionUtils.getApproveStarts() != null && paymentCollectionUtils.getApproveStarts() == 2) {
            examineStateList.add("待审核");
        }
        if (examineStateList.size() > 0) {
            param.put("examineStateList", examineStateList);
        }
        if (paymentCollectionUtils.getApproveStarts() == null || paymentCollectionUtils.getApproveStarts() == 2
                || paymentCollectionUtils.getApproveStarts() == 4) {
            //判断是否有审批权限
            boolean approvalAuth = teamSysAgService.checkApprovalAuthority(loginUser);
            if (approvalAuth) {
                //有审批权限的,审批步骤都设置为1，写死只有一级审批
                param.put("approveSort", 1);
            } else {
                param.put("approveSort", 9999);
            }
            param.put("approveStart", null);
        }

        //部门是否有负责人
        if (TokenInformation.getType(loginUser) == UserConstants.ACCOUNT_TYPE_0) {
            //团队主账号 只能查看没有 部门负责人的部门
            param.put("haveHead", 0);
        } else {
            //只能查本部门的
            param.put("deptIds", Arrays.asList(TokenInformation.getDeptId()));
        }
        return param;
    }

    /**
     * 根据条件查询我的减免审批信息-通过/未通过案件/待审核案件
     *
     * @param paymentCollectionUtils
     * @return
     */
    @Override
    public List<myReductionRecordUtils> selectReductionRecord(PaymentCollectionUtils paymentCollectionUtils) {
        String state = paymentCollectionUtils.getState();
        if (!ObjectUtils.isEmpty(state)) {
            List<String> list = SplitUtils.strSplitComma(state);
            paymentCollectionUtils.setStateList(list);
        }
        PaymentCollectionUtils verification = verification(paymentCollectionUtils);
        Map<String, Object> map = BeanUtil.beanToMap(verification);

        Map<String, Object> map1 = generateProceParam(paymentCollectionUtils, map);
        PageUtils.startPage();
//        PageDomain pageDomain = TableSupport.buildPageRequest();
//        Integer pageNum = pageDomain.getPageNum();
//        Integer pageSize = pageDomain.getPageSize();
//        PageHelper.startPage(pageNum, pageSize);
        List<myReductionRecordUtils> myReductionRecordUtils = myApprovalMapper.selectReductionRecord(map1);

        return myReductionRecordUtils;
    }

//    /**
//     * 根据条件查询我的减免审批信息-待审核案件
//     *
//     * @param paymentCollectionUtils
//     * @return
//     */
//    @Override
//    public List<myReductionRecordUtils> selectReductionRecord(PaymentCollectionUtils paymentCollectionUtils) {
//        String state = paymentCollectionUtils.getState();
//        if (!ObjectUtils.isEmpty(state)) {
//            List<String> list = SplitUtils.strSplitComma(state);
//            paymentCollectionUtils.setStateList(list);
//        }
//        PaymentCollectionUtils verification = verification(paymentCollectionUtils);
//        Map<String, Object> map = BeanUtil.beanToMap(verification);
//        List<myReductionRecordUtils> myReductionRecordUtils = myApprovalMapper.selectReductionRecord(map);
//        for (myReductionRecordUtils myReductionRecordUtil : myReductionRecordUtils) {
//            if (myReductionRecordUtil.getState().equals("待审核") || myReductionRecordUtil.getState().equals("审核中")) {
//                myReductionRecordUtil.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
//            }
//        }
//        return myReductionRecordUtils;
//    }
//
//    /**
//     * 根据条件查询我的减免审批信息-通过/未通过案件
//     *
//     * @param paymentCollectionUtils
//     * @return
//     */
//    @Override
//    public List<myReductionRecordUtils> selectReductionRecordProce(PaymentCollectionUtils paymentCollectionUtils) {
//        String state = paymentCollectionUtils.getState();
//        if (!ObjectUtils.isEmpty(state)) {
//            List<String> list = SplitUtils.strSplitComma(state);
//            paymentCollectionUtils.setStateList(list);
//        }
//        PaymentCollectionUtils verification = verification(paymentCollectionUtils);
//        Map<String, Object> map = BeanUtil.beanToMap(verification);
//        return myApprovalMapper.selectReductionRecordProce(map);
//    }

    /**
     * 根据条件查询我的分期还款审批信息-通过/未通过案件/待审核案件
     *
     * @param paymentCollectionUtils
     * @return
     */
    @Override
    public List<MyStagingRecordUtils> selectStagingRecord(PaymentCollectionUtils paymentCollectionUtils) {
        String examineState = paymentCollectionUtils.getExamineState();
        if (!ObjectUtils.isEmpty(examineState)) {
            List<String> list = SplitUtils.strSplitComma(examineState);
            paymentCollectionUtils.setExamineStateList(list);
        }
        PaymentCollectionUtils verification = verification(paymentCollectionUtils);
        Map<String, Object> map = BeanUtil.beanToMap(verification);

        Map<String, Object> map1 = generateProceParam(paymentCollectionUtils, map);
        PageUtils.startPage();
        List<MyStagingRecordUtils> myStagingRecordUtils = myApprovalMapper.selectStagingRecord(map1);

        return myStagingRecordUtils;
    }

//    /**
//     * 根据条件查询我的分期还款审批信息-待审核案件
//     *
//     * @param paymentCollectionUtils
//     * @return
//     */
//    @Override
//    public List<MyStagingRecordUtils> selectStagingRecord(PaymentCollectionUtils paymentCollectionUtils) {
//        String examineState = paymentCollectionUtils.getExamineState();
//        if (!ObjectUtils.isEmpty(examineState)) {
//            List<String> list = SplitUtils.strSplitComma(examineState);
//            paymentCollectionUtils.setExamineStateList(list);
//        }
//        PaymentCollectionUtils verification = verification(paymentCollectionUtils);
//        Map<String, Object> map = BeanUtil.beanToMap(verification);
//        List<MyStagingRecordUtils> myStagingRecordUtils = myApprovalMapper.selectStagingRecord(map);
//        for (MyStagingRecordUtils myStagingRecordUtil : myStagingRecordUtils) {
//            if (myStagingRecordUtil.getExamineState().equals("待审核") || myStagingRecordUtil.getExamineState().equals("审核中")) {
//                myStagingRecordUtil.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
//            }
//        }
//        return myStagingRecordUtils;
//    }
//
//    /**
//     * 根据条件查询我的分期还款审批信息-通过/未通过案件
//     *
//     * @param paymentCollectionUtils
//     * @return
//     */
//    @Override
//    public List<MyStagingRecordUtils> selectStagingRecordProce(PaymentCollectionUtils paymentCollectionUtils) {
//        String examineState = paymentCollectionUtils.getExamineState();
//        if (!ObjectUtils.isEmpty(examineState)) {
//            List<String> list = SplitUtils.strSplitComma(examineState);
//            paymentCollectionUtils.setExamineStateList(list);
//        }
//        PaymentCollectionUtils verification = verification(paymentCollectionUtils);
//        Map<String, Object> map = BeanUtil.beanToMap(verification);
//        return myApprovalMapper.selectStagingRecordProce(map);
//    }

    /**
     * 处理时间-（我的外访审批）
     *
     * @param outsideCollectionUtils
     * @return
     */
    public OutsideCollectionUtils verificationOutsideRecord(OutsideCollectionUtils outsideCollectionUtils) {
        //        修改结束时间为一天的结束
        if (!ObjectUtils.isEmpty(outsideCollectionUtils.getApproveTime2())) {
            DateTime dateTime = DateUtil.endOfDay(outsideCollectionUtils.getApproveTime2());  //一天的结束，结果：2017-03-01 23:59:59
            outsideCollectionUtils.setApproveTime2(dateTime);
        }
        if (!ObjectUtils.isEmpty(outsideCollectionUtils.getEntrustingCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(outsideCollectionUtils.getEntrustingCaseDate2());
            outsideCollectionUtils.setEntrustingCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(outsideCollectionUtils.getReturnCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(outsideCollectionUtils.getReturnCaseDate2());
            outsideCollectionUtils.setReturnCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(outsideCollectionUtils.getCreateTime2())) {
            DateTime dateTime1 = DateUtil.endOfDay(outsideCollectionUtils.getCreateTime2());
            outsideCollectionUtils.setCreateTime2(dateTime1);
        }
        return outsideCollectionUtils;
    }

    /**
     * 处理时间-（我的签章审批）
     *
     * @param signCollectionUtils
     * @return
     */
    public SignCollectionUtils verificationSignRecord(SignCollectionUtils signCollectionUtils) {
        //        修改结束时间为一天的结束
        if (!ObjectUtils.isEmpty(signCollectionUtils.getApproveTime2())) {
            DateTime dateTime = DateUtil.endOfDay(signCollectionUtils.getApproveTime2());  //一天的结束，结果：2017-03-01 23:59:59
            signCollectionUtils.setApproveTime2(dateTime);
        }
        if (!ObjectUtils.isEmpty(signCollectionUtils.getEntrustingCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(signCollectionUtils.getEntrustingCaseDate2());
            signCollectionUtils.setEntrustingCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(signCollectionUtils.getReturnCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(signCollectionUtils.getReturnCaseDate2());
            signCollectionUtils.setReturnCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(signCollectionUtils.getCreateTime2())) {
            DateTime dateTime1 = DateUtil.endOfDay(signCollectionUtils.getCreateTime2());
            signCollectionUtils.setCreateTime2(dateTime1);
        }
        return signCollectionUtils;
    }

    /**
     * 生成审批查询参数-外访审批
     *
     * @param outsideCollectionUtils1
     * @param param
     * @return
     */
    private Map<String, Object> generateProceOutsideRecord(OutsideCollectionUtils outsideCollectionUtils1, Map<String, Object> param) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        param.put("reviewerId", TokenInformation.getUserid());
        param.remove("selectAll");
        if (outsideCollectionUtils1.getApproveStarts() == null) {
            param.put("selectAll", "true");
        } else {
            param.put("approveStart", outsideCollectionUtils1.getApproveStarts());
        }
        param.put("operationType", TokenInformation.getType());
        if (outsideCollectionUtils1.getApproveStarts() == null || outsideCollectionUtils1.getApproveStarts() == 2) {
            boolean approvalAuthority = teamSysAgService.checkApprovalAuthority(loginUser);
            if (approvalAuthority) {
                param.put("approveSort", 1);
            } else {
                param.put("approveSort", 999999);
            }
            param.put("approveStart", null);
        }
        //部门是否有负责人
        if (TokenInformation.getType(loginUser) == UserConstants.ACCOUNT_TYPE_0) {
            //团队主账号 只能查看没有 部门负责人的部门
            param.put("haveHead", 0);
        }
        return param;
    }

    /**
     * 生成审批查询参数-签章审批
     *
     * @param signCollectionUtils1
     * @param param
     * @return
     */
    private Map<String, Object> generateProceSignRecord(SignCollectionUtils signCollectionUtils1, Map<String, Object> param) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        param.put("reviewerId", TokenInformation.getUserid());
        param.remove("selectAll");
        if (signCollectionUtils1.getApproveStarts() == null) {
            param.put("selectAll", "true");
        } else {
            param.put("approveStart", signCollectionUtils1.getApproveStarts());
        }
        param.put("operationType", TokenInformation.getType());
        if (signCollectionUtils1.getApproveStarts() == null || signCollectionUtils1.getApproveStarts() == 2) {
            boolean approvalAuthority = teamSysAgService.checkApprovalAuthority(loginUser);
            if (approvalAuthority) {
                param.put("approveSort", 1);
            } else {
                param.put("approveSort", 999999);
            }
            param.put("approveStart", null);
        }

        //部门是否有负责人
        if (TokenInformation.getType(loginUser) == UserConstants.ACCOUNT_TYPE_0) {
            //团队主账号 只能查看没有 部门负责人的部门
            param.put("haveHead", 0);
        } else {
            //只能查本部门的
            param.put("deptIds", Arrays.asList(TokenInformation.getDeptId()));
        }
        return param;
    }

    /**
     * 根据条件查询我的外访审批信息-通过/未通过案件/待审核案件
     *
     * @param outsideCollectionUtils
     * @return
     */
    @Override
    public List<MyOutsideRecordUtils> selectOutsideRecord(OutsideCollectionUtils outsideCollectionUtils) {
        String state = outsideCollectionUtils.getState();
        if (!ObjectUtils.isEmpty(state)) {
            List<String> list = SplitUtils.strSplitComma(state);
            outsideCollectionUtils.setStateList(list);
        }
        OutsideCollectionUtils outsideCollectionUtils1 = verificationOutsideRecord(outsideCollectionUtils);
        Map<String, Object> map = BeanUtil.beanToMap(outsideCollectionUtils1);

        Map<String, Object> map1 = generateProceOutsideRecord(outsideCollectionUtils1, map);
        PageUtils.startPage();
        List<MyOutsideRecordUtils> myOutsideRecordUtils = myApprovalMapper.selectOutsideRecord(map1);

        return myOutsideRecordUtils;
    }

    /**
     * 根据条件查询我的签章审批信息-通过/未通过案件/待审核案件
     *
     * @param signCollectionUtils
     * @return
     */
    @Override
    public List<MySignRecordUtils> selectSignRecord(SignCollectionUtils signCollectionUtils) {
        String state = signCollectionUtils.getState();
        if (!ObjectUtils.isEmpty(state)) {
            List<String> list = SplitUtils.strSplitComma(state);
            signCollectionUtils.setStateList(list);
        }
        SignCollectionUtils signCollectionUtils1 = verificationSignRecord(signCollectionUtils);
        Map<String, Object> map = BeanUtil.beanToMap(signCollectionUtils1);

        Map<String, Object> map1 = generateProceSignRecord(signCollectionUtils1, map);
        PageUtils.startPage();
        List<MySignRecordUtils> myOutsideRecordUtils = myApprovalMapper.selectSignRecord(map1);

        return myOutsideRecordUtils;
    }

//    /**
//     * 根据条件查询我的外访审批信息-待审核案件
//     *
//     * @param outsideCollectionUtils
//     * @return
//     */
//    @Override
//    public List<MyOutsideRecordUtils> selectOutsideRecord(OutsideCollectionUtils outsideCollectionUtils) {
//        String state = outsideCollectionUtils.getState();
//        if (!ObjectUtils.isEmpty(state)) {
//            List<String> list = SplitUtils.strSplitComma(state);
//            outsideCollectionUtils.setStateList(list);
//        }
//        OutsideCollectionUtils outsideCollectionUtils1 = verificationOutsideRecord(outsideCollectionUtils);
//        Map<String, Object> map = BeanUtil.beanToMap(outsideCollectionUtils1);
//        map.put("operationType", TokenInformation.getType());
//        map.put("userId", TokenInformation.getUserid());
//        List<MyOutsideRecordUtils> myOutsideRecordUtils = myApprovalMapper.selectOutsideRecord(map);
//        for (MyOutsideRecordUtils myOutsideRecordUtil : myOutsideRecordUtils) {
//            if (myOutsideRecordUtil.getState().equals("待审核") || myOutsideRecordUtil.getState().equals("审核中")) {
//                myOutsideRecordUtil.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
//            }
//        }
//        return myOutsideRecordUtils;
//    }
//
//    /**
//     * 根据条件查询我的外访审批信息-通过/未通过案件
//     *
//     * @param outsideCollectionUtils
//     * @return
//     */
//    @Override
//    public List<MyOutsideRecordUtils> selectOutsideRecordProce(OutsideCollectionUtils outsideCollectionUtils) {
//        String state = outsideCollectionUtils.getState();
//        if (!ObjectUtils.isEmpty(state)) {
//            List<String> list = SplitUtils.strSplitComma(state);
//            outsideCollectionUtils.setStateList(list);
//        }
//        OutsideCollectionUtils outsideCollectionUtils1 = verificationOutsideRecord(outsideCollectionUtils);
//        Map<String, Object> map = BeanUtil.beanToMap(outsideCollectionUtils1);
//        return myApprovalMapper.selectOutsideRecordProce(map);
//    }

    /**
     * 根据减免申请表id查询减免申请凭证信息
     *
     * @param id
     * @return
     */
    @Override
    public List<ReductionFile> selectReductionFile(int id) {
        return myApprovalMapper.selectReductionFile(id);
    }

    /**
     * 根据申请表id查询审核进程表数据
     *
     * @param approveProce
     * @return
     */
    @Override
    public List<ApproveProce> selectApproveProce(ApproveProce approveProce) {
        List<ApproveProce> approveProces = myApprovalMapper.selectApproveProce(approveProce);
        if (!ObjectUtils.isEmpty(approveProces)) {
            for (ApproveProce approveProce1 : approveProces) {
                Integer reviewerId = approveProce1.getReviewerId();  //审核人id
                if (!ObjectUtils.isEmpty(approveProce1.getOperationType())) {
                    if (approveProce1.getOperationType() == 0) {  //操作人类型（0-主账号;1-员工账号;2-资产端账号）
//                       根据团队id获取团队信息
                        Create idCreate = teamService.findIdCreate(reviewerId);
                        approveProce1.setReviewer(idCreate.getCname());
                    } else if (approveProce1.getOperationType() == 1) {
                        Employees employees = settingsService.selectEmployeesId(reviewerId);
                        approveProce1.setReviewer(employees.getEmployeeName());
                    } else if (approveProce1.getOperationType() == 2) {
                        if (reviewerId != null && reviewerId > 0) {
                            SysUser sysUser = settingsService.selectSysUserId(reviewerId);
                            approveProce1.setReviewer(sysUser.getNickName());
                        }
                    }
                }
            }
        }
        return approveProces;
    }


    /**
     * 退案/留案/停催审批（写入审批记录历史表）
     *
     * @param approveProce
     * @return
     */
    @Override
    public int insertApproveProce(List<ApproveProce> approveProce) {
        return myApprovalMapper.insertApproveProce(approveProce);
    }

    /**
     * 退案/留案/停催审批（修改申请表中审核状态等）
     *
     * @param applyRecord
     * @return
     */
    @Override
    public int updateApplyRecord(List<ApplyRecord> applyRecord) {
        return myApprovalMapper.updateApplyRecord(applyRecord);
    }

    /**
     * 资料调取审批（修改申请表中审核状态等）
     *
     * @param retrievalRecords
     * @return
     */
    @Override
    public int updateRetrievalRecord(List<RetrievalRecord> retrievalRecords) {
        return myApprovalMapper.updateRetrievalRecord(retrievalRecords);
    }

    /**
     * 回款审批（修改申请表中审核状态等）
     *
     * @param repaymentRecords
     * @return
     */
    @Override
    public int updateRepaymentRecord(List<RepaymentRecord> repaymentRecords) {
        for (RepaymentRecord record : repaymentRecords) {
            myApprovalMapper.updateRepaymentRecord(record);
        }
        return repaymentRecords.size();
        //return myApprovalMapper.updateRepaymentRecord(repaymentRecords);
    }

    /**
     * 减免审批（修改申请表中审核状态等）
     *
     * @param reductionRecords
     * @return
     */
    @Override
    public int updateReductionRecord(List<ReductionRecord> reductionRecords) {
        return myApprovalMapper.updateReductionRecord(reductionRecords);
    }

    /**
     * 分期还款审批（修改申请表中审核状态等）
     *
     * @param stagingRecords
     * @return
     */
    @Override
    public int updateStagingRecord(List<StagingRecord> stagingRecords) {
        return myApprovalMapper.updateStagingRecord(stagingRecords);
    }

    /**
     * 外访审批（修改申请表中审核状态等）
     *
     * @param outsideRecords
     * @return
     */
    @Override
    public int updateOutsideRecord(List<OutsideRecord> outsideRecords) {
        return myApprovalMapper.updateOutsideRecord(outsideRecords);
    }

    @Override
    public int updateSignRecord(CaseSignRecord signRecord) {
        myApprovalMapper.updateSignRecord(signRecord);
        return 0;
    }

    @Override
    public CaseSignRecord selectSignRecordInfo(Long id){
        return myApprovalMapper.selectSignRecordInfo(id);
    }

    /**
     * 根据申请id集合查询回款申请详情
     *
     * @param ids
     * @return
     */
    @Override
    public List<RepaymentRecord> selectRepaymentRecordById(List<Long> ids) {
        Map<String, Object> map = new HashMap<>();
        map.put("ids", ids);
        return myApprovalMapper.selectRepaymentRecordById(map);
    }

    /**
     * 根据申请id集合查询减免申请详情
     *
     * @param ids
     * @return
     */
    @Override
    public List<ReductionRecord> selectReductionRecordById(List<Long> ids) {
        Map<String, Object> map = new HashMap<>();
        map.put("ids", ids);
        return myApprovalMapper.selectReductionRecordById(map);
    }

    /**
     * 根据申请id集合查询分期还款申请详情
     *
     * @param ids
     * @return
     */
    @Override
    public List<StagingRecord> selectStagingRecordById(List<Long> ids) {
        Map<String, Object> map = new HashMap<>();
        map.put("ids", ids);
        return myApprovalMapper.selectStagingRecordById(map);
    }

    /**
     * 根据申请id集合查询外访申请详情
     *
     * @param ids
     * @return
     */
    @Override
    public List<OutsideRecord> selectOutsideRecordById(List<Long> ids) {
        Map<String, Object> map = new HashMap<>();
        map.put("ids", ids);
        return myApprovalMapper.selectOutsideRecordById(map);
    }

    /**
     * 根据申请id集合查询留案/退案申请详情
     *
     * @param ids
     * @param applyState
     * @return
     */
    @Override
    public List<ApplyRecord> selectApplyRecordById(List<Long> ids, int applyState) {
        Map<String, Object> map = new HashMap<>();
        map.put("ids", ids);
        map.put("applyState", applyState);
        return myApprovalMapper.selectApplyRecordById(map);
    }

    /**
     * 选择按id应用记录排序
     *
     * @param ids         ids
     * @param sort        分类
     * @param approveCode
     * @return {@link List }<{@link ApplyRecord }>
     */
    @Override
    public List<ApproveProce> selectApplyRecordByIdsAndSort(List<Long> ids, Integer sort, Integer approveCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("ids", ids);
        map.put("sort", sort);
        map.put("operationType", "0");
        map.put("approveCode", approveCode);
        List<ApproveProce> approveProces = myApprovalMapper.selectApplyRecordByIdsAndSort(map);
        return approveProces;
    }
}
