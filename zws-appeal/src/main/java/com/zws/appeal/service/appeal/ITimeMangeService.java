package com.zws.appeal.service.appeal;


import com.zws.common.core.domain.TimeManage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ITimeMangeService {
    /**
     * 新增案件跟进记录
     *
     * @param timeManage
     * @return
     */
    int insertInfoContact(TimeManage timeManage);

    /**
     * 批量新增案件跟进记录
     * @param list
     * @return
     */
    int batchInsert(List<TimeManage> list);

    void updateTimeBatch();

    void addCaseToTime();
}
