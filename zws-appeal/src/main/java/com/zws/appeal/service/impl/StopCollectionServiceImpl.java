package com.zws.appeal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zws.appeal.agservice.AgCaseService;
import com.zws.appeal.domain.*;
import com.zws.appeal.domain.appeal.CaseApprovalQueryDTO;
import com.zws.appeal.mapper.CaseMapper;
import com.zws.appeal.mapper.MyApprovalMapper;
import com.zws.appeal.mapper.RecordMapper;
import com.zws.appeal.mapper.TeamCaseMapper;
import com.zws.appeal.pojo.InheritanceCollection;
import com.zws.appeal.pojo.QueryCriteria;
import com.zws.appeal.pojo.RetentionWithdrawalCase;
import com.zws.appeal.pojo.StateDesensitization;
import com.zws.appeal.pojo.myApproval.myApplyRecordUtils;
import com.zws.appeal.pojo.staticConst.StaticConstantClass;
import com.zws.appeal.pojo.teamApplication.CreateApplyRecordUtils;
import com.zws.appeal.pojo.teamParameters.TeamApplyRecord;
import com.zws.appeal.service.RecordService;
import com.zws.appeal.service.TeamCaseService;
import com.zws.appeal.utils.DecryptUtils;
import com.zws.appeal.utils.DesensitizationRedis;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.DataMaskingUtils;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.domain.ZwsDataPageVo;
import com.zws.common.domain.ZwsLoginUser;
import com.zws.common.exception.ZwsServiceException;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.common.utils.ZwsObjectTurnUtil;
import com.zws.dispose.pojo.ApplicationUtils;
import com.zws.standardmain.domain.dto.ZwsJgAsyncNotificationDto;
import com.zws.standardmain.domain.dto.ZwsJgVerificationDto;
import com.zws.standardmain.domain.dto.operate.ZwsJgAddApproveOperateDto;
import com.zws.standardmain.domain.dto.operate.ZwsJgApprovePageOperateDto;
import com.zws.standardmain.domain.dto.operate.ZwsSqlDataDto;
import com.zws.standardmain.service.ZwsJgApproveMainCustomService;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 新审批-停催审批
 */
@Service
@Slf4j
public class StopCollectionServiceImpl implements ZwsJgApproveMainCustomService {

    @Autowired
    private CaseMapper caseMapper;
    @Resource
    private MyApprovalMapper myApprovalMapper;
    @Resource
    private TeamCaseMapper teamCaseMapper;
    @Resource
    private RecordMapper recordMapper;
    @Autowired
    private RecordService recordService;
    @Autowired
    private DesensitizationRedis desensitizationRedis;
    @Autowired
    private TeamCaseService teamCaseService;
    @Autowired
    private AgCaseService agCaseService;


    /**
     * 添加停催审批
     * @param jgAddApproveOperateDto
     * @param loginUser
     */
    @Override
    public void jgAddApprove(ZwsJgAddApproveOperateDto jgAddApproveOperateDto, ZwsLoginUser loginUser) {
        RetentionWithdrawalCase retentionWithdrawalCase = ZwsObjectTurnUtil.parentTurnSon(jgAddApproveOperateDto.getApproveData(), RetentionWithdrawalCase.class);
        List<Long> approveIdList = jgAddApproveOperateDto.getApproveIdList();
        List<CaseManage> caseManages = retentionWithdrawalCase.getCaseManages();
        List<Long> caseIdList= retentionWithdrawalCase.getCaseId();
        if (approveIdList.size() != caseManages.size()) {
            throw new ZwsServiceException("申请停催审批记录数量不一致。");
        }
        //List<CaseManage> caseManages = caseMapper.selectCaseManageId(retentionWithdrawalCase.getCaseId(), Math.toIntExact(loginUser.getTeamId()));
        //过滤停催的
        caseManages = caseManages.stream().filter(p -> !"2".equals(p.getCaseState())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(caseManages)) {
            throw new GlobalException("该批案件为停催状态或已提交停催申请！");
        }
        List<ApplyRecord> list = new ArrayList<>();
        int num=0;
        for (int i=0 ; i<caseManages.size();i++) {
            CaseManage caseManage = caseManages.get(i);
            Long caseId = caseManage.getCaseId();
            if (!caseIdList.contains(caseId)) {
                continue;
            }
//            Map<String, Object> rmap = new HashMap<>();
//            rmap.put("id", caseManage.getCaseId());
//            rmap.put("createId", SecurityUtils.getTeamId());
//            ApplyRecord applyRecords = caseMapper.selectApplyRecordByUserId(rmap);
//            if (!ObjectUtils.isEmpty(applyRecords)) {
//                if (applyRecords.getApplyState().equals(0)) {
//                    if (applyRecords.getExamineState().equals("待审核") || applyRecords.getExamineState().equals("审核中")) {
//                        log.info(caseManage.getCaseId() + "，案件有停催申请,跳过");
//                        continue;
////                            throw new GlobalException("案件有停催申请");
//                    }
//                }
//            }


            Long approveId = approveIdList.get(num);
            num++;

            ApplyRecord applyRecord = new ApplyRecord();
            applyRecord.setCaseId(caseManage.getCaseId());  //案件id
            applyRecord.setApplyState(0);  //申请的案件状态，0-停催，1-留案，2-退案
            applyRecord.setApplyDate(new Date());  //申请时间
            applyRecord.setApplicant(TokenInformation.getUsername());  //申请人
            applyRecord.setApplicantId(new Long((long) TokenInformation.getUserid()));  //申请人id
            applyRecord.setTeamId(caseManage.getOutsourcingTeamId());  //委托团队id
            applyRecord.setTeamName(caseManage.getOutsourcingTeamName());  //委托团队名称
            applyRecord.setExamineState("待审核");  //审核状态
            applyRecord.setProce(0);  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
            applyRecord.setProceSort(0);   //审核顺序
            applyRecord.setDelFlag(0); //删除标志
            applyRecord.setEntrustingCaseBatchNum(caseManage.getEntrustingCaseBatchNum());  //委案批次号
            applyRecord.setEntrustingCaseDate(caseManage.getEntrustingCaseDate());  //委案日期
            applyRecord.setReturnCaseDate(caseManage.getReturnCaseDate());  //退案日期
            applyRecord.setOdvId(caseManage.getOdvId());
            applyRecord.setOdvName(caseManage.getOdvName());
            applyRecord.setOperationType(TokenInformation.getType());
            applyRecord.setStopEndTime( caseManage.getStopEndTime());
            applyRecord.setStopEndApprovalTime(caseManage.getStopEndTime());
            applyRecord.setPermanentlyStop( caseManage.getPermanentlyStop());
            applyRecord.setApproveId(approveId);
            applyRecord.setReason(caseManage.getReason());
            list.add(applyRecord);
        }
        log.info("新审批-停催申请{}条",list.size());
        caseMapper.insertApplyRecord(list);
    }



    /**
     * 通过
     * @param jgVerification
     * @param loginUser
     */
    @Override
    public void jgApproveFinalPass(ZwsJgVerificationDto jgVerification, ZwsLoginUser loginUser) {
        List<Long> approveIds = jgVerification.getApproveIds();
        for (Long approveId : approveIds){
            ApplyRecord applyRecord = new ApplyRecord();
            applyRecord.setApproveId(approveId);
            applyRecord.setExamineState("审核中");
            caseMapper.updateByApproveIdSelective(applyRecord);
        }
    }

    /**
     * 不通过
     * @param jgVerification
     * @param loginUser
     */
    @Override
    public void jgApproveNotPass(ZwsJgVerificationDto jgVerification, ZwsLoginUser loginUser) {
        List<Long> approveIds = jgVerification.getApproveIds();
        for (Long approveId : approveIds){
            ApplyRecord applyRecord = new ApplyRecord();
            applyRecord.setApproveId(approveId);
            applyRecord.setExamineState("不通过");
            caseMapper.updateByApproveIdSelective(applyRecord);
        }
    }

    /**
     * 撤销
     * @param jgVerification
     * @param loginUser
     */
    @Override
    public void jgApproveRevoke(ZwsJgVerificationDto jgVerification, ZwsLoginUser loginUser) {
        List<Long> approveIds = jgVerification.getApproveIds();
        for (Long approveId : approveIds){
            ApplyRecord applyRecord = new ApplyRecord();
            applyRecord.setApproveId(approveId);
            applyRecord.setExamineState("已撤销");
            caseMapper.updateByApproveIdSelective(applyRecord);
        }
    }

    /**
     * 列表
     * @param pageOperateDto
     * @return
     */
    @Override
    public ZwsDataPageVo jgQueryApproveList(ZwsJgApprovePageOperateDto pageOperateDto) {
        CaseApprovalQueryDTO queryDTO = ZwsObjectTurnUtil.parentTurnSon(pageOperateDto.getApproveData(), CaseApprovalQueryDTO.class);
        PageHelper.startPage(pageOperateDto.getPageNum(), pageOperateDto.getPageSize());
        ZwsSqlDataDto sqlDataDto = pageOperateDto.getSqlDataDto();

        List<myApplyRecordUtils> list = myApprovalMapper.jgQueryApproveList(sqlDataDto,queryDTO);
        if (!ObjectUtils.isEmpty(list)) {
//            进行数据脱敏
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            Desensitization desensitization = null;
            if (state.getInformationStatus() == 1) {
                desensitization = stateDesensitization.getDesensitization();
            }

            for (myApplyRecordUtils myApplyRecordUtil : list) {
                //
//                if (ObjectUtils.isEmpty(myApplyRecordUtil.getApproveStart())) {
////                    if (myApplyRecordUtil.getExamineState().equals("0") ||
////                            myApplyRecordUtil.getExamineState().equals("1")) {
////
////                        myApplyRecordUtil.setApproveStart(0);  //处理状态,0-待处理，1-审核中，2-已同意
////                    }
//                    if (myApplyRecordUtil.getExamineState().equals("0")) {
//                        myApplyRecordUtil.setApproveStart(0);
//                    }
//                }
//                if (myApplyRecordUtil.getExamineState().equals("6")) {
//                    myApplyRecordUtil.setApproveStart(6);
//                }

                //判断案件是否属于当前团队
                boolean isBelongToTeam = agCaseService.checkCaseBelongToTeam(myApplyRecordUtil.getCaseId());
                myApplyRecordUtil.setButton(isBelongToTeam ? 1 : 0);

                //解密
                DecryptUtils.dataDecrypt(myApplyRecordUtil);
                //脱敏
                if (desensitization != null) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(myApplyRecordUtil.getClientName())) {
                        myApplyRecordUtil.setClientName(DataMaskingUtils.nameMasking(myApplyRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(myApplyRecordUtil.getClientIdcard())) {
                        myApplyRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(myApplyRecordUtil.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(myApplyRecordUtil.getClientCensusRegister())) {
                        myApplyRecordUtil.setClientCensusRegister(DataMaskingUtils.Masking(myApplyRecordUtil.getClientCensusRegister()));
                    }
                }
            }
        }
        PageInfo<myApplyRecordUtils> pageInfo = new PageInfo<>(list);
        ZwsDataPageVo pageDataTable = PageUtils.getPageDataTable(list, pageInfo);
        return pageDataTable;
    }

    /**
     * 异步通知下级审批
     * @param jgAsyncNotificationDto
     * @param loginUser
     */
    @Override
    public void jgAsyncNotification(ZwsJgAsyncNotificationDto jgAsyncNotificationDto, ZwsLoginUser loginUser) {
        ZwsJgApproveMainCustomService.super.jgAsyncNotification(jgAsyncNotificationDto, loginUser);
    }

    /**
     * 团队申请列表
     * @param pageOperateDto
     * @return
     */
    @Override
    public ZwsDataPageVo queryJgTeamApplyList(ZwsJgApprovePageOperateDto pageOperateDto) {
        List<Long> approveIds = pageOperateDto.getApproveIds();
        ZwsLoginUser zwsLoginUser = pageOperateDto.getLoginUser();
        LoginUser loginUser = BeanUtil.copyProperties(zwsLoginUser, LoginUser.class);
        TeamApplyRecord teamApplyRecord = ZwsObjectTurnUtil.parentTurnSon(pageOperateDto.getApproveData(), TeamApplyRecord.class);
        teamApplyRecord.setClientName(FieldEncryptUtil.encrypt(teamApplyRecord.getClientName()));
        teamApplyRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamApplyRecord.getClientIdcard()));
        ZwsSqlDataDto sqlDataDto = pageOperateDto.getSqlDataDto();
        if (!ObjectUtils.isEmpty(teamApplyRecord.getStringDeptId())) {
            int dept = teamApplyRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamApplyRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamApplyRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamApplyRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamApplyRecord.setUserId(Integer.parseInt(teamApplyRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamApplyRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamApplyRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamApplyRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamApplyRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamApplyRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageHelper.startPage(pageOperateDto.getPageNum(), pageOperateDto.getPageSize());
        teamApplyRecord.setDeptIds(list);
        teamApplyRecord.setCreateId(TokenInformation.getCreateid());
        TeamApplyRecord teamApplyRecord1 = applyRecordId(teamApplyRecord);
//        Map<String, Object> map = BeanUtil.beanToMap(teamApplyRecord1);

        List<CreateApplyRecordUtils> createApplyRecordUtils = teamCaseMapper.selectApplyRecordIdNew(teamApplyRecord1,sqlDataDto);//根据员工id查询同部门以及以下部门的员工停案/留案/停催申请表以及案加详情信息
        if (!ObjectUtils.isEmpty(createApplyRecordUtils)) {
            for (CreateApplyRecordUtils createApplyRecordUtil : createApplyRecordUtils) {
                DecryptUtils.dataDecrypt(createApplyRecordUtil);  //解密

                CaseManage caseManage = recordService.selectCaseManageCaseId(createApplyRecordUtil.getCaseId());
                if (caseManage == null) {
                    createApplyRecordUtil.setButton(0);
                    continue;
                }
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (new Long((long) TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                        createApplyRecordUtil.setButton(1);
                    } else {
                        createApplyRecordUtil.setButton(0);
                    }
                } else {
                    createApplyRecordUtil.setButton(0);
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (CreateApplyRecordUtils createApplyRecordUtil : createApplyRecordUtils) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(createApplyRecordUtil.getClientName())) {
                        createApplyRecordUtil.setClientName(DataMaskingUtils.nameMasking(createApplyRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(createApplyRecordUtil.getClientIdcard())) {
                        createApplyRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(createApplyRecordUtil.getClientIdcard()));
                    }
                }
            }
        }
        PageInfo<CreateApplyRecordUtils> pageInfo = new PageInfo<>(createApplyRecordUtils);
        return PageUtils.getPageDataTable(createApplyRecordUtils, pageInfo);
    }


    /**
     * 我的申请列表
     * @param pageOperateDto
     * @return
     */
    @Override
    public ZwsDataPageVo queryJgMyApplyList(ZwsJgApprovePageOperateDto pageOperateDto) {
        List<Long> approveIds = pageOperateDto.getApproveIds();
        ZwsLoginUser loginUser = pageOperateDto.getLoginUser();
        LoginUser user = BeanUtil.copyProperties(loginUser, LoginUser.class);
        ApplicationUtils applicationUtils = ZwsObjectTurnUtil.parentTurnSon(pageOperateDto.getApproveData(), ApplicationUtils.class);
        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
        PageHelper.startPage(applicationUtils.getPageNum(), applicationUtils.getPageSize());
        applicationUtils.setApplicantId(new Long((long) TokenInformation.getUserid()));   //申请人id
        applicationUtils.setOperationType(TokenInformation.getType());
        applicationUtils.setApproveIds(approveIds);
        applicationUtils.setOperationType(TokenInformation.getType());
        ApplicationUtils verification = verification(applicationUtils);
        ZwsSqlDataDto sqlDataDto = pageOperateDto.getSqlDataDto();

        List<InheritanceCollection> inheritanceCollections = recordMapper.selectApplyRecordIdNew(verification,sqlDataDto);

        List<InheritanceCollection> inheritanceCollections1 = whetherItBelongs(inheritanceCollections);
        List<InheritanceCollection> inheritanceCollection = desensitizationTreatment(inheritanceCollections1);  //处理脱敏

        PageInfo<InheritanceCollection> pageInfo = new PageInfo<>(inheritanceCollection);
        return PageUtils.getPageDataTable(inheritanceCollection, pageInfo);
    }

    /**
     * 我的申请-处理脱敏
     *
     * @return
     */
    public List<InheritanceCollection> desensitizationTreatment(List<InheritanceCollection> inheritanceCollections) {
        if (!ObjectUtils.isEmpty(inheritanceCollections)) {
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (InheritanceCollection inheritanceCollection : inheritanceCollections) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientName())) {
                        inheritanceCollection.setClientName(DataMaskingUtils.nameMasking(inheritanceCollection.getClientName()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientPhone())) {
                        inheritanceCollection.setClientPhone(DataMaskingUtils.phoneMasking(inheritanceCollection.getClientPhone()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientIdcard())) {
                        inheritanceCollection.setClientIdcard(DataMaskingUtils.idMasking(inheritanceCollection.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientCensusRegister())) {
                        inheritanceCollection.setClientCensusRegister(DataMaskingUtils.Masking(inheritanceCollection.getClientCensusRegister()));
                    }
                }
            }
        }
        return inheritanceCollections;
    }


    /**
     * 我的申请-判断案件是否属于登录人
     *
     * @param inheritanceCollections
     * @return
     */
    public List<InheritanceCollection> whetherItBelongs(List<InheritanceCollection> inheritanceCollections) {
        for (InheritanceCollection inheritanceCollection : inheritanceCollections) {
            //当前团队Id等于案件所属团队，就有权限作废
            if (ObjectUtil.equals(inheritanceCollection.getTeamId(),TokenInformation.getCreateid()) ){
                inheritanceCollection.setAuthority(0);
            }else {
                inheritanceCollection.setAuthority(1);
            }

            DecryptUtils.dataDecrypt(inheritanceCollection);  //解密
            CaseManage caseManage = recordService.selectCaseManageCaseId(inheritanceCollection.getCaseId());
            if (ObjectUtils.isEmpty(caseManage)) {
                inheritanceCollection.setButton(0);
                continue;
            }
            if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                if (TokenInformation.getType() == 0 && new Long(TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                    inheritanceCollection.setButton(0);
                } else {
                    if (ObjectUtils.isEmpty(caseManage.getOdvId())) {
                        inheritanceCollection.setButton(0);
                    } else {
                        if (new Long(TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId()) && new Long(TokenInformation.getUserid()).equals(caseManage.getOdvId())) {
                            inheritanceCollection.setButton(1);
                        } else {
                            inheritanceCollection.setButton(0);
                        }
                    }
                }
            } else {
                inheritanceCollection.setButton(0);
            }
        }
        return inheritanceCollections;
    }

    /**
     * 处理时间-（我的申请）
     *
     * @param applicationUtils
     * @return
     */
    public ApplicationUtils verification(ApplicationUtils applicationUtils) {
        //        修改结束时间为一天的结束
        if (!ObjectUtils.isEmpty(applicationUtils.getApplyDate2())) {
            DateTime dateTime = DateUtil.endOfDay(applicationUtils.getApplyDate2());  //一天的结束，结果：2017-03-01 23:59:59
            applicationUtils.setApplyDate2(dateTime);
        }
        if (!ObjectUtils.isEmpty(applicationUtils.getEntrustingCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(applicationUtils.getEntrustingCaseDate2());
            applicationUtils.setEntrustingCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(applicationUtils.getReturnCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(applicationUtils.getReturnCaseDate2());
            applicationUtils.setReturnCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(applicationUtils.getUpdateTime2())) {
            DateTime dateTime1 = DateUtil.endOfDay(applicationUtils.getUpdateTime2());
            applicationUtils.setUpdateTime2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(applicationUtils.getRepaymentDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(applicationUtils.getRepaymentDate2());
            applicationUtils.setRepaymentDate2(dateTime1);
        }
        return applicationUtils;
    }

    /**
     * 根据部门id查询部门及以下部门的所有部门id
     *
     * @return
     */
    public List<Integer> Encapsulations(Integer deptId, LoginUser loginUser) {
        if (deptId == null) {
            return Encapsulation(loginUser);
        }
        Map<String, Object> accountInfo = loginUser.getAccountInfo();
        Integer teamId = (Integer) accountInfo.get(UserConstants.TEAM_ID);
        List<Dept> deptList = teamCaseService.selectDept(teamId);//根据团队id查询该团队所有部门信息
        List<Integer> list = new ArrayList<>();
        list.add(deptId);  //所传的部门id
        for (Dept dept : deptList) {
            String ancestors = dept.getAncestors();  //祖级列表
            List<String> splits = com.zws.common.core.utils.SplitUtils.strSplitComma(ancestors);
            for (String split : splits) {
                if (StrUtil.equals(split, StrUtil.toString(deptId))) {
                    list.add(dept.getId());
                }
            }
        }
        return list;
    }

    /**
     * 根据登录人id查询登录人部门及以下部门的所有部门id
     *
     * @param loginUser 当前登录人信息
     * @return
     */
    public List<Integer> Encapsulation(LoginUser loginUser) {
        if (loginUser == null) {
            throw new ServiceException("当前登录信息获取失败");
        }
        Map<String, Object> accountInfo = loginUser.getAccountInfo();
        Integer userId = (Integer) accountInfo.get(UserConstants.USER_ID);
        Employees employees = teamCaseService.selectEmployeesId(userId);  //根据员工id查询员工部门id
        if (ObjectUtils.isEmpty(employees)) {
            return new ArrayList<Integer>();
        }
        Integer departmentId = employees.getDepartmentId();    //登录用户的部门id


        String departmentIds = String.valueOf(departmentId);  //强转为string类型
        List<Dept> deptList = teamCaseService.selectDept(TokenInformation.getCreateid(loginUser));//根据团队id查询该团队所有部门信息
        List<Integer> list = new ArrayList<>();
        list.add(departmentId);  //登录人本身的部门id
        for (Dept dept : deptList) {
            String ancestors = dept.getAncestors();  //祖级列表
            String[] split = ancestors.split(",");
            for (String splits : split) {
                if (splits.equals(departmentIds)) {
                    list.add(dept.getId());
                }
            }
        }
        return list;
    }

    /**
     * 停案/留案/停催申请-处理时间
     *
     * @return
     */
    public TeamApplyRecord applyRecordId(TeamApplyRecord teamApplyRecord) {
        if (!ObjectUtils.isEmpty(teamApplyRecord.getExamineTime2())) {
            DateTime dateTime = DateUtil.endOfDay(teamApplyRecord.getExamineTime2());  //一天的结束，结果：2017-03-01 23:59:59
            teamApplyRecord.setExamineTime2(dateTime);
        }
        if (!ObjectUtils.isEmpty(teamApplyRecord.getEntrustingCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(teamApplyRecord.getEntrustingCaseDate2());
            teamApplyRecord.setEntrustingCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(teamApplyRecord.getReturnCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(teamApplyRecord.getReturnCaseDate2());
            teamApplyRecord.setReturnCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(teamApplyRecord.getApplyDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(teamApplyRecord.getApplyDate2());
            teamApplyRecord.setApplyDate2(dateTime1);
        }
        return teamApplyRecord;
    }


}
