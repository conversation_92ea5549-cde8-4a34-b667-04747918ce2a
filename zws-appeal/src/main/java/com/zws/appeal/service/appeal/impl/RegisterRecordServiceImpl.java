package com.zws.appeal.service.appeal.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zws.appeal.controller.letterDoc.law.domain.LawAgency;
import com.zws.appeal.controller.letterDoc.law.service.ILawAgencyService;
import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.domain.appeal.RegisterRecord;
import com.zws.appeal.domain.appeal.StageConfig;
import com.zws.appeal.mapper.appeal.CaseManageMapper;
import com.zws.appeal.mapper.appeal.RegisterRecordMapper;
import com.zws.appeal.mapper.appeal.StageConfigMapper;
import com.zws.appeal.pojo.appeal.StageFieldPojo;
import com.zws.appeal.service.CollectionService;
import com.zws.appeal.service.appeal.IFilingCaseService;
import com.zws.appeal.service.appeal.RegisterRecordService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.domain.Option;
import com.zws.common.core.enums.approval.WebSideEnum;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.HistoryPowerUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.domain.SysDictData;
import com.zws.system.api.domain.SysDictType;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zws.common.core.utils.PageUtils.startPage;

@Service
public class RegisterRecordServiceImpl implements RegisterRecordService {

    @Autowired
    private RegisterRecordMapper registerRecordMapper;
    @Autowired
    private StageConfigMapper stageConfigMapper;
    @Autowired
    private CaseManageMapper caseManageMapper;
    @Autowired
    private ILawAgencyService lawAgencyService;
    @Autowired
    private CollectionService collectionService;
    @Autowired
    private IFilingCaseService filingCaseService;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return registerRecordMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(RegisterRecord record) {
        record.setDelFlag(BaseConstant.DelFlag_Being);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String username = loginUser.getUsername();
        String teamId = loginUser.getAccountInfo().get(UserConstants.TEAM_ID).toString();
        record.setCreateId(Integer.valueOf(teamId));
        record.setCreateBy(username);
        record.setUpdateBy(username);
        record.setWebSide(WebSideEnum.APPEAL.getCode());
        return registerRecordMapper.insert(record);
    }

    @Override
    public int insertSelective(RegisterRecord record) {
        return registerRecordMapper.insertSelective(record);
    }

    @Override
    public RegisterRecord selectByPrimaryKey(Long id) {
        return registerRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(RegisterRecord record) {
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateTime(new Date());
        return registerRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(RegisterRecord record) {
        return registerRecordMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<RegisterRecord> list) {
        return registerRecordMapper.updateBatch(list);
    }

    @Override
    public int batchInsert(List<RegisterRecord> list) {
        return registerRecordMapper.batchInsert(list);
    }

    @Override
    public List<RegisterRecord> getRecordByType(Long caseId, Integer type,Integer webSide) {

        CaseManage caseManage = collectionService.selectCaseManageId(caseId);
        Map<String, Object> map = HistoryPowerUtils.handleTsHistoryPower(caseManage.getEntrustingCaseDate(), caseManage.getHistoryPower(), TokenInformation.getCreateid().longValue(), webSide);
        map.put("caseId",caseId);
        map.put("registerType",type);
        map.put("webSide",webSide);
        startPage();
        return registerRecordMapper.getRecordByType(map);
    }

    @Override
    public RegisterRecord getStageByType(RegisterRecord stage) {
        return registerRecordMapper.getStageByType(stage);
    }

    @Override
    public void addRegisterRecord(RegisterRecord record) {
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        if (record.getCaseId()==null){throw new ServiceException("案件id不能为空"); }
        if (record.getRegisterType()==2 && record.getObjContent()!=null){
            record.setMediateContent(JSONUtil.toJsonStr(record.getObjContent()));
        }else if(record.getRegisterType()==3 && record.getObjContent()!=null){
            record.setSaveContent(JSONUtil.toJsonStr(record.getObjContent()));
        }else if(record.getRegisterType()==4 && record.getObjContent()!=null){
            record.setPursueContent(JSONUtil.toJsonStr(record.getObjContent()));
        }

        List<Option> list = filingCaseService.selectWithLawsuitStage();
        if (list==null || list.size()==0){throw new ServiceException("案件流转阶段配置不能为空");}
        List<String> stageCollect = list.stream().map(Option::getInfo).collect(Collectors.toList());

        if (record.getRegisterType()==1){
            record.setConnectContent(JSONUtil.toJsonStr(record.getObjContent()));
            //更新案件调解状态
            String mediateStage = record.getUrgeState();
            if (StringUtils.isNotEmpty(mediateStage) && stageCollect.contains(mediateStage)){
                record.setStageValue(mediateStage);
            }
            registerRecordMapper.updateCaseUrge(record);
            insert(record);
        }
        if (record.getRegisterType()==3){
            registerRecordMapper.updateCaseTime(record);
            this.registerRecordMapper.updateCaseSaveStage(record);
            insert(record);

        }
        //调诉阶段一样
        if (record.getRegisterType()==2){
            //更新调诉阶段 字段状态
            String disposeStage = record.getMediateFieldName();
            if (StringUtils.isNotEmpty(disposeStage) && stageCollect.contains(disposeStage)){
                record.setStageValue(disposeStage);
            }
            this.registerRecordMapper.updateCaseDisposeStage(record);
            insert(record);

        }
        //调执登记
        if(record.getRegisterType()==4){
            record.setConnectContent(null);
            record.setMediateFieldName(record.getPursue());
            this.registerRecordMapper.updateCaseDisposeStage(record);
            insert(record);
        }
    }


    /**
     * 更新caseManage 最新处置阶段（最大阶段作为案件当前阶段）
     * @param record
     */
    public void updateDisposeStage(RegisterRecord record){

        List<String> stageByCaseId = registerRecordMapper.getStageByCaseId(record.getCaseId());
        if (stageByCaseId==null || stageByCaseId.size()<2){
            //更新至caseManage 对应案件的阶段中
            caseManageMapper.updateDisposeStage(record);
        }else {
            List<StageConfig> configList = registerRecordMapper.getStageList();
            Map<String,StageConfig> map = configList.stream().collect(Collectors.toMap(StageConfig::getStageName, Function.identity()));
            ArrayList<StageConfig> stageConfigs = new ArrayList<>();
            for (String stageName : stageByCaseId) {
                if (StringUtils.isEmpty(stageName)){continue;}
                stageConfigs.add(map.get(stageName));
            }
            //降序
            stageConfigs.sort(Comparator.comparing(StageConfig::getSortNum).reversed());
            //最大的阶段名
            String stageName = stageConfigs.get(0).getStageName();
            RegisterRecord record1 = new RegisterRecord();
            record1.setCaseId(record.getCaseId());
            record1.setMediateStage(stageName);
            record1.setUpdateTime(new Date());
            caseManageMapper.updateDisposeStage(record1);
        }
    }
    /**
     * 获取各个调解阶段的案件数量
     * @return
     */
    @Override
    public List<Option> getStageCount(Long teamId) {

        //  获取调解机构下的  案件ID集合
        List<String> caseIdList= registerRecordMapper.getCaseIds(teamId,null);
        //  获取调解阶段的集合
        List<Option> list = new ArrayList<>();
        Option option = new Option();
        option.setCode("全部诉讼状态");
        option.setInfo(String.valueOf(caseIdList.size()));
        list.add(option);
        List<StageConfig> configList = registerRecordMapper.getStageList();

        for (int i = 0; i < configList.size(); i++) {
            StageConfig stageConfig = configList.get(i);
            Integer num = registerRecordMapper.getStageCount(stageConfig.getStageName(),caseIdList,null);
            Option option2 = new Option();
            option2.setCode(stageConfig.getStageName());
            option2.setInfo(String.valueOf(num));
            list.add(option2);
        }
        return list;
    }

    @Override
    public List<Option> getStageCountByTeam() {
        //  获取调解阶段的集合
        List<Option> list = new ArrayList<>();
        Option option = new Option();
        option.setCode("全部诉讼状态");
        option.setInfo("0");
        list.add(option);
        List<StageConfig> configList = registerRecordMapper.getStageList();
        for (StageConfig config : configList) {
            Option option2 = new Option();
            option2.setCode(config.getStageName());
            option2.setInfo("0");
            list.add(option2);
        }
        return list;
    }

    /**
     * 主账号的"我的诉讼案件" 下拉框
     * @param teamId
     * @param employeeId
     * @return
     */

    @Override
    public List<Option> getStageCountByEmployee(Long teamId, Long employeeId) {

        //  获取调解机构下的  案件ID集合
        List<String> caseIdList= registerRecordMapper.getCaseIds(teamId,employeeId);
        //  获取调解阶段的集合
        List<Option> list = new ArrayList<>();
        Option option = new Option();
        option.setCode("全部诉讼状态");
        option.setInfo(String.valueOf(caseIdList.size()));
        list.add(option);
        List<StageConfig> configList = registerRecordMapper.getStageList();

        for (int i = 0; i < configList.size(); i++) {
            StageConfig stageConfig = configList.get(i);
            Integer num = registerRecordMapper.getStageCount(stageConfig.getStageName(),caseIdList,employeeId);
            Option option2 = new Option();
            option2.setCode(stageConfig.getStageName());
            option2.setInfo(String.valueOf(num));
            list.add(option2);
        }
        return list;
    }
    /**
     * 获取调诉机构案件中 未进行调诉登记的caseId
     * @return
     */
    public List<Long> getUnregisteredCaseId(Long teamId){
        return stageConfigMapper.getUnregisteredCaseId(teamId);
    }

    @Override
    public List<Option> getStage(Integer stageType) {
        return registerRecordMapper.getStage(stageType);
    }

    @Override
    public List<StageFieldPojo> getFormParam(Integer disposeWay,String stageTwoName) {
        //返回 结构类型 {表单对象{字段管理{字典数据}}}

        List<StageFieldPojo> pojoList = registerRecordMapper.getFormParam(stageTwoName,disposeWay);

        for (StageFieldPojo pojo : pojoList) {
            //字段管理
           /* SysDictType dictType = registerRecordMapper.getFieldById(pojo.getDictId());
            String replace = dictType.getDictType().replace(" " , "");
            dictType.setReceiveParam(replace);
            //判断字段类型
            String fieldType = dictType.getFieldType();
            if (StrUtil.equals(fieldType,"单选") || StrUtil.equals(fieldType,"多选")){
                List<SysDictData> dictDataList = registerRecordMapper.getSysDictDataByType(dictType.getDictType());
                //字典数据 集合
                dictType.setSysDictDataList(dictDataList);
            }*/


            SysDictType sysDictType=new SysDictType();
            if (StrUtil.equals(pojo.getFieldName(),"选择法院")){
                //获取当前的法院选择
                List<LawAgency> lawAgencies = lawAgencyService.getCourtNameOptions();
                List<SysDictData> dictDataList=new ArrayList<>();
                for (LawAgency row:lawAgencies) {
                    SysDictData dictData=new SysDictData();
                    dictData.setDictLabel(row.getCourtName());
                    dictData.setDictValue(row.getCourtName());
                    dictDataList.add(dictData);
                }
                sysDictType.setSysDictDataList(dictDataList);
            }else {
                if (pojo.getDictId()!=null){
                    sysDictType = registerRecordMapper.getFieldById(pojo.getDictId());
                    List<SysDictData> dictDataList = registerRecordMapper.getSysDictDataByType(sysDictType.getDictType());
                    //字典数据 集合
                    sysDictType.setSysDictDataList(dictDataList);
                }
            }
            sysDictType.setFieldType(pojo.getFieldType());
            pojo.setSysDictType(sysDictType);
        }
        return pojoList;
    }

    @Override
    public List<RegisterRecord> getAppMed(Long caseId, Integer type, Integer type1, Integer webSide) {
        CaseManage caseManage = collectionService.selectCaseManageId(caseId);
        Map<String, Object> map = HistoryPowerUtils.handleTsHistoryPower(caseManage.getEntrustingCaseDate(), caseManage.getHistoryPower(), TokenInformation.getCreateid().longValue(), webSide);
        map.put("caseId",caseId);
        map.put("registerType",type);
        map.put("registerType1",type1);
        map.put("webSide",webSide);
        startPage();
        return registerRecordMapper.getRecordByTypes(map);
    }

}
