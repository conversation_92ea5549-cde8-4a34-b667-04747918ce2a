package com.zws.appeal.service.approve;

import com.zws.system.api.model.LoginUser;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/11 17:05
 */
public interface ThreadServie {


    /**
     * 生成签章文件
     * 签章生成完后 生成压缩包、更新函件批次状态
     *
     * @param ids 函件量id
     */
    @Async
    void createSignFile(List<Long> ids, String type, LoginUser loginUser);
}
