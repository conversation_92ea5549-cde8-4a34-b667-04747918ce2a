package com.zws.appeal.service.appeal;

import com.zws.appeal.domain.appeal.ExecuteCase;
import com.zws.appeal.pojo.appeal.ExecuteCasePojo;
import com.zws.appeal.pojo.appeal.FilingCasePojo;

import java.util.List;
import java.util.Map;

/**
*编辑名称
*@Author：liuxifeng
*@Date：2024/6/25  16:36
*@Describe：编辑描述
*/
public interface IExecuteCaseService {


    int deleteByPrimaryKey(Long id);

    int insert(ExecuteCase record);

    int insertSelective(ExecuteCase record);

    ExecuteCase selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ExecuteCase record);

    int updateByPrimaryKey(ExecuteCase record);

    int batchInsert(List<ExecuteCase> list);

    /**
     * 列表查询
     * @param pojo
     * @return
     */
    List<ExecuteCasePojo> selectList(ExecuteCasePojo pojo);

    /**
     * 根据搜索条件获取caseId
     * @return
     */
    List<Long> selectCaseIds(ExecuteCasePojo pojo);

    /**
     * 列表查询-统计
     * @param pojo
     * @return
     */
    Map<String, Object> selectWithMoney(ExecuteCasePojo pojo);

    /**
     * 批量执行
     * @param pojo
     * @return
     */
    int batchExecute(ExecuteCasePojo pojo);


}
