package com.zws.appeal.service;

import com.zws.appeal.domain.*;
import com.zws.appeal.domain.letter.LetterTemplate;
import com.zws.appeal.pojo.*;
import com.zws.system.api.model.LoginUser;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface CaseService {

    /**
     * 根据id集合查询团队案件
     *
     * @param list
     * @return
     */
    List<CaseManage> selectCaseManageId(List<Long> list, int outsourcingTeamId);

    /**
     *
     * @param list
     * @param outsourcingTeamId
     * @param distributeType 1 分配给调诉员 2分配给调解员
     * @return
     */
    List<CaseManage> selectCaseManageId(List<Long> list, int outsourcingTeamId,Integer distributeType);

    /**
     * 根据字段查询该团队的案件/该团队案件全查(分页)
     *
     * @param manageQueryParam
     * @return
     */
    List<CaseManage> selectCaseManage1(ManageQueryParam manageQueryParam);

    /**
     * 根据字段查询该团队的案件/该团队案件全查
     *
     * @param exportReminder
     * @return
     */
    List<CaseManage> selectCaseManages(ExportReminder exportReminder);

    /**
     * 根据字段查询该团队的案件id
     *
     * @param exportReminder
     * @return
     */
    List<Long> selectCaseManageCaseIdList(ExportReminder exportReminder);

    /**
     * 根据字段查询该团队的案件/该团队案件全查-(根据分配状态查询案件总量以及案件总金额)
     *
     * @param exportReminder
     * @return
     */
    Map<String, Object> selectCaseManageCount(ExportReminder exportReminder);

    /**
     * 根据字段查询该团队的案件/该团队案件全查（分页）
     *
     * @param exportReminder
     * @return
     */
    List<CaseManage> selectCaseManage(ExportReminder exportReminder);

    /**
     * 根据团队id以及催员id查询该团队的案件
     *
     * @param map
     * @return
     */
    List<CaseManage> selectCaseManageByOdvId(Map<String, Object> map);

    /**
     * 根据字段查询该团队的案件总金额以及总案件量
     *
     * @param exportReminder
     * @return
     */
    Map<String, Object> selectCaseManagesMoney(ExportReminder exportReminder);

    /**
     * 查询统计每天团队的总委托金额
     *
     * @param map
     * @return
     */
    BigDecimal selectCaseManagesByTime(Map<String, Object> map);

    /**
     * 查询统计每天团队的总案件数量
     *
     * @param map
     * @return
     */
    int selectCaseManagesByTimeCount(Map<String, Object> map);

    /**
     * 根据字段查询该团队的案件/该团队案件全查
     *
     * @param manageQueryParam
     * @return
     */
    List<CaseManage> selectCase(ManageQueryParam manageQueryParam);

    /**
     * 根据团队id以及案件id查询统计金额以及可分配案件数量
     *
     * @param caseIds
     * @return
     */
    List<CaseManage> selectCaseStatistics(List<Long> caseIds);

    /**
     * 根据资产方id查询
     *
     * @param map
     * @return
     */
    List<CaseManage> selectCaseId(Map<String, Object> map);

    /**
     * 根据案件id查询案件申请记录表
     *
     * @param map
     * @return
     */
    List<ApplyRecord> selectApplyRecordByUserIdList(Map<String, Object> map);

    /**
     * 根据资产方id查询案件信息详情（验证批量申请留案）
     *
     * @param id
     * @return
     */
    void selectName(Long id, String clientIdcard, String clientName);

    /**
     * 根据资产方id查询案件信息详情（验证批量申请退案）
     *
     * @param id
     * @return
     */
    void selectId(Long id, String clientIdcard, String clientName);

    /**
     * 根据资产方id查询案件
     *
     * @param id
     * @return
     */
    void selectCaseId(Long id, String clientIdcard, String clientName);

    /**
     * 根据资产方名称查询
     *
     * @param name
     * @return
     */
    Owner selectOwner(String name);

    /**
     * 根据条件查询案件申请记录表
     *
     * @param
     * @return
     */
    List<ApplyRecord> selectApplyRecordCondition(ApplyRecord applyRecord);

    /**
     * 根据案件id/查询条件查询催收记录
     *
     * @param
     * @return
     */
    List<ExportDataUtils> selectUrgeRecord(ExportReminder exportReminder);

    /**
     * 指定分案写入催收员（批量）
     *
     * @param dataPreviews
     * @return
     */
    int updateCaseManage(List<DataPreview> dataPreviews);

    /**
     * 指定分案写入催收员（批量）-线程处理
     *
     * @param dataPreviews
     * @param type 分配类型: 1分配给诉讼员 、2分配给调解员
     * @return
     */
    int updateCaseManageThread(List<DataPreview> dataPreviews, ThreadEntityPojo threadEntityPojo,Integer distributeType);

    /**
     * 规则分案写入催收员（批量）
     *
     * @param divisionPersonnels
     * @param distributeType 分配类型: 1分配给诉讼员 、2分配给调解员
     * @return
     */
    int writeRuleDivision(DivisionPersonnel divisionPersonnels,Integer distributeType,LoginUser loginUser);

    /**
     * 模板分案写入催收员（批量）
     *
     * @param caseManage
     * @return
     */
    int updateCaseTemplate(List<CaseManage> caseManage, LoginUser loginUser);

    /**
     * 标记案件
     *
     * @param caseManage
     * @return
     */
    int updateCaseRecovery(List<CaseManage> caseManage);

    /**
     * 回收案件（批量勾选）
     *
     * @param caseManage
     * @return
     */
    int updateRecovery(List<CaseManage> caseManage);

    /**
     * 回收案件（批量勾选）-（线程处理）
     *
     * @param caseManage
     * @return
     */
    int updateRecoveryThread(List<CaseManage> caseManage);

    /**
     * 退案（批量勾选）
     *
     * @param retentionWithdrawalCase
     * @return
     */
    int insertRecord(RetentionWithdrawalCase retentionWithdrawalCase);

    /**
     * 退案（批量勾选）-线程处理
     *
     * @param retentionWithdrawalCase
     * @return
     */
    int insertRecordThread(RetentionWithdrawalCase retentionWithdrawalCase, ThreadEntityPojo threadEntityPojo);

    /**
     * 留案（批量勾选）
     *
     * @param retentionWithdrawalCase
     * @return
     */
    int insertKeepCase(RetentionWithdrawalCase retentionWithdrawalCase);

    /**
     * 留案（批量勾选）-线程处理
     *
     * @param retentionWithdrawalCase
     * @return
     */
    int insertKeepCaseThread(RetentionWithdrawalCase retentionWithdrawalCase, ThreadEntityPojo threadEntityPojo);

    /**
     * 停催（批量勾选）
     *
     * @param caseId
     * @return
     */
    int insertStopUrging(List<Long> caseId, Map<String, Object> map);

    /**
     * 停催（批量勾选）-线程处理
     *
     * @param caseId
     * @return
     */
    int insertStopUrgingThread(List<CaseManage> caseManages, ThreadEntityPojo threadEntityPojo);

    /**
     * （指定分案）分案记录写入
     *
     * @param distributionHistory
     * @return
     */
    int insertDistributionHistory(DistributionHistory distributionHistory, List<Long> caseIds);

    /**
     * （指定分案）分案记录写入-(线程处理)
     *
     * @param distributionHistory
     * @return
     */
    int insertDistributionHistoryThread(DistributionHistory distributionHistory, List<Long> caseIds, ThreadEntityPojo threadEntityPojo);

    /**
     * （模板分案）分案记录写入
     *
     * @param caseManage
     * @param loginUser  当前登录用户信息
     * @return
     */
    int insertTemplate(List<CaseManage> caseManage, LoginUser loginUser);

    /**
     * （模板分案）分案记录写入-(线程处理)
     *
     * @param caseManage
     * @return
     */
    int insertTemplateThread(List<CaseManage> caseManage, ThreadEntityPojo threadEntityPojo);

    /**
     * 写入案件申请记录表(批量申请停催)
     *
     * @param key
     * @return
     */
    int insertApplyRecord(String key);

    /**
     * 写入案件申请记录表(批量申请退案)
     *
     * @param key
     * @return
     */
    int insertApplyRecordDataBack(String key);

    /**
     * 写入案件申请记录表(批量申请留案)
     *
     * @param key
     * @return
     */
    int insertApplyRecordKeepCase(String key);

    /**
     * 模板分案信息验证
     *
     * @param caseManage
     * @return
     */
    CaseManage yanzheng(TemplateAllocationExcelVo vo, LoginUser loginUser);


    InfoLoan selectCaseInfoLoan(Long caseId);

    /**
     * 通过标签查询模版
     *
     * @param labelId
     * @return
     */
    LetterTemplate selectLetterTemplateByLabel(Long labelId);

    Map<String, List<TemplateSms>> selectAllSmsTemplate();


    /**
     * 客户管理-根据条件查询案件id
     *
     * @param exportReminder
     * @return
     */
    List<Long> selectCaseIdList(ExportReminder exportReminder);

    /**
     * 修改案件标记(合并资产端后)
     *
     * @param exportReminder
     * @return
     */
    void editMarkCase(ExportReminder exportReminder);
}
