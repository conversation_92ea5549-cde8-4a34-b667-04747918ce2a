package com.zws.appeal.service.impl;

import com.zws.appeal.domain.financial.FinancialAccount;
import com.zws.appeal.mapper.financial.FinancialAccountMapper;
import com.zws.appeal.service.IFinancialAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 银行账号 业务层
 *
 * <AUTHOR>
 * @date 2023年12月21日10:55:23
 */
@Service
public class FinancialAccountServiceImpl implements IFinancialAccountService {

    @Autowired
    private FinancialAccountMapper accountMapper;

    @Override
    public List<FinancialAccount> selectByCaseId(Long caseId) {
        if (caseId == null) {
            return new ArrayList<>();
        }
        return accountMapper.selectByCaseId(caseId);
    }
}
