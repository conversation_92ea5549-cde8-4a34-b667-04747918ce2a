package com.zws.appeal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zws.appeal.domain.*;
import com.zws.appeal.pojo.InheritanceCollection;
import com.zws.appeal.pojo.StateDesensitization;
import com.zws.appeal.pojo.staticConst.StaticConstantClass;
import com.zws.appeal.pojo.teamApplication.CreateStagingRecordUtils;
import com.zws.appeal.pojo.teamParameters.TeamStagingRecord;
import com.zws.appeal.service.TeamCaseService;
import com.zws.appeal.utils.*;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.smartcx.dto.TodoPushRequest;
import com.zws.common.core.domain.smartcx.dto.TodoPushResponse;
import com.zws.common.core.enums.MessageFormats;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.domain.ZwsDataPageVo;
import com.zws.common.domain.ZwsLoginUser;
import com.zws.common.utils.ZwsObjectTurnUtil;
import com.zws.appeal.agservice.DesensitizationAgService;
import com.zws.appeal.domain.record.StagingRecord;
import com.zws.appeal.mapper.CaseReorganizationRecordMapper;
import com.zws.appeal.pojo.PaymentCollectionUtils;
import com.zws.appeal.pojo.messageCenter.TeamMessageCenters;
import com.zws.appeal.pojo.myApproval.MyStagingRecordUtils;
import com.zws.appeal.pojo.vo.RecombinationRecordVo;
import com.zws.appeal.service.IReorganizationService;
import com.zws.appeal.service.RecordService;
import com.zws.dispose.pojo.ApplicationUtils;
import com.zws.standardmain.domain.dto.*;
import com.zws.standardmain.domain.dto.operate.ZwsJgAddApproveOperateDto;
import com.zws.standardmain.domain.dto.operate.ZwsJgApprovePageOperateDto;
import com.zws.standardmain.domain.dto.operate.ZwsSqlDataDto;
import com.zws.standardmain.service.ZwsJgApproveMainCustomService;
import com.zws.system.api.RemoteUserService;
import com.zws.system.api.domain.SysUser;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * @PackageName: com.zws.dispose.service.impl
 * @ClassName: ReorganizationServiceImpl
 * @Description: //TODO
 * @Author: Aasee
 * @Company: M+科技&深圳市债卫士信息科技有限公司
 * @website: www.amcmj.com
 * @Date: 2025/4/29 17:29
 */
@Service
@Slf4j
public class ReorganizationServiceImpl implements ZwsJgApproveMainCustomService, IReorganizationService {

    @Autowired
    private CaseReorganizationRecordMapper caseReorganizationRecordMapper;

    @Autowired
    private RecordService recordService;

    @Autowired
    private MessageToolUtils messageToolUtils;

    @Autowired
    private DesensitizationAgService desensitizationAgService;

    @Autowired
    private DesensitizationRedis desensitizationRedis;

    @Autowired
    private TeamCaseService teamCaseService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private TodoPushService todoPushService;


    private static void verifyParameters(RecombinationRecordVo recordVo) {
        if (ObjectUtils.isEmpty(recordVo.getCaseId())) {
            throw new GlobalException("案件id不能为空");
        }
        if (ObjectUtils.isEmpty(recordVo.getStagingNum())) {
            throw new GlobalException("分期期数不能为空");
        }
//        if (ObjectUtils.isEmpty(recordVo.getRepaymentMonthly())) {
//            throw new GlobalException("每期还款金额不能为空");
//        }
        if (ObjectUtils.isEmpty(recordVo.getRepaymentDate())) {
            throw new GlobalException("每期还款时间不能为空");
        }
//        if (recordVo.getRepaymentMonthly().compareTo(BigDecimal.ZERO) == 0 || recordVo.getRepaymentMonthly().compareTo(BigDecimal.ZERO) == -1) {
//            throw new GlobalException("分期还款金额必须大于0");
//        }
        if (!StrUtil.isBlankIfStr(recordVo.getDownPayment()) && (recordVo.getDownPayment().compareTo(BigDecimal.ZERO) == 0 || recordVo.getDownPayment().compareTo(BigDecimal.ZERO) == -1)) {
            throw new GlobalException("首付款金额必须大于0");
        }
        if (!ObjectUtils.isEmpty(recordVo.getReason()) && recordVo.getReason().length() > 200) {
            throw new GlobalException("申请原因字数超出限制,请重新输入");
        }
        if (CollectionUtils.isEmpty(recordVo.getInfoPlanVoList())) {
            throw new GlobalException("还款计划表不能为空！");
        }
        if (StrUtil.isBlankIfStr(recordVo.getTotalRepayment())) {
            throw new GlobalException("分期后还款总金额不能为空！");
        }
    }

    @Override
    public void jgAddApprove(ZwsJgAddApproveOperateDto jgAddApproveOperateDto, ZwsLoginUser loginUser) {
        RecombinationRecordVo recombinationRecordVo = ZwsObjectTurnUtil.parentTurnSon(jgAddApproveOperateDto.getApproveData(), RecombinationRecordVo.class);
//        CaseReorganizationRecord caseReorganizationRecord = new CaseReorganizationRecord();
//        caseReorganizationRecord.setDownPayment(recombinationRecordVo.getDownPayment());
//        caseReorganizationRecord.setFinalPayment(recombinationRecordVo.getFinalPayment());
//        caseReorganizationRecord.setDownPayment(recombinationRecordVo.getDownPayment());
//        caseReorganizationRecord.setDownPayment(recombinationRecordVo.getDownPayment());

        // 基础校验参数
        verifyParameters(recombinationRecordVo);
        List<StagingRecord> stagingRecords = recordService.selectStagingRecord(recombinationRecordVo.getCaseId());//根据案件id查询分期还款申请表
//        判断该案件是否已经退案或者停催
//        ManageQueryParam manageQueryParam = new ManageQueryParam();
//        manageQueryParam.setOutsourcingTeamId(TokenInformation.getCreateid());   //团队id
//        manageQueryParam.setCaseId(record.getCaseId());   //案件id
        CaseManage caseManage = recordService.selectCaseManageByCaseId(recombinationRecordVo.getCaseId().intValue());
        if (ObjectUtils.isEmpty(caseManage)) {
            throw new GlobalException("该案件已被退案或者停催");
        }
        CaseReorganizationRecord caseReorganizationRecord = new CaseReorganizationRecord();
        caseReorganizationRecord.setCaseId(recombinationRecordVo.getCaseId());
        caseReorganizationRecord.setTeamId(loginUser.getTeamId());
        caseReorganizationRecord.setOdvId(loginUser.getUserid());
        Integer count = caseReorganizationRecordMapper.selectNotFinishCountByCaseId(caseReorganizationRecord);
        if (count > 0){
            throw new GlobalException("该案件已提交减免申请，请勿重复提交");
        }
//        if (!ObjectUtils.isEmpty(stagingRecords)) {
//            for (StagingRecord stagingRecord1 : stagingRecords) {
//                if (!stagingRecord1.getProce().equals(5) && !stagingRecord1.getProce().equals(4) && !stagingRecord1.getProce().equals(61) && !stagingRecord1.getProce().equals(62) && !stagingRecord1.getProce().equals(7)) {
//                    throw new GlobalException("该案件已提交分期还款申请，请勿重复提交");
//                }
//            }
//        }
        BigDecimal remainingDue = recordService.selectInfoLoan(recombinationRecordVo.getCaseId());
        BigDecimal afterBaseDateInterest = caseReorganizationRecordMapper.selectAfterBaseDateInterestByInfoLoan(recombinationRecordVo.getCaseId());
        if (recombinationRecordVo.getTotalRepayment().compareTo(remainingDue) > 0){
            throw new GlobalException("分期后的还款总金额大于剩余应还债权金额");
        }
        Date first = null;
        Date last = null;
        //根据还款期数StagingNum和还款日RepaymentDate计算首期和最后一期还款日期，如果还款日小于等于今天则首期还款日从下月开始
        if (recombinationRecordVo.getRepaymentDate() <= Integer.valueOf(LocalDate.now().getDayOfMonth())) {
            //首期还款日月份天数
            int nextFirstDay = LocalDate.now().plusMonths(1).lengthOfMonth();
            if(Integer.valueOf(nextFirstDay)<recombinationRecordVo.getRepaymentDate()){
                first = Date.from(LocalDate.now().plusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }else{
                first = Date.from(LocalDate.now().plusMonths(1).withDayOfMonth(recombinationRecordVo.getRepaymentDate()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }
            //末期还款日月份天数
            int nextLastDay = LocalDate.now().plusMonths(recombinationRecordVo.getStagingNum()).lengthOfMonth();
            if(Integer.valueOf(nextLastDay)<recombinationRecordVo.getRepaymentDate()){
                last = Date.from(LocalDate.now().plusMonths(recombinationRecordVo.getStagingNum()).with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }else{
                last = Date.from(LocalDate.now().plusMonths(recombinationRecordVo.getStagingNum()).withDayOfMonth(recombinationRecordVo.getRepaymentDate()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }
        }
        //如果还款日大于今天则首期还款日从当月开始
        else {
            //first = Date.from(LocalDate.now().withDayOfMonth(record.getRepaymentDate()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            //首期还款日月份天数
            int nextFirstDay = LocalDate.now().lengthOfMonth();
            if(Integer.valueOf(nextFirstDay)<recombinationRecordVo.getRepaymentDate()){
                first = Date.from(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }else{
                first = Date.from(LocalDate.now().withDayOfMonth(recombinationRecordVo.getRepaymentDate()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }

            //末期还款日月份天数
            int nextLastDay = LocalDate.now().plusMonths(recombinationRecordVo.getStagingNum()-1).lengthOfMonth();
            if(Integer.valueOf(nextLastDay)<recombinationRecordVo.getRepaymentDate()) {
                last = Date.from(LocalDate.now().plusMonths(recombinationRecordVo.getStagingNum()-1).with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }else{
                last = Date.from(LocalDate.now().plusMonths(recombinationRecordVo.getStagingNum()-1).withDayOfMonth(recombinationRecordVo.getRepaymentDate()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }
        }
        // 复制属性
        CaseReorganizationRecord record = BeanUtil.copyProperties(recombinationRecordVo, CaseReorganizationRecord.class);
        // 委案批次号
        record.setEntrustingCaseBatchNum(caseManage.getEntrustingCaseBatchNum());
        // 委案日期
        record.setEntrustingCaseDate(caseManage.getEntrustingCaseDate());
        // 退案日期
        record.setReturnCaseDate(caseManage.getReturnCaseDate());
        // 催员id
        record.setOdvId(loginUser.getUserid());
        // 催员名称
        record.setOdvName(loginUser.getUsername());
        // 首期还款日
        record.setFirstRepaymentDate(first);
        // 末期还款日
        record.setLastRepaymentDate(last);
        // 还款计划记录
        record.setRepaymentPlan(JSON.toJSONStringWithDateFormat(recombinationRecordVo.getInfoPlanVoList(),"yyyy-MM-dd"));
        //分期减免金额=剩余应还-总分期还款金额
        record.setDeductionAmount(remainingDue.subtract(recombinationRecordVo.getTotalRepayment()));
        //分期时剩余应还
        record.setOldRemainingDue(remainingDue);
        record.setDownPayment(recombinationRecordVo.getDownPayment());
        record.setFinalPayment(recombinationRecordVo.getFinalPayment());
        record.setTotalRepayment(recombinationRecordVo.getTotalRepayment());
        record.setUploadCredentials(recombinationRecordVo.getUploadCredentials());
        record.setApproveId(jgAddApproveOperateDto.getApproveId());
        record.setTeamId(loginUser.getTeamId());
        record.setApplyType(1);
        record.setOperationType(loginUser.getOperationType().intValue());
        record.setState("待审核");
        record.setAfterBaseDateInterest(afterBaseDateInterest);
        caseReorganizationRecordMapper.insertSelective(record);

        try{
            //        申请成功给第一级审批人发送提醒
            ApprovalSteps approvalSteps = new ApprovalSteps();
            approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
            approvalSteps.setApproveCode(2); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
            TeamMessageCenters teamMessageCenters = messageToolUtils.selectApprovalProcess(approvalSteps);
            messageToolUtils.messageReminder(MessageFormats.APPROVAL_REDUCTION, teamMessageCenters);
        }catch (Exception e){
            log.error("发送站内消息失败",e);
        }

    }

    @Override
    public void jgApproveFinalPass(ZwsJgVerificationDto jgVerification, ZwsLoginUser loginUser) {
        List<Long> approveIds = jgVerification.getApproveIds();
        Long l = approveIds.get(0);
        CaseReorganizationRecord caseReorganizationRecord = new CaseReorganizationRecord();
        caseReorganizationRecord.setState("审核中");
        caseReorganizationRecord.setApproveId(l);
        caseReorganizationRecordMapper.updateByApproveIdSelective(caseReorganizationRecord);
    }

    @Override
    public void jgApproveNotPass(ZwsJgVerificationDto jgVerification, ZwsLoginUser loginUser) {
        List<Long> approveIds = jgVerification.getApproveIds();
        for (Long approveId : approveIds) {
            CaseReorganizationRecord caseReorganizationRecord = new CaseReorganizationRecord();
            caseReorganizationRecord.setState("未通过");
            caseReorganizationRecord.setApproveId(approveId);
            caseReorganizationRecordMapper.updateByApproveIdSelective(caseReorganizationRecord);
        }
    }

    @Override
    public void jgApproveRevoke(ZwsJgVerificationDto jgVerification, ZwsLoginUser loginUser) {
        List<Long> approveIds = jgVerification.getApproveIds();
        for (Long approveId : approveIds) {
            CaseReorganizationRecord caseReorganizationRecord = new CaseReorganizationRecord();
            caseReorganizationRecord.setState("已撤销");
            caseReorganizationRecord.setApproveId(approveId);
            caseReorganizationRecordMapper.updateByApproveIdSelective(caseReorganizationRecord);
        }
    }

    @Override
    public void jgAsyncNotification(ZwsJgAsyncNotificationDto jgAsyncNotificationDto, ZwsLoginUser loginUser) {
        log.info("notification 参数：{}", jgAsyncNotificationDto.toString());
        ZwsZcApproveSetupNodeDto zcNextApproveSetupNode = jgAsyncNotificationDto.getZcNextApproveSetupNode();
        if (!ObjectUtils.isEmpty(zcNextApproveSetupNode)){
            List<Long> approveIds = jgAsyncNotificationDto.getApproveIds();
            List<ZwsZcApproveSetupInfoDto> zcApproveSetupInfoDtoList = zcNextApproveSetupNode.getZcApproveSetupInfoDtoList();
            if (!ObjectUtils.isEmpty(zcApproveSetupInfoDtoList)){
                for (Long approveId : approveIds) {
                    for (ZwsZcApproveSetupInfoDto zwsZcApproveSetupInfoDto : zcApproveSetupInfoDtoList) {
                        Long userId = zwsZcApproveSetupInfoDto.getUserId();
                        R<SysUser> userById = remoteUserService.getUserById(userId);
                        log.info("远程调用查询审批人的信息：{}",userById);
                        if (userById != null && userById.getCode() == 200){
                            SysUser data = userById.getData();
                            if (data != null && !ObjectUtils.isEmpty(data.getEmployeeNo())){
                                // todo 推送数据到智慧财信的待办页面
                                TodoPushRequest todoPushRequest = new TodoPushRequest();
                                todoPushRequest.setObjectType(24);
                                todoPushRequest.setTitle(StrUtil.format("{}的减免审批待办",data.getNickName()));
                                todoPushRequest.setImportLevel(1);
                                todoPushRequest.setTodoStatus(1);
                                todoPushRequest.setUrl(StrUtil.format("https://assest-test.amc.hnchasing.com/service/caseApprove?flag=true&approveId={}&approveCode={}", approveId, "reorganization"));
                                todoPushRequest.setAppUrl(StrUtil.format("https://assest-test.amc.hnchasing.com/approve/examine?flag=true&approveId={}&approveCode={}", approveId, "reorganization"));
                                long l = System.currentTimeMillis();
//                        todoPushRequest.setCreateTime(String.valueOf(l));
//                        todoPushRequest.setReceiveTime(String.valueOf(l));
                                todoPushRequest.setUpdateTime(String.valueOf(l));
                                todoPushRequest.setIsDelete(0);
//                        todoPushRequest.setThirdFlag("GDCZXT001");
                                todoPushRequest.setSenderLoginName("admin");
                                if (!ObjectUtils.isEmpty(data.getCxUserId())){
                                    todoPushRequest.setMemberLoginName(data.getCxUserId());
                                    todoPushRequest.setBusinessId(StrUtil.format("{}-{}",approveId,data.getUserId()));
                                    log.info("推送待办数据的请求：{}", JSONUtil.toJsonStr(todoPushRequest));
                                    TodoPushResponse todoPushResponse = todoPushService.pushTodo(todoPushRequest);
                                    log.info("推送待办数据后的响应：{}", JSONUtil.toJsonStr(todoPushResponse));
                                    Integer code = todoPushResponse.getCode();
                                    if (!code.equals(20000)){
                                        // 如果失败，需要补发
                                        log.info("此审批ID初次推送待办数据失败：{}，原因为：{}",todoPushRequest.getBusinessId(),todoPushResponse.getMessage());
                                        todoPushService.storeToRedis(todoPushRequest);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public ZwsDataPageVo jgQueryApproveList(ZwsJgApprovePageOperateDto pageOperateDto) {
        List<Long> approveIds = pageOperateDto.getApproveIds();
        List<Long> approveProcessIds = pageOperateDto.getApproveProcessIds();
        ZwsSqlDataDto sqlDataDto = pageOperateDto.getSqlDataDto();
        ZwsLoginUser loginUser = pageOperateDto.getLoginUser();
        LoginUser user = BeanUtil.copyProperties(loginUser, LoginUser.class);
        PaymentCollectionUtils paymentCollectionUtils = ZwsObjectTurnUtil.parentTurnSon(pageOperateDto.getApproveData(), PaymentCollectionUtils.class);
        paymentCollectionUtils.setClientName(FieldEncryptUtil.encrypt(paymentCollectionUtils.getClientName()));
        paymentCollectionUtils.setClientIdcard(FieldEncryptUtil.encrypt(paymentCollectionUtils.getClientIdcard()));
        PageHelper.startPage(pageOperateDto.getPageNum(), pageOperateDto.getPageSize());
        verification(paymentCollectionUtils);
        paymentCollectionUtils.setApproveIds(approveIds);
        paymentCollectionUtils.setApproveProcessIds(approveProcessIds);
        paymentCollectionUtils.setTeamId(TokenInformation.getCreateid());  //团队id
        //部门是否有负责人
        if (TokenInformation.getType(user) == UserConstants.ACCOUNT_TYPE_0) {
            //团队主账号 只能查看没有 部门负责人的部门
            paymentCollectionUtils.setHaveHead(0);
        } else {
            //只能查本部门的
            paymentCollectionUtils.setDeptIds(Arrays.asList(TokenInformation.getDeptId()));
        }
        //List<MyStagingRecordUtils> myStagingRecordUtils = caseReorganizationRecordMapper.jgQueryApproveList(paymentCollectionUtils);
        List<MyStagingRecordUtils> myStagingRecordUtils = caseReorganizationRecordMapper.jgQueryApproveListNew(paymentCollectionUtils,sqlDataDto);
        if (!ObjectUtils.isEmpty(myStagingRecordUtils)) {
            Desensitization desensitization = desensitizationAgService.getDesensitization();
            for (MyStagingRecordUtils myStagingRecordUtil : myStagingRecordUtils) {

                if (ObjectUtils.isEmpty(myStagingRecordUtil.getApproveStart())) {
                    if (myStagingRecordUtil.getExamineState().equals("待审核") || myStagingRecordUtil.getExamineState().equals("审核中")) {
                        myStagingRecordUtil.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
                    }
                }
                if (myStagingRecordUtil.getExamineState().equals("已退案关闭")) {
                    myStagingRecordUtil.setApproveStart(5);
                }
                boolean caseBelongToTeam = checkCaseBelongToTeam(myStagingRecordUtil.getCaseId());
                myStagingRecordUtil.setButton(caseBelongToTeam ? 1 : 0);

                DecryptUtils.dataDecrypt(myStagingRecordUtil);
                if (desensitization != null) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(myStagingRecordUtil.getClientName())) {
                        myStagingRecordUtil.setClientName(DataMaskingUtils.nameMasking(myStagingRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(myStagingRecordUtil.getClientIdcard())) {
                        myStagingRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(myStagingRecordUtil.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(myStagingRecordUtil.getClientCensusRegister())) {
                        myStagingRecordUtil.setClientCensusRegister(DataMaskingUtils.Masking(myStagingRecordUtil.getClientCensusRegister()));
                    }

                }
            }
        }
        PageInfo<MyStagingRecordUtils> pageInfo = new PageInfo<>(myStagingRecordUtils);
        return PageUtils.getPageDataTable(myStagingRecordUtils, pageInfo);
    }

    @Override
    public ZwsDataPageVo queryJgTeamApplyList(ZwsJgApprovePageOperateDto pageOperateDto) {
        List<Long> approveIds = pageOperateDto.getApproveIds();
        ZwsLoginUser zwsLoginUser = pageOperateDto.getLoginUser();
        LoginUser loginUser = BeanUtil.copyProperties(zwsLoginUser, LoginUser.class);
        TeamStagingRecord teamStagingRecord = ZwsObjectTurnUtil.parentTurnSon(pageOperateDto.getApproveData(), TeamStagingRecord.class);
        teamStagingRecord.setClientName(FieldEncryptUtil.encrypt(teamStagingRecord.getClientName()));
        teamStagingRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamStagingRecord.getClientIdcard()));
        if (!ObjectUtils.isEmpty(teamStagingRecord.getStringDeptId())) {
            int dept = teamStagingRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamStagingRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamStagingRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamStagingRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamStagingRecord.setUserId(Integer.parseInt(teamStagingRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamStagingRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamStagingRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamStagingRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamStagingRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamStagingRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        teamStagingRecord.setApproveIds(approveIds);
        PageHelper.startPage(teamStagingRecord.getPageNum(), teamStagingRecord.getPageSize());
        teamStagingRecord.setDeptIds(list);
        teamStagingRecord.setCreateId(TokenInformation.getCreateid());
        TeamStagingRecord teamStagingRecord1 = stagingRecordId(teamStagingRecord);
        Map<String, Object> map = BeanUtil.beanToMap(teamStagingRecord1);
        List<CreateStagingRecordUtils> createStagingRecordUtils = caseReorganizationRecordMapper.queryJgTeamApplyList(map);
        if (!ObjectUtils.isEmpty(createStagingRecordUtils)) {
            for (CreateStagingRecordUtils createStagingRecordUtil : createStagingRecordUtils) {
                //当前团队Id等于案件所属团队，就有权限作废
                if (ObjectUtil.equals(createStagingRecordUtil.getTeamId(), TokenInformation.getCreateid())){
                    createStagingRecordUtil.setAuthority(0);
                }else {
                    createStagingRecordUtil.setAuthority(1);
                }

                DecryptUtils.dataDecrypt(createStagingRecordUtil);  //解密

                CaseManage caseManage = recordService.selectCaseManageCaseId(createStagingRecordUtil.getCaseId());
                if (caseManage == null) {
                    createStagingRecordUtil.setButton(0);
                    continue;
                }
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (new Long((long) TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                        createStagingRecordUtil.setButton(1);
                    } else {
                        createStagingRecordUtil.setButton(0);
                    }
                } else {
                    createStagingRecordUtil.setButton(0);
                }
            }

            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (CreateStagingRecordUtils createStagingRecordUtil : createStagingRecordUtils) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(createStagingRecordUtil.getClientName())) {
                        createStagingRecordUtil.setClientName(DataMaskingUtils.nameMasking(createStagingRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(createStagingRecordUtil.getClientIdcard())) {
                        createStagingRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(createStagingRecordUtil.getClientIdcard()));
                    }
                }
            }
        }
        PageInfo<CreateStagingRecordUtils> pageInfo = new PageInfo<>(createStagingRecordUtils);
        return PageUtils.getPageDataTable(createStagingRecordUtils, pageInfo);
    }

    /**
     * 分期申请-处理时间
     *
     * @return
     */
    public TeamStagingRecord stagingRecordId(TeamStagingRecord teamStagingRecord) {
        if (!ObjectUtils.isEmpty(teamStagingRecord.getEntrustingCaseDate2())) {
            DateTime dateTime = DateUtil.endOfDay(teamStagingRecord.getEntrustingCaseDate2());  //一天的结束，结果：2017-03-01 23:59:59
            teamStagingRecord.setEntrustingCaseDate2(dateTime);
        }
        if (!ObjectUtils.isEmpty(teamStagingRecord.getReturnCaseDate2())) {
            DateTime dateTime = DateUtil.endOfDay(teamStagingRecord.getReturnCaseDate2());  //一天的结束，结果：2017-03-01 23:59:59
            teamStagingRecord.setReturnCaseDate2(dateTime);
        }
        if (!ObjectUtils.isEmpty(teamStagingRecord.getExamineTime2())) {
            DateTime dateTime = DateUtil.endOfDay(teamStagingRecord.getExamineTime2());  //一天的结束，结果：2017-03-01 23:59:59
            teamStagingRecord.setExamineTime2(dateTime);
        }
        if (!ObjectUtils.isEmpty(teamStagingRecord.getApplyDate2())) {
            DateTime dateTime = DateUtil.endOfDay(teamStagingRecord.getApplyDate2());  //一天的结束，结果：2017-03-01 23:59:59
            teamStagingRecord.setApplyDate2(dateTime);
        }
        return teamStagingRecord;
    }

    /**
     * 根据部门id查询部门及以下部门的所有部门id
     *
     * @return
     */
    public List<Integer> Encapsulations(Integer deptId, LoginUser loginUser) {
        if (deptId == null) {
            return Encapsulation(loginUser);
        }
        Map<String, Object> accountInfo = loginUser.getAccountInfo();
        Integer teamId = (Integer) accountInfo.get(UserConstants.TEAM_ID);
        List<Dept> deptList = teamCaseService.selectDept(teamId);//根据团队id查询该团队所有部门信息
        List<Integer> list = new ArrayList<>();
        list.add(deptId);  //所传的部门id
        for (Dept dept : deptList) {
            String ancestors = dept.getAncestors();  //祖级列表
            List<String> splits = com.zws.common.core.utils.SplitUtils.strSplitComma(ancestors);
            for (String split : splits) {
                if (StrUtil.equals(split, StrUtil.toString(deptId))) {
                    list.add(dept.getId());
                }
            }
        }
        return list;
    }

    /**
     * 根据登录人id查询登录人部门及以下部门的所有部门id
     *
     * @param loginUser 当前登录人信息
     * @return
     */
    public List<Integer> Encapsulation(com.zws.system.api.model.LoginUser loginUser) {
        if (loginUser == null) {
            throw new ServiceException("当前登录信息获取失败");
        }
        Map<String, Object> accountInfo = loginUser.getAccountInfo();
        Integer userId = (Integer) accountInfo.get(UserConstants.USER_ID);
        Employees employees = teamCaseService.selectEmployeesId(userId);  //根据员工id查询员工部门id
        if (ObjectUtils.isEmpty(employees)) {
            return new ArrayList<Integer>();
        }
        Integer departmentId = employees.getDepartmentId();    //登录用户的部门id


        String departmentIds = String.valueOf(departmentId);  //强转为string类型
        List<Dept> deptList = teamCaseService.selectDept(TokenInformation.getCreateid(loginUser));//根据团队id查询该团队所有部门信息
        List<Integer> list = new ArrayList<>();
        list.add(departmentId);  //登录人本身的部门id
        for (Dept dept : deptList) {
            String ancestors = dept.getAncestors();  //祖级列表
            String[] split = ancestors.split(",");
            for (String splits : split) {
                if (splits.equals(departmentIds)) {
                    list.add(dept.getId());
                }
            }
        }
        return list;
    }


    @Override
    public ZwsDataPageVo queryJgMyApplyList(ZwsJgApprovePageOperateDto pageOperateDto) {
        List<Long> approveIds = pageOperateDto.getApproveIds();
        ZwsLoginUser loginUser = pageOperateDto.getLoginUser();
        LoginUser user = BeanUtil.copyProperties(loginUser, LoginUser.class);
        ApplicationUtils applicationUtils = ZwsObjectTurnUtil.parentTurnSon(pageOperateDto.getApproveData(), ApplicationUtils.class);
        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
        PageHelper.startPage(applicationUtils.getPageNum(), applicationUtils.getPageSize());
        verification(applicationUtils);
        applicationUtils.setOperationType(TokenInformation.getType());
        applicationUtils.setApproveIds(approveIds);

        List<InheritanceCollection> myApplyList = caseReorganizationRecordMapper.queryJgMyApplyList(applicationUtils);
        List<InheritanceCollection> inheritanceCollections1 = whetherItBelongs(myApplyList);
        List<InheritanceCollection> inheritanceCollections = desensitizationTreatment(inheritanceCollections1);

        PageInfo<InheritanceCollection> pageInfo = new PageInfo<>(inheritanceCollections);
        return PageUtils.getPageDataTable(inheritanceCollections, pageInfo);
    }

    /**
     * 我的申请-处理脱敏
     *
     * @return
     */
    public List<InheritanceCollection> desensitizationTreatment(List<InheritanceCollection> inheritanceCollections) {
        if (!ObjectUtils.isEmpty(inheritanceCollections)) {
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (InheritanceCollection inheritanceCollection : inheritanceCollections) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientName())) {
                        inheritanceCollection.setClientName(DataMaskingUtils.nameMasking(inheritanceCollection.getClientName()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientPhone())) {
                        inheritanceCollection.setClientPhone(DataMaskingUtils.phoneMasking(inheritanceCollection.getClientPhone()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientIdcard())) {
                        inheritanceCollection.setClientIdcard(DataMaskingUtils.idMasking(inheritanceCollection.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientCensusRegister())) {
                        inheritanceCollection.setClientCensusRegister(DataMaskingUtils.Masking(inheritanceCollection.getClientCensusRegister()));
                    }
                }
            }
        }
        return inheritanceCollections;
    }


    /**
     * 我的申请-判断案件是否属于登录人
     *
     * @param inheritanceCollections
     * @return
     */
    public List<InheritanceCollection> whetherItBelongs(List<InheritanceCollection> inheritanceCollections) {
        for (InheritanceCollection inheritanceCollection : inheritanceCollections) {
            //当前团队Id等于案件所属团队，就有权限作废
            if (ObjectUtil.equals(inheritanceCollection.getTeamId(),TokenInformation.getCreateid()) ){
                inheritanceCollection.setAuthority(0);
            }else {
                inheritanceCollection.setAuthority(1);
            }

            DecryptUtils.dataDecrypt(inheritanceCollection);  //解密
            CaseManage caseManage = recordService.selectCaseManageCaseId(inheritanceCollection.getCaseId());
            if (ObjectUtils.isEmpty(caseManage)) {
                inheritanceCollection.setButton(0);
                continue;
            }
            if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                if (TokenInformation.getType() == 0 && new Long(TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                    inheritanceCollection.setButton(0);
                } else {
                    if (ObjectUtils.isEmpty(caseManage.getOdvId())) {
                        inheritanceCollection.setButton(0);
                    } else {
                        if (new Long(TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId()) && new Long(TokenInformation.getUserid()).equals(caseManage.getOdvId())) {
                            inheritanceCollection.setButton(1);
                        } else {
                            inheritanceCollection.setButton(0);
                        }
                    }
                }
            } else {
                inheritanceCollection.setButton(0);
            }
        }
        return inheritanceCollections;
    }

    /**
     * 处理时间-（我的申请）
     *
     * @param applicationUtils
     * @return
     */
    public ApplicationUtils verification(ApplicationUtils applicationUtils) {
        //        修改结束时间为一天的结束
        if (!ObjectUtils.isEmpty(applicationUtils.getApplyDate2())) {
            DateTime dateTime = DateUtil.endOfDay(applicationUtils.getApplyDate2());  //一天的结束，结果：2017-03-01 23:59:59
            applicationUtils.setApplyDate2(dateTime);
        }
        if (!ObjectUtils.isEmpty(applicationUtils.getEntrustingCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(applicationUtils.getEntrustingCaseDate2());
            applicationUtils.setEntrustingCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(applicationUtils.getReturnCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(applicationUtils.getReturnCaseDate2());
            applicationUtils.setReturnCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(applicationUtils.getUpdateTime2())) {
            DateTime dateTime1 = DateUtil.endOfDay(applicationUtils.getUpdateTime2());
            applicationUtils.setUpdateTime2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(applicationUtils.getRepaymentDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(applicationUtils.getRepaymentDate2());
            applicationUtils.setRepaymentDate2(dateTime1);
        }
        return applicationUtils;
    }


    /**
     * 判断案件是否属于当前团队
     *
     * @param caseId 案件ID
     * @return true-是，false-否（不属于当前团队）
     */
    public boolean checkCaseBelongToTeam(Long caseId) {
        CaseManage caseManage = recordService.selectCaseManageCaseId(caseId);
        if (ObjectUtils.isEmpty(caseManage)) {
            //找不到案件，案件已删除
            return false;
        }
        //案件当前的委案团队
        Long caseOutsourcingTeamId = caseManage.getOutsourcingTeamId();
        if (caseOutsourcingTeamId == null) {
            //当前委案团队为null 表示为委案，肯定不属于当前团队
            return false;
        }

        if (ObjectUtil.equals(caseOutsourcingTeamId.longValue(), TokenInformation.getCreateid().longValue())) {
            //案件当前的委案团队 的 用户当前所在的团队
            return true;
        }
        return false;
    }

    /**
     * 处理时间-(回款/减免/分期还款)
     *
     * @param paymentCollectionUtils
     * @return
     */
    public PaymentCollectionUtils verification(PaymentCollectionUtils paymentCollectionUtils) {
        //        修改结束时间为一天的结束
        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getUpdateTime2())) {
            DateTime dateTime = DateUtil.endOfDay(paymentCollectionUtils.getUpdateTime2());  //一天的结束，结果：2017-03-01 23:59:59
            paymentCollectionUtils.setUpdateTime2(dateTime);
        }
        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getEntrustingCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(paymentCollectionUtils.getEntrustingCaseDate2());
            paymentCollectionUtils.setEntrustingCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getReturnCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(paymentCollectionUtils.getReturnCaseDate2());
            paymentCollectionUtils.setReturnCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getApplyDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(paymentCollectionUtils.getApplyDate2());
            paymentCollectionUtils.setApplyDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getCreateTime2())) {
            DateTime dateTime1 = DateUtil.endOfDay(paymentCollectionUtils.getCreateTime2());
            paymentCollectionUtils.setCreateTime2(dateTime1);
        }
        return paymentCollectionUtils;
    }
}