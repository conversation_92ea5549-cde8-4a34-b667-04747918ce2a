package com.zws.appeal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.mapper.RuleDivisionMapper;
import com.zws.appeal.pojo.ExportReminder;
import com.zws.appeal.pojo.QueryTimeInterval;
import com.zws.appeal.service.RuleDivisionService;
import com.zws.appeal.utils.TokenInformation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 规则划分服务 实现类
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@Service
public class RuleDivisionServiceImpl implements RuleDivisionService {

    @Resource
    private RuleDivisionMapper ruleDivisionMapper;
    @Autowired
    private CaseServiceImpl caseService;

    /**
     * 根据条件或者id集合查询案件信息
     *
     * @return
     */
    @Override
    public List<CaseManage> selectCaseManages(ExportReminder exportReminder) {
        ExportReminder exportReminder1 = conditionalTreatment(exportReminder);
        List<CaseManage> caseManages = ruleDivisionMapper.selectCaseManages(exportReminder1);
        return caseManages;
    }

    /**
     * 根据条件或者id集合查询案件金额总和
     *
     * @return
     */
    @Override
    public Map<String, Object> selectCaseManageMoneyCount(ExportReminder exportReminder) {
        ExportReminder exportReminder1 = conditionalTreatment(exportReminder);
        Map<String, Object> map = ruleDivisionMapper.selectCaseManageMoneyCount(exportReminder1);
        return map;
    }

    /**
     * 根据条件或者id集合查询案件信息-(查询共债人身份证号信息)
     *
     * @return
     */
    @Override
    public List<String> selectCaseClientIdcard(ExportReminder exportReminder) {
        ExportReminder exportReminder1 = conditionalTreatment(exportReminder);
        return ruleDivisionMapper.selectCaseClientIdcard(exportReminder1);
    }

    /**
     * 根据条件或者id集合查询案件信息-(查询非共债人身份证号信息)
     *
     * @return
     */
    @Override
    public List<String> selectCaseClientIdcardOne(ExportReminder exportReminder) {
        ExportReminder exportReminder1 = conditionalTreatment(exportReminder);
        return ruleDivisionMapper.selectCaseClientIdcardOne(exportReminder1);
    }

    /**
     * 根据身份证号查询共债案件信息
     *
     * @return
     */
    @Override
    public List<CaseManage> selectCaseManageClientIdcard(ExportReminder exportReminder) {
//        ExportReminder exportReminder1 = conditionalTreatment(exportReminder);
        return ruleDivisionMapper.selectCaseManageClientIdcard2(exportReminder);
    }

    /**
     * 根据身份证号查询非共债案件信息
     *
     * @return
     */
    @Override
    public List<CaseManage> selectCaseManageClientIdcardOne(ExportReminder exportReminder) {
//        ExportReminder exportReminder1 = conditionalTreatment(exportReminder);
        return ruleDivisionMapper.selectCaseManageClientIdcardOne(exportReminder);
    }

    /**
     * 根据身份证号查询共债案件委托金额总和
     *
     * @return
     */
    @Override
    public Map<String, Object> selectCaseManageMoney(ExportReminder exportReminder) {
        return ruleDivisionMapper.selectCaseManageMoney(exportReminder);
    }

    /**
     * 根据条件查询案件操作信息
     *
     * @return
     */
    @Override
    public List<Long> selectDistributionHistory(QueryTimeInterval queryTimeInterval) {
        Map<String, Object> map = BeanUtil.beanToMap(queryTimeInterval);
        List<Long> distributionHistories = ruleDivisionMapper.selectDistributionHistory(map);   //不能交叉分案的id集合
        return distributionHistories;
    }

    /**
     * 案件管理查询条件处理工具
     *
     * @param exportReminder
     * @return
     */
    public ExportReminder conditionalTreatment(ExportReminder exportReminder) {
        exportReminder.setDecryptKey(FieldEncryptUtil.fieldKey);
        if (!ObjectUtils.isEmpty(exportReminder.getCondition())) {
            if (!exportReminder.getCondition()) {
                ExportReminder exportReminder1 = new ExportReminder();
                exportReminder1.setIds(exportReminder.getIds());
                if (exportReminder.getCreateId() == null) {//异步的时候是从外面传进来的
                    exportReminder1.setCreateId(TokenInformation.getCreateid());  //团队id
                } else {
                    exportReminder1.setCreateId(exportReminder.getCreateId());  //团队id
                }
                exportReminder1.setCaseStateList(exportReminder.getCaseStateList());
                if (exportReminder.getDistributeType()!=null){
                    exportReminder1.setDistributeType(exportReminder.getDistributeType());
                }
                return exportReminder1;
            } else {
                exportReminder.setIds(null);
                if (exportReminder.getCreateId() == null) {//异步的时候是从外面传进来的
                    exportReminder.setCreateId(TokenInformation.getCreateid());  //团队id
                }

            }
        } else {
            throw new GlobalException("是否全选字段不能为空");
        }
//        修改结束时间为一天的结束
        if (!ObjectUtils.isEmpty(exportReminder.getCreateTime2())) {
            DateTime dateTime = DateUtil.endOfDay(exportReminder.getCreateTime2());  //一天的结束，结果：2017-03-01 23:59:59
            exportReminder.setCreateTime2(dateTime);
        }
        if (!ObjectUtils.isEmpty(exportReminder.getClientOverdueStart2())) {
            DateTime dateTime1 = DateUtil.endOfDay(exportReminder.getClientOverdueStart2());
            exportReminder.setClientOverdueStart2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(exportReminder.getEntrustingCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(exportReminder.getEntrustingCaseDate2());
            exportReminder.setEntrustingCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(exportReminder.getReturnCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(exportReminder.getReturnCaseDate2());
            exportReminder.setReturnCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(exportReminder.getAllocatedTime2())) {
            DateTime dateTime1 = DateUtil.endOfDay(exportReminder.getAllocatedTime2());
            exportReminder.setAllocatedTime2(dateTime1);
        }

//        字符串转数组集合形式
        exportReminder.setCaseIds(caseService.splitCharacterLong(exportReminder.getCaseId()));
        exportReminder.setClientNames(caseService.splitCharacterString(exportReminder.getClientName()));
        exportReminder.setClientPhones(caseService.splitCharacterString(exportReminder.getClientPhone()));
        exportReminder.setClientIdcards(caseService.splitCharacterString(exportReminder.getClientIdcard()));
        exportReminder.setEntrustingCaseBatchNums(caseService.splitCharacterString(exportReminder.getEntrustingCaseBatchNum()));
        exportReminder.setEntrustingPartyIds(caseService.splitCharacterLong(exportReminder.getEntrustingPartyId()));
        exportReminder.setFollowUpStates(caseService.splitCharacterString(exportReminder.getFollowUpState()));
        exportReminder.setUrgeStates(caseService.splitCharacterString(exportReminder.getUrgeState()));
        exportReminder.setClientCensusRegisters(caseService.splitCharacterString(exportReminder.getClientCensusRegister()));
        exportReminder.setClientCensusRegister(null);
        exportReminder.setOdvIds(caseService.splitCharacterLong(exportReminder.getOdvId()));
        exportReminder.setAreas(caseService.splitCharacterString(exportReminder.getArea()));
        exportReminder.setLabels(caseService.splitCharacterString(exportReminder.getLabel()));

//        if (!ObjectUtils.isEmpty(exportReminder.getClientCensusRegisters())) {
//            if (exportReminder.getClientCensusRegisters().size() > 1) {
//                List<String> list = new ArrayList<>();
//                for (String row : exportReminder.getClientCensusRegisters()) {
//                    String encrypt = FieldEncryptUtil.encrypt(row);
//                    list.add(encrypt);
//                }
//                exportReminder.setClientCensusRegisters(list);
//            } else {
//                exportReminder.setClientCensusRegistersEns(FieldEncryptUtil.censusRegisterLikeSelect(exportReminder.getClientCensusRegisters().get(0)));
//                exportReminder.setClientCensusRegisters(null);
//            }
//        }

        /*if (!ObjectUtils.isEmpty(exportReminder.getClientNames())) {
            if (exportReminder.getClientNames().size() > 1) { //不模糊查询
                List<String> list = new ArrayList<>();
                for (String row : exportReminder.getClientNames()) {
                    String encrypt = FieldEncryptUtil.encrypt(row);
                    list.add(encrypt);
                }
                exportReminder.setClientNames(list);
            } else { //模糊查询
                exportReminder.setClientNamesEns(FieldEncryptUtil.nameLikeSelect(exportReminder.getClientNames().get(0)));
                exportReminder.setClientNames(null);
            }
        }*/
//        if (!ObjectUtils.isEmpty(exportReminder.getClientIdcards())) {
//            if (exportReminder.getClientIdcards().size() > 1) { //不模糊查询
//                List<String> list = new ArrayList<>();
//                for (String row : exportReminder.getClientIdcards()) {
//                    String encrypt = FieldEncryptUtil.encrypt(row);
//                    list.add(encrypt);
//                }
//                exportReminder.setClientIdcards(list);
//            } else { //模糊查询
//                exportReminder.setClientIdcardsEns(FieldEncryptUtil.idLikeSelect(exportReminder.getClientIdcards().get(0)));
//                exportReminder.setClientIdcards(null);
//            }
//        }
        /*
        if (!ObjectUtils.isEmpty(exportReminder.getClientIdcards())) {
            if (exportReminder.getClientIdcards().size() > 1) { //不模糊查询
                List<String> list = new ArrayList<>();
                for (String row : exportReminder.getClientIdcards()) {
                    String encrypt = FieldEncryptUtil.encrypt(row);
                    list.add(encrypt);
                }
                exportReminder.setClientIdcards(list);
            } else { //模糊查询
                String str = exportReminder.getClientIdcards().get(0);
                if (str.length() == 18) {
                    List<String> list = new ArrayList<>();
                    list.add(FieldEncryptUtil.encrypt(str));
                    exportReminder.setClientIdcards(list);
                    exportReminder.setClientIdcardsEns(null);
                } else {
                    exportReminder.setClientIdcardsEns(FieldEncryptUtil.idLikeSelect(str));
                    exportReminder.setClientIdcards(null);
                }
            }
        }
        */
//        if (!ObjectUtils.isEmpty(exportReminder.getClientPhones())) {
//            if (exportReminder.getClientPhones().size() > 1) { //不模糊查询
//                List<String> list = new ArrayList<>();
//                for (String row : exportReminder.getClientPhones()) {
//                    String encrypt = FieldEncryptUtil.encrypt(row);
//                    list.add(encrypt);
//                }
//                exportReminder.setClientPhones(list);
//            } else { //模糊查询
//                exportReminder.setClientPhonesEns(FieldEncryptUtil.phoneLikeSelect(exportReminder.getClientPhones().get(0)));
//                exportReminder.setClientPhones(null);
//            }
//        }

/*
        if (!ObjectUtils.isEmpty(exportReminder.getClientPhones())) {
            if (exportReminder.getClientPhones().size() > 1) { //不模糊查询
                List<String> list = new ArrayList<>();
                for (String row : exportReminder.getClientPhones()) {
                    String encrypt = FieldEncryptUtil.encrypt(row);
                    list.add(encrypt);
                }
                exportReminder.setClientPhones(list);
            } else { //模糊查询
                String str = exportReminder.getClientPhones().get(0);
                if (str.length() == 11) {
                    List<String> list = new ArrayList<>();
                    list.add(FieldEncryptUtil.encrypt(str));
                    exportReminder.setClientPhones(list);
                    exportReminder.setClientPhonesEns(null);
                } else {
                    exportReminder.setClientPhonesEns(FieldEncryptUtil.phoneLikeSelect(str));
                    exportReminder.setClientPhones(null);
                }
            }
        }
*/

//        未跟进天数转换成日期
        if (!ObjectUtils.isEmpty(exportReminder.getNotFollowed1())) {
            Date date = new Date();
            Integer notFollowed1 = exportReminder.getNotFollowed1();
            DateTime dateTime1 = DateUtil.offsetDay(date, 0 - notFollowed1);
            exportReminder.setFollowUpAst2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(exportReminder.getNotFollowed2())) {
            Date date = new Date();
            Integer notFollowed2 = exportReminder.getNotFollowed2();
            DateTime dateTime1 = DateUtil.offsetDay(date, 0 - notFollowed2);
            exportReminder.setFollowUpAst1(dateTime1);
        }
        return exportReminder;
    }

    /**
     * 案件管理查询条件处理工具
     *
     * @param exportReminder
     * @return
     */
    public ExportReminder conditionalProcessing(ExportReminder exportReminder) {
        exportReminder.setCreateId(TokenInformation.getCreateid());  //团队id
//        修改结束时间为一天的结束
        if (!ObjectUtils.isEmpty(exportReminder.getCreateTime2())) {
            DateTime dateTime = DateUtil.endOfDay(exportReminder.getCreateTime2());  //一天的结束，结果：2017-03-01 23:59:59
            exportReminder.setCreateTime2(dateTime);
        }
        if (!ObjectUtils.isEmpty(exportReminder.getClientOverdueStart2())) {
            DateTime dateTime1 = DateUtil.endOfDay(exportReminder.getClientOverdueStart2());
            exportReminder.setClientOverdueStart2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(exportReminder.getEntrustingCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(exportReminder.getEntrustingCaseDate2());
            exportReminder.setEntrustingCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(exportReminder.getReturnCaseDate2())) {
            DateTime dateTime1 = DateUtil.endOfDay(exportReminder.getReturnCaseDate2());
            exportReminder.setReturnCaseDate2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(exportReminder.getAllocatedTime2())) {
            DateTime dateTime1 = DateUtil.endOfDay(exportReminder.getAllocatedTime2());
            exportReminder.setAllocatedTime2(dateTime1);
        }

//        字符串转数组集合形式
        exportReminder.setCaseIds(caseService.splitCharacterLong(exportReminder.getCaseId()));
        exportReminder.setClientNames(caseService.splitCharacterString(exportReminder.getClientName()));
        exportReminder.setClientPhones(caseService.splitCharacterString(exportReminder.getClientPhone()));
        exportReminder.setClientIdcards(caseService.splitCharacterString(exportReminder.getClientIdcard()));
        exportReminder.setEntrustingCaseBatchNums(caseService.splitCharacterString(exportReminder.getEntrustingCaseBatchNum()));
        exportReminder.setEntrustingPartyIds(caseService.splitCharacterLong(exportReminder.getEntrustingPartyId()));
        exportReminder.setFollowUpStates(caseService.splitCharacterString(exportReminder.getFollowUpState()));
        exportReminder.setUrgeStates(caseService.splitCharacterString(exportReminder.getUrgeState()));
        exportReminder.setClientCensusRegisters(caseService.splitCharacterString(exportReminder.getClientCensusRegister()));
        exportReminder.setClientCensusRegister(null);
        exportReminder.setOdvIds(caseService.splitCharacterLong(exportReminder.getOdvId()));
        exportReminder.setAreas(caseService.splitCharacterString(exportReminder.getArea()));
        exportReminder.setLabels(caseService.splitCharacterString(exportReminder.getLabel()));

//        未跟进天数转换成日期
        if (!ObjectUtils.isEmpty(exportReminder.getNotFollowed1())) {
            Date date = new Date();
            Integer notFollowed1 = exportReminder.getNotFollowed1();
            DateTime dateTime1 = DateUtil.offsetDay(date, 0 - notFollowed1);
            exportReminder.setFollowUpAst2(dateTime1);
        }
        if (!ObjectUtils.isEmpty(exportReminder.getNotFollowed2())) {
            Date date = new Date();
            Integer notFollowed2 = exportReminder.getNotFollowed2();
            DateTime dateTime1 = DateUtil.offsetDay(date, 0 - notFollowed2);
            exportReminder.setFollowUpAst1(dateTime1);
        }
        return exportReminder;
    }

    /*
     */
/**
 * 字符串转Long集合工具方法
 *
 * @param msg
 * @return
 *//*

    public List<Long> splitCharacterLong(String msg) {
        if (!ObjectUtils.isEmpty(msg)) {
            List<Long> list = new ArrayList<>();
            String[] split = msg.split(";");
            for (String splits : split) {
                list.add(new Long(splits));
            }
            return list;
        }
        return null;
    }

    */
/**
 * 字符串转String集合工具方法
 *
 * @param msg
 * @return
 *//*

    public List<String> splitCharacterString(String msg) {
        if (!ObjectUtils.isEmpty(msg)) {
            List<String> list = new ArrayList<>();
            String[] split = msg.split(";");
            for (String splits : split) {
                list.add(splits);
            }
            return list;
        }
        return null;
    }
*/

}
