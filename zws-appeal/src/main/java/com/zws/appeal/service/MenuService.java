package com.zws.appeal.service;


import com.zws.appeal.domain.Menu;
import com.zws.appeal.domain.TeamMenuTemplate;
import com.zws.common.core.enums.TeamLevelTypeEnum;
import com.zws.appeal.pojo.TreeSelect;
import com.zws.appeal.pojo.vo.RouterVo;

import java.util.List;
import java.util.Set;

public interface MenuService {

//    /**
//     * 菜单信息全查/返回路由菜单
//     * @return
//     */
//    List<Menu> selectMenu(Menu menu);

    /**
     * 根据用户查询系统菜单列表
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<Menu> selectMenuList(Long userId);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    public List<Long> selectMenuListByRoleId(Long roleId);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildMenuTreeSelect(List<Menu> menus);

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    public List<Menu> buildMenuTree(List<Menu> menus);

    /**
     * 根据用户查询系统菜单列表
     *
     * @param menu   菜单信息
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<Menu> selectMenuLists(Menu menu, Long userId);

    /**
     * 根据用户ID查询菜单树信息
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<Menu> selectMenuTreeByUserId(Long userId);

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @param createId 机构ID
     * @return 结果 true 存在 false 不存在
     */
    boolean hasChildByMenuId(Long menuId, Integer createId);

    /**
     * 查询菜单是否存在角色
     *
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    boolean checkMenuExistRole(Long menuId);

    /**
     * 校验菜单名称是否唯一
     *
     * @param menu 菜单信息
     * @return 结果
     */
    String checkMenuNameUnique(Menu menu);

    /**
     * 菜单信息全查/根据id查询/根据名称查询/根据状态查询（返回菜单格式）
     *
     * @return
     */
    List<Menu> selectMenus(Menu menu);

    /**
     * 新增菜单信息
     *
     * @param menu
     * @return
     */
    int insertMenu(Menu menu);

    /**
     * 修改菜单信息
     *
     * @param menu
     * @return
     */
    int updateMenu(Menu menu);

    /**
     * 删除菜单信息
     *
     * @param id
     * @param createId
     * @return
     */
    int deleteMenu(int id, Integer createId);

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> selectMenuPermsByUserId(Long userId);

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    List<RouterVo> buildMenus(List<Menu> menus);

    /**
     * 根据路由地址查询路由是否存在
     *
     * @param path 路由地址
     * @return 权限列表
     */
    List<String> selectMenuPermsPath(String path);

    /**
     * 给指定机构从模版新增默认菜单
     *
     * @param createId
     */
    void insertMenusByTemplate(Integer createId, TeamLevelTypeEnum teamLevelTypeEnum);

    /**
     * 菜单信息全查/根据id查询/根据名称查询/根据状态查询（返回菜单格式）
     * @return
     */
    List<TeamMenuTemplate> selectMenuTemplate(TeamMenuTemplate menuTemplate);

    /**
     * 校验菜单名称是否唯一
     *
     * @param menuTemplate 菜单信息
     * @return 结果
     */
    String checkTemplateMenuNameUnique(TeamMenuTemplate menuTemplate);

    /**
     * 新增菜单信息
     * @param menuTemplate
     * @return
     */
    int insertMenuTemplate(TeamMenuTemplate menuTemplate);

    /**
     * 修改菜单信息
     * @param menuTemplate
     * @return
     */
    int updateMenuTemplate(TeamMenuTemplate menuTemplate);

    /**
     * 是否存在菜单子节点
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    boolean hasChildByMenuIdTemplate(Long menuId);

    /**
     * 删除菜单信息
     * @param id
     * @return
     */
    int deleteMenuTemplate(Long id);

    /**
     * 校验模版菜单权限标识是否唯一
     * @param menuTemplate
     * @return
     */
    String checkTemplateMenuPermsUnique(TeamMenuTemplate menuTemplate);

    /**
     * 校验菜单权限标识是否唯一
     * @param menu
     * @return
     */
    String checkMenuPermsUnique(Menu menu);
}
