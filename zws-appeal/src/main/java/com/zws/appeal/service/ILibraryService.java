package com.zws.appeal.service;


import com.zws.appeal.domain.Library;

import java.util.List;

/**
 * 资产案件库 service
 *
 * <AUTHOR>
 * @date ：Created in 2022/2/15 14:41
 */
public interface ILibraryService {

    /**
     * 插入数据返回id
     *
     * @param entity
     * @return
     */
    long insert(Library entity);

    /**
     * 更新
     *
     * @param entity
     */
    void update(Library entity);

    /**
     * 更新案件分配状态为已分配
     *
     * @param caseIds
     */
    void updateAllocateCaseState(List<Long> caseIds);

    /**
     * 更新案件状态为 资产未分配
     *
     * @param caseIds
     */
    void updateUnAllocateCaseState(List<Long> caseIds);

    /**
     * 跟进资产管理id删除案件
     *
     * @param assetManageId
     */
    void deleteByAssetManageId(Long assetManageId);

    /**
     * 查询案件是否删除
     * <p>
     * <p>
     * <p>
     * List<Library> selectListByCaseId(Long caseId);
     * <p>
     * List<Library> selectListByCaseId(List<Long> caseIds);
     * <p>
     * <p>
     * <p>
     * /**
     * 根据案件id查询证件号码
     *
     * @param caseId
     * @return
     */
    String selectIdentificationNumber(Long caseId);

    /**
     * 根据证件号码查询共债案件id
     *
     * @param clientIdNum
     * @return
     */
    List<Long> selectCaseId(String clientIdNum);


}
