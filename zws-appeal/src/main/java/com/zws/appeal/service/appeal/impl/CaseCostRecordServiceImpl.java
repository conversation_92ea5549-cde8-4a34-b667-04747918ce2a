package com.zws.appeal.service.appeal.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.util.StringUtil;
import com.zws.appeal.agservice.AgLawsuitService;
import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.domain.appeal.CaseCostRecord;
import com.zws.appeal.mapper.appeal.CaseCostRecordMapper;
import com.zws.appeal.mapper.appeal.CaseManageMapper;
import com.zws.appeal.service.CollectionService;
import com.zws.appeal.service.appeal.ICaseCostRecordService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.enums.TimeContentFormats;
import com.zws.common.core.enums.approval.WebSideEnum;
import com.zws.common.core.utils.HistoryPowerUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 案件-收费记录
 * <AUTHOR>
 * @date ：Created in 2023/12/4 17:59
 */
@Service
public class CaseCostRecordServiceImpl implements ICaseCostRecordService {

    @Autowired
    private CaseCostRecordMapper baseMapper;
    @Autowired
    private CaseManageMapper caseManageMapper;
    @Autowired
    private CollectionService collectionService;
    @Autowired
    private AgLawsuitService agLawsuitService;

    @Override
    public Long insert(CaseCostRecord record) {
        Long caseId=record.getCaseId();
        String entrustingCaseBatchNum = caseManageMapper.getEntrustingCaseBatchNumByCaseId(caseId);

        record.setTeamId(Integer.valueOf(SecurityUtils.getTeamId().toString()));
        record.setOperationType(SecurityUtils.getAccountType());
        record.setEntrustingCaseBatchNum(entrustingCaseBatchNum);

        record.setId(null);
        record.setCreateBy(SecurityUtils.getUsername());
        record.setCreateById(SecurityUtils.getUserId());
        record.setCreateTime(new Date());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        record.setWebSide(WebSideEnum.APPEAL.getCode());
        baseMapper.insert(record);
        return record.getId();
    }

    @Override
    public void updateById(CaseCostRecord record) {
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        record.setCaseId(null);
        record.setTeamId(null);
        record.setCreateBy(null);
        record.setCreateById(null);
        record.setCreateTime(null);
        record.setWebSide(WebSideEnum.APPEAL.getCode());
        this.baseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void deleteById(Long id) {
        CaseCostRecord record=new CaseCostRecord();
        record.setId(id);
        record.setDelFlag(BaseConstant.DelFlag_Delete);
        updateById(record);
    }

    @Override
    public CaseCostRecord getById(Long id) {
        return this.baseMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<CaseCostRecord> selectList(CaseCostRecord record) {
//        record.setWebSide(WebSideEnum.APPEAL.getCode());
        Map<String, Object> map = BeanUtil.beanToMap(record);
        CaseManage caseManage1 = collectionService.selectCaseManageId(record.getCaseId());
        Map<String, Object> map1 = HistoryPowerUtils.handleTsHistoryPower(caseManage1.getEntrustingCaseDate(),caseManage1.getHistoryPower(), TokenInformation.getCreateid().longValue(),record.getWebSide());
        if (map1.size()>0){map.putAll(map1);}
        // 查询列表数据是否需要创建时间
        map.remove("createTime");
        return this.baseMapper.selectList(map);
    }

    @Override
    public void insertList(CaseCostRecord record) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (!record.getCondition()) {
            List<Long> caseIds = record.getCaseIds();
            for (Long caseId : caseIds) {
                String entrustingCaseBatchNum = caseManageMapper.getEntrustingCaseBatchNumByCaseId(caseId);

                record.setTeamId(Integer.valueOf(SecurityUtils.getTeamId().toString()));
                record.setOperationType(SecurityUtils.getAccountType());
                record.setEntrustingCaseBatchNum(entrustingCaseBatchNum);

                record.setId(null);
                record.setCaseId(caseId);
                record.setCreateBy(SecurityUtils.getUsername());
                record.setCreateById(SecurityUtils.getUserId());
                record.setCreateTime(new Date());
                record.setDelFlag(BaseConstant.DelFlag_Being);
                record.setUpdateBy(SecurityUtils.getUsername());
                record.setUpdateById(SecurityUtils.getUserId());
                record.setUpdateTime(new Date());
                record.setWebSide(WebSideEnum.APPEAL.getCode());
                baseMapper.insert(record);
                //判断如果是开庭费更新案件的阶段
                if ("开庭费".equals(record.getCostType())) {
                    baseMapper.updateCaseStage(record.getCaseId());
                }
            }
            agLawsuitService.addTimeManage(loginUser,caseIds, TimeContentFormats.PAYMENT);
        } else {
            //搜索结果全选取出该调诉员处于代缴费的案件id
            Long usrId = SecurityUtils.getUserId();
            List<Long> caseIds = baseMapper.getAllCaseIds(usrId);
            for (Long caseId : caseIds) {
                String entrustingCaseBatchNum = caseManageMapper.getEntrustingCaseBatchNumByCaseId(caseId);

                record.setTeamId(Integer.valueOf(SecurityUtils.getTeamId().toString()));
                record.setOperationType(SecurityUtils.getAccountType());
                record.setEntrustingCaseBatchNum(entrustingCaseBatchNum);

                record.setId(null);
                record.setCaseId(caseId);
                record.setCreateBy(SecurityUtils.getUsername());
                record.setCreateById(SecurityUtils.getUserId());
                record.setCreateTime(new Date());
                record.setDelFlag(BaseConstant.DelFlag_Being);
                record.setUpdateBy(SecurityUtils.getUsername());
                record.setUpdateById(SecurityUtils.getUserId());
                record.setUpdateTime(new Date());
                record.setWebSide(WebSideEnum.APPEAL.getCode());
                baseMapper.insert(record);
                //判断如果是开庭费更新案件的阶段
                if ("开庭费".equals(record.getCostType())) {
                    baseMapper.updateCaseStage(record.getCaseId());
                }
            }
            agLawsuitService.addTimeManage(loginUser,caseIds, TimeContentFormats.PAYMENT);
        }
    }
}
