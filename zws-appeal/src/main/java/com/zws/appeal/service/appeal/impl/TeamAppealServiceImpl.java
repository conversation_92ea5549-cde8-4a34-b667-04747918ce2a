package com.zws.appeal.service.appeal.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.zws.appeal.agservice.DesensitizationAgService;
import com.zws.appeal.controller.request.MaterialDeliveryRequest;
import com.zws.appeal.controller.response.MaterialDeliveryCountResp;
import com.zws.appeal.controller.response.*;
import com.zws.appeal.domain.DeliveryRanking;
import com.zws.appeal.domain.Dept;
import com.zws.appeal.domain.Desensitization;
import com.zws.appeal.domain.Employees;
import com.zws.appeal.enums.InfoSecurityEnum;
import com.zws.appeal.mapper.appeal.CourtSessionMapper;
import com.zws.appeal.mapper.appeal.TeamAppealMapper;
import com.zws.appeal.pojo.Option;
import com.zws.appeal.pojo.Option;
import com.zws.appeal.pojo.StateDesensitization;
import com.zws.appeal.pojo.TreeType;
import com.zws.appeal.pojo.appeal.*;
import com.zws.appeal.pojo.staticConst.StaticConstantClass;
import com.zws.appeal.service.appeal.ITeamAppealService;
import com.zws.appeal.utils.DataMaskingUtils;
import com.zws.appeal.utils.DesensitizationRedis;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.enums.approval.WebSideEnum;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.*;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class TeamAppealServiceImpl implements ITeamAppealService {
    @Autowired
    private TeamAppealMapper teamAppealMapper;
    @Autowired
    private DesensitizationAgService desensitizationAgService;
    @Autowired
    private DesensitizationRedis desensitizationRedis;
    @Override
    public List<TreeType> DeptTreeType(PhoneMediationPojo phoneMediationPojo) {
        Integer departmentId = 0;
        List<String> smallStageList = phoneMediationPojo.getSmallStageList();

        LoginUser loginUser = SecurityUtils.getLoginUser();

        List<Integer> list;
        if (ObjectUtils.isEmpty(phoneMediationPojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(phoneMediationPojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(phoneMediationPojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(phoneMediationPojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(phoneMediationPojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        //根据团队id查询该团队所有部门信息
        List<Dept> deptList = teamAppealMapper.selectMediationDept(TokenInformation.getCreateid(),phoneMediationPojo.getSmallStageList(),list);

        List<TreeType> treeTypes = new ArrayList<>();

        for (Dept dept : deptList) {
            //如果为顶级部门
            if (dept.getParentId().equals(0) || dept.getId().equals(departmentId)) {
                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept.getId());
                treeType.setName(dept.getDeptName());
                treeType.setCaseNum(dept.getCaseNum());
                Boolean aBoolean = ChildNode(dept.getId());
                if (aBoolean) {
                    List<TreeType> listr = listr(deptList, dept,smallStageList);
                    treeType.setChildren(listr);
                } else {
                    List<Employees> employeess = teamAppealMapper.selectEmployeesWithMediation(dept.getId(), TokenInformation.getCreateid(),phoneMediationPojo.getSmallStageList());
                    List<TreeType> arrlist = new ArrayList<>();
                    for (Employees employees1 : employeess) {
                        TreeType treeType1 = new TreeType();
                        treeType1.setId(employees1.getId().toString());
                        treeType1.setName(employees1.getEmployeeName());
                        treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
                        treeType1.setCaseNum(employees1.getCaseNum());
                        arrlist.add(treeType1);
                    }
                    treeType.setChildren(arrlist);
                }
                treeTypes.add(treeType);
            }
        }

        return treeTypes;
    }

    @Override
    public List<DeliveryRanking> materialDeliveryRanking() {
        Integer departmentId = 0;
        //根据团队id查询该团队所有部门信息
        Integer createid ;
        if (SecurityUtils.getAccountType() == UserConstants.ACCOUNT_TYPE_2) {
            createid = null;
        }else{
            createid = TokenInformation.getCreateid();
        }
        //todo:
        LoginUser loginUser = SecurityUtils.getLoginUser();


        List<Integer> list = null;
            //员工账号
        if (TokenInformation.getType(loginUser) == 1) {
            if (ObjectUtils.isEmpty(SecurityUtils.getDeptId())) {
                list = Encapsulation(loginUser);
            } else {
                list = Encapsulations(SecurityUtils.getDeptId(), loginUser);
            }
        } else {
            if (ObjectUtils.isEmpty(SecurityUtils.getDeptId())) {
                list = null;
            } else {
                list = Encapsulations(SecurityUtils.getDeptId(), loginUser);
            }
        }
        List<Dept> deptList = teamAppealMapper.selectDeptByDelivery(createid,list);

        List<DeliveryRanking> treeTypes = new ArrayList<>();

        for (Dept dept : deptList) {
            //如果为顶级部门
            if (dept.getParentId().equals(0) || dept.getId().equals(departmentId)) {
                DeliveryRanking treeType = new DeliveryRanking();
                treeType.setId("Dept:" + dept.getId());
                treeType.setName(dept.getDeptName());
                treeType.setCaseNum(dept.getCaseNum());
                Boolean aBoolean = ChildNode(dept.getId());
                if (aBoolean) {
                    List<DeliveryRanking> listr = deliveryRankingList(deptList, dept);
                    treeType.setChildren(listr);
                } else {
                    List<Employees> employeess = teamAppealMapper.selectEmployeesByDelivery(dept.getId(), TokenInformation.getCreateid());
                    List<DeliveryRanking> arrlist = new ArrayList<>();
                    for (Employees employees1 : employeess) {
                        DeliveryRanking treeType1 = new DeliveryRanking();
                        treeType1.setId(employees1.getId().toString());
                        treeType1.setName(employees1.getEmployeeName());
                        treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
                        treeType1.setCaseNum(employees1.getCaseNum());
                        arrlist.add(treeType1);
                    }
                    treeType.setChildren(arrlist);
                }
                treeTypes.add(treeType);
            }
        }

        return treeTypes;
    }

    @Override
    public List<PhoneMediationPojo> getPhoneMediation(PhoneMediationPojo phoneMediationPojo , LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(phoneMediationPojo.getStringDeptId())) {
            int dept = phoneMediationPojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = phoneMediationPojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    phoneMediationPojo.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(phoneMediationPojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    phoneMediationPojo.setUserId(Integer.parseInt(phoneMediationPojo.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(phoneMediationPojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(phoneMediationPojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(phoneMediationPojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(phoneMediationPojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(phoneMediationPojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }

        PageUtils.startPage();
        phoneMediationPojo.setDeptIds(list);
        phoneMediationPojo.setDeptId(null);
        phoneMediationPojo.setTeamId(SecurityUtils.getTeamId());
        phoneMediationPojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        List<PhoneMediationPojo> list1 = teamAppealMapper.getPhoneMediation(phoneMediationPojo);
        //解密
        for (PhoneMediationPojo mediationPojo : list1) {
            mediationPojo.setClientName(FieldEncryptUtil.decrypt(mediationPojo.getClientName()));
            mediationPojo.setClientPhone(FieldEncryptUtil.decrypt(mediationPojo.getClientPhone()));
            mediationPojo.setClientCensusRegister(FieldEncryptUtil.decrypt(mediationPojo.getClientCensusRegister()));
            mediationPojo.setClientIdNum(FieldEncryptUtil.decrypt(mediationPojo.getClientIdNum()));
        }
        return list1;
    }

    @Override
    public List<FilingCasePojo> selectFillingList(FilingCasePojo pojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(pojo.getStringDeptId())) {
            int dept = pojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = pojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    pojo.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(pojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    pojo.setUserId(Integer.parseInt(pojo.getStringDeptId()));
                }
            }
        }

//        List<Integer> list;
//                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
//                    list = null;
//                } else {
//                    list = Encapsulations(pojo.getDeptId(), loginUser);
//                }

        List<Integer> list;
        if (ObjectUtils.isEmpty(pojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }

        PageUtils.startPage();
        pojo.setDeptIds(list);
        pojo.setDeptId(null);
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        List<FilingCasePojo> list1 = teamAppealMapper.selectFillingList(pojo);
        //解密
        for (FilingCasePojo mediationPojo : list1) {
            mediationPojo.setClientName(FieldEncryptUtil.decrypt(mediationPojo.getClientName()));
            mediationPojo.setClientCensusRegister(FieldEncryptUtil.decrypt(mediationPojo.getClientCensusRegister()));
            mediationPojo.setClientIdcard(FieldEncryptUtil.decrypt(mediationPojo.getClientIdcard()));
        }
        return list1;
    }

    @Override
    public Map<String, Object> selectWithMoney(FilingCasePojo pojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(pojo.getStringDeptId())) {
            int dept = pojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = pojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    pojo.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(pojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    pojo.setUserId(Integer.parseInt(pojo.getStringDeptId()));
                }
            }
        }

//        List<Integer> list;
//                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
//                    list = null;
//                } else {
//                    list = Encapsulations(pojo.getDeptId(), loginUser);
//                }

        List<Integer> list;
        if (ObjectUtils.isEmpty(pojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }

        PageUtils.startPage();
        pojo.setDeptIds(list);
        pojo.setDeptId(null);
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        pojo.setOdvId(TokenInformation.getUserid());
        return teamAppealMapper.selectWithMoney(pojo);
    }

    @Override
    public List<LawInforPojo> getSessionList(LawInforPojo lawInforPojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(lawInforPojo.getStringDeptId())) {
            int dept = lawInforPojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = lawInforPojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    lawInforPojo.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(lawInforPojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    lawInforPojo.setUserId(Integer.parseInt(lawInforPojo.getStringDeptId()));
                }
            }
        }

//        List<Integer> list;
//                if (ObjectUtils.isEmpty(lawInforPojo.getDeptId())) {
//                    list = null;
//                } else {
//                    list = Encapsulations(lawInforPojo.getDeptId(), loginUser);
//                }
        List<Integer> list;
        if (ObjectUtils.isEmpty(lawInforPojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(lawInforPojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(lawInforPojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(lawInforPojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(lawInforPojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        lawInforPojo.setDeptIds(list);
        lawInforPojo.setDeptId(null);
        if (!ObjectUtils.isEmpty(lawInforPojo.getTrialTime2())) {
            Date dateTime = DateUtil.endOfDay(lawInforPojo.getTrialTime2());
            lawInforPojo.setTrialTime2(dateTime);
        }
        lawInforPojo.setTeamId(SecurityUtils.getTeamId());
        lawInforPojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        List<LawInforPojo> list1 = teamAppealMapper.getSessionList(lawInforPojo);
        //解密
        for (LawInforPojo lawInforPojo1 : list1) {
            lawInforPojo1.setClientName(FieldEncryptUtil.decrypt(lawInforPojo1.getClientName()));
            lawInforPojo1.setClientPhone(FieldEncryptUtil.decrypt(lawInforPojo1.getClientPhone()));
            lawInforPojo1.setClientCensusRegister(FieldEncryptUtil.decrypt(lawInforPojo1.getClientCensusRegister()));
            lawInforPojo1.setClientIdNum(FieldEncryptUtil.decrypt(lawInforPojo1.getClientIdNum()));
        }
        return list1;
    }

    @Override
    public List<FreezeCasePojo> selectFreezeList(FreezeCasePojo pojo, LoginUser loginUser) {
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        if (!ObjectUtils.isEmpty(pojo.getStringDeptId())) {
            int dept = pojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = pojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    pojo.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(pojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    pojo.setUserId(Integer.parseInt(pojo.getStringDeptId()));
                }
            }
        }

//        List<Integer> list;
//        List<Integer> ids = new ArrayList<>();
//        if (ObjectUtils.isEmpty(pojo.getDeptId())) {
//                    list = null;
//                } else {
//                    list = Encapsulations(pojo.getDeptId(), loginUser);
//                    ids = teamAppealMapper.selectUserIds(list);
//                }

        List<Integer> list;
        if (ObjectUtils.isEmpty(pojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.clearPage();
        List<Integer> ids = new ArrayList<>();
        ids = teamAppealMapper.selectUserIds(list);

        PageUtils.startPage();
        pojo.setDeptIds(list);
        pojo.setDeptId(null);
        pojo.setUserIds(ids);
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        List<String> smallStageList = pojo.getSmallStageList();
        if(!ObjectUtils.isEmpty(smallStageList)){
            for (String s : smallStageList) {
                if(s.equals("已缴纳保证金，待上传回执单")){
                    pojo.setSmallStage("已缴纳保证金,待上传回执单");
                }
            }
        }
        this.verification(pojo);
        List<FreezeCasePojo> list1 = teamAppealMapper.selectFreezeList(pojo);

        //解密
        for (FreezeCasePojo mediationPojo : list1) {
            mediationPojo.setClientName(FieldEncryptUtil.decrypt(mediationPojo.getClientName()));
            mediationPojo.setClientPhone(FieldEncryptUtil.decrypt(mediationPojo.getClientPhone()));
            mediationPojo.setClientCensusRegister(FieldEncryptUtil.decrypt(mediationPojo.getClientCensusRegister()));
            mediationPojo.setClientIdcard(FieldEncryptUtil.decrypt(mediationPojo.getClientIdcard()));
        }
        return list1;
    }

    @Override
    public List<MaterialDeliveryResp> selectMaterialDeliveryList(MaterialDeliveryRequest pojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(pojo.getStringDeptId())) {
            int dept = pojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = pojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    pojo.setDeptId(Long.parseLong(substring));
                }
            } else {
                if (!StringUtils.isNumeric(pojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    pojo.setUserId(Long.parseLong(pojo.getStringDeptId()));
                }
            }
        }

//        List<Integer> list;
//        if (ObjectUtils.isEmpty(pojo.getDeptId())) {
//            list = null;
//        } else {
//            list = Encapsulations(pojo.getDeptId().intValue(), loginUser);
//        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(pojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(Math.toIntExact(pojo.getDeptId()), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(Math.toIntExact(pojo.getDeptId()), loginUser);
                }
            }
        } else {
            list = null;
        }

        PageUtils.startPage();
        pojo.setDeptIds(list);
        pojo.setDeptId(null);
        //获取团队id 资产端账号不设置团队ID
        if (SecurityUtils.getAccountType(loginUser)!=2){
            pojo.setTeamId(SecurityUtils.getTeamId(loginUser));
        }
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        pojo.setWebSide(WebSideEnum.APPEAL.getCode());
        if (pojo.getType()!=null && pojo.getType()==0){
            pojo.setUserId(loginUser.getUserid());
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("0")){
            pojo.setSentStatus("15");
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("1")){
            pojo.setSentStatus("0");
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("3")){
            pojo.setSentStatus("14");
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("2")){
            pojo.setSentStatus("3");
        }
        if (TokenInformation.getType()==2){
            pojo.setWebSide(null);
        }
        List<MaterialDeliveryResp> list1 = teamAppealMapper.selectMaterialDeliveryList(pojo);
        //解密
        for (MaterialDeliveryResp mediationPojo : list1) {
            mediationPojo.setClientName(FieldEncryptUtil.decrypt(mediationPojo.getClientName()));
            mediationPojo.setClientCensusRegister(FieldEncryptUtil.decrypt(mediationPojo.getClientCensusRegister()));
            mediationPojo.setClientIdCard(FieldEncryptUtil.decrypt(mediationPojo.getClientIdCard()));
            //案件导入物流的时候就只有一个材料，联查文书函件表就是null
            if(mediationPojo.getSentCount()==null){
                mediationPojo.setSentCount("1");
            }
            if("5".equals(mediationPojo.getSentStatus())){
                mediationPojo.setSentStatus("0");
            }
        }
        return list1;
    }

   /* @Override
    public List<Option> getSum(LoginUser loginUser,MaterialDeliveryRequest pojo) {
        String[] str = new String[]{"已拒收","已送达","运输中","待寄送","全部"};
        List<String> infor = Arrays.asList(str);
        List<Option> list = new ArrayList<>();
        int codeSum = 0;
        List<MaterialDeliveryResp> list1 = new ArrayList<>();
        if (pojo.getType()!=null && pojo.getType()==0 && pojo.getStringDeptId()==null){
            pojo.setStringDeptId(null);
            pojo.setUserId(loginUser.getUserid());
        }
        if (pojo.getType()!=null && pojo.getType()==1 && pojo.getStringDeptId()==null){
            pojo.setStringDeptId(null);
        }
        for (String s : infor) {
            if ("已拒收".equals(s)){
                pojo.setSentStatus("3");
                list1 = selectMaterialDeliveryLists(pojo,loginUser);
                Option option = new Option();
                option.setInfo("已拒收");
                option.setCode(list1.size());
                codeSum += list1.size();
                list.add(option);
            }
            if ("已送达".equals(s)){
                pojo.setSentStatus("2");
                list1 = selectMaterialDeliveryLists(pojo,loginUser);
                Option option = new Option();
                option.setInfo("已送达");
                option.setCode(list1.size());
                codeSum += list1.size();
                list.add(option);
            }
            if ("运输中".equals(s)){
                pojo.setSentStatus("1");
                list1 = selectMaterialDeliveryLists(pojo,loginUser);
                Option option = new Option();
                option.setInfo("运输中");
                option.setCode(list1.size());
                codeSum += list1.size();
                list.add(option);
            }
            if ("待寄送".equals(s)){
                pojo.setSentStatus("0");
                list1 = selectMaterialDeliveryLists(pojo,loginUser);
                Option option = new Option();
                option.setInfo("待寄送");
                option.setCode(list1.size());
                codeSum += list1.size();
                list.add(option);
            }
            if("全部".equals(s)){
                list1 = selectMaterialDeliveryLists(pojo,loginUser);
                Option option = new Option();
                option.setInfo("全部");
                option.setCode(codeSum);
                list.add(option);
            }
        }
        return list;
    }
*/
  /*  public List<MaterialDeliveryResp> selectMaterialDeliveryLists(MaterialDeliveryRequest pojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(pojo.getStringDeptId())) {
            int dept = pojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = pojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    pojo.setDeptId(Long.parseLong(substring));
                }
            } else {
                if (!StringUtils.isNumeric(pojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    pojo.setUserId(Long.parseLong(pojo.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(pojo.getDeptId())) {
            list = null;
        } else {
            list = Encapsulations(pojo.getDeptId().intValue(), loginUser);
        }

        pojo.setDeptIds(list);
        pojo.setDeptId(null);
        //获取团队id
        if (SecurityUtils.getAccountType(loginUser)!=2){
            pojo.setTeamId(SecurityUtils.getTeamId(loginUser));
        }
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        pojo.setWebSide(WebSideEnum.APPEAL.getCode());
//        if (pojo.getType()!=null && pojo.getType()==0){
//            pojo.setUserId(loginUser.getUserid());
//        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("0")){
            pojo.setSentStatus("15");
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("1")){
            pojo.setSentStatus("0");
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("3")){
            pojo.setSentStatus("14");
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("2")){
            pojo.setSentStatus("3");
        }
        if (TokenInformation.getType()==2){
            pojo.setWebSide(null);
        }
        List<MaterialDeliveryResp> list1 = teamAppealMapper.selectMaterialDeliveryList(pojo);
        //解密
        //            进行数据脱敏
        boolean infoSecurity = desensitizationAgService.selectInfoSecurity(InfoSecurityEnum.ID_NUM);
        for (MaterialDeliveryResp mediationPojo : list1) {
            mediationPojo.setClientName(FieldEncryptUtil.decrypt(mediationPojo.getClientName()));
            mediationPojo.setClientCensusRegister(FieldEncryptUtil.decrypt(mediationPojo.getClientCensusRegister()));
            mediationPojo.setClientIdCard(FieldEncryptUtil.decrypt(mediationPojo.getClientIdCard()));
            //案件导入物流的时候就只有一个材料，联查文书函件表就是null
            if(mediationPojo.getSentCount()==null){
                mediationPojo.setSentCount("1");
            }
            if("5".equals(mediationPojo.getSentStatus())){
                mediationPojo.setSentStatus("0");
            }
            if (infoSecurity) {
                if (!ObjectUtils.isEmpty(mediationPojo.getClientIdCard())) {
                    mediationPojo.setClientIdCard(DataMaskingUtils.idMasking(mediationPojo.getClientIdCard()));
                }
            }
        }
        return list1;
    }
*/
    @Override
    public Map<String, Object> selectFreezeWithMoney(FreezeCasePojo pojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(pojo.getStringDeptId())) {
            int dept = pojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = pojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    pojo.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(pojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    pojo.setUserId(Integer.parseInt(pojo.getStringDeptId()));
                }
            }
        }

        //todo:
        List<Integer> list;
        List<Integer> ids = new ArrayList<>();
        if (ObjectUtils.isEmpty(pojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                    ids = teamAppealMapper.selectUserIds(list);
                }
            } else {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }

        PageUtils.startPage();
        pojo.setDeptIds(list);
        pojo.setDeptId(null);
        pojo.setUserIds(ids);
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        List<String> smallStageList = pojo.getSmallStageList();
        if(!ObjectUtils.isEmpty(smallStageList)){
            for (String s : smallStageList) {
                if(s.equals("已缴纳保证金，待上传回执单")){
                    pojo.setSmallStage("已缴纳保证金,待上传回执单");
                }
            }
        }
        if(!ObjectUtils.isEmpty(pojo.getSaveStage()) && pojo.getSaveStage().contains("已缴纳保证金")){
            pojo.setSaveStage("已缴纳保证金,待上传回执单");
        }
        //处理日期的查询范围
        this.verification(pojo);
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        return teamAppealMapper.selectFreezeWithMoney(pojo);
    }

    public void verification(FreezeCasePojo pojo) {
        if (!ObjectUtils.isEmpty(pojo.getStartFreeze())) {
            String startDate = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", pojo.getStartFreeze());
            Date date = DateUtils.parseDate(startDate);
            pojo.setStartFreeze(date);
        }

        //修改结束时间为一天的结束
        if (!ObjectUtils.isEmpty(pojo.getEndFreeze())) {
            String endDate = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", pojo.getEndFreeze());
            Date date = DateUtils.parseDate(endDate);
            pojo.setEndFreeze(date);

            DateTime dateTime = DateUtil.endOfDay(pojo.getEndFreeze());  //一天的结束，结果：2017-03-01 23:59:59
            pojo.setEndFreeze(dateTime);
        }
    }

    @Override
    public Map<String, Object> selectMediationWithMoney(PhoneMediationPojo phoneMediationPojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(phoneMediationPojo.getStringDeptId())) {
            int dept = phoneMediationPojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = phoneMediationPojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    phoneMediationPojo.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(phoneMediationPojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    phoneMediationPojo.setUserId(Integer.parseInt(phoneMediationPojo.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(phoneMediationPojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(phoneMediationPojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(phoneMediationPojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(phoneMediationPojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(phoneMediationPojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }

        PageUtils.startPage();
        phoneMediationPojo.setDeptIds(list);
        phoneMediationPojo.setDeptId(null);
        phoneMediationPojo.setTeamId(SecurityUtils.getTeamId());
        phoneMediationPojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        return teamAppealMapper.selectMediationWithMoney(phoneMediationPojo);
    }

    @Override
    public Map<String, Object> selectSessionWithMoney(LawInforPojo lawInforPojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(lawInforPojo.getStringDeptId())) {
            int dept = lawInforPojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = lawInforPojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    lawInforPojo.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(lawInforPojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    lawInforPojo.setUserId(Integer.parseInt(lawInforPojo.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(lawInforPojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(lawInforPojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(lawInforPojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(lawInforPojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(lawInforPojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        lawInforPojo.setDeptIds(list);
        lawInforPojo.setDeptId(null);
        if (!ObjectUtils.isEmpty(lawInforPojo.getTrialTime2())) {
            Date dateTime = DateUtil.endOfDay(lawInforPojo.getTrialTime2());
            lawInforPojo.setTrialTime2(dateTime);
        }
        lawInforPojo.setTeamId(SecurityUtils.getTeamId());
        lawInforPojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        return teamAppealMapper.selectSessionWithMoney(lawInforPojo);
    }

    @Override
    public List<JudgePojo> getJudgeList(JudgePojo pojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(pojo.getStringDeptId())) {
            int dept = pojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = pojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    pojo.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(pojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    pojo.setUserId(Integer.parseInt(pojo.getStringDeptId()));
                }
            }
        }

//        List<Integer> list;
//                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
//                    list = null;
//                } else {
//                    list = Encapsulations(pojo.getDeptId(), loginUser);
//                }
        List<Integer> list;
        if (ObjectUtils.isEmpty(pojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        pojo.setDeptIds(list);
        pojo.setDeptId(null);
        if (!ObjectUtils.isEmpty(pojo.getJudgeTime2())) {
            Date dateTime = DateUtil.endOfDay(pojo.getJudgeTime2());
            pojo.setJudgeTime2(dateTime);
        }
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        List<JudgePojo> list1 = teamAppealMapper.getJudgeList(pojo);
        //解密
        for (JudgePojo mediationPojo : list1) {
            mediationPojo.setClientName(FieldEncryptUtil.decrypt(mediationPojo.getClientName()));
            mediationPojo.setClientCensusRegister(FieldEncryptUtil.decrypt(mediationPojo.getClientCensusRegister()));
            mediationPojo.setClientIdNum(FieldEncryptUtil.decrypt(mediationPojo.getClientIdNum()));
        }
        return list1;
    }

    @Override
    public Map<String, Object> selectJudgeWithMoney(JudgePojo pojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(pojo.getStringDeptId())) {
            int dept = pojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = pojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    pojo.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(pojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    pojo.setUserId(Integer.parseInt(pojo.getStringDeptId()));
                }
            }
        }

//        List<Integer> list;
//                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
//                    list = null;
//                } else {
//                    list = Encapsulations(pojo.getDeptId(), loginUser);
//                }
        List<Integer> list;
        if (ObjectUtils.isEmpty(pojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        pojo.setDeptIds(list);
        pojo.setDeptId(null);
        if (!ObjectUtils.isEmpty(pojo.getJudgeTime2())) {
            Date dateTime = DateUtil.endOfDay(pojo.getJudgeTime2());
            pojo.setJudgeTime2(dateTime);
        }
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        return teamAppealMapper.selectJudgeWithMoney(pojo);
    }

    @Override
    public List<ExecuteCasePojo> selectExecuteList(ExecuteCasePojo pojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(pojo.getStringDeptId())) {
            int dept = pojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = pojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    pojo.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(pojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    pojo.setUserId(Integer.parseInt(pojo.getStringDeptId()));
                }
            }
        }

//        List<Integer> list;
//                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
//                    list = null;
//                } else {
//                    list = Encapsulations(pojo.getDeptId(), loginUser);
//                }

        List<Integer> list;
        if (ObjectUtils.isEmpty(pojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }

        PageUtils.startPage();
        pojo.setDeptIds(list);
        pojo.setDeptId(null);
        pojo.setTeamId(SecurityUtils.getTeamId());
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        List<ExecuteCasePojo> list1 = teamAppealMapper.selectExecuteList(pojo);
        //解密
        for (ExecuteCasePojo mediationPojo : list1) {
            mediationPojo.setClientName(FieldEncryptUtil.decrypt(mediationPojo.getClientName()));
            mediationPojo.setClientCensusRegister(FieldEncryptUtil.decrypt(mediationPojo.getClientCensusRegister()));
            mediationPojo.setClientIdcard(FieldEncryptUtil.decrypt(mediationPojo.getClientIdcard()));
        }
        return list1;
    }

    @Override
    public Map<String, Object> selectExecuteMoney(ExecuteCasePojo pojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(pojo.getStringDeptId())) {
            int dept = pojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = pojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    pojo.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(pojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    pojo.setUserId(Integer.parseInt(pojo.getStringDeptId()));
                }
            }
        }

        //todo:

        List<Integer> list;
        if (ObjectUtils.isEmpty(pojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(pojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }

        PageUtils.startPage();
        pojo.setDeptIds(list);
        pojo.setDeptId(null);
        pojo.setTeamId(SecurityUtils.getTeamId());
        return teamAppealMapper.selectExecuteMoney(pojo);
    }

    @Override
    public MaterialDeliveryCountResp selectMaterialDeliveryMoney(MaterialDeliveryRequest pojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(pojo.getStringDeptId())) {
            int dept = pojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = pojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    pojo.setDeptId(Long.parseLong(substring));
                }
            } else {
                if (!StringUtils.isNumeric(pojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    pojo.setUserId(Long.parseLong(pojo.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(pojo.getDeptId())) {
            list = null;
        } else {
            list = Encapsulations(pojo.getDeptId().intValue(), loginUser);
        }

        PageUtils.startPage();
        pojo.setDeptIds(list);
        pojo.setDeptId(null);
        //设置团队ID
        if (SecurityUtils.getAccountType(loginUser)!=2){
            pojo.setTeamId(SecurityUtils.getTeamId(loginUser));
        }
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        //子账号的查询限制
        if (pojo.getType()!=null&&pojo.getType()==0&&TokenInformation.getType()!=2){
            pojo.setUserId(loginUser.getUserid());
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("0")){
            pojo.setSentStatus("15");
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("1")){
            pojo.setSentStatus("0");
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("3")){
            pojo.setSentStatus("14");
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("2")){
            pojo.setSentStatus("3");
        }
        return teamAppealMapper.selectMaterialDeliveryMoney(pojo);
    }

    @Override
    public List<TreeType> DeptTreeWithDisposeStage(PhoneMediationPojo phoneMediationPojo) {
        Integer departmentId = 0;
        List<String> smallStageList = phoneMediationPojo.getSmallStageList();

        LoginUser loginUser = SecurityUtils.getLoginUser();

        List<Integer> list;
        if (ObjectUtils.isEmpty(phoneMediationPojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(phoneMediationPojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(phoneMediationPojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(phoneMediationPojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(phoneMediationPojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        //根据团队id查询该团队所有部门信息
        List<Dept> deptList = teamAppealMapper.selectDeptWithDisposeStage(TokenInformation.getCreateid(),smallStageList,list);
        List<TreeType> treeTypes = new ArrayList<>();

        for (Dept dept : deptList) {
            //如果为顶级部门
            if (dept.getParentId().equals(0) || dept.getId().equals(departmentId)) {
                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept.getId());
                treeType.setName(dept.getDeptName());
                treeType.setCaseNum(dept.getCaseNum());
                Boolean aBoolean = ChildNode(dept.getId());
                if (aBoolean) {
                    List<TreeType> listr = listrWithDisposeStage(deptList, dept,smallStageList);
                    treeType.setChildren(listr);
                } else {
                    List<Employees> employeess = teamAppealMapper.selectEmployeesWithDisposeStage(dept.getId(), TokenInformation.getCreateid(),smallStageList);
                    List<TreeType> arrlist = new ArrayList<>();
                    for (Employees employees1 : employeess) {
                        TreeType treeType1 = new TreeType();
                        treeType1.setId(employees1.getId().toString());
                        treeType1.setName(employees1.getEmployeeName());
                        treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
                        treeType1.setCaseNum(employees1.getCaseNum());
                        arrlist.add(treeType1);
                    }
                    treeType.setChildren(arrlist);
                }
                treeTypes.add(treeType);
            }
        }

        return treeTypes;
    }

    @Override
    public List<TreeType> DeptTreeWithSaveStage(PhoneMediationPojo phoneMediationPojo) {
        Integer departmentId = 0;
        List<String> smallStageList = phoneMediationPojo.getSmallStageList();
        if(!ObjectUtils.isEmpty(smallStageList)){
            for (String s : smallStageList) {
                if(s.equals("已缴纳保证金，待上传回执单")){
                    phoneMediationPojo.setSmallStage("已缴纳保证金,待上传回执单");
                }
            }
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();

        List<Integer> list;
        if (ObjectUtils.isEmpty(phoneMediationPojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(phoneMediationPojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(phoneMediationPojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(phoneMediationPojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(phoneMediationPojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }

        //根据团队id查询该团队所有部门信息
        List<Dept> deptList = teamAppealMapper.selectDeptWithSaveStage(TokenInformation.getCreateid(),smallStageList, phoneMediationPojo.getSmallStage(),list);

        List<TreeType> treeTypes = new ArrayList<>();

        for (Dept dept : deptList) {
            //如果为顶级部门
            if (dept.getParentId().equals(0) || dept.getId().equals(departmentId)) {
                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept.getId());
                treeType.setName(dept.getDeptName());
                treeType.setCaseNum(dept.getCaseNum());
                Boolean aBoolean = ChildNode(dept.getId());
                if (aBoolean) {
                    List<TreeType> listr = listrWithSaveStage(deptList, dept,smallStageList, phoneMediationPojo.getSmallStage());
                    treeType.setChildren(listr);
                } else {
                    List<Employees> employeess = teamAppealMapper.selectEmployeesWithSaveStage(dept.getId(), TokenInformation.getCreateid(),smallStageList, phoneMediationPojo.getSmallStage());
                    List<TreeType> arrlist = new ArrayList<>();
                    for (Employees employees1 : employeess) {
                        TreeType treeType1 = new TreeType();
                        treeType1.setId(employees1.getId().toString());
                        treeType1.setName(employees1.getEmployeeName());
                        treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
                        treeType1.setCaseNum(employees1.getCaseNum());
                        arrlist.add(treeType1);
                    }
                    treeType.setChildren(arrlist);
                }
                treeTypes.add(treeType);
            }
        }

        return treeTypes;
    }

    @Override
    public List<TreeType> DeptTreeWithExecute(PhoneMediationPojo phoneMediationPojo) {
        Integer departmentId = 0;
        List<String> smallStageList = phoneMediationPojo.getSmallStageList();
        //todo:
        LoginUser loginUser = SecurityUtils.getLoginUser();

        List<Integer> list;
        if (ObjectUtils.isEmpty(phoneMediationPojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(phoneMediationPojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(phoneMediationPojo.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(phoneMediationPojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(phoneMediationPojo.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        //根据团队id查询该团队所有部门信息
        List<Dept> deptList = teamAppealMapper.selectDeptWithExecute(TokenInformation.getCreateid(),smallStageList,list);
        List<TreeType> treeTypes = new ArrayList<>();

        for (Dept dept : deptList) {
            //如果为顶级部门
            if (dept.getParentId().equals(0) || dept.getId().equals(departmentId)) {
                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept.getId());
                treeType.setName(dept.getDeptName());
                treeType.setCaseNum(dept.getCaseNum());
                Boolean aBoolean = ChildNode(dept.getId());
                if (aBoolean) {
                    List<TreeType> listr = listrWithExecute(deptList, dept,smallStageList);
                    treeType.setChildren(listr);
                } else {
                    List<Employees> employeess = teamAppealMapper.selectEmployeesWithExecute(dept.getId(), TokenInformation.getCreateid(),smallStageList);
                    List<TreeType> arrlist = new ArrayList<>();
                    for (Employees employees1 : employeess) {
                        TreeType treeType1 = new TreeType();
                        treeType1.setId(employees1.getId().toString());
                        treeType1.setName(employees1.getEmployeeName());
                        treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
                        treeType1.setCaseNum(employees1.getCaseNum());
                        arrlist.add(treeType1);
                    }
                    treeType.setChildren(arrlist);
                }
                treeTypes.add(treeType);
            }
        }

        return treeTypes;
    }

    @Override
    public List<Option> getSum(LoginUser loginUser, MaterialDeliveryRequest pojo) {
        String[] str = new String[]{"已拒收","已送达","运输中","待寄送","全部"};
        List<String> infor = Arrays.asList(str);
        List<Option> list = new ArrayList<>();
        int codeSum = 0;
        List<MaterialDeliveryResp> list1 = new ArrayList<>();
        if (pojo.getType()!=null && pojo.getType()==0 && pojo.getStringDeptId()==null){
            pojo.setStringDeptId(null);
            pojo.setUserId(loginUser.getUserid());
        }
        if (pojo.getType()!=null && pojo.getType()==1 && pojo.getStringDeptId()==null){
            pojo.setStringDeptId(null);
        }
        for (String s : infor) {
            if ("已拒收".equals(s)){
                pojo.setSentStatus("3");
                list1 = selectMaterialDeliveryLists(pojo,loginUser);
                Option option = new Option();
                option.setInfo("已拒收");
                option.setCode(list1.size());
                codeSum += list1.size();
                list.add(option);
            }
            if ("已送达".equals(s)){
                pojo.setSentStatus("2");
                list1 = selectMaterialDeliveryLists(pojo,loginUser);
                Option option = new Option();
                option.setInfo("已送达");
                option.setCode(list1.size());
                codeSum += list1.size();
                list.add(option);
            }
            if ("运输中".equals(s)){
                pojo.setSentStatus("1");
                list1 = selectMaterialDeliveryLists(pojo,loginUser);
                Option option = new Option();
                option.setInfo("运输中");
                option.setCode(list1.size());
                codeSum += list1.size();
                list.add(option);
            }
            if ("待寄送".equals(s)){
                pojo.setSentStatus("0");
                list1 = selectMaterialDeliveryLists(pojo,loginUser);
                Option option = new Option();
                option.setInfo("待寄送");
                option.setCode(list1.size());
                codeSum += list1.size();
                list.add(option);
            }
            if("全部".equals(s)){
                list1 = selectMaterialDeliveryLists(pojo,loginUser);
                Option option = new Option();
                option.setInfo("全部");
                option.setCode(codeSum);
                list.add(option);
            }
        }
        return list;
    }

    public List<MaterialDeliveryResp> selectMaterialDeliveryLists(MaterialDeliveryRequest pojo, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(pojo.getStringDeptId())) {
            int dept = pojo.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = pojo.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    pojo.setDeptId(Long.parseLong(substring));
                }
            } else {
                if (!StringUtils.isNumeric(pojo.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    pojo.setUserId(Long.parseLong(pojo.getStringDeptId()));
                }
            }
        }

//        List<Integer> list;
//        if (ObjectUtils.isEmpty(pojo.getDeptId())) {
//            list = null;
//        } else {
//            list = Encapsulations(pojo.getDeptId().intValue(), loginUser);
//        }
        List<Integer> list;
        if (ObjectUtils.isEmpty(pojo.getUserId())) {
            //员工账号
            if (TokenInformation.getType(loginUser) == 1) {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(Math.toIntExact(pojo.getDeptId()), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(pojo.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(Math.toIntExact(pojo.getDeptId()), loginUser);
                }
            }
        } else {
            list = null;
        }

        pojo.setDeptIds(list);
        pojo.setDeptId(null);
        //获取团队id
        pojo.setTeamId(SecurityUtils.getTeamId());
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        pojo.setWebSide(WebSideEnum.APPEAL.getCode());
//        if (pojo.getType()!=null && pojo.getType()==0){
//            pojo.setUserId(loginUser.getUserid());
//        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("0")){
            pojo.setSentStatus("15");
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("1")){
            pojo.setSentStatus("0");
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("3")){
            pojo.setSentStatus("14");
        }
        if (pojo.getSentStatus()!=null && pojo.getSentStatus().equals("2")){
            pojo.setSentStatus("3");
        }
        if (TokenInformation.getType()==2){
            pojo.setWebSide(null);
        }
        List<MaterialDeliveryResp> list1 = teamAppealMapper.selectMaterialDeliveryList(pojo);
        //解密
        for (MaterialDeliveryResp mediationPojo : list1) {
            mediationPojo.setClientName(FieldEncryptUtil.decrypt(mediationPojo.getClientName()));
            mediationPojo.setClientCensusRegister(FieldEncryptUtil.decrypt(mediationPojo.getClientCensusRegister()));
            mediationPojo.setClientIdCard(FieldEncryptUtil.decrypt(mediationPojo.getClientIdCard()));
            //案件导入物流的时候就只有一个材料，联查文书函件表就是null
            if(mediationPojo.getSentCount()==null){
                mediationPojo.setSentCount("1");
            }
            if("5".equals(mediationPojo.getSentStatus())){
                mediationPojo.setSentStatus("0");
            }
        }
        return list1;
    }

    private List<TreeType> listrWithExecute(List<Dept> depts, Dept dept, List<String> smallStageList) {

        List<TreeType> list = new ArrayList<>();
        for (Dept dept1 : depts) {

            if (dept1.getParentId().equals(dept.getId())) {

                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept1.getId());
                treeType.setName(dept1.getDeptName());
                treeType.setCaseNum(dept.getCaseNum());
                List<TreeType> childrens = listr(depts, dept1,smallStageList);

                treeType.setChildren(childrens);
                list.add(treeType);

            }
        }
        //根据部门id以及团队id查询该部门人员信息
        List<Employees> employees = teamAppealMapper.selectEmployeesWithExecute(dept.getId(), TokenInformation.getCreateid(),smallStageList);
        List<TreeType> arrlist = new ArrayList<>();
        for (Employees employees1 : employees) {
            TreeType treeType1 = new TreeType();
            treeType1.setId(employees1.getId().toString());
            treeType1.setName(employees1.getEmployeeName());
            treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
            treeType1.setCaseNum(employees1.getCaseNum());
            arrlist.add(treeType1);
        }

        list.addAll(arrlist);


        return list;
    }

    /**
     * 根据部门id查询部门及以下部门的所有部门id
     *
     * @return
     */
    public List<Integer> Encapsulations(Integer deptId, LoginUser loginUser) {
        if (deptId == null) {
            return Encapsulation(loginUser);
        }
        Map<String, Object> accountInfo = loginUser.getAccountInfo();
        Integer teamId = (Integer) accountInfo.get(UserConstants.TEAM_ID);
        //根据团队id查询该团队所有部门信息
        List<Dept> deptList = teamAppealMapper.selectDept(teamId);
        List<Integer> list = new ArrayList<>();
        //所传的部门id
        list.add(deptId);
        for (Dept dept : deptList) {
            //祖级列表
            String ancestors = dept.getAncestors();
            List<String> splits = SplitUtils.strSplitComma(ancestors);
            for (String split : splits) {
                if (StrUtil.equals(split, StrUtil.toString(deptId))) {
                    list.add(dept.getId());
                }
            }
        }
        return list;
    }

    /**
     * 根据登录人id查询登录人部门及以下部门的所有部门id
     *
     * @param loginUser 当前登录人信息
     * @return
     */
    public List<Integer> Encapsulation(com.zws.system.api.model.LoginUser loginUser) {
        if (loginUser == null) {
            throw new ServiceException("当前登录信息获取失败");
        }
        Map<String, Object> accountInfo = loginUser.getAccountInfo();
        Integer userId = (Integer) accountInfo.get(UserConstants.USER_ID);
        //根据员工id查询员工部门id
        Employees employees = teamAppealMapper.selectEmployeesId(userId);
        if (ObjectUtils.isEmpty(employees)) {
            return new ArrayList<Integer>();
        }
        //登录用户的部门id
        Integer departmentId = employees.getDepartmentId();

        //强转为string类型
        String departmentIds = String.valueOf(departmentId);
        //根据团队id查询该团队所有部门信息
        List<Dept> deptList = teamAppealMapper.selectDept(TokenInformation.getCreateid(loginUser));
        List<Integer> list = new ArrayList<>();
        //登录人本身的部门id
        list.add(departmentId);
        for (Dept dept : deptList) {
            //祖级列表
            String ancestors = dept.getAncestors();
            String[] split = ancestors.split(",");
            for (String splits : split) {
                if (splits.equals(departmentIds)) {
                    list.add(dept.getId());
                }
            }
        }
        return list;
    }

    /**
     * 判断是否有子列表
     *
     * @param parentId
     * @return
     */
    public Boolean ChildNode(int parentId) {
        List<Dept> deptList = teamAppealMapper.selectDeptParentId(parentId);
        return !ObjectUtils.isEmpty(deptList);
    }

    /**
     * 迭代查询子列表
     *
     * @param depts
     * @param dept
     * @return
     */
    public List<DeliveryRanking> deliveryRankingList(List<Dept> depts, Dept dept) {
        List<DeliveryRanking> list = new ArrayList<>();
        for (Dept dept1 : depts) {

            if (dept1.getParentId().equals(dept.getId())) {

                DeliveryRanking treeType = new DeliveryRanking();
                treeType.setId("Dept:" + dept1.getId());
                treeType.setName(dept1.getDeptName());
                treeType.setCaseNum(dept.getCaseNum());
                List<DeliveryRanking> childrens = deliveryRankingList(depts, dept1);

                treeType.setChildren(childrens);
                list.add(treeType);

            }
        }
        //根据部门id以及团队id查询该部门人员信息
        List<Employees> employees = teamAppealMapper.selectEmployeesByDelivery(dept.getId(), TokenInformation.getCreateid());
        List<DeliveryRanking> arrlist = new ArrayList<>();
        for (Employees employees1 : employees) {
            DeliveryRanking treeType1 = new DeliveryRanking();
            treeType1.setId(employees1.getId().toString());
            treeType1.setName(employees1.getEmployeeName());
            treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
            treeType1.setCaseNum(employees1.getCaseNum());
            arrlist.add(treeType1);
        }

        list.addAll(arrlist);


        return list;
    }

    /**
     * 迭代查询子列表
     *
     * @param depts
     * @param dept
     * @return
     */
    public List<TreeType> listr(List<Dept> depts, Dept dept,List<String> smallStageList) {
        List<TreeType> list = new ArrayList<>();
        for (Dept dept1 : depts) {

            if (dept1.getParentId().equals(dept.getId())) {

                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept1.getId());
                treeType.setName(dept1.getDeptName());
                treeType.setCaseNum(dept.getCaseNum());
                List<TreeType> childrens = listr(depts, dept1,smallStageList);

                treeType.setChildren(childrens);
                list.add(treeType);

            }
        }
        //根据部门id以及团队id查询该部门人员信息
        List<Employees> employees = teamAppealMapper.selectEmployeesWithMediation(dept.getId(), TokenInformation.getCreateid(),smallStageList);
        List<TreeType> arrlist = new ArrayList<>();
        for (Employees employees1 : employees) {
            TreeType treeType1 = new TreeType();
            treeType1.setId(employees1.getId().toString());
            treeType1.setName(employees1.getEmployeeName());
            treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
            treeType1.setCaseNum(employees1.getCaseNum());
            arrlist.add(treeType1);
        }

        list.addAll(arrlist);


        return list;
    }

    /**
     * 迭代查询子列表
     *
     * @param depts
     * @param dept
     * @return
     */
    public List<TreeType> listrWithDisposeStage(List<Dept> depts, Dept dept,List<String> smallStageList) {
        List<TreeType> list = new ArrayList<>();
        for (Dept dept1 : depts) {

            if (dept1.getParentId().equals(dept.getId())) {

                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept1.getId());
                treeType.setName(dept1.getDeptName());
                treeType.setCaseNum(dept.getCaseNum());
                List<TreeType> childrens = listrWithDisposeStage(depts, dept1,smallStageList);

                treeType.setChildren(childrens);
                list.add(treeType);

            }
        }
        //根据部门id以及团队id查询该部门人员信息
        List<Employees> employees = teamAppealMapper.selectEmployeesWithDisposeStage(dept.getId(), TokenInformation.getCreateid(),smallStageList);
        List<TreeType> arrlist = new ArrayList<>();
        for (Employees employees1 : employees) {
            TreeType treeType1 = new TreeType();
            treeType1.setId(employees1.getId().toString());
            treeType1.setName(employees1.getEmployeeName());
            treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
            treeType1.setCaseNum(employees1.getCaseNum());
            arrlist.add(treeType1);
        }

        list.addAll(arrlist);


        return list;
    }

    /**
     * 迭代查询子列表
     *
     * @param depts
     * @param dept
     * @return
     */
    public List<TreeType> listrWithSaveStage(List<Dept> depts, Dept dept,List<String> smallStageList , String smallStage) {
        List<TreeType> list = new ArrayList<>();
        for (Dept dept1 : depts) {

            if (dept1.getParentId().equals(dept.getId())) {

                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept1.getId());
                treeType.setName(dept1.getDeptName());
                treeType.setCaseNum(dept.getCaseNum());
                List<TreeType> childrens = listrWithSaveStage(depts, dept1,smallStageList,smallStage);

                treeType.setChildren(childrens);
                list.add(treeType);

            }
        }
        //根据部门id以及团队id查询该部门人员信息
        List<Employees> employees = teamAppealMapper.selectEmployeesWithSaveStage(dept.getId(), TokenInformation.getCreateid(),smallStageList,smallStage);
        List<TreeType> arrlist = new ArrayList<>();
        for (Employees employees1 : employees) {
            TreeType treeType1 = new TreeType();
            treeType1.setId(employees1.getId().toString());
            treeType1.setName(employees1.getEmployeeName());
            treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
            treeType1.setCaseNum(employees1.getCaseNum());
            arrlist.add(treeType1);
        }

        list.addAll(arrlist);


        return list;
    }
}
