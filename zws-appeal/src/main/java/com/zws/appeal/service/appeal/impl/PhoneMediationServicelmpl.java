package com.zws.appeal.service.appeal.impl;

import com.zws.appeal.agservice.AgLawsuitService;
import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.mapper.appeal.PhoneMediationMapper;
import com.zws.appeal.pojo.appeal.PhoneMediationPojo;
import com.zws.appeal.service.appeal.IPhoneMediationService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.enums.TimeContentFormats;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

/**
 * 诉前调解
 * <AUTHOR>
 * @date 2024/6/18 13:58
 */
@Service
public class PhoneMediationServicelmpl implements IPhoneMediationService {

    @Autowired
    private PhoneMediationMapper phoneMediationMapper;
    @Autowired
    private AgLawsuitService agLawsuitService;


    @Override
    public List<PhoneMediationPojo> getPhoneMediation(PhoneMediationPojo phoneMediationPojo) {
        phoneMediationPojo.setTeamId(SecurityUtils.getTeamId());
        phoneMediationPojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        phoneMediationPojo.setOdvId(TokenInformation.getUserid());
//        phoneMediationPojo.setCaseIds(getCaseIds(phoneMediationPojo));
        List<PhoneMediationPojo> list = phoneMediationMapper.getPhoneMediation(phoneMediationPojo);
        //解密
        for (PhoneMediationPojo mediationPojo : list) {
            mediationPojo.setClientName(FieldEncryptUtil.decrypt(mediationPojo.getClientName()));
            mediationPojo.setClientPhone(FieldEncryptUtil.decrypt(mediationPojo.getClientPhone()));
            mediationPojo.setClientCensusRegister(FieldEncryptUtil.decrypt(mediationPojo.getClientCensusRegister()));
            mediationPojo.setClientIdNum(FieldEncryptUtil.decrypt(mediationPojo.getClientIdNum()));
        }
        return list;
    }

    @Override
    public void addNum(PhoneMediationPojo phoneMediationPojo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        agLawsuitService.addTimeManage(loginUser,phoneMediationPojo.getCaseIds(), TimeContentFormats.MEDIATION_NUMBER);
        phoneMediationMapper.addNum(phoneMediationPojo);
    }

    @Override
    public void addLink(PhoneMediationPojo phoneMediationPojo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        agLawsuitService.addTimeManage(loginUser,phoneMediationPojo.getCaseIds(), TimeContentFormats.PUBLICITY_LINK);
        phoneMediationMapper.addLink(phoneMediationPojo);
    }

    @Override
    public List<Long> getCaseIds(PhoneMediationPojo phoneMediationPojo) {
        if(ObjectUtils.isEmpty(phoneMediationPojo.getTeamId())){
            Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
            phoneMediationPojo.setTeamId(teamId);
        }
        if(ObjectUtils.isEmpty(phoneMediationPojo.getOdvId())){
            phoneMediationPojo.setOdvId(TokenInformation.getUserid());
        }

        List<Long> caseIds = null;
        if (phoneMediationPojo.getAllQuery()){
            caseIds = phoneMediationMapper.getCaseIds(phoneMediationPojo);
        }else {
            caseIds = phoneMediationPojo.getCaseIds();
        }
        return caseIds;
    }

    @Override
    public List<CaseManage> selectCaseManages(PhoneMediationPojo mediationPojo) {
        mediationPojo.setTeamId(SecurityUtils.getTeamId());
        mediationPojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        mediationPojo.setOdvId(TokenInformation.getUserid());
        return phoneMediationMapper.selectCaseManages(mediationPojo);
    }

    @Override
    public Map<String, Object> selectCaseManageMoneySize(Map<String, Object> map) {
        return phoneMediationMapper.selectCaseManageMoneySize(map);
    }

    @Override
    public Integer selectCaseStateSize(Map<String, Object> map) {
        return phoneMediationMapper.selectCaseStateSize(map);
    }

}
