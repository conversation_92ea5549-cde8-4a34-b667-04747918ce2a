package com.zws.appeal.service.impl;

import com.zws.common.core.utils.DateUtils;
import com.zws.appeal.domain.TeamLogininfor;
import com.zws.appeal.mapper.TeamLogininforMapper;
import com.zws.appeal.service.TeamLogininforService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 登录日志
 *
 * <AUTHOR>
 * @date ：Created in 2022/4/13 19:51
 */
@Service
@Primary
public class TeamLogininforServiceImpl implements TeamLogininforService {

    @Resource
    private TeamLogininforMapper mapper;

    @Override
    public long saveLogInInfor(TeamLogininfor logininfor) {
        logininfor.setAccessTime(DateUtils.getNowDate());
        int i = mapper.insert(logininfor);
        if (i > 0) {
            return logininfor.getId();
        }
        return 0;
    }
}
