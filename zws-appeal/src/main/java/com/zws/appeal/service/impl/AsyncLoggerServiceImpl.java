package com.zws.appeal.service.impl;

import cn.hutool.core.date.DateUtil;
import com.zws.appeal.domain.log.TeamOperLog;
import com.zws.appeal.mapper.AsyncLoggerMapper;
import com.zws.appeal.service.AsyncLoggerService;
import com.zws.appeal.utils.TokenInformation;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 异步调用日志服务
 *
 * <AUTHOR>
 */
@Service
public class AsyncLoggerServiceImpl implements AsyncLoggerService {

    @Resource
    private AsyncLoggerMapper asyncLoggerMapper;

    /**
     * 根据用户id查询操作日志
     *
     * @param
     */
    @Override
    public List<TeamOperLog> selectTeamOperLog(TeamOperLog teamOperLog) {
        Map<String, Object> map = new HashMap<>();
        if (!ObjectUtils.isEmpty(teamOperLog.getBusinessType())) {
            map.put("businessType", teamOperLog.getBusinessType());
        }
        if (!ObjectUtils.isEmpty(teamOperLog.getOperName())) {
            map.put("operName", teamOperLog.getOperName());
        }
        if (!ObjectUtils.isEmpty(teamOperLog.getOperTime1()) && !ObjectUtils.isEmpty(teamOperLog.getOperTime2())) {
            //一天的开始，结果：2017-03-01 00:00:00
            Date beginOfDay = DateUtil.beginOfDay(teamOperLog.getOperTime1());
            //一天的结束，结果：2017-03-01 23:59:59
            Date endOfDay = DateUtil.endOfDay(teamOperLog.getOperTime2());
            map.put("operTime1", beginOfDay);
            map.put("operTime2", endOfDay);
        }
//        账号类型，账号类型，0-团队主账号，1-员工账号,2-资产端账号
        if (TokenInformation.getType() == 0) {
            map.put("teamId", TokenInformation.getCreateid());
        } else {
            map.put("userId", TokenInformation.getUserid());
        }
        return asyncLoggerMapper.selectTeamOperLog(map);
    }

    /**
     * 删除超过一年的操作日志
     *
     * @param operTime
     * @return
     */
    @Override
    public int delectTeamLogininfor(Date operTime) {
        return asyncLoggerMapper.delectTeamLogininfor(operTime);
    }

    /**
     * 保存系统日志记录
     */
    @Async
    @Override
    public Integer saveSysLog(TeamOperLog teamOperLog) {
        asyncLoggerMapper.insertTeamOperLog(teamOperLog);
        return 1;
    }
}
