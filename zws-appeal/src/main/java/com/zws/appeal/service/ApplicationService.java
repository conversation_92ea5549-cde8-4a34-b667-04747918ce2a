package com.zws.appeal.service;

import com.zws.appeal.domain.ApplyRecord;
import com.zws.appeal.domain.record.*;

import java.util.List;

public interface ApplicationService {

    /**
     * 修改审批流程判断是否有未走完的退案/留案/停催审核流程的数据
     *
     * @param
     */
    List<ApplyRecord> selectApplyRecord(int applyState, int teamId);

    /**
     * 修改审批流程判断是否有未走完的回款审核流程的数据
     *
     * @param
     */
    List<RepaymentRecord> selectRepaymentRecord(int teamId);

    /**
     * 修改审批流程判断是否有未走完的减免审核流程的数据
     *
     * @param
     */
    List<ReductionRecord> selectReductionRecord(int teamId);

    /**
     * 修改审批流程判断是否有未走完的分期还款审核流程的数据
     *
     * @param
     */
    List<StagingRecord> selectStagingRecord(int teamId);

    /**
     * 修改审批流程判断是否有未走完的外访审核流程的数据
     *
     * @param
     */
    List<OutsideRecord> selectOutsideRecord(int teamId);

    /**
     * 修改审批流程判断是否有未走完的资料审核流程的数据
     *
     * @param
     */
    List<RetrievalRecord> selectRetrievalRecord(int teamId);
}
