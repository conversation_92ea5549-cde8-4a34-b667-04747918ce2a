package com.zws.appeal.service;

import com.zws.appeal.domain.Dept;

import java.util.List;

/**
 * 团队部门-业务层
 *
 * <AUTHOR>
 * @date 2024/1/29 16:22
 */
public interface ITeamDeptService {


    /**
     * 查询子部门
     *
     * @param parentId
     * @return
     */
    List<Dept> selectByParentId(Integer parentId);


    /**
     * 查询后代部门ID
     *
     * @param deptId
     * @return
     */
    List<Integer> selectProgenyDept(Integer deptId);


}
