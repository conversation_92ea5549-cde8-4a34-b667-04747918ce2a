package com.zws.appeal.service;

import com.zws.appeal.domain.ApplyRecord;
import com.zws.appeal.domain.ApproveProce;
import com.zws.appeal.domain.record.*;
import com.zws.appeal.pojo.OutsideCollectionUtils;
import com.zws.appeal.pojo.PaymentCollectionUtils;
import com.zws.appeal.pojo.QueryCriteria;
import com.zws.appeal.pojo.SignCollectionUtils;
import com.zws.appeal.pojo.myApproval.*;
import com.zws.system.api.domain.CaseSignRecord;

import java.util.List;

public interface MyApprovalService {

//    /**
//     * 根据条件查询我的审批信息（停案/留案/停催）-通过/未通过案件
//     *
//     * @param queryCriteria
//     * @return
//     */
//    List<myApplyRecordUtils> selectCaseDetails(QueryCriteria queryCriteria);
//
//    /**
//     * 根据条件查询我的审批信息（停案/留案/停催）-待审核案件
//     *
//     * @param queryCriteria
//     * @return
//     */
//    List<myApplyRecordUtils> selectApplyRecord(QueryCriteria queryCriteria);

    /**
     * 根据条件查询我的审批信息（停案/留案/停催）-通过/未通过案件/待审核案件
     *
     * @param queryCriteria
     * @return
     */
    List<myApplyRecordUtils> selectApplyRecord(QueryCriteria queryCriteria);

    /**
     * 根据条件查询我的资料审批审批信息-通过/未通过案件/待审核案件
     *
     * @param paymentCollectionUtils
     * @return
     */
    List<MyRetrievalRecordUtils> selectRetrievalRecord(PaymentCollectionUtils paymentCollectionUtils);

//    /**
//     * 根据条件查询我的回款审批信息-待审核案件
//     * @param paymentCollectionUtils
//     * @return
//     */
//    List<myRepaymentRecordUtils> selectRepaymentRecord(PaymentCollectionUtils paymentCollectionUtils);
//
//    /**
//     * 根据条件查询我的回款审批信息-通过/未通过案件
//     * @param paymentCollectionUtils
//     * @return
//     */
//    List<myRepaymentRecordUtils> selectRepaymentRecordProce(PaymentCollectionUtils paymentCollectionUtils);

    /**
     * 根据条件查询我的回款审批信息-通过/未通过案件/待审核案件
     *
     * @param paymentCollectionUtils
     * @return
     */
    List<myRepaymentRecordUtils> selectToBeReviewd(PaymentCollectionUtils paymentCollectionUtils);

//    /**
//     * 根据条件查询我的减免审批信息-待审核案件
//     * @param paymentCollectionUtils
//     * @return
//     */
//    List<myReductionRecordUtils> selectReductionRecord(PaymentCollectionUtils paymentCollectionUtils);
//
//    /**
//     * 根据条件查询我的减免审批信息-通过/未通过案件
//     * @param paymentCollectionUtils
//     * @return
//     */
//    List<myReductionRecordUtils> selectReductionRecordProce(PaymentCollectionUtils paymentCollectionUtils);

    /**
     * 根据条件查询我的减免审批信息-通过/未通过案件/待审核案件
     *
     * @param paymentCollectionUtils
     * @return
     */
    List<myReductionRecordUtils> selectReductionRecord(PaymentCollectionUtils paymentCollectionUtils);

//    /**
//     * 根据条件查询我的分期还款审批信息-待审核案件
//     *
//     * @param paymentCollectionUtils
//     * @return
//     */
//    List<MyStagingRecordUtils> selectStagingRecord(PaymentCollectionUtils paymentCollectionUtils);
//
//    /**
//     * 根据条件查询我的分期还款审批信息-通过/未通过案件
//     *
//     * @param paymentCollectionUtils
//     * @return
//     */
//    List<MyStagingRecordUtils> selectStagingRecordProce(PaymentCollectionUtils paymentCollectionUtils);

    /**
     * 根据条件查询我的分期还款审批信息-通过/未通过案件/待审核案件
     *
     * @param paymentCollectionUtils
     * @return
     */
    List<MyStagingRecordUtils> selectStagingRecord(PaymentCollectionUtils paymentCollectionUtils);

//    /**
//     * 根据条件查询我的外访审批信息-待审核案件
//     *
//     * @param outsideCollectionUtils
//     * @return
//     */
//    List<MyOutsideRecordUtils> selectOutsideRecord(OutsideCollectionUtils outsideCollectionUtils);
//
//    /**
//     * 根据条件查询我的外访审批信息-通过/未通过案件
//     *
//     * @param outsideCollectionUtils
//     * @return
//     */
//    List<MyOutsideRecordUtils> selectOutsideRecordProce(OutsideCollectionUtils outsideCollectionUtils);

    /**
     * 根据条件查询我的外访审批信息-通过/未通过案件/待审核案件
     *
     * @param outsideCollectionUtils
     * @return
     */
    List<MyOutsideRecordUtils> selectOutsideRecord(OutsideCollectionUtils outsideCollectionUtils);

    /**
     * 根据条件查询我的签章审批信息-通过/未通过案件/待审核案件
     *
     * @param signCollectionUtils
     * @return
     */
    List<MySignRecordUtils> selectSignRecord(SignCollectionUtils signCollectionUtils);

    /**
     * 根据减免申请表id查询减免申请凭证信息
     *
     * @param id
     * @return
     */
    List<ReductionFile> selectReductionFile(int id);

    /**
     * 根据申请表id查询审核进程表数据
     *
     * @param approveProce
     * @return
     */
    List<ApproveProce> selectApproveProce(ApproveProce approveProce);


    /**
     * 退案/留案/停催审批（写入审批记录历史表）
     *
     * @param approveProce
     * @return
     */
    int insertApproveProce(List<ApproveProce> approveProce);

    /**
     * 退案/留案/停催审批（修改申请表中审核状态等）
     *
     * @param applyRecord
     * @return
     */
    int updateApplyRecord(List<ApplyRecord> applyRecord);

    /**
     * 资料调取审批（修改申请表中审核状态等）
     *
     * @param retrievalRecords
     * @return
     */
    int updateRetrievalRecord(List<RetrievalRecord> retrievalRecords);

    /**
     * 回款审批（修改申请表中审核状态等）
     *
     * @param repaymentRecords
     * @return
     */
    int updateRepaymentRecord(List<RepaymentRecord> repaymentRecords);

    /**
     * 减免审批（修改申请表中审核状态等）
     *
     * @param reductionRecords
     * @return
     */
    int updateReductionRecord(List<ReductionRecord> reductionRecords);

    /**
     * 分期还款审批（修改申请表中审核状态等）
     *
     * @param stagingRecords
     * @return
     */
    int updateStagingRecord(List<StagingRecord> stagingRecords);

    /**
     * 外访审批（修改申请表中审核状态等）
     *
     * @param outsideRecords
     * @return
     */
    int updateOutsideRecord(List<OutsideRecord> outsideRecords);

    /**
     * 签章审批（修改申请表中审核状态等）
     * @param signRecord
     * @return
     */
    int updateSignRecord(CaseSignRecord signRecord);

    /**
     * 签章审批（修改申请表中审核状态等）
     * @param id
     * @return
     */
    CaseSignRecord selectSignRecordInfo(Long id);

    /**
     * 根据申请id集合查询回款申请详情
     *
     * @param ids
     * @return
     */
    List<RepaymentRecord> selectRepaymentRecordById(List<Long> ids);

    /**
     * 根据申请id集合查询减免申请详情
     *
     * @param ids
     * @return
     */
    List<ReductionRecord> selectReductionRecordById(List<Long> ids);

    /**
     * 根据申请id集合查询分期还款申请详情
     *
     * @param ids
     * @return
     */
    List<StagingRecord> selectStagingRecordById(List<Long> ids);

    /**
     * 根据申请id集合查询外访申请详情
     *
     * @param ids
     * @return
     */
    List<OutsideRecord> selectOutsideRecordById(List<Long> ids);

    /**
     * 根据申请id集合查询留案/退案申请详情
     *
     * @param ids
     * @param applyState
     * @return
     */
    List<ApplyRecord> selectApplyRecordById(List<Long> ids, int applyState);
    List<ApproveProce> selectApplyRecordByIdsAndSort(List<Long> ids, Integer sort, Integer approveCode);

}
