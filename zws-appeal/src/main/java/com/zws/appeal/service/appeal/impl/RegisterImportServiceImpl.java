package com.zws.appeal.service.appeal.impl;

import com.zws.appeal.service.appeal.IRegisterImportService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.zws.appeal.domain.appeal.RegisterImport;
import com.zws.appeal.mapper.appeal.RegisterImportMapper;

/**
*编辑名称
*@Author：liuxifeng
*@Date：2024/6/21  16:39
*@Describe：编辑描述
*/
@Service
public class RegisterImportServiceImpl implements IRegisterImportService {

    @Resource
    private RegisterImportMapper registerImportMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return registerImportMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(RegisterImport record) {
        return registerImportMapper.insert(record);
    }

    @Override
    public int insertSelective(RegisterImport record) {
        return registerImportMapper.insertSelective(record);
    }

    @Override
    public RegisterImport selectByPrimaryKey(Long id) {
        return registerImportMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(RegisterImport record) {
        return registerImportMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(RegisterImport record) {
        return registerImportMapper.updateByPrimaryKey(record);
    }

    @Override
    public int batchInsert(List<RegisterImport> list) {
        return registerImportMapper.batchInsert(list);
    }

}
