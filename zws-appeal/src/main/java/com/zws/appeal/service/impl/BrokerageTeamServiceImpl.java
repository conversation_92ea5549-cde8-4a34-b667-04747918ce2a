package com.zws.appeal.service.impl;

import com.zws.appeal.domain.BrokerageTeam;
import com.zws.appeal.domain.DetailsBrokerage;
import com.zws.appeal.domain.RecoveryAsset;
import com.zws.appeal.domain.RiskTeam;
import com.zws.appeal.mapper.BrokerageTeamMapper;
import com.zws.appeal.service.BrokerageTeamService;
import com.zws.appeal.utils.TokenInformation;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * 结算中心机构佣金信息
 *
 * @Author: 马博新
 * @DATE: Created in 2023/4/23 18:13
 */
@Service
@Primary
public class BrokerageTeamServiceImpl implements BrokerageTeamService {


    @Resource
    private BrokerageTeamMapper brokerageTeamMapper;

    /**
     * 查询结算中心机构佣金信息
     *
     * @param brokerageTeam
     * @return
     */
    @Override
    public List<BrokerageTeam> selectAccountById(BrokerageTeam brokerageTeam) {
        return brokerageTeamMapper.selectAccountById(brokerageTeam);
    }

//    /**
//     * 根据机构佣金信息主键id集合查询机构名称
//     *
//     * @param idList
//     * @return
//     */
//    @Override
//    public List<String> selectById(List<Long> idList) {
//        return brokerageTeamMapper.selectById(idList);
//    }

    /**
     * 根据团队id以及结佣日期（年/月）查询结算中心机构结佣信息
     *
     * @param brokerageTeam
     * @return
     */
    @Override
    public List<BrokerageTeam> selectTeamBrokerage(BrokerageTeam brokerageTeam) {
        return brokerageTeamMapper.selectTeamBrokerage(brokerageTeam);
    }

    /**
     * 根据佣金表主键id查询团队名称以及结佣日期（年/月）
     *
     * @param id
     * @return
     */
    @Override
    public BrokerageTeam selectFileName(Long id) {
        return brokerageTeamMapper.selectFileName(id);
    }

    /**
     * 根据佣金表id以及状态查询机构佣金详情表
     *
     * @param detailsBrokerage
     * @return
     */
    @Override
    public List<DetailsBrokerage> selectDetailsBrokerage(DetailsBrokerage detailsBrokerage) {
        return brokerageTeamMapper.selectDetailsBrokerage(detailsBrokerage);
    }

    /**
     * 根据佣金详情表id查询资产回收情况
     *
     * @param detailsId
     * @return
     */
    @Override
    public List<RecoveryAsset> selectRecoveryAsset(Long detailsId) {
        List<RecoveryAsset> recoveryAssets = brokerageTeamMapper.selectRecoveryAsset(detailsId);
        if(!ObjectUtils.isEmpty(recoveryAssets)){
            for (RecoveryAsset recoveryAsset : recoveryAssets) {
                recoveryAsset.setRepaymentMoney(recoveryAsset.getRepaymentMoney().setScale(2, RoundingMode.HALF_UP));
                recoveryAsset.setServiceRate(recoveryAsset.getServiceRate().setScale(2, RoundingMode.HALF_UP));
                recoveryAsset.setBrokerage(recoveryAsset.getBrokerage().setScale(2, RoundingMode.HALF_UP));
            }
        }
        return recoveryAssets;
    }

    /**
     * 根据佣金详情表id查询机构风险奖罚设置
     *
     * @param detailsId
     * @return
     */
    @Override
    public List<RiskTeam> selectRiskTeam(Long detailsId) {
        List<RiskTeam> riskTeams = brokerageTeamMapper.selectRiskTeam(detailsId);
        if(!ObjectUtils.isEmpty(riskTeams)){
            for (RiskTeam riskTeam : riskTeams) {
                riskTeam.setFloatingRate(riskTeam.getFloatingRate().setScale(2, RoundingMode.HALF_UP));
            }
        }
        return riskTeams;
    }

    /**
     * 根据佣金详情表id查询统计资产回收情况回款金额
     *
     * @param detailsId
     * @return
     */
    @Override
    public BigDecimal selectCount(Long detailsId) {
        BigDecimal bigDecimal = brokerageTeamMapper.selectCount(detailsId);
        if (bigDecimal == null) {
            return BigDecimal.ZERO;
        } else {
            return bigDecimal;
        }
    }

    /**
     * 根据主键id修改佣金详情表信息
     *
     * @param detailsBrokerage
     * @return
     */
    @Override
    public int updateDetailsBrokerage(DetailsBrokerage detailsBrokerage) {
        if (ObjectUtils.isEmpty(detailsBrokerage)) {
            return 0;
        }
        detailsBrokerage.setUpdateBy(TokenInformation.getUsername());
        detailsBrokerage.setUpdateById(TokenInformation.getUserid().longValue());
        detailsBrokerage.setUpdateTime(new Date());
        return brokerageTeamMapper.updateDetailsBrokerage(detailsBrokerage);
    }

    /**
     * 根据主键id修改佣金表信息
     *
     * @param brokerageTeam
     * @return
     */
    @Override
    public int updateBrokerageTeam(BrokerageTeam brokerageTeam) {
        if (ObjectUtils.isEmpty(brokerageTeam)) {
            return 0;
        }
        brokerageTeam.setUpdateBy(TokenInformation.getUsername());
        brokerageTeam.setUpdateById(TokenInformation.getUserid().longValue());
        brokerageTeam.setUpdateTime(new Date());
        return brokerageTeamMapper.updateBrokerageTeam(brokerageTeam);
    }

//    /**
//     * 新增结算中心机构佣金信息
//     *
//     * @param brokerageTeam
//     * @return
//     */
//    @Override
//    public Long insert(BrokerageTeam brokerageTeam) {
//        if (ObjectUtils.isEmpty(brokerageTeam)) return null;
//        brokerageTeam.setCreateBy(SecurityUtils.getUsername());
//        brokerageTeam.setCreateById(SecurityUtils.getUserId());
//        brokerageTeam.setCreateTime(new Date());
//        brokerageTeam.setDelFlag(BaseConstant.DelFlag_Being);
//        brokerageTeamMapper.insert(brokerageTeam);
//        return brokerageTeam.getId();
//    }

}
