package com.zws.appeal.service;

import com.zws.appeal.domain.TeamBatchDimension;
import com.zws.appeal.domain.TeamDataOverview;
import com.zws.appeal.domain.TeamEmployeeDimension;
import com.zws.appeal.pojo.Option;

import java.util.List;
import java.util.Map;

/**
 * 数据概览
 *
 * @Author: 刘锡锋
 * @DATE: Created in 2023/5/26
 */
public interface CensusAssetsService {

    /**
     * 添加批次维度_数据概览
     *
     * @param record
     * @return
     */
    int insert(TeamBatchDimension record);

    /**
     * 添加批次维度_数据概览
     *
     * @param record
     * @return
     */
    int insertSelective(TeamBatchDimension record);

    /**
     * 批次维度数据概览
     *
     * @param createId
     * @return
     */
    List<TeamBatchDimension> getStatisticsBatch(Long createId);

    /**
     * 定时任务
     * 每天凌晨统计存储所有团队_数据概览
     */
    void autoCensusAssets();

    /**
     * 查询批次维度列表
     *
     * @param teamId
     * @param batchNum
     * @return
     */
    List<TeamBatchDimension> getCensusBatchList(Long teamId, List<String> batchNum);

    /**
     * 获取团队所有分案批次
     *
     * @param valueOf
     * @return
     */
    List<Option> getBatchNum(Long valueOf);


    /**
     * 查询员工维度_数据概览
     *
     * @param teamEmployeeDimension
     * @return
     */
    List<TeamEmployeeDimension> getCensusEmployeeList(TeamEmployeeDimension teamEmployeeDimension);

    /**
     * 根据条件查询员工名下案件所属资产包名称
     *
     * @param map
     * @return
     */
    List<String> selectAssetsPackName(Map<String, Object> map);

    /**
     * 员工维度_数据概览_导出Excel
     *
     * @param dimension
     * @return
     */
    List<TeamEmployeeDimension> exportEmployeeDimension(TeamEmployeeDimension dimension);

    /**
     * 多个月份_统计出一条员工数据
     *
     * @param list
     * @param ids  分页展示的employeeIds数据
     * @return
     */
    List<TeamEmployeeDimension> statisticsUtil(List<TeamEmployeeDimension> list, List<Long> ids);

    /**
     * 数据概览_员工维度
     *
     * @param teamId
     * @return
     */
    List<TeamEmployeeDimension> getStatisticsEmployee(Long teamId);

    /**
     * 获取该团队的所有成员姓名_下拉框
     *
     * @param teamId
     * @return
     */
    List<Option> getEmployeeOption(Long teamId);

    /**
     * 获取该团队的所有成员ID集合
     *
     * @param teamId
     * @return
     */
    List<Long> getEmployeeIds(Long teamId);

    /**
     * 查询当前月该员工_是否已统计过_员工维度
     *
     * @param teamId
     * @param employeeId
     * @return
     */
    public List<Long> checkEmployeeExit(Long teamId, Long employeeId);


    /**
     * 数据概览_员工维度
     * 定时任务存储数据
     */
    void getStatisticsEmployeeTask();

    /**
     * 获取全部合作中的团队ID
     */
    List<Long> getAllTeamId();

    /**
     * 统计数据概览方法
     *
     * @param teamId
     * @return
     */
    List<TeamDataOverview> getStatistics(Long teamId);

    /**
     * 统计数据概览_定时任务
     *
     * @return
     */
    void getStatisticsTask();


    /**
     * 查询数据概览
     *
     * @param teamId
     * @return
     */
    TeamDataOverview selectStatistics(Long teamId);


    /**
     * 更新统计数据前_删除数据概览表旧数据
     *
     * @param teamId
     * @return
     */
    void deleteOverview(Long teamId);

}
