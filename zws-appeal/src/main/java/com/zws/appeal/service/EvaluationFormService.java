package com.zws.appeal.service;

import com.zws.appeal.domain.Evaluate;
import com.zws.appeal.domain.EvaluationForm;
import com.zws.appeal.pojo.EvaluationFormPojo;
import com.zws.appeal.pojo.TimeIntervalPojo;

import java.util.List;

/**
 * @Author: 马博新
 * @DATE: Created in 2022/9/13 20:15
 */
public interface EvaluationFormService {

    /**
     * 根据条件查询构评价记录
     *
     * @param evaluationFormPojo
     * @return
     */
    List<EvaluationForm> selectEvaluationForm(EvaluationFormPojo evaluationFormPojo);

    /**
     * 写入机构评价记录
     *
     * @param evaluationForm
     * @return
     */
    int insertEvaluationForm(EvaluationForm evaluationForm);

    /**
     * 根据时间区间以及团队id查询该团队的评价信息
     *
     * @param timeIntervalPojo
     * @return
     */
    Evaluate selectEvaluate(TimeIntervalPojo timeIntervalPojo);
}
