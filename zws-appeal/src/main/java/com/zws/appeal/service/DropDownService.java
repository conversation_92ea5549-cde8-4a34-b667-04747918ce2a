package com.zws.appeal.service;

import com.zws.appeal.domain.*;
import com.zws.appeal.pojo.AllocatedRecordUtils;
import com.zws.appeal.pojo.DictDataLinkage;
import com.zws.system.api.domain.Legal;

import java.util.List;
import java.util.Map;

public interface DropDownService {

    /**
     * 资产方信息全查
     *
     * @return
     */
    List<AssetOwner> selectAssetOwner();

    /**
     * 产品信息全查
     *
     * @return
     */
    List<AssetProduct> selectAssetProduct();

    /**
     * 跟进状态以及催收状态全查
     *
     * @return
     */
    Map<String, Object> selectDictData();

    /**
     * 跟进状态以及催收状态联动查询
     *
     * @return
     */
    List<DictDataLinkage> selectDictDataLinkage();

    /**
     * 联系渠道全查
     *
     * @return
     */
    List<String> selectDictDataContact();

    /**
     * 根据登录人团队id查询该团队所有员工id以及名称
     *
     * @return
     */
    List<Employees> selectEmployees();

    /**
     * 根据登录人团队id查询委案批次号
     *
     * @return
     */
    List<String> selectCaseManage();

    /**
     * 根据登录人团队id查询启用标签信息
     *
     * @return
     */
    List<Legal> selectLabel();

    /**
     * 查询该团队的客户户籍
     *
     * @return
     */
    List<String> selectRegisteredResidence();

    /**
     * 委案日志查询/按条件查询
     *
     * @return
     */
    List<AllocatedRecord> selectAllocatedRecord(AllocatedRecordUtils allocatedRecordUtils);
}
