package com.zws.appeal.service;

import com.zws.appeal.domain.Message.MessageCenter;
import com.zws.appeal.domain.Message.UserMessage;

import java.util.List;

public interface MessageService {

    /**
     * 查询团队所有发布消息信息
     *
     * @return
     */
    List<MessageCenter> selectMessageCenter(MessageCenter messageCenter);

    /**
     * 根据id查询发布消息信息
     *
     * @return
     */
    MessageCenter selectMessageCenterId(MessageCenter messageCenter);

    /**
     * 写入发布消息信息
     *
     * @param messageCenter
     * @return
     */
    int insertMessageCenter(MessageCenter messageCenter);

    /**
     * 写入推送消息内容
     *
     * @param userMessage
     * @return
     */
    int insertUserMessage(UserMessage userMessage);

    /**
     * 根据id修改消息标题/消息内容
     *
     * @param messageCenter
     * @return
     */
    int updateMessageCenter(MessageCenter messageCenter);

    /**
     * 删除发布消息信息
     *
     * @param messageCenter
     * @return
     */
    int deleteMessageCenter(MessageCenter messageCenter);
}
