package com.zws.appeal.service;

import com.zws.appeal.domain.*;
import com.zws.appeal.pojo.*;
import com.zws.appeal.pojo.teamApplication.*;
import com.zws.appeal.pojo.teamParameters.*;
import com.zws.system.api.model.LoginUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface TeamCaseService {


    /**
     * 规则分案写入催收员（批量）
     *
     * @return
     */
    int insertWriteRuleDivision(Distribution distributions, LoginUser loginUser);

    /**
     * （指定分案）分案记录写入
     *
     * @param distributionHistory
     * @return
     */
    int insertDistributionHistory(DistributionHistory distributionHistory, List<Long> caseIds, LoginUser loginUser);

    /**
     * 根据团队id查询该团队所有部门信息
     *
     * @return
     */
    List<Dept> selectDept(int caeateId);

    /**
     * 根据部门id查询该部门信息
     *
     * @return
     */
    Dept selectDeptByDeptId(int id);

    /**
     * 根据部门主键id查询该部门信息
     *
     * @return
     */
    Dept selectDeptById(int id);

    /**
     * 根据员工id查询员工部门id
     *
     * @return
     */
    Employees selectEmployeesId(int id);

    /**
     * 根据团队id以及部门id和条件查询该员工所在部门及以下的案件信息
     *
     * @return
     */
    List<CaseManage> selectCaseManageAndEmployees(TeamCaseUtils teamCaseUtils);

    /**
     * 根据团队id以及部门id和条件查询该员工所在部门及以下的案件信息(判断是否是搜索结果全选)
     *
     * @return
     */
    List<CaseManage> selectCaseManageAndEmployeesCondition(TeamCaseUtils teamCaseUtils);

    /**
     * 根据团队id以及部门id和条件统计案件金额与数量
     *
     * @param teamCaseUtils
     * @return
     */
    Map<String, Object> selectCaseCount(TeamCaseUtils teamCaseUtils);

    /**
     * 根据团队id以及部门id和条件查询该员工所在部门及以下的案件信息(判断是否是搜索结果全选)--团队规则分案
     *
     * @return
     */
    List<CaseManage> selectCaseManageRuleDivision(TeamCaseUtils teamCaseUtils, LoginUser loginUser);

    /**
     * 根据团队id以及部门id和条件查询该员工所在部门及以下的案件委托总金额
     *
     * @return
     */
    Map<String, Object> selectCaseManageMoneyCount(TeamCaseUtils teamCaseUtils, LoginUser loginUser);

    /**
     * 根据团队id以及部门id和条件查询该员工所在部门及以下的案件id集合
     *
     * @return
     */
    List<Long> selectTeamCaseCaseIdCount(TeamCaseUtils teamCaseUtils);

    /**
     * 根据字段查询该团队的案件/该团队案件全查-(根据分配状态查询案件总量以及案件总金额)
     *
     * @param teamCaseUtils
     * @return
     */
    Map<String, Object> selectCaseManageCount(TeamCaseUtils teamCaseUtils);

    /**
     * 根据团队id以及部门id和条件查询该员工所在部门及以下的案件id集合
     *
     * @return
     */
    List<Long> selectCaseManageCaseIdCount(@Param("teamCaseUtils") TeamCaseUtils teamCaseUtils);

    /**
     * 根据员工id查询同部门以及以下部门的员工承诺户信息
     *
     * @return
     */
    List<CreateUrgeRecordIdUtils> selectUrgeRecordId(UrgeRecordUtils urgeRecordUtils);

    /**
     * 根据员工id查询同部门以及以下部门的员工资料调取表以及案加详情信息
     *
     * @return
     */
    List<CreateRetrievalRecordUtils> selectRetrievalRecordId(TeamRetrievalRecord teamRetrievalRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工资料调取表以及案加详情信息(各状态数量)
     *
     * @return
     */
    List<Map<String, Object>> selectRetrievalRecordIdNumber(TeamRetrievalRecord teamRetrievalRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工回款表以及案加详情信息
     *
     * @return
     */
    List<CreateRepaymentRecordUtils> selectRepaymentRecordId(TeamRepaymentRecord teamRepaymentRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工回款表以及案加详情信息(各状态数量)
     *
     * @return
     */
    List<Map<String, Object>> selectRepaymentRecordIdNumber(TeamRepaymentRecord teamRepaymentRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工减免表以及案加详情信息
     *
     * @return
     */
    List<CreateReductionRecordUtils> selectReductionRecordId(TeamReductionRecord teamReductionRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工减免表以及案加详情信息(各状态数量)
     *
     * @return
     */
    List<Map<String, Object>> selectReductionRecordIdNumber(TeamReductionRecord teamReductionRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工分期还款表以及案加详情信息
     *
     * @return
     */
    List<CreateStagingRecordUtils> selectStagingRecordId(TeamStagingRecord teamStagingRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工分期还款表以及案加详情信息(各状态数量)
     *
     * @return
     */
    List<Map<String, Object>> selectStagingRecordIdNumber(TeamStagingRecord teamStagingRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工停案/留案/停催申请表以及案加详情信息
     *
     * @return
     */
    List<CreateApplyRecordUtils> selectApplyRecordId(TeamApplyRecord teamApplyRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工停案/留案/停催申请表以及案加详情信息(各状态数量)
     *
     * @return
     */
    List<Map<String, Object>> selectApplyRecordIdNumber(TeamApplyRecord teamApplyRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工协催申请表以及案加详情信息
     *
     * @return
     */
    List<CreateAssistRecordUtils> selectAssistRecordId(TeamAssistRecord teamAssistRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工外访申请表以及案加详情信息
     *
     * @return
     */
    List<CreateOutsideRecordUtils> selectOutsideRecordId(TeamOutsideRecord teamOutsideRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工外访申请表以及案加详情信息(各状态数量)
     *
     * @return
     */
    List<Map<String, Object>> selectOutsideRecordIdNumber(TeamOutsideRecord teamOutsideRecord);

    /**
     * 根据员工id查询同部门以及以下部门的员工工单申请表以及案加详情信息
     *
     * @return
     */
    List<CreateWorkOrderUtils> selectWorkOrder(TeamWorkOrder teamWorkOrder);

    /**
     * 工单ID 查找工单信息
     *
     * @return
     */
    CreateWorkOrderUtils getWorkOrderById(Long id);


    /**
     * 根据员工id查询同部门以及以下部门的员工工单申请表以及案加详情信息(各状态数量)
     *
     * @return
     */
    List<Map<String, Object>> selectWorkOrderNumber(TeamWorkOrder teamWorkOrder);

    /**
     * 查询团队 共债案件集合
     *
     * @param teamCaseUtils
     * @return
     */
    List<String> selectJointDebtIdCards(TeamCaseUtils teamCaseUtils);

    /**
     * 查询团队催记(解密)
     *
     * @param param
     * @return
     */
    List<ExportDataUtils> selectTeamUrgeRecord(TeamExportUrgeParam param);

}
