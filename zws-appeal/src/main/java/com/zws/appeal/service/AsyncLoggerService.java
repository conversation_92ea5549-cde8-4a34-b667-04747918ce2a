package com.zws.appeal.service;

import com.zws.appeal.domain.log.TeamOperLog;

import java.util.Date;
import java.util.List;

public interface AsyncLoggerService {

    /**
     * 根据用户id查询操作日志
     *
     * @param
     */
    List<TeamOperLog> selectTeamOperLog(TeamOperLog teamOperLog);

    /**
     * 删除超过一年的操作日志
     *
     * @param operTime
     * @return
     */
    int delectTeamLogininfor(Date operTime);

    /**
     * 保存系统日志记录
     */
    Integer saveSysLog(TeamOperLog teamOperLog);

}
