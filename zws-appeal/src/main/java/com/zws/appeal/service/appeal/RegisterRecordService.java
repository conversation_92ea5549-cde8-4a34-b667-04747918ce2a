package com.zws.appeal.service.appeal;


import com.zws.appeal.domain.appeal.RegisterRecord;
import com.zws.appeal.pojo.appeal.StageFieldPojo;
import com.zws.common.core.domain.Option;

import java.util.List;
public interface RegisterRecordService{


    int deleteByPrimaryKey(Long id);

    int insert(RegisterRecord record);

    int insertSelective(RegisterRecord record);

    RegisterRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RegisterRecord record);

    int updateByPrimaryKey(RegisterRecord record);

    int updateBatch(List<RegisterRecord> list);

    int batchInsert(List<RegisterRecord> list);

    /**
     * 根据类型 获取登记记录
     * @param caseId 案件id
     * @param type 登记类型
     */
    List<RegisterRecord> getRecordByType(Long caseId,Integer type,Integer webSide);

    /**
     * 获取指定登记类型下 的指定阶段
     * @param record 登记记录
     * @return
     */
    RegisterRecord getStageByType(RegisterRecord record);

    /**
     * 新增登记记录
     *
     * @param record
     */
    void addRegisterRecord(RegisterRecord record);

    /**
     * 获取下拉框
     * @return
     */
    List<Option> getStageCount(Long teamId);

    /**
     * 主账号 我的诉讼案件  下拉框
     * @return
     */
    List<Option> getStageCountByTeam();

    /**
     * 员工获取下拉框
     * @param teamId
     * @param employeeId
     * @return
     */
    List<Option> getStageCountByEmployee(Long teamId,Long employeeId);

    /**
     * 获取阶段列表
     * @param stageType 阶段类型
     * @return
     */
    List<Option> getStage(Integer stageType);

    /**
     * 获取表单配置 pojo结构
     * @param disposeWay
     * @return
     */
    List<StageFieldPojo> getFormParam(Integer disposeWay,String stageTwoName);

    /**
     * 查询调诉执记录
     * @param caseId
     * @param type
     * @param type1
     * @param webSide
     * @return
     */
    List<RegisterRecord> getAppMed(Long caseId, Integer type, Integer type1, Integer webSide);
}
