package com.zws.appeal.service.approve.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zws.appeal.config.LinkConfig;
import com.zws.appeal.controller.letterDoc.law.Base64Utils;
import com.zws.appeal.controller.letterDoc.law.domain.LawSign;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;
import com.zws.appeal.controller.letterDoc.letter.service.IDocumentMessageService;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterDocService;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterTemplateService;
import com.zws.appeal.enums.SourceTypeEnum;
import com.zws.appeal.service.approve.ThreadServie;
import com.zws.appeal.utils.ShortLinkUtils;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.seal.SealStamp;
import com.zws.common.core.domain.seal.SignatureParam;
import com.zws.common.core.enums.SmsChannelEnumeration;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.signature.SealParameters;
import com.zws.common.core.signature.SignatureUtils;
import com.zws.common.core.signature.StampPosition;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.file.FileUtils;
import com.zws.common.core.utils.pdf.PdfUtils;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.RemoteLetterService;
import com.zws.system.api.domain.SysFile;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/6/11 17:08
 */
@Slf4j
@Service
public class ThreadServieImpl implements ThreadServie {


    @Autowired
    private ILetterDocService letterService;
    @Autowired
    private ILetterTemplateService templateService;
    @Autowired
    private RemoteFileService remoteFileService;
    @Autowired
    private LinkConfig linkConfig;

    @Autowired
    private IDocumentMessageService documentMessageService;
    @Autowired
    private RemoteLetterService remoteLetterService;
    @Override
    public void createSignFile(List<Long> ids, String type, LoginUser loginUser) {
        log.info("开始执行签章：{}",ids);
        if(ObjectUtils.isEmpty(ids) || CollectionUtils.isEmpty(ids)){
            return;
        }
        try {
            for (Long id : ids) {
                LetterDoc letter = new LetterDoc();
                if ("1".equals(type)) {
                    letter = letterService.getById(id);
                } else {
//                    BeanUtil.copyProperties(documentService.getById(id), letter);
                }
                createSing(letter, type);
            }
            List<Integer> letterIds = new ArrayList<>();
            if ("1".equals(type)) {
                letterIds = letterService.selectLetterIdByIds(ids);
            } else {
//                letterIds.addAll(documentService.selectLetterIdByIds(ids));
            }
            log.info("签章批次：{}",letterIds);
            //生成压缩文件包
            if ("1".equals(type)) {
                documentMessageService.createZipFile(letterIds,loginUser);
            } else {
//                documentMessageService.createZipFile(letterIds);
            }

            //更新函件批次的审批状态
//            log.info("type:{}", type);
//            if (type.equals("0")) {
//                documentMessageService.updateLetterMessageProce(letterIds);
//            } else {
////                documentMessageService.updateLetterMessageProce(letterIds);
//            }
        } catch (Exception e) {
            log.error("签章异常", e);
            throw new ServiceException("签章生成错误:" + e.getMessage());
        }
    }


    /**
     * 执行生成签章
     *
     * @param letter
     */
    private void createSing(LetterDoc letter, String type) {
        if (letter == null) {
            return;
        }
        File pdfFile = null;
        File hideInfo = null;
        try {
            Integer id = letter.getId().intValue();
            String previewUrl = letter.getPreviewUrl();
            if (!StringUtils.isEmpty(previewUrl) && letter.getSourceFileType().equals(SourceTypeEnum.Excel_Type.getCode())) {
                //如果预览url为空 重新生成预览文件
                if (type.equals("1")) {
                    previewUrl = letterService.createPreview(letter, null, false);
                } else {
//                    LetterDoc document = new LetterDoc();
//                    BeanUtil.copyProperties(letter, document);
//                    previewUrl = documentService.createPreview(document, null, false);
                }
                letter.setPreviewUrl(previewUrl);
            }
            //生成脱敏函件

            String hideInfoUrl = "";
            if (type.equals("1")) {
                hideInfoUrl = letterService.createPreview(letter, null, true);
            } else {
//                Document document = new Document();
//                BeanUtil.copyProperties(letter, document);
//                hideInfoUrl = documentService.createPreview(document, null, true);
            }


            pdfFile = FileDownloadUtils.downloadTempFile(previewUrl);
            hideInfo = FileDownloadUtils.downloadTempFile(hideInfoUrl);

            Integer templateId;
            String signPreviewUrl = "";
            String signHidePreviewUrl = "";
            if (type.equals("1")) {
                templateId = letterService.getTemplateId(id);
                LetterTemplate template = templateService.getById(templateId.longValue());
                signPreviewUrl = pdfSign(pdfFile, template);
                signHidePreviewUrl = pdfSign(hideInfo, template);
            } else {
//                templateId = documentService.getTemplateId(id);
//                DocumentTemplate template = documenttemplateService.getById(templateId);
//                LetterTemplate letterTemplate = new LetterTemplate();
//                BeanUtil.copyProperties(template,letterTemplate);
//                signPreviewUrl = pdfSign(pdfFile, letterTemplate);
//                signHidePreviewUrl = pdfSign(hideInfo, letterTemplate);
            }
            log.info("签章 letterId:{} signPreviewUrl:{} signHidePreviewUrl:{}",id,signPreviewUrl,signHidePreviewUrl);
            int pages = PdfUtils.getPdfPage(pdfFile);

            letter.setSignPreviewUrl(signPreviewUrl);
            letter.setDesensitizeUrl(signHidePreviewUrl);
            letter.setSignStatus(1);
            letter.setPreviewPages(pages);
            letter.setSignErrMsg("");
            //拼接访问脱敏文件链接
//            String uuid = UUID.randomUUID().toString();
            String uuid = ShortLinkUtils.generateShortUuid();
            letter.setRandomId(uuid);
            //String url = LetterConstants.URL+uuid;
            if (type.equals("1")) {
                String url = linkConfig.getDesensitizationUrl() + uuid;
                letter.setFileLink(url);
                letterService.updateById(letter);
            } else {
//                Document document = new Document();
//                BeanUtil.copyProperties(letter, document);
//                String url = linkConfig.getDocumentUrl() + uuid;
//                document.setFileLink(url);
//                documentService.updateById(document);
            }

        } catch (Exception e) {
            letter.setSignErrMsg(e.getMessage());
            if (type.equals("1")) {
                letterService.updateById(letter);
            } else {
//                Document document = new Document();
//                BeanUtil.copyProperties(letter, document);
//                log.info("签章后的url：++++++++++document：" + document.getSignPreviewUrl());
//                log.info("签章后的url：++++++++++letter：" + letter.getSignPreviewUrl());
//                documentService.updateById(document);
            }
        } finally {
            if (pdfFile != null) {
                FileDownloadUtils.deletedTempFile(pdfFile);
            }else if (hideInfo != null){
                FileDownloadUtils.deletedTempFile(hideInfo);
            }
        }
    }

    /**
     * PDF 签章
     *
     * @param pdfFile  PDF文件
     * @param template 模板
     * @return
     * @throws Exception
     */
    private String pdfSign(File pdfFile, LetterTemplate template) throws Exception {

        String pdfBase64 = FileUtils.getPDFBinary(pdfFile);
        List<SealStamp> positionDatas = JSONArray.parseArray(template.getPositionData(), SealStamp.class);
        SignatureParam signatureParam = new SignatureParam();
        signatureParam.setFileBase64(pdfBase64);
        signatureParam.setSealStamps(positionDatas);
        signatureParam.setPage(PdfUtils.getPdfPage(pdfFile));
        return remoteLetterService.doSignature(signatureParam);
//        String signFileBase64 = null;
//
//        //返回来的参数PSFBase64,如果还有第二个签章ID, 将作为入参继续进行签章！
//        log.info("开始执行 getParam 方法");
//        HashMap<String, SealParameters> hashMap = getParam(pdfFile, template.getPositionData());
//        log.info(" ======================== 执行签章方法 ========================");
//
//        Iterator<Map.Entry<String, SealParameters>> iterator = hashMap.entrySet().iterator();
//        while (iterator.hasNext()) {
//            Map.Entry<String, SealParameters> next = iterator.next();
//            SealParameters sealParameters = next.getValue();
//            //SealParameters sealParam = next.getValue();
//            if (signFileBase64 != null) {
//                sealParameters.setBase64File(signFileBase64);
//            }
//            //CA签章接口
//            signFileBase64 = SignatureUtils.doSignature(sealParameters);
//        }
//        MultipartFile multipartFile = Base64Utils.decoderBase64File(signFileBase64);
//        R<SysFile> r = remoteFileService.upload(multipartFile);
//        if (r.getCode() == R.SUCCESS) {
//            log.info("签章文件上传响应URL：" + JSON.toJSONString(r.getData()));
//            return r.getData().getUrl();
//        } else {
//            throw new ServiceException("文件保存异常：" + r.getMsg());
//        }
    }

//    /**
//     * PDF 签章
//     *
//     * @param pdfFile  PDF文件
//     * @param template 模板
//     * @return
//     * @throws Exception
//     */
//    private String pdfSign(File pdfFile, LetterTemplate template) throws Exception {
//
//
//        String signFileBase64 = null;
//
//        //返回来的参数PSFBase64,如果还有第二个签章ID, 将作为入参继续进行签章！
//        log.info("开始执行 getParam 方法");
//        HashMap<String, SealParameters> hashMap = getParam(pdfFile, template.getPositionData());
//        log.info(" ======================== 执行签章方法 ========================");
//
//        Iterator<Map.Entry<String, SealParameters>> iterator = hashMap.entrySet().iterator();
//        while (iterator.hasNext()) {
//            Map.Entry<String, SealParameters> next = iterator.next();
//            SealParameters sealParameters = next.getValue();
//            //SealParameters sealParam = next.getValue();
//            if (signFileBase64 != null) {
//                sealParameters.setBase64File(signFileBase64);
//            }
//            //CA签章接口
//            signFileBase64 = SignatureUtils.doSignature(sealParameters);
//        }
//        MultipartFile multipartFile = Base64Utils.decoderBase64File(signFileBase64);
//        R<SysFile> r = remoteFileService.upload(multipartFile);
//        if (r.getCode() == R.SUCCESS) {
//            log.info("签章文件上传响应URL：" + JSON.toJSONString(r.getData()));
//            return r.getData().getUrl();
//        } else {
//            throw new ServiceException("文件保存异常：" + r.getMsg());
//        }
//    }


    /**
     * 获取参数 签章参数
     *
     * @param pdfFile      pdf文件对象
     * @param positionData 定位对象
     * @return
     */
    private HashMap<String, SealParameters> getParam(File pdfFile, String positionData) {

        if (StringUtils.isEmpty(positionData)) {
            throw new ServiceException("模板定位JSON数据为空");
        }

        int pages = PdfUtils.getPdfPage(pdfFile);
        String pdfBase64 = FileUtils.getPDFBinary(pdfFile);

        List<StampPosition> positionDatas = JSONArray.parseArray(positionData, StampPosition.class);
        HashMap<String, SealParameters> map = new HashMap<>();
        for (StampPosition position : positionDatas) {
            String signerId = position.getSignerId();
            int pageNum = position.getPageNum();
            if (map.containsKey(signerId) && pageNum <= pages) {
                SealParameters parameters = map.get(signerId);
                List<StampPosition> stampPositionList = parameters.getStampPositionList();
                stampPositionList.add(position);
                parameters.setStampPositionList(stampPositionList);
                continue;
            }

            List<StampPosition> list = new ArrayList<>();
            if (position.getPageNum() <= pages) {
                list.add(position);
            }

            Long signId = position.getSignId();
            if (signId == null && position.getLawId() != null) {
                signId = position.getLawId();
            } else if (signId == null && position.getLawId() == null) {
                throw new ServiceException("签章Id为空,无法进行签章 定位数据：" + JSON.toJSONString(positionData));
            }
            String base64Str1 = getBase64Str(signId);

            SealParameters parameters = new SealParameters();
            parameters.setSealImage(base64Str1);
            parameters.setStampPositionList(list);
            parameters.setBase64File(pdfBase64);
            parameters.setSignerId(signerId);
            map.put(signerId, parameters);
        }

        return map;
    }


    /**
     * 获取签章图片base64格式
     */
    private String getBase64Str(Long signIdStr) {
        Integer signId = Integer.valueOf(signIdStr.toString());
        LawSign option = templateService.getSignPicBase64(signId);
        String imageBase64 = null;
        if (StringUtils.isEmpty(option.getSignPicStr())) {
            imageBase64 = Base64Utils.getImgUrlToBase64(option.getSignPic().toString());
            //更新设置签章base64格式
            LawSign lawSign = new LawSign();
            lawSign.setId(signId.longValue());
            lawSign.setSignPicStr(imageBase64);
            int i = templateService.updateLawSignById(lawSign);
        } else {
            imageBase64 = option.getSignPicStr();
        }
        return imageBase64;
    }




}
