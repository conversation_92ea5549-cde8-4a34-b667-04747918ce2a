package com.zws.appeal.service.appeal.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.zws.appeal.agservice.AgLawsuitService;
import com.zws.appeal.mapper.appeal.ExecuteCaseMapper;
import com.zws.appeal.pojo.appeal.ExecuteCasePojo;
import com.zws.appeal.pojo.appeal.FilingCasePojo;
import com.zws.appeal.service.appeal.IExecuteCaseService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.enums.TimeContentFormats;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import javafx.scene.input.DataFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.zws.appeal.domain.appeal.ExecuteCase;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
*编辑名称
*@Author：liuxifeng
*@Date：2024/6/25  16:36
*@Describe：编辑描述
*/
@Service
public class ExecuteCaseServiceImpl implements IExecuteCaseService {

    @Resource
    private ExecuteCaseMapper executeCaseMapper;
    @Autowired
    private AgLawsuitService agLawsuitService;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return executeCaseMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(ExecuteCase record) {
        return executeCaseMapper.insert(record);
    }

    @Override
    public int insertSelective(ExecuteCase record) {
        return executeCaseMapper.insertSelective(record);
    }

    @Override
    public ExecuteCase selectByPrimaryKey(Long id) {
        return executeCaseMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ExecuteCase record) {
        return executeCaseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ExecuteCase record) {
        return executeCaseMapper.updateByPrimaryKey(record);
    }

    @Override
    public int batchInsert(List<ExecuteCase> list) {
        return executeCaseMapper.batchInsert(list);
    }

    @Override
    public List<ExecuteCasePojo> selectList(ExecuteCasePojo pojo) {
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        pojo.setOdvId(TokenInformation.getUserid());
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        List<ExecuteCasePojo> list = executeCaseMapper.selectList(pojo);
        for (ExecuteCasePojo executeCasePojo : list) {
            executeCasePojo.setClientName(FieldEncryptUtil.decrypt(executeCasePojo.getClientName()));
            executeCasePojo.setClientCensusRegister(FieldEncryptUtil.decrypt(executeCasePojo.getClientCensusRegister()));
            executeCasePojo.setClientIdcard(FieldEncryptUtil.decrypt(executeCasePojo.getClientIdcard()));
            executeCasePojo.setPhone(FieldEncryptUtil.decrypt(executeCasePojo.getPhone()));
        }
        return list;
    }

    @Override
    public List<Long> selectCaseIds(ExecuteCasePojo pojo) {
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        return executeCaseMapper.selectCaseIds(pojo);
    }

    @Override
    public Map<String, Object> selectWithMoney(ExecuteCasePojo pojo) {
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        return executeCaseMapper.selectWithMoney(pojo);
    }

    @Override
    public int batchExecute(ExecuteCasePojo pojo) {
        //将案件ID 集合传入 判断全选参数
        pojo.setTeamId(TokenInformation.getCreateid().longValue());
        List<Long> ids = null;
        if (pojo.getAllQuery()){
            ids = this.selectCaseIds(pojo);
        }else {
            ids = pojo.getIds();
        }
        if (ids==null || ids.size()==0){ throw new ServiceException("案件数量为0，执行失败"); }

        ExecuteCase executeCase = pojo.getExecuteCase();

        if (executeCase==null ){throw new ServiceException("执行信息不能为空"); }
        List<Long> list = executeCaseMapper.selectWithCaeId(ids);

        //将实体类传入
        Long userId = Long.valueOf(TokenInformation.getUserid().toString());
        String username = TokenInformation.getUsername();
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        Date now = new Date();
        executeCase.setCreateBy(username);
        executeCase.setCreateById(userId);
        executeCase.setCreateTime(now);
        executeCase.setDelFlag(BaseConstant.DelFlag_Being);
        executeCase.setTeamId(teamId);
        if (list==null || list.size()==0){
            //新增
            for (Long aLong : ids) {
                ExecuteCase aCase = new ExecuteCase();
                BeanUtil.copyProperties(executeCase,aCase);
                aCase.setCaseId(aLong);
                executeCaseMapper.insertSelective(aCase);
            }
            return ids.size();
        }

        //待新增ids
        List<Long> addIds = (List<Long>) CollUtil.subtract(ids, list);
        //待更新ids
        List<Long> updateIds = (List<Long>) CollUtil.subtract(ids, addIds);

        addIds.forEach(s->{
            ExecuteCase aCase = new ExecuteCase();
            BeanUtil.copyProperties(executeCase,aCase);
            aCase.setCaseId(s);
            executeCaseMapper.insertSelective(aCase);
        });

        updateIds.forEach(s->{
            ExecuteCase aCase = new ExecuteCase();
            BeanUtil.copyProperties(executeCase,aCase);
            aCase.setCaseId(s);
            //根据caseId、 进行更新
            int i = executeCaseMapper.updateByCaseId(aCase);
        });
        //写入时效记录

        LoginUser loginUser = SecurityUtils.getLoginUser();
        agLawsuitService.addTimeManage(loginUser,ids, TimeContentFormats.BATCH_EXECUTE);

        return 0;
    }



}
