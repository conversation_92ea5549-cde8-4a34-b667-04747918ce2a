package com.zws.appeal.service.appeal;


import com.zws.appeal.domain.appeal.CaseCostRecord;

import java.util.List;

/**
 * 案件-收费记录
 * <AUTHOR>
 * @date ：Created in 2023/12/4 17:54
 */
public interface ICaseCostRecordService {

    /**
     * 创建
     * @param record
     * @return
     */
    Long insert(CaseCostRecord record);

    /**
     * 主键选择更新
     * @param record
     */
    void updateById(CaseCostRecord record);

    /**
     * 主键删除
     * @param id
     */
    void deleteById(Long id);

    /**
     * 主键查询
     * @param id
     * @return
     */
    CaseCostRecord getById(Long id);

    /**
     * 条件查询列表
     * @param record
     * @return
     */
    List<CaseCostRecord> selectList(CaseCostRecord record);

    /**
     * 开庭缴费
     * @param record
     */
    void insertList(CaseCostRecord record);
}
