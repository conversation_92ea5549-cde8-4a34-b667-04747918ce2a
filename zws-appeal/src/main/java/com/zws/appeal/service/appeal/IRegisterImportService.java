package com.zws.appeal.service.appeal;

import java.util.List;
import com.zws.appeal.domain.appeal.RegisterImport;
    /**
*编辑名称
*@Author：liu<PERSON><PERSON>
*@Date：2024/6/21  16:39
*@Describe：编辑描述
*/
public interface IRegisterImportService {


    int deleteByPrimaryKey(Long id);

    int insert(RegisterImport record);

    int insertSelective(RegisterImport record);

    RegisterImport selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RegisterImport record);

    int updateByPrimaryKey(RegisterImport record);

    int batchInsert(List<RegisterImport> list);

}
