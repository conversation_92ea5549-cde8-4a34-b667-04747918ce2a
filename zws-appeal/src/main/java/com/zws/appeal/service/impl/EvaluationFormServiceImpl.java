package com.zws.appeal.service.impl;

import cn.hutool.core.date.DateUtil;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.PageUtils;
import com.zws.appeal.domain.Evaluate;
import com.zws.appeal.domain.EvaluationForm;
import com.zws.appeal.mapper.EvaluationFormMapper;
import com.zws.appeal.mapper.RecordMapper;
import com.zws.appeal.mapper.TeamMapper;
import com.zws.appeal.pojo.EvaluationFormPojo;
import com.zws.appeal.pojo.TimeIntervalPojo;
import com.zws.appeal.service.EvaluationFormService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 评估表服务实现类
 *
 * @Author: 马博新
 * @DATE: Created in 2022/9/13 20:15
 */
@Service
@Primary
public class EvaluationFormServiceImpl implements EvaluationFormService {

    @Resource
    private EvaluationFormMapper evaluationFormMapper;
    @Resource
    private TeamMapper teamMapper;
    @Resource
    private RecordMapper recordMapper;


    /**
     * 根据条件查询构评价记录
     *
     * @param evaluationFormPojo
     * @return
     */
    @Override
    public List<EvaluationForm> selectEvaluationForm(EvaluationFormPojo evaluationFormPojo) {
        Date begin = DateUtils.beginOfDay(evaluationFormPojo.getGenerationDate1());
        Date end = DateUtils.endOfDay(evaluationFormPojo.getGenerationDate2());
        evaluationFormPojo.setGenerationDate1(begin);
        evaluationFormPojo.setGenerationDate2(end);
        return evaluationFormMapper.selectEvaluationForm(evaluationFormPojo);
    }

    /**
     * 写入机构评价记录
     *
     * @param evaluationForm
     * @return
     */
    @Override
    public int insertEvaluationForm(EvaluationForm evaluationForm) {
        return evaluationFormMapper.insertEvaluationForm(evaluationForm);
    }

    /**
     * 根据时间区间以及团队id查询该团队的评价信息
     *
     * @param timeIntervalPojo
     * @return
     */
    @Override
    public Evaluate selectEvaluate(TimeIntervalPojo timeIntervalPojo) {
//                一天的开始，结果：2017-03-01 00:00:00
        Date beginOfDay = DateUtil.beginOfDay(timeIntervalPojo.getRecordTime1());
        timeIntervalPojo.setStartTime(beginOfDay);
//                一天的结束，结果：2017-03-01 23:59:59
        Date endOfDay = DateUtil.endOfDay(timeIntervalPojo.getRecordTime2());
        timeIntervalPojo.setEndTime(endOfDay);
        Evaluate evaluate = teamMapper.selectEvaluateByTeamId(timeIntervalPojo);
        if (ObjectUtils.isEmpty(evaluate)) {
            throw new GlobalException("查询错误");
        } else {
            BigDecimal collectedPrincipal = evaluate.getCollectedPrincipal();  //累计回款本金
            BigDecimal entrustedAmount = evaluate.getEntrustedAmount();  //原始不良本金余额
            if (entrustedAmount.compareTo(BigDecimal.ZERO) == 0) {
                evaluate.setCollectionRate("0.00%");
            } else {
                BigDecimal divide = collectedPrincipal.multiply(new BigDecimal(100)).divide(entrustedAmount, 2, BigDecimal.ROUND_HALF_UP);
                evaluate.setCollectionRate(divide + "%");
            }
            TimeIntervalPojo timeIntervalPojo1 = new TimeIntervalPojo();
            timeIntervalPojo1.setRecordTime1(beginOfDay);
            timeIntervalPojo1.setRecordTime2(endOfDay);
            timeIntervalPojo1.setTeamId(evaluate.getTeamId());
            PageUtils.startPage();
            int number = recordMapper.selectOutsideRecordIdByTime(timeIntervalPojo1);  //外访申请数量
            if (evaluate.getCaseNumber() == 0) {
                evaluate.setCountryCoverage("0.00%");
            } else {
                BigDecimal divide = new BigDecimal(number * 100).divide(new BigDecimal(evaluate.getCaseNumber()), 2, BigDecimal.ROUND_HALF_UP);
                evaluate.setCountryCoverage(divide + "%");
            }
            return evaluate;
        }
    }
}
