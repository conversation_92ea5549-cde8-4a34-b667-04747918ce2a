package com.zws.appeal.service.appeal;

import com.zws.appeal.controller.request.MaterialDeliveryRequest;
import com.zws.appeal.controller.response.MaterialDeliveryCountResp;
import com.zws.appeal.controller.response.*;
import com.zws.appeal.domain.DeliveryRanking;
import com.zws.appeal.pojo.Option;
import com.zws.appeal.pojo.TreeType;
import com.zws.appeal.pojo.appeal.*;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;

import java.util.List;
import java.util.Map;

public interface ITeamAppealService {

    List<TreeType> DeptTreeType(PhoneMediationPojo phoneMediationPojo);

    List<DeliveryRanking> materialDeliveryRanking();

    /**
     * 查询调解案件
     * @param phoneMediationPojo
     * @return
     */
    List<PhoneMediationPojo> getPhoneMediation(PhoneMediationPojo phoneMediationPojo, LoginUser loginUser);

    List<FilingCasePojo> selectFillingList(FilingCasePojo pojo, LoginUser loginUser);

    Map<String, Object> selectWithMoney(FilingCasePojo pojo, LoginUser loginUser);

    List<LawInforPojo> getSessionList(LawInforPojo lawInforPojo, LoginUser loginUser);

    List<FreezeCasePojo> selectFreezeList(FreezeCasePojo pojo, LoginUser loginUser);

    List<MaterialDeliveryResp> selectMaterialDeliveryList(MaterialDeliveryRequest pojo, LoginUser loginUser);

    Map<String, Object> selectFreezeWithMoney(FreezeCasePojo pojo, LoginUser loginUser);

    Map<String, Object> selectMediationWithMoney(PhoneMediationPojo phoneMediationPojo, LoginUser loginUser);

    Map<String, Object> selectSessionWithMoney(LawInforPojo lawInforPojo, LoginUser loginUser);

    List<JudgePojo> getJudgeList(JudgePojo judgePojo, LoginUser loginUser);

    Map<String, Object> selectJudgeWithMoney(JudgePojo judgePojo, LoginUser loginUser);

    List<ExecuteCasePojo> selectExecuteList(ExecuteCasePojo pojo, LoginUser loginUser);

    Map<String, Object> selectExecuteMoney(ExecuteCasePojo pojo, LoginUser loginUser);

    MaterialDeliveryCountResp selectMaterialDeliveryMoney(MaterialDeliveryRequest materialDeliveryRequest, LoginUser loginUser);

    List<TreeType> DeptTreeWithDisposeStage(PhoneMediationPojo phoneMediationPojo);

    List<TreeType> DeptTreeWithSaveStage(PhoneMediationPojo phoneMediationPojo);

    List<TreeType> DeptTreeWithExecute(PhoneMediationPojo phoneMediationPojo);

    List<Option> getSum(LoginUser loginUser,MaterialDeliveryRequest pojo);

}
