package com.zws.appeal.service.appeal.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zws.appeal.agservice.AgLawsuitService;
import com.zws.appeal.domain.Employees;
import com.zws.appeal.domain.appeal.CourtSession;
import com.zws.appeal.domain.appeal.FilingCase;
import com.zws.appeal.domain.appeal.JudgeInfor;
import com.zws.appeal.mapper.SettingsMapper;
import com.zws.appeal.mapper.appeal.FilingCaseMapper;
import com.zws.appeal.mapper.appeal.JudgeInforMapper;
import com.zws.appeal.pojo.Option;
import com.zws.appeal.pojo.appeal.FilingCasePojo;
import com.zws.appeal.pojo.appeal.JudgePojo;
import com.zws.appeal.pojo.appeal.LawInforPojo;
import com.zws.appeal.service.appeal.IJudgeResultService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.enums.TimeContentFormats;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;

/**
 * 判决与结果
 * <AUTHOR>
 * @date 2024/6/25 15:34
 */
@Service
public class JudgeResultServiceImpl implements IJudgeResultService {

    @Autowired
    private JudgeInforMapper judgeInforMapper;
    @Autowired
    private FilingCaseMapper filingCaseMapper;
    @Autowired
    private SettingsMapper settingsMapper;
    @Autowired
    private AgLawsuitService agLawsuitService;


    /**
     * 查询
     * @param judgePojo
     * @return
     */
    @Override
    public List<JudgePojo> getJudgeList(JudgePojo judgePojo) {
        if (!ObjectUtils.isEmpty(judgePojo.getJudgeTime2())) {
            Date dateTime = DateUtil.endOfDay(judgePojo.getJudgeTime2());
            judgePojo.setJudgeTime2(dateTime);
        }
//        judgePojo.setCaseIds(getCaseIds(judgePojo));
        judgePojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        judgePojo.setOdvId(TokenInformation.getUserid());
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        judgePojo.setTeamId(teamId);
        List<JudgePojo> list = judgeInforMapper.getJudgeList(judgePojo);
        for (JudgePojo pojo : list) {
            pojo.setClientName(FieldEncryptUtil.decrypt(pojo.getClientName()));
            pojo.setClientPhone(FieldEncryptUtil.decrypt(pojo.getClientPhone()));
            pojo.setClientCensusRegister(FieldEncryptUtil.decrypt(pojo.getClientCensusRegister()));
            pojo.setClientIdNum(FieldEncryptUtil.decrypt(pojo.getClientIdNum()));
        }
        return list;
    }

    /**
     * 新增判决
     * @param judgeInfor
     */
    @Override
    public void addJudge(JudgeInfor judgeInfor) {
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        judgeInfor.setOdvId(TokenInformation.getUserid());
        judgeInfor.setTeamId(teamId);
        List<Long> caseIds = null;
        if (judgeInfor.getAllQuery()){
            //搜索结果全选
            caseIds = judgeInforMapper.getAllList(judgeInfor);
            for (Long caseId : caseIds) {
                //存在就更新，不存在就插入
                if(judgeInforMapper.selectCaseId(caseId) == null){
                    judgeInfor.setId(null);
                    judgeInfor.setCaseId(caseId);
                    judgeInfor.setDelFlag("0");
                    judgeInfor.setCreateTime(new Date());
                    judgeInfor.setUpdateTime(new Date());
                    judgeInfor.setTeamId(SecurityUtils.getTeamId());
                    judgeInfor.setCreateBy(SecurityUtils.getUsername());
                    judgeInfor.setUpdateBy(SecurityUtils.getUsername());
                    judgeInforMapper.insertSelective(judgeInfor);
                }else {
                    judgeInfor.setId(null);
                    judgeInfor.setUpdateTime(new Date());
                    judgeInfor.setUpdateBy(SecurityUtils.getUsername());
                    judgeInforMapper.updateByCaseId(judgeInfor);
                }
            }
        }else {
            caseIds = judgeInfor.getCaseIds();
            for (Long caseId : caseIds) {
                if(judgeInforMapper.selectCaseId(caseId) == null){
                    judgeInfor.setId(null);
                    judgeInfor.setCaseId(caseId);
                    judgeInfor.setDelFlag("0");
                    judgeInfor.setCreateTime(new Date());
                    judgeInfor.setUpdateTime(new Date());
                    judgeInfor.setTeamId(SecurityUtils.getTeamId());
                    judgeInfor.setCreateBy(SecurityUtils.getUsername());
                    judgeInfor.setUpdateBy(SecurityUtils.getUsername());
                    judgeInforMapper.insertSelective(judgeInfor);
                }else {
                    judgeInfor.setId(null);
                    judgeInfor.setCaseId(caseId);
                    judgeInfor.setUpdateTime(new Date());
                    judgeInfor.setUpdateBy(SecurityUtils.getUsername());
                    judgeInforMapper.updateByCaseId(judgeInfor);
                }
            }
        }
        //写入时效记录
        LoginUser loginUser = SecurityUtils.getLoginUser();
        agLawsuitService.addTimeManage(loginUser,caseIds, TimeContentFormats.JUDGMENT);

    }

    @Override
    public List<Long> getCaseIds(JudgePojo judgePojo) {
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        if(ObjectUtils.isEmpty(judgePojo.getTeamId())){
            judgePojo.setTeamId(teamId);
        }
        if(ObjectUtils.isEmpty(judgePojo.getOdvId())){
            judgePojo.setOdvId(TokenInformation.getUserid());
        }
        List<Long> caseIds = null;
        if (judgePojo.getAllQuery()){
            caseIds = judgeInforMapper.getCaseIds(judgePojo);
        }else {
            caseIds = judgePojo.getCaseIds();
        }
        return caseIds;
    }

    @Override
    public List<Long> selectCaseIds(Map<String, Object> map) {
        return judgeInforMapper.selectCaseIds(map);
    }

    @Override
    public void addAplayCase(FilingCasePojo pojo) {
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setOdvId(TokenInformation.getUserid());
        pojo.setTeamId(teamId);
        List<Long> ids = null;
        if (pojo.getAllQuery()){
            //搜索结果全选
            Map<String, Object> map = BeanUtil.beanToMap(pojo);
            ids = selectCaseIds(map);
        } else {
            //取出所有的 案件ID集合
            ids = pojo.getIds();
        }

        if (ObjectUtil.isNull(ids)){
            throw new GlobalException("所选案件列表数据为空!");
        }

        FilingCase filingCase = new FilingCase();
        filingCase.setCourt(pojo.getCourt());
        filingCase.setFilingNumber(pojo.getFilingNumber());
        filingCase.setFilingTime(pojo.getFilingTime());
        filingCase.setClerk(pojo.getClerk());
        filingCase.setContractor(pojo.getContractor());
        filingCase.setType(1);
        //查询案件id是否存在，存在更新，不存在就插入
        for (Long id : ids) {
            if (filingCaseMapper.selectCase(id) == null){
                filingCase.setCreateBy(SecurityUtils.getUsername());
                filingCase.setDelFlag("0");
                filingCase.setCreateById(SecurityUtils.getUserId());
                filingCase.setCreateTime(new Date());
                //更新案件状态
                filingCaseMapper.updateCaseStage(id);
                filingCaseMapper.insert(filingCase);
            }else {
                filingCase.setUpdateBy(SecurityUtils.getUsername());
                filingCase.setUpdateById(SecurityUtils.getUserId());
                filingCase.setUpdateTime(new Date());
                //更新案件状态
                filingCaseMapper.updateCaseStage(id);
                filingCaseMapper.updateByPrimaryKeySelective(filingCase);
            }
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        agLawsuitService.addTimeManage(loginUser,ids,TimeContentFormats.APPLY_CASE);
    }

    @Override
    public List<Option> getEmployees() {
        int i = 1;
        int createId = Math.toIntExact(SecurityUtils.getTeamId());
        List<Employees> list = settingsMapper.selectUserByCreateId(createId);
        List<Option> list1 = new ArrayList<>();
        for (Employees employees : list) {
            Option option = new Option();
            option.setCode(i);
            option.setInfo(employees.getEmployeeName());
            list1.add(option);
            i++;
        }
        return list1;
    }

    @Override
    public int batchConcludeCase(JudgePojo judgePojo) {
        judgePojo.setTeamId(TokenInformation.getCreateid().longValue());
        judgePojo.setOdvId(TokenInformation.getUserid());
        List<Long> ids = null;
        if (judgePojo.getAllQuery()){
            ids = judgeInforMapper.getCaseIds(judgePojo);
        }else {ids = judgePojo.getCaseIds();}

        if (ids==null || ids.size()==0){throw new ServiceException("案件id不存在,批量结案失败"); }
        Date now = new Date();
        String username = TokenInformation.getUsername();
        Long teamId = TokenInformation.getCreateid().longValue();
        ids.forEach(s->{
            if(judgeInforMapper.selectCaseId(s) == null){
                JudgeInfor infor = new JudgeInfor();
                infor.setLastStatus(judgePojo.getLastStatus());
                infor.setUndertakingLawyer(judgePojo.getUndertakingLawyer());
                infor.setCaseId(s);
                infor.setDelFlag(BaseConstant.DelFlag_Being);
                infor.setCreateTime(now);
                infor.setCreateBy(username);
                infor.setTeamId(teamId);
                judgeInforMapper.insertSelective(infor);
            }else {
                JudgeInfor infor = new JudgeInfor();
                infor.setCaseId(s);
                infor.setLastStatus(judgePojo.getLastStatus());
                infor.setUndertakingLawyer(judgePojo.getUndertakingLawyer());
                infor.setUpdateTime(now);
                infor.setUpdateBy(username);
                judgeInforMapper.updateByCaseId(infor);
            }
        });
        LoginUser loginUser = SecurityUtils.getLoginUser();
        agLawsuitService.addTimeManage(loginUser,ids,TimeContentFormats.BATCH_CLOSED);
        return 0;
    }


    /**
     * 导入校验
     *
     * @param judgeInfor
     * @return
     */
    public Map<String, Object> checkInfor(JudgeInfor judgeInfor){
        Map<String, Object> map1= new HashMap<>();
        if (judgeInfor.getCaseId()==null){
            map1.put("案件ID","案件id不能为空");
        }
        if (judgeInfor.getCaseStatus()==null || judgeInfor.getCaseStatus()==""){
            map1.put("判决状态","判决状态不能为空");
        }
        if (judgeInfor.getJudgeLetter()==null || judgeInfor.getJudgeLetter()==""){
            map1.put("判决文书","判决文书不能为空");
        }
        if (judgeInfor.getJudgeResult()==null || judgeInfor.getJudgeResult()==""){
            map1.put("判决结果","判决结果不能为空");
        }
        if (judgeInfor.getJudgeSum()==null){
            map1.put("判决金额","判决金额不能为空");
        }
        if (judgeInfor.getCourierNum()==null || judgeInfor.getCourierNum()==""){
            map1.put("快递单号","快递单号不能为空");
        }
        if (judgeInfor.getJudgeTime()==null){
            map1.put("判决时间","判决时间不能为空");
        }
        if (judgeInfor.getJudgeContent()==null || judgeInfor.getJudgeContent()==""){
            map1.put("判决描述","判决描述不能为空");
        }
        return map1;
    }
}
