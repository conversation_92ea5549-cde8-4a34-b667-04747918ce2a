package com.zws.appeal.service;

import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.pojo.ExportReminder;
import com.zws.appeal.pojo.QueryTimeInterval;

import java.util.List;
import java.util.Map;

public interface RuleDivisionService {

    /**
     * 根据条件或者id集合查询案件信息
     *
     * @return
     */
    List<CaseManage> selectCaseManages(ExportReminder exportReminder);

    /**
     * 根据条件或者id集合查询案件金额总和
     *
     * @return
     */
    Map<String, Object> selectCaseManageMoneyCount(ExportReminder exportReminder);

    /**
     * 根据条件或者id集合查询案件信息-(查询共债人身份证号信息)
     *
     * @return
     */
    List<String> selectCaseClientIdcard(ExportReminder exportReminder);

    /**
     * 根据条件或者id集合查询案件信息-(查询非共债人身份证号信息)
     *
     * @return
     */
    List<String> selectCaseClientIdcardOne(ExportReminder exportReminder);

    /**
     * 根据身份证号查询共债案件信息
     *
     * @return
     */
    List<CaseManage> selectCaseManageClientIdcard(ExportReminder exportReminder);

    /**
     * 根据身份证号查询非共债案件信息
     *
     * @return
     */
    List<CaseManage> selectCaseManageClientIdcardOne(ExportReminder exportReminder);

    /**
     * 根据身份证号查询共债案件委托金额总和
     *
     * @return
     */
    Map<String, Object> selectCaseManageMoney(ExportReminder exportReminder);

    /**
     * 根据条件查询案件操作信息
     *
     * @return
     */
    List<Long> selectDistributionHistory(QueryTimeInterval queryTimeInterval);
}
