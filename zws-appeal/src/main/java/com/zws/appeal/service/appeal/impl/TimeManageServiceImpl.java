package com.zws.appeal.service.appeal.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.zws.appeal.mapper.appeal.TimeManageMapper;
import com.zws.appeal.pojo.appeal.ManageDTO;
import com.zws.appeal.service.appeal.ITimeMangeService;
import com.zws.common.core.domain.TimeManage;
import com.zws.common.core.exception.GlobalException;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Primary
public class TimeManageServiceImpl implements ITimeMangeService {
    @Resource
    private TimeManageMapper timeManageMapper;

    /**
     * 新增案件跟进记录
     *
     * @param timeManage
     * @return
     */
    @Override
    public int insertInfoContact(TimeManage timeManage) {
        if(ObjectUtils.isEmpty(timeManage.getCaseId())){
            throw new GlobalException("案件id不能为空");
        }
        //timeManageMapper.updateCaseTime(timeManage);
        //调诉端只进行时效记录
        return timeManageMapper.insertInfoContact(timeManage);
    }

    @Override
    public int batchInsert(List<TimeManage> list) {
        return timeManageMapper.batchInsert(list);
    }

    //    @Scheduled(cron = "0 0 1 * * ?")
    @Override
    public void updateTimeBatch() {
        //查询案件时效表 已跟进过的案件信息
        List<ManageDTO> manageDTO = timeManageMapper.selectCaseTimeed();
        if (!ObjectUtils.isEmpty(manageDTO)) {
            for (ManageDTO dto : manageDTO) {
                Date date = new Date();
                long between = DateUtil.between(dto.getLastContactTime(), date, DateUnit.DAY);
                dto.setResidueTime(between);
                //更新时效天数
                timeManageMapper.updateCaseTimeed(dto);
            }
        }
        //查询未跟进过的案件id
        List<ManageDTO> manage = timeManageMapper.selectCaseTimeNo();
        if (!ObjectUtils.isEmpty(manage)) {

            for (ManageDTO dto : manage) {
                // 根据案件id查询逾期时间
                ManageDTO mangeResp = timeManageMapper.selectOverdueStart(dto.getCaseId());
                //计算 更新时效天数
                if (!ObjectUtils.isEmpty(mangeResp.getClientOverdueStart())) {
                    long time = System.currentTimeMillis();
                    long time1 = mangeResp.getClientOverdueStart().getTime();
                    if (mangeResp.getSettlementStatus() == null || mangeResp.getSettlementStatus() == 1) {
                        dto.setResidueTime(0L);
                    } else {
                        if (time1 >= time) {
                            dto.setResidueTime(0L);
                        } else {
                            long between = DateUtil.between(mangeResp.getClientOverdueStart(), new Date(), DateUnit.DAY);
                            dto.setResidueTime(between);
                        }
                    }
                }
                   //更新时效天数
                timeManageMapper.updateCaseTimeed(dto);
            }
        }
    }
    @Override
    public void addCaseToTime(){
        // 查询案件表
        List<Long> caseIds = timeManageMapper.selectCaseId();
        if(!ObjectUtils.isEmpty(caseIds)){
            //根据案件id查询跟进记录表
            for (Long caseId : caseIds) {
                ManageDTO manageDTO = timeManageMapper.selectAssetTimeById(caseId);
                TimeManage timeManage = new TimeManage();
               if(!ObjectUtils.isEmpty(manageDTO)){
                   timeManage.setCaseId(caseId);
                   timeManage.setCreateTime(manageDTO.getLastContactTime());
                   timeManage.setContactInformation(manageDTO.getLastInformation());
                   timeManage.setContactStateContent(manageDTO.getLastStateContent());
                   Date date = new Date();
                   long between = DateUtil.between(manageDTO.getLastContactTime(), date, DateUnit.DAY);
                   timeManage.setResidueTime(between);
               }else {
                   timeManage.setCaseId(caseId);
                   // 根据案件id查询逾期时间
                   ManageDTO mangeResp = timeManageMapper.selectOverdueStart(caseId);
                   //计算 更新时效天数
                   if (!ObjectUtils.isEmpty(mangeResp.getClientOverdueStart())) {
                       long time = System.currentTimeMillis();
                       long time1 = mangeResp.getClientOverdueStart().getTime();
                       if (mangeResp.getSettlementStatus() == null || mangeResp.getSettlementStatus() == 1) {
                           timeManage.setResidueTime(0L);
                       } else {
                           if (time1 >= time) {
                               timeManage.setResidueTime(0L);
                           } else {
                               long between = DateUtil.between(mangeResp.getClientOverdueStart(), new Date(), DateUnit.DAY);
                               timeManage.setResidueTime(between);
                           }
                       }
                   }
               }
                // 新增时效表数据
                timeManageMapper.insertCaseTime(timeManage);
            }

        }
    }

}
