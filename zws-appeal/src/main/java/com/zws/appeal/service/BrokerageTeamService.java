package com.zws.appeal.service;

import com.zws.appeal.domain.BrokerageTeam;
import com.zws.appeal.domain.DetailsBrokerage;
import com.zws.appeal.domain.RecoveryAsset;
import com.zws.appeal.domain.RiskTeam;

import java.math.BigDecimal;
import java.util.List;

/**
 * 结算中心机构佣金信息
 *
 * @Author: 马博新
 * @DATE: Created in 2023/4/23 18:12
 */
public interface BrokerageTeamService {

    /**
     * 查询结算中心机构佣金信息
     *
     * @param brokerageTeam
     * @return
     */
    List<BrokerageTeam> selectAccountById(BrokerageTeam brokerageTeam);

//    /**
//     * 根据机构佣金信息主键id集合查询机构名称
//     *
//     * @param idList
//     * @return
//     */
//    List<String> selectById(@Param("idList") List<Long> idList);

    /**
     * 根据团队id以及结佣日期（年/月）查询结算中心机构结佣信息
     *
     * @param brokerageTeam
     * @return
     */
    List<BrokerageTeam> selectTeamBrokerage(BrokerageTeam brokerageTeam);

    /**
     * 根据佣金表主键id查询团队名称以及结佣日期（年/月）
     *
     * @param id
     * @return
     */
    BrokerageTeam selectFileName(Long id);

    /**
     * 根据佣金表id以及状态查询机构佣金详情表
     *
     * @param detailsBrokerage
     * @return
     */
    List<DetailsBrokerage> selectDetailsBrokerage(DetailsBrokerage detailsBrokerage);

    /**
     * 根据佣金详情表id查询资产回收情况
     *
     * @param detailsId
     * @return
     */
    List<RecoveryAsset> selectRecoveryAsset(Long detailsId);

    /**
     * 根据佣金详情表id查询机构风险奖罚设置
     *
     * @param detailsId
     * @return
     */
    List<RiskTeam> selectRiskTeam(Long detailsId);

    /**
     * 根据佣金详情表id查询统计资产回收情况回款金额
     *
     * @param detailsId
     * @return
     */
    BigDecimal selectCount(Long detailsId);

//    /**
//     * 新增结算中心机构佣金信息
//     *
//     * @param brokerageTeam
//     * @return
//     */
//    Long insert(BrokerageTeam brokerageTeam);

    /**
     * 根据主键id修改佣金详情表信息
     *
     * @param detailsBrokerage
     * @return
     */
    int updateDetailsBrokerage(DetailsBrokerage detailsBrokerage);

    /**
     * 根据主键id修改佣金表信息
     *
     * @param brokerageTeam
     * @return
     */
    int updateBrokerageTeam(BrokerageTeam brokerageTeam);

}
