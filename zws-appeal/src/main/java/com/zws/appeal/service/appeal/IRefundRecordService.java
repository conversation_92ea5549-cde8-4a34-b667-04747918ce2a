package com.zws.appeal.service.appeal;

import java.util.List;
import java.util.Map;

import com.zws.appeal.domain.appeal.RefundRecord;
import com.zws.appeal.pojo.appeal.ExecuteCasePojo;
import com.zws.appeal.pojo.appeal.RefundRecordPojo;

/**
*执行回款
*@Author：liuxifeng
*@Date：2024/6/25  16:38
*@Describe：编辑描述
*/
public interface IRefundRecordService {


    int deleteByPrimaryKey(Long id);

    int insert(RefundRecord record);

    int insertSelective(RefundRecord record);

    RefundRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RefundRecord record);

    int updateByPrimaryKey(RefundRecord record);

    int batchInsert(List<RefundRecord> list);

    /**
     * 列表查询
     * @param pojo
     * @return
     */
    List<RefundRecordPojo> selectWithRefund(RefundRecordPojo pojo);

    /**
     * 列表查询-统计
     * @param pojo
     * @return
     */
    Map<String, Object> selectWithRefundMoney(RefundRecordPojo pojo);

    /**
     * 根据搜索条件获取CaseId
     * @param pojo
     * @return
     */
    List<Long> selectCaseIds(RefundRecordPojo pojo);

    void importWithExcel(RefundRecordPojo pojo);

    int batchRegisterRefund(RefundRecordPojo pojo);

    /**
     * 查询是否已存在案件Ids
     * @param ids 案件Ids
     * @return 将存在的caseId返回
     */
    List<Long> selectWithCaseIds(List<Long> ids);

    /**
     * 根据caseId 更新记录
     * @param aRecord
     * @return
     */
    int updateByCaseIdSelective(RefundRecord aRecord);
}
