package com.zws.appeal.service.appeal;

import com.zws.appeal.domain.appeal.CourtSession;
import com.zws.appeal.pojo.appeal.LawInforPojo;
import com.zws.appeal.pojo.appeal.PhoneMediationPojo;

import java.util.List;

/**
 * 立案开庭
 * <AUTHOR>
 * @date 2024/6/21 15:29
 */
public interface ICourtSessionService {

    /**
     * 查询
     * @param lawInforPojo
     * @return
     */
    List<LawInforPojo> getSessionList(LawInforPojo lawInforPojo);

    /**
     * 预约开庭
     * @param courtSession
     */
    void insertCourtSession(CourtSession courtSession);

    /**
     * 批量登记
     * @param list1
     */
    void insertCourtBach(List<CourtSession> list1);

    List<Long> getCaseIds(LawInforPojo lawInforPojo);
}
