package com.zws.appeal.service.appeal;

import com.zws.appeal.domain.appeal.JudgeInfor;
import com.zws.appeal.pojo.Option;
import com.zws.appeal.pojo.appeal.FilingCasePojo;
import com.zws.appeal.pojo.appeal.JudgePojo;
import com.zws.appeal.pojo.appeal.LawInforPojo;

import java.util.List;
import java.util.Map;

/**
 * 判决与结果
 * <AUTHOR>
 * @date 2024/6/25 15:33
 */
public interface IJudgeResultService {
    List<JudgePojo> getJudgeList(JudgePojo judgePojo);

    void addJudge(JudgeInfor judgeInfor);

    List<Long> getCaseIds(JudgePojo judgePojo);

    /**
     * 获取案件ID
     * @param map
     * @return
     */
    List<Long> selectCaseIds(Map<String, Object> map);

    void addAplayCase(FilingCasePojo pojo);

    List<Option> getEmployees();

    /**
     * 批量结案
     * @param judgePojo
     * @return
     */
    int batchConcludeCase(JudgePojo judgePojo);
}
