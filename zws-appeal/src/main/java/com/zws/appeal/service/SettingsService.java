package com.zws.appeal.service;

import com.zws.appeal.domain.*;
import com.zws.system.api.domain.SysUser;
import com.zws.system.api.model.LoginUser;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface SettingsService {

    /**
     * 根据团队id查找团队审批设置
     *
     * @return
     */
    List<ApprovalSettings> selectApprovalSettings();

    /**
     * 根据团队id创建审批设置
     *
     * @return
     */
    int insertApprovalSettings();

    /**
     * 根据查询审批设置补全缺失的审批设置类型
     *
     * @return
     */
    int addApprovalSetting(ApprovalSettings approvalSetting);

    /**
     * 根据团队id查找团队审批流程默认字段
     *
     * @return
     */
    List<ApprovalSteps> selectApprovalSteps(ApprovalSteps approvalSteps);

    /**
     * 添加部门
     *
     * @param dept
     * @return
     */
    int insertDept(Dept dept);

    /**
     * 添加角色
     *
     * @param role
     * @return
     */
    Integer insertRole(Role role);

    /**
     * 添加角色 (团队)
     *
     * @param role
     * @return
     */
    int insertRoles(Role role);

    /**
     * 添加员工信息
     *
     * @param employees
     * @return
     */
    int insertEmployees(Employees employees);

    /**
     * 倒序查询最后一条数据
     *
     * @param
     * @return
     */
    Employees selectEmployeesLimitService();

    /**
     * 批量添加员工信息
     *
     * @param employees
     * @return
     */
    int insertEmployeesBatch(List<Employees> employees);

    /**
     * 添加角色菜单权限信息
     *
     * @param roleMenus
     * @return
     */
    int addRoleMenu(List<RoleMenu> roleMenus);

    /**
     * 添加员工信息验证
     */
    void yanzheng(Employees employees);

    /**
     * 该团队员工信息全查
     *
     * @param
     * @return
     */
    List<Employees> selectEmployeesCreateId();

    /**
     * 根据团队id查询团队员工账号中的最大值
     *
     * @return
     */
    int selectEmployeesCreateIdMax();

    /**
     * 根据员工工号查询员工信息
     *
     * @param map
     * @return
     */
    List<Employees> selectEmployeesCount(Map<String, Object> map);

    /**
     * 修改部门信息
     *
     * @param
     * @return
     */
    int updateDept(Dept dept);

    /**
     * 修改角色信息
     *
     * @param
     * @return
     */
    int updateRole(Role role);

    /**
     * 修改角色信息同步更新员工角色信息
     *
     * @param
     * @return
     */
    int updateEmployeesRole(Employees employees);

    /**
     * 修改员工信息
     *
     * @param
     * @return
     */
    int updateEmployees(Employees employees);

    /**
     * 修改员工信息--(修改员工修改密码时间)
     *
     * @param
     * @return
     */
    int updatePasswordTime(Employees employees);

    /**
     * 修改员工信息-最后登录时间
     *
     * @param
     * @return
     */
    int updateEmployeesDate(Employees employees);

    /**
     * 更改员工SIP坐席
     *
     * @param employees
     * @return
     */
    int updateEmployeesSip(Employees employees);

    /**
     * 修改员工密码
     *
     * @param
     * @return
     */
    int updateEmployeesPassWord(Employees employees);

    /**
     * 删除角色信息并且修改员工角色信息为null
     *
     * @param
     * @return
     */
    int deleteEmployeesRole(Employees employees);

    /**
     * 批量修改员工账号状态
     *
     * @param
     * @return
     */
    int updateAccountStatus(List<Employees> employees);

    /**
     * 批量重置员工账号密码
     *
     * @param
     * @return
     */
    int updatePassword(List<Employees> employees);

    /**
     * 员工密码重置信息
     */
    String password();

    /**
     * 根据id删除部门信息
     *
     * @param
     * @return
     */
    int deleteDept(int id);

    /**
     * 根据id删除角色信息
     *
     * @param
     * @return
     */
    int deleteRole(int id);

    /**
     * 根据id删除员工信息
     *
     * @param
     * @return
     */
    int deleteEmployees(int id);

    /**
     * 倒序查询最后一条数据
     *
     * @param
     * @return
     */
    Employees selectEmployeesLimit(int createId);

    /**
     * 根据登录账号查询员工信息
     *
     * @param
     * @return
     */
    LoginUser selectLogin(Employees employees);

    /**
     * 根据登录账号查询员工信息
     *
     * @param
     * @return
     */
    Employees selectLoginDate(Long userId);

    /**
     * 根据员工工号查询员工信息
     *
     * @param employeesWorking
     * @return
     */
    Employees selectWorking(int employeesWorking, Integer createid);

    /**
     * 员工登录账号获取员工信息
     *
     * @param loginAccount
     * @param createid
     * @return
     */
    Employees selectByLoginAccount(String loginAccount, Integer createid);

    /**
     * 角色信息全查/根据名称字段查询
     *
     * @param
     * @return
     */
    List<Role> selectRoles(int id, String roleName);

    /**
     * 角色信息全查/根据名称字段查询
     *
     * @param
     * @return
     */
    List<Role> selectRolees(Role role);

    /**
     * 角色信息全查/根据名称字段查询-过滤已禁用角色
     *
     * @param
     * @return
     */
    List<Role> selectRoleeDisable(Role role);

    /**
     * 根据团队id查询团队设置状态（页面限制）
     *
     * @param
     * @return
     */
    State selectState();

    /**
     * 根据团队id查询团队设置状态（水印设置）
     *
     * @param
     * @return
     */
    State selectStateWatermark();

    /**
     * 根据团队id查询团队水印设置字段信息
     *
     * @param
     * @return
     */
    Watermark selectWatermark();

    /**
     * 获取团队id 的双重验证状态
     *
     * @param teamId
     * @return
     */
    Integer getSecurityVerificationStatus(Integer teamId);

    /**
     * 根据字段查询员工信息
     *
     * @param
     * @return
     */
    List<Employees> selectDeptFuzzy(Employees employees);

    /**
     * 根据字段查询员工信息
     *
     * @param
     * @return
     */
    List<Employees> selectDeptFuzzyByCreateId(Employees employees);

    /**
     * 根据团队id查询团队员工信息
     *
     * @param
     * @return
     */
    List<Employees> selectUserByCreateId();

    /**
     * 根据员工id查询员工信息
     *
     * @param
     * @return
     */
    Employees selectEmployeesId(int id);

    /**
     * 根据员工id查询员工信息--资产端
     *
     * @param
     * @return
     */
    SysUser selectSysUserId(int id);

    /**
     * 根据团队id以及用户id查询员工信息
     *
     * @param
     * @return
     */
    Employees selectDeptFuzzyById(Map<String, Object> map);

    /**
     * 根据用户id查询用户登陆日志
     *
     * @return
     */
    List<TeamLogininfor> selectTeamLogininfor(TeamLogininfor teamLogininfor);

    /**
     * 删除超过一年的登录日志
     *
     * @param accessTime
     * @return
     */
    int delectTeamLogininfor(Date accessTime);

    /**
     * 根据角色信息查询员工
     *
     * @param
     * @return
     */
    List<Employees> selectEmployeesRole(Employees employees);

    /**
     * 部门信息全查
     *
     * @param
     * @return
     */
    List<Dept> selectDept(int createId);

    /**
     * 根据部门id查询该部门人员信息
     *
     * @param
     * @return
     */
    List<Employees> selectEmployeesParentId(int parentId, int createId);

    /**
     * 根据parentId查询对应部门信息
     *
     * @param
     * @return
     */
    List<Dept> selectDeptParentId(int parentId);

    /**
     * 更新部门是否有负责人
     *
     * @param departmentId 部门ID
     */
    void updateDeptHaveHead(Integer departmentId);

    /**
     * 根据id删除角色菜单权限信息
     *
     * @param
     * @return
     */
    int deleteRoleMenu(int roleId);

    List<Long> selectRecordFile(Long id);

    List<RetrievalFileSeparate> selectRetrievalFileSeparateIdList(List<Long> recordFileList);

}
