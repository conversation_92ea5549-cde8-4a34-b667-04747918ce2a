package com.zws.appeal.service.appeal.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zws.appeal.agservice.AgLawsuitService;
import com.zws.appeal.domain.appeal.FilingCase;
import com.zws.appeal.mapper.appeal.RefundRecordMapper;
import com.zws.appeal.pojo.appeal.RefundRecordPojo;
import com.zws.appeal.service.appeal.IRefundRecordService;
import com.zws.appeal.task.RefundRegisterTask;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.enums.TimeContentFormats;
import com.zws.common.core.threadpool.PoolManageMent;
import com.zws.common.core.threadpool.TaskManager;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.zws.appeal.domain.appeal.RefundRecord;

/**
*执行回款
*@Author：liuxifeng
*@Date：2024/6/25  16:38
*@Describe：编辑描述
*/
@Service
public class RefundRecordServiceImpl implements IRefundRecordService {

    @Resource
    private RefundRecordMapper refundRecordMapper;
    @Autowired
    private AgLawsuitService agLawsuitService;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return refundRecordMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(RefundRecord record) {
        return refundRecordMapper.insert(record);
    }

    @Override
    public int insertSelective(RefundRecord record) {
        return refundRecordMapper.insertSelective(record);
    }

    @Override
    public RefundRecord selectByPrimaryKey(Long id) {
        return refundRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(RefundRecord record) {
        return refundRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(RefundRecord record) {
        return refundRecordMapper.updateByPrimaryKey(record);
    }

    @Override
    public int batchInsert(List<RefundRecord> list) {
        return refundRecordMapper.batchInsert(list);
    }

    @Override
    public List<RefundRecordPojo> selectWithRefund(RefundRecordPojo pojo) {
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        pojo.setOdvId(TokenInformation.getUserid());
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        List<RefundRecordPojo> list = refundRecordMapper.selectWithRefund(pojo);
        for (RefundRecordPojo refundRecordPojo : list) {
            refundRecordPojo.setClientName(FieldEncryptUtil.decrypt(refundRecordPojo.getClientName()));
            refundRecordPojo.setClientCensusRegister(FieldEncryptUtil.decrypt(refundRecordPojo.getClientCensusRegister()));
            refundRecordPojo.setPhone(FieldEncryptUtil.decrypt(refundRecordPojo.getPhone()));
            refundRecordPojo.setClientIdcard(FieldEncryptUtil.decrypt(refundRecordPojo.getClientIdcard()));
        }
        return list;
    }

    @Override
    public Map<String, Object> selectWithRefundMoney(RefundRecordPojo pojo) {
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        pojo.setOdvId(TokenInformation.getUserid());
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        return refundRecordMapper.selectWithRefundMoney(pojo);
    }

    @Override
    public List<Long> selectCaseIds(RefundRecordPojo pojo) {
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        return refundRecordMapper.selectCaseIds(pojo);
    }

    @Override
    public void importWithExcel(RefundRecordPojo pojo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        RefundRegisterTask workTask = new RefundRegisterTask(pojo, loginUser);
        TaskManager.addTask(workTask);
    }

    @Override
    public int batchRegisterRefund(RefundRecordPojo pojo) {

        List<Long> ids = null;
        if (pojo.getAllQuery()){
            pojo.setTeamId(TokenInformation.getCreateid().longValue());
            pojo.setOdvId(TokenInformation.getUserid());
            ids = this.selectCaseIds(pojo);
        }else {
            ids = pojo.getIds();
        }

        RefundRecord record = pojo.getRefundRecord();
        record.setCaseId(0L);
        agLawsuitService.verifyRefundExcelData(record,null);
        //取出所有的 案件ID集合
        ids = pojo.getIds();
        HashMap<Long, RefundRecord> map = new HashMap<Long, RefundRecord>(ids.size());
        for (Long id : ids) {
            RefundRecord refundRecord = new RefundRecord();
            BeanUtil.copyProperties(record,refundRecord);
            refundRecord.setCaseId(id);
            map.put(id,refundRecord);
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        int i = agLawsuitService.addRefundRecord(map,loginUser, ids);
        agLawsuitService.addTimeManage(loginUser,ids, TimeContentFormats.REFUND_REGISTER);
        return 0;
    }

    @Override
    public List<Long> selectWithCaseIds(List<Long> ids) {
        return refundRecordMapper.selectWithCaseIds(ids);
    }

    @Override
    public int updateByCaseIdSelective(RefundRecord aRecord) {
        return refundRecordMapper.updateByCaseIdSelective(aRecord);
    }

}
