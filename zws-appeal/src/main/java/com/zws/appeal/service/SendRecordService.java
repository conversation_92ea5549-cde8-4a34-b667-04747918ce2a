package com.zws.appeal.service;

import com.zws.appeal.domain.ReplyRecord;
import com.zws.appeal.domain.SendRecords;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface SendRecordService {

    /**
     * 根据手机号查询该用户当天是否成功发送短信
     *
     * @param phone
     * @param dateTime
     * @param dateTime1
     * @return
     */

    List<SendRecords> selectFailMessage(String phone, Date dateTime, Date dateTime1, Long caseId);


    /**
     * 根据条件查询发送记录
     *
     * @param sendRecords
     * @return
     */
    List<SendRecords> selectSendRecords(SendRecords sendRecords);

    /**
     * 根据id查询回复记录（催收端）
     */
    List<ReplyRecord> selectReplayById(Long id);

    /**
     * 查询发送状态数量统计（催收端）
     */
    List<Map<String, Integer>> selectRecordNumber(SendRecords sendRecords);

    Long selectCountNowDay(Long templateId, String clientPhone);
}
