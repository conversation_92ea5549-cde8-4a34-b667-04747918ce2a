package com.zws.appeal.service.appeal;

import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.domain.appeal.FilingCase;
import com.zws.appeal.pojo.appeal.FilingCasePojo;
import com.zws.common.core.domain.Option;

import java.util.List;
import java.util.Map;

/**
 * 网上立案
 *
 * @Author：liuxifeng
 * @Date：2024/6/19 11:43
 * @Describe：编辑描述
 */
public interface IFilingCaseService {

    int deleteByPrimaryKey(Long id);

    int insert(FilingCase record);

    int insertSelective(FilingCase record);

    FilingCase selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FilingCase record);

    int updateByCaseIdSelective(FilingCase record);

    int updateByPrimaryKey(FilingCase record);


    int batchInsert(List<FilingCase> list);

    /**
     * 列表查询
     * @param pojo
     * @return
     */
    List<FilingCasePojo> selectList(FilingCasePojo pojo);

    /**
     * 获取案件ID
     * @param map
     * @return
     */
    List<Long> selectCaseIds(Map<String, Object> map);

    /**
     * 根据案件id集合 获取caseId
     * @param ids 案件id集合
     * @return
     */
    List<Long> selectWithCaseId(List<Long> ids);

    /**
     * 列表查询-统计金额
     * @param pojo
     * @return
     */
    Map<String, Object> selectWithMoney(FilingCasePojo pojo);

    /**
     * 网上立案审核登记
     * @param filingCase
     * @return
     */
    int updateWithCheck(FilingCasePojo filingCase);


    /**
     * 更新CaseManage阶段
     * @param map
     * @return
     */
    int updateWithStage(Map<String, Object> map);

    List<Option> selectWithStage(Integer type,Long caseId);

    List<Option> selectWithLawsuitStage();

    /**
     * 停案、留案、退案 操作查询数据
     * @param map
     * @return
     */
    List<CaseManage> selectCaseManages(Map<String, Object> map);

    /**
     * 员工-退案/留案等 预览统计弹窗数量、金额
     * @param map
     * @return
     */
    Map<String, Object> selectCaseManageMoneySize(Map<String, Object> map);

    /**
     * 员工-退案/留案等 预览统计弹窗已分配、未分配
     * @param map
     * @return
     */
    Integer selectCaseStateSize(Map<String, Object> map);
}
