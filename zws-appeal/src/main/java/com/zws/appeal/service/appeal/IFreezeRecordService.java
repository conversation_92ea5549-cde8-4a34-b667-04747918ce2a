package com.zws.appeal.service.appeal;

import com.zws.appeal.domain.appeal.FreezeRecord;
import com.zws.appeal.pojo.appeal.FilingCasePojo;
import com.zws.appeal.pojo.appeal.FreezeCasePojo;
import com.zws.appeal.pojo.appeal.StageProgressVo;

import java.util.List;
import java.util.Map;

/**
*编辑名称
*@Author：liuxifeng
*@Date：2024/6/20  15:28
*@Describe：编辑描述
*/
public interface IFreezeRecordService {


    int deleteByPrimaryKey(Long id);

    int insert(FreezeRecord record);

    int insertSelective(FreezeRecord record);

    FreezeRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FreezeRecord record);

    int updateByPrimaryKey(FreezeRecord record);

    /**
     * 诉讼保全-列表查询
     * @param pojo
     * @return
     */
    List<FreezeCasePojo> selectList(FreezeCasePojo pojo);

    /**
     * 列表查询-统计
     * @param pojo
     * @return
     */
    Map<String, Object> selectWithMoney(FreezeCasePojo pojo);


    /**
     * 获取搜索条件下 CaseIds
     * @param pojo
     * @return
     */
    List<Long> selectCaseIds(FreezeCasePojo pojo);

    List<Long> selectFreezeIds(FreezeCasePojo pojo);

    /**
     * 批量申请保全-默认保全首个阶段"材料提交"
     * @param pojo
     * @return
     */
    Integer batchFreezeCase(Map<String, Object> map);

    int updateWithStage(Map<String, Object> map);

    StageProgressVo stageProgress(Long caseId);
}
