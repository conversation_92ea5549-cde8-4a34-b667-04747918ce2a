package com.zws.appeal.service.appeal.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.zws.appeal.agservice.AgLawsuitService;
import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.domain.appeal.StageConfig;
import com.zws.appeal.mapper.CaseMapper;
import com.zws.appeal.mapper.appeal.FilingCaseMapper;
import com.zws.appeal.mapper.appeal.FreezeRecordMapper;
import com.zws.appeal.mapper.appeal.StageConfigMapper;
import com.zws.appeal.pojo.appeal.FilingCasePojo;
import com.zws.appeal.pojo.appeal.FreezeCasePojo;
import com.zws.appeal.pojo.appeal.StageProgressVo;
import com.zws.appeal.service.CollectionService;
import com.zws.appeal.service.appeal.IFreezeRecordService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.enums.TimeContentFormats;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import javafx.scene.input.DataFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import com.zws.appeal.domain.appeal.FreezeRecord;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
*诉讼保全service
*@Author：liuxifeng
*@Date：2024/6/20  15:28
*@Describe：编辑描述
*/
@Service
public class FreezeRecordServiceImpl implements IFreezeRecordService {

    @Resource
    private FreezeRecordMapper freezeRecordMapper;
    @Resource
    private FilingCaseMapper filingCaseMapper;
    @Autowired
    private AgLawsuitService agLawsuitService;
    @Autowired
    private CaseMapper caseMapper;
    @Autowired
    private StageConfigMapper stageConfigMapper;
    @Autowired
    private CollectionService collectionService;


    @Override
    public int deleteByPrimaryKey(Long id) {
        return freezeRecordMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(FreezeRecord record) {
        return freezeRecordMapper.insert(record);
    }

    @Override
    public int insertSelective(FreezeRecord record) {
        return freezeRecordMapper.insertSelective(record);
    }

    @Override
    public FreezeRecord selectByPrimaryKey(Long id) {
        return freezeRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(FreezeRecord record) {
        return freezeRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(FreezeRecord record) {
        return freezeRecordMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<FreezeCasePojo> selectList(FreezeCasePojo pojo) {
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        pojo.setOdvId(TokenInformation.getUserid());
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        if (!ObjectUtils.isEmpty(pojo.getStartDate2())) {
            Date dateTime = DateUtil.endOfDay(pojo.getStartDate2());
            pojo.setStartDate2(dateTime);
        }
        List<FreezeCasePojo> list = freezeRecordMapper.selectList(pojo);
        for (FreezeCasePojo freezeCasePojo : list) {
            freezeCasePojo.setClientName(FieldEncryptUtil.decrypt(freezeCasePojo.getClientName()));
            freezeCasePojo.setClientPhone(FieldEncryptUtil.decrypt(freezeCasePojo.getClientPhone()));
            freezeCasePojo.setClientCensusRegister(FieldEncryptUtil.decrypt(freezeCasePojo.getClientCensusRegister()));
            freezeCasePojo.setClientIdcard(FieldEncryptUtil.decrypt(freezeCasePojo.getClientIdcard()));
        }
        return list;
    }

    @Override
    public Map<String, Object> selectWithMoney(FreezeCasePojo pojo) {
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        return freezeRecordMapper.selectWithMoney(pojo);
    }


    @Override
    public List<Long> selectCaseIds(FreezeCasePojo pojo) {
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        return freezeRecordMapper.selectCaseIds(pojo);
    }

    @Override
    public List<Long> selectFreezeIds(FreezeCasePojo pojo) {
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        return freezeRecordMapper.selectFreezeIds(pojo);
    }

    @Override
    public Integer batchFreezeCase(Map<String, Object> map) {

        if (map.get("freezeRecord")==null){throw new ServiceException("保全信息不能为空");}
        List<Long> ids = agLawsuitService.searchIds(map);
        if (ids==null || ids.size()==0){throw new ServiceException("案件Id不存在,无法保全");}
        List<FreezeRecord> list = new ArrayList<>(ids.size());

        FreezeRecord record = JSONUtil.toBean(JSONUtil.toJsonStr(map.get("freezeRecord")), FreezeRecord.class);
        Date now = new Date();
        String username = TokenInformation.getUsername();
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        Long userId = Long.valueOf(TokenInformation.getUserid().toString());
        record.setCreateTime(now);
        record.setCreateBy(username);
        record.setCreateById(userId);
        record.setDelFlag(BaseConstant.DelFlag_Being);
        Object o = map.get("disposeStage")==null?map.get("mediatedStage"):map.get("disposeStage");
        record.setStageTwoName(o==null?"":o.toString());
        String stage = caseMapper.selectFirstStageByWay(3);
        if (StringUtils.isEmpty(stage)){throw new ServiceException("保全阶段配置,第一阶段不能为空");}
        //默认更新为第一个阶段 每次保全都是一次新的冻结资产流程 需要走完保全流程
        record.setSaveStage(stage);
        for (Long id : ids) {
            FreezeRecord record1 = new FreezeRecord();
            BeanUtil.copyProperties(record,record1);
            record1.setCaseId(id);
            record1.setTeamId(teamId);
            list.add(record1);
        }
        int i = freezeRecordMapper.batchInsert(list);

        //需要更新 caseManage saveStage
        Map<String, Object> mapInfo = new HashMap<>();
        mapInfo.put("ids",ids);
        mapInfo.put("isFreeze",1);
        mapInfo.put("updateTime",new Date());
        mapInfo.put("updateBy",TokenInformation.getUsername());
        int result = filingCaseMapper.updateWithStage(mapInfo);
        //写入时效记录

        LoginUser loginUser = SecurityUtils.getLoginUser();
        agLawsuitService.addTimeManage(loginUser,ids, TimeContentFormats.BATCH_FREEZE);

        return result;
    }

    @Override
    public int updateWithStage(Map<String, Object> map) {
        return freezeRecordMapper.updateWithStage(map);
    }

    /**
     * 阶段性进展
     *
     * @param caseId 案例id
     * @return {@link List }<{@link StageProgressVo }>
     */
    @Override
    public StageProgressVo stageProgress(Long caseId) {
        StageProgressVo progressVo = new StageProgressVo();
        CaseManage caseManage = collectionService.selectCaseManageId(caseId);
        if (caseManage == null) {
            throw new ServiceException("案件不存在！");
        }
        String disposeStage = caseManage.getDisposeStage();
        List<StageProgressVo.StageProgress> stageProgressList = new ArrayList<>();
//        Arrays.asList("网上立案","立案开庭","判决审理阶段","诉讼执行");
        Map<String, Integer> map = new HashMap<>();
        map.put("网上立案", 1);
        map.put("立案开庭", 2);
        map.put("判决与结果", 3);
        map.put("诉讼执行", 4);
        map.put("执行领款", 5);

        Map<Integer, String> mapBack = new HashMap<>();
        mapBack.put(1, "网上立案");
        mapBack.put(2, "立案开庭");
        mapBack.put(3, "判决审理");
        mapBack.put(4, "诉讼执行");
        mapBack.put(5, "执行回款");
        if (ObjectUtils.isEmpty(disposeStage)) {
            progressVo.setStageProgressList(stageProgressList);
            return progressVo;
        }
        StageConfig config = stageConfigMapper.selectByStageTwoName(disposeStage);
        progressVo.setStageName(mapBack.get(map.get(config.getStageName())));

        List<FreezeRecord> freezeRecords = freezeRecordMapper.selectListByCaseId(caseId, SecurityUtils.getTeamId());

        if (ObjectUtils.isEmpty(freezeRecords)) {
            progressVo.setStageProgressList(stageProgressList);
            return progressVo;
        }
//        Set<String> set = new HashSet<>();
        Map<String,Date> setMap = new HashMap<>();
        freezeRecords.forEach(freezeRecord -> {
            String stageTwoName = freezeRecord.getStageTwoName();
            if (StringUtils.isEmpty(stageTwoName)) {
                stageTwoName = disposeStage;
            }
//            set.add(stageTwoName);
            setMap.put(stageTwoName,freezeRecord.getCreateTime());
        });

        for (String s : setMap.keySet()) {
            StageProgressVo.StageProgress stageProgressVo = new StageProgressVo.StageProgress();
            StageConfig stageConfig = stageConfigMapper.selectByStageTwoName(s);

            //临时实现/映射
            Integer i = map.get(stageConfig.getStageName());
            String stage = mapBack.get(i);
            stageProgressVo.setSort(i);
            stageProgressVo.setStageName(stage);
            stageProgressVo.setCreateTime(setMap.get(s));
            stageProgressVo.setIfFreeze(true);
            stageProgressList.add(stageProgressVo);
        }

        progressVo.setStageProgressList(stageProgressList);
        return progressVo;
    }

}
