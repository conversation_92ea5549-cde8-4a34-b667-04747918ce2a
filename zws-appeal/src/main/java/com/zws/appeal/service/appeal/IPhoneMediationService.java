package com.zws.appeal.service.appeal;

import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.pojo.appeal.PhoneMediationPojo;

import java.util.List;
import java.util.Map;

/**
 * 诉前调解
 * <AUTHOR>
 * @date 2024/6/18 13:57
 */
public interface IPhoneMediationService {
    List<PhoneMediationPojo> getPhoneMediation(PhoneMediationPojo phoneMediationPojo);

    void addNum(PhoneMediationPojo phoneMediationPojo);

    void addLink(PhoneMediationPojo phoneMediationPojo);

    List<Long> getCaseIds(PhoneMediationPojo phoneMediationPojo);

    /**
     * 停案、留案、退案 操作查询数据
     * @param mediationPojo
     * @return
     */
    List<CaseManage> selectCaseManages(PhoneMediationPojo mediationPojo);

    /**
     * 员工-退案/留案等 预览统计弹窗数量、金额
     * @param map
     * @return
     */
    Map<String, Object> selectCaseManageMoneySize(Map<String, Object> map);

    /**
     * 员工-退案/留案等 预览统计弹窗已分配、未分配
     * @param map
     * @return
     */
    Integer selectCaseStateSize(Map<String, Object> map);
}
