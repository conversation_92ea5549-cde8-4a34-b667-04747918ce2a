package com.zws.appeal.service.appeal.impl;

import cn.hutool.core.date.DateUtil;
import com.zws.appeal.agservice.AgLawsuitService;
import com.zws.appeal.controller.letterDoc.law.service.ILawAgencyService;
import com.zws.appeal.domain.appeal.CourtSession;
import com.zws.appeal.domain.appeal.FilingCase;
import com.zws.appeal.mapper.appeal.CourtSessionMapper;
import com.zws.appeal.pojo.appeal.LawInforPojo;
import com.zws.appeal.service.appeal.ICourtSessionService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.enums.TimeContentFormats;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;

/**
 * 立案开庭
 * <AUTHOR>
 * @date 2024/6/21 15:30
 */
@Service
public class CourtSessionServiceImpl implements ICourtSessionService {

    @Autowired
    private CourtSessionMapper courtSessionMapper;
    @Autowired
    private ILawAgencyService lawAgencyService;
    @Autowired
    private AgLawsuitService agLawsuitService;

    @Override
    public List<LawInforPojo> getSessionList(LawInforPojo lawInforPojo) {
        if (!ObjectUtils.isEmpty(lawInforPojo.getTrialTime2())) {
            Date dateTime = DateUtil.endOfDay(lawInforPojo.getTrialTime2());
            lawInforPojo.setTrialTime2(dateTime);
        }
//        lawInforPojo.setCaseIds(getCaseIds(lawInforPojo));
        lawInforPojo.setTeamId(SecurityUtils.getTeamId());
        lawInforPojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        lawInforPojo.setOdvId(TokenInformation.getUserid());
        List<LawInforPojo> list = courtSessionMapper.getSessionList(lawInforPojo);
        //解密
        for (LawInforPojo lawInforPojo1 : list) {
            lawInforPojo1.setClientName(FieldEncryptUtil.decrypt(lawInforPojo1.getClientName()));
            lawInforPojo1.setClientPhone(FieldEncryptUtil.decrypt(lawInforPojo1.getClientPhone()));
            lawInforPojo1.setClientCensusRegister(FieldEncryptUtil.decrypt(lawInforPojo1.getClientCensusRegister()));
            lawInforPojo1.setClientIdNum(FieldEncryptUtil.decrypt(lawInforPojo1.getClientIdNum()));
        }
        return list;
    }

    /**
     * 预约开庭
     * @param courtSession
     */
    @Override
    public void insertCourtSession(CourtSession courtSession) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
//        List<Long> list = getCaseIdsList(courtSession);
        List<Long> list = courtSession.getCaseIds();
        for (Long caseId : list) {
            if(courtSessionMapper.selecCaseId(caseId) == null){
                courtSession.setCaseId(caseId);
                courtSession.setCreateById(SecurityUtils.getUserId());
                courtSession.setCreateBy(SecurityUtils.getUsername());
                courtSession.setDelFlag("0");
                courtSession.setCreateTime(new Date());
                courtSession.setTeamId(SecurityUtils.getTeamId());
                courtSessionMapper.insert(courtSession);
            }else {
                courtSession.setCaseId(caseId);
                courtSession.setUpdateBy(SecurityUtils.getUsername());
                courtSession.setUpdateTime(new Date());
                courtSessionMapper.updateByCaseId(courtSession);
            }
        }
        agLawsuitService.addTimeManage(loginUser,list, TimeContentFormats.HOLD_COURT);
    }


    /**
     * 批量导入登记
     * @param list1
     */
    @Override
    public void insertCourtBach(List<CourtSession> list1) {
        HashSet<Long> caseIds = new HashSet<>();
        //指定机构所有法院
        Long teamId = TokenInformation.getCreateid().longValue();
        Map<String,Long> map = lawAgencyService.selectWithLawAgency(teamId);
        if (map==null || map.size()==0){throw new ServiceException("该机构创建法院数量为 0");}
        Long useId = Long.valueOf(TokenInformation.getUserid());
        List<Long> caseIds1 = courtSessionMapper.selecCaseIds(useId);//获取该阶段的案件id
        for (CourtSession courtSession : list1) {
            if (!caseIds1.contains(courtSession.getCaseId())){
                throw new ServiceException("该案件不在本阶段范围内");
            }
            if (map!=null && !map.containsKey(courtSession.getTrialCourt())){
                throw new ServiceException("法院不存在");
            }
            if ((courtSession.getCaseId()!=null&&!caseIds.contains(courtSession.getCaseId()))&&caseIds1.contains(courtSession.getCaseId())){
                caseIds.add(courtSession.getCaseId());
                if(courtSessionMapper.selecCaseId(courtSession.getCaseId()) == null) {
                    courtSession.setCourtId(map.get(courtSession.getTrialCourt()));
                    courtSession.setCreateById(SecurityUtils.getUserId());
                    courtSession.setCreateBy(SecurityUtils.getUsername());
                    courtSession.setDelFlag("0");
                    courtSession.setCreateTime(new Date());
                    courtSession.setTeamId(SecurityUtils.getTeamId());
                    courtSessionMapper.insertSelective(courtSession);
                }else {
                    courtSession.setCourtId(map.get(courtSession.getTrialCourt()));
                    courtSession.setCaseId(courtSession.getCaseId());
                    courtSession.setUpdateBy(SecurityUtils.getUsername());
                    courtSession.setUpdateTime(new Date());
                    courtSessionMapper.updateByCaseId(courtSession);
                }
            }
        }
    }

    @Override
    public List<Long> getCaseIds(LawInforPojo lawInforPojo) {
        if(ObjectUtils.isEmpty(lawInforPojo.getTeamId())){
            Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
            lawInforPojo.setTeamId(teamId);
        }
        List<Long> caseIds = null;
        if (lawInforPojo.getAllQuery()){
            caseIds = courtSessionMapper.getCaseIds(lawInforPojo);
        }else {
            caseIds = lawInforPojo.getCaseIds();
        }
        return caseIds;
    }

    public List<Long> getCaseIdsList(CourtSession courtSession){
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        courtSession.setTeamId(teamId);
        List<Long> caseIds = null;
        if (courtSession.getAllQuery()){
            caseIds = courtSessionMapper.getCaseIdsList(courtSession);
        }else {
            caseIds = courtSession.getCaseIds();
        }
        return caseIds;
    }

    /**
     * 导入校验
     *
     * @param courtSession
     * @param map
     * @return
     */
    public Map<String, Object> checkInfor(CourtSession courtSession,Map<String, Long> map,List<Long>caseIds){
        Map<String, Object> map1= new HashMap<>();
        if(!caseIds.contains(courtSession.getCaseId())){
            map1.put("案件ID","该案件不在本阶段范围内");
        }
        if(courtSession.getCaseId()==null){
            map1.put("案件ID","案件id不能为空");
        }
        if(courtSession.getTrialLawyer()==null||courtSession.getTrialLawyer()==""){
            map1.put("开庭律师","开庭律师不能为空");
        }
        if(courtSession.getUndertakingLawyer()==null||courtSession.getUndertakingLawyer()==""){
            map1.put("承办律师","承办律师不能为空");
        }
        if((courtSession.getTrialTime() == null)){
            map1.put("开庭时间","开庭时间不能为空");
        }
        if(courtSession.getTrialCity()==null||courtSession.getTrialCity()==""){
            map1.put("开庭城市","开庭城市不能为空");
        }
        if(courtSession.getTrialCourt()==null||courtSession.getTrialCourt()==""){
            map1.put("开庭法院","开庭法院不能为空");
        }
        if (courtSession.getLocation()==null||courtSession.getLocation()==""){
            map1.put("应到处所","应到处所不能为空");
        }
        if (courtSession.getTrialMethod()==null||courtSession.getTrialMethod()==""){
            map1.put("开庭方式","开庭方式不能为空");
        }
        if (courtSession.getTrialSum()==null||courtSession.getTrialSum()==""){
            map1.put("开庭次数","开庭次数不能为空");
        }
        if (map!=null && !map.containsKey(courtSession.getTrialCourt())){
            map1.put("开庭法院","法院不存在");
        }
        return map1;
    }
}
