package com.zws.appeal.service.appeal.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zws.appeal.agservice.AgLawsuitService;
import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.domain.appeal.FilingCase;
import com.zws.appeal.mapper.CaseMapper;
import com.zws.appeal.mapper.appeal.FilingCaseMapper;
import com.zws.appeal.mapper.appeal.StageConfigMapper;
import com.zws.appeal.pojo.appeal.FilingCasePojo;
import com.zws.appeal.service.CollectionService;
import com.zws.appeal.service.appeal.IFilingCaseService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.domain.Option;
import com.zws.common.core.enums.TimeContentFormats;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 网上立案
 *
 * @Author：liuxifeng
 * @Date：2024/6/19 11:43
 * @Describe：编辑描述
 */
@Service
public class FilingCaseService implements IFilingCaseService {


    @Autowired
    private FilingCaseMapper filingCaseMapper;
    @Autowired
    private StageConfigMapper stageConfigMapper;
    @Autowired
    private CollectionService collectionService;
    @Autowired
    private AgLawsuitService agLawsuitService;
    @Autowired
    private CaseMapper caseMapper;


    @Override
    public int deleteByPrimaryKey(Long id) {
        return filingCaseMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(FilingCase record) {
        return filingCaseMapper.insert(record);
    }

    @Override
    public int insertSelective(FilingCase record) {
        return filingCaseMapper.insertSelective(record);
    }

    @Override
    public FilingCase selectByPrimaryKey(Long id) {
        return filingCaseMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(FilingCase record) {
        return filingCaseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByCaseIdSelective(FilingCase record) {
        return filingCaseMapper.updateByCaseIdSelective(record);
    }

    @Override
    public int updateByPrimaryKey(FilingCase record) {
        return filingCaseMapper.updateByPrimaryKey(record);
    }


    @Override
    public int batchInsert(List<FilingCase> list) {
        return filingCaseMapper.batchInsert(list);
    }

    @Override
    public List<FilingCasePojo> selectList(FilingCasePojo pojo) {
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setOdvId(TokenInformation.getUserid());
        pojo.setTeamId(teamId);
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        List<FilingCasePojo> list = filingCaseMapper.selectList(pojo);
        //解密
        for (FilingCasePojo filingCasePojo : list) {
            filingCasePojo.setClientName(FieldEncryptUtil.decrypt(filingCasePojo.getClientName()));
            filingCasePojo.setClientIdcard(FieldEncryptUtil.decrypt(filingCasePojo.getClientIdcard()));
            filingCasePojo.setClientCensusRegister(FieldEncryptUtil.decrypt(filingCasePojo.getClientCensusRegister()));
            filingCasePojo.setClientPhone(FieldEncryptUtil.decrypt(filingCasePojo.getClientPhone()));
        }
        //如果案件资产端标签不为空，则覆盖标签内容为资产端标签内容
        List<Map<String, String>> resultList = caseMapper.getAssetKeyVal();
        Map<String, String> map = new HashMap<>();

        for (Map<String, String> entry : resultList) {
            String key = entry.get("key");
            String value = entry.get("value");
            map.put(key, value);
        }
        for (FilingCasePojo caseManage : list) {
            //覆盖标签内容为资产端标签内容
            if (!ObjectUtils.isEmpty(caseManage.getLabelAsset())) {
                String s = map.get(caseManage.getLabelAsset());
                if (!ObjectUtils.isEmpty(s)) {
                    caseManage.setLabelContent(s);
                    caseManage.setLabel(caseManage.getLabelAsset());
                }
            } else {
                caseManage.setLabel("*" + caseManage.getLabel());
            }
        }
        return list;
    }

    @Override
    public List<Long> selectCaseIds(Map<String, Object> map) {
        map.put("decryptKey", FieldEncryptUtil.fieldKey);
        return filingCaseMapper.selectCaseIds(map);
    }

    @Override
    public List<Long> selectWithCaseId(List<Long> ids) {
        return filingCaseMapper.selectWithCaseId(ids);
    }

    @Override
    public Map<String, Object> selectWithMoney(FilingCasePojo pojo) {
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        pojo.setOdvId(TokenInformation.getUserid());
        pojo.setDecryptKey(FieldEncryptUtil.fieldKey);
        return filingCaseMapper.selectWithMoney(pojo);
    }

    @Override
    public int updateWithCheck(FilingCasePojo pojo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        pojo.setTeamId(TokenInformation.getCreateid().longValue());
        List<Long> ids = null;
        if (pojo.getAllQuery()) {
            //获取全部CaseId
            Map<String, Object> map = BeanUtil.beanToMap(pojo);
            map.put("disposeStage", "审核中案件");
            ids = this.selectCaseIds(map);
        } else {
            ids = pojo.getIds();
        }

        //需要更新 caseManage disposeStage
        Map<String, Object> map = new HashMap<>();
        map.put("ids", ids);
        map.put("disposeStage", pojo.getDisposeStage());
        map.put("updateTime", new Date());
        map.put("updateBy", TokenInformation.getUsername());
        int i = filingCaseMapper.updateWithStage(map);
        //并且更新立案信息表
        map.put("checkStatus", pojo.getCheckStatus());
        map.put("remark", pojo.getRemark());
        //加入时效管理
        agLawsuitService.addTimeManage(loginUser, ids, TimeContentFormats.STATE_REGISTRATION);
        return filingCaseMapper.updateWithCheck(map);
    }


    @Override
    public int updateWithStage(Map<String, Object> map) {
        return filingCaseMapper.updateWithStage(map);
    }

    @Override
    public List<Option> selectWithStage(Integer type, Long caseId) {
        if (caseId != null && type != 3) {

            CaseManage caseManage = collectionService.selectCaseManageId(caseId);
            List<Option> list = this.selectWithLawsuitStage();
            List<String> optionList = list.stream().map(Option::getInfo).collect(Collectors.toList());

            Boolean ao = StringUtils.isNotEmpty(caseManage.getDisposeStage()) && optionList.contains(caseManage.getDisposeStage());
            Boolean bo = StringUtils.isNotEmpty(caseManage.getMediatedStage()) && optionList.contains(caseManage.getMediatedStage());
            if (ao || bo) {
                //查询当前的案件 对应的调解、诉讼阶段  是否是 诉讼执行或执行回款大阶段内
                return list;
            }

        }
        return stageConfigMapper.selectWithStage(type);
    }

    @Override
    public List<CaseManage> selectCaseManages(Map<String, Object> map) {
        map.put("decryptKey", FieldEncryptUtil.fieldKey);
        return filingCaseMapper.selectCaseManages(map);
    }

    @Override
    public Map<String, Object> selectCaseManageMoneySize(Map<String, Object> map) {
        return filingCaseMapper.selectCaseManageMoneySize(map);
    }

    @Override
    public Integer selectCaseStateSize(Map<String, Object> map) {
        return filingCaseMapper.selectCaseStateSize(map);
    }

    @Override
    public List<Option> selectWithLawsuitStage() {
        List<Option> list = stageConfigMapper.selectWithLawsuitStage(4);
        return list;
    }
}
