package com.zws.appeal.service.impl;

import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.exception.ServiceException;
import com.zws.appeal.domain.TeamBatchDimension;
import com.zws.appeal.domain.TeamDataOverview;
import com.zws.appeal.domain.TeamEmployeeDimension;
import com.zws.appeal.mapper.CensusAssetsMapper;
import com.zws.appeal.pojo.Option;
import com.zws.appeal.service.CensusAssetsService;
import com.zws.appeal.utils.TimeUtils;
import com.zws.appeal.utils.TokenInformation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据概览
 *
 * @Author: 刘锡锋
 * @DATE: Created in 2023/5/26
 */
@Service
public class CensusAssetsServiceImpl implements CensusAssetsService {

    @Autowired
    private CensusAssetsMapper censusAssetsMapper;

    private static Boolean bo(BigDecimal param) {
        if (param == null) {
            return true;
        }
        if (param.compareTo(BigDecimal.ZERO) < 1) {
            return true;
        }
        return false;
    }

    /**
     * 同一员工数据概览集合进行相加
     *
     * @param list1
     * @return
     */
    private static TeamEmployeeDimension addEmployeeUtil(List<TeamEmployeeDimension> list1) {

        TeamEmployeeDimension dimension = new TeamEmployeeDimension();
        BigDecimal collectionAmountTotal = list1.stream().map(TeamEmployeeDimension::getCollectionAmountTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal recordedAmount = list1.stream().map(TeamEmployeeDimension::getRecordedAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal entrustMoney = list1.stream().map(TeamEmployeeDimension::getEntrustMoney).reduce(BigDecimal.ZERO, BigDecimal::add);

        int commissionHouseholds = list1.stream().mapToInt(TeamEmployeeDimension::getCommissionHouseholds).sum();
        int agentDuration = list1.stream().mapToInt(TeamEmployeeDimension::getAgentDuration).sum();
        int callNum = list1.stream().mapToInt(TeamEmployeeDimension::getCallNum).sum();
        int availableNum = list1.stream().mapToInt(TeamEmployeeDimension::getAvailableNum).sum();
        int recordedNumber = list1.stream().mapToInt(TeamEmployeeDimension::getRecordedNumber).sum();
        int monthReminderQuantity = list1.stream().mapToInt(TeamEmployeeDimension::getMonthReminderQuantity).sum();
        int reminderTotal = list1.stream().mapToInt(TeamEmployeeDimension::getReminderTotal).sum();
        int caseVolume = list1.stream().mapToInt(TeamEmployeeDimension::getCaseVolume).sum();
        int tsSum = list1.stream().mapToInt(TeamEmployeeDimension::getTsSum).sum();
        int urgeSum = list1.stream().mapToInt(TeamEmployeeDimension::getUrgeSum).sum();


        dimension.setEmployeeName(list1.get(0).getEmployeeName());
        dimension.setCollectionAmountTotal(collectionAmountTotal);
        dimension.setRecordedAmount(recordedAmount);
        dimension.setEntrustMoney(entrustMoney);

        dimension.setCommissionHouseholds(commissionHouseholds);
        dimension.setAgentDuration(agentDuration);
        dimension.setCallNum(callNum);
        dimension.setAvailableNum(availableNum);
        dimension.setRecordedNumber(recordedNumber);
        dimension.setMonthReminderQuantity(monthReminderQuantity);
        dimension.setReminderTotal(reminderTotal);
        dimension.setCaseVolume(caseVolume);
        dimension.setTsSum(tsSum);
        dimension.setUrgeSum(urgeSum);
        if (bo(dimension.getEntrustMoney()) || bo(dimension.getCollectionAmountTotal())) {
            dimension.setRecoveryRateCumulative(BigDecimal.ZERO);
        } else {
            //累计回收率 = 累计回收金额/累计委案金额
            BigDecimal rate = dimension.getCollectionAmountTotal().divide(dimension.getEntrustMoney(), 4).multiply(BigDecimal.valueOf(100));
            dimension.setRecoveryRateCumulative(rate);
        }
        // todo 累计结佣金额 （结算中心未上线）
        return dimension;
    }

    @Override
    public int insert(TeamBatchDimension record) {
        return censusAssetsMapper.insert(record);
    }

    @Override
    public int insertSelective(TeamBatchDimension record) {
        return censusAssetsMapper.insertBatchSelective(record);
    }

    @Override
    public List<TeamBatchDimension> getStatisticsBatch(Long createId) {

        List<TeamBatchDimension> batchList = censusAssetsMapper.censusAssets(createId);
        List<TeamBatchDimension> repaymentList = censusAssetsMapper.getRepaymentMoney(createId);

        Map<Long, TeamBatchDimension> map = repaymentList.stream().collect(Collectors.toMap(TeamBatchDimension::getId, Function.identity()));
        for (TeamBatchDimension dimension : batchList) {
            Long id = dimension.getId();
            //String batchNum = dimension.getEntrustingBatchNum();
            //初始本金之和
            //BigDecimal residualPrincipal = dimension.getResidualPrincipal();
            //初始债权之和
            BigDecimal entrustMoney = dimension.getEntrustMoney();
            //剩余应还债权总额
            BigDecimal remainingDue = dimension.getRemainingDue();

            //回收金额之和
            BigDecimal repaymentMoney = map.get(id).getRepaymentMoney();
            dimension.setRepaymentMoney(repaymentMoney);

            //回收率= 回收金额/初始债权
            if (bo(repaymentMoney) || bo(entrustMoney)) {
                dimension.setRecoveryRate(BigDecimal.ZERO);
            } else {
                BigDecimal divide = repaymentMoney.divide(entrustMoney, 4, RoundingMode.HALF_UP);
                BigDecimal multiply = divide.multiply(BigDecimal.valueOf(100));
                dimension.setRecoveryRate(multiply);
            }

            //回款目标 = 剩余应还债权总额 乘以 回款率百分比
            BigDecimal targetBackMoney = map.get(id).getTargetBackMoney();
            if (bo(targetBackMoney) || bo(remainingDue)) {
                dimension.setRecoveryTarget(BigDecimal.ZERO);
            } else {
                BigDecimal recoveryTarget = remainingDue.multiply(targetBackMoney.divide(BigDecimal.valueOf(100), 4));
                dimension.setRecoveryTarget(recoveryTarget);
            }
            //回收进度 = 回收金额/回收目标
            if (bo(repaymentMoney) || bo(dimension.getRecoveryTarget())) {
                dimension.setRecoveryProgress(BigDecimal.ZERO);
            } else {
                BigDecimal recoveryProgress = repaymentMoney.divide(dimension.getRecoveryTarget(), 4).multiply(BigDecimal.valueOf(100));
                dimension.setRecoveryProgress(recoveryProgress);
            }
            dimension.setId(null);
            dimension.setTeamId(createId);
            dimension.setDelFlag(BaseConstant.DelFlag_Being);
            dimension.setCreateTime(new Date());
        }
        return batchList;
    }

    @Override
    public void autoCensusAssets() {
        //获取全部的teamId
        List<Long> list = censusAssetsMapper.getAllTeamId();
        //删除全部旧数据
        censusAssetsMapper.deleteBatchDimension();
        for (Long teamId : list) {
            if (teamId == null) {
                continue;
            }
            List<TeamBatchDimension> batchDimensions = getStatisticsBatch(teamId);
            //新增数据
            batchDimensions.forEach(s -> {
                censusAssetsMapper.insertBatchSelective(s);
            });
        }
    }

    @Override
    public List<TeamBatchDimension> getCensusBatchList(Long teamId, List<String> batchNums) {
        List<TeamBatchDimension> censusList = censusAssetsMapper.getCensusBatchList(teamId, batchNums);
        return censusList;
    }

    @Override
    public List<Option> getBatchNum(Long teamId) {
        List<String> list = censusAssetsMapper.getBatchNum(teamId);
        List<Option> optionList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Option option = new Option(i + 1, list.get(i));
            optionList.add(option);
        }
        return optionList;
    }

    @Override
    public List<TeamEmployeeDimension> getCensusEmployeeList(TeamEmployeeDimension dimension) {

        int type = TokenInformation.getType();
        List<TeamEmployeeDimension> list = new ArrayList<TeamEmployeeDimension>();
        if (dimension == null) {
            dimension = new TeamEmployeeDimension();
        }
        Long teamId = Long.valueOf(TokenInformation.getCreateid());
        dimension.setTeamId(teamId);
        if (type == 0) {
            //dimension.setEmployeeId(null);
            List<TeamEmployeeDimension> dimensionList = censusAssetsMapper.getCensusEmployeeList(dimension);
            return dimensionList;
        } else if (type == 1) {
            Long userId = Long.valueOf(TokenInformation.getUserid());
            dimension.setEmployeeId(userId);
            List<TeamEmployeeDimension> dimensionList = censusAssetsMapper.getCensusEmployeeList(dimension);
            return dimensionList;
        } else {
            throw new ServiceException("账号类型不存在");
        }
    }

    /**
     * 根据条件查询员工名下案件所属资产包名称
     *
     * @param map
     * @return
     */
    @Override
    public List<String> selectAssetsPackName(Map<String, Object> map) {
        return censusAssetsMapper.selectAssetsPackName(map);
    }

    @Override
    public List<TeamEmployeeDimension> exportEmployeeDimension(TeamEmployeeDimension dimension) {

        List<TeamEmployeeDimension> list = new ArrayList<TeamEmployeeDimension>();
        if (dimension == null) {
            dimension = new TeamEmployeeDimension();
        }
        if (TokenInformation.getType() == 0) {
            dimension.setEmployeeId(null);
            List<TeamEmployeeDimension> dimensionList = censusAssetsMapper.getCensusEmployeeList(dimension);
            if (dimensionList != null && dimensionList.size() > 0) {
                list = statisticsUtil(dimensionList, null);
            }
        } else if (TokenInformation.getType() == 1) {
            Long userId = Long.valueOf(TokenInformation.getUserid());
            dimension.setEmployeeId(userId);
            List<TeamEmployeeDimension> dimensionList = censusAssetsMapper.getCensusEmployeeList(dimension);
            if (dimensionList != null && dimensionList.size() > 0) {
                list = statisticsUtil(dimensionList, null);
            }
        } else {
            throw new ServiceException("账号类型不存在");
        }
        return list;
    }

    /**
     * 多个月分统计出一条员工数据
     *
     * @param list
     * @param longs 分页展示的employeeIds数据
     * @return
     */
    @Override
    public List<TeamEmployeeDimension> statisticsUtil(List<TeamEmployeeDimension> list, List<Long> longs) {

        List<TeamEmployeeDimension> dimensionList = new ArrayList<>();
        HashMap<Long, List<TeamEmployeeDimension>> map = new HashMap<>();

        if (longs == null) {
            Set<Long> set = list.stream().map(TeamEmployeeDimension::getEmployeeId).collect(Collectors.toSet());
            for (Long aLong : set) {
                List<TeamEmployeeDimension> list1 = new ArrayList<>();
                Iterator<TeamEmployeeDimension> iterator = list.iterator();
                while (iterator.hasNext()) {
                    TeamEmployeeDimension next = iterator.next();
                    if (next.getEmployeeId().equals(aLong)) {
                        list1.add(next);
                        iterator.remove();
                    }
                }
                map.put(aLong, list1);
            }
        } else {
            for (Long aLong : longs) {
                List<TeamEmployeeDimension> list1 = new ArrayList<>();
                Iterator<TeamEmployeeDimension> iterator = list.iterator();
                while (iterator.hasNext()) {
                    TeamEmployeeDimension next = iterator.next();
                    if (next.getEmployeeId().equals(aLong)) {
                        list1.add(next);
                        iterator.remove();
                    }
                }
                map.put(aLong, list1);
            }
        }

        Set<Map.Entry<Long, List<TeamEmployeeDimension>>> entries = map.entrySet();
        Iterator<Map.Entry<Long, List<TeamEmployeeDimension>>> iterator1 = entries.iterator();
        while (iterator1.hasNext()) {
            List<TeamEmployeeDimension> list1 = iterator1.next().getValue();
            if (list1.size() < 2) {
                //查询结果为空的字段
                dimensionList.add(list1.get(0));
            } else {
                TeamEmployeeDimension dimension = addEmployeeUtil(list1);
                dimensionList.add(dimension);
            }
        }
        return dimensionList;
    }

    @Override
    public List<TeamEmployeeDimension> getStatisticsEmployee(Long teamId) {
        List<Long> employeeIds = getEmployeeIds(teamId);
        if (employeeIds == null || employeeIds.size() == 0) {
            return new ArrayList<>();
        }

        List<TeamEmployeeDimension> list = new ArrayList<>();
        for (Long employeeId : employeeIds) {

            Date beginDateMonth = TimeUtils.getMonthBegins();
            Date endDateMonth = TimeUtils.getMonthEnds();
            TeamEmployeeDimension param = new TeamEmployeeDimension();
//            DateTime parse = DateUtil.parse("2023-05-01");
//            DateTime parse2 = DateUtil.parse("2023-05-31");
            param.setEmployeeId(employeeId);
            param.setTeamId(teamId);
            param.setCreateTime1(beginDateMonth);
            param.setCreateTime2(endDateMonth);

            TeamEmployeeDimension dimension = censusAssetsMapper.getEntrustMoneyTotal(param);
            BigDecimal collectionAmountTotal = censusAssetsMapper.getCollectionAmountTotal(param);
            TeamEmployeeDimension callRecordInfo = censusAssetsMapper.getCallRecordInfo(param);
            TeamEmployeeDimension recordedInfo = censusAssetsMapper.getRecordedInfo(param);
            Integer reminderTotal = censusAssetsMapper.getReminderTotal(param);
            Integer monthReminderQuantity = censusAssetsMapper.getMonthReminderQuantity(param);

            dimension.setAgentDuration(callRecordInfo.getAgentDuration());
            dimension.setCallNum(callRecordInfo.getCallNum());
            dimension.setAvailableNum(callRecordInfo.getAvailableNum());

            dimension.setCollectionAmountTotal(collectionAmountTotal);

            dimension.setRecordedNumber(recordedInfo.getRecordedNumber());
            dimension.setCaseVolume(recordedInfo.getCaseVolume());
            dimension.setRecordedAmount(recordedInfo.getRecordedAmount());

            dimension.setReminderTotal(reminderTotal);
            dimension.setMonthReminderQuantity(monthReminderQuantity);
//            dimension.setCreateTime(beginDateMonth);
            dimension.setCreateTime(new Date());
            dimension.setDelFlag(BaseConstant.DelFlag_Being);
            dimension.setTeamId(teamId);
            dimension.setEmployeeId(employeeId);
            if (bo(dimension.getEntrustMoney()) || bo(dimension.getCollectionAmountTotal())) {
                dimension.setRecoveryRateCumulative(BigDecimal.ZERO);
            } else {
                //回收率 = 累计回收金额/累计委案金额
                BigDecimal divide = dimension.getCollectionAmountTotal().divide(dimension.getEntrustMoney(), 4, RoundingMode.HALF_UP);
                dimension.setRecoveryRateCumulative(divide.multiply(BigDecimal.valueOf(100)));
            }

            list.add(dimension);
        }
        return list;
    }

    @Override
    public List<Option> getEmployeeOption(Long teamId) {
        List<Option> i = censusAssetsMapper.getEmployeeOption(teamId);
        return i;
    }

    @Override
    public List<Long> getEmployeeIds(Long teamId) {
        List<Long> i = censusAssetsMapper.getEmployeeIds(teamId);
        return i;
    }

    @Override
    public List<Long> checkEmployeeExit(Long teamId, Long employeeId) {
        Date beginDateMonth = TimeUtils.getMonthBegins();
        Date endDateMonth = TimeUtils.getMonthEnds();
        List<Long> longs = censusAssetsMapper.checkEmployeeExit(employeeId, teamId, beginDateMonth, endDateMonth);
        return longs;
    }


    @Override
    public void getStatisticsEmployeeTask() {
        List<Long> allTeamId = getAllTeamId();
        for (Long id : allTeamId) {
            List<TeamEmployeeDimension> employeeList = getStatisticsEmployee(id);
            if (employeeList == null || employeeList.size() == 0) {
                continue;
            }
            for (TeamEmployeeDimension dimension : employeeList) {
                //先判断当月下 该员工的数据是否存在
                List<Long> longs = checkEmployeeExit(dimension.getTeamId(), dimension.getEmployeeId());
                //不存在则添加
                if (ObjectUtils.isEmpty(longs) || longs.size() == 0) {
                    int i = censusAssetsMapper.insertEmployeeSelective(dimension);
                } else {
                    dimension.setId(longs.get(0));
                    int i = censusAssetsMapper.updateEmployeeSelective(dimension);
                }
            }
        }
    }

    @Override
    public List<Long> getAllTeamId() {
        return censusAssetsMapper.getAllTeamId();
    }


    @Override
    public List<TeamDataOverview> getStatistics(Long teamId) {
        //获取全部团队成员
        List<Long> list = getEmployeeIds(teamId);
        List<TeamDataOverview> overviews = new ArrayList<>();
//        催员统计
        for (Long employeeId : list) {
            TeamDataOverview dataOverview = new TeamDataOverview();
            dataOverview.setTeamId(teamId);
            dataOverview.setEmployeeId(employeeId);

            TeamDataOverview overview = new TeamDataOverview();
            TeamDataOverview entrustMoneyInfo = censusAssetsMapper.getEntrustMoneyInfo(dataOverview);
            if (entrustMoneyInfo != null) {
                overview.setInitialDebtTotal(entrustMoneyInfo.getInitialDebtTotal());
                overview.setResidualDebtTotal(entrustMoneyInfo.getResidualDebtTotal());
            }
            TeamDataOverview penTotal = censusAssetsMapper.getPenTotal(dataOverview);
            if (penTotal != null) {
                overview.setPenNumberTotal(penTotal.getPenNumberTotal());
                overview.setHouseholdsTotal(penTotal.getHouseholdsTotal());
            }
//            查询已归属/未归属的回款金额
            TeamDataOverview belongedInfo = censusAssetsMapper.getBelongedInfo(dataOverview);
            if (!ObjectUtils.isEmpty(belongedInfo)) {
                overview.setRecoveryBelonged(belongedInfo.getRecoveryBelonged());
//                overview.setRecoveryNotBelonged(belongedInfo.getRecoveryNotBelonged());
                BigDecimal notBelonged = belongedInfo.getNotBelonged1().add(belongedInfo.getNotBelonged2());
                overview.setRecoveryNotBelonged(notBelonged);
            } else {
                overview.setRecoveryBelonged(BigDecimal.ZERO);
                overview.setRecoveryNotBelonged(BigDecimal.ZERO);
            }

            BigDecimal total = overview.getRecoveryNotBelonged().add(overview.getRecoveryBelonged());
            overview.setRecoveryTotal(total);

            overview.setCreatedTime(new Date());
            overview.setTeamId(teamId);
            overview.setEmployeeId(employeeId);
            overview.setDelFlag(BaseConstant.DelFlag_Being);
            //员工类型
            overview.setAccountType(2);
            //统计员工
            overviews.add(overview);
        }
//        统计团队总数据
        addOverviewUtil(overviews, teamId);
//        overviews.add(overview);
        return overviews;
    }

    /**
     * 团队_数据概览之和
     *
     * @param overviews
     * @param teamId
     * @return
     */
    private TeamDataOverview addOverviewUtil(List<TeamDataOverview> overviews, Long teamId) {

//        TeamDataOverview overview = new TeamDataOverview();
//        BigDecimal initialDebtTotal = overviews.stream().map(TeamDataOverview::getInitialDebtTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal residualDebtTotal = overviews.stream().map(TeamDataOverview::getResidualDebtTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal recoveryTotal = overviews.stream().map(TeamDataOverview::getRecoveryTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal recoveryNotBelonged = overviews.stream().map(TeamDataOverview::getRecoveryNotBelonged).reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal recoveryBelonged = overviews.stream().map(TeamDataOverview::getRecoveryBelonged).reduce(BigDecimal.ZERO, BigDecimal::add);
//        int householdsTotal = overviews.stream().mapToInt(TeamDataOverview::getHouseholdsTotal).sum();
//        int penNumberTotal = overviews.stream().mapToInt(TeamDataOverview::getPenNumberTotal).sum();
//
//        overview.setTeamId(teamId);
//        overview.setInitialDebtTotal(initialDebtTotal);
//        overview.setResidualDebtTotal(residualDebtTotal);
//        overview.setRecoveryTotal(recoveryTotal);
//        overview.setRecoveryBelonged(recoveryBelonged);
//        overview.setRecoveryNotBelonged(recoveryNotBelonged);
//        overview.setHouseholdsTotal(householdsTotal);
//        overview.setPenNumberTotal(penNumberTotal);
//        overview.setDelFlag(BaseConstant.DelFlag_Being);
//        overview.setAccountType(1);
//        overview.setCreatedTime(new Date());
        TeamDataOverview dataOverview = new TeamDataOverview();
        dataOverview.setTeamId(teamId);

        TeamDataOverview overview = new TeamDataOverview();
        TeamDataOverview entrustMoneyInfo = censusAssetsMapper.getEntrustMoneyInfo(dataOverview);
        if (entrustMoneyInfo != null) {
            overview.setInitialDebtTotal(entrustMoneyInfo.getInitialDebtTotal());
            overview.setResidualDebtTotal(entrustMoneyInfo.getResidualDebtTotal());
        }
        TeamDataOverview penTotal = censusAssetsMapper.getPenTotal(dataOverview);
        if (penTotal != null) {
            overview.setPenNumberTotal(penTotal.getPenNumberTotal());
            overview.setHouseholdsTotal(penTotal.getHouseholdsTotal());
        }
//            查询已归属/未归属的回款金额
        TeamDataOverview belongedInfo = censusAssetsMapper.getBelongedInfo(dataOverview);
        if (!ObjectUtils.isEmpty(belongedInfo)) {
            overview.setRecoveryBelonged(belongedInfo.getRecoveryBelonged());
//                overview.setRecoveryNotBelonged(belongedInfo.getRecoveryNotBelonged());
            BigDecimal notBelonged = belongedInfo.getNotBelonged1().add(belongedInfo.getNotBelonged2());
            overview.setRecoveryNotBelonged(notBelonged);
        } else {
            overview.setRecoveryBelonged(BigDecimal.ZERO);
            overview.setRecoveryNotBelonged(BigDecimal.ZERO);
        }

        BigDecimal total = overview.getRecoveryNotBelonged().add(overview.getRecoveryBelonged());
        overview.setRecoveryTotal(total);

        overview.setCreatedTime(new Date());
        overview.setTeamId(teamId);
        overview.setDelFlag(BaseConstant.DelFlag_Being);
        //员工类型
        overview.setAccountType(1);
        //统计员工
        overviews.add(overview);
        return overview;
    }

    @Override
    public void getStatisticsTask() {
        List<Long> longList = getAllTeamId();
        //统计团队、每个员工各一条数据
        for (Long teamId : longList) {
            List<TeamDataOverview> overviewList = getStatistics(teamId);
            deleteOverview(teamId);
            overviewList.forEach(s -> {
                int i = censusAssetsMapper.addOverviewSelective(s);
            });
        }
    }

    @Override
    public TeamDataOverview selectStatistics(Long teamId) {
        int type = TokenInformation.getType();
        TeamDataOverview overview = new TeamDataOverview();
        if (type == 0) {
            //主账号
            overview = censusAssetsMapper.selectStatistics(1, teamId, null);
        } else if (type == 1) {
            //员工账号
            Integer userId = TokenInformation.getUserid();
            overview = censusAssetsMapper.selectStatistics(2, teamId, Long.valueOf(userId));
        } else {
            throw new SecurityException("该账号类型不存在");
        }
        return overview;
    }

    @Override
    public void deleteOverview(Long teamId) {
        censusAssetsMapper.deleteOverview(teamId);
    }


}
