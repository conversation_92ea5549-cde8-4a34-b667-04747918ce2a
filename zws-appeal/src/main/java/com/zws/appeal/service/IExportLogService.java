package com.zws.appeal.service;


import com.zws.appeal.domain.log.ExportLog;

import java.util.List;

/**
 * 导出日志 业务层接口
 *
 * <AUTHOR>
 * @date 2024年1月23日16:34:20
 */
public interface IExportLogService {

    /**
     * 写入
     *
     * @param record
     * @return
     */
    Long insert(ExportLog record);

    /**
     * 更新
     *
     * @param record
     */
    void update(ExportLog record);

    /**
     * 查询列表
     *
     * @param record
     * @return
     */
    List<ExportLog> selectList(ExportLog record);

}
