package com.zws.appeal.agservice;


import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.domain.ScheduleVo;
import com.zws.common.core.enums.MessageFormats;
import com.zws.common.core.enums.TimeContentFormats;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.threadpool.PoolManageMent;
import com.zws.common.core.threadpool.TaskManager;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.utils.SplitUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.domain.*;
import com.zws.appeal.pojo.*;
import com.zws.appeal.pojo.messageCenter.TeamMessageCenters;
import com.zws.appeal.pojo.staticConst.StaticConstantClass;
import com.zws.appeal.pojo.teamApplication.*;
import com.zws.appeal.pojo.teamParameters.*;
import com.zws.appeal.schedule.IScheduleService;
import com.zws.appeal.service.*;
import com.zws.appeal.thread.TeamSpecifyDivisionalPreview;
import com.zws.appeal.thread.TeamSpecifyDivisionalTask;
import com.zws.appeal.thread.TramRuleSplitPreviewTask;
import com.zws.appeal.thread.TramRuleSplitTask;
import com.zws.appeal.utils.*;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@Primary
@Transactional //开启事务注解
public class AgTeamCaseService {

    /**
     * 上传文件存储在本地的根路径
     */
    @Value("${filePate.file_path_link}")
    private String localFilePath;

    @Autowired
    private TeamCaseService teamCaseService;
    @Autowired
    private SettingsService settingsService;
    @Autowired
    private CaseService caseService;
    @Autowired
    private RuleDivisionService ruleDivisionService;
    @Autowired
    private RecordService recordService;
    @Autowired
    private DesensitizationRedis desensitizationRedis;
    @Autowired
    private MessageToolUtils messageToolUtils;
    @Autowired
    private AgCaseService agCaseService;
    @Autowired
    private IScheduleService scheduleService;
    @Autowired
    private ICaseRecordService caseRecordService;
    @Autowired
    private AgLawsuitService agLawsuitService;

    /**
     * 根据登录人id查询登录人部门及以下部门的所有部门id
     *
     * @param loginUser 当前登录人信息
     * @return
     */
    public List<Integer> Encapsulation(com.zws.system.api.model.LoginUser loginUser) {
        if (loginUser == null) {
            throw new ServiceException("当前登录信息获取失败");
        }
        Map<String, Object> accountInfo = loginUser.getAccountInfo();
        Integer userId = (Integer) accountInfo.get(UserConstants.USER_ID);
        Employees employees = teamCaseService.selectEmployeesId(userId);  //根据员工id查询员工部门id
        if (ObjectUtils.isEmpty(employees)) {
            return new ArrayList<Integer>();
        }
        Integer departmentId = employees.getDepartmentId();    //登录用户的部门id


        String departmentIds = String.valueOf(departmentId);  //强转为string类型
        List<Dept> deptList = teamCaseService.selectDept(TokenInformation.getCreateid(loginUser));//根据团队id查询该团队所有部门信息
        List<Integer> list = new ArrayList<>();
        list.add(departmentId);  //登录人本身的部门id
        for (Dept dept : deptList) {
            String ancestors = dept.getAncestors();  //祖级列表
            String[] split = ancestors.split(",");
            for (String splits : split) {
                if (splits.equals(departmentIds)) {
                    list.add(dept.getId());
                }
            }
        }
        return list;
    }


    /**
     * 根据部门id查询部门及以下部门的所有部门id
     *
     * @return
     */
    public List<Integer> Encapsulations(Integer deptId, LoginUser loginUser) {
        if (deptId == null) {
            return Encapsulation(loginUser);
        }
        Map<String, Object> accountInfo = loginUser.getAccountInfo();
        Integer teamId = (Integer) accountInfo.get(UserConstants.TEAM_ID);
        List<Dept> deptList = teamCaseService.selectDept(teamId);//根据团队id查询该团队所有部门信息
        List<Integer> list = new ArrayList<>();
        list.add(deptId);  //所传的部门id
        for (Dept dept : deptList) {
            String ancestors = dept.getAncestors();  //祖级列表
            List<String> splits = SplitUtils.strSplitComma(ancestors);
            for (String split : splits) {
                if (StrUtil.equals(split, StrUtil.toString(deptId))) {
                    list.add(dept.getId());
                }
            }
        }
        return list;
    }

    /**
     * 根据团队id以及部门id和条件查询该员工所在部门及以下的案件信息
     *
     * @param teamCaseUtils
     * @return
     */
    public List<CaseManage> selectTeamCase(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamCaseUtils.getStringDeptId())) {
            int dept = teamCaseUtils.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamCaseUtils.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamCaseUtils.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamCaseUtils.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamCaseUtils.setUserId(Integer.parseInt(teamCaseUtils.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamCaseUtils.getUserId())) {
            if (TokenInformation.getType(loginUser) == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }

        PageUtils.startPage();
        teamCaseUtils.setDeptIds(list);
        teamCaseUtils.setDeptId(null);
        teamCaseUtils.setCreateId(TokenInformation.getCreateid(loginUser));
        String packageNameList = teamCaseUtils.getPackageName();
        if (StringUtils.isNotEmpty(packageNameList)) {
            List<String> packageNameArr = SplitUtils.strSplit(packageNameList, BaseConstant.comma);
            teamCaseUtils.setPackageNameArr(packageNameArr);
        }
        List<CaseManage> caseManages = teamCaseService.selectCaseManageAndEmployees(teamCaseUtils);  //根据团队id以及部门id和条件查询该员工所在部门及以下的案件信息
        if (!ObjectUtils.isEmpty(caseManages)) {
            for (CaseManage caseManage : caseManages) {
                //解密
                DecryptUtils.dataDecrypt(caseManage);

                Date clientOverdueStart = caseManage.getClientOverdueStart();
                if (!ObjectUtils.isEmpty(clientOverdueStart)) {
                    long between = DateUtil.between(clientOverdueStart, new Date(), DateUnit.DAY);
                    caseManage.setOverdueDays(between);
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid(loginUser));
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (CaseManage caseManage : caseManages) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(caseManage.getClientName())) {
                        caseManage.setClientName(DataMaskingUtils.nameMasking(caseManage.getClientName()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(caseManage.getClientPhone())) {
                        caseManage.setClientPhone(DataMaskingUtils.phoneMasking(caseManage.getClientPhone()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(caseManage.getClientIdcard())) {
                        caseManage.setClientIdcard(DataMaskingUtils.idMasking(caseManage.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(caseManage.getClientCensusRegister())) {
                        caseManage.setClientCensusRegister(DataMaskingUtils.Masking(caseManage.getClientCensusRegister()));
                    }
                }
            }
        }
        return caseManages;
    }

    /**
     * 根据团队id以及部门id和条件查询该员工所在部门及以下的案件信息 （没有分页，导出使用）
     *
     * @param teamCaseUtils
     * @return
     */
    public List<CaseManage> selectExportTeamCase(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamCaseUtils.getStringDeptId())) {
            int dept = teamCaseUtils.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamCaseUtils.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamCaseUtils.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamCaseUtils.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamCaseUtils.setUserId(Integer.parseInt(teamCaseUtils.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamCaseUtils.getUserId())) {
            if (TokenInformation.getType(loginUser) == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }

        // 搜索结果全选
        if (teamCaseUtils.getCondition()) {
            teamCaseUtils.setCaseIds(null);
        }

//        PageUtils.startPage();
        teamCaseUtils.setDeptIds(list);
        teamCaseUtils.setDeptId(null);
        teamCaseUtils.setCreateId(TokenInformation.getCreateid(loginUser));
        String packageNameList = teamCaseUtils.getPackageName();
        if (StringUtils.isNotEmpty(packageNameList)) {
            List<String> packageNameArr = SplitUtils.strSplit(packageNameList, BaseConstant.comma);
            teamCaseUtils.setPackageNameArr(packageNameArr);
        }
        List<CaseManage> caseManages = teamCaseService.selectCaseManageAndEmployees(teamCaseUtils);  //根据团队id以及部门id和条件查询该员工所在部门及以下的案件信息
        if (!ObjectUtils.isEmpty(caseManages)) {
            for (CaseManage caseManage : caseManages) {
                //解密
                DecryptUtils.dataDecrypt(caseManage);

                Date clientOverdueStart = caseManage.getClientOverdueStart();
                if (!ObjectUtils.isEmpty(clientOverdueStart)) {
                    long between = DateUtil.between(clientOverdueStart, new Date(), DateUnit.DAY);
                    caseManage.setOverdueDays(between);
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid(loginUser));
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (CaseManage caseManage : caseManages) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(caseManage.getClientName())) {
                        caseManage.setClientName(DataMaskingUtils.nameMasking(caseManage.getClientName()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(caseManage.getClientPhone())) {
                        caseManage.setClientPhone(DataMaskingUtils.phoneMasking(caseManage.getClientPhone()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(caseManage.getClientIdcard())) {
                        caseManage.setClientIdcard(DataMaskingUtils.idMasking(caseManage.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(caseManage.getClientCensusRegister())) {
                        caseManage.setClientCensusRegister(DataMaskingUtils.Masking(caseManage.getClientCensusRegister()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(caseManage.getRegisteredAddress())) {
                        caseManage.setRegisteredAddress(DataMaskingUtils.Masking(caseManage.getRegisteredAddress()));
                    }
                    if (desensitization.getHomeAddress() == 1 && !ObjectUtils.isEmpty(caseManage.getHomeAddress())) {
                        caseManage.setHomeAddress(DataMaskingUtils.Masking(caseManage.getHomeAddress()));
                    }
                    if (desensitization.getEntityName() == 1 && !ObjectUtils.isEmpty(caseManage.getPlaceOfWork())) {
                        caseManage.setPlaceOfWork(DataMaskingUtils.Masking(caseManage.getPlaceOfWork()));
                    }
                }
            }
        }
        return caseManages;
    }

    /**
     * 根据团队id以及部门id和条件查询该员工所在部门及以下的案件信息(本页选中/搜索结果全选)
     *
     * @param teamCaseUtils
     * @return
     */
    public List<CaseManage> selectTeamCases(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamCaseUtils.getStringDeptId())) {
            int dept = teamCaseUtils.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamCaseUtils.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamCaseUtils.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamCaseUtils.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamCaseUtils.setUserId(Integer.parseInt(teamCaseUtils.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamCaseUtils.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        teamCaseUtils.setDeptIds(list);
        teamCaseUtils.setDeptId(null);

        List<CaseManage> caseManages = teamCaseService.selectCaseManageAndEmployeesCondition(teamCaseUtils);  //根据团队id以及部门id和条件查询该员工所在部门及以下的案件信息
        return caseManages;
    }


    /**
     * 获取团队案件的查询部门ID集合
     *
     * @param teamCaseUtils
     * @param loginUser     当前登录人信息
     * @return
     */
    private List<Integer> getTeamCaseDeptIds(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        List<Integer> list;
        Map<String, Object> accountInfo = loginUser.getAccountInfo();
        Integer accountNumber = (Integer) accountInfo.get(UserConstants.ACCOUNT_TYPE);
        if (UserConstants.ACCOUNT_TYPE_1 == accountNumber) {    //员工账号
            if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                list = Encapsulation(loginUser);
            } else {
                list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
            }
        } else {
            if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                list = null;
            } else {
                list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
            }
        }
        return list;
    }

    /**
     * 根据团队id以及部门id和条件查询该员工所在部门及以下的案件id集合(本页选中/搜索结果全选)--团队规则分案
     *
     * @param teamCaseUtils
     * @return
     */
    public List<Long> selectTeamCaseCaseIdCount(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        List<Integer> list;
        Map<String, Object> accountInfo = loginUser.getAccountInfo();
        Integer accountNumber = (Integer) accountInfo.get(UserConstants.ACCOUNT_TYPE);
        if (UserConstants.ACCOUNT_TYPE_1 == accountNumber) {    //员工账号
            if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                list = Encapsulation(loginUser);
            } else {
                list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
            }
        } else {
            if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                list = null;
            } else {
                list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
            }
        }
        teamCaseUtils.setDeptIds(list);
        teamCaseUtils.setDeptId(null);
        List<Long> list1 = teamCaseService.selectTeamCaseCaseIdCount(teamCaseUtils);//根据团队id以及部门id和条件查询该员工所在部门及以下的案件信息
        return list1;
    }


    /**
     * 根据字段查询该团队的案件总金额以及总案件量
     *
     * @param teamCaseUtils
     * @return
     */
    public Map<String, Object> selectCaseManagesMoney(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        Map<String, Object> map = selectTeamCaseCount(teamCaseUtils, loginUser);
        return map;
    }

    /**
     * 根据团队id以及部门id和条件统计案件金额与数量
     *
     * @param teamCaseUtils
     * @return
     */
    public Map<String, Object> selectTeamCaseCount(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamCaseUtils.getStringDeptId())) {
            int dept = teamCaseUtils.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamCaseUtils.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamCaseUtils.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamCaseUtils.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamCaseUtils.setUserId(Integer.parseInt(teamCaseUtils.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamCaseUtils.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }

        teamCaseUtils.setDeptIds(list);
        teamCaseUtils.setDeptId(null);
        Map<String, Object> map = teamCaseService.selectCaseCount(teamCaseUtils);
        return map;
    }

    /**
     * 退案（根据所传条件查询案件申请退案）
     *
     * @param teamCaseUtils
     */
    public void caseWithdrawal(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        List<CaseManage> caseManages = selectTeamCases(teamCaseUtils, loginUser);
        List<Long> list = new ArrayList<>();
        if (ObjectUtils.isEmpty(caseManages)) {
            throw new GlobalException("退案申请案件不能为空");
        }
        for (CaseManage caseManage : caseManages) {
            list.add(caseManage.getCaseId());
        }
        RetentionWithdrawalCase retentionWithdrawalCase = new RetentionWithdrawalCase();
        retentionWithdrawalCase.setCaseId(list);
        retentionWithdrawalCase.setReason(teamCaseUtils.getReason());
        retentionWithdrawalCase.setCaseManages(caseManages);
        caseService.insertRecord(retentionWithdrawalCase);

        if (TokenInformation.getType(loginUser) == UserConstants.ACCOUNT_TYPE_0) {
            List<Long> ids = caseRecordService.selectMainRecord(2, TokenInformation.getCreateid(loginUser));
            //催收端主账号,自动审批通过
            ApprovalRecord approvalRecord = new ApprovalRecord();
            approvalRecord.setApproveCode(5);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
            approvalRecord.setIds(ids);
            approvalRecord.setApproveStart(0);
            agCaseService.WriteModification(approvalRecord, loginUser);
        } else {
            try {
                //        申请成功给第一级审批人发送提醒
                ApprovalSteps approvalSteps = new ApprovalSteps();
                approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
                approvalSteps.setApproveCode(5); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
                TeamMessageCenters teamMessageCenters = messageToolUtils.selectApprovalProcess(approvalSteps);
                messageToolUtils.messageReminder(MessageFormats.APPROVAL_RETURN, teamMessageCenters);
            } catch (Exception e) {
                log.error("申请成功给第一级审批人发送提醒 异常：", e);
            }
        }
        //加入时效管理
        agLawsuitService.addTimeManage(loginUser,teamCaseUtils.getCaseIds(), TimeContentFormats.RETURN_RECORD);
    }

    /**
     * 留案（根据所传条件查询案件申请留案）
     *
     * @param teamCaseUtils
     */
    public void caseKeepCase(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        List<CaseManage> caseManages = selectTeamCases(teamCaseUtils, loginUser);
        List<Long> list = new ArrayList<>();
        if (ObjectUtils.isEmpty(caseManages)) {
            throw new GlobalException("留案申请案件不能为空");
        }
        RetentionWithdrawalCase retentionWithdrawalCase = new RetentionWithdrawalCase();
        for (CaseManage caseManage : caseManages) {
            list.add(caseManage.getCaseId());
        }
        retentionWithdrawalCase.setCaseId(list);
        retentionWithdrawalCase.setReason(teamCaseUtils.getReason());
        retentionWithdrawalCase.setCaseManages(caseManages);
        caseService.insertKeepCase(retentionWithdrawalCase);

        if (TokenInformation.getType(loginUser) == UserConstants.ACCOUNT_TYPE_0) {
            List<Long> ids = caseRecordService.selectMainRecord(1, TokenInformation.getCreateid(loginUser));
            //催收端主账号,自动审批通过
            ApprovalRecord approvalRecord = new ApprovalRecord();
            approvalRecord.setApproveCode(3);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
            approvalRecord.setIds(ids);
            approvalRecord.setApproveStart(0);
            agCaseService.WriteModification(approvalRecord, loginUser);
        } else {
            try {
                //        申请成功给第一级审批人发送提醒
                ApprovalSteps approvalSteps = new ApprovalSteps();
                approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
                approvalSteps.setApproveCode(3); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
                TeamMessageCenters teamMessageCenters = messageToolUtils.selectApprovalProcess(approvalSteps);
                messageToolUtils.messageReminder(MessageFormats.APPROVAL_STAY, teamMessageCenters);
            } catch (Exception e) {
                log.error("申请成功给第一级审批人发送提醒 异常：", e);
            }

        }
        //加入时效管理
        agLawsuitService.addTimeManage(loginUser,teamCaseUtils.getCaseIds(), TimeContentFormats.STAY_RECORD);

    }

    /**
     * 停催（根据所传条件查询案件申请停催）
     *
     * @param teamCaseUtils
     */
    public void caseStopUrging(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        throw new GlobalException("暂不支持");
//        List<CaseManage> caseManages = selectTeamCases(teamCaseUtils, loginUser);
//        List<Long> list = new ArrayList<>();
//        if (ObjectUtils.isEmpty(caseManages)) {
//            throw new GlobalException("停催申请案件不能为空");
//        }
//        for (CaseManage caseManage : caseManages) {
//            list.add(caseManage.getCaseId());
//        }
//        //caseService.insertStopUrging(list);
//
//        if (TokenInformation.getType(loginUser) == UserConstants.ACCOUNT_TYPE_0) {
//            List<Long> ids = caseRecordService.selectMainRecord(0, TokenInformation.getCreateid(loginUser));
//            //催收端主账号,自动审批通过
//            ApprovalRecord approvalRecord = new ApprovalRecord();
//            approvalRecord.setApproveCode(4);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
//            approvalRecord.setIds(ids);
//            approvalRecord.setApproveStart(0);
//            agCaseService.WriteModification(approvalRecord, loginUser);
//        } else {
//            try {
//                //        申请成功给第一级审批人发送提醒
//                ApprovalSteps approvalSteps = new ApprovalSteps();
//                approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
//                approvalSteps.setApproveCode(4); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
//                TeamMessageCenters teamMessageCenters = messageToolUtils.selectApprovalProcess(approvalSteps);
//                messageToolUtils.messageReminder(MessageFormats.STOP_URGING, teamMessageCenters);
//            } catch (Exception e) {
//                log.error("申请成功给第一级审批人发送提醒 异常：", e);
//            }
//        }
//        //加入时效管理
//        agLawsuitService.addTimeManage(loginUser,teamCaseUtils.getCaseIds(), TimeContentFormats.ASSET_STOP_RECORD);
    }

    /**
     * 标记案件案件查询
     *
     * @param teamCaseUtils
     * @return
     */
    public void selectMarkCase(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        List<CaseManage> caseManages = selectTeamCases(teamCaseUtils, loginUser);
        List<CaseManage> caseManageList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(caseManages)) {
            for (CaseManage caseManage : caseManages) {
                CaseManage caseManage1 = new CaseManage();
                caseManage1.setCaseId(caseManage.getCaseId());
                caseManage1.setId(caseManage.getId());
                caseManage1.setLabel(teamCaseUtils.getCode().toString());
                caseManage1.setOutsourcingTeamId(new Long((long) TokenInformation.getCreateid()));
                caseManageList.add(caseManage1);
            }
        }
        caseService.updateCaseRecovery(caseManageList);
        //加入时效管理
        agLawsuitService.addTimeManage(loginUser,teamCaseUtils.getCaseIds(), TimeContentFormats.MARK);
    }

    /**
     * 指定分案查询数据以及可分配案件/案件总金额
     *
     * @param teamCaseUtils
     * @return
     */
    public Map<String, Object> selectId(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        teamCaseUtils.setDecryptKey(FieldEncryptUtil.fieldKey);
        List<Long> arrayList = selectTeamCaseCaseIdCount(teamCaseUtils, loginUser);  //案件id集合
        Map<String, Object> map = new HashMap<>();

//        未分配
        List<Integer> list = new ArrayList<>();
        list.add(0);
        teamCaseUtils.setCaseStateList(list);
        Map<String, Object> map1 = teamCaseService.selectCaseManageCount(teamCaseUtils);
        Long quantity = (Long) map1.get("quantity");  //数量
        BigDecimal clientMoney = (BigDecimal) map1.get("clientMoney");  //金额总量
        if (ObjectUtils.isEmpty(clientMoney)) {
            clientMoney = BigDecimal.ZERO;
        }

//        已分配+留案
        List<Integer> arrayLists = new ArrayList<>();
        arrayLists.add(1);
        arrayLists.add(3);
        teamCaseUtils.setCaseStateList(arrayLists);
        Map<String, Object> map2 = teamCaseService.selectCaseManageCount(teamCaseUtils);
        Long quantitys = (Long) map2.get("quantity");  //数量
        BigDecimal clientMoneys = (BigDecimal) map2.get("clientMoney");  //金额总量
        if (ObjectUtils.isEmpty(clientMoneys)) {
            clientMoneys = BigDecimal.ZERO;
        }

        map.put("weifenpei", quantity);
        map.put("yifenpei", quantitys);
        map.put("zongshu", quantity + quantitys);
        map.put("zongjine", clientMoney.add(clientMoneys));
        map.put("arrayList", arrayList);
        return map;
    }

    /**
     * 指定分案-预览数据
     *
     * @param specifyRules
     * @param scheduleVo
     * @return
     */
    public void specifyDivisionalDataPreview(SpecifyRules specifyRules, ScheduleVo scheduleVo) {

        scheduleVo.setSchedule(1);
        scheduleService.setSchedule(scheduleVo);

        specifyRules.getTeamCaseUtils().setCreateId(TokenInformation.getCreateid());
        log.info("异步前，团队ID：" + TokenInformation.getCreateid());
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        TeamSpecifyDivisionalPreview workTask = new TeamSpecifyDivisionalPreview(specifyRules, SecurityUtils.getLoginUser(), scheduleVo);
        TaskManager.addTask(workTask);

        return;
    }

    /**
     * 指定分案返回数据
     *
     * @param specifyRules
     * @param scheduleVo   异步进度
     * @return
     */
    public List<DataPreview> specifyDivisionalData(SpecifyRules specifyRules, LoginUser loginUser, ScheduleVo scheduleVo) {
        TeamCaseUtils teamCaseUtils = specifyRules.getTeamCaseUtils();  //查询条件
        scheduleVo.setSchedule(10);
        scheduleService.setSchedule(scheduleVo);
        List<CaseManage> assignableCases = selectTeamCases(teamCaseUtils, loginUser);

        List<DataPreview> dataPreviews = new ArrayList<>();   //返回预览的数据


        List<Distribution> personnelInfos = specifyRules.getDistributions();  //人员信息集合
        for (int i = 0; i < personnelInfos.size(); i++) {
            personnelInfos.get(i).setSort(i);
        }

        TimeInterval timer = DateUtil.timer();
        timer.start("1");

        //已经满足添加的人员
        List<Distribution> reachedPersonnels = new ArrayList<>();

        for (Distribution team : personnelInfos) {
            team.setNumericalValue(new BigDecimal(team.getNumber()));
            team.setMoney(BigDecimal.ZERO);
            team.setNumber(0);
        }

        scheduleVo.setSchedule(20);
        scheduleService.setSchedule(scheduleVo);


        //把案件根据金额排序
        // 使用Java 8 Stream API对objList进行金额倒序排序
        List<CaseManage> sortedList = assignableCases.stream()
                .sorted(Comparator.comparing(CaseManage::getClientMoney).reversed())
                .collect(Collectors.toList());
        assignableCases = sortedList;

        scheduleVo.setSchedule(30);
        scheduleService.setSchedule(scheduleVo);

        int inistSchedule = 30;
        int initSize = assignableCases.size();

        int order = 0;//0-正序，1-倒序
        int lastIndex = 0; //上一个团队index
        int index = 0;     //当前团队index
        while (assignableCases.size() > 0) {
            if (personnelInfos.size() == 0) {
                break;
            }
            if (index > personnelInfos.size() - 1) {
                index = personnelInfos.size() - 1;
            }

            //更新进度，无关业务
            double s = 1 - (double) assignableCases.size() / initSize;
            int si = inistSchedule + (int) (s * 60);
            if (si > 90) {
                si = 90;
            }
            scheduleVo.setSchedule(si);
            scheduleService.setSchedule(scheduleVo);
            //更新进度，无关业务

            Distribution team = personnelInfos.get(index);
            int notNum = 0;
            Iterator<CaseManage> iterator = assignableCases.iterator();
            while (iterator.hasNext()) {

                CaseManage manage = iterator.next();
                List<CaseManage> manages = team.getList();
                manages.add(manage);
                team.setList(manages);
                team.setMoney(team.getMoney().add(manage.getClientMoney()));
                team.setNumber(manages.size());

                log.info("团队指定分案，案件：{}，金额：{}，--分给：{}", manage.getCaseId(), manage.getClientMoney(), team.getOdvName());
                iterator.remove();
                break;
            }
            //判断是否满足团队要求
            boolean isReached = agCaseService.checkIsReachedDistri(0, team);
            //boolean isReached =false;
            if (isReached || notNum == assignableCases.size()) {
                reachedPersonnels.add(team);
                personnelInfos.remove(team);
                log.info("移除：" + team.getOdvName());
                if (order == 0) {
                    if (index > personnelInfos.size() - 1) {
                        index--;
                        order = 1;//改变顺序
                    }
                } else {
                    if (index == 0) {
                        order = 0;//改变顺序
                    }
                }
            } else {
                if (personnelInfos.size() > 1) {
                    if (index == 0) {
                        if (order == 1) {
                            order = 0;
                            lastIndex = -1;
                            index = -1;
                        }
                    }
                    if (index == personnelInfos.size() - 1) {
                        if (order == 0) {
                            order = 1;
                            lastIndex = personnelInfos.size();
                            index = personnelInfos.size();
                        }
                    }
                    lastIndex = index;
                    if (order == 0) {
                        index++;
                    } else {
                        index--;
                    }
                }
            }
        }

        scheduleVo.setSchedule(90);
        scheduleService.setSchedule(scheduleVo);

        personnelInfos.addAll(reachedPersonnels);

        List<Distribution> teamsSort = personnelInfos.stream()
                .sorted(Comparator.comparing(Distribution::getSort))
                .collect(Collectors.toList());

        for (Distribution temp : teamsSort) {
            log.info("催员：{}，工号：{}， 案件量：{}，案件金额：{}", temp.getOdvName(), temp.getJobNumber(), temp.getNumber(), temp.getMoney());
        }

        long t = timer.intervalMs("1");
        log.info("执行分案-广度优先 耗时:" + t + " ms");

        for (Distribution temp : teamsSort) {
            DataPreview dataPreview = new DataPreview();
            dataPreview.setMoney(temp.getMoney());
            List<Long> caseIds = new ArrayList<>();
            for (CaseManage caseManage : temp.getList()) {
                caseIds.add(caseManage.getCaseId());
            }
            dataPreview.setIds(caseIds);
            dataPreview.setNumber(temp.getNumber());
            dataPreview.setJobNumber(temp.getJobNumber());
            dataPreview.setOdvId(temp.getOdvId());
            dataPreview.setOdvName(temp.getOdvName());
            dataPreviews.add(dataPreview);
        }
        scheduleVo.setSchedule(95);
        scheduleService.setSchedule(scheduleVo);
        return dataPreviews;
    }


    /**
     * 指定分案写入催收员（我的团队）
     *
     * @param specifyRules
     */
    public void updateCase(SpecifyRules specifyRules) {
        specifyRules.getTeamCaseUtils().analysiStringDeptId();
        specifyRules.getTeamCaseUtils().setCreateId(TokenInformation.getCreateid());

        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        TeamSpecifyDivisionalTask workTask = new TeamSpecifyDivisionalTask(specifyRules, SecurityUtils.getLoginUser());
        TaskManager.addTask(workTask);
        /*List<DataPreview> dataPreviews = specifyDivisionalData(specifyRules,new ScheduleVo());
        caseService.updateCaseManage(dataPreviews);*/
    }

    /**
     * 按分案范围匹配数据
     *
     * @param specifyRules
     * @return
     */
    public List<CaseManage> selectData(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        List<CaseManage> caseManages = teamCaseService.selectCaseManageRuleDivision(teamCaseUtils, loginUser);  //根据团队id以及部门id和条件查询该员工所在部门及以下的案件信息
        return caseManages;
    }

    /**
     * 查询共债的身份证号集合
     *
     * @param specifyRules
     * @return
     */
    public List<String> selectJointDebtIdCards(SpecifyRules specifyRules, LoginUser loginUser) {
        TeamCaseUtils teamCaseUtils = getTeamCaseAllocationStatus(specifyRules);
        List<Integer> deptIds = getTeamCaseDeptIds(teamCaseUtils, loginUser);
        teamCaseUtils.setDeptIds(deptIds);
        teamCaseUtils.setDeptId(null);
        List<String> idCards = teamCaseService.selectJointDebtIdCards(teamCaseUtils);  //根
        return idCards;
    }

    /**
     * 按分案范围匹配数据-查询案件总的金额
     *
     * @param teamCaseUtils
     * @return
     */
    public Map<String, Object> selectDataMoney(TeamCaseUtils teamCaseUtils, LoginUser loginUser) {
        Map<String, Object> map = teamCaseService.selectCaseManageMoneyCount(teamCaseUtils, loginUser);//根据团队id以及部门id和条件查询该员工所在部门及以下的案件信息
        return map;
    }

    /**
     * 团队案件的分配范围
     *
     * @param specifyRules
     * @return
     */
    private TeamCaseUtils getTeamCaseAllocationStatus(SpecifyRules specifyRules) {
        TeamCaseUtils teamCaseUtils = specifyRules.getTeamCaseUtils();
//        按分案范围匹配数据
        if (specifyRules.getDivisionScope().equals(0)) {
            List<String> list = new ArrayList<>();
            list.add("0");
            list.add("1");
            list.add("3");
            teamCaseUtils.setAllocationStatus(list);
        } else if (specifyRules.getDivisionScope().equals(1)) {
            List<String> list = new ArrayList<>();
            list.add("0");  //未分配
            teamCaseUtils.setAllocationStatus(list);
        } else {
            throw new GlobalException("分案范围错误");
        }
        return teamCaseUtils;
    }

    /**
     * 规则分案(写入分案记录，修改案件催收员)
     *
     * @param specifyRules
     */
    public void writeRuleDivision(SpecifyRules specifyRules) {

        specifyRules.getTeamCaseUtils().analysiStringDeptId();
        specifyRules.getTeamCaseUtils().setCreateId(TokenInformation.getCreateid());
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        TramRuleSplitTask workTask = new TramRuleSplitTask(specifyRules, SecurityUtils.getLoginUser());
        TaskManager.addTask(workTask);
    }

    /**
     * 规则分案（预览分案结果）
     *
     * @param specifyRules 分案规则
     * @param scheduleVo   异步进度
     * @return
     */
    public void tramRuleSplitPreview(SpecifyRules specifyRules, ScheduleVo scheduleVo) {
        if (ObjectUtils.isEmpty(specifyRules.getDivisionScope())) {
            throw new ServiceException("分案范围不能为空");
        }
        if (ObjectUtils.isEmpty(specifyRules.getDivisionalMode())) {
            throw new ServiceException("分配模式不能为空");
        }
        if (ObjectUtils.isEmpty(specifyRules.getDistributions())) {
            throw new ServiceException("请选择分案人员");
        }
        if (ObjectUtils.isEmpty(specifyRules.getDivisionMethod())) {
            throw new ServiceException("请选择分配方式");
        }
        if (specifyRules.getDivisionalPrinciple() == null) {
            throw new ServiceException("分配规则数据格式错误，请联系管理员处理");
        }

        scheduleVo.setSchedule(1);
        scheduleService.setSchedule(scheduleVo);

        specifyRules.getTeamCaseUtils().analysiStringDeptId();
        specifyRules.getTeamCaseUtils().setCreateId(TokenInformation.getCreateid());
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        TramRuleSplitPreviewTask workTask = new TramRuleSplitPreviewTask(specifyRules, SecurityUtils.getLoginUser(), scheduleVo);
        TaskManager.addTask(workTask);

    }


    /**
     * 团队-规则分案
     *
     * @param specifyRules
     * @param loginUser    当前登录人信息
     * @param scheduleVo
     * @return
     */
    public List<Distribution> tramRuleSplit(SpecifyRules specifyRules, LoginUser loginUser, ScheduleVo scheduleVo) {
        scheduleVo.setSchedule(3);
        scheduleService.setSchedule(scheduleVo);

        if (specifyRules.getTeamCaseUtils().getCondition()) {
            specifyRules.getTeamCaseUtils().setCaseIds(null);
        }

        Map<String, Object> accountInfo = loginUser.getAccountInfo();
        Integer teamId = (Integer) accountInfo.get(UserConstants.TEAM_ID);
        TeamCaseUtils teamCaseUtils = getTeamCaseAllocationStatus(specifyRules);
        List<Integer> deptIds = getTeamCaseDeptIds(teamCaseUtils, loginUser);
        teamCaseUtils.setDeptIds(deptIds);
        teamCaseUtils.setDeptId(null);
        teamCaseUtils.setCreateId(teamId);
        teamCaseUtils.setDecryptKey(FieldEncryptUtil.fieldKey);
        teamCaseUtils.setCreateId(teamId);
        //log.info("统计-团队ID："+teamCaseUtils.getCreateId());

        BigDecimal number = BigDecimal.ZERO;    //案件总金额
        long numbers = 0;    //案件总量
        Map<String, Object> map = selectDataMoney(teamCaseUtils, loginUser);
        number = (BigDecimal) map.get("money");
        numbers = (Long) map.get("num");
        scheduleVo.setSchedule(5);
        scheduleService.setSchedule(scheduleVo);

//        分配模式如果为百分比转换为具体数字
        List<Distribution> distributions = specifyRules.getDistributions();
        for (Distribution distribution : distributions) {
            if (ObjectUtils.isEmpty(distribution.getValuePercentage()) && ObjectUtils.isEmpty(distribution.getNumericalValue())) {
                throw new GlobalException("员工分案数量/数量百分比不能为空");
            }
            String valuePercentage = distribution.getValuePercentage();

            if (!ObjectUtils.isEmpty(valuePercentage)) {
                if (!StringUtils.isNumeric(valuePercentage)) {
                    throw new GlobalException("请填写正确的数量百分比");
                }
                double v = Double.parseDouble(valuePercentage);
                double v1 = v / 100;
                if (specifyRules.getDivisionalMode() == 0 || specifyRules.getDivisionalMode() == 2) {
                    if (specifyRules.getDivisionMethod() == 1) {
                        int v2 = (int) (numbers * v1);   //该员工所占案件数量
                        distribution.setNumericalValue(new BigDecimal(v2 + ""));
                    }
                } else if (specifyRules.getDivisionalMode() == 1 && specifyRules.getDivisionMethod() == 1) {
                    BigDecimal bigDecimal = new BigDecimal(v1 + "");
                    BigDecimal multiply = number.multiply(bigDecimal);   //该员工所占总金额
                    distribution.setNumericalValue(multiply);
                }
            }
        }

        scheduleVo.setSchedule(10);
        scheduleService.setSchedule(scheduleVo);

        //按分案范围匹配数据
        List<CaseManage> caseManageList = selectData(teamCaseUtils, loginUser);

        if (numbers != caseManageList.size() || numbers == 0) {
            log.error("分案异常 进度编号：{}，统计量：{}，明细量：{},参数:{}", scheduleVo.getScheduleNo(), numbers,
                    caseManageList.size(), JSONUtil.toJsonStr(teamCaseUtils));

            throw new ServiceException("分案异常，请联系管理员处理");
        }

        scheduleVo.setSchedule(15);
        scheduleService.setSchedule(scheduleVo);

        List<Distribution> distribution = null;  //分配完毕的人员案件信息

        distribution = generalTreatment(specifyRules, caseManageList, scheduleVo);


        scheduleVo.setSchedule(90);
        scheduleService.setSchedule(scheduleVo);

        return distribution;
    }

    /**
     * 判断分案原则
     *
     * @param divisionalPrinciple
     * @return
     */
    public int selectSwitch(Integer divisionalPrinciple) {
        if (ObjectUtils.isEmpty(divisionalPrinciple)) {   //没有交叉选项
            return 1;
        } else if (!ObjectUtils.isEmpty(divisionalPrinciple) && divisionalPrinciple == 1) {  //选择交叉分案
            return 2;
        }
        return 0;
    }

    /**
     * 处理共债信息
     *
     * @param allCaseManages 所有案件
     * @param idCards        共债身份证
     * @return
     */
    public Map<String, Object> coDebtData(List<CaseManage> allCaseManages, List<String> idCards) {
        TimeInterval timer = DateUtil.timer();
        timer.start("1");

        List<JointDebtDivision> jointDebts = new ArrayList<>();
        List<CaseManage> caseManageList = new ArrayList<>();
        for (String id : idCards) {
            List<CaseManage> caseManages = new ArrayList<>();
            BigDecimal amount = BigDecimal.ZERO;

            Iterator<CaseManage> caseManageIterator = allCaseManages.iterator();
            while (caseManageIterator.hasNext()) {
                CaseManage temp = caseManageIterator.next();
                if (StrUtil.equals(temp.getClientIdcard(), id)) {
                    caseManages.add(temp);
                    amount = amount.add(temp.getClientMoney());
                    caseManageIterator.remove();
                }

            }
            JointDebtDivision jointDebtDivision = new JointDebtDivision();
            jointDebtDivision.setIdentityNumber(id);
            jointDebtDivision.setList(caseManages);
            jointDebtDivision.setMoney(amount);
            jointDebtDivision.setNumber(caseManages.size());
            jointDebts.add(jointDebtDivision);
        }
        //剩余的就是非共债
        caseManageList = allCaseManages;
        Map<String, Object> map = new HashMap<>();
        map.put("arrayList", jointDebts);
        map.put("caseManage1", caseManageList);
        return map;
    }


    /**
     * 处理分案
     *
     * @param specifyRules
     * @param assignableCases
     * @return
     */
    public List<Distribution> generalTreatment(SpecifyRules specifyRules, List<CaseManage> assignableCases, ScheduleVo scheduleVo) {
        List<Distribution> personnelInfos = specifyRules.getDistributions();  //人员信息集合

        for (int i = 0; i < personnelInfos.size(); i++) {
            personnelInfos.get(i).setSort(i);
        }

        log.info("处理分案,进度编号：{}， 总案件量：{}", scheduleVo.getScheduleNo(), assignableCases.size());


        TimeInterval timer = DateUtil.timer();
        timer.start("1");

        //团队按照需要分案案件数量从大到小排序
        int mode = specifyRules.getDivisionalMode();  //分配模式
        //是否共债优先
        boolean jointDet = specifyRules.getDivisionalPrinciple().contains(0);
        //是否隔月分案
        boolean intervalMonth = specifyRules.getDivisionalPrinciple().contains(1);
        //已经满足添加的人员
        List<Distribution> reachedPersonnels = new ArrayList<>();
        for (Distribution team : personnelInfos) {
            team.setMoney(BigDecimal.ZERO);
            team.setNumber(0);
            if (jointDet) {
                team.setTotalJointDebNum(0);
            }
        }

        scheduleVo.setSchedule(16);
        scheduleService.setSchedule(scheduleVo);

        //唯一
        Set<String> uniqueIds = new HashSet<>();
        //重复，有共债的案件
        Set<String> duplicateIds = new HashSet<>();

        //案件的案件数
        Map<String, Integer> idCountMap = new HashMap<>();
        //共债案件金额
        Map<String, BigDecimal> idSumMap = new HashMap<>();
        Map<String, JointDebtDivision> jointDebtMap = new HashMap<>();

        if (jointDet) {
            //共债优先的，先找出共债 的身份证号
            timer.start("2-1");
            for (CaseManage temp : assignableCases) {
                String idcard = temp.getClientIdcard();

                if (!uniqueIds.add(idcard)) {
                    duplicateIds.add(idcard);
                    //删除第一次存入uniqueIds  Set中的数据
                    uniqueIds.remove(idcard);
                }
                int num = idCountMap.getOrDefault(idcard, 0) + 1;
                idCountMap.put(idcard, num);
                BigDecimal amount = idSumMap.getOrDefault(idcard, BigDecimal.ZERO).add(temp.getClientMoney());
                idSumMap.put(idcard, amount);

                JointDebtDivision jointDebtDivision = new JointDebtDivision();
                jointDebtDivision.setIdentityNumber(idcard);
                //jointDebtDivision.setList(caseManages);
                jointDebtDivision.setMoney(amount);
                jointDebtDivision.setNumber(num);
                jointDebtMap.put(idcard, jointDebtDivision);
            }

            /*//没有共债的 身份证
            Set<String> notJointDeb=new HashSet<>();
            for (String key :jointDebtMap.keySet()) {
                JointDebtDivision jointDebtDivision = jointDebtMap.get(key);
                if (jointDebtDivision.getNumber()<2){
                    notJointDeb.add(key);
                }
            }*/


            scheduleVo.setSchedule(18);
            scheduleService.setSchedule(scheduleVo);


            List<JointDebtDivision> jointDebts = new ArrayList<>();
            for (String idcard : jointDebtMap.keySet()) {
                JointDebtDivision jointDebt = jointDebtMap.get(idcard);
                if (jointDebt.getNumber() > 1) {
                    jointDebts.add(jointDebt);
                }
                //jointDebts.add(jointDebt);
            }
            scheduleVo.setSchedule(20);
            scheduleService.setSchedule(scheduleVo);

            // 使用Java 8 Stream API对objList进行金额倒序排序
            List<JointDebtDivision> sortedList = jointDebts.stream()
                    .sorted(Comparator.comparing(JointDebtDivision::getMoney).reversed())
                    .collect(Collectors.toList());
            scheduleVo.setSchedule(22);
            scheduleService.setSchedule(scheduleVo);

            long t21 = timer.intervalMs("2-1");
            log.info("执行分案-广度优先-共债 2-1 耗时:" + t21 + " ms");

            int inistSchedule = 25;
            int initSize = jointDebts.size();

            timer.start("2-3");
            jointDebts = sortedList;
            int order = 0;//0-正序，1-倒序
            int lastIndex = 0; //上一个团队index
            int index = 0;     //当前团队index
            while (jointDebts.size() > 0) {
                if (personnelInfos.size() == 0) {
                    break;
                }
                //更新进度，无关业务
                double s = 1 - (double) jointDebts.size() / initSize;
                int si = inistSchedule + (int) (s * 25);
                if (si > 50) {
                    si = 50;
                }
                scheduleVo.setSchedule(si);
                scheduleService.setSchedule(scheduleVo);
                //更新进度，无关业务


                if (index > personnelInfos.size() - 1) {
                    index = personnelInfos.size() - 1;
                }
                Distribution team = personnelInfos.get(index);
                int notNum = 0;


                Iterator<JointDebtDivision> iterator = jointDebts.iterator();
                while (iterator.hasNext()) {
                    JointDebtDivision jointDebt = iterator.next();

                    List<CaseManage> tempManages = assignableCases.stream().filter(e -> StrUtil.equals(e.getClientIdcard(), jointDebt.getIdentityNumber())).collect(Collectors.toList());
                    assignableCases.removeAll(tempManages);

                    List<CaseManage> manages = team.getList();
                    manages.addAll(tempManages);
                    team.setList(manages);
                    for (CaseManage tempManage : tempManages) {
                        team.setMoney(team.getMoney().add(tempManage.getClientMoney()));
                    }
                    team.setNumber(manages.size());
                    if (tempManages.size() > 1) {
                        //案件数大于1 的才算共债一个
                        int totalJoinCount = team.getTotalJointDebNum() == null ? 0 : team.getTotalJointDebNum();
                        team.setTotalJointDebNum(totalJoinCount + 1);
                    }
                    log.info("团队分案 共债：{},案件量：{},金额：{}， --分给：{}", jointDebt.getIdentityNumber(), jointDebt.getNumber(), jointDebt.getNumber(), team.getOdvName());
                    iterator.remove();
                    break;
                }


                //判断是否满足团队要求
                boolean isReached = agCaseService.checkIsReachedDistri(mode, team);
                if (isReached || notNum == assignableCases.size()) {
                    reachedPersonnels.add(team);
                    personnelInfos.remove(team);
                    log.info("移除：" + team.getOdvName());
                    if (order == 0) {
                        if (index > personnelInfos.size() - 1) {
                            index--;
                            order = 1;//改变顺序
                        }
                    } else {
                        if (index == 0) {
                            order = 0;//改变顺序
                        }
                    }
                } else {
                    if (personnelInfos.size() > 1) {
                        if (index == 0) {
                            if (order == 1) {
                                order = 0;
                                lastIndex = -1;
                                index = -1;
                            }
                        }
                        if (index == personnelInfos.size() - 1) {
                            if (order == 0) {
                                order = 1;
                                lastIndex = personnelInfos.size();
                                index = personnelInfos.size();
                            }
                        }
                        lastIndex = index;
                        if (order == 0) {
                            index++;
                        } else {
                            index--;
                        }
                    }
                }
            }
            long t23 = timer.intervalMs("2-3");
            log.info("执行分案-广度优先-共债 2-3 耗时:" + t23 + " ms");

        }

        scheduleVo.setSchedule(50);
        scheduleService.setSchedule(scheduleVo);

        long t2 = timer.intervalMs("2");
        log.info("执行分案-广度优先-共债 耗时:" + t2 + " ms");


        timer.start("3");
        //全部团队 都无法 符合隔月分案 要求  的案件
        List<Long> allTeamLastMonthCaseIds = new ArrayList<>();
        Map<Long, List<Long>> lastMonthCaseMap = new HashMap<>();
        if (intervalMonth) {
            List<List<Long>> lists = new ArrayList<>();
            for (Distribution temp : personnelInfos) {
                //开启隔月分案，查询一下团队中不能隔月分案的案件
                List<Long> lastMonthCaseIds = selectTeamIntervalMonth(temp.getOdvId());
                temp.setLastMonthCaseIds(lastMonthCaseIds);
                lists.add(lastMonthCaseIds);
                lastMonthCaseMap.put(temp.getOdvId(), lastMonthCaseIds);
            }

            if (lists.size() == 1) {
                allTeamLastMonthCaseIds = lists.get(0);
            } else {
                // 使用Java 8 Stream API对多个列表执行交集操作
                allTeamLastMonthCaseIds = lists.stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                        .entrySet()
                        .stream()
                        .filter(entry -> entry.getValue() == lists.size())
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());
            }

        }
        long t3 = timer.intervalMs("3");
        log.info("执行分案-广度优先-隔月 耗时:" + t3 + " ms");

        scheduleVo.setSchedule(60);
        scheduleService.setSchedule(scheduleVo);

        //把案件根据金额排序
        // 使用Java 8 Stream API对objList进行金额倒序排序
        List<CaseManage> sortedList = assignableCases.stream()
                .sorted(Comparator.comparing(CaseManage::getClientMoney).reversed())
                .collect(Collectors.toList());
        assignableCases = sortedList;


        int inistSchedule = 60;
        int initSize = assignableCases.size();

        int order = 0;//0-正序，1-倒序
        int lastIndex = 0; //上一个团队index
        int index = 0;     //当前团队index
        while (assignableCases.size() > 0) {
            if (personnelInfos.size() == 0) {
                break;
            }
            if (index > personnelInfos.size() - 1) {
                index = personnelInfos.size() - 1;
            }

            //更新进度，无关业务
            double s = 1 - (double) assignableCases.size() / initSize;
            int si = inistSchedule + (int) (s * 30);
            if (si > 90) {
                si = 90;
            }
            scheduleVo.setSchedule(si);
            scheduleService.setSchedule(scheduleVo);
            //更新进度，无关业务


            Distribution team = personnelInfos.get(index);

            int notNum = 0;

            //System.out.println("" + team.getTeamName());
            Iterator<CaseManage> iterator = assignableCases.iterator();
            while (iterator.hasNext()) {
                CaseManage manage = iterator.next();
                if (intervalMonth) {
                    if (allTeamLastMonthCaseIds.contains(manage.getCaseId())) {
                        //该案件所有团队都不符合隔月分案要求
                        iterator.remove();
                        continue;
                    }

                    if (team.getLastMonthCaseIds().contains(manage.getCaseId())) {
                        //该案件不满足 当前团队隔月分案要求
                        log.info("团队分案， 案件：{},金额：{}--不满足隔月：{}，跳过", manage.getCaseId(), manage.getClientMoney(), team.getOdvName());

                        List<CaseManage> manages = team.getList();
                        team.setNumber(manages.size());
                        notNum++;
                        if (notNum == assignableCases.size()) {
                            //全部案件都不满足当前团队，移除当前团队
                            System.out.println("所有案件都不满足：" + team.getOdvName());
                            log.info("团队分案， 案件：{},金额：{}--所有都不满足隔月：{}，跳过", manage.getCaseId(), manage.getClientMoney(), team.getOdvName());

                            break;
                        }
                        continue;
                    }
                }
                log.info("团队分案， 案件：{},金额：{}--分给：{}", manage.getCaseId(), manage.getClientMoney(), team.getOdvName());

                List<CaseManage> manages = team.getList();
                manages.add(manage);
                team.setList(manages);
                team.setMoney(team.getMoney().add(manage.getClientMoney()));
                team.setNumber(manages.size());
                iterator.remove();
                break;
            }
            //判断是否满足团队要求
            boolean isReached = agCaseService.checkIsReachedDistri(mode, team);
            //boolean isReached =false;
            if (isReached || notNum == assignableCases.size()) {
                reachedPersonnels.add(team);
                personnelInfos.remove(team);
                log.info("移除：" + team.getOdvName());
                /*if (order==0){
                    index--;
                    lastIndex--;
                }else {
                    index--;
                }*/
                if (order == 0) {
                    if (index > personnelInfos.size() - 1) {
                        index--;
                        order = 1;//改变顺序
                    }
                } else {
                    if (index == 0) {
                        order = 0;//改变顺序
                    }
                }
            } else {
                if (personnelInfos.size() > 1) {
                    if (index == 0) {
                        if (order == 1) {
                            order = 0;
                            lastIndex = -1;
                            index = -1;
                        }
                    }
                    if (index == personnelInfos.size() - 1) {
                        if (order == 0) {
                            order = 1;
                            lastIndex = personnelInfos.size();
                            index = personnelInfos.size();
                        }
                    }
                    lastIndex = index;
                    if (order == 0) {
                        index++;
                    } else {
                        index--;
                    }
                }
            }
        }


        scheduleVo.setSchedule(90);
        scheduleService.setSchedule(scheduleVo);

        personnelInfos.addAll(reachedPersonnels);

        List<Distribution> teamsSort = personnelInfos.stream()
                .sorted(Comparator.comparing(Distribution::getSort))
                .collect(Collectors.toList());

        for (Distribution temp : teamsSort) {
            log.info("催员：{}，工号：{}， 案件量：{}，案件金额：{}", temp.getOdvName(), temp.getJobNumber(), temp.getNumber(), temp.getMoney());
        }

        long t = timer.intervalMs("1");
        log.info("执行分案-广度优先 耗时:" + t + " ms");

        scheduleVo.setSchedule(95);
        scheduleService.setSchedule(scheduleVo);

        return teamsSort;

    }


    /**
     * 查询催员交叉分案的案件
     *
     * @param odvId
     */
    private List<Long> selectTeamIntervalMonth(Long odvId) {
        QueryTimeInterval queryTimeInterval = new QueryTimeInterval();   //交叉分案查询案件是否符合条件实体类
        //queryTimeInterval.setIds(list);
        queryTimeInterval.setEmployeesId(odvId.intValue());
        queryTimeInterval.setMonthFirst(TimeUtils.getMonthBegin());
        //queryTimeInterval.setMonthEnd(TimeUtils.getMonthEnd());
        queryTimeInterval.setMonthEnd(new Date());
        List<Long> caseIds = ruleDivisionService.selectDistributionHistory(queryTimeInterval);//查询该员工的交叉案件信息
        return caseIds;
    }


    /**
     * 只存在交叉分案
     *
     * @param specifyRules
     * @param caseManageList
     * @return
     */
    public List<Distribution> crossDivision(SpecifyRules specifyRules, List<CaseManage> caseManageList) {
        Integer divisionalMode = specifyRules.getDivisionalMode();  //分配模式

//        List<Long> list = new ArrayList<>();
//        for (CaseManage caseManage : caseManageList) {
//            list.add(caseManage.getCaseId());
//        }
        List<Long> list = teamCaseService.selectCaseManageCaseIdCount(specifyRules.getTeamCaseUtils());  //案件id集合

        for (Distribution distribution : specifyRules.getDistributions()) {    //遍历人员信息
            distribution.setList(new ArrayList<>());

            QueryTimeInterval queryTimeInterval = new QueryTimeInterval();   //交叉分案查询案件是否符合条件实体类
            Long odvId = distribution.getOdvId();//员工id
            queryTimeInterval.setIds(list);
            queryTimeInterval.setEmployeesId(odvId.intValue());
            queryTimeInterval.setMonthFirst(TimeUtils.getMonthBegin());
            queryTimeInterval.setMonthEnd(TimeUtils.getMonthEnd());

            List<Long> list1 = ruleDivisionService.selectDistributionHistory(queryTimeInterval);//查询该员工的交叉案件信息

            int size = distribution.getQuantity().intValue();  //已经分配的案件数量
            BigDecimal money = distribution.getMoney();  //已经分配的案件总金额

            List<CaseManage> list2 = distribution.getList();  //分配给员工的案件信息集合

            List<CaseManage> arrayList = new ArrayList<>();  //已经分配的案件集合

            for (CaseManage caseManage : caseManageList) {
                if (list1.contains(caseManage.getCaseId())) {
                    continue;
                }
                if (divisionalMode == 0 || divisionalMode == 1) {   //按照案件数量分配
                    if (BigDecimal.valueOf(size).compareTo(distribution.getNumericalValue()) == 0) {
                        break;
                    }
                    size = size + 1;
                    money = money.add(caseManage.getClientMoney());
                    distribution.getList().add(caseManage);
                    arrayList.add(caseManage);
                    list2.add(caseManage);
                }

                if (divisionalMode == 2 || divisionalMode == 3) {   //按照案件金额分配
                    if (money.compareTo(distribution.getNumericalValue()) == 0 || money.compareTo(distribution.getNumericalValue()) == 1) {
                        break;
                    }
                    size = size + 1;
                    money = money.add(caseManage.getClientMoney());
                    distribution.getList().add(caseManage);
                    arrayList.add(caseManage);
                    list2.add(caseManage);
                }

            }
            distribution.setMoney(money);  //分案金额
            distribution.setQuantity(BigDecimal.valueOf(size)); //分案数量
            distribution.setList(list2);
            caseManageList.removeAll(arrayList);
        }

        if (!ObjectUtils.isEmpty(caseManageList)) {    //交叉分案结束，有未分配案件则全部分给第一个人
            for (CaseManage caseManage : caseManageList) {
                Distribution distribution = specifyRules.getDistributions().get(0);
                if (divisionalMode == 0 || divisionalMode == 1) {
                    if (distribution.getQuantity().compareTo(distribution.getNumericalValue()) == 0) {
                        break;
                    }
                } else if (divisionalMode == 2 || divisionalMode == 3) {
                    if (distribution.getMoney().compareTo(distribution.getNumericalValue()) == 0 || distribution.getMoney().compareTo(distribution.getNumericalValue()) == 1) {
                        break;
                    }
                }
                distribution.getList().add(caseManage);
                distribution.setMoney(distribution.getMoney().add(caseManage.getClientMoney()));
                distribution.setQuantity(distribution.getQuantity().add(BigDecimal.ONE));
            }
        }
        return specifyRules.getDistributions();
    }

    /**
     * 我的团队/团队承诺户全查以及根据字段条件查询
     *
     * @param
     * @return
     */
    public List<CreateUrgeRecordIdUtils> selectUrgeRecordId(UrgeRecordUtils urgeRecordUtils, LoginUser loginUser) {
        urgeRecordUtils.setDecryptKey(FieldEncryptUtil.fieldKey);
        if (!ObjectUtils.isEmpty(urgeRecordUtils.getStringDeptId())) {
            int dept = urgeRecordUtils.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = urgeRecordUtils.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    urgeRecordUtils.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(urgeRecordUtils.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    urgeRecordUtils.setUserId(Integer.parseInt(urgeRecordUtils.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(urgeRecordUtils.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(urgeRecordUtils.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(urgeRecordUtils.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(urgeRecordUtils.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(urgeRecordUtils.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        urgeRecordUtils.setDeptIds(list);

        urgeRecordUtils.setCreateId(TokenInformation.getCreateid());
        List<CreateUrgeRecordIdUtils> inheritanceCollections = teamCaseService.selectUrgeRecordId(urgeRecordUtils);  //根据员工id查询同部门以及以下部门的员工承诺户信息
        if (!ObjectUtils.isEmpty(inheritanceCollections)) {
            for (CreateUrgeRecordIdUtils inheritanceCollection : inheritanceCollections) {
                //解密
                DecryptUtils.dataDecrypt(inheritanceCollection);
                CaseManage caseManage = recordService.selectCaseManageCaseId(inheritanceCollection.getCaseId());
                if (ObjectUtils.isEmpty(caseManage)) {
                    inheritanceCollection.setButton(0);
                    continue;
                }
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (new Long((long) TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                        inheritanceCollection.setButton(1);
                    } else {
                        inheritanceCollection.setButton(0);
                    }
                } else {
                    inheritanceCollection.setButton(0);
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (CreateUrgeRecordIdUtils inheritanceCollection : inheritanceCollections) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientName())) {
                        inheritanceCollection.setClientName(DataMaskingUtils.nameMasking(inheritanceCollection.getClientName()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientPhone())) {
                        inheritanceCollection.setClientPhone(DataMaskingUtils.phoneMasking(inheritanceCollection.getClientPhone()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientIdcard())) {
                        inheritanceCollection.setClientIdcard(DataMaskingUtils.idMasking(inheritanceCollection.getClientIdcard()));
                    }
                }
            }
        }

        return inheritanceCollections;
    }

    /**
     * 查询团队案件催记
     *
     * @param param
     * @param loginUser
     * @return
     */
    public List<ExportDataUtils> selectTeamUrgeRecord(TeamExportUrgeParam param, LoginUser loginUser) {
        //TODO 处理团队案件 催记

        return new ArrayList<>();
    }

    /**
     * 我的团队/团队资料调取申请全查以及根据字段条件查询
     *
     * @param
     * @return
     */
    public List<CreateRetrievalRecordUtils> selectRetrievalRecordId(TeamRetrievalRecord teamRetrievalRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamRetrievalRecord.getStringDeptId())) {
            int dept = teamRetrievalRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamRetrievalRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamRetrievalRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamRetrievalRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamRetrievalRecord.setUserId(Integer.parseInt(teamRetrievalRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamRetrievalRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamRetrievalRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamRetrievalRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamRetrievalRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamRetrievalRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        teamRetrievalRecord.setDeptIds(list);
        teamRetrievalRecord.setCreateId(TokenInformation.getCreateid());
        List<CreateRetrievalRecordUtils> createRetrievalRecordUtils = teamCaseService.selectRetrievalRecordId(teamRetrievalRecord);  //根据员工id查询同部门以及以下部门的员工回款表以及案加详情信息
        if (!ObjectUtils.isEmpty(createRetrievalRecordUtils)) {
            for (CreateRetrievalRecordUtils createRetrievalRecordUtils1 : createRetrievalRecordUtils) {
                DecryptUtils.dataDecrypt(createRetrievalRecordUtils1);  //解密

                CaseManage caseManage = recordService.selectCaseManageCaseId(createRetrievalRecordUtils1.getCaseId());
                if (caseManage == null) {
                    createRetrievalRecordUtils1.setButton(0);
                    continue;
                }
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (new Long((long) TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                        createRetrievalRecordUtils1.setButton(1);
                    } else {
                        createRetrievalRecordUtils1.setButton(0);
                    }
                } else {
                    createRetrievalRecordUtils1.setButton(0);
                }
//                判断是否是已通过状态，已通过状态拼接访问连接
                if (createRetrievalRecordUtils1.getExamineState().equals("已通过") && !ObjectUtils.isEmpty(createRetrievalRecordUtils1.getRandom())) {
                    String archiveFileAddress = createRetrievalRecordUtils1.getArchiveFileAddress();
                    if (!ObjectUtils.isEmpty(archiveFileAddress)){
                        createRetrievalRecordUtils1.setPath(archiveFileAddress);
                    }
                    if (!ObjectUtils.isEmpty(createRetrievalRecordUtils1.getWatermarkedFilePath())){
                        createRetrievalRecordUtils1.setPath(createRetrievalRecordUtils1.getWatermarkedFilePath());
                    }
                    if (!ObjectUtils.isEmpty(archiveFileAddress) && !ObjectUtils.isEmpty(createRetrievalRecordUtils1.getWatermarkedFilePath())){
                        createRetrievalRecordUtils1.setPath(createRetrievalRecordUtils1.getWatermarkedFilePath()+","+archiveFileAddress);
                    }
//                    判断申请是否到期
                    int become = agCaseService.fileExpirationTime(createRetrievalRecordUtils1.getExamineTime());
                    createRetrievalRecordUtils1.setFileExpiration(become);
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (CreateRetrievalRecordUtils createRetrievalRecordUtils1 : createRetrievalRecordUtils) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(createRetrievalRecordUtils1.getClientName())) {
                        createRetrievalRecordUtils1.setClientName(DataMaskingUtils.nameMasking(createRetrievalRecordUtils1.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(createRetrievalRecordUtils1.getClientIdcard())) {
                        createRetrievalRecordUtils1.setClientIdcard(DataMaskingUtils.idMasking(createRetrievalRecordUtils1.getClientIdcard()));
                    }
                }
            }
        }
        return createRetrievalRecordUtils;
    }

    /**
     * 我的团队/团队资料调取申请全查以及根据字段条件查询(各状态数量)
     *
     * @param
     * @return
     */
    public List<Map<String, Object>> selectRetrievalRecordIdNumber(TeamRetrievalRecord teamRetrievalRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamRetrievalRecord.getStringDeptId())) {
            int dept = teamRetrievalRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamRetrievalRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamRetrievalRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamRetrievalRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamRetrievalRecord.setUserId(Integer.parseInt(teamRetrievalRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamRetrievalRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamRetrievalRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamRetrievalRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamRetrievalRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamRetrievalRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
//        PageUtils.startPage();
        teamRetrievalRecord.setDeptIds(list);
        teamRetrievalRecord.setCreateId(TokenInformation.getCreateid());
        List<Map<String, Object>> maps = teamCaseService.selectRetrievalRecordIdNumber(teamRetrievalRecord);//根据员工id查询同部门以及以下部门的员工回款表以及案加详情信息
        return maps;
    }


    /**
     * 我的团队/团队回款申请全查以及根据字段条件查询
     *
     * @param
     * @return
     */
    public List<CreateRepaymentRecordUtils> selectRepaymentRecordId(TeamRepaymentRecord teamRepaymentRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamRepaymentRecord.getStringDeptId())) {
            int dept = teamRepaymentRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamRepaymentRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamRepaymentRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamRepaymentRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamRepaymentRecord.setUserId(Integer.parseInt(teamRepaymentRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamRepaymentRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamRepaymentRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamRepaymentRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamRepaymentRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamRepaymentRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        teamRepaymentRecord.setDeptIds(list);
        teamRepaymentRecord.setCreateId(TokenInformation.getCreateid());
        List<CreateRepaymentRecordUtils> createRepaymentRecordUtils = teamCaseService.selectRepaymentRecordId(teamRepaymentRecord);  //根据员工id查询同部门以及以下部门的员工回款表以及案加详情信息
        if (!ObjectUtils.isEmpty(createRepaymentRecordUtils)) {
            for (CreateRepaymentRecordUtils createRepaymentRecordUtil : createRepaymentRecordUtils) {
                DecryptUtils.dataDecrypt(createRepaymentRecordUtil);  //解密

                CaseManage caseManage = recordService.selectCaseManageCaseId(createRepaymentRecordUtil.getCaseId());
                if (caseManage == null) {
                    createRepaymentRecordUtil.setButton(0);
                    continue;
                }
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (new Long((long) TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                        createRepaymentRecordUtil.setButton(1);
                    } else {
                        createRepaymentRecordUtil.setButton(0);
                    }
                } else {
                    createRepaymentRecordUtil.setButton(0);
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (CreateRepaymentRecordUtils createRepaymentRecordUtil : createRepaymentRecordUtils) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(createRepaymentRecordUtil.getClientName())) {
                        createRepaymentRecordUtil.setClientName(DataMaskingUtils.nameMasking(createRepaymentRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(createRepaymentRecordUtil.getClientIdcard())) {
                        createRepaymentRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(createRepaymentRecordUtil.getClientIdcard()));
                    }
                }
            }
        }
        return createRepaymentRecordUtils;
    }

    /**
     * 我的团队/团队回款申请全查以及根据字段条件查询(各状态数量)
     *
     * @param
     * @return
     */
    public List<Map<String, Object>> selectRepaymentRecordIdNumber(TeamRepaymentRecord teamRepaymentRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamRepaymentRecord.getStringDeptId())) {
            int dept = teamRepaymentRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamRepaymentRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamRepaymentRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamRepaymentRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamRepaymentRecord.setUserId(Integer.parseInt(teamRepaymentRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamRepaymentRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamRepaymentRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamRepaymentRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamRepaymentRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamRepaymentRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
//        PageUtils.startPage();
        teamRepaymentRecord.setDeptIds(list);
        teamRepaymentRecord.setCreateId(TokenInformation.getCreateid());
        List<Map<String, Object>> maps = teamCaseService.selectRepaymentRecordIdNumber(teamRepaymentRecord);//根据员工id查询同部门以及以下部门的员工回款表以及案加详情信息
        return maps;
    }

    /**
     * 我的团队/团队减免申请全查以及根据字段条件查询
     *
     * @param
     * @return
     */
    public List<CreateReductionRecordUtils> selectReductionRecordId(TeamReductionRecord teamReductionRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamReductionRecord.getStringDeptId())) {
            int dept = teamReductionRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamReductionRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamReductionRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamReductionRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamReductionRecord.setUserId(Integer.parseInt(teamReductionRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamReductionRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamReductionRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamReductionRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamReductionRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamReductionRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        teamReductionRecord.setDeptIds(list);
        teamReductionRecord.setCreateId(TokenInformation.getCreateid());
        List<CreateReductionRecordUtils> createReductionRecordUtils = teamCaseService.selectReductionRecordId(teamReductionRecord);//根据员工id查询同部门以及以下部门的员工减免表以及案加详情信息
        if (!ObjectUtils.isEmpty(createReductionRecordUtils)) {
            for (CreateReductionRecordUtils createReductionRecordUtil : createReductionRecordUtils) {
                DecryptUtils.dataDecrypt(createReductionRecordUtil);  //解密

                CaseManage caseManage = recordService.selectCaseManageCaseId(createReductionRecordUtil.getCaseId());
                if (caseManage == null) {
                    createReductionRecordUtil.setButton(0);
                    continue;
                }
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (new Long((long) TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                        createReductionRecordUtil.setButton(1);
                    } else {
                        createReductionRecordUtil.setButton(0);
                    }
                } else {
                    createReductionRecordUtil.setButton(0);
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (CreateReductionRecordUtils createReductionRecordUtil : createReductionRecordUtils) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(createReductionRecordUtil.getClientName())) {
                        createReductionRecordUtil.setClientName(DataMaskingUtils.nameMasking(createReductionRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(createReductionRecordUtil.getClientIdcard())) {
                        createReductionRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(createReductionRecordUtil.getClientIdcard()));
                    }
                }
            }
        }
        return createReductionRecordUtils;
    }

    /**
     * 我的团队/团队减免申请全查以及根据字段条件查询(各状态数量)
     *
     * @param
     * @return
     */
    public List<Map<String, Object>> selectReductionRecordIdNumber(TeamReductionRecord teamReductionRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamReductionRecord.getStringDeptId())) {
            int dept = teamReductionRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamReductionRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamReductionRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamReductionRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamReductionRecord.setUserId(Integer.parseInt(teamReductionRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamReductionRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamReductionRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamReductionRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamReductionRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamReductionRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        teamReductionRecord.setDeptIds(list);
        teamReductionRecord.setCreateId(TokenInformation.getCreateid());
        List<Map<String, Object>> maps = teamCaseService.selectReductionRecordIdNumber(teamReductionRecord);//根据员工id查询同部门以及以下部门的员工减免表以及案加详情信息
        return maps;
    }

    /**
     * 我的团队/团队分期还款申请全查以及根据字段条件查询
     *
     * @param
     * @return
     */
    public List<CreateStagingRecordUtils> selectStagingRecordId(TeamStagingRecord teamStagingRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamStagingRecord.getStringDeptId())) {
            int dept = teamStagingRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamStagingRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamStagingRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamStagingRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamStagingRecord.setUserId(Integer.parseInt(teamStagingRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamStagingRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamStagingRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamStagingRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamStagingRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamStagingRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        teamStagingRecord.setDeptIds(list);
        teamStagingRecord.setCreateId(TokenInformation.getCreateid());
        List<CreateStagingRecordUtils> createStagingRecordUtils = teamCaseService.selectStagingRecordId(teamStagingRecord);//根据员工id查询同部门以及以下部门的员工分期还款表以及案加详情信息
        if (!ObjectUtils.isEmpty(createStagingRecordUtils)) {
            for (CreateStagingRecordUtils createStagingRecordUtil : createStagingRecordUtils) {
                DecryptUtils.dataDecrypt(createStagingRecordUtil);  //解密

                CaseManage caseManage = recordService.selectCaseManageCaseId(createStagingRecordUtil.getCaseId());
                if (caseManage == null) {
                    createStagingRecordUtil.setButton(0);
                    continue;
                }
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (new Long((long) TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                        createStagingRecordUtil.setButton(1);
                    } else {
                        createStagingRecordUtil.setButton(0);
                    }
                } else {
                    createStagingRecordUtil.setButton(0);
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (CreateStagingRecordUtils createStagingRecordUtil : createStagingRecordUtils) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(createStagingRecordUtil.getClientName())) {
                        createStagingRecordUtil.setClientName(DataMaskingUtils.nameMasking(createStagingRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(createStagingRecordUtil.getClientIdcard())) {
                        createStagingRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(createStagingRecordUtil.getClientIdcard()));
                    }
                }
            }
        }
        return createStagingRecordUtils;
    }

    /**
     * 我的团队/团队分期还款申请全查以及根据字段条件查询(各状态数量)
     *
     * @param
     * @return
     */
    public List<Map<String, Object>> selectStagingRecordIdNumber(TeamStagingRecord teamStagingRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamStagingRecord.getStringDeptId())) {
            int dept = teamStagingRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamStagingRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamStagingRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamStagingRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamStagingRecord.setUserId(Integer.parseInt(teamStagingRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamStagingRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamStagingRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamStagingRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamStagingRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamStagingRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        teamStagingRecord.setDeptIds(list);
        teamStagingRecord.setCreateId(TokenInformation.getCreateid());
        List<Map<String, Object>> maps = teamCaseService.selectStagingRecordIdNumber(teamStagingRecord);//根据员工id查询同部门以及以下部门的员工分期还款表以及案加详情信息
        return maps;
    }

    /**
     * 根据员工id查询同部门以及以下部门的员工停案/留案/停催申请表以及案加详情信息
     *
     * @param
     * @return
     */
    public List<CreateApplyRecordUtils> selectApplyRecordId(TeamApplyRecord teamApplyRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamApplyRecord.getStringDeptId())) {
            int dept = teamApplyRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamApplyRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamApplyRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamApplyRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamApplyRecord.setUserId(Integer.parseInt(teamApplyRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamApplyRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamApplyRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamApplyRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamApplyRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamApplyRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        teamApplyRecord.setDeptIds(list);
        teamApplyRecord.setCreateId(TokenInformation.getCreateid());
        List<CreateApplyRecordUtils> createApplyRecordUtils = teamCaseService.selectApplyRecordId(teamApplyRecord);//根据员工id查询同部门以及以下部门的员工停案/留案/停催申请表以及案加详情信息
        if (!ObjectUtils.isEmpty(createApplyRecordUtils)) {
            for (CreateApplyRecordUtils createApplyRecordUtil : createApplyRecordUtils) {
                DecryptUtils.dataDecrypt(createApplyRecordUtil);  //解密

                CaseManage caseManage = recordService.selectCaseManageCaseId(createApplyRecordUtil.getCaseId());
                if (caseManage == null) {
                    createApplyRecordUtil.setButton(0);
                    continue;
                }
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (new Long((long) TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                        createApplyRecordUtil.setButton(1);
                    } else {
                        createApplyRecordUtil.setButton(0);
                    }
                } else {
                    createApplyRecordUtil.setButton(0);
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (CreateApplyRecordUtils createApplyRecordUtil : createApplyRecordUtils) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(createApplyRecordUtil.getClientName())) {
                        createApplyRecordUtil.setClientName(DataMaskingUtils.nameMasking(createApplyRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(createApplyRecordUtil.getClientIdcard())) {
                        createApplyRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(createApplyRecordUtil.getClientIdcard()));
                    }
                }
            }
        }
        return createApplyRecordUtils;
    }

    /**
     * 根据员工id查询同部门以及以下部门的员工停案/留案/停催申请表以及案加详情信息(各状态数量)
     *
     * @param
     * @return
     */
    public List<Map<String, Object>> selectApplyRecordIdNumber(TeamApplyRecord teamApplyRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamApplyRecord.getStringDeptId())) {
            int dept = teamApplyRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamApplyRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamApplyRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamApplyRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamApplyRecord.setUserId(Integer.parseInt(teamApplyRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamApplyRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamApplyRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamApplyRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamApplyRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamApplyRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        teamApplyRecord.setDeptIds(list);
        teamApplyRecord.setDeptId(null);
        teamApplyRecord.setCreateId(TokenInformation.getCreateid());
        List<Map<String, Object>> maps = teamCaseService.selectApplyRecordIdNumber(teamApplyRecord);//根据员工id查询同部门以及以下部门的员工停案/留案/停催申请表以及案加详情信息数量
        return maps;
    }

    /**
     * 根据员工id查询同部门以及以下部门的员工协催申请表以及案加详情信息
     *
     * @param
     * @return
     */
    public List<CreateAssistRecordUtils> selectAssistRecordId(TeamAssistRecord teamAssistRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamAssistRecord.getStringDeptId())) {
            int dept = teamAssistRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamAssistRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamAssistRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamAssistRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamAssistRecord.setUserId(Integer.parseInt(teamAssistRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamAssistRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamAssistRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamAssistRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamAssistRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamAssistRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        teamAssistRecord.setDeptIds(list);
        teamAssistRecord.setCreateId(TokenInformation.getCreateid());
        List<CreateAssistRecordUtils> createAssistRecordUtils = teamCaseService.selectAssistRecordId(teamAssistRecord);//根据员工id查询同部门以及以下部门的员工协催申请表以及案加详情信息
        if (!ObjectUtils.isEmpty(createAssistRecordUtils)) {
            for (CreateAssistRecordUtils createAssistRecordUtil : createAssistRecordUtils) {
                DecryptUtils.dataDecrypt(createAssistRecordUtil);

                CaseManage caseManage = recordService.selectCaseManageCaseId(createAssistRecordUtil.getCaseId());
                if (caseManage == null) {
                    createAssistRecordUtil.setButton(0);
                    continue;
                }
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (new Long((long) TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                        createAssistRecordUtil.setButton(1);
                    } else {
                        createAssistRecordUtil.setButton(0);
                    }
                } else {
                    createAssistRecordUtil.setButton(0);
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (CreateAssistRecordUtils createAssistRecordUtil : createAssistRecordUtils) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(createAssistRecordUtil.getClientName())) {
                        createAssistRecordUtil.setClientName(DataMaskingUtils.nameMasking(createAssistRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(createAssistRecordUtil.getClientIdcard())) {
                        createAssistRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(createAssistRecordUtil.getClientIdcard()));
                    }
                }
            }
        }
        return createAssistRecordUtils;
    }

    /**
     * 根据员工id查询同部门以及以下部门的员工外访申请表以及案加详情信息
     *
     * @param
     * @return
     */
    public List<CreateOutsideRecordUtils> selectOutsideRecordId(TeamOutsideRecord teamOutsideRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamOutsideRecord.getStringDeptId())) {
            int dept = teamOutsideRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamOutsideRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamOutsideRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamOutsideRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamOutsideRecord.setUserId(Integer.parseInt(teamOutsideRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamOutsideRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamOutsideRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamOutsideRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamOutsideRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamOutsideRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        teamOutsideRecord.setDeptIds(list);
        teamOutsideRecord.setCreateId(TokenInformation.getCreateid());
        List<CreateOutsideRecordUtils> createOutsideRecordUtils = teamCaseService.selectOutsideRecordId(teamOutsideRecord);//根据员工id查询同部门以及以下部门的员工外访申请表以及案加详情信息
        if (!ObjectUtils.isEmpty(createOutsideRecordUtils)) {
            for (CreateOutsideRecordUtils createOutsideRecordUtil : createOutsideRecordUtils) {
                DecryptUtils.dataDecrypt(createOutsideRecordUtil);  //解密

                CaseManage caseManage = recordService.selectCaseManageCaseId(createOutsideRecordUtil.getCaseId());
                if (caseManage == null) {
                    createOutsideRecordUtil.setButton(0);
                    continue;
                }
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (new Long((long) TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                        createOutsideRecordUtil.setButton(1);
                    } else {
                        createOutsideRecordUtil.setButton(0);
                    }
                } else {
                    createOutsideRecordUtil.setButton(0);
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (CreateOutsideRecordUtils createOutsideRecordUtil : createOutsideRecordUtils) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(createOutsideRecordUtil.getClientName())) {
                        createOutsideRecordUtil.setClientName(DataMaskingUtils.nameMasking(createOutsideRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(createOutsideRecordUtil.getClientIdcard())) {
                        createOutsideRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(createOutsideRecordUtil.getClientIdcard()));
                    }
                }
            }
        }
        return createOutsideRecordUtils;
    }

    /**
     * 根据员工id查询同部门以及以下部门的员工外访申请表以及案加详情信息(各状态数量)
     *
     * @param
     * @return
     */
    public List<Map<String, Object>> selectOutsideRecordIdNumber(TeamOutsideRecord teamOutsideRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(teamOutsideRecord.getStringDeptId())) {
            int dept = teamOutsideRecord.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamOutsideRecord.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamOutsideRecord.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamOutsideRecord.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamOutsideRecord.setUserId(Integer.parseInt(teamOutsideRecord.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamOutsideRecord.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamOutsideRecord.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamOutsideRecord.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamOutsideRecord.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamOutsideRecord.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        teamOutsideRecord.setDeptIds(list);
        teamOutsideRecord.setCreateId(TokenInformation.getCreateid());
        List<Map<String, Object>> maps = teamCaseService.selectOutsideRecordIdNumber(teamOutsideRecord);//根据员工id查询同部门以及以下部门的员工外访申请表以及案加详情信息
        return maps;
    }

    /**
     * 根据员工id查询同部门以及以下部门的员工工单申请表以及案加详情信息
     *
     * @param
     * @return
     */
    public List<CreateWorkOrderUtils> selectWorkOrder(TeamWorkOrder teamWorkOrder, LoginUser loginUser) {
        teamWorkOrder.setDecryptKey(FieldEncryptUtil.fieldKey);
        if (!ObjectUtils.isEmpty(teamWorkOrder.getStringDeptId())) {
            int dept = teamWorkOrder.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamWorkOrder.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamWorkOrder.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamWorkOrder.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamWorkOrder.setUserId(Integer.parseInt(teamWorkOrder.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamWorkOrder.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamWorkOrder.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamWorkOrder.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamWorkOrder.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamWorkOrder.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        PageUtils.startPage();
        teamWorkOrder.setDeptIds(list);
        if (TokenInformation.getCreateid() != 0 && TokenInformation.getCreateid() != null) {
            teamWorkOrder.setCreateId(TokenInformation.getCreateid());
        }
        List<CreateWorkOrderUtils> createWorkOrderUtils = teamCaseService.selectWorkOrder(teamWorkOrder);//根据员工id查询同部门以及以下部门的员工工单申请表以及案加详情信息
        if (ObjectUtils.isEmpty(createWorkOrderUtils)) {
            return new ArrayList<>();
        }
        for (CreateWorkOrderUtils createWorkOrderUtil : createWorkOrderUtils) {
            DecryptUtils.dataDecrypt(createWorkOrderUtil); //解密

            CaseManage caseManage = recordService.selectCaseManageCaseId(createWorkOrderUtil.getCaseId());
            if (ObjectUtils.isEmpty(caseManage)) {
                createWorkOrderUtil.setButton(0);
            } else {
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (new Long((long) TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                        createWorkOrderUtil.setButton(1);
                    } else {
                        createWorkOrderUtil.setButton(0);
                    }
                } else {
                    createWorkOrderUtil.setButton(0);
                }
            }
        }
        StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
        State state = stateDesensitization.getState();
        if (state.getInformationStatus() == 1) {
            Desensitization desensitization = stateDesensitization.getDesensitization();
            for (CreateWorkOrderUtils createWorkOrderUtil : createWorkOrderUtils) {
                if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(createWorkOrderUtil.getClientName())) {
                    createWorkOrderUtil.setClientName(DataMaskingUtils.nameMasking(createWorkOrderUtil.getClientName()));
                }
                if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(createWorkOrderUtil.getClientPhone())) {
                    createWorkOrderUtil.setClientPhone(DataMaskingUtils.phoneMasking(createWorkOrderUtil.getClientPhone()));
                }
                if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(createWorkOrderUtil.getClientIdcard())) {
                    createWorkOrderUtil.setClientIdcard(DataMaskingUtils.idMasking(createWorkOrderUtil.getClientIdcard()));
                }
                if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(createWorkOrderUtil.getCallNumber())) {
                    createWorkOrderUtil.setCallNumber(DataMaskingUtils.phoneMasking(createWorkOrderUtil.getCallNumber()));
                }
            }
        }
        return createWorkOrderUtils;
    }

    /**
     * 查询团队案件催记
     */
    public List<ExportDataUtils> selectTeamUrgeRecord(TeamExportUrgeParam param) {
        //TODO 查询团队案件催记
        handleDeptParam(param.getQueryParams());
        param.getQueryParams().setDecryptKey(FieldEncryptUtil.fieldKey);

        return this.teamCaseService.selectTeamUrgeRecord(param);
    }


    /**
     * 根据员工id查询同部门以及以下部门的员工工单申请表以及案加详情信息(各状态数量)
     *
     * @param
     * @return
     */
    public List<Map<String, Object>> selectWorkOrderNumber(TeamWorkOrder teamWorkOrder, LoginUser loginUser) {
        teamWorkOrder.setDecryptKey(FieldEncryptUtil.fieldKey);
        if (!ObjectUtils.isEmpty(teamWorkOrder.getStringDeptId())) {
            int dept = teamWorkOrder.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamWorkOrder.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamWorkOrder.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamWorkOrder.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamWorkOrder.setUserId(Integer.parseInt(teamWorkOrder.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamWorkOrder.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamWorkOrder.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamWorkOrder.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamWorkOrder.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamWorkOrder.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }
        teamWorkOrder.setDeptIds(list);
        teamWorkOrder.setCreateId(TokenInformation.getCreateid());
        List<Map<String, Object>> maps = teamCaseService.selectWorkOrderNumber(teamWorkOrder);//根据员工id查询同部门以及以下部门的员工工单申请表以及案加详情信息
        return maps;
    }

    /**
     * 返回部门列表树类型信息
     *
     * @param
     * @return
     */
    public List<TreeType> DeptTreeType() {
        String accountNumber = TokenInformation.getAccountNumber();
        String substring = accountNumber.substring(accountNumber.length() - 3);
        String substring1 = substring.substring(0, 1);

        String departmentIds = null;
        Integer departmentId = 0;

        Employees employees = teamCaseService.selectEmployeesId(TokenInformation.getUserid());  //根据员工id查询员工部门id
        if (!ObjectUtils.isEmpty(employees)) {
            departmentId = employees.getDepartmentId();    //登录用户的部门id
            departmentIds = String.valueOf(departmentId);  //强转为string类型
        }

        List<Dept> deptList = teamCaseService.selectDept(TokenInformation.getCreateid());//根据团队id查询该团队所有部门信息

        if ("@".equals(substring1)) {   //判断登录账号是员工还是团队主账号
            List<Dept> list = new ArrayList<>();
            for (Dept dept : deptList) {
                if (dept.getId().equals(departmentId)) {
                    list.add(dept);
                } else {
                    String ancestors = dept.getAncestors();  //祖级列表
                    String[] split = ancestors.split(",");
                    for (String splits : split) {
                        if (!splits.equals("0") && splits.equals(departmentIds)) {
                            Dept dept1 = teamCaseService.selectDeptById(dept.getId());
                            list.add(dept1);
                        }
                    }
                }
            }
            deptList = list;
        }

        List<TreeType> treeTypes = new ArrayList<>();

        for (Dept dept : deptList) {
            if (dept.getParentId().equals(0) || dept.getId().equals(departmentId)) {    //如果为顶级部门
                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept.getId());
                treeType.setName(dept.getDeptName());
                Boolean aBoolean = ChildNode(dept.getId());
                if (aBoolean) {
                    List<TreeType> listr = listr(deptList, dept);
                    treeType.setChildren(listr);
                } else {
                    List<Employees> employeess = settingsService.selectEmployeesParentId(dept.getId(), TokenInformation.getCreateid());
                    List<TreeType> arrlist = new ArrayList<>();
                    for (Employees employees1 : employeess) {
                        TreeType treeType1 = new TreeType();
                        treeType1.setId(employees1.getId().toString());
                        treeType1.setName(employees1.getEmployeeName());
                        treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
                        arrlist.add(treeType1);
                    }
                    treeType.setChildren(arrlist);
                }
                treeTypes.add(treeType);
            }
        }

        return treeTypes;
    }


    /**
     * 部门处理
     *
     * @param treeTypes
     * @param dept
     * @param deptList
     */
    public void DeptHandle(List<TreeType> treeTypes, Dept dept, List<Dept> deptList) {
        TreeType treeType = new TreeType();
        treeType.setId("Dept:" + dept.getId());
        treeType.setName(dept.getDeptName());
        Boolean aBoolean = ChildNode(dept.getId());
        if (aBoolean) {
            List<TreeType> listr = listr(deptList, dept);
            treeType.setChildren(listr);
        } else {
            List<Employees> employeess = settingsService.selectEmployeesParentId(dept.getId(), TokenInformation.getCreateid());
            List<TreeType> arrlist = new ArrayList<>();
            for (Employees employees1 : employeess) {
                TreeType treeType1 = new TreeType();
                treeType1.setId(employees1.getId().toString());
                treeType1.setName(employees1.getEmployeeName());
                treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
                arrlist.add(treeType1);
            }
            treeType.setChildren(arrlist);
        }
        treeTypes.add(treeType);
    }


    /**
     * 判断是否有子列表
     *
     * @param parentId
     * @return
     */
    public Boolean ChildNode(int parentId) {
        List<Dept> deptList = settingsService.selectDeptParentId(parentId);
        return !ObjectUtils.isEmpty(deptList);
    }

    /**
     * 迭代查询子列表
     *
     * @param depts
     * @param dept
     * @return
     */
    public List<TreeType> listr(List<Dept> depts, Dept dept) {
        List<TreeType> list = new ArrayList<>();
        for (Dept dept1 : depts) {

            if (dept1.getParentId().equals(dept.getId())) {

                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept1.getId());
                treeType.setName(dept1.getDeptName());
                List<TreeType> childrens = listr(depts, dept1);

                treeType.setChildren(childrens);
                list.add(treeType);

            }
        }
        List<Employees> employees = settingsService.selectEmployeesParentId(dept.getId(), TokenInformation.getCreateid());  //根据部门id以及团队id查询该部门人员信息
        List<TreeType> arrlist = new ArrayList<>();
        for (Employees employees1 : employees) {
            TreeType treeType1 = new TreeType();
            treeType1.setId(employees1.getId().toString());
            treeType1.setName(employees1.getEmployeeName());
            treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
            arrlist.add(treeType1);
        }

        list.addAll(arrlist);


        return list;
    }

    /**
     * 返回部门列表树类型信息-（屏蔽当前登录人信息）
     *
     * @param
     * @return
     */
    public List<TreeType> DeptTreeTypeById() {
        List<Dept> deptList = teamCaseService.selectDept(TokenInformation.getCreateid());//根据团队id查询该团队所有部门信息
        List<TreeType> treeTypes = new ArrayList<>();
        for (Dept dept : deptList) {
            if (dept.getParentId().equals(0)) {    //如果为顶级部门
                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept.getId());
                treeType.setName(dept.getDeptName());
                Boolean aBoolean = ChildNode(dept.getId());
                if (aBoolean) {
                    List<TreeType> listrById = listrById(deptList, dept);
                    treeType.setChildren(listrById);
                } else {
                    List<Employees> employeess = settingsService.selectEmployeesParentId(dept.getId(), TokenInformation.getCreateid());
                    List<TreeType> arrlist = new ArrayList<>();
                    for (Employees employees1 : employeess) {
                        if (TokenInformation.getType() == 1) {
                            if (employees1.getId().equals(TokenInformation.getUserid()) && employees1.getEmployeeName().equals(TokenInformation.getUsername())) {
                                continue;
                            }
                        }
                        TreeType treeType1 = new TreeType();
                        treeType1.setId(employees1.getId().toString());
                        treeType1.setName(employees1.getEmployeeName());
                        treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
                        arrlist.add(treeType1);

                    }
                    treeType.setChildren(arrlist);
                }
                treeTypes.add(treeType);
            }
        }

        return treeTypes;
    }

    /**
     * 迭代查询子列表-(屏蔽当前登录人信息)
     *
     * @param depts
     * @param dept
     * @return
     */
    public List<TreeType> listrById(List<Dept> depts, Dept dept) {
        List<TreeType> list = new ArrayList<>();
        for (Dept dept1 : depts) {

            if (dept1.getParentId().equals(dept.getId())) {

                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept1.getId());
                treeType.setName(dept1.getDeptName());
                List<TreeType> childrens = listrById(depts, dept1);

                treeType.setChildren(childrens);
                list.add(treeType);

            }
        }
        List<Employees> employees = settingsService.selectEmployeesParentId(dept.getId(), TokenInformation.getCreateid());  //根据部门id以及团队id查询该部门人员信息
        List<TreeType> arrlist = new ArrayList<>();
        for (Employees employees1 : employees) {
            if (TokenInformation.getType() == 1) {
                if (employees1.getId().equals(TokenInformation.getUserid())
                        && employees1.getEmployeeName().equals(TokenInformation.getUsername())) {
                    continue;
                }
            }
            TreeType treeType1 = new TreeType();
            treeType1.setId(employees1.getId().toString());
            treeType1.setName(employees1.getEmployeeName());
            treeType1.setEmployeesWorking(employees1.getEmployeesWorking());
            arrlist.add(treeType1);
        }

        list.addAll(arrlist);


        return list;
    }


    /**
     * 处理团队部门参数
     *
     * @param teamCaseUtils 查询参数-需要带当前登录信息
     */
    public void handleDeptParam(TeamCaseUtils teamCaseUtils) {
        LoginUser loginUser = teamCaseUtils.getLoginUser();
        if (!ObjectUtils.isEmpty(teamCaseUtils.getStringDeptId())) {
            int dept = teamCaseUtils.getStringDeptId().indexOf("Dept");
            if (dept >= 0) {
                String substring = teamCaseUtils.getStringDeptId().substring(5);
                if (!StringUtils.isNumeric(substring)) {
                    throw new GlobalException("部门id有误");
                } else {
                    teamCaseUtils.setDeptId(Integer.parseInt(substring));
                }
            } else {
                if (!StringUtils.isNumeric(teamCaseUtils.getStringDeptId())) {
                    throw new GlobalException("员工id有误");
                } else {
                    teamCaseUtils.setUserId(Integer.parseInt(teamCaseUtils.getStringDeptId()));
                }
            }
        }

        List<Integer> list;
        if (ObjectUtils.isEmpty(teamCaseUtils.getUserId())) {
            if (TokenInformation.getType() == 1) {    //员工账号
                if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                    list = Encapsulation(loginUser);
                } else {
                    list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
                }
            } else {
                if (ObjectUtils.isEmpty(teamCaseUtils.getDeptId())) {
                    list = null;
                } else {
                    list = Encapsulations(teamCaseUtils.getDeptId(), loginUser);
                }
            }
        } else {
            list = null;
        }

        teamCaseUtils.setDeptIds(list);
        teamCaseUtils.setDeptId(null);
        teamCaseUtils.setCreateId(TokenInformation.getCreateid(loginUser));
    }


}
