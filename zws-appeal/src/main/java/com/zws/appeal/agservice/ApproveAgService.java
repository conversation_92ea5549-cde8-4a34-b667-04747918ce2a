package com.zws.appeal.agservice;

import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.enums.MessageFormats;
import com.zws.common.core.enums.approval.ProceEnum;
import com.zws.common.core.exception.GlobalException;
import com.zws.appeal.domain.ApproveProce;
import com.zws.appeal.enums.ApproveStatusEnum;
import com.zws.appeal.pojo.ApprovalRecord;
import com.zws.appeal.pojo.SignCollectionUtils;
import com.zws.appeal.pojo.messageCenter.TeamMessageCenters;
import com.zws.appeal.pojo.myApproval.MySignRecordUtils;
import com.zws.appeal.service.MyApprovalService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.system.api.RemoteLetterService;
import com.zws.system.api.domain.CaseSignRecord;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 审批AgService
 *
 * <AUTHOR>
 * @date 2024/5/7 20:50
 */
@Slf4j
@Component
public class ApproveAgService {

    @Autowired
    private TeamSysAgService teamSysAgService;
    @Autowired
    private MyApprovalService myApprovalService;
    @Autowired
    private RemoteLetterService remoteLetterService;
    @Autowired
    private AgCaseService agCaseService;

    /**
     * 签章审批 处理(通过/不通过)
     *
     * @param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void signRecordHandle(ApprovalRecord<SignCollectionUtils> approvalRecord,LoginUser loginUser) {
        //参数处理
        parameterHandling(approvalRecord);
        List<Long> ids = approvalRecord.getIds();
        if (approvalRecord.getAllQuery()!=null && approvalRecord.getAllQuery()){
            ids=new ArrayList<>();
            //搜索结果全选
            SignCollectionUtils queryParam = approvalRecord.getQueryParam();
            queryParam.setApproveStarts(2);
            List<MySignRecordUtils> mySignRecordUtils = agCaseService.selectSignRecord(queryParam);
            for (MySignRecordUtils temp:mySignRecordUtils){
                ids.add(temp.getId());
            }
        }


        List<CaseSignRecord> records = new ArrayList<>();
        List<ApproveProce> proceList = new ArrayList<>();


        Integer sort = teamSysAgService.getApprovalSort(loginUser);
        teamSysAgService.filterDuplicates(ids, sort, approvalRecord.getApproveCode());
        for (Long id : ids) {
            CaseSignRecord signRecord = new CaseSignRecord();
            if (approvalRecord.getApproveStart().equals(0)) {   //审批通过
                signRecord.setProce(new Long(ProceEnum.CSD_END_REVIEW.getCode()));  //审核进程，0-待审核，1-催收端审核中，2-审核结束
                signRecord.setExamineState(ApproveStatusEnum.REVIEWED_ING.getInfo());  //审核状态

            } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
                signRecord.setProce(new Long(ProceEnum.END_REVIEW.getCode()));  //审核进程，0-待审核，1-催收端审核中，2-审核结束
                signRecord.setExamineState(ApproveStatusEnum.NOT_PASS.getInfo());  //审核状态
            }

            signRecord.setId(id);
            signRecord.setProceSort(sort.longValue());  //审核进程顺序
            signRecord.setUpdateById(loginUser.getUserid());
            signRecord.setUpdateBy(loginUser.getUsername());
            records.add(signRecord);
            //更新记录
            myApprovalService.updateSignRecord(signRecord);


            //更新函件批次状态
            CaseSignRecord caseSignRecord = myApprovalService.selectSignRecordInfo(signRecord.getId());
            remoteLetterService.updateLetterMessage(caseSignRecord.getBatchNum(),signRecord.getExamineState());

            //审批记录
            ApproveProce approveProce = new ApproveProce();
            approveProce.setApproveCode(approvalRecord.getApproveCode());  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
            approveProce.setApplyId(id);  //申请id,根据审核类型查找不同的申请表
            approveProce.setApproveStart(approvalRecord.getApproveStart());  //审核状态,0-通过，1-不通过
            approveProce.setApproveTime(new Date());  //审核时间
            approveProce.setReviewer(loginUser.getUsername());  //审核人
            approveProce.setReviewerId(loginUser.getUserid().intValue());  //审核人id
            approveProce.setRefuseReason(approvalRecord.getFailureReason());  //拒绝理由
            approveProce.setApproveSort(sort);  //审核顺序，审核流程中的排序
            approveProce.setDelFlag(BaseConstant.DelFlag_Being);
            approveProce.setOperationType(  TokenInformation.getType());  //操作人类型（0-主账号;1-员工账号;2-资产端账号）
            proceList.add(approveProce);
        }

        //写入审批记录历史表
        myApprovalService.insertApproveProce(proceList);

        //审批消息提醒
        //approveMessageCenters();


    }


    /**
     * 审批通用参数处理
     */
    private void parameterHandling(ApprovalRecord approvalRecord){
        if (!ObjectUtils.isEmpty(approvalRecord.getFailureReason())) {
            if (approvalRecord.getFailureReason().length() > 300) {
                throw new GlobalException("字数限制为300字,请重新输入");
            }
        }
        if (ObjectUtils.isEmpty(approvalRecord.getIds())) {
            throw new GlobalException("申请id不能为空，至少选择一个审批");
        }
    }

    /**
     * 审批消息提醒
     */
    private void approveMessageCenters(MessageFormats messageFormats,TeamMessageCenters teamMessageCenters){

    }
}
