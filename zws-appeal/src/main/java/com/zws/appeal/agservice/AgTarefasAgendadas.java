package com.zws.appeal.agservice;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.zws.appeal.service.AsyncLoggerService;
import com.zws.appeal.service.SettingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 定时任务
 *
 * @Author: 马博新
 * @DATE: Created in 2022/11/17 15:35
 */
@Component
@Transactional //开启事务注解
public class AgTarefasAgendadas {

    @Autowired
    private SettingsService settingsService;
    @Autowired
    private AsyncLoggerService asyncLoggerService;


    /**
     * 定时删除登录日志以及操作日志
     */
    @Scheduled(cron = "0 30 0 * * ? ")
//    @Scheduled(cron = "0 0/6 * * * ?")
    public void commodityTiming() {
//        System.err.println("定时删除日志开始（催收端）");
        Date newDate = DateUtil.offset(new Date(), DateField.YEAR, -1);
        settingsService.delectTeamLogininfor(newDate);
        asyncLoggerService.delectTeamLogininfor(newDate);
//        System.err.println("定时删除日志结束（催收端）");
    }
}
