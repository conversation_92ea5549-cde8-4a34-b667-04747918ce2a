package com.zws.appeal.agservice;

import cn.hutool.core.date.DateUtil;
import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.exception.GlobalException;
import com.zws.appeal.domain.BrokerageTeam;
import com.zws.appeal.domain.DetailsBrokerage;
import com.zws.appeal.domain.RecoveryAsset;
import com.zws.appeal.domain.RiskTeam;
import com.zws.appeal.enums.SettlementStatusEnum;
import com.zws.appeal.pojo.AggregatedData;
import com.zws.appeal.pojo.GenerateFileData;
import com.zws.appeal.service.BrokerageTeamService;
import com.zws.appeal.utils.FileConvertSaas;
import com.zws.appeal.utils.TimeUtils;
import com.zws.appeal.utils.TokenInformation;
import com.zws.appeal.utils.pdfutils.BrokeragePdfUtils;
import com.zws.system.api.RemoteCaseService;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.domain.SysFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 佣金结算处理类
 *
 * @Author: 马博新
 * @DATE: Created in 2023/4/27 11:45
 */
@Slf4j
@Component
@Transactional //开启事务注解
public class AgBrokerageTeamService {

    @Autowired
    private BrokerageTeamService brokerageTeamService;
    @Autowired
    private RemoteCaseService remoteCaseService;
    @Autowired
    private RemoteFileService remoteFileService;


    /**
     * 根据结佣日期（年/月）查询本机构的结佣详情表数据
     *
     * @param brokerageTeam
     * @return
     */
    public DetailsBrokerage selectDetailsBrokerage(BrokerageTeam brokerageTeam) {
        List<Integer> list = new ArrayList<>();
        list.add(SettlementStatusEnum.TO_BE_CONFIRMED.getCode());
        list.add(SettlementStatusEnum.CONFIRMED.getCode());
        list.add(SettlementStatusEnum.PAID_COMMISSION.getCode());
        brokerageTeam.setTeamId(TokenInformation.getCreateid().longValue());
        brokerageTeam.setStatesList(list);
//        根据团队id以及结佣日期（年/月）查询结算中心机构结佣信息
        List<BrokerageTeam> brokerageTeams = brokerageTeamService.selectTeamBrokerage(brokerageTeam);
        if (ObjectUtils.isEmpty(brokerageTeams)) {
            return null;
        }
        BrokerageTeam brokerageTeam1 = brokerageTeams.get(0);
//        根据佣金表id以及状态查询机构佣金详情表
        DetailsBrokerage detailsBrokerage = new DetailsBrokerage();
        detailsBrokerage.setBrokerageId(brokerageTeam1.getId());
        detailsBrokerage.setStatesList(list);
        List<DetailsBrokerage> detailsBrokerages = brokerageTeamService.selectDetailsBrokerage(detailsBrokerage);
        if (ObjectUtils.isEmpty(detailsBrokerages)) {
            return null;
        }
        return detailsBrokerages.get(0);
    }

    /**
     * 根据佣金详情表id查询统计回收合计以及风险奖惩合计
     *
     * @param detailsId
     * @return
     */
    public AggregatedData statisticsMoney(Long detailsId) {
        AggregatedData aggregatedData = new AggregatedData();
//        统计回款金额
        BigDecimal bigDecimal = brokerageTeamService.selectCount(detailsId);
        aggregatedData.setTotalRecycling(bigDecimal.setScale(2, RoundingMode.HALF_UP));//回收合计
        List<RiskTeam> riskTeams = brokerageTeamService.selectRiskTeam(detailsId);
        BigDecimal money = BigDecimal.ZERO;
        if (!ObjectUtils.isEmpty(riskTeams)) {
            for (RiskTeam row : riskTeams) {
                if (row.getPositiveNegative() == 1) {
                    money = money.add(row.getFloatingRate() == null ? BigDecimal.ZERO : row.getFloatingRate());
                } else if (row.getPositiveNegative() == 2) {
                    money = money.subtract(row.getFloatingRate() == null ? BigDecimal.ZERO : row.getFloatingRate());
                }
            }
        }
        aggregatedData.setTotalRisk(money.setScale(2, RoundingMode.HALF_UP));//风险奖罚合计
        BigDecimal bigDecimal1 = money.add(bigDecimal);
        aggregatedData.setTotal(bigDecimal1.setScale(2, RoundingMode.HALF_UP));//总计
        return aggregatedData;
    }


    /**
     * 有异议（驳回佣金结算信息）--生成新的佣金信息
     *
     * @param detailsId
     * @param rejectReason 驳回原因
     */
    public void rejectObjection(Long detailsId, String rejectReason) {
//        根据佣金表id以及状态查询机构佣金详情表
        DetailsBrokerage detailsBrokerage = new DetailsBrokerage();
        detailsBrokerage.setId(detailsId);
        List<DetailsBrokerage> detailsBrokerages = brokerageTeamService.selectDetailsBrokerage(detailsBrokerage);
        if (ObjectUtils.isEmpty(detailsBrokerages)) {
            throw new GlobalException("机构佣金详情查询错误");
        }
        DetailsBrokerage detailsBrokerage1 = detailsBrokerages.get(0);
        if (detailsBrokerage1.getBrokerageId() == null) {
            throw new GlobalException("机构佣金id查询错误");
        }
        if (detailsBrokerage1.getStates() != SettlementStatusEnum.TO_BE_CONFIRMED.getCode()) {
            throw new GlobalException("该佣金详情不是待确认状态，无法操作驳回");
        }
        detailsBrokerage.setStates(SettlementStatusEnum.REJECTED.getCode());
        detailsBrokerage.setRejectDate(new Date());
        detailsBrokerage.setRejectReason(rejectReason);
//        修改佣金详情表状态为已驳回
        brokerageTeamService.updateDetailsBrokerage(detailsBrokerage);

        BrokerageTeam brokerageTeam = new BrokerageTeam();
        brokerageTeam.setId(detailsBrokerage1.getBrokerageId());
//        根据团队id以及结佣日期（年/月）查询结算中心机构结佣信息
        List<BrokerageTeam> brokerageTeams = brokerageTeamService.selectTeamBrokerage(brokerageTeam);
        if (ObjectUtils.isEmpty(brokerageTeams)) {
            throw new GlobalException("机构佣金查询错误");
        }
        BrokerageTeam brokerageTeam1 = brokerageTeams.get(0);
        if (brokerageTeam1.getId() == null) {
            throw new GlobalException("机构佣金id查询错误");
        }
        if (brokerageTeam1.getCreateTime() == null) {
            throw new GlobalException("创建时间查询错误");
        }
//        修改佣金表以及佣金详情表状态为已驳回
        brokerageTeam.setStates(SettlementStatusEnum.REJECTED.getCode());
        brokerageTeamService.updateBrokerageTeam(brokerageTeam);

//        重新生成新的结佣数据
        Date monthBegin = TimeUtils.getMonthBeginBeforeAny(brokerageTeam1.getCreateTime());//获取上个月月初
        String format = DateUtil.format(monthBegin, "yyyy-MM-dd HH:mm:ss");
        monthBegin = DateUtil.parse(format, "yyyy-MM-dd HH:mm:ss");
        Date monthEnd = TimeUtils.getMonthEndBeforeAny(brokerageTeam1.getCreateTime());//获取上个月月末
        String format1 = DateUtil.format(monthEnd, "yyyy-MM-dd HH:mm:ss");
        monthEnd = DateUtil.parse(format1, "yyyy-MM-dd HH:mm:ss");
        try {
            R r = remoteCaseService.calculateTeamCommission(brokerageTeam1.getId(), TokenInformation.getCreateid().longValue(), monthBegin, monthEnd, detailsId, SecurityConstants.INNER);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new GlobalException("相关服务调用失败，请联系管理员");
        }
//        if (r.getCode() != R.SUCCESS) {
//            throw new ServiceException(r.getMsg());
//        }
    }


    /**
     * 点击确认提交结算佣金--（生成pdf文件）
     *
     * @param detailsId
     */
    public void confirm(Long detailsId) {
//        根据佣金表id以及状态查询机构佣金详情表
        DetailsBrokerage detailsBrokerage = new DetailsBrokerage();
        detailsBrokerage.setId(detailsId);
        List<DetailsBrokerage> detailsBrokerages = brokerageTeamService.selectDetailsBrokerage(detailsBrokerage);
        if (ObjectUtils.isEmpty(detailsBrokerages)) {
            throw new GlobalException("机构佣金详情查询错误");
        }
        DetailsBrokerage detailsBrokerage1 = detailsBrokerages.get(0);
        if (detailsBrokerage1.getBrokerageId() == null) {
            throw new GlobalException("机构佣金id查询错误");
        }
        if (detailsBrokerage1.getStates() != SettlementStatusEnum.TO_BE_CONFIRMED.getCode()) {
            throw new GlobalException("该佣金详情不是待确认状态，无法操作确认");
        }
        if (detailsBrokerage1.getGenerateNot() != null && detailsBrokerage1.getGenerateNot() == 1) {
            throw new GlobalException("该佣金详情数据已确认，请勿重复提交");
        }
//        detailsBrokerage.setStates(SettlementStatusEnum.CONFIRMED.getCode());
        detailsBrokerage.setGenerateNot(1);
//        修改佣金详情表 正在生成文件
        brokerageTeamService.updateDetailsBrokerage(detailsBrokerage);

        BrokerageTeam brokerageTeam = new BrokerageTeam();
        brokerageTeam.setId(detailsBrokerage1.getBrokerageId());
//        根据主键id查询机构佣金表数据
        List<BrokerageTeam> brokerageTeams = brokerageTeamService.selectTeamBrokerage(brokerageTeam);
        if (ObjectUtils.isEmpty(brokerageTeams)) {
            throw new GlobalException("机构佣金查询错误");
        }
        BrokerageTeam brokerageTeam1 = brokerageTeams.get(0);
        if (brokerageTeam1.getId() == null) {
            throw new GlobalException("机构佣金id查询错误");
        }
        if (brokerageTeam1.getCreateTime() == null) {
            throw new GlobalException("创建时间查询错误");
        }

//        根据佣金详情表id查询资产回收情况
        List<RecoveryAsset> recoveryAssets = brokerageTeamService.selectRecoveryAsset(detailsId);
//        根据佣金详情表id查询机构风险奖罚设置
        List<RiskTeam> riskTeams = brokerageTeamService.selectRiskTeam(detailsId);
//        根据佣金详情表id查询统计回收合计以及风险奖惩合计
        AggregatedData aggregatedData = statisticsMoney(detailsId);
        GenerateFileData generateFileData = new GenerateFileData();
        generateFileData.setAggregatedData(aggregatedData);
        generateFileData.setRiskTeams(riskTeams);
        generateFileData.setRecoveryAssets(recoveryAssets);

//        根据数据信息生成pdf文件
        try {
            File brokeragePdfFile = BrokeragePdfUtils.createBrokeragePdfFile(generateFileData);
            MultipartFile multipartFile = FileConvertSaas.fileConvertMultipartFile(brokeragePdfFile);
            R<SysFile> retUpload = remoteFileService.upload(multipartFile);
            if (retUpload.getCode() == R.SUCCESS) {
                String url = retUpload.getData().getUrl();  //文件路径
                brokerageTeam.setFileUrl(url);
                detailsBrokerage.setGenerateNot(3);
            } else {
                throw new GlobalException("上传文件失败");
            }
        } catch (Exception e) {
            detailsBrokerage.setGenerateNot(2);
            detailsBrokerage.setFailureReason(e.getMessage());
        }
////        修改佣金表以及佣金详情表状态为已确认
        brokerageTeam.setStates(SettlementStatusEnum.CONFIRMED.getCode());
        brokerageTeamService.updateBrokerageTeam(brokerageTeam);

        detailsBrokerage.setStates(SettlementStatusEnum.CONFIRMED.getCode());
        brokerageTeamService.updateDetailsBrokerage(detailsBrokerage);
    }

    /**
     * 导出（根据佣金详情表id查询文件url地址）
     *
     * @param detailsId
     * @return
     */
    public Map<String, Object> exportInformation(Long detailsId) {
        Map<String, Object> map = new HashMap<>();
//        根据佣金表id以及状态查询机构佣金详情表
        DetailsBrokerage detailsBrokerage = new DetailsBrokerage();
        detailsBrokerage.setId(detailsId);
        List<DetailsBrokerage> detailsBrokerages = brokerageTeamService.selectDetailsBrokerage(detailsBrokerage);
        if (ObjectUtils.isEmpty(detailsBrokerages)) {
            throw new GlobalException("机构佣金详情查询错误");
        }
        DetailsBrokerage detailsBrokerage1 = detailsBrokerages.get(0);
        if (detailsBrokerage1.getBrokerageId() == null) {
            throw new GlobalException("机构佣金id查询错误");
        }
        if (detailsBrokerage1.getStates() != SettlementStatusEnum.CONFIRMED.getCode() && detailsBrokerage1.getStates() != SettlementStatusEnum.PAID_COMMISSION.getCode()) {
            throw new GlobalException("该佣金详情不是已确认/已结佣状态，无法导出文件");
        }

        BrokerageTeam brokerageTeam = new BrokerageTeam();
        brokerageTeam.setId(detailsBrokerage1.getBrokerageId());
//        根据主键id查询机构佣金表数据
        List<BrokerageTeam> brokerageTeams = brokerageTeamService.selectTeamBrokerage(brokerageTeam);
        if (ObjectUtils.isEmpty(brokerageTeams)) {
            throw new GlobalException("机构佣金查询错误");
        }
        BrokerageTeam brokerageTeam1 = brokerageTeams.get(0);
        String fileUrl = brokerageTeam1.getFileUrl();
        if (fileUrl == null) {
            throw new GlobalException("文件生成错误，请联系管理员");
        }
//        根据佣金表主键id查询团队名称以及结佣日期（年/月）------生成文件名称
        BrokerageTeam brokerageTeam2 = brokerageTeamService.selectFileName(detailsBrokerage1.getBrokerageId());
        if (ObjectUtils.isEmpty(brokerageTeam2)) {
            throw new GlobalException("文件名称数据查询为空");
        }
        String teamName = brokerageTeam2.getTeamName() == null ? "" : brokerageTeam2.getTeamName();
        String settlementDate = brokerageTeam2.getSettlementDate() == null ? "" : brokerageTeam2.getSettlementDate();
        String fileName = teamName + settlementDate;
        map.put("fileUrl", fileUrl);
        map.put("fileName", fileName);
        return map;
    }
}
