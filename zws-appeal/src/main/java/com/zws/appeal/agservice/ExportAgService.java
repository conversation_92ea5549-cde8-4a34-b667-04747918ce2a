package com.zws.appeal.agservice;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.core.domain.ExportTaskData;
import com.zws.common.core.enums.ExportStartEnum;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.threadpool.PoolManageMent;
import com.zws.common.core.threadpool.TaskManager;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.redis.service.RedisService;
import com.zws.appeal.domain.log.ExportLog;
import com.zws.appeal.enums.CsExportClassEnum;
import com.zws.appeal.service.IExportLogService;
import com.zws.appeal.task.ExportTask;
import com.zws.appeal.utils.TokenInformation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 导出任务的 agservice
 *
 * <AUTHOR>
 * @date 2024年1月23日16:50:12
 */
@Component
public class ExportAgService {

    @Autowired
    private IExportLogService exportLogService;
    @Autowired
    private RedisService redisService;

    /**
     * 后台执行导出任务
     *
     * @param exportClass 导出类别
     * @param clazz       bean类       例如：IExportLogService.class
     * @param funName     函数名        例如：“insert”
     * @param params      参数          例如：new Object[]{exportLog}
     * @param exportType  导出类型       列如：“导出案件”
     * @return 返回文件名称
     */
    public String exportTask(CsExportClassEnum exportClass, Class clazz, String funName, Object[] params, String exportType) {
        if (exportClass == null) {
            throw new ServiceException("导出类别 不能为空");
        }
        if (StrUtil.isEmpty(exportType)) {
            throw new ServiceException("导出类型 不能为空");
        }
        if (clazz == null) {
            throw new ServiceException("bean 不能为空");
        }
        if (funName == null) {
            throw new ServiceException("函数名 不能为空");
        }
        String key = CacheConstants.EXPORT_ORDER_KEY + DateUtils.getDate() + ":" + exportClass.name() + ":" + TokenInformation.getCreateid();
        long orderNum = 0;
        if (redisService.hasKey(key)) {
            orderNum = redisService.getCacheObject(key, Long.class);
        }
        orderNum++;
        String orderNumStr = NumberUtil.decimalFormat("000", orderNum);
        String fileNme = StrUtil.format("{}{}", exportType, DateUtils.dateTimeNow() + orderNumStr);

        ExportLog record = new ExportLog();
        record.setCreateBy(TokenInformation.getUsername());
        record.setCreateById(TokenInformation.getUserid().longValue());
        record.setUpdateBy(TokenInformation.getUsername());
        record.setUpdateById(TokenInformation.getUserid().longValue());
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setOperationType(TokenInformation.getType());
        record.setExportType(exportType);
        record.setExportClass(exportClass.getCode());
        record.setFileName(funName);
        record.setFileName(fileNme);
        record.setTeamId(TokenInformation.getCreateid().longValue());
        record.setExportStart(ExportStartEnum.ING.getCode());
        Long exportLogId = exportLogService.insert(record);
        record.setId(exportLogId);

        ExportTaskData taskData = new ExportTaskData();
        taskData.setExportLogId(exportLogId);
        taskData.setClazz(clazz);
        //taskData.setExportClass(exportClass);
        taskData.setFileNme(fileNme);
        taskData.setParams(params);
        taskData.setExportType(exportType);
        taskData.setFunName(funName);


        redisService.setCacheObject(CacheConstants.EXPORT_TASK_KEY + exportLogId, taskData);
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        ExportTask workTask = new ExportTask(taskData);
        TaskManager.addTask(workTask);
        redisService.setCacheObject(key, orderNum, 24L, TimeUnit.HOURS);
        return fileNme;
    }

}
