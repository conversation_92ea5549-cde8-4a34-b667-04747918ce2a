package com.zws.appeal.agservice;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zws.appeal.mapper.CaseMapper;
import com.zws.appeal.mapper.MyApprovalMapper;
import com.zws.appeal.pojo.appeal.PhoneMediationPojo;
import com.zws.appeal.service.appeal.ITimeMangeService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.ScheduleVo;
import com.zws.common.core.domain.sms.SendMessagePojo;
import com.zws.common.core.domain.sms.SmsParameters;
import com.zws.common.core.enums.MessageFormats;
import com.zws.common.core.enums.TimeContentFormats;
import com.zws.common.core.enums.approval.ProceEnum;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.threadpool.PoolManageMent;
import com.zws.common.core.threadpool.TaskManager;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.domain.*;
import com.zws.appeal.domain.division.DivisionalConditions;
import com.zws.appeal.domain.record.*;
import com.zws.appeal.enums.ApproveEnum;
import com.zws.appeal.pojo.*;
import com.zws.appeal.pojo.messageCenter.TeamMessageCenters;
import com.zws.appeal.pojo.myApproval.*;
import com.zws.appeal.pojo.staticConst.StaticConstantClass;
import com.zws.appeal.schedule.IScheduleService;
import com.zws.appeal.service.*;
import com.zws.appeal.task.CoDebtDataTask;
import com.zws.appeal.thread.*;
import com.zws.appeal.utils.*;
import com.zws.system.api.RemoteCaseService;
import com.zws.system.api.RemoteCisService;
import com.zws.system.api.RemoteSmsService;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@Primary
@Transactional //开启事务注解
public class AgCaseService {

    /**
     * 上传文件存储在本地的根路径
     */
    @Value("${filePate.file_path_link}")
    private String localFilePath;

    @Autowired
    private RedisService redisService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private MyApprovalService myApprovalService;
    @Autowired
    private RuleDivisionService ruleDivisionService;
    @Autowired
    private CaseService caseService;
    @Autowired
    private RecordService recordService;
    @Autowired
    private DesensitizationRedis desensitizationRedis;
    @Autowired
    private MessageToolUtils messageToolUtils;
    @Autowired
    private RemoteCaseService remoteCaseService;
    @Autowired
    private IScheduleService scheduleService;
    @Autowired
    private DesensitizationAgService desensitizationAgService;
    @Autowired
    private TeamSysAgService teamSysAgService;
    @Resource
    private RemoteSmsService remoteSmsService;
    @Autowired
    private AgLawsuitService agLawsuitService;
    @Autowired
    private ITimeMangeService timeMangeService;
    @Autowired
    private SettingsService settingsService;
    @Autowired
    private RemoteCisService remoteCisService;
    @Autowired
    private CaseMapper caseMapper;


    /**
     * 规则分案(写入分案记录，修改案件催收员)
     *
     * @param divisionalConditions
     */
    public void writeRuleDivision(DivisionalConditions divisionalConditions) {

        divisionalConditions.getExportReminder().setCreateId(TokenInformation.getCreateid());

        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        RuleDivisionThread ruleDivisionThread = new RuleDivisionThread(divisionalConditions,SecurityUtils.getLoginUser());
        TaskManager.addTask(ruleDivisionThread);
    }

    /**
     * 回收案件查询案件详情--异步
     *
     * @param exportReminder
     */
    @Async
    public void updateRecovery(ExportReminder exportReminder) {
        LoginUser loginUser = exportReminder.getLoginUser();
        ThreadEntityPojo threadEntityPojo = new ThreadEntityPojo();
        threadEntityPojo.setCreateId(TokenInformation.getCreateid(loginUser));
        threadEntityPojo.setUserId(TokenInformation.getUserid(loginUser));
        threadEntityPojo.setUser(TokenInformation.getUsername(loginUser));
        threadEntityPojo.setType(TokenInformation.getType(loginUser));

//        PoolManageMent pool = PoolManageMent.getInstance();
//        pool.init();
//        RecoveryCasesThread recoveryCasesThread = new RecoveryCasesThread(exportReminder, threadEntityPojo);
//        TaskManager.addTask(recoveryCasesThread);

        List<CaseManage> caseManages = null;
        if (exportReminder.getMethodNo() == 0) {
            caseManages = caseService.selectCaseManages(exportReminder);
        } else if (exportReminder.getMethodNo() == 1) {
            if (!ObjectUtils.isEmpty(exportReminder.getExpeditorId())) {
                Map<String, Object> map = new HashMap<>();
                map.put("odvId", exportReminder.getExpeditorId());
                map.put("createId", TokenInformation.getCreateid());
                caseManages = caseService.selectCaseManageByOdvId(map);
            } else {
                caseManages = new ArrayList<>();
            }
        } else {
            caseManages = new ArrayList<>();
        }
        if (!ObjectUtils.isEmpty(caseManages) && caseManages.size() > 0) {
            for (CaseManage caseManage1 : caseManages) {
                caseManage1.setUpdateBy(TokenInformation.getUsername());
                caseManage1.setUpdateTime(new Date());
                caseManage1.setOperation("回收案件");
            }
            caseService.insertTemplate(caseManages, loginUser);
            caseService.updateRecovery(caseManages);
        }
        log.info("异步写入回收案件");
    }

    /**
     * 按分案范围匹配数据
     *
     * @param divisionalConditions
     * @return
     */
    public List<CaseManage> selectData(DivisionalConditions divisionalConditions) {
        List<CaseManage> caseManageList;

        Integer distributeType = divisionalConditions.getDistributeType();
//        按分案范围匹配数据
        if (divisionalConditions.getDivisionScope().equals(0)) {
            ExportReminder exportReminder = divisionalConditions.getExportReminder();
            List<String> list = new ArrayList<>();
            list.add("0");
            list.add("1");
            list.add("3");
            exportReminder.setCaseStates(list);
            exportReminder.setDistributeType(distributeType);
            caseManageList = ruleDivisionService.selectCaseManages(exportReminder);
        } else if (divisionalConditions.getDivisionScope().equals(1)) {
            ExportReminder exportReminder = divisionalConditions.getExportReminder();
            List<String> list = new ArrayList<>();
            list.add("0");  //未分配
            exportReminder.setCaseStates(list);
            exportReminder.setDistributeType(distributeType);
            caseManageList = ruleDivisionService.selectCaseManages(exportReminder);
        } else {
            throw new GlobalException("分案范围错误");
        }
        return caseManageList;
    }

    /**
     * 按分案范围匹配数据-查询案件总的金额
     *
     * @param divisionalConditions
     * @return
     */
    public Map<String, Object> selectDataMoney(DivisionalConditions divisionalConditions) {
        Map<String, Object> map;
//        按分案范围匹配数据
        if (divisionalConditions.getDivisionScope().equals(0)) {
            ExportReminder exportReminder = divisionalConditions.getExportReminder();
            List<String> list = new ArrayList<>();
            list.add("0");
            list.add("1");
            list.add("3");
            exportReminder.setCaseStates(list);
            map = ruleDivisionService.selectCaseManageMoneyCount(exportReminder);
        } else if (divisionalConditions.getDivisionScope().equals(1)) {
            ExportReminder exportReminder = divisionalConditions.getExportReminder();
            List<String> list = new ArrayList<>();
            list.add("0");  //未分配
            exportReminder.setCaseStates(list);
            map = ruleDivisionService.selectCaseManageMoneyCount(exportReminder);
        } else {
            throw new GlobalException("分案范围错误");
        }
        return map;
    }

    /**
     * 规则分案（预览分案结果）
     *
     * @param divisionalConditions
     * @param scheduleVo           异步进度
     * @return
     */
    public void ruleSplitPreview(DivisionalConditions divisionalConditions, ScheduleVo scheduleVo) {

        if (ObjectUtils.isEmpty(divisionalConditions.getDivisionScope())) {
            throw new GlobalException("分案范围不能为空");
        }
        if (ObjectUtils.isEmpty(divisionalConditions.getDivisionalMode())) {
            throw new GlobalException("分配模式不能为空");
        }
        if (ObjectUtils.isEmpty(divisionalConditions.getPersonnelInformation())) {
            throw new GlobalException("请选择分案人员");
        }

        scheduleVo.setSchedule(1);
        scheduleService.setSchedule(scheduleVo);

        divisionalConditions.getExportReminder().setCreateId(TokenInformation.getCreateid());

        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        RuleSplitPreviewTask workTask = new RuleSplitPreviewTask(divisionalConditions, scheduleVo);
        TaskManager.addTask(workTask);

        return;
    }

    /**
     * 规则分案
     *
     * @param divisionalConditions
     * @param scheduleVo
     * @return
     */
    public List<DivisionPersonnel> ruleSplit(DivisionalConditions divisionalConditions, ScheduleVo scheduleVo) {
        TimeInterval timer = DateUtil.timer();
        timer.start("1");

        scheduleVo.setSchedule(5);
        scheduleService.setSchedule(scheduleVo);
        List<CaseManage> caseManageList = selectData(divisionalConditions);  //按分案范围匹配数据

        BigDecimal number = BigDecimal.ZERO;    //案件总金额
        int numbers = caseManageList.size();    //案件总量

        Map<String, Object> map1 = selectDataMoney(divisionalConditions);
        number = (BigDecimal) map1.get("money");

        scheduleVo.setSchedule(8);
        scheduleService.setSchedule(scheduleVo);


//        分配模式如果为百分比转换为具体数字
        List<DivisionPersonnel> personnelInformation = divisionalConditions.getPersonnelInformation();
        for (DivisionPersonnel divisionPersonnel : personnelInformation) {
            if (ObjectUtils.isEmpty(divisionPersonnel.getValuePercentage()) && ObjectUtils.isEmpty(divisionPersonnel.getNumericalValue())) {
                throw new GlobalException("员工分案数量/数量百分比不能为空");
            }
            String valuePercentage = divisionPersonnel.getValuePercentage();
            if (!ObjectUtils.isEmpty(valuePercentage)) {
                if (!StringUtils.isNumeric(valuePercentage)) {
                    throw new GlobalException("请填写正确的数量百分比");
                }
                double v = Double.parseDouble(valuePercentage);
                double v1 = v / 100;
                if (divisionalConditions.getDivisionalMode() == 1) {
                    double v2 = numbers * v1;   //该员工所占案件数量
                    divisionPersonnel.setNumericalValue(new BigDecimal(v2));

                } else if (divisionalConditions.getDivisionalMode() == 3) {
                    BigDecimal bigDecimal = new BigDecimal(v1 + "");
                    BigDecimal multiply = number.multiply(bigDecimal);   //该员工所占总金额
                    divisionPersonnel.setNumericalValue(multiply);
                }
            }
        }

        scheduleVo.setSchedule(10);
        scheduleService.setSchedule(scheduleVo);


        List<DivisionPersonnel> divisionPersonnels = new ArrayList<>();   //分配完毕的人员案件信息
        long t = timer.intervalMs("1");
        log.info("分案耗时:" + t + " ms");

        timer.start("2");
//        按分案原则（0-共债分案，1-交叉分案）
        if (!ObjectUtils.isEmpty(caseManageList)) {

            divisionPersonnels = generalTreatmentBreadth(divisionalConditions, caseManageList, scheduleVo);

            scheduleVo.setSchedule(80);
            scheduleService.setSchedule(scheduleVo);
        }


        long t2 = timer.intervalMs("2");
        log.info("按分案原则（0-共债分案，1-交叉分案）耗时:" + t2 + " ms");
        return divisionPersonnels;
    }

    /**
     * 判断分案原则
     *
     * @param divisionalPrinciple
     * @return
     */
    public int selectSwitch(List<Integer> divisionalPrinciple) {
        if (ObjectUtils.isEmpty(divisionalPrinciple)) {   //没有共债和交叉选项
            return 1;
        } else if (divisionalPrinciple.contains(0) && divisionalPrinciple.size() == 1) {  //只选择共债分案
            return 2;
        } else if (divisionalPrinciple.contains(1) && divisionalPrinciple.size() == 1) {  //只选择交叉分案
            return 3;
        } else if (divisionalPrinciple.contains(1) && divisionalPrinciple.contains(0) && divisionalPrinciple.size() == 2) {  //同时选择共债与交叉
            return 4;
        }
        return 0;
    }

    /**
     * 处理共债信息
     *
     * @param divisionalConditions
     * @return
     */
    public Map<String, Object> coDebtData(DivisionalConditions divisionalConditions) {
        TimeInterval timer = DateUtil.timer();
        timer.start("1");

        ExportReminder exportReminder = divisionalConditions.getExportReminder();
        exportReminder.setDecryptKey(FieldEncryptUtil.fieldKey);
        if (ObjectUtils.isEmpty(exportReminder.getCondition())) {
            throw new GlobalException("查询参数错误");
        }
        if (exportReminder.getCondition()) {
            exportReminder.setIds(null);
        }
        Map map = new HashMap();
        List<JointDebtDivision> arrayList = new ArrayList<>();  //共债信息集合
        List<CaseManage> caseManage1 = new ArrayList<>();  //非共债信息集合

        exportReminder.setClientIdcardes(null);
        exportReminder.setClientId(null);
        exportReminder.setCreateId(TokenInformation.getCreateid());

        List<String> list = ruleDivisionService.selectCaseClientIdcard(exportReminder);  //共债人身份证号信息集合
        List<String> idcardOne = ruleDivisionService.selectCaseClientIdcardOne(exportReminder);//非共债人的身份证号信息

        log.info("共债-处理汇总信息 耗时: " + timer.intervalMs("1"));

        timer.start("2");
        if (!ObjectUtils.isEmpty(idcardOne)) {
            exportReminder.setClientIdEns(idcardOne);
            exportReminder.setCreateId(TokenInformation.getCreateid());
            if (divisionalConditions.getDivisionScope().equals(0)) {
                List<String> lists = new ArrayList<>();
                lists.add("0");
                lists.add("1");
                lists.add("3");
                exportReminder.setCaseStates(lists);
            } else if (divisionalConditions.getDivisionScope().equals(1)) {
                List<String> lists = new ArrayList<>();
                lists.add("0");
                exportReminder.setCaseStates(lists);
            }
            //caseManage1 = ruleDivisionService.selectCaseManageClientIdcardOne(exportReminder);
            caseManage1 = groupListIdcardOne(idcardOne, 800, exportReminder);
            System.err.println("非公债案件总数：" + caseManage1.size());
        }

        log.info("共债-非共债信息查询 耗时: " + timer.intervalMs("2"));
        //TODO

        if (!ObjectUtils.isEmpty(list)) {

            int idleWorkTask = 2;
            List<Thread> tasks = new ArrayList<>();
            System.err.println("总数：" + list.size());

            System.err.println(list.size());
            CoDebtDataTask sonWorkTask = new CoDebtDataTask(exportReminder, list, arrayList);
            sonWorkTask.start();
            tasks.add(sonWorkTask);

            // 线程状态监控【如果全部线程都执行完成 或者 未全部执行完成但总耗时超过了最大等待时间，就结束等待】-----
            for (Thread service : tasks) {
                Thread.State state = service.getState();
                while (state != Thread.State.TERMINATED) {
                    state = service.getState();
                    //System.out.println("当前线程状态：" + state);
                    try {
                        Thread.sleep(200);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        if (!ObjectUtils.isEmpty(arrayList)) {
            if (divisionalConditions.getDivisionalMode() == 0 || divisionalConditions.getDivisionalMode() == 1) {
//                降序
                arrayList.sort(Comparator.comparing(JointDebtDivision::getNumber).reversed());
            } else if (divisionalConditions.getDivisionalMode() == 2 || divisionalConditions.getDivisionalMode() == 3) {
                arrayList.sort(Comparator.comparing(JointDebtDivision::getMoney).reversed());
            }
        }
        log.info("案件数量" + caseManage1.size());
        map.put("arrayList", arrayList);
        map.put("caseManage1", caseManage1);
        return map;
    }


    /**
     * 分片查询 非共债案件
     *
     * @param list
     * @param size
     * @param exportReminder
     * @return
     */
    public List<CaseManage> groupListIdcardOne(List<String> list, int size, ExportReminder exportReminder) {
        List<CaseManage> gongzhaiCaseList = new ArrayList<>();
        int listSize = list.size();
        int fetchSize = size;
        int keyToken = 0;
        for (int i = 0; i < list.size(); i += fetchSize) {
            if (i + fetchSize > listSize) { //作用为fetchSize最后没有100条数据则剩余几条newList中就装几条
                fetchSize = listSize - i;
            }
            List newList = list.subList(i, i + fetchSize);
            System.out.println("查询身份证数量：" + newList.size());
            keyToken++;
            exportReminder.setClientIdcardes(null);
            exportReminder.setClientIdcardsEns(null);
            exportReminder.setClientId(null);
            exportReminder.setClientIdEns(newList);
            exportReminder.setCreateId(TokenInformation.getCreateid());
            exportReminder.setClientCensusRegistersEns(null);
            List<CaseManage> caseManages = ruleDivisionService.selectCaseManageClientIdcardOne(exportReminder);  //共债案件信息
            gongzhaiCaseList.addAll(caseManages);
        }
        return gongzhaiCaseList;
    }


    /**
     * 不选择共债和交叉分案的处理
     *
     * @param divisionalConditions
     * @param caseManageList
     * @return
     */
    public List<DivisionPersonnel> generalTreatment(DivisionalConditions divisionalConditions, List<CaseManage> caseManageList) {
        Integer divisionalMode = divisionalConditions.getDivisionalMode();  //分配模式
        List<DivisionPersonnel> personnelInformation1 = divisionalConditions.getPersonnelInformation();  //人员信息集合

        Iterator<CaseManage> iterator = caseManageList.iterator();  //迭代器

        for (DivisionPersonnel personnel : personnelInformation1) {    //遍历人员信息
//            List<CaseManage> arrayList = new ArrayList<>();  //分案成功的案件集合

            List<CaseManage> list = personnel.getList();   //分配给员工的案件集合

            int size = personnel.getList().size();  //已经分配的案件数量
            BigDecimal money = personnel.getMoney();  //已经分配的案件总金额
            while (iterator.hasNext()) {
                if (divisionalMode == 0 || divisionalMode == 1) {   //按照案件数量分配
                    if (BigDecimal.valueOf(size).compareTo(personnel.getNumericalValue()) == 0) {
//                       当案件分配给该催收员的数量大于设置的数量，则跳出循环，进行下一个人
                        break;
                    }
                    CaseManage next = iterator.next();
                    size = size + 1;
                    money = money.add(next.getClientMoney());
//                    personnel.getList().add(next);
//                    arrayList.add(next);
                    list.add(next);
                    iterator.remove();
                }

                if (divisionalMode == 2 || divisionalMode == 3) {   //按照案件金额分配
                    if (money.compareTo(personnel.getNumericalValue()) == 0 || money.compareTo(personnel.getNumericalValue()) == 1) {
//                       当案件分配给该催收员的数量大于设置的数量，则跳出循环，进行下一个人
                        break;
                    }
                    CaseManage next = iterator.next();
                    size = size + 1;
                    money = money.add(next.getClientMoney());
//                    personnel.getList().add(next);
//                    arrayList.add(next);
                    list.add(next);
                    iterator.remove();
                }
            }
            personnel.setQuantity(BigDecimal.valueOf(size));  //分案数量
            personnel.setMoney(money);  //分案金额
            personnel.setList(list);
//            caseManageList.removeAll(arrayList);

//            for (CaseManage caseManage : caseManageList) {
//
//                if (divisionalMode == 0 || divisionalMode == 1) {   //按照案件数量分配
//                    if (BigDecimal.valueOf(size).compareTo(personnel.getNumericalValue()) == 0) {
////                       当案件分配给该催收员的数量大于设置的数量，则跳出循环，进行下一个人
//                        break;
//                    }
//                    size = size + 1;
//                    money = money.add(caseManage.getClientMoney());
//                    personnel.getList().add(caseManage);
//                    arrayList.add(caseManage);
//                    list.add(caseManage);
//                }
//
//                if (divisionalMode == 2 || divisionalMode == 3) {   //按照案件金额分配
//                    if (money.compareTo(personnel.getNumericalValue()) == 0 || money.compareTo(personnel.getNumericalValue()) == 1) {
////                       当案件分配给该催收员的数量大于设置的数量，则跳出循环，进行下一个人
//                        break;
//                    }
//                    size = size + 1;
//                    money = money.add(caseManage.getClientMoney());
//                    personnel.getList().add(caseManage);
//                    arrayList.add(caseManage);
//                    list.add(caseManage);
//                }
//            }
//            personnel.setQuantity(BigDecimal.valueOf(size));  //分案数量
//            personnel.setMoney(money);  //分案金额
//            personnel.setList(list);
//            caseManageList.removeAll(arrayList);

        }
        return personnelInformation1;
    }

    /**
     * 不选择共债和交叉分案的处理
     * 广度优先
     *
     * @param divisionalConditions
     * @param assignableCases
     * @param scheduleVo
     * @return
     */
    public List<DivisionPersonnel> generalTreatmentBreadth(DivisionalConditions divisionalConditions, List<CaseManage> assignableCases, ScheduleVo scheduleVo) {
        List<DivisionPersonnel> personnelInfos = divisionalConditions.getPersonnelInformation();  //人员信息集合

        for (int i = 0; i < personnelInfos.size(); i++) {
            personnelInfos.get(i).setSort(i);
        }

        log.info("处理分案,进度编号：{}， 总案件量：{}", scheduleVo.getScheduleNo(), assignableCases.size());


        TimeInterval timer = DateUtil.timer();
        timer.start("1");

        //团队按照需要分案案件数量从大到小排序
        int mode = divisionalConditions.getDivisionalMode();  //分配模式
        //是否共债优先
        boolean jointDet = divisionalConditions.getDivisionalPrinciple().contains(0);
        //是否隔月分案
        boolean intervalMonth = divisionalConditions.getDivisionalPrinciple().contains(1);
        //已经满足添加的人员
        List<DivisionPersonnel> reachedPersonnels = new ArrayList<>();
        for (DivisionPersonnel team : personnelInfos) {
            team.setMoney(BigDecimal.ZERO);
            team.setQuantity(BigDecimal.ZERO);
            if (jointDet) {
                team.setTotalJointDebNum(0);
            }
        }

        scheduleVo.setSchedule(16);
        scheduleService.setSchedule(scheduleVo);

        //唯一
        Set<String> uniqueIds = new HashSet<>();
        //重复，有共债的案件
        Set<String> duplicateIds = new HashSet<>();

        //案件的案件数
        Map<String, Integer> idCountMap = new HashMap<>();
        //共债案件金额
        Map<String, BigDecimal> idSumMap = new HashMap<>();
        Map<String, JointDebtDivision> jointDebtMap = new HashMap<>();

        if (jointDet) {
            //共债优先的，先找出共债 的身份证号
            timer.start("2-1");
            for (CaseManage temp : assignableCases) {
                String idcard = temp.getClientIdcard();
                if (!uniqueIds.add(idcard)) {
                    duplicateIds.add(idcard);
                    //删除第一次存入uniqueIds  Set中的数据
                    uniqueIds.remove(idcard);
                }
                int num = idCountMap.getOrDefault(idcard, 0) + 1;
                idCountMap.put(idcard, num);
                BigDecimal amount = idSumMap.getOrDefault(idcard, BigDecimal.ZERO).add(temp.getClientMoney());
                idSumMap.put(idcard, amount);

                JointDebtDivision jointDebtDivision = new JointDebtDivision();
                jointDebtDivision.setIdentityNumber(idcard);
                //jointDebtDivision.setList(caseManages);
                jointDebtDivision.setMoney(amount);
                jointDebtDivision.setNumber(num);
                jointDebtMap.put(idcard, jointDebtDivision);
            }
            scheduleVo.setSchedule(18);
            scheduleService.setSchedule(scheduleVo);


            List<JointDebtDivision> jointDebts = new ArrayList<>();
            for (String idcard : jointDebtMap.keySet()) {
                JointDebtDivision jointDebt = jointDebtMap.get(idcard);
                if (jointDebt.getNumber() > 1) {
                    jointDebts.add(jointDebt);
                }
            }
            scheduleVo.setSchedule(20);
            scheduleService.setSchedule(scheduleVo);

            // 使用Java 8 Stream API对objList进行金额倒序排序
            List<JointDebtDivision> sortedList = jointDebts.stream()
                    .sorted(Comparator.comparing(JointDebtDivision::getMoney).reversed())
                    .collect(Collectors.toList());
            scheduleVo.setSchedule(22);
            scheduleService.setSchedule(scheduleVo);

            long t21 = timer.intervalMs("2-1");
            log.info("执行分案-广度优先-共债 2-1 耗时:" + t21 + " ms");

            int inistSchedule = 25;
            int initSize = jointDebts.size();

            timer.start("2-3");
            jointDebts = sortedList;
            int order = 0;//0-正序，1-倒序
            int lastIndex = 0; //上一个团队index
            int index = 0;     //当前团队index
            while (jointDebts.size() > 0) {
                if (personnelInfos.size() == 0) {
                    break;
                }
                //更新进度，无关业务
                double s = 1 - (double) jointDebts.size() / initSize;
                int si = inistSchedule + (int) (s * 25);
                if (si > 50) {
                    si = 50;
                }
                scheduleVo.setSchedule(si);
                scheduleService.setSchedule(scheduleVo);
                //更新进度，无关业务


                if (index > personnelInfos.size() - 1) {
                    index = personnelInfos.size() - 1;
                }
                DivisionPersonnel team = personnelInfos.get(index);
                int notNum = 0;


                Iterator<JointDebtDivision> iterator = jointDebts.iterator();
                while (iterator.hasNext()) {
                    JointDebtDivision jointDebt = iterator.next();

                    List<CaseManage> tempManages = assignableCases.stream().filter(e -> StrUtil.equals(e.getClientIdcard(), jointDebt.getIdentityNumber())).collect(Collectors.toList());
                    assignableCases.removeAll(tempManages);

                    List<CaseManage> manages = team.getList();
                    manages.addAll(tempManages);
                    team.setList(manages);
                    for (CaseManage tempManage : tempManages) {
                        team.setMoney(team.getMoney().add(tempManage.getClientMoney()));
                    }
                    team.setQuantity(new BigDecimal(manages.size()));
                    if (tempManages.size() > 1) {
                        //案件数大于1 的才算共债一个
                        int totalJoinCount = team.getTotalJointDebNum() == null ? 0 : team.getTotalJointDebNum();
                        team.setTotalJointDebNum(totalJoinCount + 1);

                    }
                    log.info("共债：{},案件量：{},金额：{}， --分给：{}", jointDebt.getIdentityNumber(), jointDebt.getNumber(), jointDebt.getNumber(), team.getName());
                    iterator.remove();
                    break;
                }


                //判断是否满足团队要求
                boolean isReached = checkIsReached(mode, team);
                if (isReached || notNum == assignableCases.size()) {
                    reachedPersonnels.add(team);
                    personnelInfos.remove(team);
                    log.info("移除：" + team.getName());
                    if (order == 0) {
                        if (index > personnelInfos.size() - 1) {
                            index--;
                            order = 1;//改变顺序
                        }
                    } else {
                        if (index == 0) {
                            order = 0;//改变顺序
                        }
                    }
                } else {
                    if (personnelInfos.size() > 1) {
                        if (index == 0) {
                            if (order == 1) {
                                order = 0;
                                lastIndex = -1;
                                index = -1;
                            }
                        }
                        if (index == personnelInfos.size() - 1) {
                            if (order == 0) {
                                order = 1;
                                lastIndex = personnelInfos.size();
                                index = personnelInfos.size();
                            }
                        }
                        lastIndex = index;
                        if (order == 0) {
                            index++;
                        } else {
                            index--;
                        }
                    }
                }
            }
            long t23 = timer.intervalMs("2-3");
            log.info("执行分案-广度优先-共债 2-3 耗时:" + t23 + " ms");

        }

        scheduleVo.setSchedule(50);
        scheduleService.setSchedule(scheduleVo);

        long t2 = timer.intervalMs("2");
        log.info("执行分案-广度优先-共债 耗时:" + t2 + " ms");


        timer.start("3");
        //全部团队 都无法 符合隔月分案 要求  的案件
        List<Long> allTeamLastMonthCaseIds = new ArrayList<>();
        Map<Integer, List<Long>> lastMonthCaseMap = new HashMap<>();
        if (intervalMonth) {
            List<List<Long>> lists = new ArrayList<>();
            for (DivisionPersonnel temp : personnelInfos) {
                //开启隔月分案，查询一下团队中不能隔月分案的案件
                List<Long> lastMonthCaseIds = selectTeamIntervalMonth(temp.getId());
                temp.setLastMonthCaseIds(lastMonthCaseIds);
                lists.add(lastMonthCaseIds);
                lastMonthCaseMap.put(temp.getId(), lastMonthCaseIds);
            }

            if (lists.size() == 1) {
                allTeamLastMonthCaseIds = lists.get(0);
            } else {
                // 使用Java 8 Stream API对多个列表执行交集操作
                allTeamLastMonthCaseIds = lists.stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                        .entrySet()
                        .stream()
                        .filter(entry -> entry.getValue() == lists.size())
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());
            }

        }
        long t3 = timer.intervalMs("3");
        log.info("执行分案-广度优先-隔月 耗时:" + t3 + " ms");

        scheduleVo.setSchedule(60);
        scheduleService.setSchedule(scheduleVo);

        //把案件根据金额排序
        // 使用Java 8 Stream API对objList进行金额倒序排序
        List<CaseManage> sortedList = assignableCases.stream()
                .sorted(Comparator.comparing(CaseManage::getClientMoney).reversed())
                .collect(Collectors.toList());
        assignableCases = sortedList;


        int inistSchedule = 60;
        int initSize = assignableCases.size();

        int order = 0;//0-正序，1-倒序
        int lastIndex = 0; //上一个团队index
        int index = 0;     //当前团队index
        while (assignableCases.size() > 0) {
            if (personnelInfos.size() == 0) {
                break;
            }
            if (index > personnelInfos.size() - 1) {
                index = personnelInfos.size() - 1;
            }

            //更新进度，无关业务
            double s = 1 - (double) assignableCases.size() / initSize;
            int si = inistSchedule + (int) (s * 30);
            if (si > 90) {
                si = 90;
            }
            scheduleVo.setSchedule(si);
            scheduleService.setSchedule(scheduleVo);
            //更新进度，无关业务


            DivisionPersonnel team = personnelInfos.get(index);

            int notNum = 0;

            //System.out.println("" + team.getTeamName());
            Iterator<CaseManage> iterator = assignableCases.iterator();
            while (iterator.hasNext()) {
                CaseManage manage = iterator.next();
                if (intervalMonth) {
                    if (allTeamLastMonthCaseIds.contains(manage.getCaseId())) {
                        //该案件所有团队都不符合隔月分案要求
                        log.info("案件：{},金额：{}--所有团队都不满足，跳过", manage.getCaseId(), manage.getClientMoney());
                        iterator.remove();
                        continue;
                    }

                    if (team.getLastMonthCaseIds().contains(manage.getCaseId())) {
                        //该案件不满足 当前团队隔月分案要求
                        log.info(manage.getCaseId() + "不满足隔月分案：" + team.getName());
                        log.info("案件：{},金额：{}--不满足催员：{}， 的隔月分案，跳过", manage.getCaseId(), manage.getClientMoney(), team.getName());
                        List<CaseManage> manages = team.getList();
                        team.setQuantity(new BigDecimal(manages.size()));
                        notNum++;
                        if (notNum == assignableCases.size()) {
                            //全部案件都不满足当前团队，移除当前团队
                            System.out.println("所有案件都不满足：" + team.getName());
                            break;
                        }
                        continue;
                    }
                }
                log.info("案件：{},金额：{}--分给：{}  (排序：{}，下标：{})", manage.getCaseId(), manage.getClientMoney(), team.getName(),
                        order, index);
                List<CaseManage> manages = team.getList();
                manages.add(manage);
                team.setList(manages);
                team.setMoney(team.getMoney().add(manage.getClientMoney()));
                team.setQuantity(new BigDecimal(manages.size()));
                iterator.remove();
                break;
            }
            //判断是否满足团队要求
            boolean isReached = checkIsReached(mode, team);
            //boolean isReached =false;
            if (isReached || notNum == assignableCases.size()) {
                reachedPersonnels.add(team);
                personnelInfos.remove(team);
                log.info("满足条件，移除：{}", team.getName());
                //System.out.println("移除：" + team.getName());
                /*if (order==0){
                    index--;
                    lastIndex--;
                }else {
                    index--;
                }*/
                if (order == 0) {
                    if (index > personnelInfos.size() - 1) {
                        index--;
                        order = 1;//改变顺序
                    }
                } else {
                    if (index == 0 || index == personnelInfos.size() - 1) {
                        order = 0;//改变顺序
                    }
                }
            } else {
                if (personnelInfos.size() > 1) {
                    if (index == 0) {
                        if (order == 1) {
                            order = 0;
                            lastIndex = -1;
                            index = -1;
                        }
                    }
                    if (index == personnelInfos.size() - 1) {
                        if (order == 0) {
                            order = 1;
                            lastIndex = personnelInfos.size();
                            index = personnelInfos.size();
                        }
                    }
                    lastIndex = index;
                    if (order == 0) {
                        index++;
                    } else {
                        index--;
                    }
                }
            }
        }


        scheduleVo.setSchedule(90);
        scheduleService.setSchedule(scheduleVo);

        personnelInfos.addAll(reachedPersonnels);

        List<DivisionPersonnel> teamsSort = personnelInfos.stream()
                .sorted(Comparator.comparing(DivisionPersonnel::getSort))
                .collect(Collectors.toList());

        for (DivisionPersonnel temp : teamsSort) {
            log.info("催员：{}，工号：{}， 案件量：{}，案件金额：{}", temp.getName(), temp.getNumber(), temp.getQuantity(), temp.getMoney());
        }

        long t = timer.intervalMs("1");
        log.info("执行分案-广度优先 耗时:" + t + " ms");

        scheduleVo.setSchedule(95);
        scheduleService.setSchedule(scheduleVo);

        return teamsSort;
    }


    /**
     * 判断是否满足目标
     *
     * @param mode      分配模式
     * @param personnel
     * @return
     */
    private boolean checkIsReached(int mode, DivisionPersonnel personnel) {
        //mode=0;//测试 可能需要删除
        // 0,1按照案件数量分配
        //2,3 按金额分配
        switch (mode) {
            case 0:
            case 1:
                if (personnel.getQuantity().compareTo(personnel.getNumericalValue()) >= 0) {
                    return true;
                }
                break;
            case 2:
            case 3:
                if (personnel.getMoney().compareTo(personnel.getNumericalValue()) >= 0) {
                    return true;
                }
                break;
            default:
                return false;
        }
        return false;
    }

    /**
     * 查询催员交叉分案的案件
     *
     * @param odvId
     */
    private List<Long> selectTeamIntervalMonth(Integer odvId) {
        QueryTimeInterval queryTimeInterval = new QueryTimeInterval();   //交叉分案查询案件是否符合条件实体类
        //queryTimeInterval.setIds(list);
        queryTimeInterval.setEmployeesId(odvId);
        queryTimeInterval.setMonthFirst(TimeUtils.getMonthBegin());
        //queryTimeInterval.setMonthEnd(TimeUtils.getMonthEnd());
        queryTimeInterval.setMonthEnd(new Date());
        List<Long> caseIds = ruleDivisionService.selectDistributionHistory(queryTimeInterval);//查询该员工的交叉案件信息
        return caseIds;
    }

    /**
     * 只处理共债分案
     *
     * @param arrayList
     * @param caseManage1
     * @param divisionalConditions
     * @return
     */
    public List<DivisionPersonnel> jointDebtDivision(List<JointDebtDivision> arrayList, List<CaseManage> caseManage1, DivisionalConditions divisionalConditions) {
        TimeInterval timer = DateUtil.timer();
        timer.start("1");

        List<Integer> divisionalPrinciple = divisionalConditions.getDivisionalPrinciple();
        Integer divisionalMode = divisionalConditions.getDivisionalMode();

        Iterator<JointDebtDivision> iterator = arrayList.iterator();  //迭代器
        Iterator<CaseManage> iterator1 = caseManage1.iterator();

        for (DivisionPersonnel personnel : divisionalConditions.getPersonnelInformation()) {    //遍历人员信息
            personnel.setList(new ArrayList<>());
//            List<JointDebtDivision> jointDebtDivisions = new ArrayList<>();  //已分配的共债案件

            List<CaseManage> list1 = personnel.getList();   //分配给员工的案件集合

            int size = personnel.getList().size();  //已经分配的案件数量
            BigDecimal money = personnel.getMoney();  //已经分配的案件总金额

//            if (!ObjectUtils.isEmpty(arrayList)) {
//                for (JointDebtDivision arrayLists : arrayList) {
//                    if (divisionalMode == 0 || divisionalMode == 1) {   //按照案件数量分配
//                        if (BigDecimal.valueOf(size).compareTo(personnel.getNumericalValue()) >= 0) {
//                            break;
//                        }
//                        jointDebtDivisions.add(arrayLists);
//
//                        int sizes = arrayLists.getList().size();   //所共债的案件数量
//                        size = size + sizes;
//                        BigDecimal moneys = arrayLists.getMoney();  //所共债的案件金额
//                        money = money.add(moneys);
//                        personnel.setMoney(personnel.getMoney().add(moneys));
//                        list1.addAll(arrayLists.getList());
//                    }
//
//                    if (divisionalMode == 2 || divisionalMode == 3) {   //按照案件金额分配
//                        if (money.compareTo(personnel.getNumericalValue()) == 0 || money.compareTo(personnel.getNumericalValue()) == 1) {
//                            break;
//                        }
//                        jointDebtDivisions.add(arrayLists);
//
//                        BigDecimal moneys = arrayLists.getMoney();  //所共债的案件金额
//                        money = money.add(moneys);
//                        int sizes = arrayLists.getList().size();   //所共债的案件数量
//                        size = size + sizes;
//                        personnel.setMoney(personnel.getMoney().add(moneys));
//                        list1.addAll(arrayLists.getList());
//                    }
//
//                }
//                arrayList.removeAll(jointDebtDivisions);
//            }
            while (iterator.hasNext()) {
                if (divisionalMode == 0 || divisionalMode == 1) {   //按照案件数量分配
                    if (BigDecimal.valueOf(size).compareTo(personnel.getNumericalValue()) >= 0) {
                        break;
                    }
                    JointDebtDivision next = iterator.next();
                    int sizes = next.getList().size();   //所共债的案件数量
                    size = size + sizes;
                    BigDecimal moneys = next.getMoney();  //所共债的案件金额
                    money = money.add(moneys);
                    personnel.setMoney(personnel.getMoney().add(moneys));
                    list1.addAll(next.getList());
                    iterator.remove();
                } else if (divisionalMode == 2 || divisionalMode == 3) {   //按照案件金额分配
                    if (money.compareTo(personnel.getNumericalValue()) == 0 || money.compareTo(personnel.getNumericalValue()) == 1) {
                        break;
                    }
                    JointDebtDivision next = iterator.next();
                    BigDecimal moneys = next.getMoney();  //所共债的案件金额
                    money = money.add(moneys);
                    int sizes = next.getList().size();   //所共债的案件数量
                    size = size + sizes;
                    personnel.setMoney(personnel.getMoney().add(moneys));
                    list1.addAll(next.getList());
                    iterator.remove();
                }
            }

//                如果只选择共债案件模式，则将剩余案件继续分给员工
            if (divisionalPrinciple.contains(0) && divisionalPrinciple.size() == 1 && ObjectUtils.isEmpty(arrayList)) {
//                List<CaseManage> caseManages = new ArrayList<>();   //已分配案件信息
//                if (!ObjectUtils.isEmpty(caseManage1)) {
//
//                    for (CaseManage caseManage : caseManage1) {
//
//                        if (divisionalMode == 0 || divisionalMode == 1) {   //按照案件数量分配
//                            if (BigDecimal.valueOf(size).compareTo(personnel.getNumericalValue()) == 0 || BigDecimal.valueOf(size).compareTo(personnel.getNumericalValue()) == 1) {
//                                break;
//                            }
//                            size = size + 1;
//                            money = money.add(caseManage.getClientMoney());
//                            caseManages.add(caseManage);
//                            list1.add(caseManage);
//                        }
//
//                        if (divisionalMode == 2 || divisionalMode == 3) {   //按照案件金额分配
//                            if (money.compareTo(personnel.getNumericalValue()) == 0 || money.compareTo(personnel.getNumericalValue()) == 1) {
//                                break;
//                            }
//                            size = size + 1;
//                            money = money.add(caseManage.getClientMoney());
//                            caseManages.add(caseManage);
//                            list1.add(caseManage);
//                        }
//                    }
//                    caseManage1.removeAll(caseManages);
//                }
                while (iterator1.hasNext()) {
                    if (divisionalMode == 0 || divisionalMode == 1) {   //按照案件数量分配
                        if (BigDecimal.valueOf(size).compareTo(personnel.getNumericalValue()) == 0 || BigDecimal.valueOf(size).compareTo(personnel.getNumericalValue()) == 1) {
                            break;
                        }
                        CaseManage next = iterator1.next();
                        size = size + 1;
                        money = money.add(next.getClientMoney());
                        list1.add(next);
                        iterator1.remove();
                    } else if (divisionalMode == 2 || divisionalMode == 3) {   //按照案件金额分配
                        if (money.compareTo(personnel.getNumericalValue()) == 0 || money.compareTo(personnel.getNumericalValue()) == 1) {
                            break;
                        }
                        CaseManage next = iterator1.next();
                        size = size + 1;
                        money = money.add(next.getClientMoney());
                        list1.add(next);
                        iterator1.remove();
                    }
                }
            }
            personnel.setQuantity(BigDecimal.valueOf(size));   //分案数量
            personnel.setMoney(money);  //分案金额
            personnel.setList(list1);
        }

        long t = timer.intervalMs("1");
        log.info("只处理共债分案耗时:" + t + " ms");
        return divisionalConditions.getPersonnelInformation();
    }


    /**
     * 只处理共债分案
     * 广度优先
     *
     * @param arrayList            共债案件集合
     * @param caseManage1          非共债案件集合
     * @param divisionalConditions
     * @return
     */
    public List<DivisionPersonnel> jointDebtDivisionBreadth(List<JointDebtDivision> arrayList, List<CaseManage> caseManage1, DivisionalConditions divisionalConditions) {
        TimeInterval timer = DateUtil.timer();
        timer.start("1");

        List<Integer> divisionalPrinciple = divisionalConditions.getDivisionalPrinciple();
        Integer divisionalMode = divisionalConditions.getDivisionalMode();

        List<DivisionPersonnel> personnelInfos = divisionalConditions.getPersonnelInformation();

        //Iterator<JointDebtDivision> iterator = arrayList.iterator();  //迭代器
        //Iterator<CaseManage> iterator1 = caseManage1.iterator();

        //共债案件排序
        List<JointDebtDivision> sortedJointList = arrayList.stream()
                .sorted(Comparator.comparing(JointDebtDivision::getMoney).reversed())
                .collect(Collectors.toList());
        //非共债案件排序
        List<CaseManage> sortedList = caseManage1.stream()
                .sorted(Comparator.comparing(CaseManage::getClientMoney).reversed())
                .collect(Collectors.toList());

        //可分配的共债
        List<JointDebtDivision> assignableJoints = sortedJointList;
        //可分配案件
        List<CaseManage> assignableCases = sortedList;

        //已经满足添加的人员
        List<DivisionPersonnel> reachedPersonnels = new ArrayList<>();

        int order = 0;      //0-正序，1-倒序
        int lastIndex = 0; //上一个催员index
        int index = 0;     //当前催员index
        while (assignableCases.size() > 0) {
            if (personnelInfos.size() == 0) {
                break;
            }
            if (index > personnelInfos.size() - 1) {
                index = personnelInfos.size() - 1;
            }
            DivisionPersonnel personnel = personnelInfos.get(index);
            System.out.println("催员:" + personnel.getName());
            int notNum = 0;

            //是否进入了共债案件
            boolean isJoint = false;
            Iterator<JointDebtDivision> iteratorJoint = assignableJoints.iterator();
            while (iteratorJoint.hasNext()) {
                JointDebtDivision joint = iteratorJoint.next();
                List<CaseManage> manages = personnel.getList();
                manages.addAll(joint.getList());
                personnel.setList(manages);
                personnel.setMoney(personnel.getMoney().add(joint.getMoney()));
                personnel.setQuantity(BigDecimal.valueOf(manages.size()));
                iteratorJoint.remove();
                isJoint = true;
                break;
            }

            //如果没进共债，则进非共债
            if (!isJoint) {
                Iterator<CaseManage> iterator = assignableCases.iterator();
                while (iterator.hasNext()) {
                    CaseManage manage = iterator.next();
                    List<CaseManage> manages = personnel.getList();
                    manages.add(manage);
                    personnel.setList(manages);
                    personnel.setMoney(personnel.getMoney().add(manage.getClientMoney()));
                    personnel.setQuantity(BigDecimal.valueOf(manages.size()));
                    iterator.remove();
                    break;
                }
            }

            //判断是否满足团队要求
            boolean isReached = checkIsReached(divisionalMode, personnel);
            //boolean isReached =false;
            if (isReached || notNum == assignableCases.size()) {
                reachedPersonnels.add(personnel);
                personnelInfos.remove(personnel);
                log.info("移除：" + personnel.getName());
                if (order == 0) {
                    if (index > personnelInfos.size() - 1) {
                        index--;
                    }
                }
            } else {
                if (personnelInfos.size() > 1) {
                    if (index == 0) {
                        if (order == 1) {
                            order = 0;
                            lastIndex = -1;
                            index = -1;
                        }
                    }
                    if (index == personnelInfos.size() - 1) {
                        if (order == 0) {
                            order = 1;
                            lastIndex = personnelInfos.size();
                            index = personnelInfos.size();
                        }
                    }
                    lastIndex = index;
                    if (order == 0) {
                        index++;
                    } else {
                        index--;
                    }
                }
            }
        }


        personnelInfos.addAll(reachedPersonnels);


        long t = timer.intervalMs("1");
        log.info("只处理共债分案耗时:" + t + " ms");
        return personnelInfos;
    }


    /**
     * 只存在交叉分案
     *
     * @param divisionalConditions
     * @param caseManageList
     * @return
     */
    public List<DivisionPersonnel> crossDivision(DivisionalConditions divisionalConditions, List<CaseManage> caseManageList) {
        Integer divisionalMode = divisionalConditions.getDivisionalMode();  //分配模式

        List<Long> list = new ArrayList<>();
        for (CaseManage caseManage : caseManageList) {
            list.add(caseManage.getCaseId());
        }

        for (DivisionPersonnel personnel : divisionalConditions.getPersonnelInformation()) {    //遍历人员信息
            personnel.setList(new ArrayList<>());

            QueryTimeInterval queryTimeInterval = new QueryTimeInterval();   //交叉分案查询案件是否符合条件实体类
            Integer id = personnel.getId();   //员工id
            queryTimeInterval.setIds(list);
            queryTimeInterval.setEmployeesId(id);
            queryTimeInterval.setMonthFirst(TimeUtils.getMonthBegin());
//            queryTimeInterval.setMonthEnd(TimeUtils.getMonthEnd());
            queryTimeInterval.setMonthEnd(new Date());
            queryTimeInterval.setOperation("回收案件");

            List<Long> list1 = ruleDivisionService.selectDistributionHistory(queryTimeInterval);//查询该员工的交叉案件信息

            int size = personnel.getQuantity().intValue();  //已经分配的案件数量
            BigDecimal money = personnel.getMoney();  //已经分配的案件总金额

//            List<CaseManage> arrayList = new ArrayList<>();  //已经分配的案件集合

            List<CaseManage> list2 = personnel.getList();  //分配给员工的案件信息集合

            Iterator<CaseManage> iterator = caseManageList.iterator();  //迭代器
            while (iterator.hasNext()) {
                if (divisionalMode == 0 || divisionalMode == 1) {   //按照案件数量分配
                    if (BigDecimal.valueOf(size).compareTo(personnel.getNumericalValue()) == 0 || BigDecimal.valueOf(size).compareTo(personnel.getNumericalValue()) == 1) {
//                       当案件分配给该催收员的数量大于设置的数量，则删除该员工数据，加入新的集合中
//                        personnelInformation1.remove(personnel);
                        break;
                    }
                    CaseManage next = iterator.next();
                    if (list1.contains(next.getCaseId())) {
                        continue;
                    }
                    size = size + 1;
                    money = money.add(next.getClientMoney());
//                    personnel.getList().add(caseManage);
//                    arrayList.add(caseManage);
                    list2.add(next);
                    iterator.remove();
                }

                if (divisionalMode == 2 || divisionalMode == 3) {   //按照案件金额分配
                    if (money.compareTo(personnel.getNumericalValue()) == 0 || money.compareTo(personnel.getNumericalValue()) == 1) {
                        break;
                    }
                    CaseManage next = iterator.next();
                    if (list1.contains(next.getCaseId())) {
                        continue;
                    }
                    size = size + 1;
                    money = money.add(next.getClientMoney());
//                    personnel.getList().add(caseManage);
//                    arrayList.add(caseManage);
                    list2.add(next);
                    iterator.remove();
                }

            }
            personnel.setMoney(money);  //分案金额
            personnel.setQuantity(BigDecimal.valueOf(size)); //分案数量
            personnel.setList(list2);
        }

        if (!ObjectUtils.isEmpty(caseManageList)) {    //交叉分案结束，有未分配案件则全部分给第一个人
            Iterator<CaseManage> iterator = caseManageList.iterator();  //迭代器
            DivisionPersonnel divisionPersonnel = divisionalConditions.getPersonnelInformation().get(0);

            QueryTimeInterval queryTimeInterval = new QueryTimeInterval();   //交叉分案查询案件是否符合条件实体类
            Integer id = divisionPersonnel.getId();   //员工id
            queryTimeInterval.setIds(list);
            queryTimeInterval.setEmployeesId(id);
            queryTimeInterval.setMonthFirst(TimeUtils.getMonthBegin());
//            queryTimeInterval.setMonthEnd(TimeUtils.getMonthEnd());
            queryTimeInterval.setMonthEnd(new Date());
            queryTimeInterval.setOperation("回收案件");

            List<Long> list1 = ruleDivisionService.selectDistributionHistory(queryTimeInterval);//查询该员工的交叉案件信息

            while (iterator.hasNext()) {

                CaseManage next = iterator.next();
                if (list1.contains(next.getCaseId())) {
                    continue;
                }
                divisionPersonnel.getList().add(next);
                divisionPersonnel.setMoney(divisionPersonnel.getMoney().add(next.getClientMoney()));
                divisionPersonnel.setQuantity(divisionPersonnel.getQuantity().add(BigDecimal.ONE));
            }
        }
        return divisionalConditions.getPersonnelInformation();
    }

    /**
     * 指定分案查询数据以及可分配案件/案件总金额
     *
     * @param exportReminder
     * @return
     */
    public Map<String, Object> selectId(ExportReminder exportReminder) {
        exportReminder.setDecryptKey(FieldEncryptUtil.fieldKey);
        List<Long> list1 = caseService.selectCaseManageCaseIdList(exportReminder);  //案件id集合

        Map<String, Object> map = new HashMap<>();

//        未分配
        List<Integer> list = new ArrayList<>();
        list.add(0);
        exportReminder.setCaseStateList(list);
        Map<String, Object> map1 = caseService.selectCaseManageCount(exportReminder);
        Long quantity = (Long) map1.get("quantity");  //数量
        BigDecimal clientMoney = (BigDecimal) map1.get("clientMoney");  //金额总量
        if (ObjectUtils.isEmpty(clientMoney)) {
            clientMoney = BigDecimal.ZERO;
        }

//        已分配+留案
        List<Integer> arrayList = new ArrayList<>();
        arrayList.add(1);
        arrayList.add(3);
        exportReminder.setCaseStateList(arrayList);
        Map<String, Object> map2 = caseService.selectCaseManageCount(exportReminder);
        Long quantitys = (Long) map2.get("quantity");  //数量
        BigDecimal clientMoneys = (BigDecimal) map2.get("clientMoney");  //金额总量
        if (ObjectUtils.isEmpty(clientMoneys)) {
            clientMoneys = BigDecimal.ZERO;
        }

        map.put("weifenpei", quantity);
        map.put("yifenpei", quantitys);
        map.put("zongshu", quantity + quantitys);
        map.put("zongjine", clientMoney.add(clientMoneys));
        map.put("arrayList", list1);

        return map;
    }

    /**
     * 指定分案写入催收员（批量）
     *
     * @param specifyRules
     */
    public void updateCase(SpecifyRules specifyRules) {
        ThreadEntityPojo threadEntityPojo = new ThreadEntityPojo();
        threadEntityPojo.setCreateId(TokenInformation.getCreateid());
        threadEntityPojo.setUserId(TokenInformation.getUserid());
        threadEntityPojo.setUser(TokenInformation.getUsername());
        threadEntityPojo.setType(TokenInformation.getType());

        if (specifyRules.getTeamCaseUtils() == null) {
            specifyRules.setTeamCaseUtils(new TeamCaseUtils());
        }
        specifyRules.getTeamCaseUtils().setCreateId(TokenInformation.getCreateid());
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        AssignDivisionsThread assignDivisionsThread = new AssignDivisionsThread(specifyRules, threadEntityPojo);
        TaskManager.addTask(assignDivisionsThread);
    }

    /**
     * 指定分案异步
     *
     * @param specifyRules
     * @param scheduleVo   异步进度
     * @return
     */
    public void specifyDivisionalDataPreview(SpecifyRules specifyRules, ScheduleVo scheduleVo) {

        /*if (ObjectUtils.isEmpty(specifyRules.getList())) {
            throw new GlobalException("分配案件id不能为空");
        }
        if (specifyRules.getList().size() > 5000) throw new GlobalException("案加数量过大，请分批操作");*/

        scheduleVo.setSchedule(1);
        scheduleService.setSchedule(scheduleVo);
        if (specifyRules.getTeamCaseUtils() == null) {
            specifyRules.setTeamCaseUtils(new TeamCaseUtils());
        }
        specifyRules.getTeamCaseUtils().setCreateId(TokenInformation.getCreateid());
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        SpecifyDivisionalPreviewTask workTask = new SpecifyDivisionalPreviewTask(specifyRules, scheduleVo);
        TaskManager.addTask(workTask);
    }

    /**
     * 指定分案 返回数据
     *
     * @param specifyRules
     * @param scheduleVo
     * @return
     */
    public List<DataPreview> specifyDivisionalData(SpecifyRules specifyRules, ScheduleVo scheduleVo) {

        scheduleVo.setSchedule(10);
        scheduleService.setSchedule(scheduleVo);

        List<DataPreview> dataPreviews = new ArrayList<>();   //返回预览的数据
        List<Distribution> distributions = specifyRules.getDistributions();   //分案员工数据
        List<Long> list = specifyRules.getList();//案件id集合

        Integer outsourcingTeamId = specifyRules.getTeamCaseUtils().getCreateId() == null ? TokenInformation.getCreateid() :
                specifyRules.getTeamCaseUtils().getCreateId();
        List<CaseManage> assignableCases = caseService.selectCaseManageId(list, outsourcingTeamId,specifyRules.getDistributeType());


        List<Distribution> personnelInfos = specifyRules.getDistributions();  //人员信息集合
        for (int i = 0; i < personnelInfos.size(); i++) {
            personnelInfos.get(i).setSort(i);
        }
        log.info("指定分案,进度编号：{}， 总案件量：{}", scheduleVo.getScheduleNo(), assignableCases.size());

        TimeInterval timer = DateUtil.timer();
        timer.start("1");

        //已经满足添加的人员
        List<Distribution> reachedPersonnels = new ArrayList<>();

        for (Distribution team : personnelInfos) {
            team.setNumericalValue(new BigDecimal(team.getNumber()));
            team.setMoney(BigDecimal.ZERO);
            team.setNumber(0);
        }

        scheduleVo.setSchedule(20);
        scheduleService.setSchedule(scheduleVo);


        //把案件根据金额排序
        // 使用Java 8 Stream API对objList进行金额倒序排序
        List<CaseManage> sortedList = assignableCases.stream()
                .sorted(Comparator.comparing(CaseManage::getClientMoney).reversed())
                .collect(Collectors.toList());
        assignableCases = sortedList;

        scheduleVo.setSchedule(30);
        scheduleService.setSchedule(scheduleVo);

        int inistSchedule = 30;
        int initSize = assignableCases.size();

        int order = 0;//0-正序，1-倒序
        int lastIndex = 0; //上一个团队index
        int index = 0;     //当前团队index
        while (assignableCases.size() > 0) {
            if (personnelInfos.size() == 0) {
                break;
            }
            if (index > personnelInfos.size() - 1) {
                index = personnelInfos.size() - 1;
            }

            //更新进度，无关业务
            double s = 1 - (double) assignableCases.size() / initSize;
            int si = inistSchedule + (int) (s * 60);
            if (si > 90) {
                si = 90;
            }
            scheduleVo.setSchedule(si);
            scheduleService.setSchedule(scheduleVo);
            //更新进度，无关业务

            Distribution team = personnelInfos.get(index);
            int notNum = 0;
            Iterator<CaseManage> iterator = assignableCases.iterator();
            while (iterator.hasNext()) {
                CaseManage manage = iterator.next();
                List<CaseManage> manages = team.getList();
                manages.add(manage);
                team.setList(manages);
                team.setMoney(team.getMoney().add(manage.getClientMoney()));
                team.setNumber(manages.size());
                log.info("指定分案，案件：{}，金额：{}，分给--：{}。(排序：{}，下标:{})", manage.getCaseId(), manage.getClientMoney(), team.getOdvName(),
                        order, index);
                iterator.remove();
                break;
            }
            //判断是否满足团队要求
            boolean isReached = checkIsReachedDistri(0, team);
            //boolean isReached =false;
            if (isReached || notNum == assignableCases.size()) {
                reachedPersonnels.add(team);
                personnelInfos.remove(team);
                log.info("移除：" + team.getOdvName());
                if (order == 0) {
                    if (index > personnelInfos.size() - 1) {
                        index--;
                        order = 1;//改变顺序
                    }
                } else {
                    if (index == 0) {
                        order = 0;//改变顺序
                    }
                }
            } else {
                if (personnelInfos.size() > 1) {
                    if (index == 0) {
                        if (order == 1) {
                            order = 0;
                            lastIndex = -1;
                            index = -1;
                        }
                    }
                    if (index == personnelInfos.size() - 1) {
                        if (order == 0) {
                            order = 1;
                            lastIndex = personnelInfos.size();
                            index = personnelInfos.size();
                        }
                    }
                    lastIndex = index;
                    if (order == 0) {
                        index++;
                    } else {
                        index--;
                    }
                }
            }
        }

        scheduleVo.setSchedule(90);
        scheduleService.setSchedule(scheduleVo);

        personnelInfos.addAll(reachedPersonnels);

        List<Distribution> teamsSort = personnelInfos.stream()
                .sorted(Comparator.comparing(Distribution::getSort))
                .collect(Collectors.toList());

        for (Distribution temp : teamsSort) {
            log.info("催员：{}，工号：{}， 案件量：{}，案件金额：{}", temp.getOdvName(), temp.getJobNumber(), temp.getNumber(), temp.getMoney());
        }

        long t = timer.intervalMs("1");
        log.info("执行分案-广度优先 耗时:" + t + " ms");

        for (Distribution temp : teamsSort) {
            DataPreview dataPreview = new DataPreview();
            dataPreview.setMoney(temp.getMoney());
            List<Long> caseIds = new ArrayList<>();
            for (CaseManage caseManage : temp.getList()) {
                caseIds.add(caseManage.getCaseId());
            }
            dataPreview.setIds(caseIds);
            dataPreview.setNumber(temp.getNumber());
            dataPreview.setJobNumber(temp.getJobNumber());
            dataPreview.setOdvId(temp.getOdvId());
            dataPreview.setOdvName(temp.getOdvName());
            dataPreviews.add(dataPreview);
            log.info("催员：{},案件量：{}", dataPreview.getOdvName(), caseIds.size());
        }
        scheduleVo.setSchedule(95);
        scheduleService.setSchedule(scheduleVo);
        return dataPreviews;
    }


//---------------------------------------------------------------------------------------------------------------------

    /**
     * 停催（根据所传条件查询案件申请停催）
     *
     * @param exportReminder
     */
    public void caseStopUrging(ExportReminder exportReminder) {
        if (!ObjectUtils.isEmpty(exportReminder.getReason())) {
            if (exportReminder.getReason().length() > 300) {
                throw new GlobalException("申请原因限制300字,请重新输入");
            }
        }
        List<CaseManage> caseManages = caseService.selectCaseManages(exportReminder);
        if (ObjectUtils.isEmpty(caseManages)) {
            throw new GlobalException("申请停催案件不能为空");
        }
        for (CaseManage caseManage : caseManages){
            caseManage.setStopEndTime(exportReminder.getStopEndTime());
            caseManage.setReason(exportReminder.getReason());
            caseManage.setPermanentlyStop(exportReminder.getPermanentlyStop());
        }
        exportReminder.setLoginUser(SecurityUtils.getLoginUser());

        ThreadEntityPojo threadEntityPojo = new ThreadEntityPojo();
        threadEntityPojo.setCreateId(TokenInformation.getCreateid());
        threadEntityPojo.setUserId(TokenInformation.getUserid());
        threadEntityPojo.setUser(TokenInformation.getUsername());
        threadEntityPojo.setType(TokenInformation.getType());

        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        CaseStopUrgingThread caseStopUrgingThread = new CaseStopUrgingThread(exportReminder, threadEntityPojo, caseManages);
        TaskManager.addTask(caseStopUrgingThread);
    }

    /**
     * 留案（根据所传条件查询案件申请留案）
     *
     * @param exportReminder
     */
    public void caseKeepCase(ExportReminder exportReminder) {
        if (!ObjectUtils.isEmpty(exportReminder.getReason())) {
            if (exportReminder.getReason().length() > 300) {
                throw new GlobalException("申请原因限制300字,请重新输入");
            }
        }
        List<CaseManage> caseManages = caseService.selectCaseManages(exportReminder);
        if (ObjectUtils.isEmpty(caseManages)) {
            throw new GlobalException("申请留案案件不能为空");
        }
        exportReminder.setLoginUser(SecurityUtils.getLoginUser());

        ThreadEntityPojo threadEntityPojo = new ThreadEntityPojo();
        threadEntityPojo.setCreateId(TokenInformation.getCreateid());
        threadEntityPojo.setUserId(TokenInformation.getUserid());
        threadEntityPojo.setUser(TokenInformation.getUsername());
        threadEntityPojo.setType(TokenInformation.getType());

        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        CaseKeepCaseThread caseKeepCaseThread = new CaseKeepCaseThread(exportReminder, threadEntityPojo, caseManages);
        TaskManager.addTask(caseKeepCaseThread);
    }

    /**
     * 退案（根据所传条件查询案件申请退案）
     *
     * @param exportReminder
     */
    public void caseWithdrawal(ExportReminder exportReminder) {
        if (!ObjectUtils.isEmpty(exportReminder.getReason())) {
            if (exportReminder.getReason().length() > 300) {
                throw new GlobalException("申请原因限制300字,请重新输入");
            }
        }

        List<CaseManage> caseManages = caseService.selectCaseManages(exportReminder);
        if (ObjectUtils.isEmpty(caseManages)) {
            throw new GlobalException("申请退案案件不能为空");
        }

        ThreadEntityPojo threadEntityPojo = new ThreadEntityPojo();
        threadEntityPojo.setCreateId(TokenInformation.getCreateid());
        threadEntityPojo.setUserId(TokenInformation.getUserid());
        threadEntityPojo.setUser(TokenInformation.getUsername());
        threadEntityPojo.setType(TokenInformation.getType());

        exportReminder.setLoginUser(SecurityUtils.getLoginUser());
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();

        CaseWithdrawalThread caseWithdrawalThread = new CaseWithdrawalThread(exportReminder, threadEntityPojo, caseManages);
        TaskManager.addTask(caseWithdrawalThread);
    }

//---------------------------------------------------------------------------------------------------------------------

    /**
     * excel表格导入分案信息验证
     *
     * @param vos
     * @param loginUser 登录人信息
     * @return
     */
    public List<Map<String, Object>> insertCaseManage(List<TemplateAllocationExcelVo> vos, LoginUser loginUser) {

        List<Map<String, Object>> mapList = new ArrayList<>();
        List<CaseManage> list = new ArrayList<>();
        for (int i = 0; i < vos.size(); i++) {
            TemplateAllocationExcelVo vo = vos.get(i);
            try {
                CaseManage caseManage = caseService.yanzheng(vo, loginUser);
                list.add(caseManage);
            } catch (Exception e) {
                Map<String, Object> maps = new LinkedHashMap<>();
                maps.put("案件ID", vo.getCaseId());
                //maps.put("客户姓名", caseManage.getClientName());
                //maps.put("身份证号", caseManage.getClientIdcard());
                //maps.put("员工工号", caseManage.getEmployeesWorking());
                maps.put("姓名", vo.getClientName());
                maps.put("员工登录账号", vo.getEmployeesLoginAccount());
                maps.put("错误信息", e.getMessage());
                mapList.add(maps);
            }
        }
//         将正确的分案信息修改数据库
        if (!ObjectUtils.isEmpty(list)) {
            caseService.updateCaseTemplate(list, loginUser);
        }
        return mapList;
    }

    /**
     * 表头工具方法--模板分案
     *
     * @return
     */
    public List<String> excelHeader() {
        List<String> list = new ArrayList<>();
        list.add("案件ID");
        //list.add("借款人");
        list.add("姓名");
        //list.add("证件号码");
        //list.add("证件类型");
        //list.add("员工工号");
        list.add("员工登录账号");
        return list;
    }

    /**
     * excel表格导入案件批量操作信息（批量申请停催）
     *
     * @param excelBatches
     * @return
     */
    public Map<String, Object> insertExcelBatch(List<ExcelBatch> excelBatches) {
//        错误信息
        List<ErrorBatch> list = new ArrayList<>();
//        正确信息
        List<ExcelBatch> arrayList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        if (ObjectUtils.isEmpty(excelBatches)) {
            throw new GlobalException("文件内容不能为空");
        }
        for (int i = 0; i < excelBatches.size(); i++) {
            try {
                ExcelBatch excelBatch = excelBatches.get(i);
                String entrustingPartyName = excelBatch.getEntrustingPartyName();
                Owner owner = caseService.selectOwner(entrustingPartyName);
                Long id = owner.getId();
                excelBatch.setId(id);
                String clientIdcard = FieldEncryptUtil.encrypt(excelBatch.getClientIdcard());
                String clientName = FieldEncryptUtil.encrypt(excelBatch.getClientName());
                caseService.selectCaseId(id, clientIdcard, clientName);
                arrayList.add(excelBatch);
            } catch (Exception e) {
                e.printStackTrace();
                ExcelBatch excelBatch = excelBatches.get(i);
                ErrorBatch errorBatch = new ErrorBatch();
//                复制相同字段信息
                BeanUtils.copyProperties(excelBatch, errorBatch);
                errorBatch.setError(e.getMessage());
                list.add(errorBatch);
            }
        }
        if (!ObjectUtils.isEmpty(arrayList)) {
//        设置随机key
            String s = UUID.randomUUID().toString();
            String sb = s + "StopUrging";
//        设置超时时间
            redisService.expire(sb, 1, TimeUnit.HOURS);
//        将正确信息存入redis中
            redisService.setCacheList(sb, arrayList);
            map.put("key", sb);
        }
        map.put("list", list);
        map.put("arrayList", arrayList);
        return map;
    }

    /**
     * excel表格导入案件批量操作信息（批量申请留案）
     *
     * @param excelBatches
     * @return
     */
    public Map<String, Object> insertKeepCase(List<ExcelBatch> excelBatches) {
//        错误信息
        List<ErrorBatch> list = new ArrayList<>();
//        正确信息
        List<ExcelBatch> arrayList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < excelBatches.size(); i++) {
            try {
                ExcelBatch excelBatch = excelBatches.get(i);
                String entrustingPartyName = excelBatch.getEntrustingPartyName();
                Owner owner = caseService.selectOwner(entrustingPartyName);
                Long id = owner.getId();
                excelBatch.setId(id);
                String clientIdcard = FieldEncryptUtil.encrypt(excelBatch.getClientIdcard());
                String clientName = FieldEncryptUtil.encrypt(excelBatch.getClientName());
                caseService.selectName(id, clientIdcard, clientName);
                arrayList.add(excelBatch);
            } catch (Exception e) {
                e.printStackTrace();
                ExcelBatch excelBatch = excelBatches.get(i);
                ErrorBatch errorBatch = new ErrorBatch();
//                复制相同字段信息
                BeanUtils.copyProperties(excelBatch, errorBatch);
                errorBatch.setError(e.getMessage());
                list.add(errorBatch);
            }
        }
        if (!ObjectUtils.isEmpty(arrayList)) {
//        设置随机key
            String s = UUID.randomUUID().toString();
            String sb = s + "KeepCase";
//        设置超时时间
            redisService.expire(sb, 1, TimeUnit.HOURS);
//        将正确信息存入redis中
            redisService.setCacheList(sb, arrayList);
            map.put("key", sb);
        }
        map.put("list", list);
        map.put("arrayList", arrayList);
        return map;
    }

    /**
     * excel表格导入案件批量操作信息（批量申请退案）
     *
     * @param excelBatches
     * @return
     */
    public Map<String, Object> insertWithdrawal(List<ExcelBatch> excelBatches) {
//        错误信息
        List<ErrorBatch> list = new ArrayList<>();
//        正确信息
        List<ExcelBatch> arrayList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < excelBatches.size(); i++) {
            try {
                ExcelBatch excelBatch = excelBatches.get(i);
                if (ObjectUtils.isEmpty(excelBatch.getClientIdcard())) {
                    throw new GlobalException("客户身份证号不能为空");
                }
                if (ObjectUtils.isEmpty(excelBatch.getClientName())) {
                    throw new GlobalException("客户名字不能为空");
                }
                if (ObjectUtils.isEmpty(excelBatch.getEntrustingPartyName())) {
                    throw new GlobalException("资产方名称不能为空");
                }
                String entrustingPartyName = excelBatch.getEntrustingPartyName();
                Owner owner = caseService.selectOwner(entrustingPartyName);
                Long id = owner.getId();
                excelBatch.setId(id);
                String clientIdcard = FieldEncryptUtil.encrypt(excelBatch.getClientIdcard());
                String clientName = FieldEncryptUtil.encrypt(excelBatch.getClientName());
                caseService.selectId(id, clientIdcard, clientName);
                arrayList.add(excelBatch);
            } catch (Exception e) {
                e.printStackTrace();
                ExcelBatch excelBatch = excelBatches.get(i);
                ErrorBatch errorBatch = new ErrorBatch();
//                复制相同字段信息
                BeanUtils.copyProperties(excelBatch, errorBatch);
                errorBatch.setError(e.getMessage());
                list.add(errorBatch);
            }
        }
        if (!ObjectUtils.isEmpty(arrayList)) {
//        设置随机key
            String s = UUID.randomUUID().toString();
            String sb = s + "Withdrawal";
//        设置超时时间
            redisService.expire(sb, 1, TimeUnit.HOURS);
//        将正确信息存入redis中
            redisService.setCacheList(sb, arrayList);
            map.put("key", sb);
        }
        map.put("list", list);
        map.put("arrayList", arrayList);
        return map;
    }

    /**
     * 根据团队id以及案件id集合查询统计金额以及可分配案件数量
     *
     * @param exportReminder
     * @return
     */
    public Map<String, Object> selectStatistics(ExportReminder exportReminder) {
        List<CaseManage> caseManages = caseService.selectCaseManages(exportReminder);
        if (!ObjectUtils.isEmpty(caseManages)) {
            List<Long> arrayList = new ArrayList();
            int count1 = 0; //未分配案件数量
            int count2 = 0; //已分配案件数量
            BigDecimal count3 = BigDecimal.ZERO; //可分配案件总金额

            for (CaseManage caseManage : caseManages) {
//                未分配案件
                if (caseManage.getCaseState().equals("0")) {
                    count1++;
                    arrayList.add(caseManage.getCaseId());
                    count3 = count3.add(caseManage.getClientMoney());
                }
//                已分配案件
                if (caseManage.getCaseState().equals("1")) {
                    count2++;
                    arrayList.add(caseManage.getCaseId());
                    count3 = count3.add(caseManage.getClientMoney());
                }
            }
            Map<String, Object> map = new HashMap<>();
            map.put("weifenpei", count1);
            map.put("yifenpei", count2);
            map.put("zongshu", count1 + count2);
            map.put("zongjine", count3);
            map.put("arrayList", arrayList);
            return map;
        }
        return null;
    }

    /**
     * 标记案件案件查询
     *
     * @param exportReminder
     * @return
     */
    public void selectMarkCase(ExportReminder exportReminder) {
        List<CaseManage> caseManages = caseService.selectCaseManages(exportReminder);
        List<CaseManage> caseManageList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(caseManages)) {
            for (CaseManage caseManage : caseManages) {
                CaseManage caseManage1 = new CaseManage();
                caseManage1.setCaseId(caseManage.getCaseId());
                caseManage1.setId(caseManage.getId());
                caseManage1.setLabel(exportReminder.getCode().toString());
                caseManage1.setOutsourcingTeamId(new Long((long) TokenInformation.getCreateid()));
                caseManageList.add(caseManage1);
            }
        }
        caseService.updateCaseRecovery(caseManageList);
    }

    /**
     * 留案/退案/停催审批查询（根据团队审批流程查询登录人的审批信息）
     *
     * @param
     * @return
     */
    public List<myApplyRecordUtils> retention(QueryCriteria queryCriteria) {
        queryCriteria.setTeamId(new Long(TokenInformation.getCreateid()));  //团队id
        if (queryCriteria.getApplyState().equals(0)) {
            queryCriteria.setApproveCode(4); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核，7-外放审核
        } else if (queryCriteria.getApplyState().equals(1)) {
            queryCriteria.setApproveCode(3);
        } else if (queryCriteria.getApplyState().equals(2)) {
            queryCriteria.setApproveCode(5);
        }
        PageUtils.startPage();
        List<myApplyRecordUtils> myApplyRecordUtils = myApprovalService.selectApplyRecord(queryCriteria);
        return myApplyRecordUtils;
    }

//    /**
//     * 我的审批查询待审核
//     *
//     * @param queryCriteria
//     * @param approvalSteps
//     * @return
//     */
//    public List<myApplyRecordUtils> beReviewed(QueryCriteria queryCriteria, ApprovalSteps approvalSteps) {
//        if (!ObjectUtils.isEmpty(queryCriteria.getExamineTime1()) && !ObjectUtils.isEmpty(queryCriteria.getExamineTime2())) {
//            return new ArrayList<>();
//        }
//        List<ApprovalSteps> approvalSteps1 = teamService.selectApprovalSteps(approvalSteps);//根据团队id查找团队审批设置流程数据
//        for (ApprovalSteps approvalSteps2 : approvalSteps1) {
//            if (approvalSteps2.getApproverId().equals(TokenInformation.getUserid())) {   //查询团队审批流程中是否有该员工的审批流程
//                if (approvalSteps2.getSort().equals(1)) {  //如果排序为第一个
//                    queryCriteria.setProce(0);  //  0-待审核
//                    queryCriteria.setProceSort(0);  //审核进程顺序
//                } else {
//                    queryCriteria.setProce(1);  //  1-催收端审核中
//                    queryCriteria.setProceSort(approvalSteps2.getSort() - 1);   //审核进程顺序
//                }
//                PageUtils.startPage();
//                List<myApplyRecordUtils> myApplyRecordUtils = myApprovalService.selectApplyRecord(queryCriteria);  //根据条件查询我的审批信息（停案/留案/停催）-待审核案件
//                return myApplyRecordUtils;
//            }
//        }
//        return new ArrayList<>();
//    }
//
//    /**
//     * 留案/退案/停催审批查询（根据团队审批流程查询登录人的审批信息）
//     *
//     * @param
//     * @return
//     */
//    public List<myApplyRecordUtils> retention(QueryCriteria queryCriteria) {
//        ApprovalSteps approvalSteps = new ApprovalSteps();
//        approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
//        if (queryCriteria.getApplyState().equals(0)) {
//            queryCriteria.setApproveCode(4);
//            approvalSteps.setApproveCode(4); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核，7-外放审核
//        } else if (queryCriteria.getApplyState().equals(1)) {
//            queryCriteria.setApproveCode(3);
//            approvalSteps.setApproveCode(3); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核，7-外放审核
//        } else if (queryCriteria.getApplyState().equals(2)) {
//            queryCriteria.setApproveCode(5);
//            approvalSteps.setApproveCode(5); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核，7-外放审核
//        }
//
//        queryCriteria.setTeamId(new Long((long) approvalSteps.getCreateId()));  //团队id
////        queryCriteria.setApproveCode(approvalSteps.getApproveCode());   //审核类型
//
//        if (!ObjectUtils.isEmpty(queryCriteria.getApproveStarts())) {
//            if (queryCriteria.getApproveStarts().equals(2)) {  //查询状态（0-通过，1-不通过，2-待审核，3-已完成）
//                List<myApplyRecordUtils> myApplyRecordUtils = beReviewed(queryCriteria, approvalSteps);
//                return myApplyRecordUtils;
//            } else if (queryCriteria.getApproveStarts().equals(0) || queryCriteria.getApproveStarts().equals(1)) {  //查询状态（0-通过，1-不通过，2-待审核，3-已完成）
//                queryCriteria.setApproveStart(queryCriteria.getApproveStarts());
//                queryCriteria.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//                List<myApplyRecordUtils> myApplyRecordUtils = myApprovalService.selectCaseDetails(queryCriteria);  //根据条件查询我的审批信息（停案/留案/停催）-通过/未通过案件
//                return myApplyRecordUtils;
//            } else if (queryCriteria.getApproveStarts().equals(3)) {  //查询状态（0-通过，1-不通过，2-待审核，3-已完成）
//                List<Integer> list = new ArrayList<>();
//                list.add(0);
//                list.add(1);
//                queryCriteria.setList(list);
//                queryCriteria.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//                List<myApplyRecordUtils> myApplyRecordUtils = myApprovalService.selectCaseDetails(queryCriteria);  //根据条件查询我的审批信息（停案/留案/停催）-通过/未通过案件
//                return myApplyRecordUtils;
//            }
//        }
//
//        List<myApplyRecordUtils> lists = new ArrayList<>();
//        if (ObjectUtils.isEmpty(queryCriteria.getApproveStarts())) {
//            List<myApplyRecordUtils> myApplyRecordUtils = beReviewed(queryCriteria, approvalSteps);//根据条件查询我的审批信息（停案/留案/停催）-待审核案件
//            List<Integer> list = new ArrayList<>();
//            list.add(0);
//            list.add(1);
//            queryCriteria.setList(list);
//            queryCriteria.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//            List<myApplyRecordUtils> myApplyRecordUtil2 = myApprovalService.selectCaseDetails(queryCriteria);  //根据条件查询我的审批信息（停案/留案/停催）-通过/未通过案件
//            for (myApplyRecordUtils myApplyRecordUtils1 : myApplyRecordUtils) {
//                lists.add(myApplyRecordUtils1);
//            }
//            for (myApplyRecordUtils myApplyRecordUtils1 : myApplyRecordUtil2) {
//                lists.add(myApplyRecordUtils1);
//            }
//        }
//        Collections.sort(lists);
//        return lists;
//    }

    /**
     * 留案/退案/停催审批查询（根据团队审批流程查询登录人的审批信息）-判断该审批案件是否属于本团队
     *
     * @param queryCriteria
     * @return
     */
    public List<myApplyRecordUtils> judgeButtonOne(QueryCriteria queryCriteria) {
        List<myApplyRecordUtils> retention = retention(queryCriteria);
        if (!ObjectUtils.isEmpty(retention)) {
//            进行数据脱敏
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            Desensitization desensitization = null;
            if (state.getInformationStatus() == 1) {
                desensitization = stateDesensitization.getDesensitization();
            }

            for (myApplyRecordUtils myApplyRecordUtil : retention) {
                //
                if (ObjectUtils.isEmpty(myApplyRecordUtil.getApproveStart())) {
                    if (myApplyRecordUtil.getExamineState().equals("待审核") ||
                            myApplyRecordUtil.getExamineState().equals("审核中")) {

                        myApplyRecordUtil.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
                    }
                }

                //判断案件是否属于当前团队
                boolean isBelongToTeam = checkCaseBelongToTeam(myApplyRecordUtil.getCaseId());
                myApplyRecordUtil.setButton(isBelongToTeam ? 1 : 0);

                //解密
                DecryptUtils.dataDecrypt(myApplyRecordUtil);
                //脱敏
                if (desensitization != null) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(myApplyRecordUtil.getClientName())) {
                        myApplyRecordUtil.setClientName(DataMaskingUtils.nameMasking(myApplyRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(myApplyRecordUtil.getClientIdcard())) {
                        myApplyRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(myApplyRecordUtil.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(myApplyRecordUtil.getClientCensusRegister())) {
                        myApplyRecordUtil.setClientCensusRegister(DataMaskingUtils.Masking(myApplyRecordUtil.getClientCensusRegister()));
                    }
                }
            }
        }

        return retention;
    }


    /**
     * 资料调取审批查询（根据团队审批流程查询登录人的审批信息）
     *
     * @param
     * @return
     */
    public List<MyRetrievalRecordUtils> selectRetrievalRecord(PaymentCollectionUtils paymentCollectionUtils) {
        paymentCollectionUtils.setTeamId(TokenInformation.getCreateid());  //团队id
        paymentCollectionUtils.setApproveCode(8); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,8-资料调取审核
        List<MyRetrievalRecordUtils> myRetrievalRecordUtils = myApprovalService.selectRetrievalRecord(paymentCollectionUtils);
        return myRetrievalRecordUtils;
    }

    /**
     * 资料调取审批查询（根据团队审批流程查询登录人的审批信息）-判断该审批案件是否属于本团队
     *
     * @param paymentCollectionUtils
     * @return
     */
    public List<MyRetrievalRecordUtils> judgeButtonFive(PaymentCollectionUtils paymentCollectionUtils) {
        List<MyRetrievalRecordUtils> myRetrievalRecordUtils = selectRetrievalRecord(paymentCollectionUtils);

        if (!ObjectUtils.isEmpty(myRetrievalRecordUtils)) {
//            进行数据脱敏
            Desensitization desensitization = desensitizationAgService.getDesensitization();
            for (MyRetrievalRecordUtils myRetrievalRecordUtils1 : myRetrievalRecordUtils) {
                try {
                    if (ObjectUtils.isEmpty(myRetrievalRecordUtils1.getApproveStart())) {
                        if (myRetrievalRecordUtils1.getExamineState().equals("待审核") || myRetrievalRecordUtils1.getExamineState().equals("审核中")) {
                            myRetrievalRecordUtils1.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
                        }
                    }

                    boolean caseBelongToTeam = checkCaseBelongToTeam(myRetrievalRecordUtils1.getCaseId());
                    myRetrievalRecordUtils1.setButton(caseBelongToTeam ? 1 : 0);

                    if (myRetrievalRecordUtils1.getExamineState().equals("已通过") && !ObjectUtils.isEmpty(myRetrievalRecordUtils1.getRandom())) {
                        String archiveFileAddress = myRetrievalRecordUtils1.getArchiveFileAddress();
                        if (!ObjectUtils.isEmpty(archiveFileAddress)){
                            myRetrievalRecordUtils1.setPath(archiveFileAddress);
                        }
                        if (!ObjectUtils.isEmpty(myRetrievalRecordUtils1.getWatermarkedFilePath())){
                            myRetrievalRecordUtils1.setPath(myRetrievalRecordUtils1.getWatermarkedFilePath());
                        }
                        if (!ObjectUtils.isEmpty(archiveFileAddress) && !ObjectUtils.isEmpty(myRetrievalRecordUtils1.getWatermarkedFilePath())){
                            myRetrievalRecordUtils1.setPath(myRetrievalRecordUtils1.getWatermarkedFilePath()+","+archiveFileAddress);
                        }
//                    判断申请是否到期
                        int become = fileExpirationTime(myRetrievalRecordUtils1.getExamineTime());
                        myRetrievalRecordUtils1.setFileExpiration(become);
                    }
                    DecryptUtils.dataDecrypt(myRetrievalRecordUtils1);
                    if (desensitization != null) {
                        if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(myRetrievalRecordUtils1.getClientName())) {
                            myRetrievalRecordUtils1.setClientName(DataMaskingUtils.nameMasking(myRetrievalRecordUtils1.getClientName()));
                        }
                        if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(myRetrievalRecordUtils1.getClientIdcard())) {
                            myRetrievalRecordUtils1.setClientIdcard(DataMaskingUtils.idMasking(myRetrievalRecordUtils1.getClientIdcard()));
                        }
//                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(myRetrievalRecordUtils1.getClientCensusRegister()))
//                        myRetrievalRecordUtils1.setClientCensusRegister(DataMaskingUtils.Masking(myRetrievalRecordUtils1.getClientCensusRegister()));

                    }
                } catch (Exception e) {
                    log.error("资料调取审批查询 异常", e);
                }
            }

        }

        return myRetrievalRecordUtils;
    }

    /**
     * 根据审核日期判断该申请是否过期（资料调取申请）
     *
     * @param auditTime 审核日期
     * @return
     */
    public int fileExpirationTime(Date auditTime) {
        Date newDate2 = DateUtil.offsetDay(auditTime, 3);
        if (newDate2.getTime() <= System.currentTimeMillis()) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 回款审批查询（根据团队审批流程查询登录人的审批信息）
     *
     * @param
     * @return
     */
    public List<myRepaymentRecordUtils> selectRepaymentRecord(PaymentCollectionUtils paymentCollectionUtils) {
        paymentCollectionUtils.setTeamId(TokenInformation.getCreateid());  //团队id
        paymentCollectionUtils.setApproveCode(0); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        List<myRepaymentRecordUtils> myRepaymentRecordUtils = myApprovalService.selectToBeReviewd(paymentCollectionUtils);
        return myRepaymentRecordUtils;
    }

//    /**
//     * 回款审批待审核案件查询
//     *
//     * @param paymentCollectionUtils
//     * @param approvalSteps
//     * @return
//     */
//    public List<myRepaymentRecordUtils> UnderReview(PaymentCollectionUtils paymentCollectionUtils, ApprovalSteps
//            approvalSteps) {
//        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getUpdateTime1()) && !ObjectUtils.isEmpty(paymentCollectionUtils.getUpdateTime2())) {
//            return new ArrayList<>();
//        }
//        List<ApprovalSteps> approvalSteps1 = teamService.selectApprovalSteps(approvalSteps);//根据团队id查找团队审批设置流程数据
//        for (ApprovalSteps approvalSteps2 : approvalSteps1) {
//            if (approvalSteps2.getApproverId().equals(TokenInformation.getUserid())) {   //查询团队审批流程中是否有该员工的审批流程
//                if (approvalSteps2.getSort().equals(1)) {  //如果排序为第一个
//                    paymentCollectionUtils.setProce(0);  //  0-待审核
//                    paymentCollectionUtils.setProceSort(0);   //审核进程顺序
//                } else {
//                    paymentCollectionUtils.setProce(1);  //  1-催收端审核中
//                    paymentCollectionUtils.setProceSort(approvalSteps2.getSort() - 1);   //审核进程顺序
//                }
////                PageUtils.startPage();
//                List<myRepaymentRecordUtils> myRepaymentRecordUtils = myApprovalService.selectRepaymentRecord(paymentCollectionUtils);
//                return myRepaymentRecordUtils;
//            }
//        }
//        return new ArrayList<>();
//    }
//
//    /**
//     * 回款审批查询（根据团队审批流程查询登录人的审批信息）
//     *
//     * @param
//     * @return
//     */
//    public List<myRepaymentRecordUtils> selectRepaymentRecord(PaymentCollectionUtils paymentCollectionUtils) {
//        ApprovalSteps approvalSteps = new ApprovalSteps();
//        approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
//        approvalSteps.setApproveCode(0); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
//
//        paymentCollectionUtils.setTeamId(TokenInformation.getCreateid());  //团队id
//        paymentCollectionUtils.setApproveCode(approvalSteps.getApproveCode());  //审核类型
//
//        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getApproveStarts())) {
//            if (paymentCollectionUtils.getApproveStarts().equals(2)) { //查询状态（0-通过，1-不通过，2-待审核，3-已完成）
//                List<myRepaymentRecordUtils> myRepaymentRecordUtils = UnderReview(paymentCollectionUtils, approvalSteps);
//                return myRepaymentRecordUtils;
//            } else if (paymentCollectionUtils.getApproveStarts().equals(0) || paymentCollectionUtils.getApproveStarts().equals(1)) {
//                paymentCollectionUtils.setApproveStart(paymentCollectionUtils.getApproveStarts());
//                paymentCollectionUtils.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//                List<myRepaymentRecordUtils> myRepaymentRecordUtils = myApprovalService.selectRepaymentRecordProce(paymentCollectionUtils);//根据条件查询我的审批信息（回款）-通过/未通过案件
//                return myRepaymentRecordUtils;
//            } else if (paymentCollectionUtils.getApproveStarts().equals(3)) {
//                List<Integer> list = new ArrayList<>();
//                list.add(0);
//                list.add(1);
//                paymentCollectionUtils.setList(list);
//                paymentCollectionUtils.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//                List<myRepaymentRecordUtils> myRepaymentRecordUtils = myApprovalService.selectRepaymentRecordProce(paymentCollectionUtils);//根据条件查询我的审批信息（回款）-通过/未通过案件
//                return myRepaymentRecordUtils;
//            }
//        }
//        List<myRepaymentRecordUtils> lists = new ArrayList<>();
//        if (ObjectUtils.isEmpty(paymentCollectionUtils.getApproveStarts())) {
//            List<myRepaymentRecordUtils> myRepaymentRecordUtils = UnderReview(paymentCollectionUtils, approvalSteps);//根据条件查询我的审批信息（回款）-待审核案件
//            System.err.println("待审核大小:" + myRepaymentRecordUtils.size());
//            List<Integer> list = new ArrayList<>();
//            list.add(0);
//            list.add(1);
//            paymentCollectionUtils.setList(list);
//            paymentCollectionUtils.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
////            PageUtils.startPage();
//            List<myRepaymentRecordUtils> myRepaymentRecordUtils2 = myApprovalService.selectRepaymentRecordProce(paymentCollectionUtils);//根据条件查询我的审批信息（回款）-通过/未通过案件
//            System.err.println("已通过/未通过大小:" + myRepaymentRecordUtils2.size());
//            for (myRepaymentRecordUtils myRepaymentRecordUtils1 : myRepaymentRecordUtils) {
//                lists.add(myRepaymentRecordUtils1);
//            }
//            for (myRepaymentRecordUtils myRepaymentRecordUtils1 : myRepaymentRecordUtils2) {
//                lists.add(myRepaymentRecordUtils1);
//            }
//        }
//        Collections.sort(lists);
//        return lists;
//    }

    /**
     * 回款审批查询（根据团队审批流程查询登录人的审批信息）-判断该审批案件是否属于本团队
     *
     * @param paymentCollectionUtils
     * @return
     */
    public List<myRepaymentRecordUtils> judgeButtonTwo(PaymentCollectionUtils paymentCollectionUtils) {
        List<myRepaymentRecordUtils> myRepaymentRecordUtils = selectRepaymentRecord(paymentCollectionUtils);
        if (!ObjectUtils.isEmpty(myRepaymentRecordUtils)) {
//            进行数据脱敏
            Desensitization desensitization = desensitizationAgService.getDesensitization();
            for (myRepaymentRecordUtils myRepaymentRecordUtil : myRepaymentRecordUtils) {
                if (ObjectUtils.isEmpty(myRepaymentRecordUtil.getApproveStart())) {
                    if (myRepaymentRecordUtil.getExamineState().equals("待审核")
                            || myRepaymentRecordUtil.getExamineState().equals("审核中")
                            || myRepaymentRecordUtil.getExamineState().equals("待上传凭证")) {
                        myRepaymentRecordUtil.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
                    }
                }

                boolean caseBelongToTeam = checkCaseBelongToTeam(myRepaymentRecordUtil.getCaseId());
                myRepaymentRecordUtil.setButton(caseBelongToTeam ? 1 : 0);

                DecryptUtils.dataDecrypt(myRepaymentRecordUtil);
                if (desensitization != null) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(myRepaymentRecordUtil.getClientName())) {
                        myRepaymentRecordUtil.setClientName(DataMaskingUtils.nameMasking(myRepaymentRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(myRepaymentRecordUtil.getClientIdcard())) {
                        myRepaymentRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(myRepaymentRecordUtil.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(myRepaymentRecordUtil.getClientCensusRegister())) {
                        myRepaymentRecordUtil.setClientCensusRegister(DataMaskingUtils.Masking(myRepaymentRecordUtil.getClientCensusRegister()));
                    }

                }

            }

        }
        return myRepaymentRecordUtils;
    }

    /**
     * 减免审批查询（根据团队审批流程查询登录人的审批信息）
     *
     * @param
     * @return
     */
    public List<myReductionRecordUtils> selectReductionRecord(PaymentCollectionUtils paymentCollectionUtils) {
        paymentCollectionUtils.setTeamId(TokenInformation.getCreateid());  //团队id
        paymentCollectionUtils.setApproveCode(1); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        List<myReductionRecordUtils> myReductionRecordUtils = myApprovalService.selectReductionRecord(paymentCollectionUtils);
        return myReductionRecordUtils;
    }

//    /**
//     * 减免审批待审核案件查询
//     *
//     * @param paymentCollectionUtils
//     * @param approvalSteps
//     * @return
//     */
//    public List<myReductionRecordUtils> reduction(PaymentCollectionUtils paymentCollectionUtils, ApprovalSteps
//            approvalSteps) {
//        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getUpdateTime1()) && !ObjectUtils.isEmpty(paymentCollectionUtils.getUpdateTime2())) {
//            return new ArrayList<>();
//        }
//        List<ApprovalSteps> approvalSteps1 = teamService.selectApprovalSteps(approvalSteps);//根据团队id查找团队审批设置流程数据
//        for (ApprovalSteps approvalSteps2 : approvalSteps1) {
//            if (approvalSteps2.getApproverId().equals(TokenInformation.getUserid())) {   //查询团队审批流程中是否有该员工的审批流程
//                if (approvalSteps2.getSort().equals(1)) {  //如果排序为第一个
//                    paymentCollectionUtils.setProce(0);  //  0-待审核
//                    paymentCollectionUtils.setProceSort(0);   //审核进程顺序
//                } else {
//                    paymentCollectionUtils.setProce(1);  //  1-催收端审核中
//                    paymentCollectionUtils.setProceSort(approvalSteps2.getSort() - 1);   //审核进程顺序
//                }
//                List<myReductionRecordUtils> myReductionRecordUtils = myApprovalService.selectReductionRecord(paymentCollectionUtils);
//                return myReductionRecordUtils;
//            }
//        }
//        return new ArrayList<>();
//    }
//
//    /**
//     * 减免审批查询（根据团队审批流程查询登录人的审批信息）
//     *
//     * @param
//     * @return
//     */
//    public List<myReductionRecordUtils> selectReductionRecord(PaymentCollectionUtils paymentCollectionUtils) {
//        ApprovalSteps approvalSteps = new ApprovalSteps();
//        approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
//        approvalSteps.setApproveCode(1); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
//
//        paymentCollectionUtils.setTeamId(TokenInformation.getCreateid());  //团队id
//        paymentCollectionUtils.setApproveCode(approvalSteps.getApproveCode());  //审核类型
//
//        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getApproveStarts())) {
//            if (paymentCollectionUtils.getApproveStarts().equals(2)) { //查询状态（0-通过，1-不通过，2-待审核，3-已完成）
//                List<myReductionRecordUtils> reduction = reduction(paymentCollectionUtils, approvalSteps);
//                return reduction;
//            } else if (paymentCollectionUtils.getApproveStarts().equals(0) || paymentCollectionUtils.getApproveStarts().equals(1)) {
//                paymentCollectionUtils.setApproveStart(paymentCollectionUtils.getApproveStarts());
//                paymentCollectionUtils.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//                List<myReductionRecordUtils> myReductionRecordUtils = myApprovalService.selectReductionRecordProce(paymentCollectionUtils);//根据条件查询我的审批信息（减免）-通过/未通过案件
//                return myReductionRecordUtils;
//            } else if (paymentCollectionUtils.getApproveStarts().equals(3)) {
//                List<Integer> list = new ArrayList<>();
//                list.add(0);
//                list.add(1);
//                paymentCollectionUtils.setList(list);
//                paymentCollectionUtils.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//                List<myReductionRecordUtils> myReductionRecordUtils = myApprovalService.selectReductionRecordProce(paymentCollectionUtils);//根据条件查询我的审批信息（减免）-通过/未通过案件
//                return myReductionRecordUtils;
//            }
//        }
//        List<myReductionRecordUtils> lists = new ArrayList<>();
//        if (ObjectUtils.isEmpty(paymentCollectionUtils.getApproveStarts())) {
//            List<myReductionRecordUtils> reduction = reduction(paymentCollectionUtils, approvalSteps);//根据条件查询我的审批信息（减免）-待审核案件
//            List<Integer> list = new ArrayList<>();
//            list.add(0);
//            list.add(1);
//            paymentCollectionUtils.setList(list);
//            paymentCollectionUtils.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//            List<myReductionRecordUtils> myReductionRecordUtils = myApprovalService.selectReductionRecordProce(paymentCollectionUtils);//根据条件查询我的审批信息（减免）-通过/未通过案件
//            for (myReductionRecordUtils myReductionRecordUtil : reduction) {
//                lists.add(myReductionRecordUtil);
//            }
//            for (myReductionRecordUtils myReductionRecordUtil : myReductionRecordUtils) {
//                lists.add(myReductionRecordUtil);
//            }
//        }
//        Collections.sort(lists);
//        return lists;
//    }

    /**
     * 减免审批查询（根据团队审批流程查询登录人的审批信息）-判断该审批案件是否属于本团队
     *
     * @param paymentCollectionUtils
     * @return
     */
    public List<myReductionRecordUtils> judgeButtonThree(PaymentCollectionUtils paymentCollectionUtils) {
        List<myReductionRecordUtils> myReductionRecordUtils = selectReductionRecord(paymentCollectionUtils);
        if (!ObjectUtils.isEmpty(myReductionRecordUtils)) {
//            进行数据脱敏
            Desensitization desensitization = desensitizationAgService.getDesensitization();
            for (myReductionRecordUtils temp : myReductionRecordUtils) {
                if (ObjectUtils.isEmpty(temp.getApproveStart())) {
                    if (temp.getState().equals("待审核") || temp.getState().equals("审核中")) {
                        temp.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
                    }
                }
                boolean caseBelongToTeam = checkCaseBelongToTeam(temp.getCaseId());
                temp.setButton(caseBelongToTeam ? 1 : 0);
                DecryptUtils.dataDecrypt(temp);
                if (desensitization != null) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(temp.getClientName())) {
                        temp.setClientName(DataMaskingUtils.nameMasking(temp.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(temp.getClientIdcard())) {
                        temp.setClientIdcard(DataMaskingUtils.idMasking(temp.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(temp.getClientCensusRegister())) {
                        temp.setClientCensusRegister(DataMaskingUtils.Masking(temp.getClientCensusRegister()));
                    }
                }
            }
        }
        return myReductionRecordUtils;
    }

//    ----------------------------------------------------------------------------------------------------------------------------

    /**
     * 分期还款审批查询（根据团队审批流程查询登录人的审批信息）
     *
     * @param
     * @return
     */
    public List<MyStagingRecordUtils> selectStagingRecord(PaymentCollectionUtils paymentCollectionUtils) {
        paymentCollectionUtils.setTeamId(TokenInformation.getCreateid());  //团队id
        paymentCollectionUtils.setApproveCode(2); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        List<MyStagingRecordUtils> myStagingRecordUtils = myApprovalService.selectStagingRecord(paymentCollectionUtils);
        return myStagingRecordUtils;
    }

//    /**
//     * 分期还款审批待审核案件查询
//     *
//     * @param paymentCollectionUtils
//     * @param approvalSteps
//     * @return
//     */
//    public List<MyStagingRecordUtils> Amortization(PaymentCollectionUtils paymentCollectionUtils, ApprovalSteps
//            approvalSteps) {
//        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getUpdateTime1()) && !ObjectUtils.isEmpty(paymentCollectionUtils.getUpdateTime2())) {
//            return new ArrayList<>();
//        }
//        List<ApprovalSteps> approvalSteps1 = teamService.selectApprovalSteps(approvalSteps);//根据团队id查找团队审批设置流程数据
//        for (ApprovalSteps approvalSteps2 : approvalSteps1) {
//            if (approvalSteps2.getApproverId().equals(TokenInformation.getUserid())) {   //查询团队审批流程中是否有该员工的审批流程
//                if (approvalSteps2.getSort().equals(1)) {  //如果排序为第一个
//                    paymentCollectionUtils.setProce(0);  //  0-待审核
//                    paymentCollectionUtils.setProceSort(0);   //审核进程顺序
//                } else {
//                    paymentCollectionUtils.setProce(1);  //  1-催收端审核中
//                    paymentCollectionUtils.setProceSort(approvalSteps2.getSort() - 1);   //审核进程顺序
//                }
//                List<MyStagingRecordUtils> myStagingRecordUtils = myApprovalService.selectStagingRecord(paymentCollectionUtils);
//                return myStagingRecordUtils;
//            }
//        }
//        return new ArrayList<>();
//    }
//
//    /**
//     * 分期还款审批查询（根据团队审批流程查询登录人的审批信息）
//     *
//     * @param
//     * @return
//     */
//    public List<MyStagingRecordUtils> selectStagingRecord(PaymentCollectionUtils paymentCollectionUtils) {
//        ApprovalSteps approvalSteps = new ApprovalSteps();
//        approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
//        approvalSteps.setApproveCode(2); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
//
//        paymentCollectionUtils.setTeamId(TokenInformation.getCreateid());  //团队id
//        paymentCollectionUtils.setApproveCode(approvalSteps.getApproveCode());  //审核类型
//
//        if (!ObjectUtils.isEmpty(paymentCollectionUtils.getApproveStarts())) {
//            if (paymentCollectionUtils.getApproveStarts().equals(2)) { //查询状态（0-通过，1-不通过，2-待审核，3-已完成）
//                List<MyStagingRecordUtils> amortization = Amortization(paymentCollectionUtils, approvalSteps);
//                return amortization;
//            } else if (paymentCollectionUtils.getApproveStarts().equals(0) || paymentCollectionUtils.getApproveStarts().equals(1)) {
//                paymentCollectionUtils.setApproveStart(paymentCollectionUtils.getApproveStarts());
//                paymentCollectionUtils.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//                List<MyStagingRecordUtils> myStagingRecordUtils = myApprovalService.selectStagingRecordProce(paymentCollectionUtils);//根据条件查询我的审批信息（分期还款）-通过/未通过案件
//                return myStagingRecordUtils;
//            } else if (paymentCollectionUtils.getApproveStarts().equals(3)) {
//                List<Integer> list = new ArrayList<>();
//                list.add(0);
//                list.add(1);
//                paymentCollectionUtils.setList(list);
//                paymentCollectionUtils.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//                List<MyStagingRecordUtils> myStagingRecordUtils = myApprovalService.selectStagingRecordProce(paymentCollectionUtils);//根据条件查询我的审批信息（分期还款）-通过/未通过案件
//                return myStagingRecordUtils;
//            }
//        }
//        List<MyStagingRecordUtils> lists = new ArrayList<>();
//        if (ObjectUtils.isEmpty(paymentCollectionUtils.getApproveStarts())) {
//            List<MyStagingRecordUtils> amortization = Amortization(paymentCollectionUtils, approvalSteps);//根据条件查询我的审批信息（分期还款）-待审核案件
//            List<Integer> list = new ArrayList<>();
//            list.add(0);
//            list.add(1);
//            paymentCollectionUtils.setList(list);
//            paymentCollectionUtils.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//            List<MyStagingRecordUtils> myStagingRecordUtils = myApprovalService.selectStagingRecordProce(paymentCollectionUtils);//根据条件查询我的审批信息（分期还款）-通过/未通过案件
//            for (MyStagingRecordUtils amortizations : amortization) {
//                lists.add(amortizations);
//            }
//            for (MyStagingRecordUtils myStagingRecordUtil : myStagingRecordUtils) {
//                lists.add(myStagingRecordUtil);
//            }
//        }
//        Collections.sort(lists);
//        return lists;
//    }

    /**
     * 分期还款审批查询（根据团队审批流程查询登录人的审批信息）-判断该审批案件是否属于本团队
     *
     * @param paymentCollectionUtils
     * @return
     */
    public List<MyStagingRecordUtils> judgeButtonFour(PaymentCollectionUtils paymentCollectionUtils) {
        List<MyStagingRecordUtils> myStagingRecordUtils = selectStagingRecord(paymentCollectionUtils);
        if (!ObjectUtils.isEmpty(myStagingRecordUtils)) {
            Desensitization desensitization = desensitizationAgService.getDesensitization();
            for (MyStagingRecordUtils myStagingRecordUtil : myStagingRecordUtils) {

                if (ObjectUtils.isEmpty(myStagingRecordUtil.getApproveStart())) {
                    if (myStagingRecordUtil.getExamineState().equals("待审核") || myStagingRecordUtil.getExamineState().equals("审核中")) {
                        myStagingRecordUtil.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
                    }
                }

                boolean caseBelongToTeam = checkCaseBelongToTeam(myStagingRecordUtil.getCaseId());
                myStagingRecordUtil.setButton(caseBelongToTeam ? 1 : 0);

                DecryptUtils.dataDecrypt(myStagingRecordUtil);
                if (desensitization != null) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(myStagingRecordUtil.getClientName())) {
                        myStagingRecordUtil.setClientName(DataMaskingUtils.nameMasking(myStagingRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(myStagingRecordUtil.getClientIdcard())) {
                        myStagingRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(myStagingRecordUtil.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(myStagingRecordUtil.getClientCensusRegister())) {
                        myStagingRecordUtil.setClientCensusRegister(DataMaskingUtils.Masking(myStagingRecordUtil.getClientCensusRegister()));
                    }

                }
            }
        }
        return myStagingRecordUtils;
    }

    /**
     * 外访审批查询（根据团队审批流程查询登录人的审批信息）
     *
     * @param
     * @return
     */
    public List<MyOutsideRecordUtils> selectOutsideRecord(OutsideCollectionUtils outsideCollectionUtils) {
        outsideCollectionUtils.setTeamId(TokenInformation.getCreateid());  //团队id
        outsideCollectionUtils.setApproveCode(ApproveEnum.OUTVISIT.getCode()); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        List<MyOutsideRecordUtils> myOutsideRecordUtils = myApprovalService.selectOutsideRecord(outsideCollectionUtils);
        return myOutsideRecordUtils;
    }

    /**
     * 签章审批查询（根据团队审批流程查询登录人的审批信息）
     *
     * @param
     * @return
     */
    public List<MySignRecordUtils> selectSignRecord(SignCollectionUtils signCollectionUtils) {
        signCollectionUtils.setTeamId(TokenInformation.getCreateid());  //团队id
        signCollectionUtils.setApproveCode(ApproveEnum.LAWAPPROVE.getCode());
        List<MySignRecordUtils> mySignRecordUtils = myApprovalService.selectSignRecord(signCollectionUtils);
        return mySignRecordUtils;
    }

//    /**
//     * 外访审批待审核案件查询
//     *
//     * @param outsideCollectionUtils
//     * @param approvalSteps
//     * @return
//     */
//    public List<MyOutsideRecordUtils> VisitCountry(OutsideCollectionUtils outsideCollectionUtils, ApprovalSteps
//            approvalSteps) {
//        if (!ObjectUtils.isEmpty(outsideCollectionUtils.getApproveTime1()) && !ObjectUtils.isEmpty(outsideCollectionUtils.getApproveTime2())) {
//            return new ArrayList<>();
//        }
//        List<ApprovalSteps> approvalSteps1 = teamService.selectApprovalSteps(approvalSteps);//根据团队id查找团队审批设置流程数据
//        for (ApprovalSteps approvalSteps2 : approvalSteps1) {
//            if (approvalSteps2.getApproverId().equals(TokenInformation.getUserid())) {   //查询团队审批流程中是否有该员工的审批流程
//                if (approvalSteps2.getSort().equals(1)) {  //如果排序为第一个
//                    outsideCollectionUtils.setProce(0);  //  0-待审核
//                    outsideCollectionUtils.setProceSort(0);   //审核进程顺序
//                } else {
//                    outsideCollectionUtils.setProce(1);  //  1-催收端审核中
//                    outsideCollectionUtils.setProceSort(approvalSteps2.getSort() - 1);   //审核进程顺序
//                }
//                List<MyOutsideRecordUtils> myOutsideRecordUtils = myApprovalService.selectOutsideRecord(outsideCollectionUtils);
//                return myOutsideRecordUtils;
//            }
//        }
//        return new ArrayList<>();
//    }
//
//    /**
//     * 外访审批查询（根据团队审批流程查询登录人的审批信息）
//     *
//     * @param
//     * @return
//     */
//    public List<MyOutsideRecordUtils> selectOutsideRecord(OutsideCollectionUtils outsideCollectionUtils) {
//        ApprovalSteps approvalSteps = new ApprovalSteps();
//        approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
//        approvalSteps.setApproveCode(7); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核，7-外访审核
//
//        outsideCollectionUtils.setTeamId(TokenInformation.getCreateid());  //团队id
//        outsideCollectionUtils.setApproveCode(approvalSteps.getApproveCode());  //审核类型
//
//        if (!ObjectUtils.isEmpty(outsideCollectionUtils.getApproveStarts())) {
//            if (outsideCollectionUtils.getApproveStarts().equals(2)) { //查询状态（0-通过，1-不通过，2-待审核，3-已完成）
//                List<MyOutsideRecordUtils> myOutsideRecordUtils = VisitCountry(outsideCollectionUtils, approvalSteps);
//                return myOutsideRecordUtils;
//            } else if (outsideCollectionUtils.getApproveStarts().equals(0) || outsideCollectionUtils.getApproveStarts().equals(1)) {
//                outsideCollectionUtils.setApproveStart(outsideCollectionUtils.getApproveStarts());
//                outsideCollectionUtils.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//                List<MyOutsideRecordUtils> myOutsideRecordUtils = myApprovalService.selectOutsideRecordProce(outsideCollectionUtils);//根据条件查询我的审批信息（外访）-通过/未通过案件
//                return myOutsideRecordUtils;
//            } else if (outsideCollectionUtils.getApproveStarts().equals(3)) {
//                List<Integer> list = new ArrayList<>();
//                list.add(0);
//                list.add(1);
//                outsideCollectionUtils.setList(list);
//                outsideCollectionUtils.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//                List<MyOutsideRecordUtils> myOutsideRecordUtils = myApprovalService.selectOutsideRecordProce(outsideCollectionUtils);//根据条件查询我的审批信息（外访）-通过/未通过案件
//                return myOutsideRecordUtils;
//            }
//        }
//        List<MyOutsideRecordUtils> lists = new ArrayList<>();
//        if (ObjectUtils.isEmpty(outsideCollectionUtils.getApproveStarts())) {
//            List<MyOutsideRecordUtils> myOutsideRecordUtils = VisitCountry(outsideCollectionUtils, approvalSteps);//根据条件查询我的审批信息（外访）-待审核案件
//            List<Integer> list = new ArrayList<>();
//            list.add(0);
//            list.add(1);
//            outsideCollectionUtils.setList(list);
//            outsideCollectionUtils.setReviewerId(new Long((long) TokenInformation.getUserid()));  //审核人id
//            List<MyOutsideRecordUtils> myOutsideRecordUtils1 = myApprovalService.selectOutsideRecordProce(outsideCollectionUtils);//根据条件查询我的审批信息（外访）-通过/未通过案件
//            for (MyOutsideRecordUtils myOutsideRecordUtil : myOutsideRecordUtils) {
//                lists.add(myOutsideRecordUtil);
//            }
//            for (MyOutsideRecordUtils myOutsideRecordUtil : myOutsideRecordUtils1) {
//                lists.add(myOutsideRecordUtil);
//            }
//        }
//        Collections.sort(lists);
//        return lists;
//    }

    /**
     * 外访审批查询（根据团队审批流程查询登录人的审批信息）-判断该审批案件是否属于本团队
     *
     * @param outsideCollectionUtils
     * @return
     */
    public List<MyOutsideRecordUtils> judgeButtonFive(OutsideCollectionUtils outsideCollectionUtils) {
        List<MyOutsideRecordUtils> myOutsideRecordUtils = selectOutsideRecord(outsideCollectionUtils);
        if (!ObjectUtils.isEmpty(myOutsideRecordUtils)) {

            Desensitization desensitization = desensitizationAgService.getDesensitization();
            for (MyOutsideRecordUtils myOutsideRecordUtil : myOutsideRecordUtils) {

                if (ObjectUtils.isEmpty(myOutsideRecordUtil.getApproveStart())) {
                    if (myOutsideRecordUtil.getState().equals("待审核") || myOutsideRecordUtil.getState().equals("审核中")) {
                        myOutsideRecordUtil.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
                    }
                }
                boolean belongToTeam = checkCaseBelongToTeam(myOutsideRecordUtil.getCaseId());
                myOutsideRecordUtil.setButton(belongToTeam ? 1 : 0);
                DecryptUtils.dataDecrypt(myOutsideRecordUtil);
                if (desensitization != null) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(myOutsideRecordUtil.getClientName())) {
                        myOutsideRecordUtil.setClientName(DataMaskingUtils.nameMasking(myOutsideRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(myOutsideRecordUtil.getClientIdcard())) {
                        myOutsideRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(myOutsideRecordUtil.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(myOutsideRecordUtil.getClientCensusRegister())) {
                        myOutsideRecordUtil.setClientCensusRegister(DataMaskingUtils.Masking(myOutsideRecordUtil.getClientCensusRegister()));
                    }

                }
            }
        }
        return myOutsideRecordUtils;
    }

    /**
     * 签章审批查询（根据团队审批流程查询登录人的审批信息）-判断该审批案件是否属于本团队
     *
     * @param outsideCollectionUtils
     * @return
     */
    public List<MySignRecordUtils> judgeButtonSix(SignCollectionUtils outsideCollectionUtils) {
        List<MySignRecordUtils> mySignRecordUtils = selectSignRecord(outsideCollectionUtils);
        if (!ObjectUtils.isEmpty(mySignRecordUtils)) {

            Desensitization desensitization = desensitizationAgService.getDesensitization();
            for (MySignRecordUtils mySignRecordUtil : mySignRecordUtils) {

                if (ObjectUtils.isEmpty(mySignRecordUtil.getApproveStart())) {
                    if (StrUtil.equals(mySignRecordUtil.getState(), "待审核") || StrUtil.equals(mySignRecordUtil.getState(), "审核中")) {
                        mySignRecordUtil.setApproveStart(2);  //处理状态,0-已同意，1-未同意，2-待处理
                    }
                }
                boolean belongToTeam = checkCaseBelongToTeam(mySignRecordUtil.getCaseId());
                mySignRecordUtil.setButton(belongToTeam ? 1 : 0);
                DecryptUtils.dataDecrypt(mySignRecordUtil);
                if (desensitization != null) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(mySignRecordUtil.getClientName())) {
                        mySignRecordUtil.setClientName(DataMaskingUtils.nameMasking(mySignRecordUtil.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(mySignRecordUtil.getClientIdcard())) {
                        mySignRecordUtil.setClientIdcard(DataMaskingUtils.idMasking(mySignRecordUtil.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(mySignRecordUtil.getClientCensusRegister())) {
                        mySignRecordUtil.setClientCensusRegister(DataMaskingUtils.Masking(mySignRecordUtil.getClientCensusRegister()));
                    }

                }
            }
        }
        return mySignRecordUtils;
    }

    /**
     * 留案/退案/停催审批（写入历史表并修改申请表中审核状态等）
     *
     * @param
     * @return
     */
    public void WriteModification(ApprovalRecord approvalRecord, LoginUser loginUser) {
        if (!ObjectUtils.isEmpty(approvalRecord.getFailureReason())) {
            if (approvalRecord.getFailureReason().length() > 300) {
                throw new GlobalException("字数限制为300字,请重新输入");
            }
        }
        if (ObjectUtils.isEmpty(approvalRecord.getIds())) {
            throw new GlobalException("申请id不能为空，至少选择一个审批");
        }
        List<ApplyRecord> list = new ArrayList<>();

        List<Long> ids = approvalRecord.getIds();   //案件申请记录id集合

        boolean approvalAuthority = teamSysAgService.checkApprovalAuthority(loginUser);
        if (!approvalAuthority) {
            throw new GlobalException("没有该用户的审批流程");
        }
        int sort = 1;
        teamSysAgService.filterDuplicates(ids, sort, approvalRecord.getApproveCode());
        for (Long id : ids) {
            ApplyRecord applyRecord = new ApplyRecord();
            if (approvalRecord.getApproveStart().equals(0)) {   //审批通过
               /* if (sort == approvalSteps1.get(approvalSteps1.size() - 1).getSort()) {
                    applyRecord.setProce(2);  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                } else {
                    applyRecord.setProce(1);  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                }*/

                //修改为固定只有一级审批
                applyRecord.setProce(2);  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                applyRecord.setExamineState("审核中");  //审核状态


            } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
                applyRecord.setProce(5);  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                applyRecord.setExamineState("未通过");  //审核状态
                applyRecord.setExamineTime(new Date());  //最后审核时间
            }
            applyRecord.setId(id);
            applyRecord.setProceSort(sort);  //审核进程顺序
            applyRecord.setExamineBy(TokenInformation.getUsername());   //审核人-最后审核人
            Long userid1 = new Long((long) TokenInformation.getUserid());
            applyRecord.setExamineById(userid1);   //审核人id
            list.add(applyRecord);
        }
        myApprovalService.updateApplyRecord(list);    //退案/留案/停催审批（修改申请表中审核状态等）
        List<ApproveProce> arrayList = new ArrayList<>();
        for (Long id : ids) {
            ApproveProce approveProce = new ApproveProce();
            approveProce.setApproveCode(approvalRecord.getApproveCode());  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
            approveProce.setApplyId(id);  //申请id,根据审核类型查找不同的申请表
            approveProce.setApproveStart(approvalRecord.getApproveStart());  //审核状态,0-通过，1-不通过
            approveProce.setApproveTime(new Date());  //审核时间
            approveProce.setReviewer(TokenInformation.getUsername());  //审核人
            approveProce.setReviewerId(TokenInformation.getUserid());  //审核人id
            approveProce.setRefuseReason(approvalRecord.getFailureReason());  //拒绝理由
            approveProce.setApproveSort(sort);  //审核顺序，审核流程中的排序
            approveProce.setDelFlag("0");
            approveProce.setOperationType(TokenInformation.getType());  //操作人类型（0-主账号;1-员工账号;2-资产端账号）
            arrayList.add(approveProce);
        }
        myApprovalService.insertApproveProce(arrayList);  //退案/留案/停催审批（写入审批记录历史表）

//        审批完推送下一级审批人提醒消息
        if (approvalRecord.getApproveStart().equals(0)) {   //审批通过
            try {
                //                    向资产端发送消息提醒
                R<Long> firstApprove = remoteCaseService.findFirstApprove(approvalRecord.getApproveCode());
                if (approvalRecord.getApproveCode() == 3) {
                    if (firstApprove.getCode() == R.SUCCESS) {
                        messageToolUtils.messageReminderMain(MessageFormats.APPROVAL_STAY, firstApprove.getData());
                    }
                } else if (approvalRecord.getApproveCode() == 5) {
                    if (firstApprove.getCode() == R.SUCCESS) {
                        messageToolUtils.messageReminderMain(MessageFormats.APPROVAL_RETURN, firstApprove.getData());
                    }
                } else if (approvalRecord.getApproveCode() == 4) {
                    if (firstApprove.getCode() == R.SUCCESS) {
                        messageToolUtils.messageReminderMain(MessageFormats.STOP_URGING, firstApprove.getData());
                    }
                }
            } catch (Exception e) {
                log.error("向资产端发送消息提醒 异常", e);
            }

/*
            if (sort == approvalSteps1.get(approvalSteps1.size() - 1).getSort()) {
//                    向资产端发送消息提醒
                R<Long> firstApprove = remoteCaseService.findFirstApprove(approvalRecord.getApproveCode());
                if (approvalRecord.getApproveCode() == 3) {
                    if (firstApprove.getCode() == R.SUCCESS) {
                        messageToolUtils.messageReminderMain(MessageFormats.APPROVAL_STAY, firstApprove.getData());
                    }
                } else if (approvalRecord.getApproveCode() == 5) {
                    if (firstApprove.getCode() == R.SUCCESS) {
                        messageToolUtils.messageReminderMain(MessageFormats.APPROVAL_RETURN, firstApprove.getData());
                    }
                } else if (approvalRecord.getApproveCode() == 4) {
                    if (firstApprove.getCode() == R.SUCCESS) {
                        messageToolUtils.messageReminderMain(MessageFormats.STOP_URGING, firstApprove.getData());
                    }
                }
            } else {
                int number = sort + 1;   //下一级审批人排序
                for (ApprovalSteps approvalStep : approvalSteps1) {
                    if (approvalStep.getSort() == number) {
                        TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
                        teamMessageCenters.setUserId(new Long(approvalStep.getApproverId()));
                        teamMessageCenters.setCreateId(new Long(approvalStep.getCreateId()));
                        if (approvalStep.getApprovalRole().equals("催收端主账号")) {
                            teamMessageCenters.setIdentification(0);
                        } else {
                            teamMessageCenters.setIdentification(1);
                        }
                        if (approvalRecord.getApproveCode() == 3) {  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
                            messageToolUtils.messageReminder(MessageFormats.APPROVAL_STAY, teamMessageCenters);
                        } else if (approvalRecord.getApproveCode() == 5) {
                            messageToolUtils.messageReminder(MessageFormats.APPROVAL_RETURN, teamMessageCenters);
                        } else if (approvalRecord.getApproveCode() == 4) {
                            messageToolUtils.messageReminder(MessageFormats.STOP_URGING, teamMessageCenters);
                        }
                    }
                }
            }*/
        } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
            List<Long> ids1 = approvalRecord.getIds();
            int state = 0;
            if (approvalRecord.getApproveCode() == 3) {
                state = 1;
            } else if (approvalRecord.getApproveCode() == 4) {
                state = 0;
            } else if (approvalRecord.getApproveCode() == 5) {
                state = 2;
            } else {
                throw new GlobalException("审批类型错误");
            }
            List<ApplyRecord> applyRecords = myApprovalService.selectApplyRecordById(ids1, state);

            if (!ObjectUtils.isEmpty(applyRecords)) {
                for (ApplyRecord applyRecord : applyRecords) {
                    TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
                    teamMessageCenters.setIdentification(applyRecord.getOperationType());
                    teamMessageCenters.setUserId(applyRecord.getApplicantId());
                    teamMessageCenters.setCreateId(applyRecord.getTeamId());
                    if (approvalRecord.getApproveCode() == 3) {  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
                        messageToolUtils.messageReminder(MessageFormats.STAY_REVIEW_REMINDER, teamMessageCenters);
                    } else if (approvalRecord.getApproveCode() == 5) {
                        messageToolUtils.messageReminder(MessageFormats.RETURN_REVIEW_REMINDER, teamMessageCenters);
                    } else if (approvalRecord.getApproveCode() == 4) {
                        messageToolUtils.messageReminder(MessageFormats.RETURN_STOP_URGING, teamMessageCenters);
                    }
                }
            }
        }
    }

    /**
     * 资料调取审批（写入历史表并修改申请表中审核状态等）
     *
     * @param
     * @return
     */
    public void dataRetrievalApproval(ApprovalRecord approvalRecord) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (!ObjectUtils.isEmpty(approvalRecord.getFailureReason())) {
            if (approvalRecord.getFailureReason().length() > 300) {
                throw new GlobalException("字数限制为300字,请重新输入");
            }
        }
        if (ObjectUtils.isEmpty(approvalRecord.getIds())) {
            throw new GlobalException("申请id不能为空，至少选择一个审批");
        }
        List<RetrievalRecord> list = new ArrayList<>();

        List<Long> ids = approvalRecord.getIds();   //案件申请记录id集合
        /*int userid = TokenInformation.getUserid();  //登陆人的主键id
        ApprovalSteps approvalSteps = new ApprovalSteps();
        approvalSteps.setCreateId(TokenInformation.getCreateid());  //登录人的团队id
        approvalSteps.setApproveCode(approvalRecord.getApproveCode());  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        List<ApprovalSteps> approvalSteps1 = teamService.selectApprovalSteps(approvalSteps);   //根据团队id以及审核类型查找团队审批流程信息
        int sort = 0;   //登录人审核顺序
        if (!ObjectUtils.isEmpty(approvalSteps1)) {
            for (ApprovalSteps approvalSteps2 : approvalSteps1) {
                if (approvalSteps2.getApproverId().equals(userid)) {
                    sort = approvalSteps2.getSort();
                }
            }
        }
        System.out.println("登录人审核顺序" + sort);
        if (sort == 0) {
            throw new GlobalException("没有该用户的审批流程");
        }*/
        boolean approvalAuthority = teamSysAgService.checkApprovalAuthority(loginUser);
        if (!approvalAuthority) {
            throw new GlobalException("没有该用户的审批流程");
        }
        int sort = 1;
        teamSysAgService.filterDuplicates(ids, sort, approvalRecord.getApproveCode());
        for (Long id : ids) {
            RetrievalRecord retrievalRecord = new RetrievalRecord();
            if (approvalRecord.getApproveStart().equals(0)) {
                //审批通过

                retrievalRecord.setProce(ProceEnum.CSD_END_REVIEW.getCode());
                retrievalRecord.setExamineState("审核中");  //审核状态


            } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
                retrievalRecord.setProce(ProceEnum.END_REVIEW.getCode());  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                retrievalRecord.setExamineState("未通过");  //审核状态
                retrievalRecord.setExamineTime(new Date());  //最后审核时间
            }
            retrievalRecord.setId(id);
            retrievalRecord.setProceSort(sort);  //审核进程顺序
            retrievalRecord.setExamineBy(TokenInformation.getUsername());   //审核人-最后审核人

            Long userid1 = new Long((long) TokenInformation.getUserid());
            retrievalRecord.setExamineById(userid1);   //审核人id
            list.add(retrievalRecord);
        }
        myApprovalService.updateRetrievalRecord(list);    //回款审批（修改申请表中审核状态等）
        List<ApproveProce> arrayList = new ArrayList<>();
        for (Long id : ids) {
            ApproveProce approveProce = new ApproveProce();
            approveProce.setApproveCode(approvalRecord.getApproveCode());  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
            approveProce.setApplyId(id);  //申请id,根据审核类型查找不同的申请表
            approveProce.setApproveStart(approvalRecord.getApproveStart());  //审核状态,0-通过，1-不通过
            approveProce.setApproveTime(new Date());  //审核时间
            approveProce.setReviewer(TokenInformation.getUsername());  //审核人
            approveProce.setReviewerId(TokenInformation.getUserid());  //审核人id
            approveProce.setRefuseReason(approvalRecord.getFailureReason());  //拒绝理由
            approveProce.setApproveSort(sort);  //审核顺序，审核流程中的排序
            approveProce.setDelFlag(BaseConstant.DelFlag_Being);
            approveProce.setOperationType(TokenInformation.getType());  //操作人类型（0-主账号;1-员工账号;2-资产端账号）
            arrayList.add(approveProce);
        }
        myApprovalService.insertApproveProce(arrayList);  //退案/留案/停催审批（写入审批记录历史表）

////        审批完推送下一级审批人提醒消息
//        if (approvalRecord.getApproveStart().equals(0)) {   //审批通过
//            if (sort == approvalSteps1.get(approvalSteps1.size() - 1).getSort()) {
////                向资产端发送消息提醒
//                R<Long> firstApprove = remoteCaseService.findFirstApprove(approvalRecord.getApproveCode());
//                if (firstApprove.getCode() == R.SUCCESS) {
//                    messageToolUtils.messageReminderMain(MessageFormats.APPROVAL_REPAYMENT, firstApprove.getData());
//                }
//            } else {
//                int number = sort + 1;   //下一级审批人排序
//                for (ApprovalSteps approvalStep : approvalSteps1) {
//                    if (approvalStep.getSort().intValue() == number) {
//                        TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
//                        teamMessageCenters.setUserId(new Long(approvalStep.getApproverId()));
//                        teamMessageCenters.setCreateId(new Long(approvalStep.getCreateId()));
//                        if (approvalStep.getApprovalRole().equals("催收端主账号")) {
//                            teamMessageCenters.setIdentification(0);
//                        } else {
//                            teamMessageCenters.setIdentification(1);
//                        }
//                        messageToolUtils.messageReminder(MessageFormats.APPROVAL_REPAYMENT, teamMessageCenters);
//                    }
//                }
//            }
//        } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
//            List<Long> ids1 = approvalRecord.getIds();
//            List<RepaymentRecord> list1 = myApprovalService.selectRepaymentRecordById(ids1);
//            if (!ObjectUtils.isEmpty(list1)) {
//                for (RepaymentRecord repaymentRecord : list1) {
//                    TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
//                    teamMessageCenters.setIdentification(repaymentRecord.getOperationType());
//                    teamMessageCenters.setUserId(repaymentRecord.getRegistrarId());
//                    teamMessageCenters.setCreateId(new Long(repaymentRecord.getTeamId()));
//                    messageToolUtils.messageReminder(MessageFormats.REPAYMENT_REVIEW_REMINDER, teamMessageCenters);
//                }
//            }
//        }
    }

    /**
     * 回款审批（写入历史表并修改申请表中审核状态等）
     *
     * @param
     * @return
     */
    public void WriteCollectionApproval(ApprovalRecord approvalRecord) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (!ObjectUtils.isEmpty(approvalRecord.getFailureReason())) {
            if (approvalRecord.getFailureReason().length() > 300) {
                throw new GlobalException("字数限制为300字,请重新输入");
            }
        }
        if (ObjectUtils.isEmpty(approvalRecord.getIds())) {
            throw new GlobalException("申请id不能为空，至少选择一个审批");
        }
        List<RepaymentRecord> list = new ArrayList<>();

        List<Long> ids = approvalRecord.getIds();   //案件申请记录id集合
        int sort = teamSysAgService.getApprovalSort(loginUser);
        teamSysAgService.filterDuplicates(ids, sort, approvalRecord.getApproveCode());
        Iterator<Long> iterator = ids.iterator();
        while (iterator.hasNext()) {
            Long id = iterator.next();
            RepaymentRecord repaymentRecord = new RepaymentRecord();
            if (approvalRecord.getApproveStart().equals(0)) {   //审批通过
                RepaymentRecord repaymentRecord1 = recordService.selectRepaymentRecordById(id);
                if ("待上传凭证".equals(repaymentRecord1.getExamineState())) {

                    //判断是否已上传凭证
                    if (StringUtils.isEmpty(repaymentRecord1.getRepaymentProof())) {
                        throw new GlobalException("凭证还未上传，请上传凭证后再操作！");
                    }
                    repaymentRecord.setExamineState("待审核");  //审核状态
                    repaymentRecord.setProce(0);
                    iterator.remove();

                } else {
                   /* if (sort == approvalSteps1.get(approvalSteps1.size() - 1).getSort()) {
                        repaymentRecord.setProce(2);  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                    } else {
                        repaymentRecord.setProce(1);  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                    }*/
                    repaymentRecord.setProce(ProceEnum.CSD_END_REVIEW.getCode());
                    repaymentRecord.setExamineState("审核中");  //审核状态
                }

            } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
                repaymentRecord.setProce(ProceEnum.END_REVIEW.getCode());  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                repaymentRecord.setExamineState("未通过");  //审核状态
                repaymentRecord.setUpdateTime(new Date());  //最后审核时间
            }
            repaymentRecord.setId(id);
            repaymentRecord.setProceSort(sort);  //审核进程顺序
            repaymentRecord.setUpdateBy(TokenInformation.getUsername());   //审核人-最后审核人

            Long userid1 = new Long((long) TokenInformation.getUserid());
            repaymentRecord.setUpdateById(userid1);   //审核人id
            list.add(repaymentRecord);
        }
        myApprovalService.updateRepaymentRecord(list);    //回款审批（修改申请表中审核状态等）
        List<ApproveProce> arrayList = new ArrayList<>();
        for (Long id : ids) {
            ApproveProce approveProce = new ApproveProce();
            approveProce.setApproveCode(approvalRecord.getApproveCode());  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
            approveProce.setApplyId(id);  //申请id,根据审核类型查找不同的申请表
            approveProce.setApproveStart(approvalRecord.getApproveStart());  //审核状态,0-通过，1-不通过
            approveProce.setApproveTime(new Date());  //审核时间
            approveProce.setReviewer(TokenInformation.getUsername());  //审核人
            approveProce.setReviewerId(TokenInformation.getUserid());  //审核人id
            approveProce.setRefuseReason(approvalRecord.getFailureReason());  //拒绝理由
            approveProce.setApproveSort(sort);  //审核顺序，审核流程中的排序
            approveProce.setDelFlag(BaseConstant.DelFlag_Being);
            approveProce.setOperationType(TokenInformation.getType());  //操作人类型（0-主账号;1-员工账号;2-资产端账号）
            arrayList.add(approveProce);
        }
        if (arrayList.size() > 0) {
            myApprovalService.insertApproveProce(arrayList);  //退案/留案/停催审批（写入审批记录历史表）
        }

//        审批完推送下一级审批人提醒消息
        if (approvalRecord.getApproveStart().equals(0)) {   //审批通过
            try {
                //向资产端发送消息提醒
                R<Long> firstApprove = remoteCaseService.findFirstApprove(approvalRecord.getApproveCode());
                if (firstApprove.getCode() == R.SUCCESS) {
                    messageToolUtils.messageReminderMain(MessageFormats.APPROVAL_REPAYMENT, firstApprove.getData());
                }
            } catch (Exception e) {
                log.error("回款审批 向资产端发送消息提醒 异常", e);
            }


            /*if (sort == approvalSteps1.get(approvalSteps1.size() - 1).getSort()) {
//                向资产端发送消息提醒
                R<Long> firstApprove = remoteCaseService.findFirstApprove(approvalRecord.getApproveCode());
                if (firstApprove.getCode() == R.SUCCESS) {
                    messageToolUtils.messageReminderMain(MessageFormats.APPROVAL_REPAYMENT, firstApprove.getData());
                }
            } else {
                int number = sort + 1;   //下一级审批人排序
                for (ApprovalSteps approvalStep : approvalSteps1) {
                    if (approvalStep.getSort().intValue() == number) {
                        TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
                        teamMessageCenters.setUserId(new Long(approvalStep.getApproverId()));
                        teamMessageCenters.setCreateId(new Long(approvalStep.getCreateId()));
                        if (approvalStep.getApprovalRole().equals("催收端主账号")) {
                            teamMessageCenters.setIdentification(0);
                        } else {
                            teamMessageCenters.setIdentification(1);
                        }
                        messageToolUtils.messageReminder(MessageFormats.APPROVAL_REPAYMENT, teamMessageCenters);
                    }
                }
            }*/
        } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
            List<Long> ids1 = approvalRecord.getIds();
            List<RepaymentRecord> list1 = myApprovalService.selectRepaymentRecordById(ids1);
            if (!ObjectUtils.isEmpty(list1)) {
                for (RepaymentRecord repaymentRecord : list1) {
                    TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
                    teamMessageCenters.setIdentification(repaymentRecord.getOperationType());
                    teamMessageCenters.setUserId(repaymentRecord.getRegistrarId());
                    teamMessageCenters.setCreateId(new Long(repaymentRecord.getTeamId()));
                    messageToolUtils.messageReminder(MessageFormats.REPAYMENT_REVIEW_REMINDER, teamMessageCenters);
                }
            }
        }
    }

    /**
     * 减免审批（写入历史表并修改申请表中审核状态等）
     *
     * @param
     * @return
     */
    public void ExemptionApproval(ApprovalRecord approvalRecord) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (!ObjectUtils.isEmpty(approvalRecord.getFailureReason())) {
            if (approvalRecord.getFailureReason().length() > 300) {
                throw new GlobalException("字数限制为300字,请重新输入");
            }
        }
        if (ObjectUtils.isEmpty(approvalRecord.getIds())) {
            throw new GlobalException("申请id不能为空，至少选择一个审批");
        }
        List<ReductionRecord> list = new ArrayList<>();

        List<Long> ids = approvalRecord.getIds();   //案件申请记录id集合
        if (ObjectUtils.isEmpty(ids)) {
            throw new GlobalException("申请id不能为空");
        }

        int sort = teamSysAgService.getApprovalSort(loginUser);
        teamSysAgService.filterDuplicates(ids, sort, approvalRecord.getApproveCode());
        for (Long id : ids) {
            ReductionRecord reductionRecord = new ReductionRecord();
            if (approvalRecord.getApproveStart().equals(0)) {   //审批通过
                reductionRecord.setProce(ProceEnum.CSD_END_REVIEW.getCode());
                reductionRecord.setState("审核中");  //审核状态

            } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
                reductionRecord.setProce(ProceEnum.END_REVIEW.getCode());  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                reductionRecord.setState("未通过");  //审核状态
                reductionRecord.setUpdateTime(new Date());  //最后审核时间
            }

            reductionRecord.setId(id);
            reductionRecord.setProceSort(sort);  //审核进程顺序
            reductionRecord.setExamineBy(TokenInformation.getUsername());   //审核人-最后审核人

            Long userid1 = new Long((long) TokenInformation.getUserid());
            reductionRecord.setExamineById(userid1);   //审核人id
            list.add(reductionRecord);
        }
        myApprovalService.updateReductionRecord(list);    //减免审批（修改申请表中审核状态等）
        List<ApproveProce> arrayList = new ArrayList<>();
        for (Long id : ids) {
            ApproveProce approveProce = new ApproveProce();
            approveProce.setApproveCode(approvalRecord.getApproveCode());  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
            approveProce.setApplyId(id);  //申请id,根据审核类型查找不同的申请表
            approveProce.setApproveStart(approvalRecord.getApproveStart());  //审核状态,0-通过，1-不通过
            approveProce.setApproveTime(new Date());  //审核时间
            approveProce.setReviewer(TokenInformation.getUsername());  //审核人
            approveProce.setReviewerId(TokenInformation.getUserid());  //审核人id
            approveProce.setRefuseReason(approvalRecord.getFailureReason());  //拒绝理由
            approveProce.setApproveSort(sort);  //审核顺序，审核流程中的排序
            approveProce.setDelFlag(BaseConstant.DelFlag_Being);
            approveProce.setOperationType(TokenInformation.getType());  //操作人类型（0-主账号;1-员工账号;2-资产端账号）
            arrayList.add(approveProce);
        }
        myApprovalService.insertApproveProce(arrayList);  //退案/留案/停催审批（写入审批记录历史表）

//        审批完推送下一级审批人提醒消息
        if (approvalRecord.getApproveStart().equals(0)) {   //审批通过
            try {
                //向资产端发送消息提醒
                R r = remoteCaseService.autoReduction(ids, SecurityConstants.INNER);
                R<Long> firstApprove = remoteCaseService.findFirstApprove(approvalRecord.getApproveCode());
                if (firstApprove.getCode() == R.SUCCESS) {
                    messageToolUtils.messageReminderMain(MessageFormats.APPROVAL_REDUCTION, firstApprove.getData());
                }
            } catch (Exception e) {
                log.error("减免审批 向资产端发送消息提醒", e);
            }
        } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
            List<Long> ids1 = approvalRecord.getIds();
            List<ReductionRecord> reductionRecords = myApprovalService.selectReductionRecordById(ids1);
            if (!ObjectUtils.isEmpty(reductionRecords)) {
                for (ReductionRecord reductionRecord : reductionRecords) {
                    TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
                    teamMessageCenters.setIdentification(reductionRecord.getOperationType());
                    teamMessageCenters.setUserId(reductionRecord.getApplicantId());
                    teamMessageCenters.setCreateId(new Long(reductionRecord.getTeamId()));
                    messageToolUtils.messageReminder(MessageFormats.REDUCTION_REVIEW_REMINDER, teamMessageCenters);
                }
            }
        }
    }


    /**
     * 分期还款审批（写入历史表并修改申请表中审核状态等）
     *
     * @param
     * @return
     */
    public void InstallmentApproval(ApprovalRecord approvalRecord) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (!ObjectUtils.isEmpty(approvalRecord.getFailureReason())) {
            if (approvalRecord.getFailureReason().length() > 300) {
                throw new GlobalException("字数限制为300字,请重新输入");
            }
        }
        if (ObjectUtils.isEmpty(approvalRecord.getIds())) {
            throw new GlobalException("申请id不能为空，至少选择一个审批");
        }
        List<StagingRecord> list = new ArrayList<>();

        List<Long> ids = approvalRecord.getIds();   //案件申请记录id集合
      /*  int userid = TokenInformation.getUserid();  //登陆人的主键id
        ApprovalSteps approvalSteps = new ApprovalSteps();
        approvalSteps.setCreateId(TokenInformation.getCreateid());  //登录人的团队id
        approvalSteps.setApproveCode(approvalRecord.getApproveCode());  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        List<ApprovalSteps> approvalSteps1 = teamService.selectApprovalSteps(approvalSteps);   //根据团队id以及审核类型查找团队审批流程信息
        int sort = 0;   //登录人审核顺序
        if (!ObjectUtils.isEmpty(approvalSteps1)) {
            for (ApprovalSteps approvalSteps2 : approvalSteps1) {
                if (approvalSteps2.getApproverId().equals(userid)) {
                    sort = approvalSteps2.getSort();
                }
            }
        }
        if (sort == 0) {
            throw new GlobalException("没有该用户的审批流程");
        }*/

        Integer sort = teamSysAgService.getApprovalSort(loginUser);
        teamSysAgService.filterDuplicates(ids, sort, approvalRecord.getApproveCode());
        for (Long id : ids) {
            StagingRecord stagingRecord = new StagingRecord();
            if (approvalRecord.getApproveStart().equals(0)) {   //审批通过

               /* if (sort == approvalSteps1.get(approvalSteps1.size() - 1).getSort()) {
                    stagingRecord.setProce(2);  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                } else {
                    stagingRecord.setProce(1);  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                }*/
                stagingRecord.setProce(ProceEnum.CSD_END_REVIEW.getCode());
                stagingRecord.setExamineState("审核中");  //审核状态
            } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
                stagingRecord.setProce(ProceEnum.END_REVIEW.getCode());  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                stagingRecord.setExamineState("未通过");  //审核状态
                stagingRecord.setExamineTime(new Date());  //最后审核时间
            }
            stagingRecord.setId(id);
            stagingRecord.setProceSort(sort);  //审核进程顺序
            stagingRecord.setExamineBy(TokenInformation.getUsername());   //审核人-最后审核人

            Long userid1 = new Long((long) TokenInformation.getUserid());
            stagingRecord.setExamineById(userid1);   //审核人id
            list.add(stagingRecord);
        }
        myApprovalService.updateStagingRecord(list);    //减免审批（修改申请表中审核状态等）
        List<ApproveProce> arrayList = new ArrayList<>();
        for (Long id : ids) {
            ApproveProce approveProce = new ApproveProce();
            approveProce.setApproveCode(approvalRecord.getApproveCode());  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
            approveProce.setApplyId(id);  //申请id,根据审核类型查找不同的申请表
            approveProce.setApproveStart(approvalRecord.getApproveStart());  //审核状态,0-通过，1-不通过
            approveProce.setApproveTime(new Date());  //审核时间
            approveProce.setReviewer(TokenInformation.getUsername());  //审核人
            approveProce.setReviewerId(TokenInformation.getUserid());  //审核人id
            approveProce.setRefuseReason(approvalRecord.getFailureReason());  //拒绝理由
            approveProce.setApproveSort(sort);  //审核顺序，审核流程中的排序
            approveProce.setDelFlag("0");
            approveProce.setOperationType(TokenInformation.getType());  //操作人类型（0-主账号;1-员工账号;2-资产端账号）
            arrayList.add(approveProce);
        }
        myApprovalService.insertApproveProce(arrayList);  //（写入审批记录历史表）

//        审批完推送下一级审批人提醒消息
        if (approvalRecord.getApproveStart().equals(0)) {   //审批通过
            try {
                //向资产端发送消息提醒
                R<Long> firstApprove = remoteCaseService.findFirstApprove(approvalRecord.getApproveCode());
                if (firstApprove.getCode() == R.SUCCESS) {
                    messageToolUtils.messageReminderMain(MessageFormats.APPROVAL_STAGING, firstApprove.getData());
                }
            } catch (Exception e) {
                log.error("分期还款审批 向资产端发送消息提醒 异常", e);
            }
        } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
            List<Long> ids1 = approvalRecord.getIds();
            List<StagingRecord> stagingRecords = myApprovalService.selectStagingRecordById(ids1);
            if (!ObjectUtils.isEmpty(stagingRecords)) {
                for (StagingRecord stagingRecord : stagingRecords) {
                    TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
                    teamMessageCenters.setIdentification(stagingRecord.getOperationType());
                    teamMessageCenters.setUserId(stagingRecord.getApplicantId());
                    teamMessageCenters.setCreateId(stagingRecord.getTeamId());
                    messageToolUtils.messageReminder(MessageFormats.STAGING_REVIEW_REMINDER, teamMessageCenters);
                }
            }
        }
    }

    /**
     * 外访审批（写入历史表并修改申请表中审核状态等）
     *
     * @param
     * @return
     */
    public void updateOutsideRecord(ApprovalRecord approvalRecord) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (!ObjectUtils.isEmpty(approvalRecord.getFailureReason())) {
            if (approvalRecord.getFailureReason().length() > 300) {
                throw new GlobalException("字数限制为300字,请重新输入");
            }
        }
        if (ObjectUtils.isEmpty(approvalRecord.getIds())) {
            throw new GlobalException("申请id不能为空，至少选择一个审批");
        }
        List<OutsideRecord> list = new ArrayList<>();

        List<Long> ids = approvalRecord.getIds();   //案件申请记录id集合

        Integer sort = teamSysAgService.getApprovalSort(loginUser);
        teamSysAgService.filterDuplicates(ids, sort, approvalRecord.getApproveCode());
        for (Long id : ids) {
            OutsideRecord outsideRecord = new OutsideRecord();
            if (approvalRecord.getApproveStart().equals(0)) {   //审批通过

               /* if (sort == approvalSteps1.get(approvalSteps1.size() - 1).getSort()) {
                    outsideRecord.setProce(2);  //审核进程，0-待审核，1-催收端审核中，2-审核结束
                    outsideRecord.setState("已通过");  //审核状态
                    outsideRecord.setExamineTime(new Date());  //最后审核时间
                    outsideRecord.setStateCode(3);  //状态码，0-待审核，1-审核中，2-未通过，3-已通过，4-已签到，5-已完成，6-已撤销
                } else {
                    outsideRecord.setProce(1);  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
                    outsideRecord.setState("审核中");  //审核状态
                    outsideRecord.setStateCode(1);  //状态码，0-待审核，1-审核中，2-未通过，3-已通过，4-已签到，5-已完成，6-已撤销
                }*/

                outsideRecord.setProce(ProceEnum.CSD_END_REVIEW.getCode());  //审核进程，0-待审核，1-催收端审核中，2-审核结束
                outsideRecord.setState("已通过");  //审核状态
                outsideRecord.setExamineTime(new Date());  //最后审核时间
                outsideRecord.setStateCode(3);  //状态码，0-待审核，1-审核中，2-未通过，3-已通过，4-已签到，5-已完成，6-已撤销

            } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
                outsideRecord.setProce(2);  //审核进程，0-待审核，1-催收端审核中，2-审核结束
                outsideRecord.setState("未通过");  //审核状态
                outsideRecord.setExamineTime(new Date());  //最后审核时间
                outsideRecord.setStateCode(2);  //状态码，0-待审核，1-审核中，2-未通过，3-已通过，4-已签到，5-已完成，6-已撤销
            }
            outsideRecord.setId(id);
            outsideRecord.setProceSort(sort);  //审核进程顺序
            outsideRecord.setExamineBy(TokenInformation.getUsername());   //审核人-最后审核人

            Integer userid1 = TokenInformation.getUserid();
            outsideRecord.setExamineById(userid1);   //审核人id
            list.add(outsideRecord);
        }
        myApprovalService.updateOutsideRecord(list);    //外访审批（修改申请表中审核状态等）
        List<ApproveProce> arrayList = new ArrayList<>();
        for (Long id : ids) {
            ApproveProce approveProce = new ApproveProce();
            approveProce.setApproveCode(approvalRecord.getApproveCode());  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
            approveProce.setApplyId(id);  //申请id,根据审核类型查找不同的申请表
            approveProce.setApproveStart(approvalRecord.getApproveStart());  //审核状态,0-通过，1-不通过
            approveProce.setApproveTime(new Date());  //审核时间
            approveProce.setReviewer(TokenInformation.getUsername());  //审核人
            approveProce.setReviewerId(TokenInformation.getUserid());  //审核人id
            approveProce.setRefuseReason(approvalRecord.getFailureReason());  //拒绝理由
            approveProce.setApproveSort(sort);  //审核顺序，审核流程中的排序
            approveProce.setDelFlag("0");
            approveProce.setOperationType(TokenInformation.getType());  //操作人类型（0-主账号;1-员工账号;2-资产端账号）
            arrayList.add(approveProce);
        }
        myApprovalService.insertApproveProce(arrayList);  //（写入审批记录历史表）

//        审批完推送下一级审批人提醒消息
        if (approvalRecord.getApproveStart().equals(0)) {   //审批通过
            try {
                List<Long> ids1 = approvalRecord.getIds();
                List<OutsideRecord> outsideRecords = myApprovalService.selectOutsideRecordById(ids1);
                if (!ObjectUtils.isEmpty(outsideRecords)) {
                    for (OutsideRecord outsideRecord : outsideRecords) {
                        TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
                        teamMessageCenters.setIdentification(outsideRecord.getOperationType());
                        teamMessageCenters.setUserId(new Long(outsideRecord.getCreateById()));
                        teamMessageCenters.setCreateId(new Long(outsideRecord.getTeamId()));
                        messageToolUtils.messageReminder(MessageFormats.OUTSIDE_REVIEW_REMINDER, teamMessageCenters);
                        String odvId = outsideRecord.getOdvId();
                        List<Long> list1 = SplitUtils.strSplitToLong(odvId, ",");
                        for (Long lists : list1) {
                            TeamMessageCenters teamMessageCenter = new TeamMessageCenters();
                            teamMessageCenter.setUserId(lists);
                            teamMessageCenter.setCreateId(new Long(outsideRecord.getTeamId()));
                            teamMessageCenter.setIdentification(1);
                            messageToolUtils.messageReminder(MessageFormats.OUTSIDE_REMINDER, teamMessageCenter);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("外访审批 发送消息提示异常", e);
            }
            /*if (sort == approvalSteps1.get(approvalSteps1.size() - 1).getSort()) {
                List<Long> ids1 = approvalRecord.getIds();
                List<OutsideRecord> outsideRecords = myApprovalService.selectOutsideRecordById(ids1);
                if (!ObjectUtils.isEmpty(outsideRecords)) {
                    for (OutsideRecord outsideRecord : outsideRecords) {
                        TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
                        teamMessageCenters.setIdentification(outsideRecord.getOperationType());
                        teamMessageCenters.setUserId(new Long(outsideRecord.getCreateById()));
                        teamMessageCenters.setCreateId(new Long(outsideRecord.getTeamId()));
                        messageToolUtils.messageReminder(MessageFormats.OUTSIDE_REVIEW_REMINDER, teamMessageCenters);
                        String odvId = outsideRecord.getOdvId();
                        List<Long> list1 = SplitUtils.strSplitToLong(odvId, ",");
                        for (Long lists : list1) {
                            TeamMessageCenters teamMessageCenter = new TeamMessageCenters();
                            teamMessageCenter.setUserId(lists);
                            teamMessageCenter.setCreateId(new Long(outsideRecord.getTeamId()));
                            teamMessageCenter.setIdentification(1);
                            messageToolUtils.messageReminder(MessageFormats.OUTSIDE_REMINDER, teamMessageCenter);
                        }
                    }
                }
            } else {
                int number = sort + 1;   //下一级审批人排序
                for (ApprovalSteps approvalStep : approvalSteps1) {
                    if (approvalStep.getSort() == number) {
                        TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
                        teamMessageCenters.setUserId(new Long(approvalStep.getApproverId()));
                        teamMessageCenters.setCreateId(new Long(approvalStep.getCreateId()));
                        if (approvalStep.getApprovalRole().equals("催收端主账号")) {
                            teamMessageCenters.setIdentification(0);
                        } else {
                            teamMessageCenters.setIdentification(1);
                        }
                        messageToolUtils.messageReminder(MessageFormats.APPROVAL_OUTSIDE, teamMessageCenters);
                    }
                }
            }*/
        } else if (approvalRecord.getApproveStart().equals(1)) {  //审批不通过
            List<Long> ids1 = approvalRecord.getIds();
            List<OutsideRecord> outsideRecords = myApprovalService.selectOutsideRecordById(ids1);
            if (!ObjectUtils.isEmpty(outsideRecords)) {
                for (OutsideRecord outsideRecord : outsideRecords) {
                    TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
                    teamMessageCenters.setIdentification(outsideRecord.getOperationType());
                    teamMessageCenters.setUserId(new Long(outsideRecord.getCreateById()));
                    teamMessageCenters.setCreateId(new Long(outsideRecord.getTeamId()));
                    messageToolUtils.messageReminder(MessageFormats.OUTSIDE_REVIEW_REMINDER, teamMessageCenters);
                }
            }
        }
    }

    /**
     * 判断数据是否需要脱敏
     *
     * @param exportReminder
     */
    public List<CaseManage> dataDesensitization(ExportReminder exportReminder) {

        List<CaseManage> caseManages = caseService.selectCaseManage(exportReminder);

        Desensitization desensitization = desensitizationAgService.getDesensitization(exportReminder.getLoginUser());
        for (CaseManage caseManage : caseManages) {
            //解密
            DecryptUtils.dataDecrypt(caseManage);
            if (desensitization != null) {
                if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(caseManage.getClientName())) {
                    caseManage.setClientName(DataMaskingUtils.nameMasking(caseManage.getClientName()));
                }
                if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(caseManage.getClientPhone())) {
                    caseManage.setClientPhone(DataMaskingUtils.phoneMasking(caseManage.getClientPhone()));
                }
                if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(caseManage.getClientIdcard())) {
                    caseManage.setClientIdcard(DataMaskingUtils.idMasking(caseManage.getClientIdcard()));
                }
                if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(caseManage.getClientCensusRegister())) {
                    caseManage.setClientCensusRegister(DataMaskingUtils.Masking(caseManage.getClientCensusRegister()));
                }
                if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(caseManage.getRegisteredAddress())) {
                    caseManage.setRegisteredAddress(DataMaskingUtils.Masking(caseManage.getRegisteredAddress()));
                }
                if (desensitization.getHomeAddress() == 1 && !ObjectUtils.isEmpty(caseManage.getHomeAddress())) {
                    caseManage.setHomeAddress(DataMaskingUtils.Masking(caseManage.getHomeAddress()));
                }
                if (desensitization.getEntityName() == 1 && !ObjectUtils.isEmpty(caseManage.getPlaceOfWork())) {
                    caseManage.setPlaceOfWork(DataMaskingUtils.Masking(caseManage.getPlaceOfWork()));
                }
            }
        }

        return caseManages;
    }


    /**
     * 判断数据是否需要脱敏
     * 导出案件字段
     */
 /*   public List<ExportCasePojo> exportCase(ExportReminder exportReminder) {
        List<CaseManage> caseManages = caseService.selectCaseManage(exportReminder);

        Desensitization desensitization = null;
        if (!ObjectUtils.isEmpty(caseManages)) {
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                desensitization = stateDesensitization.getDesensitization();
            }
        }

        ArrayList<ExportCasePojo> exportCases = new ArrayList<>();
        for (CaseManage caseManage : caseManages) {
            ExportCasePojo exportCase = new ExportCasePojo();
            if(caseManage.getSettlementStatus()==1){
                exportCase.setSettlementStatus("已结清");
            }else{
                exportCase.setSettlementStatus("未结清");
            }
            InfoLoan infoLoan = caseService.selectCaseInfoLoan(caseManage.getCaseId());
            if(infoLoan.getClearDate()!=null){
                String format1 = new SimpleDateFormat("yyyy-MM-dd").format(infoLoan.getClearDate());
                exportCase.setClearDate(format1);
            }

            exportCase.setYcContractNo(caseManage.getContractNo());
            exportCase.setYcOverdueDays(infoLoan.getYcOverdueDays());
            *//*Date amountFinalDate = infoLoan.getAmountFinalDate();
            if(amountFinalDate!=null){
                String format = new SimpleDateFormat("yyyy-MM-dd").format(amountFinalDate);
                exportCase.setAmountFinalDate(format);
            }*//*

           Date returnCaseDate= caseManage.getEntrustingCaseDate();
            if(returnCaseDate!=null){
                Date amountFinalDate = DateUtil.offsetDay(returnCaseDate, -1);
                exportCase.setAmountFinalDate(DateUtil.format(amountFinalDate, DateUtils.YYYY_MM_DD));
            }
            //exportCase.setRepaymenDate();

            //解密
            DecryptUtils.dataDecrypt(caseManage);
            if (desensitization != null) {
                if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(caseManage.getClientName()))
                    caseManage.setClientName(DataMaskingUtils.nameMasking(caseManage.getClientName()));
                if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(caseManage.getClientPhone()))
                    caseManage.setClientPhone(DataMaskingUtils.phoneMasking(caseManage.getClientPhone()));
                if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(caseManage.getClientIdcard()))
                    caseManage.setClientIdcard(DataMaskingUtils.idMasking(caseManage.getClientIdcard()));
                if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(caseManage.getClientCensusRegister()))
                    caseManage.setClientCensusRegister(DataMaskingUtils.Masking(caseManage.getClientCensusRegister()));
            }
            exportCase.setClientName(caseManage.getClientName());
            exportCase.setClientPhone(caseManage.getClientPhone());
            exportCases.add(exportCase);
        }

        return exportCases;
    }
*/

    /**
     * 判断是否满足目标
     *
     * @param mode
     * @param team
     * @return
     */
    public boolean checkIsReachedDistri(int mode, Distribution team) {
        //mode=0;//测试 可能需要删除
        //（0-按案件数量分配，1-按案件金额分配，2-综合（案件数量优先））
        switch (mode) {
            case 0:
            case 2:
                if (new BigDecimal(team.getNumber()).compareTo(team.getNumericalValue()) >= 0) {
                    return true;
                }
                break;
            case 1:
                if (team.getMoney().compareTo(team.getNumericalValue()) >= 0) {
                    return true;
                }
                break;
            default:
                return false;
        }
        return false;
    }


    /**
     * 判断案件是否属于当前团队
     *
     * @param caseId 案件ID
     * @return true-是，false-否（不属于当前团队）
     */
    public boolean checkCaseBelongToTeam(Long caseId) {
        CaseManage caseManage = recordService.selectCaseManageCaseId(caseId);
        if (ObjectUtils.isEmpty(caseManage)) {
            //找不到案件，案件已删除
            return false;
        }
        //案件当前的委案团队
        Long caseOutsourcingTeamId = caseManage.getOutsourcingTeamId();
        if (caseOutsourcingTeamId == null) {
            //当前委案团队为null 表示为委案，肯定不属于当前团队
            return false;
        }

        if (ObjectUtil.equals(caseOutsourcingTeamId.longValue(), TokenInformation.getCreateid().longValue())) {
            //案件当前的委案团队 的 用户当前所在的团队
            return true;
        }
        return false;
    }

    /**
     * 调诉端批量发送生成短信模板短信
     *
     * @return
     */
    public SendRecordsPojos previewTemplate(Map<String, Object> hashMap) {
        SendRecordsPojos sendRecordsPojos = new SendRecordsPojos();
        int sendNum1 = 0; // 已结清案件数量
        int sendNum2 = 0; // 重复发送案件数量
        int sendNum3 = 0; // 共债数量
        List<String> stringList = new ArrayList<>();
//        String templateContent = smsCustomPojo.getTemplateContent();
        String templateContent = (String) hashMap.get("templateContent");//短信模版
        Integer templateId = (Integer) hashMap.get("templateId");// 模版id
        Integer listOrDetails = (Integer) hashMap.get("listOrDetails");//1-列表批量发送，2-案件详情单条发送
        Integer contactId = (Integer) hashMap.get("contactId");// 联系人id
        Integer caseId = (Integer) hashMap.get("caseId");// 案件id
        com.zws.common.core.domain.sms.TemplateSms template = recordService.selectTemplateById(templateId.longValue());//根据id查询短信模板信息
        Integer createid = TokenInformation.getCreateid();
        if (listOrDetails == 1) {
            List<Long> ids = agLawsuitService.searchIds(hashMap);
            if (ids == null) {
                throw new ServiceException("案件Id不存在");
            }
//                List<SendMsg> sendMsgs = recordService.selectSendMsg(smsCustomPojo.getPhoneMediationPojo());
            Map<String, Object> map = new HashMap<>();
            map.put("ids", ids);
            if (hashMap.containsKey("saveStage")) {
                map.put("saveStage", hashMap.get("saveStage").toString());
            }
            if (hashMap.containsKey("disposeStage")) {
                map.put("disposeStage", hashMap.get("disposeStage").toString());
            }
            if (hashMap.containsKey("mediatedStage")) {
                map.put("mediatedStage", hashMap.get("mediatedStage").toString());
            }
            List<SendMsg> sendMsgs = recordService.selectSendMsgByIds(map);//查询案件信息

            if (ObjectUtils.isEmpty(sendMsgs) || sendMsgs.size() == 0) {
                throw new GlobalException("案件查询为空");
            }
            if (!ObjectUtils.isEmpty(sendMsgs)) {
                for (SendMsg sendMsg : sendMsgs) {
                    //查询24小时内发送成功的短信并且发送状态不为成功的
                    DateTime sendtime1 = DateUtil.beginOfDay(new Date());
                    DateTime sendtime2 = DateUtil.endOfDay(new Date());
                    List<SendRecords> sendRecords = recordService.selectFailMessage(sendMsg.getClientPhone(), sendtime1, sendtime2);
                    //已结清
                    if (sendMsg.getSettlementStatus() == 1) {
                        sendNum1 += 1;
                        continue;
                    }
                    if (sendRecords.size() > 0) {
                        sendNum2 += 1;
                        continue;
                    }
                    if (stringList.contains(sendMsg.getClientIdcard())) {
                        sendNum3 += 1;
                        continue;
                    }
                    stringList.add(sendMsg.getClientIdcard());
                }
            }
        } else if (listOrDetails == 2) {
//                案件详情发送短信
            if (contactId != null) {
                InfoContact infoContact = recordService.selectCwContact(contactId.longValue());
                if (ObjectUtils.isEmpty(infoContact)) {
                    throw new GlobalException("联系人查询为空");
                }

                List<String> bankCardNumbers = new ArrayList<>(); //共债信用卡号后四位
                SmsParameters smsParameter = recordService.selectSmsParameter(infoContact.getCaseId());
                Map<String, String> map = new HashMap<>();
                map.put("clientIdcard", smsParameter.getClientIdcard());
                map.put("fieldKey", FieldEncryptUtil.fieldKey);

                //查询共债案件
                List<String> bankCardNumber = recordService.selectBankCardNumber(map);

                for (String bankCard : bankCardNumber) {
                    bankCardNumbers.add(bankCard.substring(bankCard.length() - 4));
                }
                smsParameter.setBankCardNumber(String.join("/", bankCardNumbers));
                templateContent = contentData(smsParameter, template);
            } else {
                if (ObjectUtils.isEmpty(caseId)) {
                    throw new GlobalException("案件id不能为空");
                }
                SmsParameters smsParameter = recordService.selectSmsParameter(Long.valueOf(caseId));
                ArrayList<String> bankCardNumbers = new ArrayList<>(); //共债信用卡号后四位
                HashMap<String, String> mapStr = new HashMap<>();
                mapStr.put("clientIdcard", smsParameter.getClientIdcard());
                mapStr.put("fieldKey", FieldEncryptUtil.fieldKey);

                //查询共债案件
                List<String> bankCardNumber = recordService.selectBankCardNumber(mapStr);

                for (String bankCard : bankCardNumber) {
                    bankCardNumbers.add(bankCard.substring(bankCard.length() - 4));
                }
                smsParameter.setBankCardNumber(String.join("/", bankCardNumbers));
                templateContent = contentData(smsParameter, template);
            }

        } else {
            throw new GlobalException("必要参数值错误");
        }
        int unSendNum = sendNum1 + sendNum2 + sendNum3;
        sendRecordsPojos.setUnSendNum(unSendNum);
        sendRecordsPojos.setSendNum(stringList.size());
        sendRecordsPojos.setSendNum1(sendNum1);
        sendRecordsPojos.setSendNum2(sendNum2);
        sendRecordsPojos.setSendNum3(sendNum3);
        sendRecordsPojos.setTemplateContent(templateContent);
        return sendRecordsPojos;
    }

    /**
     * 根据模板设置发送短信内容
     *
     * @param smsParameters
     * @param templateSms
     * @return
     */
    public String contentData(SmsParameters smsParameters, com.zws.common.core.domain.sms.TemplateSms templateSms) {
        String autographName = templateSms.getAutographName();  //短信签名内容
        String templateContent = templateSms.getTemplateContent();  //短信模板内容

        templateContent = templateContent.replace("[借款人]", smsParameters.getCustomerName());
        templateContent = templateContent.replace("[初始债权总额]", smsParameters.getInitialClaim().toString());
        templateContent = templateContent.replace("[初始本金余额]", smsParameters.getInitialPrincipal().toString());
        templateContent = templateContent.replace("[初始本息余额]", smsParameters.getInitialInterest().toString());
        templateContent = templateContent.replace("[转让方]", smsParameters.getTransferor());
        templateContent = templateContent.replace("[产品类型]", smsParameters.getProductType());
        templateContent = templateContent.replace("[合同号]", smsParameters.getContractNo());
        templateContent = templateContent.replace("[借据号]", smsParameters.getReceiptNo());
        templateContent = templateContent.replace("[开户行]", smsParameters.getBankName());
        templateContent = templateContent.replace("[开户名]", smsParameters.getAccountName());
        templateContent = templateContent.replace("[还款账号]", smsParameters.getAccountNumber());
        templateContent = templateContent.replace("[银行卡号后4位]", smsParameters.getBankCardNumber());

        String day = "";
        if (smsParameters.getYcOverdueDays() == null) {
            day = "0";
        } else {
            day = smsParameters.getYcOverdueDays().toString();
        }
        templateContent = templateContent.replace("[逾期天数]", day);
        String strDue = smsParameters.getRemainingDue() == null ? "" : smsParameters.getRemainingDue().toString();
        templateContent = templateContent.replace("[剩余应还总额]", strDue);

        String str = "【" + autographName + "】" + templateContent;
        return str;
    }

    /**
     * 批量发送短信
     *
     * @return
     */
    @Async
    public void sendCwMessage(Map<String, Object> hashMap, com.zws.common.core.domain.sms.ThreadEntityPojo threadEntityPojo,LoginUser loginUser) {
        try {
            Integer templateId = (Integer) hashMap.get("templateId");// 模版id
            com.zws.common.core.domain.sms.TemplateSms templateSms = recordService.selectTemplateById(templateId.longValue());

            // 发送短信过滤数据
            hashMap.put("teamId", threadEntityPojo.getCreateId());
            hashMap.put("odvId", threadEntityPojo.getUserId());
            List<String> caseIds = sendCuiShouMessage(hashMap);
            List<Long> ids = new ArrayList<>();
            if (ObjectUtils.isEmpty(caseIds)) {
//            throw new GlobalException("短信提交失败：24小时内该手机号码已经发过短信");
                return;
            }
            List<String> phoneList = new ArrayList<>(); // 过滤相同手机号

            for (String caseId : caseIds) {
                SmsParameters smsParameters = recordService.selectSmsParameter(Long.valueOf(caseId));
                ids.add(Long.valueOf(caseId));
                if (phoneList.contains(smsParameters.getClientPhone())) {
                    continue;
                }
                phoneList.add(smsParameters.getClientPhone());

                List<String> bankCardNumbers = new ArrayList<>(); //共债信用卡号后四位
                Map<String, String> map = new HashMap<>();
                map.put("clientIdcard", smsParameters.getClientIdcard());
                map.put("fieldKey", FieldEncryptUtil.fieldKey);

                //查询共债案件
                List<String> bankCardNumber = recordService.selectBankCardNumber(map);

                for (String bankCard : bankCardNumber) {
                    bankCardNumbers.add(bankCard.substring(bankCard.length() - 4));
                }
                smsParameters.setBankCardNumber(String.join("/", bankCardNumbers));

                smsParameters.setUserName(threadEntityPojo.getUser());
                smsParameters.setUserId(threadEntityPojo.getUserId().longValue());
                smsParameters.setRelationship("本人");
                smsParameters.setThreadEntityPojo(threadEntityPojo);
                SendMessagePojo sendMessagePojo = new SendMessagePojo();
                sendMessagePojo.setTemplateSms(templateSms);
                sendMessagePojo.setSmsParameters(smsParameters);
                // 发送短信
                R rSmsResponseVo = remoteSmsService.sendAppealMessages(sendMessagePojo);
                log.info("接收到短信服务返回的结果：" + rSmsResponseVo.toString());

            }
            //写入时效记录
            agLawsuitService.addTimeManage(loginUser,ids, TimeContentFormats.BATCH_MESSAGE);
        } catch (Exception e) {
            redisService.deleteObject(CacheConstants.BATCH_SEND_MESSAGE + threadEntityPojo.getUserId());
            throw new GlobalException(e.getMessage());
        }
        redisService.deleteObject(CacheConstants.BATCH_SEND_MESSAGE + threadEntityPojo.getUserId());
    }

    /**
     * 发送短信过滤数据
     * @param hashMap
     * @return
     */
    public List<String> sendCuiShouMessage(Map<String, Object> hashMap) {
        List<Long> ids = agLawsuitService.searchIds(hashMap);
        if (ids == null) {
            throw new ServiceException("案件Id不存在");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("ids", ids);
        if (hashMap.containsKey("saveStage")) {
            map.put("saveStage", hashMap.get("saveStage").toString());
        }
        if (hashMap.containsKey("disposeStage")) {
            map.put("disposeStage", hashMap.get("disposeStage").toString());
        }
        if (hashMap.containsKey("mediatedStage")) {
            map.put("mediatedStage", hashMap.get("mediatedStage").toString());
        }
        List<SendMsg> sendMsgs = recordService.selectSendMsgByIds(map);//查询案件信息
        if (ObjectUtils.isEmpty(sendMsgs) || sendMsgs.size() == 0) {
            throw new GlobalException("案件查询为空");
        }
        List<String> stringList = new ArrayList<>();
        List<String> caseIds = new ArrayList<>();
        if (!ObjectUtils.isEmpty(sendMsgs)) {
            for (SendMsg sendMsg : sendMsgs) {
                //查询24小时内发送成功的短信并且发送状态不为成功的
                DateTime sendtime1 = DateUtil.beginOfDay(new Date());
                DateTime sendtime2 = DateUtil.endOfDay(new Date());
                List<SendRecords> sendRecords = recordService.selectFailMessage(sendMsg.getClientPhone(), sendtime1, sendtime2);
                //已结清
                if (sendMsg.getSettlementStatus() == 1) {
                    continue;
                }
                if (sendRecords.size() > 0) {
                    continue;
                }
                if (stringList.contains(sendMsg.getClientIdcard())) {
                    continue;
                }
                stringList.add(sendMsg.getClientIdcard());
                caseIds.add(sendMsg.getCaseId());
            }
        }
        return caseIds;
    }

    /**
     * 调诉端单条/案件详情 联系人发送短信
     *
     * @return
     */
    public void sendCwuMessage(Map<String, Object> hashMap) {
        com.zws.common.core.domain.sms.ThreadEntityPojo threadEntityPojo = new com.zws.common.core.domain.sms.ThreadEntityPojo();
        threadEntityPojo.setType(TokenInformation.getType());
        threadEntityPojo.setUser(TokenInformation.getUsername());
        threadEntityPojo.setUserId(TokenInformation.getUserid());
        threadEntityPojo.setCreateId(TokenInformation.getCreateid());
        threadEntityPojo.setTeamLevelType(TokenInformation.getTeamLevelType());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<Long> ids = new ArrayList<>();

        String templateContent = (String) hashMap.get("templateContent");//短信模版
        Integer templateId = (Integer) hashMap.get("templateId");// 模版id
        Integer listOrDetails = (Integer) hashMap.get("listOrDetails");//1-列表批量发送，2-案件详情单条发送
        Integer contactId = (Integer) hashMap.get("contactId");// 联系人id
        Integer caseId = (Integer) hashMap.get("caseId");// 案件id

//        Long templateId = smsCustomPojo.getTemplateId();
        com.zws.common.core.domain.sms.TemplateSms templateSms = recordService.selectTemplateById(templateId.longValue());
        if (!ObjectUtils.isEmpty(contactId)) {
            InfoContact infoContact = recordService.selectCwContact(contactId.longValue());
            if (ObjectUtils.isEmpty(infoContact)) {
                throw new GlobalException("联系人查询为空");
            }
            //查询24小时内发送成功的短信
            DateTime sendtime1 = DateUtil.beginOfDay(new Date());
            DateTime sendtime2 = DateUtil.endOfDay(new Date());
            List<SendRecords> sendRecords = recordService.selectFailMessage(infoContact.getContactPhone(), sendtime1, sendtime2);
            if (infoContact.getSettlementStatus() == 1) {
                throw new GlobalException("已结清案件不能发送短信");
            }
            if (!ObjectUtils.isEmpty(sendRecords) && sendRecords.size() > 0) {
                throw new GlobalException("短信提交失败：24小时内该手机号码已经发过短信");
            }
            List<String> bankCardNumbers = new ArrayList<>(); //共债信用卡号后四位
            SmsParameters smsParameter = recordService.selectSmsParameter(infoContact.getCaseId());
            Map<String, String> map = new HashMap<>();
            map.put("clientIdcard", smsParameter.getClientIdcard());
            map.put("fieldKey", FieldEncryptUtil.fieldKey);

            //查询共债案件
            List<String> bankCardNumber = recordService.selectBankCardNumber(map);

            for (String bankCard : bankCardNumber) {
                bankCardNumbers.add(bankCard.substring(bankCard.length() - 4));
            }
            smsParameter.setBankCardNumber(String.join("/", bankCardNumbers));
            smsParameter.setUserName(threadEntityPojo.getUser());
            smsParameter.setUserId(threadEntityPojo.getUserId().longValue());
            smsParameter.setRelationship("本人");
            smsParameter.setThreadEntityPojo(threadEntityPojo);
            SendMessagePojo sendMessagePojo = new SendMessagePojo();
            sendMessagePojo.setTemplateSms(templateSms);
            sendMessagePojo.setSmsParameters(smsParameter);
            // 发送短信
            R rSmsResponseVo = remoteSmsService.sendAppealMessages(sendMessagePojo);
            log.info("接收到短信服务返回的结果：" + rSmsResponseVo.toString());
            //写入时效记录
            ids.add(infoContact.getCaseId());
            agLawsuitService.addTimeManage(loginUser,ids, TimeContentFormats.BATCH_MESSAGE);
        } else if (!ObjectUtils.isEmpty(caseId)) {
            SmsParameters smsParameter = recordService.selectSmsParameter(Long.valueOf(caseId));
            //查询24小时内发送成功的短信
            DateTime sendtime1 = DateUtil.beginOfDay(new Date());
            DateTime sendtime2 = DateUtil.endOfDay(new Date());
            List<SendRecords> sendRecords = recordService.selectFailMessage(smsParameter.getClientPhone(), sendtime1, sendtime2);
            if (smsParameter.getSettlementStatus() == 1) {
                throw new GlobalException("已结清案件不能发送短信");
            }
            if (!ObjectUtils.isEmpty(sendRecords) && sendRecords.size() > 0) {
                throw new GlobalException("短信提交失败：24小时内该手机号码已经发过短信");
            }
            List<String> bankCardNumbers = new ArrayList<>(); //共债信用卡号后四位
            Map<String, String> map = new HashMap<>();
            map.put("clientIdcard", smsParameter.getClientIdcard());
            map.put("fieldKey", FieldEncryptUtil.fieldKey);

            //查询共债案件
            List<String> bankCardNumber = recordService.selectBankCardNumber(map);

            for (String bankCard : bankCardNumber) {
                bankCardNumbers.add(bankCard.substring(bankCard.length() - 4));
            }
            smsParameter.setBankCardNumber(String.join("/", bankCardNumbers));
            smsParameter.setUserName(threadEntityPojo.getUser());
            smsParameter.setUserId(threadEntityPojo.getUserId().longValue());
            smsParameter.setRelationship("本人");
            smsParameter.setThreadEntityPojo(threadEntityPojo);
            SendMessagePojo sendMessagePojo = new SendMessagePojo();
            sendMessagePojo.setTemplateSms(templateSms);
            sendMessagePojo.setSmsParameters(smsParameter);
            // 发送短信
            R rSmsResponseVo = remoteSmsService.sendAppealMessages(sendMessagePojo);
            log.info("接收到短信服务返回的结果：" + rSmsResponseVo.toString());
            //写入时效记录
            ids.add(Long.valueOf(caseId));
            agLawsuitService.addTimeManage(loginUser,ids, TimeContentFormats.BATCH_MESSAGE);
        }
    }


//-------------------------------------------预测试外呼-----------------------------------------------------------

    /**
     * 根据勾选案件查找对应有预测试外呼坐席的催员id集合
     *
     * @param exportReminder
     * @return
     */
    public List<Integer> verifyCaseList(ExportReminder exportReminder) {
//        根据搜索条件查询案件id
        List<Long> longList = caseService.selectCaseIdList(exportReminder);
        if (ObjectUtils.isEmpty(longList)) throw new GlobalException("所选案件查询为空");
        if (longList.size() > 500) throw new GlobalException("创建预测试外呼任务案件数量不能大于五百个");
//        远程调用cis服务查询催员id集合
        com.zws.common.core.domain.sms.ThreadEntityPojo entityPojo = new com.zws.common.core.domain.sms.ThreadEntityPojo();
        entityPojo.setType(TokenInformation.getType());
        entityPojo.setUser(TokenInformation.getUsername());
        entityPojo.setUserId(TokenInformation.getUserid());
        entityPojo.setCreateId(TokenInformation.getCreateid());
        entityPojo.setCaseIdList(longList);
        R<List<Integer>> r = remoteCisService.verifyCaseListAppeal(entityPojo, SecurityConstants.INNER);
        if (r.getCode() == R.SUCCESS) {
            return r.getData();
        } else {
            throw new GlobalException(r.getMsg());
        }
    }

    /**
     * 案件管理-创建预测试外呼任务
     *
     * @param exportReminder
     * @return
     */
    public Integer caseSubmitTaskData(ExportReminder exportReminder) {
//        根据搜索条件查询案件id
        List<Long> longList = caseService.selectCaseIdList(exportReminder);
        if (ObjectUtils.isEmpty(longList)) throw new GlobalException("所选案件查询为空");
        if (longList.size() > 500) throw new GlobalException("创建预测试外呼任务案件数量不能大于五百个");
        List<CaseManage> caseManages = caseMapper.selectState(longList);
        for (CaseManage caseManage : caseManages ){
            if (caseManage.getCaseMediateState().equals("0") && exportReminder.getIntelligenceTask().getAnswerSettings() == 1){
                throw new GlobalException("选择的预测试外呼坐席不是案件归属坐席，无法提交，请重新选择坐席或修改接通设置");
            }
        }
//        远程调用cis服务处理生成预测试外呼任务
        com.zws.common.core.domain.sms.ThreadEntityPojo entityPojo = new com.zws.common.core.domain.sms.ThreadEntityPojo();
        entityPojo.setType(TokenInformation.getType());
        entityPojo.setUser(TokenInformation.getUsername());
        entityPojo.setUserId(TokenInformation.getUserid());
        entityPojo.setCreateId(TokenInformation.getCreateid());
        entityPojo.setCaseIdList(longList);
        exportReminder.getIntelligenceTask().setEntityPojo(entityPojo);
        R<Integer> integer = remoteCisService.caseSubmitTaskData(exportReminder.getIntelligenceTask(), SecurityConstants.INNER);
        if (integer.getCode() == R.SUCCESS) {
            return integer.getData();
        } else {
            throw new GlobalException(integer.getMsg());
        }
    }

    /**
     * 案件管理-创建AI语音通知
     * @param exportReminder
     * @return
     */
    public Integer AiVoiceCaseSubmitTaskData(ExportReminder exportReminder) {
//        根据搜索条件查询案件id
        List<Long> longList = caseService.selectCaseIdList(exportReminder);
        if (ObjectUtils.isEmpty(longList)) throw new GlobalException("所选案件查询为空");
        if (longList.size() > 500) throw new GlobalException("创建AI语音通知任务案件数量不能大于五百个");
//        远程调用cis服务处理生成AI语音通知任务
        com.zws.common.core.domain.sms.ThreadEntityPojo entityPojo = new com.zws.common.core.domain.sms.ThreadEntityPojo();
        entityPojo.setType(TokenInformation.getType());
        entityPojo.setUser(TokenInformation.getUsername());
        entityPojo.setUserId(TokenInformation.getUserid());
        entityPojo.setCreateId(TokenInformation.getCreateid());
        entityPojo.setCaseIdList(longList);
        exportReminder.getAiVoiceTask().setEntityPojo(entityPojo);
        R<Integer> integer = remoteCisService.AiVoiceCaseSubmitTaskData(exportReminder.getAiVoiceTask(), SecurityConstants.INNER);
        if (integer.getCode() == R.SUCCESS) {
            return integer.getData();
        } else {
            throw new GlobalException(integer.getMsg());
        }
    }

    /**
     * 案件管理-创建AI语音通知、并执行
     * @param exportReminder
     * @return
     */
    public Integer AiVoiceCaseSubmitTaskDataAndExecute(ExportReminder exportReminder) {
        //        根据搜索条件查询案件id
        List<Long> longList = caseService.selectCaseIdList(exportReminder);
        if (ObjectUtils.isEmpty(longList)) throw new GlobalException("所选案件查询为空");
        if (longList.size() > 500) throw new GlobalException("创建预测试外呼任务案件数量不能大于五百个");
//        远程调用cis服务处理生成预测试外呼任务
        com.zws.common.core.domain.sms.ThreadEntityPojo entityPojo = new com.zws.common.core.domain.sms.ThreadEntityPojo();
        entityPojo.setType(TokenInformation.getType());
        entityPojo.setUser(TokenInformation.getUsername());
        entityPojo.setUserId(TokenInformation.getUserid());
        entityPojo.setCreateId(TokenInformation.getCreateid());
        entityPojo.setCaseIdList(longList);
        exportReminder.getAiVoiceTask().setEntityPojo(entityPojo);
        R<Integer> integer = remoteCisService.AiVoiceCaseSubmitTaskDataAndExecute(exportReminder.getAiVoiceTask(), SecurityConstants.INNER);
        if (integer.getCode() == R.SUCCESS) {
            return integer.getData();
        } else {
            throw new GlobalException(integer.getMsg());
        }
    }

    /**
     * 催收端-案件管理-案件标记（合并资产端后）
     * @param exportReminder
     */
    public void editMarkCase(ExportReminder exportReminder) {
        caseService.editMarkCase(exportReminder);
    }
}



