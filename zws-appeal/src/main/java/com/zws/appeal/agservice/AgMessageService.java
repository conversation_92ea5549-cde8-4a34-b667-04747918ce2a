package com.zws.appeal.agservice;

import com.zws.common.core.exception.GlobalException;
import com.zws.appeal.domain.Message.MessageCenter;
import com.zws.appeal.domain.Message.UserMessage;
import com.zws.appeal.service.MessageService;
import com.zws.appeal.utils.TokenInformation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;

@Component
@Transactional //开启事务注解
public class AgMessageService {

    @Autowired
    private MessageService messageService;

    /**
     * 写入发布消息信息（实时推送写入）
     *
     * @param messageCenter
     */
    public void insertMessageCenter(MessageCenter messageCenter) {
        messageService.insertMessageCenter(messageCenter);
        if (messageCenter.getPushMode() == 1) {
            UserMessage userMessage = new UserMessage();
            userMessage.setCreateId(new Long((long) TokenInformation.getCreateid()));
            userMessage.setIdentification(1);
            userMessage.setMessageType(0);
            userMessage.setMessageTitle(messageCenter.getMessageTitle());
            userMessage.setMessageContent(messageCenter.getMessageContent());
            userMessage.setCreateTime(new Date());
            userMessage.setDelFlag(0);
            messageService.insertUserMessage(userMessage);
        }
    }

    /**
     * 修改消息信息时查看是否是待发送状态
     *
     * @param messageCenter
     */
    public void updateMessageCenter(MessageCenter messageCenter) {
        MessageCenter messageCenters = messageService.selectMessageCenterId(messageCenter);
        if (ObjectUtils.isEmpty(messageCenters)) {
            throw new GlobalException("消息查询为空，无法修改");
        }
        if (!ObjectUtils.isEmpty(messageCenters) && messageCenters.getStates().equals("已推送")) {
            throw new GlobalException("消息已推送，无法修改");
        } else {
            messageService.updateMessageCenter(messageCenter);
        }
    }
}
