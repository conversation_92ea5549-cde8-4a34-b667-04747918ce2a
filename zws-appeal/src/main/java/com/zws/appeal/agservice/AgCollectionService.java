package com.zws.appeal.agservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zws.appeal.domain.appeal.StageConfig;
import com.zws.appeal.mapper.MyApprovalMapper;
import com.zws.appeal.mapper.appeal.StageConfigMapper;
import com.zws.appeal.pojo.appeal.FilingCasePojo;
import com.zws.appeal.pojo.vo.RecombinationRecordVo;
import com.zws.appeal.pojo.vo.StagingRecordVo;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.enums.MessageFormats;
import com.zws.common.core.enums.approval.ProceEnum;
import com.zws.common.core.enums.approval.WebSideEnum;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.utils.SplitUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.domain.*;
import com.zws.appeal.domain.financial.FinancialAccount;
import com.zws.appeal.domain.record.*;
import com.zws.appeal.enums.CanDownloadEnum;
import com.zws.appeal.enums.MatchingResultsEnum;
import com.zws.appeal.pojo.*;
import com.zws.appeal.pojo.messageCenter.TeamMessageCenters;
import com.zws.appeal.pojo.staticConst.StaticConstantClass;
import com.zws.appeal.pojo.work.WorkOrderFollowUp;
import com.zws.appeal.service.*;
import com.zws.appeal.utils.*;
import com.zws.dispose.pojo.ApplicationUtils;
import com.zws.system.api.RemoteSmsService;
import com.zws.system.api.domain.WorkAnnex;
import com.zws.system.api.domain.WorkFollowUp;
import com.zws.system.api.pojo.WorkAnnexPojo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Primary
@Transactional //开启事务注解
@Slf4j
public class AgCollectionService {

    /**
     * 上传文件存储在本地的根路径
     */
    @Value("${filePate.file_path_link}")
    private String localFilePath;

    @Autowired
    private CollectionService collectionService;
    @Autowired
    private SettingsService settingsService;
    @Autowired
    private RecordService recordService;
    @Autowired
    private CaseService caseService;
    @Autowired
    private DesensitizationRedis desensitizationRedis;
    @Autowired
    private MessageToolUtils messageToolUtils;
    @Autowired
    private MyApprovalService myApprovalService;
    @Autowired
    private AgCaseService agCaseService;
    @Autowired
    private SendRecordService sendRecordService;
    @Autowired
    private IFinancialAccountService financialAccountService;
    @Autowired
    private RemoteSmsService remoteSmsService;
    @Resource
    private SmsRedisUtils smsRedisUtils;
    @Resource
    private ApplicationContext applicationContext;
    @Autowired
    private DesensitizationAgService desensitizationAgService;
    @Resource
    private TemplateService templateService;
    @Autowired
    private AgLawsuitService agLawsuitService;
    @Autowired
    private StageConfigMapper stageConfigMapper;
    @Resource
    private MyApprovalMapper myApprovalMapper;

    /**
     * 验证该案件是否属于本团队
     *
     * @param caseId
     */
    public void intercept(Long caseId) {
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);
        if (ObjectUtils.isEmpty(caseManage1)) {
            throw new GlobalException("该案件不存在,无法查看案件详情");
        }
        Long outsourcingTeamId = caseManage1.getOutsourcingTeamId();
        if (ObjectUtils.isEmpty(outsourcingTeamId) || !outsourcingTeamId.equals(new Long((long) TokenInformation.getCreateid()))) {
            throw new GlobalException("该案件不属于本团队,无法查看案件详情");
        }
    }

    /**
     * 案件基础信息脱敏处理
     *
     * @param infoBase
     * @return
     */
    public InfoBase infoBaseDesensitization(InfoBase infoBase) {
        DecryptUtils.dateDecryptDetails(infoBase);
        if (!ObjectUtils.isEmpty(infoBase)) {
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(infoBase.getClientName())) {
                    infoBase.setClientName(DataMaskingUtils.nameMasking(infoBase.getClientName()));
                    infoBase.setSecurityName(DataMaskingUtils.nameMasking(infoBase.getSecurityName()));
                }
                if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(infoBase.getClientPhone())) {
                    infoBase.setClientPhone(DataMaskingUtils.phoneMasking(infoBase.getClientPhone()));
                    infoBase.setSecurityPhone(DataMaskingUtils.phoneMasking(infoBase.getSecurityPhone()));
                }
                if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(infoBase.getClientIdNum())) {
                    infoBase.setClientIdNum(DataMaskingUtils.idMasking(infoBase.getClientIdNum()));
                    infoBase.setSecurityIdNum(DataMaskingUtils.idMasking(infoBase.getSecurityIdNum()));
                }

                if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(infoBase.getClientCensusRegister())) {
                    infoBase.setClientCensusRegister(DataMaskingUtils.Masking(infoBase.getClientCensusRegister()));
                }
                if (desensitization.getQq() == 1 && !ObjectUtils.isEmpty(infoBase.getQq())) {
                    infoBase.setQq(DataMaskingUtils.QQMasking(infoBase.getQq()));
                }
                if (desensitization.getWeChat() == 1 && !ObjectUtils.isEmpty(infoBase.getWeixin())) {
                    infoBase.setWeixin(DataMaskingUtils.WeChatMasking(infoBase.getWeixin()));
                }
                if (desensitization.getUnitAddress() == 1 && !ObjectUtils.isEmpty(infoBase.getWorkingAddress())) {
                    infoBase.setWorkingAddress(DataMaskingUtils.Masking(infoBase.getWorkingAddress()));
                }
                if (desensitization.getEntityName() == 1 && !ObjectUtils.isEmpty(infoBase.getPlaceOfWork())) {
                    infoBase.setPlaceOfWork(DataMaskingUtils.Masking(infoBase.getPlaceOfWork()));
                }
                if (desensitization.getResidentialAddress() == 1 && !ObjectUtils.isEmpty(infoBase.getResidentialAddress())) {
                    infoBase.setResidentialAddress(DataMaskingUtils.Masking(infoBase.getResidentialAddress()));
                }
                if (desensitization.getHomeAddress() == 1 && !ObjectUtils.isEmpty(infoBase.getHomeAddress())) {
                    infoBase.setHomeAddress(DataMaskingUtils.Masking(infoBase.getHomeAddress()));
                }
                if (desensitization.getBankCard() == 1 && !ObjectUtils.isEmpty(infoBase.getBankCardNumber())) {
                    List<String> bankCardNumbers = SplitUtils.strSplitComma(infoBase.getBankCardNumber());
                    List<String> strs = new ArrayList<>();
                    for (String bankCardNumber : bankCardNumbers) {
                        strs.add(DataMaskingUtils.Masking(bankCardNumber));
                    }
                    infoBase.setBankCardNumber(String.join(SplitUtils.regex_comma, strs));
                }

            }
        }
        return infoBase;
    }

    /**
     * 根据案件id查询案件所有信息以及共债信息
     *
     * @param caseId
     * @return
     */
    public Map<String, Object> caseDetails(Long caseId) {
        Map<String, Object> map = new HashMap<>();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);

        String label1 = caseManage1.getLabel();
        Label label = collectionService.selectLabelCode(caseId, TokenInformation.getCreateid(), label1);  //查询案件标签信息
        map.put("label", label);

//        List<ReductionRecord> reductionRecords = recordService.selectReductionRecord(caseId);
        List<ReorganizationVo> reorganizationVos = recordService.selectReorganizationRecordCaseId(caseId, null);
        InfoBase infoBase = collectionService.selectInfoBase(caseId);  //根据案件id查询案件详情

        InfoBase infoBase1 = infoBaseDesensitization(infoBase);
        infoBase.setFollowUpState(caseManage1.getFollowUpState());  //案件跟进状态
        infoBase.setFollowUpStateString(caseManage1.getFollowUpState());
        if (!ObjectUtils.isEmpty(caseManage1.getDisposeStage())) {
            StageConfig stageConfig = stageConfigMapper.selectByStageTwoName(caseManage1.getDisposeStage());
            infoBase.setFollowUpStateString(stageConfig.getStageName()+" | "+stageConfig.getStageTwoName());  //案件跟进状态
        }
        if (!ObjectUtils.isEmpty(reorganizationVos)) {
            infoBase.setState(reorganizationVos.get(0).getState());  //减免状态
            if (reorganizationVos.get(0).getState().equals("待审核") || reorganizationVos.get(0).getState().equals("审核中")) {
                infoBase.setStateNumber(0);
            } else {
                infoBase.setStateNumber(1);
            }
        } else {
            infoBase.setState("未申请");
            infoBase.setStateNumber(1);
        }
        Criteria criteria = new Criteria();
        criteria.setCaseId(caseId);
        List<InfoContact> infoContact = collectionService.selectInfoContact(criteria);  //根据案件id查询案件联系人信息
        List<InfoExtra> infoExtra = collectionService.selectInfoExtra(caseId);  //根据案件id查询案件附加信息
        InfoLoan infoLoan = collectionService.selectInfoLoan(caseId);  //根据案件id查询案件贷款信息
//        InfoPlan infoPlan1 = new InfoPlan();
//        infoPlan1.setSortOrder(2);
//        infoPlan1.setCaseId(caseId);
//        List<InfoPlan> infoPlan = collectionService.selectInfoPlan(infoPlan1);  //根据案件id查询案件还款计划信息
//        String clientIdcard = caseManage1.getClientIdcard();
//        List<InfoLoanPojo> caseManage = collectionService.selectCaseManage(clientIdcard, TokenInformation.getCreateid());//共债案件
//        if (caseManage.size() > 1) {
//            map.put("caseManage", caseManage);
//        }
        //查询减免金额总和
        BigDecimal amountAfterDeductionSun = collectionService.selectAmountAfterDeductionSun(caseId);
        BigDecimal reorganizationCaseIdSum= collectionService.selectReorganizationCaseIdSum(caseId);
        //减免总金额
        infoLoan.setAmountAfterDeductionSun(infoLoan.getAmountAfterDeductionSun());

        //没有减免金额就展示为空
        if (ObjectUtil.isNotEmpty(reorganizationCaseIdSum)&&ObjectUtil.isNotEmpty(amountAfterDeductionSun)){
            infoLoan.setAmountAfterDeductionSun(null);
            infoLoan.setAmountAfterDeduction(null);
        }

        String clientIdcard = caseManage1.getClientIdcard();
        int count = collectionService.selectCaseManageCount(clientIdcard, TokenInformation.getCreateid());//共债案件数量
        if (count <= 1) {
            map.put("count", 0);
        } else {
            map.put("count", count);
        }

        if (infoLoan != null) {
            if (caseManage1.getSettlementStatus() == 1) {
                infoLoan.setYcOverdueDays(null);
            }
            Long propertyId = infoLoan.getProductId();
            infoBase1.setProductShortName(collectionService.getProductShortName(propertyId));
        }

        //银行账号
        List<FinancialAccount> financialAccounts = financialAccountService.selectByCaseId(caseId);
        String cardType= myApprovalMapper.selectCardType(caseId);
        for (FinancialAccount account :financialAccounts){
            if (ObjectUtil.isNotEmpty(cardType)){
                //如未返回虚拟账户就展示主账户信息/selectCase
                account.setAccountNumber(cardType);
            }
        }

        map.put("infoBase", infoBase1);
        map.put("infoContact", infoContact);
        map.put("infoExtra", infoExtra);
        map.put("infoLoan", infoLoan);
        map.put("accounts", financialAccounts);
//        map.put("infoPlan", infoPlan);

        return map;
    }

    public Map<String, Object> caseDetailsCis(Long caseId) {
        Map<String, Object> map = new HashMap<>();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);

        String label1 = caseManage1.getLabel();
        Label label = collectionService.selectLabelCode(caseId, TokenInformation.getCreateid(), label1);  //查询案件标签信息
        map.put("label", label);

        //List<ReductionRecord> reductionRecords = recordService.selectReductionRecord(caseId);
        List<ReorganizationVo> reductionRecords = recordService.selectReorganizationRecordCaseId(caseId, null);
        InfoBase infoBase = collectionService.selectInfoBase(caseId);  //根据案件id查询案件详情
        DecryptUtils.dateDecryptDetails(infoBase);

        infoBase.setFollowUpState(caseManage1.getFollowUpState());  //案件跟进状态
        infoBase.setFollowUpStateString(caseManage1.getFollowUpState());
        if (!ObjectUtils.isEmpty(caseManage1.getDisposeStage())) {
            StageConfig stageConfig = stageConfigMapper.selectByStageTwoName(caseManage1.getDisposeStage());
            infoBase.setFollowUpStateString(stageConfig.getStageName()+" | "+stageConfig.getStageTwoName());  //案件跟进状态
        }
        if (!ObjectUtils.isEmpty(reductionRecords)) {
            infoBase.setState(reductionRecords.get(0).getState());  //减免状态
            if (reductionRecords.get(0).getState().equals("待审核") || reductionRecords.get(0).getState().equals("审核中")) {
                infoBase.setStateNumber(0);
            } else {
                infoBase.setStateNumber(1);
            }
        } else {
            infoBase.setState("未申请");
            infoBase.setStateNumber(1);
        }
        Criteria criteria = new Criteria();
        criteria.setCaseId(caseId);
        List<InfoContact> infoContact = collectionService.selectInfoContact(criteria);  //根据案件id查询案件联系人信息
        List<InfoExtra> infoExtra = collectionService.selectInfoExtra(caseId);  //根据案件id查询案件附加信息
        InfoLoan infoLoan = collectionService.selectInfoLoan(caseId);  //根据案件id查询案件贷款信息
//        InfoPlan infoPlan1 = new InfoPlan();
//        infoPlan1.setSortOrder(2);
//        infoPlan1.setCaseId(caseId);
//        List<InfoPlan> infoPlan = collectionService.selectInfoPlan(infoPlan1);  //根据案件id查询案件还款计划信息
//        String clientIdcard = caseManage1.getClientIdcard();
//        List<InfoLoanPojo> caseManage = collectionService.selectCaseManage(clientIdcard, TokenInformation.getCreateid());//共债案件
//        if (caseManage.size() > 1) {
//            map.put("caseManage", caseManage);
//        }
        String clientIdcard = caseManage1.getClientIdcard();
        int count = collectionService.selectCaseManageCount(clientIdcard, TokenInformation.getCreateid());//共债案件数量
        if (count <= 1) {
            map.put("count", 0);
        } else {
            map.put("count", count);
        }

        if (infoLoan != null) {
            if (caseManage1.getSettlementStatus() == 1) {
                infoLoan.setYcOverdueDays(null);
            }
            Long propertyId = infoLoan.getProductId();
        }

        //银行账号
        List<FinancialAccount> financialAccounts = financialAccountService.selectByCaseId(caseId);


        map.put("infoBase", infoBase);
        map.put("infoContact", infoContact);
        map.put("infoExtra", infoExtra);
        map.put("infoLoan", infoLoan);
        map.put("accounts", financialAccounts);
//        map.put("infoPlan", infoPlan);

        return map;
    }

    /**
     * 根据案件id查询共债案件信息列表
     *
     * @return
     */
    public List<InfoLoanPojo> queryJointDebt(Long caseId) {
        PageUtils.clearPage();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);
        if (ObjectUtils.isEmpty(caseManage1)) {
            throw new GlobalException("案件查询错误");
        }
        PageUtils.startPage();
        List<InfoLoanPojo> caseManage = collectionService.selectCaseManage(caseManage1.getClientIdcard(), TokenInformation.getCreateid());//共债案件
        return caseManage;
    }

    /**
     * 根据案件id查询共债案件信息（统计共债案件总欠款金额）
     *
     * @return
     */
    public ArrearsTotalPojo queryAmountMoney(Long caseId) {
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);
        if (ObjectUtils.isEmpty(caseManage1)) {
            throw new GlobalException("案件查询错误");
        }
        List<InfoLoanPojo> caseManage = collectionService.selectCaseManage(caseManage1.getClientIdcard(), TokenInformation.getCreateid());//共债案件
        BigDecimal syYhPrincipal = BigDecimal.ZERO;  //剩余本金
        BigDecimal syYhInterest = BigDecimal.ZERO;  //剩余利息
        BigDecimal syYhFees = BigDecimal.ZERO;  //剩余费用
        BigDecimal syYhDefault = BigDecimal.ZERO;  //剩余罚息
        if (!ObjectUtils.isEmpty(caseManage)) {
            for (InfoLoanPojo row : caseManage) {
                syYhPrincipal = row.getSyYhPrincipal().add(syYhPrincipal);
                syYhInterest = row.getSyYhInterest().add(syYhInterest);
                syYhFees = row.getSyYhFees().add(syYhFees);
                syYhDefault = row.getSyYhDefault().add(syYhDefault);
            }
        }
        BigDecimal syYhArrearsTotal = syYhPrincipal.add(syYhInterest).add(syYhFees).add(syYhDefault);  //剩余欠款总额
        ArrearsTotalPojo arrearsTotalPojo = new ArrearsTotalPojo();
        arrearsTotalPojo.setSyYhArrearsTotal(syYhArrearsTotal);
        arrearsTotalPojo.setSyYhPrincipal(syYhPrincipal);
        arrearsTotalPojo.setSyYhInterest(syYhInterest);
        arrearsTotalPojo.setSyYhFees(syYhFees);
        arrearsTotalPojo.setSyDefaultInterest(syYhDefault);
        return arrearsTotalPojo;
    }

    /**
     * 根据案件id以及联系人/联系人号码查询联系人信息-(数据脱敏处理)
     *
     * @param criteria
     * @return
     */
    public OutboundVisitButton selectInfoContact(Criteria criteria) {
        OutboundVisitButton outboundVisitButton = new OutboundVisitButton();
        criteria.setDecryptKey(FieldEncryptUtil.fieldKey);
        List<InfoContact> infoContacts = collectionService.selectInfoContact(criteria);
        outboundVisitButton.setInfoContacts(infoContacts);
        StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
        State state = stateDesensitization.getState();
        if (!ObjectUtils.isEmpty(infoContacts)) {
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (InfoContact infoContact : infoContacts) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(infoContact.getContactName())) {
                        infoContact.setContactName(DataMaskingUtils.nameMasking(infoContact.getContactName()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(infoContact.getContactPhone())) {
                        infoContact.setContactPhone(DataMaskingUtils.phoneMasking(infoContact.getContactPhone()));
                    }
                }
            }
        }
        outboundVisitButton.setAuthorizationStatus(state.getAuthorizationStatus());
        return outboundVisitButton;
    }

    /**
     * 根据案件权限获取对应的权限催记信息
     *
     * @return
     */
    public List<UrgeRecord> reminderAuthority(Long caseId,String urgeTpye,Integer webSide) {
        PageUtils.clearPage();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);   //根据案件id查询案件详情
        if (ObjectUtils.isEmpty(caseManage1)) {
            throw new GlobalException("案件查询错误");
        }
        String clientIdcard = caseManage1.getClientIdcard();
        if (ObjectUtils.isEmpty(clientIdcard)) {
            throw new GlobalException("客户证件号码查询错误");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("createId", TokenInformation.getCreateid());
        map.put("clientIdcard", clientIdcard);
        List<Long> list = collectionService.selectIdentificationAndTeamId(map);
        if (ObjectUtils.isEmpty(list)) {
            throw new GlobalException("共债案件查询错误");
        }
        if (list.size() > 5000) {
            throw new GlobalException("共债案件数量过多,查询错误");
        }
        PageUtils.startPage();
        List<UrgeRecord> urgeRecords = recordService.selectUrgeRecord(caseId, list, caseManage1,urgeTpye,webSide);
        if (!ObjectUtils.isEmpty(urgeRecords)) {
            PageUtils.clearPage();
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            Desensitization desensitization = null;
            if (state.getInformationStatus() == 1) {
                desensitization = stateDesensitization.getDesensitization();
            }
            for (UrgeRecord urgeRecord : urgeRecords) {
                DecryptUtils.dataDecrypt(urgeRecord);
                if (desensitization != null) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(urgeRecord.getLiaison())) {
                        urgeRecord.setLiaison(DataMaskingUtils.nameMasking(urgeRecord.getLiaison()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(urgeRecord.getContactMode())) {
                        urgeRecord.setContactMode(DataMaskingUtils.phoneMasking(urgeRecord.getContactMode()));
                    }
                }
            }
        }
        return urgeRecords;
    }

    public List<UrgeRecord> reminderAuthoritys(Long caseId) {
        PageUtils.clearPage();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);   //根据案件id查询案件详情
        if (ObjectUtils.isEmpty(caseManage1)) {
            throw new GlobalException("案件查询错误");
        }
        String clientIdcard = caseManage1.getClientIdcard();
        if (ObjectUtils.isEmpty(clientIdcard)) {
            throw new GlobalException("客户证件号码查询错误");
        }
        Map<String, Object> map = new HashMap<>();
//        map.put("createId", TokenInformation.getCreateid());
        map.put("clientIdcard", clientIdcard);
        List<Long> list = collectionService.selectIdentification(map);
        if (ObjectUtils.isEmpty(list)) {
            throw new GlobalException("共债案件查询错误");
        }
        if (list.size() > 5000) {
            throw new GlobalException("共债案件数量过多,查询错误");
        }
        PageUtils.startPage();
        List<UrgeRecord> urgeRecords = recordService.selectUrgeRecords(caseId, list, caseManage1);
        if (!ObjectUtils.isEmpty(urgeRecords)) {
            PageUtils.clearPage();
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            Desensitization desensitization = null;
            if (state.getInformationStatus() == 1) {
                desensitization = stateDesensitization.getDesensitization();
            }
            for (UrgeRecord urgeRecord : urgeRecords) {
                DecryptUtils.dataDecrypt(urgeRecord);
                if (desensitization != null) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(urgeRecord.getLiaison())) {
                        urgeRecord.setLiaison(DataMaskingUtils.nameMasking(urgeRecord.getLiaison()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(urgeRecord.getContactMode())) {
                        urgeRecord.setContactMode(DataMaskingUtils.phoneMasking(urgeRecord.getContactMode()));
                    }
                }
            }
        }
        return urgeRecords;
    }

    /**
     * 根据案件id查询便签/投诉记录的上一条数据
     *
     * @param caseId
     * @return
     */
    public Map<String, Object> selectNoteComplaint(Long caseId) {
        Map<String, Object> map = new HashMap<>();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);   //根据案件id查询案件详情
        NoteRecord noteRecord = recordService.selectNoteRecordLast(caseId, caseManage1);
        ComplaintRecord complaintRecord = recordService.selectComplaintRecordLast(caseId, caseManage1);
        map.put("Note", noteRecord);
        map.put("complaint", complaintRecord);
        return map;
    }

    /**
     * 根据案件权限获取对应的权限协催记录信息
     *
     * @return
     */
    public List<AssistRecord> expeditingAuthority(Long caseId) {
        PageUtils.clearPage();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);   //根据案件id查询案件详情
        if (ObjectUtils.isEmpty(caseManage1)) {
            throw new GlobalException("案件查询错误");
        }
        PageUtils.startPage();
        List<AssistRecord> list = recordService.selectAssistRecord(caseId, caseManage1);
        return list;
    }

    /**
     * 根据案件权限获取对应的权限回款记录信息
     *
     * @return
     */
    public List<RepaymentRecord> collectionAuthority(Long caseId) {
        PageUtils.clearPage();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);   //根据案件id查询案件详情
        if (ObjectUtils.isEmpty(caseManage1)) {
            throw new GlobalException("案件查询错误");
        }
        PageUtils.startPage();
        List<RepaymentRecord> list = recordService.selectRepaymentRecord(caseId, caseManage1);
        return list;
    }

    /**
     * 根据案件权限获取对应的权限减免记录信息
     *
     * @return
     */
    public List<ReductionRecord> reliefAuthority(Long caseId) {
        PageUtils.clearPage();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);   //根据案件id查询案件详情
        if (ObjectUtils.isEmpty(caseManage1)) {
            throw new GlobalException("案件查询错误");
        }
        PageUtils.startPage();
        List<ReductionRecord> list = recordService.selectReductionRecordCaseId(caseId, caseManage1);
        return list;
    }

    /**
     * 根据案件权限获取对应的权限诉讼记录信息
     *
     * @return
     */
    public List<LawsuitRecord> litigationAuthority(Long caseId) {
        PageUtils.clearPage();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);   //根据案件id查询案件详情
        if (ObjectUtils.isEmpty(caseManage1)) {
            throw new GlobalException("案件查询错误");
        }
        PageUtils.startPage();
        List<LawsuitRecord> list = recordService.selectLawsuitRecord(caseId, caseManage1);
        return list;
    }

    /**
     * 根据案件权限获取对应的权限便签记录信息
     *
     * @return
     */
    public List<NoteRecord> noteAuthority(Long caseId) {
        PageUtils.clearPage();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);   //根据案件id查询案件详情
        if (ObjectUtils.isEmpty(caseManage1)) {
            throw new GlobalException("案件查询错误");
        }
        PageUtils.startPage();
        List<NoteRecord> list = recordService.selectNoteRecord(caseId, caseManage1);
        return list;
    }

    /**
     * 根据案件权限获取对应的权限投诉记录信息
     *
     * @return
     */
    public List<ComplaintRecord> complaintAuthority(Long caseId) {
        PageUtils.clearPage();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);   //根据案件id查询案件详情
        if (ObjectUtils.isEmpty(caseManage1)) {
            throw new GlobalException("案件查询错误");
        }
        PageUtils.startPage();
        List<ComplaintRecord> list = recordService.selectComplaintRecord(caseId, caseManage1);
        return list;
    }

    /**
     * 根据案件权限获取对应的权限外访记录信息
     *
     * @return
     */
    public List<OutsideRecord> foreignVisitAuthority(Long caseId) {
        PageUtils.clearPage();
        CaseManage caseManage1 = collectionService.selectCaseManageId(caseId);   //根据案件id查询案件详情
        if (ObjectUtils.isEmpty(caseManage1)) {
            throw new GlobalException("案件查询错误");
        }
        PageUtils.startPage();
        List<OutsideRecord> list = recordService.selectOutsideRecord(caseId, caseManage1);
        for (OutsideRecord outsideRecord : list) {
            Map<String, Object> map = new HashMap<>();
            Long zp = 0L, ly = 0L, bj = 0L;
            PageUtils.clearPage();
            List<Map<String, Object>> mapList = recordService.selectOutsideResource(outsideRecord.getId());

            for (Map map1 : mapList) {
                Integer resourceType = (Integer) map1.get("resourceType");
                Long number = (Long) map1.get("number");
                if (resourceType == 0) {
                    bj = number;
                }
                if (resourceType == 1) {
                    zp = number;
                }
                if (resourceType == 2) {
                    ly = number;
                }
            }
            map.put("record", bj);  //记录
            map.put("soundRecording", ly);  //录音
            map.put("photo", zp);  //照片
            outsideRecord.setMap(map);
        }
        if (!ObjectUtils.isEmpty(list)) {
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (OutsideRecord outsideRecord : list) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(outsideRecord.getClientName())) {
                        outsideRecord.setClientName(DataMaskingUtils.nameMasking(outsideRecord.getClientName()));
                    }
                }
            }
        }
        return list;
    }

    /**
     * 查询上一条案件信息
     *
     * @param caseBeating
     */
    public CaseBeating selectCaseManagePrevious(CaseBeating caseBeating) {
        int count = recordService.selectNeedCaseManageCount(caseBeating);   //案件总数量
        CaseBeating caseBeating1 = new CaseBeating();   //返回的数据

        Integer pageNumber = caseBeating.getPageNumber();//当前第几页
        Integer pageSize = caseBeating.getPageSize();//一页多少条
        Integer pageIndex = caseBeating.getPageIndex();//当前第几条
        if (pageIndex == 0 && pageNumber > 1) {
            caseBeating.setPageNumber(pageNumber - 1);
        }
        if (pageIndex == 0 && pageNumber == 1) {
            throw new GlobalException("已经是第一条数据");
        }
        caseBeating1.setPageNumber(caseBeating.getPageNumber());
        caseBeating1.setPageSize(caseBeating.getPageSize());

        Integer number = (caseBeating.getPageNumber() - 1) * pageSize;
        caseBeating.setPageNumber(number);
        List<CaseManage> caseManages = recordService.selectCaseManagePrevious(caseBeating);


        if (pageIndex == 0 && pageNumber > 1) {
            Long caseId = caseManages.get(caseManages.size() - 1).getCaseId();
            caseBeating1.setCaseIdCurrent(caseId);
            caseBeating1.setPageIndex(caseManages.size() - 1);
        } else {
            Long caseId = caseManages.get(pageIndex - 1).getCaseId();
            caseBeating1.setCaseIdCurrent(caseId);
            caseBeating1.setPageIndex(pageIndex - 1);
        }
        caseBeating1.setCount(count);
        return caseBeating1;
    }

    /**
     * 查询下一条案件信息
     *
     * @param caseBeating
     */
    public CaseBeating selectCaseManageNext(CaseBeating caseBeating) {
        int count = recordService.selectNeedCaseManageCount(caseBeating);   //案件总数量
        CaseBeating caseBeating1 = new CaseBeating();   //返回的数据

        Integer pageNumber = caseBeating.getPageNumber();//当前第几页
        Integer pageSize = caseBeating.getPageSize();//一页多少条
        Integer pageIndex = caseBeating.getPageIndex();//当前第几条
        int remainder = count % pageSize;
        int number;  //当前搜索页数
        if (remainder == 0) {
            number = count / pageSize;
            if (pageNumber == number && pageIndex == pageSize - 1) {
                throw new GlobalException("当前已是最后一条案件信息");
            }
            if (pageIndex == pageSize - 1 && pageNumber < number) {
                caseBeating.setPageNumber(pageNumber + 1);
            }
        } else {
            number = (count - remainder) / pageSize + 1;
            if (pageNumber == number && pageIndex == remainder - 1) {
                throw new GlobalException("当前已是最后一条案件信息");
            }
            if (pageIndex == pageSize - 1 && pageNumber < number) {
                caseBeating.setPageNumber(pageNumber + 1);
            }
        }
        caseBeating1.setPageNumber(caseBeating.getPageNumber());
        caseBeating1.setPageSize(caseBeating.getPageSize());

        Integer numbers = (caseBeating.getPageNumber() - 1) * pageSize;
        caseBeating.setPageNumber(numbers);
        List<CaseManage> caseManages = recordService.selectCaseManagePrevious(caseBeating);

        if (pageIndex == pageSize - 1 && pageNumber < number) {
            Long caseId = caseManages.get(0).getCaseId();
            caseBeating1.setCaseIdCurrent(caseId);
            caseBeating1.setPageIndex(0);
        } else {
            Long caseId = caseManages.get(pageIndex + 1).getCaseId();
            caseBeating1.setCaseIdCurrent(caseId);
            caseBeating1.setPageIndex(pageIndex + 1);
        }

        caseBeating1.setCount(count);
        return caseBeating1;
    }

    /**
     * 判断便签是否和上次一致
     *
     * @param noteRecord
     */
    public void insertNote(NoteRecord noteRecord) {
        CaseManage caseManage1 = collectionService.selectCaseManageId(noteRecord.getCaseId());   //根据案件id查询案件详情
        NoteRecord noteRecord1 = recordService.selectNoteRecordLast(noteRecord.getCaseId(), caseManage1);
        if (!ObjectUtils.isEmpty(noteRecord1)) {
            String noteContent = noteRecord1.getNoteContent();
            if (noteRecord.getNoteContent().equals(noteContent)) {
                throw new GlobalException("本次便签没有修改，无需保存");
            }
        }
        recordService.insertNoteRecord(noteRecord);
    }

    /**
     * 判断投诉是否和上次一致
     *
     * @param complaintRecord
     */
    public void insertComplaint(ComplaintRecord complaintRecord) {
        CaseManage caseManage1 = collectionService.selectCaseManageId(complaintRecord.getCaseId());   //根据案件id查询案件详情
        ComplaintRecord complaintRecord1 = recordService.selectComplaintRecordLast(complaintRecord.getCaseId(), caseManage1);
        if (!ObjectUtils.isEmpty(complaintRecord1)) {
            String complaintContent = complaintRecord1.getComplaintContent();
            if (complaintRecord.getComplaintContent().equals(complaintContent)) {
                throw new GlobalException("本次记录没有修改，无需保存");
            }
        }
        recordService.insertComplaintRecord(complaintRecord);
    }

    /**
     * 写入减免申请表并查询该案件是否已经存在申请记录(并保存上传文件信息)
     *
     * @param reductionRecord
     */
    public void insertReduction(ReductionRecord reductionRecord) {
        if (ObjectUtils.isEmpty(reductionRecord.getCaseId())) {
            throw new GlobalException("案件id不能为空");
        }
        BigDecimal bigDecimal = recordService.selectInfoLoan(reductionRecord.getCaseId());
        if (ObjectUtils.isEmpty(bigDecimal)) {
            throw new GlobalException("案件查询失败");
        }
        if (ObjectUtils.isEmpty(reductionRecord.getAmountAfterDeduction())) {
            throw new GlobalException("减免后金额不能为空");
        }
        if (reductionRecord.getAmountAfterDeduction().compareTo(BigDecimal.ZERO) == -1) {
            throw new GlobalException("减免后金额不能为负数");
        }
        if (reductionRecord.getAmountAfterDeduction().compareTo(bigDecimal) == 1) {
            throw new GlobalException("减免后金额不能大于剩余债权总额");
        }
        List<ReductionRecord> reductionRecords = recordService.selectReductionRecordCreateId(reductionRecord.getCaseId());//根据案件id查询减免申请表
//        判断该案件是否已经退案或者停催
//        ExportReminder exportReminder = new ExportReminder();
//        exportReminder.setCreateId(TokenInformation.getCreateid());  //团队id
//        exportReminder.setCaseId(reductionRecord.getCaseId().toString());   //案件id
//        List<CaseManage> caseManages = caseService.selectCaseManage(exportReminder);
        CaseManage caseManage = recordService.selectCaseManageByCaseId(reductionRecord.getCaseId().intValue());
        if (ObjectUtils.isEmpty(caseManage)) {
            throw new GlobalException("该案件已被退案或者停催");
        }
        if (!ObjectUtils.isEmpty(reductionRecord.getReason()) && reductionRecord.getReason().length() > 300) {
            throw new GlobalException("申请原因限制300字,请重新输入");
        }
        if (!ObjectUtils.isEmpty(reductionRecords)) {
            for (ReductionRecord reductionRecord1 : reductionRecords) {
                if (!reductionRecord1.getProce().equals(5) && !reductionRecord1.getProce().equals(4) && !reductionRecord1.getProce().equals(6)) {
                    throw new GlobalException("该案件已提交减免申请，请勿重复提交");
                }
            }
        }
        reductionRecord.setEntrustingCaseBatchNum(caseManage.getEntrustingCaseBatchNum());  //委案批次号
        reductionRecord.setEntrustingCaseDate(caseManage.getEntrustingCaseDate());  //委案日期
        reductionRecord.setReturnCaseDate(caseManage.getReturnCaseDate());  //退案日期
        reductionRecord.setOdvId(caseManage.getOdvId());
        reductionRecord.setOdvName(caseManage.getOdvName());
        reductionRecord.setRemainingDue(bigDecimal);
        recordService.insertReductionRecord(reductionRecord);
        List<ReductionFile> reductionFile = reductionRecord.getReductionFile();
        if (!ObjectUtils.isEmpty(reductionFile)) {
            for (ReductionFile reductionFiles : reductionFile) {
                reductionFiles.setFounder(TokenInformation.getUsername());
                reductionFiles.setCreationTime(new Date());
                reductionFiles.setRecordId(reductionRecord.getId());
            }
            recordService.insertReductionFile(reductionFile);
        }
//        申请成功给第一级审批人发送提醒
        ApprovalSteps approvalSteps = new ApprovalSteps();
        approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
        approvalSteps.setApproveCode(1); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        TeamMessageCenters teamMessageCenters = messageToolUtils.selectApprovalProcess(approvalSteps);
        messageToolUtils.messageReminder(MessageFormats.APPROVAL_REDUCTION, teamMessageCenters);
    }

    /**
     * 写入分期还款申请表并查询该案件是否已经存在申请记录
     *
     * @param stagingRecord
     */
    public void insertAmortization(StagingRecord stagingRecord) {
        if (ObjectUtils.isEmpty(stagingRecord.getCaseId())) {
            throw new GlobalException("案件id不能为空");
        }
        if (stagingRecord.getRepaymentMonthly().compareTo(BigDecimal.ZERO) == 0 || stagingRecord.getRepaymentMonthly().compareTo(BigDecimal.ZERO) == -1) {
            throw new GlobalException("分期还款金额必须大于0");
        }
        if (!ObjectUtils.isEmpty(stagingRecord.getReason()) && stagingRecord.getReason().length() > 300) {
            throw new GlobalException("申请原因字数超出限制,请重新输入");
        }
        List<StagingRecord> stagingRecords = recordService.selectStagingRecord(stagingRecord.getCaseId());//根据案件id查询分期还款申请表
//        判断该案件是否已经退案或者停催
//        ManageQueryParam manageQueryParam = new ManageQueryParam();
//        manageQueryParam.setOutsourcingTeamId(TokenInformation.getCreateid());   //团队id
//        manageQueryParam.setCaseId(stagingRecord.getCaseId());   //案件id
        CaseManage caseManage = recordService.selectCaseManageByCaseId(stagingRecord.getCaseId().intValue());
        if (ObjectUtils.isEmpty(caseManage)) {
            throw new GlobalException("该案件已被退案或者停催");
        }
        if (!ObjectUtils.isEmpty(stagingRecords)) {
            for (StagingRecord stagingRecord1 : stagingRecords) {
                if (!stagingRecord1.getProce().equals(5) && !stagingRecord1.getProce().equals(4) && !stagingRecord1.getProce().equals(6)) {
                    throw new GlobalException("该案件已提交分期还款申请，请勿重复提交");
                }
            }
        }
        stagingRecord.setEntrustingCaseBatchNum(caseManage.getEntrustingCaseBatchNum());  //委案批次号
        stagingRecord.setEntrustingCaseDate(caseManage.getEntrustingCaseDate());  //委案日期
        stagingRecord.setReturnCaseDate(caseManage.getReturnCaseDate());  //退案日期
        stagingRecord.setOdvId(caseManage.getOdvId());
        stagingRecord.setOdvName(caseManage.getOdvName());
        Date first = null;
        Date last = null;
        //根据还款期数StagingNum和还款日RepaymentDate计算首期和最后一期还款日期，如果还款日小于等于今天则首期还款日从下月开始
        if (stagingRecord.getRepaymentDate() <= Integer.valueOf(LocalDate.now().getDayOfMonth())) {
            //首期还款日月份天数
            int nextFirstDay = LocalDate.now().plusMonths(1).lengthOfMonth();
            if (Integer.valueOf(nextFirstDay) < stagingRecord.getRepaymentDate()) {
                first = Date.from(LocalDate.now().plusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            } else {
                first = Date.from(LocalDate.now().plusMonths(1).withDayOfMonth(stagingRecord.getRepaymentDate()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }
            //末期还款日月份天数
            int nextLastDay = LocalDate.now().plusMonths(stagingRecord.getStagingNum()).lengthOfMonth();
            if (Integer.valueOf(nextLastDay) < stagingRecord.getRepaymentDate()) {
                last = Date.from(LocalDate.now().plusMonths(stagingRecord.getStagingNum()).with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            } else {
                last = Date.from(LocalDate.now().plusMonths(stagingRecord.getStagingNum()).withDayOfMonth(stagingRecord.getRepaymentDate()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }
        }
        //如果还款日大于今天则首期还款日从当月开始
        else {
            //first = Date.from(LocalDate.now().withDayOfMonth(stagingRecord.getRepaymentDate()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            //首期还款日月份天数
            int nextFirstDay = LocalDate.now().lengthOfMonth();
            if (Integer.valueOf(nextFirstDay) < stagingRecord.getRepaymentDate()) {
                first = Date.from(LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            } else {
                first = Date.from(LocalDate.now().withDayOfMonth(stagingRecord.getRepaymentDate()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }


            //末期还款日月份天数
            int nextLastDay = LocalDate.now().plusMonths(stagingRecord.getStagingNum() - 1).lengthOfMonth();
            if (Integer.valueOf(nextLastDay) < stagingRecord.getRepaymentDate()) {
                last = Date.from(LocalDate.now().plusMonths(stagingRecord.getStagingNum() - 1).with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            } else {
                last = Date.from(LocalDate.now().plusMonths(stagingRecord.getStagingNum() - 1).withDayOfMonth(stagingRecord.getRepaymentDate()).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }
        }
        stagingRecord.setFirstRepaymentDate(first);
        stagingRecord.setLastRepaymentDate(last);
        recordService.insertStagingRecord(stagingRecord);

//        申请成功给第一级审批人发送提醒
        ApprovalSteps approvalSteps = new ApprovalSteps();
        approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
        approvalSteps.setApproveCode(2); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        TeamMessageCenters teamMessageCenters = messageToolUtils.selectApprovalProcess(approvalSteps);
        messageToolUtils.messageReminder(MessageFormats.APPROVAL_STAGING, teamMessageCenters);
    }

    /**
     * 写入分期还款申请表并查询该案件是否已经存在申请记录
     *
     * @param retrievalRecord
     */
    public void insertRetrievalRecord(RetrievalRecord retrievalRecord) {
        if (ObjectUtils.isEmpty(retrievalRecord.getCaseId())) {
            throw new GlobalException("案件id不能为空");
        }
        if (ObjectUtils.isEmpty(retrievalRecord.getReason())) {
            throw new GlobalException("申请原因不能为空");
        } else {
            if (retrievalRecord.getReason().length() > 300) {
                throw new GlobalException("申请原因字数超出限制,请重新输入");
            }
        }
        List<RetrievalRecord> retrievalRecords = recordService.selectRetrievalRecord(retrievalRecord.getCaseId());  //根据案件id查询资料调取申请表
//        判断该案件是否已经退案或者停催
        CaseManage caseManage = recordService.selectCaseManageByCaseId(retrievalRecord.getCaseId().intValue());
        if (ObjectUtils.isEmpty(caseManage)) {
            throw new GlobalException("该案件已被退案或者停催");
        }
        if (!ObjectUtils.isEmpty(retrievalRecords)) {
            for (RetrievalRecord retrievalRecord1 : retrievalRecords) {
                if (!retrievalRecord1.getProce().equals(5) && !retrievalRecord1.getProce().equals(4) && !retrievalRecord1.getProce().equals(6)) {
                    throw new GlobalException("该案件有未走完的资料调取申请，请勿重复提交");
                }
            }
        }
        retrievalRecord.setEntrustingCaseBatchNum(caseManage.getEntrustingCaseBatchNum());  //委案批次号
        retrievalRecord.setEntrustingCaseDate(caseManage.getEntrustingCaseDate());  //委案日期
        retrievalRecord.setReturnCaseDate(caseManage.getReturnCaseDate());  //退案日期
        retrievalRecord.setOdvId(caseManage.getOdvId());
        retrievalRecord.setOdvName(caseManage.getOdvName());
        retrievalRecord.setWatermarkFileState(0);
        retrievalRecord.setCanDownload(CanDownloadEnum.NO.getCode());  //能否下载（1-是，2-否）
        retrievalRecord.setMatchingResults(MatchingResultsEnum.UNMATCHED.getCode());  //匹配结果（1-匹配成功，2-匹配失败，3-匹配中，4-未匹配）
        recordService.insertRetrievalRecord(retrievalRecord);
    }

    /**
     * 写入外访申请记录详情表
     *
     * @param outsideRecord
     */
    public void insertOutsideRecord(OutsideRecord outsideRecord) {
        if (ObjectUtils.isEmpty(outsideRecord.getCaseId())) {
            throw new GlobalException("案件id不能为空");
        }
        StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
        State state = stateDesensitization.getState();
        if (!ObjectUtils.isEmpty(state)) {
            if (state.getAuthorizationStatus() == 0) {
                throw new GlobalException("未开通外访功能，请联系运营人员！");
            }
        }
        List<OutsideRecord> outsideRecords = recordService.selectOutsideRecord(outsideRecord.getCaseId());//根据案件id查询外访申请表
        CaseManage caseManage = recordService.selectCaseManageByCaseId(outsideRecord.getCaseId().intValue());
        if (ObjectUtils.isEmpty(caseManage)) {
            throw new GlobalException("该案件已被退案或者停催");
        }
        if (!ObjectUtils.isEmpty(outsideRecords)) {
            for (OutsideRecord outsideRecord1 : outsideRecords) {
                if (ObjectUtils.isEmpty(caseManage.getEntrustingCaseBatchNum())) {
//                    if (!outsideRecord1.getStateCode().equals(2) && !outsideRecord1.getStateCode().equals(3) && !outsideRecord1.getStateCode().equals(6)) {
//                        throw new GlobalException("该案件提交外访申请已失效，请走完审批流程后再提交新的申请");
//                    }
                } else {
                    if (!caseManage.getEntrustingCaseBatchNum().equals(outsideRecord1.getEntrustingCaseBatchNum())) {
//                        if (!outsideRecord1.getStateCode().equals(2) && !outsideRecord1.getStateCode().equals(3) && !outsideRecord1.getStateCode().equals(6)) {
//                            throw new GlobalException("该案件提交外访申请已失效，请走完审批流程后再提交新的申请");
//                        }
                    } else {
                        if (!outsideRecord1.getStateCode().equals(2) && !outsideRecord1.getStateCode().equals(5) && !outsideRecord1.getStateCode().equals(6)) {
                            throw new GlobalException("该案件已提交外访申请，请勿重复提交");
                        }
                    }
                }
            }
        }
        outsideRecord.setClientName(caseManage.getClientName());
        outsideRecord.setEntrustingCaseBatchNum(caseManage.getEntrustingCaseBatchNum());  //委案批次号
        outsideRecord.setEntrustingCaseDate(caseManage.getEntrustingCaseDate());  //委案日期
        outsideRecord.setReturnCaseDate(caseManage.getReturnCaseDate());  //退案日期
        outsideRecord.setOdvIds(caseManage.getOdvId());
        outsideRecord.setOdvNames(caseManage.getOdvName());
        outsideRecord.setOperationType(TokenInformation.getType());
        recordService.insertOutsideRecord(outsideRecord);

//        申请成功给第一级审批人发送提醒
        ApprovalSteps approvalSteps = new ApprovalSteps();
        approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
        approvalSteps.setApproveCode(7); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        TeamMessageCenters teamMessageCenters = messageToolUtils.selectApprovalProcess(approvalSteps);
        messageToolUtils.messageReminder(MessageFormats.APPROVAL_OUTSIDE, teamMessageCenters);
    }

    /**
     * 写入协催申请表并查询该案件是否已经存在申请记录
     *
     * @param manageQueryParam
     */
    public void insertAssistRecord(ManageQueryParam manageQueryParam) {
        if (!ObjectUtils.isEmpty(manageQueryParam.getReason())) {
            if (manageQueryParam.getReason().length() > 300) {
                throw new GlobalException("申请原因限制300字,请重新输入");
            }
        }
        //        判断该案件是否已经退案或者停催
//        ManageQueryParam manageQueryParams = new ManageQueryParam();
//        manageQueryParams.setCaseId(new Long((long) TokenInformation.getCreateid()));   //团队id
//        manageQueryParams.setCaseId(expeditingRecord.getCaseId());   //案件id
        List<CaseManage> caseManages = recordService.selectCaseManages(manageQueryParam);
        if (ObjectUtils.isEmpty(caseManages)) {
            throw new GlobalException("该案件已不属于本团队");
        }
        List<Long> list = new ArrayList<>();   //案件id集合
        if (!ObjectUtils.isEmpty(caseManages)) {
            for (CaseManage caseManage : caseManages) {
                list.add(caseManage.getCaseId());
            }
        }
        List<AssistRecord> assistRecords = recordService.selectAssistRecordInvalid(list, 0);
        if (!ObjectUtils.isEmpty(assistRecords)) {
            for (AssistRecord assistRecord1 : assistRecords) {
                assistRecord1.setInvalid(1);
            }
            recordService.updateAssistRecord(assistRecords);
        }
        List<IdsNames> idsNames = manageQueryParam.getIdsNames();  //协催人id和名字集合
        List<AssistRecord> assistRecord = new ArrayList<>();
        for (CaseManage caseManage : caseManages) {

            for (IdsNames idsNames1 : idsNames) {
                AssistRecord assistRecord1 = new AssistRecord();
                assistRecord1.setCaseId(caseManage.getCaseId());  //案件id
                assistRecord1.setReason(manageQueryParam.getReason());  //申请原因
                assistRecord1.setHelper(idsNames1.getNames());  //协催人
                assistRecord1.setHelperId(idsNames1.getHelperId());  //协催人id
                assistRecord1.setInvalid(0);
                assistRecord1.setEntrustingCaseBatchNum(caseManage.getEntrustingCaseBatchNum());
                assistRecord1.setEntrustingCaseDate(caseManage.getEntrustingCaseDate());
                assistRecord1.setReturnCaseDate(caseManage.getReturnCaseDate());
                assistRecord1.setOdvId(caseManage.getOdvId());
                assistRecord1.setOdvName(caseManage.getOdvName());
                assistRecord.add(assistRecord1);
            }

        }
        recordService.insertAssistRecord(assistRecord);

        for (IdsNames idsNames1 : idsNames) {
            Long helperId = idsNames1.getHelperId();
            TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
            teamMessageCenters.setUserId(helperId);
            teamMessageCenters.setCreateId(new Long(TokenInformation.getCreateid()));
            teamMessageCenters.setIdentification(1);
            messageToolUtils.messageReminder(MessageFormats.ASSIST_REMINDER, teamMessageCenters);
        }
    }

    /**
     * 标记案件案件查询
     *
     * @param manageQueryParam
     * @return
     */
    public void MarkCase(ManageQueryParam manageQueryParam) {
        List<CaseManage> caseManages = recordService.selectCaseManages(manageQueryParam);
        List<CaseManage> caseManageList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(caseManages)) {
            for (CaseManage caseManage : caseManages) {
                CaseManage caseManage1 = new CaseManage();
                caseManage1.setCaseId(caseManage.getCaseId());
                caseManage1.setId(caseManage.getId());
                caseManage1.setLabel(manageQueryParam.getCode().toString());
                caseManage1.setOutsourcingTeamId(new Long((long) TokenInformation.getCreateid()));
                caseManageList.add(caseManage1);
            }
        }
        caseService.updateCaseRecovery(caseManageList);
    }

    /**
     * 根据申请id批量撤销回款申请（判断案件是否为“未审核”）
     *
     * @param ids
     * @return
     */
    public void updateRepaymentRecord(List<Long> ids) {
        List<RepaymentRecord> repaymentRecords = recordService.selectRepaymentRecordProce(ids);  //回款申请判断是否是待审核案件(根据申请id集合查询回款申请数据)
        if (!ObjectUtils.isEmpty(repaymentRecords)) {
            for (RepaymentRecord repaymentRecord : repaymentRecords) {
                Integer proce = repaymentRecord.getProce();  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束，6-已撤销
                if (!proce.equals(0)) {
                    throw new GlobalException("该案件状态不是待审核状态，不能撤销");
                }
            }
        }
        recordService.updateRepaymentRecord(ids);
    }

    /**
     * 根据申请id批量撤销减免申请（判断案件是否为“未审核”）
     *
     * @param ids
     * @return
     */
    public void updateReductionRecord(List<Long> ids) {
        List<ReductionRecord> reductionRecords = recordService.selectReductionRecordProce(ids);  //减免申请判断是否是待审核案件(根据申请id集合查询减免申请数据)
        if (!ObjectUtils.isEmpty(reductionRecords)) {
            for (ReductionRecord reductionRecord : reductionRecords) {
                Integer proce = reductionRecord.getProce();  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束，6-已撤销
                if (!proce.equals(0)) {
                    throw new GlobalException("该案件状态不是待审核状态，不能撤销");
                }
            }
        }
        recordService.updateReductionRecord(ids);
    }

    /**
     * 根据申请id批量撤销分期还款申请（判断案件是否为“未审核”）
     *
     * @param ids
     * @return
     */
    public void updateStagingRecord(List<Long> ids) {
        List<StagingRecord> stagingRecords = recordService.selectStagingRecordProce(ids); //减免申请判断是否是待审核案件(根据申请id集合查询分期还款申请数据)
        if (!ObjectUtils.isEmpty(stagingRecords)) {
            for (StagingRecord stagingRecord : stagingRecords) {
                Integer proce = stagingRecord.getProce();  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束，6-已撤销
                if (!proce.equals(0)) {
                    throw new GlobalException("该案件状态不是待审核状态，不能撤销");
                }
            }
        }
        recordService.updateStagingRecord(ids);
    }

    /**
     * 根据申请id批量撤销外访申请（判断案件是否为“未审核”）
     *
     * @param ids
     * @return
     */
    public void updateOutsideRecord(List<Long> ids) {
        List<OutsideRecord> outsideRecords = recordService.selectOutsideRecordProce(ids);//减免申请判断是否是待审核案件(根据申请id集合查询外访申请数据)
        if (!ObjectUtils.isEmpty(outsideRecords)) {
            for (OutsideRecord outsideRecord : outsideRecords) {
                Integer proce = outsideRecord.getProce();  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束，6-已撤销
                if (!proce.equals(0)) {
                    throw new GlobalException("该案件状态有不是待审核状态的，不能撤销");
                }
            }
        }
        recordService.updateOutsideRecord(ids);
    }

    /**
     * 根据申请id批量撤销停案/留案/停催申请（判断案件是否为“未审核”）
     *
     * @param ids
     * @return
     */
    public void updateApplyRecord(List<Long> ids, int applyState) {
        List<ApplyRecord> applyRecords = recordService.selectApplyRecordProce(ids);//减免申请判断是否是待审核案件(根据申请id集合查询减免申请数据)
        if (!ObjectUtils.isEmpty(applyRecords)) {
            for (ApplyRecord applyRecord : applyRecords) {
                Integer proce = applyRecord.getProce();  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束，6-已撤销
                if (!proce.equals(0)) {
                    throw new GlobalException("该案件状态不是待审核状态，不能撤销");
                }
            }
        }
        recordService.updateApplyRecord(ids, applyState);
    }

    /**
     * 根据申请id批量撤销资料调取申请（判断案件是否为“未审核”）
     *
     * @param ids
     * @return
     */
    public void updateRetrievalRecord(List<Long> ids) {
        List<RetrievalRecord> retrievalRecords = recordService.selectRetrievalRecordProce(ids);//资料调取申请判断是否是待审核案件(根据申请id集合查询外访申请数据)
        if (!ObjectUtils.isEmpty(retrievalRecords)) {
            for (RetrievalRecord retrievalRecord : retrievalRecords) {
                Integer proce = retrievalRecord.getProce();  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束，6-已撤销
                if (!proce.equals(0)) {
                    throw new GlobalException("该案件状态有不是待审核状态的，不能撤销");
                }
            }
        }
        recordService.updateRetrievalRecord(ids);
//        撤销申请写入审批流程表
        List<ApproveProce> arrayList = new ArrayList<>();
        for (Long id : ids) {
            ApproveProce approveProce = new ApproveProce();
            approveProce.setApproveCode(8);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核,8-资料调取审核
            approveProce.setApplyId(id);  //申请id,根据审核类型查找不同的申请表
            approveProce.setApproveStart(4);  //审核状态,0-通过，1-不通过,4-已撤销
            approveProce.setApproveTime(new Date());  //审核时间
            approveProce.setReviewer(TokenInformation.getUsername());  //审核人
            approveProce.setReviewerId(TokenInformation.getUserid());  //审核人id
            approveProce.setDelFlag("0");
            approveProce.setOperationType(TokenInformation.getType());  //操作人类型（0-主账号;1-员工账号;2-资产端账号）
            arrayList.add(approveProce);
        }
        myApprovalService.insertApproveProce(arrayList);  //退案/留案/停催审批（写入审批记录历史表）
    }

    /**
     * 根据团队id以及案件id集合/查询条件统计金额以及可分配案件数量
     *
     * @param manageQueryParam
     * @return
     */
    public Map<String, Object> myCollectionCase(ManageQueryParam manageQueryParam) {
        List<CaseManage> caseManages = recordService.selectCaseManages(manageQueryParam);
        if (!ObjectUtils.isEmpty(caseManages)) {
            List<Long> arrayList = new ArrayList();
            int count1 = 0; //未分配案件数量
            int count2 = 0; //已分配案件数量
            BigDecimal count3 = BigDecimal.ZERO; //可分配案件总金额

            for (CaseManage caseManage : caseManages) {
//                未分配案件
                if (caseManage.getCaseState().equals("0")) {
                    count1++;
                    arrayList.add(caseManage.getCaseId());
                    count3 = count3.add(caseManage.getClientMoney());
                }
//                已分配案件
                if (caseManage.getCaseState().equals("1")) {
                    count2++;
                    arrayList.add(caseManage.getCaseId());
                    count3 = count3.add(caseManage.getClientMoney());
                }
            }
            Map<String, Object> map = new HashMap<>();
            map.put("weifenpei", count1);
            map.put("yifenpei", count2);
            map.put("zongshu", count1 + count2);
            map.put("zongjine", count3);
            map.put("arrayList", arrayList);
            return map;
        }
        return null;
    }


//--------------------------------------------------------------------------------------------------------

    /**
     * 写入回款申请表
     *
     * @param repaymentRecord
     */
    public void caseRepaymentRecord(RepaymentRecord repaymentRecord) {
        if (!ObjectUtils.isEmpty(repaymentRecord.getRepaymentMoney())) {
            int i = repaymentRecord.getRepaymentMoney().compareTo(BigDecimal.ZERO);
            if (i <= 0) {
                throw new GlobalException("回款金额不能小于或者等于0");
            }
        }
        if (ObjectUtils.isEmpty(repaymentRecord.getCaseId())) {
            throw new GlobalException("案件id不能为空");
        }

        CaseManage caseManage = recordService.selectCaseManageByCaseId(repaymentRecord.getCaseId().intValue());

        if (BaseConstant.UPLOAD_APP.equals(repaymentRecord.getVoucherAppUpload())) {
            repaymentRecord.setExamineState("待上传凭证");
        } else {
            repaymentRecord.setExamineState("待审核");
        }
        repaymentRecord.setRegistrar(TokenInformation.getUsername());   //登记人
        repaymentRecord.setRegistrarId(new Long((long) TokenInformation.getUserid()));  //登记人id
        repaymentRecord.setCreateTime(new Date());   //登记时间
        repaymentRecord.setProce(ProceEnum.TO_BE_REVIEWED.getCode());  //审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束
        repaymentRecord.setProceSort(0);  //审核进程顺序
        repaymentRecord.setDelFlag(BaseConstant.DelFlag_Being);
        repaymentRecord.setTeamId(TokenInformation.getCreateid());  //团队id
        repaymentRecord.setEntrustingCaseBatchNum(caseManage.getEntrustingCaseBatchNum());  //委案批次号
        repaymentRecord.setEntrustingCaseDate(caseManage.getEntrustingCaseDate());  //委案日期
        repaymentRecord.setReturnCaseDate(caseManage.getReturnCaseDate());  //退案日期
        repaymentRecord.setOdvId(caseManage.getOdvId());
        repaymentRecord.setOdvName(caseManage.getOdvName());
        repaymentRecord.setOperationType(TokenInformation.getType());
        Long id = recordService.insertRepaymentRecord(repaymentRecord);

        if (UserConstants.ACCOUNT_TYPE_0 == TokenInformation.getType()) {
            //团队主账号 提交的回款申请 不需要审核直接推送到资产端
            //自动审批通过
            ApprovalRecord approvalRecord = new ApprovalRecord();
            approvalRecord.setIds(Arrays.asList(id));
            approvalRecord.setApproveCode(0);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
            approvalRecord.setApproveStart(0);
            agCaseService.WriteCollectionApproval(approvalRecord);
        } else {
            //非主账号的要推送提示消息
            try {
                //        申请成功给第一级审批人发送提醒
                ApprovalSteps approvalSteps = new ApprovalSteps();
                approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
                approvalSteps.setApproveCode(0); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
                TeamMessageCenters teamMessageCenters = messageToolUtils.selectApprovalProcess(approvalSteps);
                messageToolUtils.messageReminder(MessageFormats.APPROVAL_REPAYMENT, teamMessageCenters);
            } catch (Exception e) {
                log.error("回款审批 发送消息提示 异常", e);
            }
        }


    }

    /**
     * 停催（根据所传条件查询案件申请停催）
     *
     * @param manageQueryParam
     */
    public void caseStopUrging(Map<String, Object> map) {
        //List<CaseManage> caseManages = recordService.selectCaseManages(manageQueryParam);

        Object reason = map.get("reason");
        List<CaseManage> caseManages =agLawsuitService.selectCaseManages(map);
        if (ObjectUtils.isEmpty(caseManages)) {
            throw new GlobalException("案件查询为空");
        }
        List<Long> list = new ArrayList<>();
        for (CaseManage caseManage : caseManages) {
            list.add(caseManage.getCaseId());
        }
        caseService.insertStopUrging(list,map);

//        申请成功给第一级审批人发送提醒
        ApprovalSteps approvalSteps = new ApprovalSteps();
        approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
        approvalSteps.setApproveCode(4); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
        TeamMessageCenters teamMessageCenters = messageToolUtils.selectApprovalProcess(approvalSteps);
        messageToolUtils.messageReminder(MessageFormats.STOP_URGING, teamMessageCenters);
    }

    /**
     * 留案（根据所传条件查询案件申请留案）
     *
     * @param manageQueryParam
     */
    public void caseKeepCase(Map<String, Object> map) {
        //List<CaseManage> caseManages = recordService.selectCaseManages(manageQueryParam);
        Object reason = map.get("reason");
        List<CaseManage> caseManages =agLawsuitService.selectCaseManages(map);
        if (ObjectUtils.isEmpty(caseManages)) {
            throw new GlobalException("查询案件为空");
        }
        List<Long> list = new ArrayList<>();
        RetentionWithdrawalCase retentionWithdrawalCase = new RetentionWithdrawalCase();
        for (CaseManage caseManage : caseManages) {
            list.add(caseManage.getCaseId());
        }
        retentionWithdrawalCase.setCaseId(list);
        retentionWithdrawalCase.setReason(reason.toString());
        retentionWithdrawalCase.setCaseManages(caseManages);
        caseService.insertKeepCase(retentionWithdrawalCase);

//        申请成功给第一级审批人发送提醒
        ApprovalSteps approvalSteps = new ApprovalSteps();
        approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
        approvalSteps.setApproveCode(3); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
        TeamMessageCenters teamMessageCenters = messageToolUtils.selectApprovalProcess(approvalSteps);
        messageToolUtils.messageReminder(MessageFormats.APPROVAL_STAY, teamMessageCenters);
    }

    /**
     * 退案（根据所传条件查询案件申请退案）
     *
     * @param manageQueryParam
     */
    public void caseWithdrawal(Map<String, Object> map) {
        //List<CaseManage> caseManages = recordService.selectCaseManages(manageQueryParam);
        Object reason = map.get("reason");
        List<CaseManage> caseManages =agLawsuitService.selectCaseManages(map);

        if (ObjectUtils.isEmpty(caseManages)) {
            throw new GlobalException("案件查询为空");
        }
        List<Long> list = new ArrayList<>();
        RetentionWithdrawalCase retentionWithdrawalCase = new RetentionWithdrawalCase();
        for (CaseManage caseManage : caseManages) {
            list.add(caseManage.getCaseId());
        }
        retentionWithdrawalCase.setCaseId(list);
        retentionWithdrawalCase.setReason(reason.toString());
        retentionWithdrawalCase.setCaseManages(caseManages);
        caseService.insertRecord(retentionWithdrawalCase);

//        申请成功给第一级审批人发送提醒
        ApprovalSteps approvalSteps = new ApprovalSteps();
        approvalSteps.setCreateId(TokenInformation.getCreateid());  //团队id
        approvalSteps.setApproveCode(5); //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
        TeamMessageCenters teamMessageCenters = messageToolUtils.selectApprovalProcess(approvalSteps);
        messageToolUtils.messageReminder(MessageFormats.APPROVAL_RETURN, teamMessageCenters);
    }

    /**
     * 写入协催记录详情表并修改协催申请状态
     *
     * @param assistDetails
     */
    public void collaborativeWriting(AssistDetails assistDetails) {
        recordService.insertAssistDetails(assistDetails);  //写入协催申请详情表
        AssistRecord assistRecord = new AssistRecord();
        assistRecord.setId(assistDetails.getAssistId());
        assistRecord.setState(assistDetails.getState());
        assistRecord.setAssistContent(assistDetails.getAssistContent());
        recordService.updateAssistRecordState(assistRecord);  //修改协催申请状态
//        给协催人发送消息提醒
        List<AssistRecord> assistRecords = recordService.selectAssistRecordCaseId(new Long(TokenInformation.getCreateid()), assistDetails.getCaseId());
        if (!ObjectUtils.isEmpty(assistRecords)) {
            for (AssistRecord assistRecord1 : assistRecords) {
                if (assistRecord1.getHelperId().intValue() == TokenInformation.getUserid()) {
                    continue;
                }
                TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
                teamMessageCenters.setIdentification(assistRecord1.getOperationType());
                teamMessageCenters.setUserId(assistRecord1.getHelperId());
                teamMessageCenters.setCreateId(new Long(assistRecord1.getTeamId()));
                messageToolUtils.messageReminder(MessageFormats.ASSIST_PROGRE_REMINDER, teamMessageCenters);
            }
        }
        if (!ObjectUtils.isEmpty(assistRecords) && assistRecords.get(0).getApplicantId().intValue() != TokenInformation.getUserid()) {
            AssistRecord assistRecord2 = assistRecords.get(0);
            TeamMessageCenters teamMessageCenters = new TeamMessageCenters();
            teamMessageCenters.setIdentification(assistRecord2.getOperationType());
            teamMessageCenters.setUserId(assistRecord2.getApplicantId());
            teamMessageCenters.setCreateId(new Long(assistRecord2.getTeamId()));
            messageToolUtils.messageReminder(MessageFormats.ASSIST_PROGRE_REMINDER, teamMessageCenters);
        }
    }

    /**
     * 标记工单为已处理，并向资产端发送提醒消息
     *
     * @param ids
     */
    public void updateWorkOrder(List<Long> ids) {
        recordService.updateWorkOrder(ids);
        List<WorkOrder> list = recordService.selectWorkOrderByIds(ids);  //根据id集合查询工单详情
        if (ObjectUtils.isEmpty(list)) {
            throw new GlobalException("工单查询为空");
        }
        for (WorkOrder workOrder : list) {
            messageToolUtils.messageReminderMain(MessageFormats.WORK_ORDER_PROGRE, workOrder.getCreateById());
        }
    }

    /**
     * 写入催收记录并修改案件的跟进以及催收状态
     *
     * @param urgeRecord
     */
    public void updateFollowUpStatus(UrgeRecord urgeRecord) {
        CaseManage caseManage1 = collectionService.selectCaseManageId(urgeRecord.getCaseId());
        InfoContact infoContact = collectionService.selectInfoContactById(urgeRecord.getContactId());
        if (ObjectUtils.isEmpty(infoContact)) {
            throw new GlobalException("请选择有效的联系人");
        }
        urgeRecord.setLiaison(infoContact.getContactName());
        urgeRecord.setContactMode(infoContact.getContactPhone());
//        DecryptUtils.dataDecrypt(infoContact);
//
//        urgeRecord.setLiaisonDecrypt(infoContact.getContactName());  //解密姓名
        urgeRecord.setRelation(infoContact.getContactRelation());
        if (!ObjectUtils.isEmpty(caseManage1)) {
            urgeRecord.setEntrustingCaseBatchNum(caseManage1.getEntrustingCaseBatchNum());
            urgeRecord.setEntrustingCaseDate(caseManage1.getEntrustingCaseDate());
            urgeRecord.setReturnCaseDate(caseManage1.getReturnCaseDate());
        }
        urgeRecord.setWebSide(WebSideEnum.APPEAL.getCode());
        recordService.insertUrgeRecord(urgeRecord);   //写入催收记录
        CaseManage caseManage = new CaseManage();   //修改案件的跟进以及催收状态
        if (ObjectUtils.isEmpty(caseManage1.getFollowUpStart())) {
            caseManage.setFollowUpStart(new Date());
        }
        caseManage.setCaseId(urgeRecord.getCaseId());
        caseManage.setFollowUpAst(new Date());
        caseManage.setFollowUpState(urgeRecord.getFollowUpState());
        caseManage.setUrgeState(urgeRecord.getUrgeState());
        recordService.updateCaseManage(caseManage);

        InfoLoan infoLoan = new InfoLoan();
        infoLoan.setLatestFollowUpTime(new Date());
        infoLoan.setCaseId(urgeRecord.getCaseId());
        recordService.updateInfoLoan(infoLoan);
    }

    /**
     * 根据申请人id以及其他条件查询案件详情表和外访申请表
     *
     * @param outsideRecordUtils
     */
    public List<OutsideRecordCollection> selectExternalVisit(OutsideRecordUtils outsideRecordUtils) {
        if (TokenInformation.getType() == 0) {
            return new ArrayList<>();
        }
        List<OutsideRecordCollection> outsideRecordCollections = recordService.selectOutsideRecordId(outsideRecordUtils);
        if (!ObjectUtils.isEmpty(outsideRecordCollections)) {
            for (OutsideRecordCollection outsideRecordCollection : outsideRecordCollections) {
                DecryptUtils.dataDecrypt(outsideRecordCollection);  //解密

                CaseManage caseManage = recordService.selectCaseManageCaseId(outsideRecordCollection.getCaseId());
                if (ObjectUtils.isEmpty(caseManage)) {
                    outsideRecordCollection.setButton(0);
                    continue;
                }
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (!ObjectUtils.isEmpty(caseManage.getOdvId())) {
                        if (new Long(TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId()) && new Long(TokenInformation.getUserid()).equals(caseManage.getOdvId())) {
                            outsideRecordCollection.setButton(1);
                        } else {
                            outsideRecordCollection.setButton(0);
                        }
                    } else {
                        outsideRecordCollection.setButton(0);
                    }
                } else {
                    outsideRecordCollection.setButton(0);
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (OutsideRecordCollection outsideRecordCollection : outsideRecordCollections) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(outsideRecordCollection.getClientName())) {
                        outsideRecordCollection.setClientName(DataMaskingUtils.nameMasking(outsideRecordCollection.getClientName()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(outsideRecordCollection.getClientIdcard())) {
                        outsideRecordCollection.setClientIdcard(DataMaskingUtils.idMasking(outsideRecordCollection.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(outsideRecordCollection.getClientCensusRegister())) {
                        outsideRecordCollection.setClientCensusRegister(DataMaskingUtils.Masking(outsideRecordCollection.getClientCensusRegister()));
                    }
                }
            }
        }
        return outsideRecordCollections;
    }

    /**
     * 根据协催人id以及其他条件查询案件详情表和协催申请表(并判断该协催案件是否属于本团队)
     *
     * @param applicationUtils
     */
    public List<InheritanceCollection> expeditingRegistration(ApplicationUtils applicationUtils) {
        if (TokenInformation.getType() == 0) {
            return new ArrayList<>();
        }
        List<InheritanceCollection> inheritanceCollections = recordService.selectAssistRecordHelperId(applicationUtils);
        if (!ObjectUtils.isEmpty(inheritanceCollections)) {
            for (InheritanceCollection inheritanceCollection : inheritanceCollections) {
                DecryptUtils.dataDecrypt(inheritanceCollection); //解密

                CaseManage caseManage = recordService.selectCaseManageCaseId(inheritanceCollection.getCaseId());
                if (ObjectUtils.isEmpty(caseManage)) {
                    inheritanceCollection.setButton(0);
                    continue;
                }
                if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                    if (!ObjectUtils.isEmpty(caseManage.getEntrustingCaseBatchNum())) {
                        if (new Long(TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId()) && caseManage.getEntrustingCaseBatchNum().equals(inheritanceCollection.getEntrustingCaseBatchNum())) {
                            inheritanceCollection.setButton(1);
                        } else {
                            inheritanceCollection.setButton(0);
                        }
                    } else {
                        inheritanceCollection.setButton(0);
                    }
                } else {
                    inheritanceCollection.setButton(0);
                }
            }
            List<InheritanceCollection> inheritanceCollection = desensitizationTreatment(inheritanceCollections);  //处理脱敏
            return inheritanceCollection;
        }
        return inheritanceCollections;
    }

    /**
     * 根据登录人id以及其他条件查询我的工单(并判断该工单是否属于本团队)
     *
     * @param workOrderRequest
     */
    public List<WorkOrderExtends> selectWorkOrder(WorkOrderRequest workOrderRequest) {
        if (TokenInformation.getType() == 0) {
            return new ArrayList<>();
        }
        List<WorkOrderExtends> workOrderExtends = recordService.selectWorkOrder(workOrderRequest);
        if (!ObjectUtils.isEmpty(workOrderExtends)) {
            for (WorkOrderExtends workOrderExtends1 : workOrderExtends) {
                DecryptUtils.dataDecrypt(workOrderExtends1); //解密

                CaseManage caseManage = recordService.selectCaseManageCaseId(workOrderExtends1.getCaseId());
                if (ObjectUtils.isEmpty(caseManage)) {
                    workOrderExtends1.setButton(0);
                } else {
                    if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                        if (!ObjectUtils.isEmpty(caseManage.getOdvId())) {
                            if (new Long(TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId()) && new Long(TokenInformation.getUserid()).equals(caseManage.getOdvId())) {
                                workOrderExtends1.setButton(1);
                            } else {
                                workOrderExtends1.setButton(0);
                            }
                        } else {
                            workOrderExtends1.setButton(0);
                        }
                    } else {
                        workOrderExtends1.setButton(0);
                    }
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (WorkOrderExtends workOrderExtend : workOrderExtends) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(workOrderExtend.getClientName())) {
                        workOrderExtend.setClientName(DataMaskingUtils.nameMasking(workOrderExtend.getClientName()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(workOrderExtend.getClientPhone())) {
                        workOrderExtend.setClientPhone(DataMaskingUtils.phoneMasking(workOrderExtend.getClientPhone()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(workOrderExtend.getClientIdcard())) {
                        workOrderExtend.setClientIdcard(DataMaskingUtils.idMasking(workOrderExtend.getClientIdcard()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(workOrderExtend.getCallNumber())) {
                        workOrderExtend.setCallNumber(DataMaskingUtils.phoneMasking(workOrderExtend.getCallNumber()));
                    }
                }
            }
        }
        return workOrderExtends;
    }

    /**
     * 根据登录人id以及其他条件查询我的工单(并判断该工单是否是结束跟进状态)
     *
     * @param workOrderRequest
     */
    public List<WorkOrderExtends> selectWorkOrderStatus(WorkOrderRequest workOrderRequest) {
        if (TokenInformation.getType() == 0) {
            return new ArrayList<>();
        }
        List<WorkOrderExtends> workOrderExtends = recordService.selectWorkOrder(workOrderRequest);
        if (!ObjectUtils.isEmpty(workOrderExtends)) {
            for (WorkOrderExtends workOrderExtends1 : workOrderExtends) {
                if (ObjectUtils.isEmpty(workOrderExtends1.getOrderStatus())) {
                    workOrderExtends1.setOrderStatus("待处理");
                    workOrderExtends1.setButton(1);
                } else {
                    if (workOrderExtends1.getOrderStatus().equals("已处理")) {
                        workOrderExtends1.setButton(0);
                    } else {
                        workOrderExtends1.setButton(1);
                    }
                }
            }
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (WorkOrderExtends workOrderExtend : workOrderExtends) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(workOrderExtend.getClientName())) {
                        workOrderExtend.setClientName(DataMaskingUtils.nameMasking(workOrderExtend.getClientName()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(workOrderExtend.getClientPhone())) {
                        workOrderExtend.setClientPhone(DataMaskingUtils.phoneMasking(workOrderExtend.getClientPhone()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(workOrderExtend.getClientIdcard())) {
                        workOrderExtend.setClientIdcard(DataMaskingUtils.idMasking(workOrderExtend.getClientIdcard()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(workOrderExtend.getCallNumber())) {
                        workOrderExtend.setCallNumber(DataMaskingUtils.phoneMasking(workOrderExtend.getCallNumber()));
                    }
                }
            }
        }
        return workOrderExtends;
    }

    /**
     * 判断数据是否需要脱敏
     *
     * @param manageQueryParam
     */
    public List<CaseManage> dataDesensitization(ManageQueryParam manageQueryParam) {
        List<CaseManage> caseManages = recordService.selectCaseManage(manageQueryParam);


        if (!ObjectUtils.isEmpty(caseManages)) {
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            Desensitization desensitization = null;
            if (state.getInformationStatus() == 1) {
                desensitization = stateDesensitization.getDesensitization();
            }
            for (CaseManage caseManage : caseManages) {
                DecryptUtils.dataDecrypt(caseManage);
                if (desensitization != null) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(caseManage.getClientName())) {
                        caseManage.setClientName(DataMaskingUtils.nameMasking(caseManage.getClientName()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(caseManage.getClientPhone())) {
                        caseManage.setClientPhone(DataMaskingUtils.phoneMasking(caseManage.getClientPhone()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(caseManage.getClientIdcard())) {
                        caseManage.setClientIdcard(DataMaskingUtils.idMasking(caseManage.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(caseManage.getClientCensusRegister())) {
                        caseManage.setClientCensusRegister(DataMaskingUtils.Masking(caseManage.getClientCensusRegister()));
                    }
                }
            }
        }
        return caseManages;
    }

    /**
     * 我的申请-处理脱敏
     *
     * @return
     */
    public List<InheritanceCollection> desensitizationTreatment(List<InheritanceCollection> inheritanceCollections) {
        if (!ObjectUtils.isEmpty(inheritanceCollections)) {
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            if (state.getInformationStatus() == 1) {
                Desensitization desensitization = stateDesensitization.getDesensitization();
                for (InheritanceCollection inheritanceCollection : inheritanceCollections) {
                    if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientName())) {
                        inheritanceCollection.setClientName(DataMaskingUtils.nameMasking(inheritanceCollection.getClientName()));
                    }
                    if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientPhone())) {
                        inheritanceCollection.setClientPhone(DataMaskingUtils.phoneMasking(inheritanceCollection.getClientPhone()));
                    }
                    if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientIdcard())) {
                        inheritanceCollection.setClientIdcard(DataMaskingUtils.idMasking(inheritanceCollection.getClientIdcard()));
                    }
                    if (desensitization.getHouseholds() == 1 && !ObjectUtils.isEmpty(inheritanceCollection.getClientCensusRegister())) {
                        inheritanceCollection.setClientCensusRegister(DataMaskingUtils.Masking(inheritanceCollection.getClientCensusRegister()));
                    }
                }
            }
        }
        return inheritanceCollections;
    }

    /**
     * 我的申请-判断案件是否属于登录人
     *
     * @param inheritanceCollections
     * @return
     */
    public List<InheritanceCollection> whetherItBelongs(List<InheritanceCollection> inheritanceCollections) {
        for (InheritanceCollection inheritanceCollection : inheritanceCollections) {
            DecryptUtils.dataDecrypt(inheritanceCollection);  //解密
            CaseManage caseManage = recordService.selectCaseManageCaseId(inheritanceCollection.getCaseId());
            if (ObjectUtils.isEmpty(caseManage)) {
                inheritanceCollection.setButton(0);
                continue;
            }
            if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId())) {
                if (TokenInformation.getType() == 0 && new Long(TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())) {
                    inheritanceCollection.setButton(0);
                } else {
                    if (ObjectUtils.isEmpty(caseManage.getOdvId()) && ObjectUtils.isEmpty(caseManage.getMediatorId())) {
                        inheritanceCollection.setButton(0);
                    } else {
                        if (new Long(TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId())
                                && (new Long(TokenInformation.getUserid()).equals(caseManage.getOdvId())
                                || new Long(TokenInformation.getUserid()).equals(caseManage.getMediatorId()))) {
                            inheritanceCollection.setButton(1);
                        } else {
                            inheritanceCollection.setButton(0);
                        }
                    }
                }
            } else {
                inheritanceCollection.setButton(0);
            }
        }
        return inheritanceCollections;
    }

    /**
     * 根据登录人id以及其他条件查询案件详情表和回款申请表-(判断数据是否需要脱敏)
     *
     * @param applicationUtils
     */
    public List<InheritanceCollection> repaymentRecordIdDesensitization(ApplicationUtils applicationUtils) {
        List<InheritanceCollection> inheritanceCollections = recordService.selectRepaymentRecordId(applicationUtils);
        List<InheritanceCollection> inheritanceCollections1 = whetherItBelongs(inheritanceCollections);
        List<InheritanceCollection> inheritanceCollection = desensitizationTreatment(inheritanceCollections1);  //处理脱敏
        return inheritanceCollection;
    }

    /**
     * 根据登录人id以及其他条件查询案件详情表和资料调取申请表-(判断数据是否需要脱敏)
     *
     * @param applicationUtils
     */
    public List<InheritanceCollection> retrievalRecordIdDesensitization(ApplicationUtils applicationUtils) {
        List<InheritanceCollection> inheritanceCollections = recordService.selectRetrievalRecordId(applicationUtils);
        if (!ObjectUtils.isEmpty(inheritanceCollections)) {
            for (InheritanceCollection row : inheritanceCollections) {
                if (row.getExamineState().equals("已通过") && !ObjectUtils.isEmpty(row.getRandom())) {
                    String archiveFileAddress = row.getArchiveFileAddress();
                    if (!ObjectUtils.isEmpty(archiveFileAddress)){
                        row.setPath(archiveFileAddress);
                    }
                    if (!ObjectUtils.isEmpty(row.getWatermarkedFilePath())){
                        row.setPath(row.getWatermarkedFilePath());
                    }
                    if (!ObjectUtils.isEmpty(archiveFileAddress) && !ObjectUtils.isEmpty(row.getWatermarkedFilePath())){
                        row.setPath(row.getWatermarkedFilePath()+","+archiveFileAddress);
                    }
//                    判断申请是否到期
                    int become = agCaseService.fileExpirationTime(row.getUpdateTime());
                    row.setFileExpiration(become);
                }
            }
        }
        List<InheritanceCollection> inheritanceCollections1 = whetherItBelongs(inheritanceCollections);
        List<InheritanceCollection> inheritanceCollection = desensitizationTreatment(inheritanceCollections1);  //处理脱敏
        return inheritanceCollection;
    }

    /**
     * 根据登录人id以及其他条件查询案件详情表和减免申请表-(判断数据是否需要脱敏)
     *
     * @param applicationUtils
     */
    public List<InheritanceCollection> reductionRecordIdDesensitization(ApplicationUtils applicationUtils) {
        List<InheritanceCollection> inheritanceCollections = recordService.selectReductionRecordId(applicationUtils);
        List<InheritanceCollection> inheritanceCollections1 = whetherItBelongs(inheritanceCollections);
        List<InheritanceCollection> inheritanceCollection = desensitizationTreatment(inheritanceCollections1);  //处理脱敏
        return inheritanceCollection;
    }

    /**
     * 根据登录人id以及其他条件查询案件详情表和分期申请表-(判断数据是否需要脱敏)
     *
     * @param applicationUtils
     */
    public List<InheritanceCollection> stagingRecordIdDesensitization(ApplicationUtils applicationUtils) {
        List<InheritanceCollection> inheritanceCollections = recordService.selectStagingRecordId(applicationUtils);
        List<InheritanceCollection> inheritanceCollections1 = whetherItBelongs(inheritanceCollections);
        List<InheritanceCollection> inheritanceCollection = desensitizationTreatment(inheritanceCollections1);  //处理脱敏
        return inheritanceCollection;
    }

    /**
     * 根据登录人id以及其他条件查询案件详情表和留案/退案/停催申请表-(判断数据是否需要脱敏)
     *
     * @param applicationUtils
     */
    public List<InheritanceCollection> applyStateDesensitization(ApplicationUtils applicationUtils) {
        List<InheritanceCollection> inheritanceCollections = recordService.selectApplyRecordId(applicationUtils);
        List<InheritanceCollection> inheritanceCollections1 = whetherItBelongs(inheritanceCollections);
        List<InheritanceCollection> inheritanceCollection = desensitizationTreatment(inheritanceCollections1);  //处理脱敏
        return inheritanceCollection;
    }

    /**
     * 根据登录人id以及其他条件查询案件详情表和协催申请表-(判断数据是否需要脱敏)
     *
     * @param applicationUtils
     */
    public List<InheritanceCollection> assistRecordIdDesensitization(ApplicationUtils applicationUtils) {
        List<InheritanceCollection> inheritanceCollections = recordService.selectAssistRecordId(applicationUtils);
        List<InheritanceCollection> inheritanceCollections1 = whetherItBelongs(inheritanceCollections);
        List<InheritanceCollection> inheritanceCollection = desensitizationTreatment(inheritanceCollections1);  //处理脱敏
        return inheritanceCollection;
    }

    /**
     * 根据登录人id以及其他条件查询案件详情表和我的承诺户-(判断数据是否需要脱敏)
     *
     * @return
     */
    public List<InheritanceCollection> urgeRecordIdDesensitization(ApplicationUtils applicationUtils) {
        applicationUtils.setDecryptKey(FieldEncryptUtil.fieldKey);
        List<InheritanceCollection> inheritanceCollections = recordService.selectUrgeRecordId(applicationUtils);
        List<InheritanceCollection> inheritanceCollections1 = whetherItBelongs(inheritanceCollections);
        List<InheritanceCollection> inheritanceCollection = desensitizationTreatment(inheritanceCollections1);  //处理脱敏
        return inheritanceCollection;
    }

    /**
     * 根据工单id查询工单详情以及工单跟进内容-（个人）
     *
     * @param id
     * @return
     */
    public WorkOrderFollowUp selectWorkOrderFollowUp(Long id) {
        WorkOrderFollowUp workOrderFollowUp = new WorkOrderFollowUp();
        WorkOrder workOrder = recordService.selectWorkOrderById(id);
        if (ObjectUtils.isEmpty(workOrder)) {
            throw new GlobalException("工单不存在");
        }
        if (ObjectUtils.isEmpty(workOrder.getCaseId())) {
            throw new GlobalException("案件id不能为空");
        }

        CaseManage caseManage = recordService.selectCaseManageCaseId(workOrder.getCaseId());
        if (ObjectUtils.isEmpty(caseManage)) {
            workOrderFollowUp.setButton(0);
        } else {
            if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId()) && !ObjectUtils.isEmpty(caseManage.getEntrustingCaseBatchNum())) {
                if (!ObjectUtils.isEmpty(caseManage.getOdvId())) {
                    if (new Long(TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId()) && new Long(TokenInformation.getUserid()).equals(caseManage.getOdvId()) && caseManage.getEntrustingCaseBatchNum().equals(workOrder.getEntrustingCaseBatchNum())) {
                        workOrderFollowUp.setButton(1);
                    } else {
                        workOrderFollowUp.setButton(0);
                    }
                } else {
                    workOrderFollowUp.setButton(0);
                }
            } else {
                workOrderFollowUp.setButton(0);
            }
        }
        workOrderFollowUp.setOrderStatus(workOrder.getOrderStatus());
        workOrderFollowUp.setChannelSource(workOrder.getChannelSource());
        workOrderFollowUp.setCreateTime(workOrder.getCreateTime());
        workOrderFollowUp.setQuestionContent(workOrder.getQuestionContent());
        workOrderFollowUp.setQuestionType(workOrder.getQuestionType());
        workOrderFollowUp.setComplaintLevel(workOrder.getComplaintLevel());
        workOrderFollowUp.setCname(workOrder.getCname());
        workOrderFollowUp.setEmployeeName(workOrder.getEmployeeName());
        workOrderFollowUp.setNickName(workOrder.getNickName());
        workOrderFollowUp.setCallNumber(FieldEncryptUtil.decrypt(workOrder.getCallNumber()));

        if (desensitizationAgService.checkNumberDim()) {
            //脱敏
            String callNumber = DataMaskingUtils.phoneMasking(workOrderFollowUp.getCallNumber());
            workOrderFollowUp.setCallNumber(callNumber);
        }


        List<WorkFollowUp> workFollowUps = recordService.selectWorkFollowUpByWoId(id);
        if (ObjectUtils.isEmpty(workFollowUps)) {
            workOrderFollowUp.setWorkFollowUp(new ArrayList<>());
        } else {
            for (WorkFollowUp row : workFollowUps) {
                List<WorkAnnex> workAnnexes = recordService.selectWorkAnnex(row.getId());
                if (ObjectUtils.isEmpty(workAnnexes) || workAnnexes.size() == 0) {
                    workAnnexes = new ArrayList<>();
                }
                row.setWorkAnnexs(workAnnexes);
            }
            workOrderFollowUp.setWorkFollowUp(workFollowUps);
        }
        return workOrderFollowUp;
    }

    /**
     * 根据工单id查询工单详情以及工单跟进内容-(团队)
     *
     * @param id
     * @return
     */
    public WorkOrderFollowUp selectTeamWorkOrderFollowUp(Long id) {
        WorkOrderFollowUp workOrderFollowUp = new WorkOrderFollowUp();
        WorkOrder workOrder = recordService.selectWorkOrderById(id);
        if (ObjectUtils.isEmpty(workOrder)) {
            throw new GlobalException("工单不存在");
        }
        if (ObjectUtils.isEmpty(workOrder.getCaseId())) {
            throw new GlobalException("案件id不能为空");
        }

        CaseManage caseManage = recordService.selectCaseManageCaseId(workOrder.getCaseId());

        if (ObjectUtils.isEmpty(caseManage)) {
            workOrderFollowUp.setButton(0);
        } else {
            if (!ObjectUtils.isEmpty(caseManage.getOutsourcingTeamId()) && !ObjectUtils.isEmpty(caseManage.getEntrustingCaseBatchNum())) {
                if (new Long(TokenInformation.getCreateid()).equals(caseManage.getOutsourcingTeamId()) && caseManage.getEntrustingCaseBatchNum().equals(workOrder.getEntrustingCaseBatchNum())) {
                    workOrderFollowUp.setButton(1);
                } else {
                    workOrderFollowUp.setButton(0);
                }
            } else {
                workOrderFollowUp.setButton(0);
            }
        }
        workOrderFollowUp.setOrderStatus(workOrder.getOrderStatus());
        workOrderFollowUp.setChannelSource(workOrder.getChannelSource());
        workOrderFollowUp.setCreateTime(workOrder.getCreateTime());
        workOrderFollowUp.setQuestionContent(workOrder.getQuestionContent());
        workOrderFollowUp.setQuestionType(workOrder.getQuestionType());
        workOrderFollowUp.setComplaintLevel(workOrder.getComplaintLevel());
        workOrderFollowUp.setCname(workOrder.getCname());
        workOrderFollowUp.setEmployeeName(workOrder.getEmployeeName());
        workOrderFollowUp.setNickName(workOrder.getNickName());
        List<WorkFollowUp> workFollowUps = recordService.selectWorkFollowUpByWoId(id);
        if (ObjectUtils.isEmpty(workFollowUps)) {
            workOrderFollowUp.setWorkFollowUp(new ArrayList<>());
        } else {
            for (WorkFollowUp row : workFollowUps) {
                List<WorkAnnex> workAnnexes = recordService.selectWorkAnnex(row.getId());
                if (ObjectUtils.isEmpty(workAnnexes) || workAnnexes.size() == 0) {
                    workAnnexes = new ArrayList<>();
                }
                row.setWorkAnnexs(workAnnexes);
            }
            workOrderFollowUp.setWorkFollowUp(workFollowUps);
        }
        return workOrderFollowUp;
    }

    /**
     * 我的工单-写入继续跟进内容
     *
     * @param workFollowUp
     */
    public void insertWorkOrderFollowUp(WorkFollowUp workFollowUp) {
        WorkOrder workOrder = recordService.selectWorkOrderById(workFollowUp.getId());
        if (ObjectUtils.isEmpty(workOrder)) {
            throw new GlobalException("该工单不存在或已被删除");
        } else {
            WorkOrder workOrder1 = new WorkOrder();
            workOrder1.setId(workFollowUp.getId());
            workOrder1.setHandlerId(new Long(TokenInformation.getUserid()));
            workOrder1.setHandler(TokenInformation.getUsername());
            workOrder1.setProcessingTime(new Date());
            workOrder1.setOrderStatus("处理中");
            if (ObjectUtils.isEmpty(workOrder.getOrderStatus())) {
                recordService.updateWorkOrderById(workOrder1);
            } else {
                if (workOrder.getOrderStatus().equals("已处理")) {
                    throw new GlobalException("工单为已处理状态，无法再继续跟进");
                } else {
                    recordService.updateWorkOrderById(workOrder1);
                }
            }
        }
//        工单跟进信息
        WorkFollowUp workFollowUp1 = new WorkFollowUp();
        workFollowUp1.setOrderId(workFollowUp.getId());
        workFollowUp1.setWorkFollowContent(workFollowUp.getWorkFollowContent());
        workFollowUp1.setDelFlag(UserConstants.UNIQUE);
        workFollowUp1.setOrderState(UserConstants.FOLLOW_UP);
        workFollowUp1.setCreatedBy(TokenInformation.getUsername());
        workFollowUp1.setCreatedById(new Long(TokenInformation.getUserid()));
        workFollowUp1.setCreatedTime(new Date());
        Long id = recordService.insertWorkOrderFollowUp(workFollowUp1);
//        工单跟进附件
        if (!ObjectUtils.isEmpty(workFollowUp.getWorkAnnex())) {
            List<WorkAnnexPojo> workAnnex1 = workFollowUp.getWorkAnnex();
            for (WorkAnnexPojo row : workAnnex1) {
                WorkAnnex workAnnex = new WorkAnnex();
                workAnnex.setWorkId(workFollowUp.getId());
                workAnnex.setFollowId(id);
                workAnnex.setFileUrl(row.getFileUrl());
                workAnnex.setFileName(row.getFileName());
                workAnnex.setCreatedById(new Long(TokenInformation.getUserid()));
                workAnnex.setCreatedBy(TokenInformation.getUsername());
                workAnnex.setCreatedTime(new Date());
                workAnnex.setDelFlag(BaseConstant.DelFlag_Being);
                recordService.insertWorkOrderAnnex(workAnnex);
            }
        }
    }


    public SendRecordsPojos previewTemplate(MessageTemplatePojo messageTemplatePojo) {
        List<CaseManage> caseManages = dataDesensitization(messageTemplatePojo.getManageQueryParam());
        String templateContent = messageTemplatePojo.getTemplateContent();
        Integer smsType = messageTemplatePojo.getSmsType();
        String autographName = messageTemplatePojo.getAutographName();

        SendRecordsPojos sendRecordsPojos = new SendRecordsPojos();
        ArrayList<String> caseIds = new ArrayList<>();

        int sendNum = 0;
        for (CaseManage caseManage : caseManages) {
            InfoLoan infoLoan = collectionService.selectInfoLoan(caseManage.getCaseId());

            //发送内容
            templateContent = templateContent.replace("[借款人]", caseManage.getClientName());
            templateContent = templateContent.replace("[逾期天数]", caseManage.getOverdueDays().toString());
            templateContent = templateContent.replace("[剩余应还总额]", infoLoan.getRemainingDue().toString());
            String content = "【" + autographName + "】" + templateContent;

            //查询24小时内发送成功的短信并且发送状态不为成功的
            DateTime sendtime1 = DateUtil.beginOfDay(new Date());
            DateTime sendtime2 = DateUtil.endOfDay(new Date());
            List<SendRecords> sendRecords = sendRecordService.selectFailMessage(caseManage.getClientPhone(), sendtime1, sendtime2, caseManage.getCaseId());

            //没查到当天未发送短信 或者对天发送但是发送失败了
            if (sendRecords.size() <= 0) {
                sendNum = sendNum + 1;
            } else {
                for (SendRecords sendRecord : sendRecords) {
                    //当天已经发送过短信
                    caseIds.add(sendRecord.getCaseId().toString());
                }
//
//                    if (messageTemplatePojo.getIsBatchSend() == 0) {
//                        throw new GlobalException("该用户当天已成功发送过一条短信");
//                    }
            }
        }
        sendRecordsPojos.setCaseIds(caseIds);
        sendRecordsPojos.setSendNum(sendNum);
        sendRecordsPojos.setSmsType(smsType);

        return sendRecordsPojos;
    }


    /**
     * 短信发送不管脱敏开关有没有打开都不影响
     *
     * @param manageQueryParam
     */
    public List<CaseManage> sendDesensitization(ManageQueryParam manageQueryParam) {
        List<CaseManage> caseManages = recordService.selectNeedCaseManage(manageQueryParam);

        if (!ObjectUtils.isEmpty(caseManages)) {
            StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
            State state = stateDesensitization.getState();
            Desensitization desensitization = null;
            if (state.getInformationStatus() == 1) {
                desensitization = stateDesensitization.getDesensitization();
            }
            for (CaseManage caseManage : caseManages) {
                DecryptUtils.dataDecrypt(caseManage);
            }
        }
        return caseManages;
    }

    /**
     * 改变数据查询逻辑，使用拼接sql的方法进行查询，传入特定所需的参数，完成相应的sql拼接
     * 1. 先查询所有的案件数量
     * 2. 查询所有有效号码的案件数量
     * 3. 查询所有满足条件未达到发送次数限制的案件
     * <p>
     * 2024-01-25 新增：如果前端传入案件联系人详情id则说明为案件详情页面单个手机号码点击发送，则走新的逻辑进行发送短信
     *
     * @param messageTemplatePojo 消息模板 POJO
     * @return {@link Map}<{@link String}, {@link String}>
     */
    public Map<String, String> sendCuiShouMessage(MessageTemplatePojo messageTemplatePojo) {
        ManageQueryParam manageQueryParam = messageTemplatePojo.getManageQueryParam();
        Long contactId = manageQueryParam.getContactId();
        if (!StrUtil.isBlankIfStr(contactId)) {
            // 案件详情页面单个手机号码点击发送短信
            return sendCuiShouMessageOnce(messageTemplatePojo);
        }
        Long allCount = recordService.selectNeedCaseManageCount(manageQueryParam);
        if (allCount == 0L) {
            throw new GlobalException("案件查询为空");
        }

        // 排除无效号码
        manageQueryParam.setExclusionsInvalid(1);
        // 有效号码数量
        Long validCount = recordService.selectNeedCaseManageCount(manageQueryParam);
        Long templateId = messageTemplatePojo.getTemplateId();


        // 如果开启排除超出当天发送短信次数最大值的号码时，解密key和最大允许发送次数以及模板id必须传入
        manageQueryParam.setExclusionsAllowMaxNum(1);
        manageQueryParam.setDecryptKey(FieldEncryptUtil.fieldKey);
        Long allowMaxNum = smsRedisUtils.getAllowMaxNum(templateId);
        manageQueryParam.setAllowMaxNum(allowMaxNum);
        manageQueryParam.setTemplateId(templateId);
        long start = System.currentTimeMillis();
        // 根据新的sql查询出能发送短信的案件
        List<CaseManage> caseManages = sendDesensitization(manageQueryParam);
        Integer smsType = messageTemplatePojo.getSmsType();

        String autographName = messageTemplatePojo.getAutographName();
//        for (CaseManage caseManage : caseManages) {
//            InfoLoan infoLoan = collectionService.selectInfoLoan(caseManage.getCaseId());
//
//            overdueDays = caseManage.getOverdueDays();
//            remainingDue = infoLoan.getRemainingDue();

//            // 首先先去判断一下号码是否有效
//            Long voidCount = collectionService.selectPhoneIsVoid(caseManage.getCaseId(),caseManage.getClientPhone());
//            if (voidCount <= 0){
//                // 说明是有效的手机号码
//                String clientPhone = caseManage.getClientPhone();
//                // 获取当前号码发送短信的数量
//                Long count = smsRedisUtils.getPhoneSendSMSCount(clientPhone, templateId);
//
//                /*
//                判断当前号码发送短信的次数是否大于每天最大允许发送次数
//                如果小于的话说明可以发送
//                 */
//                if (count < allowMaxNum){
//                    newCaseManageList.add(caseManage);
//                }else {
//                    overAllowMaxNum+=1;
//                }
//            }else {
//                voidPhoneNum+=1;
//            }

//        }

        long finish = System.currentTimeMillis();
        log.info("查询是否可以发送耗时：" + (finish - start));
        // 远程调用发送短信
        if (!CollectionUtils.isEmpty(caseManages)) {
            AgCollectionService bean = applicationContext.getBean(AgCollectionService.class);
            bean.sendSmsFeign(caseManages, autographName, templateId, smsType, SecurityUtils.getUsername(), SecurityUtils.getUserId());
        }

        Map<String, String> map = new HashMap<>();
        // 成功数量
        map.put("success", String.valueOf(caseManages.size()));
        // 可发送次数超出数量
        map.put("exceeded", String.valueOf(validCount - caseManages.size()));
        // 无效数量
        map.put("invalid", String.valueOf(allCount - validCount));
        return map;
    }


    private Map<String, String> sendCuiShouMessageOnce(MessageTemplatePojo messageTemplatePojo) {
        ManageQueryParam manageQueryParam = messageTemplatePojo.getManageQueryParam();
        Long templateId = messageTemplatePojo.getTemplateId();
        Long contactId = manageQueryParam.getContactId();
        InfoContact infoContact = collectionService.selectInfoContactById(contactId);
        Long caseId = manageQueryParam.getCaseIds().get(0);
        long allCount = 1L;
        // 号码有效数量
        long validCount = 0L;
        // 成功发送的数量
        Long successCount = 0L;
        Long overdueDays = null;
        BigDecimal remainingDue = null;
        String autographName = messageTemplatePojo.getAutographName();
        Integer smsType = messageTemplatePojo.getSmsType();
        // 首先先去判断一下号码是否有效
        String contactPhone = infoContact.getContactPhone();
        Long voidCount = collectionService.selectPhoneIsVoid(caseId, contactPhone);
        if (voidCount <= 0) {
            validCount += 1L;
            // 说明是有效的手机号码
            String clientPhone = FieldEncryptUtil.decrypt(contactPhone);
            // 获取当前号码发送短信的数量
            Long count = smsRedisUtils.getPhoneSendSMSCount(clientPhone, templateId);
            Long allowMaxNum = smsRedisUtils.getAllowMaxNum(templateId);
                /*
                判断当前号码发送短信的次数是否大于每天最大允许发送次数
                如果小于的话说明可以发送
                 */
            if (count < allowMaxNum) {
                // 可以发送
                successCount += 1L;
                // 查询案件
                List<CaseManage> caseManages = sendDesensitization(manageQueryParam);
//                List<SmsParameters> smsParameters = recordService.selectNeedSmsParameters(manageQueryParam);
                CaseManage caseManage = caseManages.get(0);
                InfoLoan infoLoan = collectionService.selectInfoLoan(caseId);

                overdueDays = caseManage.getOverdueDays();
                remainingDue = infoLoan.getRemainingDue();
                // 解密
                String decryptContactName = FieldEncryptUtil.decrypt(infoContact.getContactName());

                // todo 案件详情页面发送单个短信（异步）
                AgCollectionService bean = applicationContext.getBean(AgCollectionService.class);
                bean.sendSmsOnceFeign(clientPhone, autographName, templateId, smsType, SecurityUtils.getUsername(), SecurityUtils.getUserId(), caseManage.getCaseId(), decryptContactName, overdueDays, remainingDue);
//                bean.sendSmsFeign(caseManages, autographName, templateId, smsType,SecurityUtils.getUsername(), SecurityUtils.getUserId());

            }
        }

        Map<String, String> map = new HashMap<>();
        // 成功数量
        map.put("success", String.valueOf(successCount));
        // 可发送次数超出数量
        map.put("exceeded", String.valueOf(validCount - successCount));
        // 无效数量
        map.put("invalid", String.valueOf(allCount - validCount));
        return map;

    }


    /**
     * 案件详情页面发送短信
     *
     * @param clientPhone        客户电话
     * @param autographName      亲笔签名姓名
     * @param templateId         模板 ID
     * @param smsType            短信类型
     * @param caseId             案例编号
     * @param decryptContactName 解密后的联系人名称
     * @param overdueDays        逾期天数
     * @param remainingDue       剩余到期
     */
    @Async
    public void sendSmsOnceFeign(String clientPhone, String autographName, Long templateId, Integer smsType, String userName, Long userId, Long caseId, String decryptContactName, Long overdueDays, BigDecimal remainingDue) {
        R r = remoteSmsService.sendCuiShouMessage(clientPhone, autographName, templateId, smsType, userName, userId, caseId, decryptContactName, overdueDays, remainingDue);
    }


    /**
     * 异步发送短信 Feign调用
     *
     * @param newCaseManageList 新短信参数列表
     * @param autographName     亲笔签名姓名
     * @param templateId        模板 ID
     * @param smsType           短信类型
     * @param overdueDays       逾期天数
     * @param remainingDue
     */
    @Async
    public void sendSmsFeign(List<CaseManage> newCaseManageList, String autographName, Long templateId, Integer smsType, String userName, Long userId) {
        for (CaseManage caseManage : newCaseManageList) {
            R r = remoteSmsService.sendCuiShouMessage(caseManage.getClientPhone(), autographName, templateId, smsType, userName, userId, caseManage.getCaseId(), caseManage.getClientName(), caseManage.getOverdueDays(), caseManage.getRemainingDue());

        }
    }

    /**
     * 查询案件申请资料
     *
     * @param caseId 案例id
     * @return {@link List }<{@link CaseMaterialsVo }>
     */
    public List<CaseMaterialsVo> selectCaseMaterials(Long caseId) {
        Date nowDate = DateUtils.getNowDate();
        Long userId = null;
        Long teamId = null;
        if (SecurityUtils.getAccountType() == 0) {
            teamId = SecurityUtils.getTeamId();
        } else {
            userId = SecurityUtils.getUserId();
        }

        List<CaseMaterialsVo> caseMaterialsVos = myApprovalMapper.selectCaseMaterials(caseId, userId,teamId,nowDate);

        caseMaterialsVos.forEach(p -> {
            String path = localFilePath + p.getRandom();
            p.setPath(path);
        });
        return caseMaterialsVos;
    }

    /**
     * 初始还款计划
     *
     * @param stagingRecordVo 暂存记录 VO
     * @return {@link List}<{@link InfoPlan}>
     */
    public List<InfoPlanVo> initialRepaymentPlan(StagingRecordVo stagingRecordVo) {
        // 如果其中一个数据不为空，则另一个数据也不能为空
        BigDecimal downPayment = stagingRecordVo.getDownPayment();
        Date downPaymentDate = stagingRecordVo.getDownPaymentDate();
        if ((!StrUtil.isBlankIfStr(downPayment) &&  StrUtil.isBlankIfStr(downPaymentDate)) || (StrUtil.isBlankIfStr(downPayment) && !StrUtil.isBlankIfStr(downPaymentDate))) {
            throw new ServiceException("首付款或首付款日期为空！");
        }
        // 准备数据
        BigDecimal totalAmount = stagingRecordVo.getTotalRepayment();
        if (!StrUtil.isBlankIfStr(downPayment)){
            totalAmount = stagingRecordVo.getTotalRepayment().subtract(downPayment);
        }
        Integer numberOfPeriods = stagingRecordVo.getStagingNum();
        Integer repaymentDay = stagingRecordVo.getRepaymentDate();

        // 计算每期应还款金额
//        BigDecimal repaymentAmount = totalAmount.divide(BigDecimal.valueOf(numberOfPeriods), 0, RoundingMode.DOWN);
//        BigDecimal remainder = totalAmount.remainder(BigDecimal.valueOf(numberOfPeriods));
        BigDecimal repaymentAmount = totalAmount.divide(BigDecimal.valueOf(numberOfPeriods), 2, RoundingMode.DOWN);
//        BigDecimal remainder1 = totalAmount.remainder(BigDecimal.valueOf(numberOfPeriods));
        BigDecimal remainder = totalAmount.subtract(BigDecimal.valueOf(numberOfPeriods-1).multiply(repaymentAmount));
        List<InfoPlanVo> infoPlanList = new ArrayList<>();

        // 根据用户输入的还款时间和期数生成还款计划
//        log.info("生成还款计划：");
        // 获取起始计算日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());

        // 首付款不为空
        if (!StrUtil.isBlankIfStr(downPayment)){
            InfoPlanVo infoPlan = new InfoPlanVo();
            infoPlan.setHkPeriodsNumber("首付款");
            infoPlan.setJhYhMoney(downPayment);
            infoPlan.setYhDate(downPaymentDate);
            infoPlanList.add(infoPlan);
            // 如果首付款日期不为空则以首付款日期为起始计算日期
            calendar.setTime(downPaymentDate);
        }
        LocalDate currentDate = calendarToLocalDate(calendar);
        // 是否需要设置下个月为第一期
        boolean flag = calendar.get(Calendar.DAY_OF_MONTH) > repaymentDay;

        for (int i = 0; i < numberOfPeriods; i++) {
            // 是否需要以下一个月为第一期
            int copyI = flag ? i+1 : i;
            // 还款日期
            LocalDate repaymentDate = currentDate.plusMonths(copyI).withDayOfMonth(Math.min(repaymentDay, currentDate.plusMonths(copyI).lengthOfMonth()));
            BigDecimal repaymentAmount1 = repaymentAmount;
            // 在第一期加上剩余的金额
            if (i == 0) {
                repaymentAmount1 = remainder;
            }
//            if (i == 0 && remainder.compareTo(BigDecimal.ZERO) > 0) {
//                repaymentAmount1 = repaymentAmount1.add(remainder);
//            }
            ZoneId zone = ZoneId.systemDefault();
            Instant instant = repaymentDate.atStartOfDay().atZone(zone).toInstant();
            Date date = Date.from(instant);
//            log.info("还款时间：" + date + "，应还款金额：" + repaymentAmount1);
            InfoPlanVo infoPlan = new InfoPlanVo();
            infoPlan.setHkPeriodsNumber("第"+(i+1)+"期");
            infoPlan.setJhYhMoney(repaymentAmount1);
            infoPlan.setYhDate(date);
            infoPlanList.add(infoPlan);
        }
        return infoPlanList;
    }


    /**
     * 初始还款计划
     *
     * @param recordVo 暂存记录 VO
     * @return {@link List}<{@link InfoPlan}>
     */
    public List<InfoPlanVo> initialRepaymentPlan2(RecombinationRecordVo recordVo) {
        BigDecimal downPayment = recordVo.getDownPayment();
        BigDecimal finalPayment = recordVo.getFinalPayment();
        // 准备数据
        BigDecimal totalAmount = recordVo.getTotalRepayment();
        // 不可变期数
        Integer finalStagingNum = recordVo.getStagingNum();
        // 计算用的期数
        Integer numberOfPeriods = recordVo.getStagingNum();
        if (!ObjectUtils.isEmpty(downPayment)){
            numberOfPeriods = numberOfPeriods - 1;
            totalAmount = totalAmount.subtract(downPayment);
        }
        if (!ObjectUtils.isEmpty(finalPayment)){
            numberOfPeriods = numberOfPeriods - 1;
            totalAmount = totalAmount.subtract(finalPayment);
        }

        // 是否填写了首期还款和末期还款
        boolean flagA = false;
        if (!ObjectUtils.isEmpty(downPayment) && !ObjectUtils.isEmpty(finalPayment)){
            flagA = true;
        }

        Integer repaymentDay = recordVo.getRepaymentDate();

        // 计算每期应还款金额
        BigDecimal repaymentAmount = totalAmount.divide(BigDecimal.valueOf(numberOfPeriods), 2, RoundingMode.DOWN);
        // 多出来的数额
        BigDecimal remainder = totalAmount.subtract(BigDecimal.valueOf(numberOfPeriods - 1).multiply(repaymentAmount));
        List<InfoPlanVo> infoPlanList = new ArrayList<>();

        // 根据用户输入的还款时间和期数生成还款计划
//        log.info("生成还款计划：");
        // 获取起始计算日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());

        LocalDate currentDate = calendarToLocalDate(calendar);
        // 是否需要设置下个月为第一期
        boolean flag = calendar.get(Calendar.DAY_OF_MONTH) > repaymentDay;

        for (int i = 0; i < finalStagingNum; i++) {
            // 是否需要以下一个月为第一期
            int copyI = flag ? i+1 : i;
            // 还款日期
            LocalDate repaymentDate = currentDate.plusMonths(copyI).withDayOfMonth(Math.min(repaymentDay, currentDate.plusMonths(copyI).lengthOfMonth()));
            BigDecimal repaymentAmount1 = repaymentAmount;
            // （重组后还款总金额-首期还款金额-末期还款金额）每期均分（如无法均分的时候将不能均分的部分放在第二期）；
            if (flagA && i == 1) {
                repaymentAmount1 = remainder;
            }
            // 如未填写首期还款金额和末期还款金额，则每期（重组后还款总金额）均分（如无法均分的时候将不能均分的部分放在第一期）
            if (!flagA && i == 0) {
                repaymentAmount1 = remainder;
            }
            ZoneId zone = ZoneId.systemDefault();
            Instant instant = repaymentDate.atStartOfDay().atZone(zone).toInstant();
            Date date = Date.from(instant);
            InfoPlanVo infoPlan = new InfoPlanVo();
            infoPlan.setHkPeriodsNumber("第"+(i+1)+"期");
            infoPlan.setJhYhMoney(repaymentAmount1);
            if (!ObjectUtils.isEmpty(downPayment) && i == 0){
                infoPlan.setJhYhMoney(downPayment);
            }
            if (!ObjectUtils.isEmpty(finalPayment) && i == finalStagingNum - 1){
                infoPlan.setJhYhMoney(finalPayment);
            }
            infoPlan.setYhDate(date);
            infoPlanList.add(infoPlan);
        }
        return infoPlanList;
    }

    /**
     * 初始还款计划
     *
     * @param recordVo 暂存记录 VO
     * @return {@link List}<{@link InfoPlan}>
     */
    public List<InfoPlanVo> initialRepaymentPlan3(StagingRecordVo recordVo) {
        BigDecimal downPayment = recordVo.getDownPayment();
        BigDecimal finalPayment = recordVo.getFinalPayment();
        // 准备数据
        BigDecimal totalAmount = recordVo.getTotalRepayment();
        // 不可变期数
        Integer finalStagingNum = recordVo.getStagingNum();
        // 计算用的期数
        Integer numberOfPeriods = recordVo.getStagingNum();
        if (!ObjectUtils.isEmpty(downPayment)){
            numberOfPeriods = numberOfPeriods - 1;
            totalAmount = totalAmount.subtract(downPayment);
        }
        if (!ObjectUtils.isEmpty(finalPayment)){
            numberOfPeriods = numberOfPeriods - 1;
            totalAmount = totalAmount.subtract(finalPayment);
        }

        // 是否填写了首期还款和末期还款
        boolean flagA = false;
        if (!ObjectUtils.isEmpty(downPayment) && !ObjectUtils.isEmpty(finalPayment)){
            flagA = true;
        }

        Integer repaymentDay = recordVo.getRepaymentDate();

        // 计算每期应还款金额
        BigDecimal repaymentAmount = totalAmount.divide(BigDecimal.valueOf(numberOfPeriods), 2, RoundingMode.DOWN);
        // 多出来的数额
        BigDecimal remainder = totalAmount.subtract(BigDecimal.valueOf(numberOfPeriods - 1).multiply(repaymentAmount));
        List<InfoPlanVo> infoPlanList = new ArrayList<>();

        // 根据用户输入的还款时间和期数生成还款计划
//        log.info("生成还款计划：");
        // 获取起始计算日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());

        LocalDate currentDate = calendarToLocalDate(calendar);
        // 是否需要设置下个月为第一期
        boolean flag = calendar.get(Calendar.DAY_OF_MONTH) > repaymentDay;

        for (int i = 0; i < finalStagingNum; i++) {
            // 是否需要以下一个月为第一期
            int copyI = flag ? i+1 : i;
            // 还款日期
            LocalDate repaymentDate = currentDate.plusMonths(copyI).withDayOfMonth(Math.min(repaymentDay, currentDate.plusMonths(copyI).lengthOfMonth()));
            BigDecimal repaymentAmount1 = repaymentAmount;
            // （重组后还款总金额-首期还款金额-末期还款金额）每期均分（如无法均分的时候将不能均分的部分放在第二期）；
            if (flagA && i == 1) {
                repaymentAmount1 = remainder;
            }
            // 如未填写首期还款金额和末期还款金额，则每期（重组后还款总金额）均分（如无法均分的时候将不能均分的部分放在第一期）
            if (!flagA && i == 0) {
                repaymentAmount1 = remainder;
            }
            ZoneId zone = ZoneId.systemDefault();
            Instant instant = repaymentDate.atStartOfDay().atZone(zone).toInstant();
            Date date = Date.from(instant);
            InfoPlanVo infoPlan = new InfoPlanVo();
            infoPlan.setHkPeriodsNumber("第"+(i+1)+"期");
            infoPlan.setJhYhMoney(repaymentAmount1);
            if (!ObjectUtils.isEmpty(downPayment) && i == 0){
                infoPlan.setJhYhMoney(downPayment);
            }
            if (!ObjectUtils.isEmpty(finalPayment) && i == finalStagingNum - 1){
                infoPlan.setJhYhMoney(finalPayment);
            }
            infoPlan.setYhDate(date);
            infoPlanList.add(infoPlan);
        }
        return infoPlanList;
    }

    // 将 Calendar 转换为 LocalDate
    public static LocalDate calendarToLocalDate(Calendar calendar) {
        // 将 Calendar 转换为 Instant
        return calendar.toInstant()
                // 将 Instant 转换为 ZonedDateTime
                .atZone(ZoneId.systemDefault())
                // 将 ZonedDateTime 转换为 LocalDate
                .toLocalDate();
    }



    /**
     * 根据案件权限获取对应的权限分期记录信息
     *
     * @return
     */
    public List<ReorganizationVo> reorganizationAuthority(Long caseId) {
        PageUtils.clearPage();
        CaseManage caseManage = collectionService.selectCaseManageId(caseId);   //根据案件id查询案件详情
        if (ObjectUtils.isEmpty(caseManage)) throw new GlobalException("案件查询错误");
        PageUtils.startPage();
        List<ReorganizationVo> list = recordService.selectReorganizationRecordCaseId(caseId, caseManage);
        return list;
    }
}
