package com.zws.appeal.agservice;

import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.auth.NotLoginException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.JwtUtils;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.auth.AuthUtil;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.domain.Error;
import com.zws.appeal.domain.*;
import com.zws.appeal.enums.ApprovalTypeEnum;
import com.zws.appeal.pojo.ExportReminder;
import com.zws.appeal.pojo.ReturnInformation;
import com.zws.appeal.pojo.StateDesensitization;
import com.zws.appeal.pojo.TreeType;
import com.zws.appeal.pojo.call.AssignSipForm;
import com.zws.appeal.pojo.staticConst.StaticConstantClass;
import com.zws.appeal.service.SettingsService;
import com.zws.appeal.service.TeamCaseService;
import com.zws.appeal.service.TeamService;
import com.zws.appeal.utils.DesensitizationRedis;
import com.zws.appeal.utils.TokenInformation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Component
@Transactional //开启事务注解
public class AgSettingsService {

    @Autowired
    private SettingsService settingsService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private DesensitizationRedis desensitizationRedis;
    @Autowired
    private AgCaseService agCaseService;
    @Autowired
    private AgCallService agCallService;
    @Autowired
    private TeamCaseService teamCaseService;
//    /**
//     * 创建角色生成角色默认的消息中心菜单
//     *
//     * @param role
//     */
//    public void insertRoleMenu(Role role) {
//        Integer id = settingsService.insertRole(role);
//        Long[] menuIds = new Long[]{130l,131l,132l};
//        List<RoleMenu> list = new ArrayList<>();
//        for (Long menuId : menuIds) {
//            RoleMenu roleMenu = new RoleMenu();
//            roleMenu.setRoleId(id.longValue());
//            roleMenu.setMenuId(menuId);
//            list.add(roleMenu);
//        }
//        settingsService.addRoleMenu(list);  //添加角色菜单权限信息
//    }

    /**
     * 验证获取员工工号
     *
     * @return
     */
    public int employeeId() {
        int number = settingsService.selectEmployeesCreateIdMax();
        int workings = 100000;
        if (number == 0) {
            number = workings;
            workings = number + 1;
        } else {
            workings = number + 1;
        }
//        if (!ObjectUtils.isEmpty(number)) {
//            workings = number + 1;
//        }
        for (int i = 0; i < 1000; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("employeesWorking", workings);
            map.put("createId", TokenInformation.getCreateid());
            List<Employees> employees1 = settingsService.selectEmployeesCount(map);
            if (!ObjectUtils.isEmpty(employees1) || employees1.size() > 0) {
                workings = workings + 1;
            } else {
                break;
            }
        }
        return workings;
    }

    /**
     * 批量添加员工信息
     *
     * @param employees
     * @return
     */
    public Error insertEmployeess(List<Employees> employees) {
        List<String> list = new ArrayList<>();   //手机号集合
        List<String> arrayList = new ArrayList<>();  //账号集合
        Error error = new Error();
        List<Employees> employees2 = new ArrayList<>();
        List<String> errors = new ArrayList<>();
//        Employees employees3 = settingsService.selectEmployeesLimit(TokenInformation.getCreateid());    //查询该团队的最后一条员工数据
//        Integer employeesWorking = ObjectUtils.isEmpty(employees3) ? 100001 : employees3.getEmployeesWorking();
        int employeesWorking = employeeId();
        for (int i = 0; i < employees.size(); i++) {
            try {
                employeesWorking++;
                Employees employees1 = employees.get(i);
                employees1.setEmployeesWorking(employeesWorking);
                if (list.contains(employees1.getPhoneNumber())) {
                    throw new GlobalException("手机号重复,请重新输入");
                }
                if (arrayList.contains(employees1.getLoginAccount())) {
                    throw new GlobalException("登录账号重复,请重新输入");
                }
                list.add(employees1.getPhoneNumber());
                arrayList.add(employees1.getLoginAccount());
                settingsService.yanzheng(employees1);
                employees2.add(employees1);
            } catch (Exception e) {
                employeesWorking--;
                e.printStackTrace();
                int x = i + 1;
                String errorMsg = "第" + x + "行:" + "   " + e.getMessage();
                errors.add(errorMsg);
            }

        }
        error.setErrorMsg(errors);
        error.setErrorNum(errors.size());
        error.setSuccessNum(employees.size() - errors.size());
        error.setEmployees(employees2);
        return error;
    }

    /**
     * 根据角色信息查询对应的员工或者团队(审批设置)
     *
     * @param employees
     */
    public List<ReturnInformation> selectRole(Employees employees) {
        List<ReturnInformation> list = new ArrayList<>();
        if (!ObjectUtils.isEmpty(employees)) {
            if (!ObjectUtils.isEmpty(employees.getTheRole())) {
                if (employees.getTheRole().equals("调诉端主账号")) {
                    ReturnInformation returnInformation = new ReturnInformation();
                    Create idCreate = teamService.findIdCreate(employees.getCreateId());
                    returnInformation.setId(idCreate.getId());  //主键id
                    returnInformation.setCreateId(idCreate.getId());  //团队id
                    returnInformation.setName(idCreate.getCname());  //团队名称
                    list.add(returnInformation);
                } else {
                    List<Employees> employees1 = settingsService.selectEmployeesRole(employees);
                    if (ObjectUtils.isEmpty(employees1)) {
                        return new ArrayList<>();
                    }
                    for (Employees employees2 : employees1) {
                        ReturnInformation returnInformation = new ReturnInformation();
                        returnInformation.setId(employees2.getId());  //主键id
                        returnInformation.setCreateId(employees2.getCreateId());  //团队id
                        returnInformation.setName(employees2.getEmployeeName());  //员工姓名
                        list.add(returnInformation);
                    }
                }
            } else {
                Create idCreate = teamService.findIdCreate(employees.getCreateId());
                ReturnInformation returnInformation = new ReturnInformation();
                returnInformation.setId(idCreate.getId());  //主键id
                returnInformation.setCreateId(idCreate.getId());  //团队id
                returnInformation.setName(idCreate.getCname());  //团队名称
                list.add(returnInformation);
                List<Employees> employees1 = settingsService.selectEmployeesRole(employees);
                if (!ObjectUtils.isEmpty(employees1)) {
                    for (Employees employees2 : employees1) {
                        ReturnInformation returnInformations = new ReturnInformation();
                        returnInformations.setId(employees2.getId());  //主键id
                        returnInformations.setCreateId(employees2.getCreateId());  //团队id
                        returnInformations.setName(employees2.getEmployeeName());  //员工姓名
                        list.add(returnInformations);
                    }
                }
            }
        }
        return list;
    }

    /**
     * 根据角色信息查询对应的员工或者团队
     *
     * @param employees
     */
    public List<ReturnInformation> selectRolees(Employees employees) {
        List<ReturnInformation> list = new ArrayList<>();
        if (!ObjectUtils.isEmpty(employees)) {
            if (employees.getTheRole().equals("催收端主账号")) {
                ReturnInformation returnInformation = new ReturnInformation();
                Create idCreate = teamService.findIdCreate(employees.getCreateId());
                returnInformation.setId(idCreate.getId());  //主键id
                returnInformation.setCreateId(idCreate.getId());  //团队id
                returnInformation.setName(idCreate.getCname());  //团队名称
                list.add(returnInformation);
            } else {
                List<Employees> employees1 = settingsService.selectEmployeesRole(employees);
                if (ObjectUtils.isEmpty(employees1)) {
                    return new ArrayList<>();
                }
                for (Employees employees2 : employees1) {
                    ReturnInformation returnInformation = new ReturnInformation();
                    returnInformation.setId(employees2.getId());  //主键id
                    returnInformation.setCreateId(employees2.getCreateId());  //团队id
                    returnInformation.setName(employees2.getEmployeeName());  //员工姓名
                    list.add(returnInformation);
                }
            }
        }
        return list;
    }

    /**
     * 返回部门列表树类型信息
     *
     * @param
     * @return
     */
    public List<TreeType> DeptTreeType() {
        List<Dept> depts = settingsService.selectDept(TokenInformation.getCreateid());   //部门全查
        List<TreeType> treeTypes = new ArrayList<>();
        for (Dept dept : depts) {
            if (dept.getParentId().equals(0)) {    //如果为顶级部门
                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept.getId());
                treeType.setName(dept.getDeptName());
                Boolean aBoolean = ChildNode(dept.getId());
                if (aBoolean) {
                    List<TreeType> listr = listrs(depts, dept);
                    treeType.setChildren(listr);
                }

                treeTypes.add(treeType);
            }
        }
        List<TreeType> list = new ArrayList<>();
        TreeType treeType1 = new TreeType();

        Create idCreate = teamService.findIdCreate(TokenInformation.getCreateid());
        if (ObjectUtils.isEmpty(idCreate)) {
            return new ArrayList<TreeType>();
        }
        treeType1.setChildren(treeTypes);
        treeType1.setName(idCreate.getCname());
        treeType1.setId("Create:" + idCreate.getId());
        list.add(treeType1);

        return list;
    }

    /**
     * 判断是否有子列表
     *
     * @param parentId
     * @return
     */
    public Boolean ChildNode(int parentId) {
        List<Dept> deptList = settingsService.selectDeptParentId(parentId);
        return !ObjectUtils.isEmpty(deptList);
    }

    /**
     * 迭代查询子列表
     *
     * @param depts
     * @param dept
     * @return
     */
    public List<TreeType> listrs(List<Dept> depts, Dept dept) {
        List<TreeType> list = new ArrayList<>();
        for (Dept dept1 : depts) {

            if (dept1.getParentId().equals(dept.getId())) {

                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept1.getId());
                treeType.setName(dept1.getDeptName());
                List<TreeType> childrens = listrs(depts, dept1);

                treeType.setChildren(childrens);
                list.add(treeType);

            }
        }

        return list;
    }

    /**
     * 修改角色权限以及信息
     *
     * @param role
     */
    public void updateRoleOrMenu(Role role) {
        settingsService.updateRole(role);  //修改角色信息
        Employees employees = new Employees();
        employees.setRoleId(role.getId());
        employees.setTheRole(role.getRoleName());
        employees.setCreateId(TokenInformation.getCreateid());
        settingsService.updateEmployeesRole(employees);  //修改角色信息同步更新员工角色信息


        Long[] menuIds = role.getMenuIds();
        if (!ObjectUtils.isEmpty(menuIds)) {
            settingsService.deleteRoleMenu(role.getId());  //根据id删除角色菜单权限信息
            List<RoleMenu> list = new ArrayList<>();
            for (Long menuId : menuIds) {
                RoleMenu roleMenu = new RoleMenu();
                roleMenu.setRoleId(new Long((long) role.getId()));
                roleMenu.setMenuId(menuId);
                list.add(roleMenu);
            }
            settingsService.addRoleMenu(list);  //添加角色菜单权限信息
        }
    }

    /**
     * 判断团队水印开启返回水印字符串
     *
     * @param caseId
     * @return
     */
    public Map<String, Object> watermark(Long caseId) {
        Map<String, Object> map = new HashMap<>();
//        State state = settingsService.selectStateWatermark();//根据团队id查询团队设置状态（水印设置）
        StateDesensitization desensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
        if (!ObjectUtils.isEmpty(desensitization)) {
            State state = desensitization.getState();
            if (!ObjectUtils.isEmpty(state) && !ObjectUtils.isEmpty(state.getSettingStatus()) && state.getSettingStatus() == 1) {
                List<String> list = new ArrayList<>();
                Watermark watermark = settingsService.selectWatermark();
                if (ObjectUtils.isEmpty(watermark)) {
                    map.put("msg", "");
                    map.put("watermarkStatus", 0);
                    return map;
                }
                String s = switchJudge(watermark.getWatermarkOne(), caseId);
                if (!ObjectUtils.isEmpty(s)) {
                    list.add(s);
                }

                String s1 = switchJudge(watermark.getWatermarkTwo(), caseId);
                if (!ObjectUtils.isEmpty(s1)) {
                    list.add(s1);
                }

                String s2 = switchJudge(watermark.getWatermarkThree(), caseId);
                if (!ObjectUtils.isEmpty(s2)) {
                    list.add(s2);
                }

                String s3 = switchJudge(watermark.getWatermarkFour(), caseId);
                if (!ObjectUtils.isEmpty(s3)) {
                    list.add(s3);
                }
                String water = String.join("-", list);
                map.put("watermarkInformation", water);
                map.put("watermarkStatus", state.getSettingStatus());

            } else {
                map.put("msg", "");
                map.put("watermarkStatus", 0);
            }
        } else {
            map.put("msg", "");
            map.put("watermarkStatus", 0);
        }
        return map;
    }

    /**
     * 根据水印字段获取相应的信息
     *
     * @param str
     * @return
     */
    public String switchJudge(String str, Long caseId) {
        String msg = null;
        switch (str) {
            case "账号名称":
                if (TokenInformation.getType() == 0) {  //团队账号
                    Create idCreate = teamService.findIdCreate(TokenInformation.getUserid());
                    if (ObjectUtils.isEmpty(idCreate)) {
//                        throw new GlobalException("该账号不存在或已被删除");
                        throw new NotLoginException("该账号不存在或已被删除");
                    }
                    msg = idCreate.getAccount();
                } else {
                    Map<String, Object> map = new HashMap<>();
                    map.put("createId", TokenInformation.getCreateid());
                    map.put("userId", TokenInformation.getUserid());
                    Employees employees = settingsService.selectDeptFuzzyById(map);//员工账号信息查询
                    if (ObjectUtils.isEmpty(employees)) {
//                        throw new GlobalException("该账号不存在或已被删除");
                        throw new NotLoginException("该账号不存在或已被删除");
                    }
                    msg = employees.getLoginAccount();
                }
                break;
            case "日期":
                msg = DateUtils.parseDateToStr("yyyy/MM/dd", new Date());
                break;
            case "账号姓名":
                if (TokenInformation.getType() == 0) {  //团队账号
                    Create idCreate = teamService.findIdCreate(TokenInformation.getUserid());
                    msg = idCreate.getCname();
                } else {
                    Map<String, Object> map = new HashMap<>();
                    map.put("createId", TokenInformation.getCreateid());
                    map.put("userId", TokenInformation.getUserid());
                    Employees employees = settingsService.selectDeptFuzzyById(map);//员工账号信息查询
                    msg = employees.getEmployeeName();
                }
                break;
            case "案件ID":
                if (!ObjectUtils.isEmpty(caseId)) {
                    msg = caseId.toString();
                } else {
                    msg = null;
                }
                break;
            case "无":
                msg = null;
                break;
        }
        return msg;
    }

    /**
     * 根据id删除角色信息并且删除以及赋予的员工角色信息
     *
     * @param id
     */
    public void deleteRoleAndUserRole(int id) {
        settingsService.deleteRole(id);
        Employees employees = new Employees();
        employees.setRoleId(id);
        employees.setCreateId(TokenInformation.getCreateid());
        List<Employees> employees1 = settingsService.selectEmployeesRole(employees);
        if (!ObjectUtils.isEmpty(employees1)) {
            for (Employees employees2 : employees1) {
                Employees employees3 = new Employees();
                employees3.setId(employees2.getId());
//                employees3.setRoleId(null);
//                employees3.setTheRole(null);
                settingsService.deleteEmployeesRole(employees3);
            }
        }
    }

    /**
     * 根据id删除员工并回收改员工的案件
     *
     * @param id
     */
    public void deleteEmployees(int id) {
        Employees employees = settingsService.selectEmployeesId(id);

        settingsService.deleteEmployees(id);
        ExportReminder exportReminder = new ExportReminder();
        exportReminder.setMethodNo(1);
        exportReminder.setExpeditorId(new Long(id));
        exportReminder.setLoginUser(SecurityUtils.getLoginUser());
        agCaseService.updateRecovery(exportReminder);

        //取消员工的坐席分配
        List<Integer> employeeIds = new ArrayList<>();
        employeeIds.add(id);
        AssignSipForm form = new AssignSipForm();
        form.setSipNumbers(new ArrayList<>());
        form.setEmployeeIds(employeeIds);
        agCallService.assignSip(form);

        if (employees != null) {
            //更新部门是否有负责人
            settingsService.updateDeptHaveHead(employees.getDepartmentId());
        }

    }

    /**
     * 登陆人修改密码
     *
     * @param passWord
     */
    public void updatePassWord(PassWord passWord, HttpServletRequest request) {
        if (passWord.getNewPassword().contains(" ")) {
            throw new GlobalException("密码长度8-20位，同时包含数字、大小写字母及符号（除空格）");
        }
//        密码验证
        String regPassword = "^(?![A-z0-9]+$)(?=.[^%&',;=?$\\x22])(?=.*[A-z])(?=.*[0-9]).{8,20}$";
        if (!passWord.getNewPassword().matches(regPassword)) {
            throw new GlobalException("密码长度8-20位，同时包含数字、大小写字母及符号（除空格）");
        }
//        if (passWord.getNewPassword().length() < 8 || passWord.getNewPassword().length() > 20) {
//            throw new GlobalException("新密码长度8-20位之间");
//        }
//        String password = SecurityUtils.encryptPassword(passWord.getPrimaryPassWord());
//        0-团队主账号，1-员工账号
        if (TokenInformation.getType() == 0) {
            Create create = teamService.selectCreateIdPassword(TokenInformation.getUserid());
            if (!SecurityUtils.matchesPassword(passWord.getPrimaryPassWord(), create.getPassword())) {
                throw new GlobalException("原密码输入不正确，请重新输入");
            } else {
                Create create1 = new Create();
                create1.setId(TokenInformation.getUserid());
                create1.setPassword(SecurityUtils.encryptPassword(passWord.getNewPassword()));
                create1.setUpdatePasswordTime(new Date());
                int i = teamService.updateCreatePassWord(create1);
                if (i > 0) {
                    String token = SecurityUtils.getToken(request);
                    if (StringUtils.isNotEmpty(token)) {
                        String username = JwtUtils.getUserName(token);
                        // 删除用户缓存记录
                        AuthUtil.logoutByToken(token);
                    }
                }
            }
        } else {
            Employees employees1 = settingsService.selectEmployeesId(TokenInformation.getUserid());
            if (!SecurityUtils.matchesPassword(passWord.getPrimaryPassWord(), employees1.getPassword())) {
                throw new GlobalException("原密码输入不正确，请重新输入");
            } else {
                Employees employees = new Employees();
                employees.setId(TokenInformation.getUserid());
                employees.setPassword(SecurityUtils.encryptPassword(passWord.getNewPassword()));
                employees.setUpdatePasswordTime(new Date());
                int i = settingsService.updateEmployeesPassWord(employees);
                if (i > 0) {
                    String token = SecurityUtils.getToken(request);
                    if (StringUtils.isNotEmpty(token)) {
                        String username = JwtUtils.getUserName(token);
                        // 删除用户缓存记录
                        AuthUtil.logoutByToken(token);
                    }
                }
            }
        }
    }

    /**
     * 查询审批设置为空赋予默认值
     *
     * @return
     */
    public List<ApprovalSettings> insertApprovalSettings() {
        List<ApprovalSettings> list = settingsService.selectApprovalSettings();
//        TODO  正式环境需要删除
        if (TokenInformation.getCreateid() == 28) {
            return list;
        }

        if (!ObjectUtils.isEmpty(list)) {
            ApprovalTypeEnum[] values = ApprovalTypeEnum.values();
//            如果审批设置少于系统审批枚举  则循环判断将少的审批写入数据库
            if (list.size() < values.length) {
                for (ApprovalTypeEnum item : values) {
                    Integer code = item.getCode();
                    boolean verify = true;
                    for (ApprovalSettings row : list) {
                        if (Objects.equals(code, row.getApproveCode())) {
                            verify = false;
                            break;
                        } else {
                            continue;
                        }
                    }
                    if (!verify) {
                        continue;
                    }
                    ApprovalSettings approvalSetting1 = new ApprovalSettings();
                    approvalSetting1.setCreateId(TokenInformation.getCreateid());
                    approvalSetting1.setApproveCode(item.getCode());
                    approvalSetting1.setApprovalName(item.getInfo());
                    approvalSetting1.setFounder(TokenInformation.getUsername());
                    approvalSetting1.setCreationtime(new Date());
                    settingsService.addApprovalSetting(approvalSetting1);
//                    审批步骤信息--写入该团队主账号审批步骤
                    List<Role> roles = teamService.selectRole(TokenInformation.getCreateid());
                    ApprovalSteps approvalSteps = new ApprovalSteps();
                    for (Role row : roles) {
                        if (Objects.equals(row.getCreateId(), TokenInformation.getCreateid()) && row.getRoleName().equals("催收端主账号")) {
                            approvalSteps.setApprovalRole(row.getRoleName());
                            approvalSteps.setApprovalId(row.getId());
                        }
                    }
                    Create idCreate = teamService.findIdCreate(TokenInformation.getCreateid());
                    approvalSteps.setApprover(idCreate.getCname());
                    approvalSteps.setApproverId(idCreate.getId());
                    approvalSteps.setApproveCode(item.getCode());
                    approvalSteps.setCreateId(TokenInformation.getCreateid());
                    approvalSteps.setSort(1);
                    approvalSteps.setDeleteLogo(0);
                    teamService.insertApprovalStepsByTeamId(approvalSteps);
                }
                List<ApprovalSettings> lists = settingsService.selectApprovalSettings();
                return lists;
            } else {
                return list;
            }
        } else {
            settingsService.insertApprovalSettings();
            List<ApprovalSettings> lists = settingsService.selectApprovalSettings();
            return lists;
        }
    }

    /**
     * 部门ID 获取本部门及 子部门ID
     *
     * @param departmentId
     * @return
     */
    public List<Long> getSubDepartmentIds(Integer departmentId) {
        List<Long> arrayList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(departmentId)) {
            arrayList.add(new Long(departmentId));
            PageUtils.clearPage();
            List<Dept> deptList = teamCaseService.selectDept(TokenInformation.getCreateid());//根据团队id查询该团队所有部门信息
            for (Dept row : deptList) {
                String ancestors = row.getAncestors();  //祖级列表
                String[] split = ancestors.split(",");
                for (String splits : split) {
                    if (splits.equals(departmentId.toString())) {
                        arrayList.add(new Long(row.getId()));
                    }
                }
            }
        }
        return arrayList;
    }

    /**
     * 根据部门id查询部门下所有子部门id
     *
     * @param employees
     * @return
     */
    public List<Employees> selectDepartment(Employees employees) {
       /* if (!ObjectUtils.isEmpty(employees.getDepartmentId())) {
            List<Long> arrayList = new ArrayList<>();
            arrayList.add(new Long(employees.getDepartmentId()));
            PageUtils.clearPage();
            List<Dept> deptList = teamCaseService.selectDept(TokenInformation.getCreateid());//根据团队id查询该团队所有部门信息
            for (Dept row : deptList) {
                String ancestors = row.getAncestors();  //祖级列表
                String[] split = ancestors.split(",");
                for (String splits : split) {
                    if (splits.equals(employees.getDepartmentId().toString())) {
                        arrayList.add(new Long(row.getId()));
                    }
                }
            }
            employees.setDepartmentIds(arrayList);  //部门id集合
            employees.setDepartmentId(null);
        }
        PageUtils.startPage();*/
        List<Employees> employees1 = settingsService.selectDeptFuzzy(employees);
        return employees1;
    }
}
