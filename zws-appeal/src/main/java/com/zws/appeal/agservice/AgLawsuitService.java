package com.zws.appeal.agservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zws.appeal.controller.letterDoc.law.service.ILawAgencyService;
import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.domain.appeal.FilingCase;
import com.zws.appeal.domain.appeal.RefundRecord;
import com.zws.appeal.pojo.appeal.*;
import com.zws.appeal.service.appeal.*;
import com.zws.appeal.task.FilingCaseRegisterTask;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.Option;
import com.zws.common.core.domain.TimeManage;
import com.zws.common.core.enums.TimeContentFormats;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.threadpool.PoolManageMent;
import com.zws.common.core.threadpool.TaskManager;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 诉讼流程相关服务
 *
 * @Author：liuxifeng
 * @Date：2024/6/19 9:41
 * @Describe：诉讼流程
 */
@Slf4j
@Component
public class AgLawsuitService {


    @Autowired
    private IFilingCaseService filingCaseService;
    @Autowired
    private ICourtSessionService courtSessionService;
    @Autowired
    private IJudgeResultService judgeResultService;
    @Autowired
    private IExecuteCaseService executeCaseService;
    @Autowired
    private IFreezeRecordService freezeRecordService;
    @Autowired
    private IRefundRecordService refundRecordService;
    @Autowired
    private IPhoneMediationService phoneMediationService;
    @Autowired
    private ILawAgencyService lawAgencyService;


    @Autowired
    private IRegisterImportService importService;
    @Autowired
    private ITimeMangeService timeMangeService;




    /**
     * 批量立案
     *
     * @param pojo
     * @return
     */
    public int batchFilingCase(FilingCaseQueryPojo pojo) {
        Long teamId = Long.valueOf(TokenInformation.getCreateid().toString());
        pojo.setTeamId(teamId);
        pojo.setOdvId(TokenInformation.getUserid());
        List<Long> ids = null;
        if (pojo.getAllQuery()) {
            //搜索结果全选
            Map<String, Object> map = BeanUtil.beanToMap(pojo.getQuery());
            ids = filingCaseService.selectCaseIds(map);
        } else {
            //取出所有的 案件ID集合
            ids = pojo.getIds();
        }

        if (ids.isEmpty()) {
            throw new ServiceException("找不到案件");
        }

        FilingCase filingCase = new FilingCase();
        filingCase.setCourtId(pojo.getCourtId());
        filingCase.setCourt(pojo.getCourt());
        filingCase.setFilingNumber(pojo.getFilingNumber());
        filingCase.setFilingTime(pojo.getFilingTime());
        filingCase.setClerk(pojo.getClerk());
        filingCase.setContractor(pojo.getContractor());
        filingCase.setTeamId(pojo.getTeamId());

        HashMap<Long, FilingCase> map = new HashMap<>(ids.size());
        for (Long id : ids) {
            FilingCase aCase = new FilingCase();
            BeanUtil.copyProperties(filingCase, aCase);
            aCase.setCaseId(id);
            //出先重复的caseId 以最后新增数据为准 （同一案件 立案记录只能产生一条）
            map.put(id, aCase);
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return addFilingCase(map, ids, loginUser,TimeContentFormats.FILING_CASE);
    }

    /**
     * 创建工具方法: 1.判断是否已存在 2.存在则更新3.不存在则插入
     *
     * @param map 立案的详情信息
     * @param ids 需要立案的案件ids
     * @return
     */
    public int addFilingCase(HashMap<Long, FilingCase> map, List<Long> ids, LoginUser loginUser,TimeContentFormats formats) {

        //查询 数据库中 含有集合的 caseId
        List<Long> caseIds = filingCaseService.selectWithCaseId(ids);
        Date now = new Date();
        Long userId = Long.valueOf(TokenInformation.getUserid(loginUser).toString());
        String username = TokenInformation.getUsername(loginUser);
        Long teamId = Long.valueOf(TokenInformation.getCreateid(loginUser).toString());

        if (caseIds == null || caseIds.size() == 0) {
            //全部新增
            Collection<FilingCase> values = map.values();
            List<FilingCase> list = new ArrayList<>(values);
            list.forEach(s -> {
                s.setType(0);
                s.setCheckStatus(0);
                s.setCreateBy(username);
                s.setCreateById(userId);
                s.setCreateTime(now);
                s.setTeamId(teamId);
                s.setDelFlag(BaseConstant.DelFlag_Being);
            });
            int i = filingCaseService.batchInsert(list);
            return i;
        }

        //待新增ids
        List<Long> addIds = (List<Long>) CollUtil.subtract(ids, caseIds);
        //待更新ids
        List<Long> updateIds = (List<Long>) CollUtil.subtract(ids, addIds);

        addIds.forEach(s -> {
            FilingCase aCase = map.get(s);
            //初始默认待审核
            aCase.setType(0);
            aCase.setCheckStatus(0);
            aCase.setCreateBy(username);
            aCase.setCreateById(userId);
            aCase.setCreateTime(now);
            aCase.setTeamId(teamId);
            aCase.setDelFlag(BaseConstant.DelFlag_Being);
            int insert = filingCaseService.insert(aCase);
        });

        updateIds.forEach(s -> {
            FilingCase aCase = map.get(s);
            //更新参数但不需更改审核状态、阶段类型
            aCase.setUpdateBy(username);
            aCase.setUpdateById(userId);
            aCase.setUpdateTime(now);
            aCase.setTeamId(teamId);
            int i = filingCaseService.updateByCaseIdSelective(aCase);
        });
        //插入时效记录表
        this.addTimeManage(loginUser,ids,formats);
        return 0;
    }


    /**
     * 插入/更新 回款记录
     *
     * @param map
     * @param ids
     * @return
     */
    public int addRefundRecord(HashMap<Long, RefundRecord> map, LoginUser loginUser, List<Long> ids) {

        //查询数据库中已存在的 caseId
        List<Long> caseIds = refundRecordService.selectWithCaseIds(ids);
        Date now = new Date();
        Long userId = TokenInformation.getUserid(loginUser).longValue();
        String username = TokenInformation.getUsername(loginUser);
        Long teamId = TokenInformation.getCreateid(loginUser).longValue();
        if (caseIds == null || caseIds.size() == 0) {
            //全部新增
            Collection<RefundRecord> values = map.values();
            List<RefundRecord> list = new ArrayList<>(values);
            list.forEach(s -> {
                s.setTeamId(teamId);
                s.setCreateById(userId);
                s.setCreateBy(username);
                s.setCreateTime(now);
                s.setDelFlag(BaseConstant.DelFlag_Being);
            });
            int i = refundRecordService.batchInsert(list);
            return i;
        }

        //待新增ids
        List<Long> addIds = (List<Long>) CollUtil.subtract(ids, caseIds);
        //待更新ids
        List<Long> updateIds = (List<Long>) CollUtil.subtract(ids, addIds);
        addIds.forEach(s -> {
            RefundRecord aRecord = map.get(s);
            aRecord.setTeamId(teamId);
            aRecord.setCreateById(userId);
            aRecord.setCreateBy(username);
            aRecord.setCreateTime(now);
            aRecord.setDelFlag(BaseConstant.DelFlag_Being);
            int i = refundRecordService.insertSelective(aRecord);
        });


        updateIds.forEach(s -> {
            RefundRecord aRecord = map.get(s);
            aRecord.setUpdateBy(username);
            aRecord.setUpdateById(userId);
            aRecord.setUpdateTime(now);
            int i = refundRecordService.updateByCaseIdSelective(aRecord);
        });
        return 0;
    }

    /**
     * 批量立案登记-导入
     *
     * @param pojo
     * @return
     */
    public void batchRegister(FilingCasePojo pojo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        FilingCaseRegisterTask workTask = new FilingCaseRegisterTask(pojo, loginUser);
        TaskManager.addTask(workTask);
    }


    /**
     * 校验Excel文件 立案登记数据
     */
    public void verifyFilingExcelData(FilingCase filingCase,Map<String,Long> map) {
        if (filingCase.getCaseId() == null) {
            throw new ServiceException("案件ID不能为空");
        }
        if (StringUtils.isEmpty(filingCase.getCourt())) {
            throw new ServiceException("法院不能为空");
        }
        if (StringUtils.isEmpty(filingCase.getFilingNumber())) {
            throw new ServiceException("立案号不能为空");
        }
        if (filingCase.getFilingTime() == null) {
            throw new ServiceException("立案时间不能为空");
        }
        if (!map.containsKey(filingCase.getCourt())){
            throw new ServiceException("法院不存在");
        }
    }

    /**
     * 校验Excel 执行回款登记
     *
     * @param record
     */
    public void verifyRefundExcelData(RefundRecord record,Map<String,Long> map) {
        if (record.getCaseId() == null) {
            throw new ServiceException("案件ID不能为空");
        }
        if (StringUtils.isEmpty(record.getAccountName())) {
            throw new ServiceException("对方户名不能为空");
        }
        if (StringUtils.isEmpty(record.getAccountNumber())) {
            throw new ServiceException("对方账号不能为空");
        }
        if (StringUtils.isEmpty(record.getExecutiveCourt())) {
            throw new ServiceException("对应法院不能为空");
        }
        if (StringUtils.isEmpty(record.getOpeningInstitution())) {
            throw new ServiceException("对方开户机构不能为空");
        }
        if (StringUtils.isEmpty(record.getSerialNumber())) {
            throw new ServiceException("交易流水号不能为空");
        }
        if (record.getRefundAmount() == null) {
            throw new ServiceException("还款金额不能为空");
        }
        if (record.getRefundTime() == null) {
            throw new ServiceException("领款时间不能为空");
        }
        if (map!=null && !map.containsKey(record.getExecutiveCourt())){
            throw new ServiceException("法院不存在");
        }
    }

    /**
     * 导入网上立案excel数据
     */
    /**
     * @param list      Excel文档数据
     * @param loginUser 当前登录对象
     * @param caseIds   当前用户在网上立案阶段的所有案件ID
     * @return
     */
    public List<Map<String, Object>> addFilingExcelData(List<FilingCase> list, LoginUser loginUser, List<Long> caseIds) {

        //指定机构所有法院
        Long teamId = TokenInformation.getCreateid(loginUser).longValue();
        Map<String,Long> map = lawAgencyService.selectWithLawAgency(teamId);
        if (map==null || map.size()==0){throw new ServiceException("该机构创建法院数量为 0");}

        //校验数据 => 记录失败数量 List
        List<Map<String, Object>> mapList = new ArrayList<>();
        ArrayList<Long> ids = new ArrayList<>(list.size());
        HashMap<Long, FilingCase> hashMap = new HashMap<>(list.size());
        for (int i = 0; i < list.size(); i++) {
            FilingCase pojo = list.get(i);
            try {
                this.verifyFilingExcelData(pojo,map);
                if (!caseIds.contains(pojo.getCaseId())) {
                    throw new ServiceException("该案件不在本阶段范围内");
                }
                pojo.setCourtId(map.get(pojo.getCourt()));
                ids.add(pojo.getCaseId());
                hashMap.put(pojo.getCaseId(), pojo);
            } catch (Exception e) {
                Map<String, Object> maps = new LinkedHashMap<>();
                maps.put("案件ID", pojo.getCaseId());
                maps.put("法院", pojo.getCourt());
                maps.put("立案号", pojo.getFilingNumber());
                maps.put("立案时间", pojo.getFilingTime());
                maps.put("承办人", pojo.getContractor());
                maps.put("书记员", pojo.getClerk());
                maps.put("错误信息", e.getMessage());
                mapList.add(maps);
            }
        }
        //插入/更新 调用工具方法
        if (ids == null || ids.size() == 0) {
            return mapList;
        }
        this.addFilingCase(hashMap, ids, loginUser,TimeContentFormats.FILING_REGISTER);
        return mapList;
    }


    /**
     * 导入执行回款Excel
     */
    public List<Map<String, Object>> addRefundExcelData(List<RefundRecord> list, LoginUser loginUser,  List<Long> caseIds) {


        //指定机构所有法院
        Long teamId = TokenInformation.getCreateid(loginUser).longValue();
        Map<String,Long> map = lawAgencyService.selectWithLawAgency(teamId);
        if (map==null || map.size()==0){throw new ServiceException("该机构创建法院数量为 0");}

        //校验数据 => 记录失败数量 List
        List<Map<String, Object>> mapList = new ArrayList<>();
        ArrayList<Long> ids = new ArrayList<>(list.size());
        HashMap<Long, RefundRecord> hashMap = new HashMap<>(list.size());
        for (int i = 0; i < list.size(); i++) {
            RefundRecord pojo = list.get(i);
            try {
                this.verifyRefundExcelData(pojo,map);
                if (!caseIds.contains(pojo.getCaseId())) {
                    throw new ServiceException("该案件不在本阶段范围内");
                }
                ids.add(pojo.getCaseId());
                pojo.setCourtId(map.get(pojo.getExecutiveCourt()));

                hashMap.put(pojo.getCaseId(), pojo);
            } catch (Exception e) {
                Map<String, Object> maps = new LinkedHashMap<>();
                maps.put("案件ID", pojo.getCaseId());
                maps.put("对应法院", pojo.getExecutiveCourt());
                maps.put("对方户名", pojo.getAccountName());
                maps.put("对方开户机构", pojo.getOpeningInstitution());
                maps.put("对方账户", pojo.getAccountNumber());
                maps.put("交易流水号", pojo.getSerialNumber());
                maps.put("还款金额", pojo.getRefundAmount());
                maps.put("领款时间", pojo.getRefundTime());
                maps.put("错误信息", e.getMessage());
                mapList.add(maps);
            }
        }
        //插入/更新 调用工具方法
        if (ids == null || ids.size() == 0) {
            return mapList;
        }
        int i = addRefundRecord(hashMap, loginUser, ids);
        return mapList;
    }


    /**
     * 批量案件转移 调解流程、诉讼流程、保全流程
     *
     * @param hashMap
     */
    public void batchTransfer(Map<String, Object> hashMap) {
        boolean bo = !hashMap.containsKey("saveStage") && !hashMap.containsKey("disposeStage") && !hashMap.containsKey("mediatedStage");
        if (bo) {
            throw new ServiceException("转移阶段不能为空");
        }
        List<Long> ids = null;
        List<Long> freezeIds = null;
        Map<String, Object> map = new HashMap<>();
        if (hashMap.containsKey("saveStage")) {
            //保全阶段流转 需要保全信息表主键id 进行流转
            FreezeCasePojo freezeCasePojo = BeanUtil.mapToBean(hashMap, FreezeCasePojo.class, true);
            freezeCasePojo.setTeamId(TokenInformation.getCreateid().longValue());
            freezeCasePojo.setOdvId(TokenInformation.getUserid());
            Boolean ao = hashMap.containsKey("originalStage") && StringUtils.isNotEmpty(hashMap.get("originalStage").toString());
            if (ao) {
                freezeCasePojo.setSaveStage(hashMap.get("originalStage").toString());
            }
            if (freezeCasePojo.getAllQuery()) {
                freezeIds = freezeRecordService.selectFreezeIds(freezeCasePojo);
            } else {
                freezeIds = freezeCasePojo.getFreezeIds();
            }

            //插入时效管理表
            List<Long> caseIds = freezeRecordService.selectCaseIds(freezeCasePojo);
            this.addTimeManage(SecurityUtils.getLoginUser(),caseIds,TimeContentFormats.BATCH_TRANSFER);

            map.put("saveStage", hashMap.get("saveStage").toString());
            map.put("freezeIds", freezeIds);
            int i = freezeRecordService.updateWithStage(map);
            return;
        } else {
            ids = searchIds(hashMap);
        }
        if (ids == null || ids.size() == 0) {
            throw new ServiceException("案件Id不存在");
        }

        List<Option> list = filingCaseService.selectWithLawsuitStage();
        if (list==null || list.size()==0){throw new ServiceException("案件流转阶段配置不能为空");}
        List<String> stageCollect = list.stream().map(Option::getInfo).collect(Collectors.toList());
        //更新CaseManage表阶段
        map.put("ids", ids);
        if (hashMap.containsKey("disposeStage")) {
            String disposeStage = hashMap.get("disposeStage").toString();
            map.put("disposeStage", disposeStage);
            //如果阶段 属于诉讼执行或执行回款大阶段,则将调解+诉讼流程一并更新
            if (stageCollect.contains(disposeStage)){map.put("mediatedStage", disposeStage);}
        }
        if (hashMap.containsKey("mediatedStage")) {
            String mediatedStage = hashMap.get("mediatedStage").toString();
            map.put("mediatedStage", mediatedStage);
            //如果阶段 属于诉讼执行或执行回款大阶段,则将调解+诉讼流程一并更新
            if (stageCollect.contains(mediatedStage)){map.put("disposeStage", mediatedStage);}
        }
        map.put("updateTime", new Date());
        map.put("updateBy", TokenInformation.getUsername());
        int i = filingCaseService.updateWithStage(map);

        //插入时效管理表
        this.addTimeManage(SecurityUtils.getLoginUser(),ids,TimeContentFormats.BATCH_TRANSFER);
    }


    /**
     * 获取案件ids
     *
     * @param map 前端传参
     * @return
     */
    public List<Long> searchIds(Map<String, Object> map) {

        if (!map.containsKey("sign") || map.get("sign") == null) {
            throw new ServiceException("页面标识不能为空");
        }
        String sign = (String) map.get("sign");
        Boolean bo = map.containsKey("originalStage") && StringUtils.isNotEmpty(map.get("originalStage").toString());
        if (!map.containsKey("teamId") || map.get("teamId") == null) {
            map.put("teamId", TokenInformation.getCreateid().longValue());
        }
        if (!map.containsKey("odvId") || map.get("odvId") == null) {
            map.put("odvId", TokenInformation.getUserid());
        }
        switch (sign) {
            case "诉前调解":
                PhoneMediationPojo mediationPojo = BeanUtil.mapToBean(map, PhoneMediationPojo.class, true);
                if (bo) {
                    mediationPojo.setMediatedStage(map.get("originalStage").toString());
                }
                if (mediationPojo.getAllQuery()) {
                    return phoneMediationService.getCaseIds(mediationPojo);
                }
                return mediationPojo.getCaseIds();
            case "网上立案":
                FilingCasePojo filingCasePojo = BeanUtil.mapToBean(map, FilingCasePojo.class, true);
                if (bo) {
                    filingCasePojo.setDisposeStage(map.get("originalStage").toString());
                }
                if (filingCasePojo.getAllQuery()) {
                    Map<String, Object> selectMap = BeanUtil.beanToMap(filingCasePojo);
                    return filingCaseService.selectCaseIds(selectMap);
                }
                return filingCasePojo.getIds() == null ? filingCasePojo.getCaseIds() : filingCasePojo.getIds();

            case "立案开庭":
                LawInforPojo lawInforPojo = BeanUtil.mapToBean(map, LawInforPojo.class, true);
                if (bo) {
                    lawInforPojo.setDisposeStage(map.get("originalStage").toString());
                }
                if (lawInforPojo.getAllQuery()) {
                    return courtSessionService.getCaseIds(lawInforPojo);
                }
                return lawInforPojo.getCaseIds();
            case "判决与结果":
                JudgePojo judgePojo = BeanUtil.mapToBean(map, JudgePojo.class, true);
                if (bo) {
                    judgePojo.setDisposeStage(map.get("originalStage").toString());
                }
                if (judgePojo.getAllQuery()) {
                    return judgeResultService.getCaseIds(judgePojo);
                }
                return judgePojo.getCaseIds();
            case "诉讼执行":
                ExecuteCasePojo executeCasePojo = BeanUtil.mapToBean(map, ExecuteCasePojo.class, true);
                if (bo) {
                    executeCasePojo.setDisposeStage(map.get("originalStage").toString());
                }
                if (executeCasePojo.getAllQuery()) {
                    return executeCaseService.selectCaseIds(executeCasePojo);
                }
                return executeCasePojo.getIds();
            case "调解执行":
                ExecuteCasePojo executePojo = BeanUtil.mapToBean(map, ExecuteCasePojo.class, true);
                if (bo) {
                    executePojo.setMediatedStage(map.get("originalStage").toString());
                }
                if (executePojo.getAllQuery()) {
                    return executeCaseService.selectCaseIds(executePojo);
                }
                return executePojo.getIds();

            case "执行回款":
                RefundRecordPojo recordPojo = BeanUtil.mapToBean(map, RefundRecordPojo.class, true);
                if (bo) {
                    recordPojo.setDisposeStage(map.get("originalStage").toString());
                }
                if (recordPojo.getAllQuery()) {
                    return refundRecordService.selectCaseIds(recordPojo);
                }
                return recordPojo.getCaseIds();
            case "诉讼保全":
                FreezeCasePojo freezeCasePojo = BeanUtil.mapToBean(map, FreezeCasePojo.class, true);
                if (bo) {
                    freezeCasePojo.setSaveStage(map.get("originalStage").toString());
                }
                if (freezeCasePojo.getAllQuery()) {
                    return freezeRecordService.selectCaseIds(freezeCasePojo);
                }
                return freezeCasePojo.getIds();

        }
        return null;
    }

    /**
     * 停案、留案、退案 操作查询数据
     *
     * @param map
     * @return
     */
    public List<CaseManage> selectCaseManages(Map<String, Object> map) {
        if (!map.containsKey("sign") || map.get("sign") == null) {
            throw new ServiceException("页面标识不能为空");
        }
        String sign = (String) map.get("sign");
        map.put("teamId", TokenInformation.getCreateid().longValue());
        map.put("odvId", TokenInformation.getUserid());
        map.put("decryptKey", FieldEncryptUtil.fieldKey);
        switch (sign) {
            case "诉前调解":
                PhoneMediationPojo mediationPojo = BeanUtil.mapToBean(map, PhoneMediationPojo.class, true);
                mediationPojo.setCaseIds(mediationPojo.getCaseIds());
                return phoneMediationService.selectCaseManages(mediationPojo);
            case "网上立案":
                //FilingCasePojo filingCasePojo = BeanUtil.mapToBean(map, FilingCasePojo.class, true);
                return filingCaseService.selectCaseManages(map);

        }
        return null;
    }

    /**
     * 预览金额、数量、已分配、未分配 数量
     *
     * @param map
     * @return
     */
    public Map<String, Object> selectMapWithCollectionCase(Map<String, Object> map) {

        if (!map.containsKey("allQuery")) {
            throw new ServiceException("全选标识不能为空");
        }
        if (!map.containsKey("sign") || map.get("sign") == null) {
            throw new ServiceException("页面标识不能为空");
        }

        map.put("odvId", TokenInformation.getUserid().longValue());
        map.put("teamId", TokenInformation.getCreateid().longValue());
        map.put("decryptKey", FieldEncryptUtil.fieldKey);

        Boolean allQuery = (Boolean) map.get("allQuery");
        String sign = (String) map.get("sign");

//        String key =StrUtil.equals(sign,"网上立案")? "ids" : "caseIds";
        List<Long> ids = (List<Long>) map.get("ids");

        if (ObjectUtils.isEmpty(ids) && !allQuery) {
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("zongshu", 0);
            resultMap.put("zongjine", 0);
            resultMap.put("yifenpei", 0L);
            resultMap.put("weifenpei", 0L);
            return resultMap;
        }

        Long yiFenPei = 0L, weiFenPei = 0L;
        Map<String, Object> mapList = null;
        switch (sign) {
            case "诉前调解":
                mapList = phoneMediationService.selectCaseManageMoneySize(map);
                //未分配
                List<Integer> list = new ArrayList<>();
                list.add(0);
                map.put("caseStates", list);
                weiFenPei = phoneMediationService.selectCaseStateSize(map).longValue();
                //weiFenPei = ObjectUtils.isEmpty(caseManages) ? 0L : Long.valueOf(caseManages.size());
                //已分配
                List<Integer> lists = new ArrayList<>();
                lists.add(1);
                lists.add(3);
                map.put("caseStates", lists);
                yiFenPei = phoneMediationService.selectCaseStateSize(map).longValue();
                //yiFenPei = ObjectUtils.isEmpty(caseManage) ? 0L : Long.valueOf(caseManage.size());

                break;
            case "网上立案":
                mapList = filingCaseService.selectCaseManageMoneySize(map);
                //未分配
                List<Integer> stateList = new ArrayList<>();
                stateList.add(0);
                map.put("caseStates", stateList);
                weiFenPei = filingCaseService.selectCaseStateSize(map).longValue();
                //weiFenPei = ObjectUtils.isEmpty(caseManageState) ? 0L : Long.valueOf(caseManageState.size());
                //已分配
                List<Integer> stateLists = new ArrayList<>();
                stateLists.add(1);
                stateLists.add(3);
                map.put("caseStates", stateLists);
                yiFenPei = filingCaseService.selectCaseStateSize(map).longValue();
                //yiFenPei = ObjectUtils.isEmpty(caseManageStates) ? 0L : Long.valueOf(caseManageStates.size());

                break;

        }

        mapList.put("yifenpei", yiFenPei);
        mapList.put("weifenpei", weiFenPei);
        return mapList;
    }


    /**
     * 插入时效记录表 asset_time_manage
     * @param loginUser 登录人信息
     * @param ids 案件id集合
     * @param formats 操作类型-枚举类
     */
    public void addTimeManage(LoginUser loginUser,List<Long> ids,TimeContentFormats formats){

        if (ids==null || ids.size()==0){
            log.error("插入时效记录表,案件id不能为空;"+formats.getContactStateContent());
            return;
        }
        Long teamId = SecurityUtils.getTeamId(loginUser);
        String username = SecurityUtils.getUsername(loginUser);
        Integer userId = SecurityUtils.getUserId(loginUser).intValue();
        Integer accountType = SecurityUtils.getAccountType(loginUser);

        List<TimeManage> list = new ArrayList<>(ids.size());
        for (Long caseId : ids) {
            TimeManage timeManage = new TimeManage();
            //调诉端来源 origin=3
            timeManage.setOrigin(3);
            timeManage.setCaseId(caseId);
            timeManage.setCreateTime(DateUtils.getNowDate());
            timeManage.setCreateBy(username);
            timeManage.setContactId(userId);
            timeManage.setDelFlag(0);
            timeManage.setOperationType(accountType);
            timeManage.setContactInformation(formats.getInformation());
            timeManage.setContactStateContent(formats.getContactStateContent());
            list.add(timeManage);
        }

        int i = timeMangeService.batchInsert(list);
    }
}