package com.zws.appeal.agservice;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.google.code.kaptcha.Producer;
import com.zws.common.core.constant.Constants;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.enums.CooperationEnum;
import com.zws.common.core.exception.CaptchaException;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.PassException;
import com.zws.common.core.utils.*;
import com.zws.common.core.utils.ip.IpUtils;
import com.zws.common.core.utils.sign.Base64;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.redis.service.RedisMessageService;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.domain.*;
import com.zws.appeal.domain.record.OutsideRecord;
import com.zws.appeal.domain.record.UrgeRecord;
import com.zws.appeal.enums.TerminalTypeEnum;
import com.zws.appeal.loginjournal.DisposeAsyncLogService;
import com.zws.appeal.pojo.Criteria;
import com.zws.appeal.pojo.VisitingEntityPojo;
import com.zws.appeal.service.*;
import com.zws.appeal.utils.DataMaskingUtils;
import com.zws.appeal.utils.DesensitizationRedis;
import com.zws.appeal.utils.TokenInformation;
import com.zws.system.api.RemoteUserService;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Transactional //开启事务注解
@Service
@RefreshScope
public class AgLoginService {

    @Autowired
    private SettingsService settingsService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private DisposeAsyncLogService disposeAsyncLogService;
    @Autowired
    private CollectionService collectionService;
    @Autowired
    private AgCollectionService agCollectionService;
    @Autowired
    private RecordService recordService;
    @Autowired
    private RedisMessageService redisMessageService;
    @Autowired
    private DesensitizationRedis desensitizationRedis;
    @Autowired
    private MenuService menuService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private RemoteUserService remoteUserService;


    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    /**
     * 是否返回详细信息。true返回详细信息，false返回简单信息，用于渗透。
     */
    @Value("${penetration.information.detail.return.flag}")
    private boolean informationDetailReturnFlag;

    /**
     * 生成验证码
     */
    public AjaxResult createCapcha() throws IOException, CaptchaException {
        AjaxResult ajax = AjaxResult.success();
        boolean captchaOnOff = true;
        ajax.put("captchaOnOff", captchaOnOff);
        if (!captchaOnOff) {
            return ajax;
        }

        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        String captchaType = "math";
        // 生成验证码
        if ("math".equals(captchaType)) {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        } else if ("char".equals(captchaType)) {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }

        redisService.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            return AjaxResult.error(e.getMessage());
        }

        ajax.put("uuid", uuid);
        ajax.put("img", Base64.encode(os.toByteArray()));
        return ajax;
    }

    /**
     * 校验验证码
     */
    public void checkCapcha(String code, String uuid) throws CaptchaException {
        if (StringUtils.isEmpty(code)) {
            throw new CaptchaException("验证码不能为空");
        }
        if (StringUtils.isEmpty(uuid)) {
            throw new CaptchaException("验证码已失效");
        }
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String captcha = redisService.getCacheObject(verifyKey, String.class);
        redisService.deleteObject(verifyKey);

        if (!code.equalsIgnoreCase(captcha)) {
            throw new CaptchaException("验证码错误");
        }
    }

    /**
     * 登录信息验证
     *
     * @param
     * @return
     */
    public LoginUser LoginUser(String username, String password, String internalIp, String browserFingerprint,String teamLevelType) {
        if (ObjectUtils.isEmpty(username)) {
            recordLogininforS(null, username, Constants.LOGIN_FAIL, "账号不能为空", null, null, internalIp, browserFingerprint);
            throw new GlobalException("账号不能为空");
        }
        if (ObjectUtils.isEmpty(password)) {
            recordLogininforS(null, username, Constants.LOGIN_FAIL, "密码不能为空", null, null, internalIp, browserFingerprint);
            throw new GlobalException("密码不能为空");
        }
        if (username.length() <= 3) {
            if(informationDetailReturnFlag){
                throw new GlobalException("账号或密码错误！");
            }else{
                throw new GlobalException("登录异常,请检查登录信息！");
            }
        }

//        根据key查找redis中的登录限制
        Integer number = redisService.getCacheObject(UserConstants.SAAS_VERIFY_LOGIN_ERROR_NUMBER + username, Integer.class);
        Long time = redisService.getCacheObject(UserConstants.SAAS_VERIFY_LOGIN_ERROR_TIME + username, Long.class);
        if (number != null && number >= 5 && time != null) {
            long date = System.currentTimeMillis();
            long division = (date - time) / 60000L + 1;
            if (division <= 30) {
                long small = 30 - division + 1;
                if(informationDetailReturnFlag){
                    throw new GlobalException("该账号多次登录失败，已被限制；请" + small + "分钟后再试");
                }else{
                    throw new GlobalException("登录异常,请检查登录信息！");
                }
            }
        }

        String substring = username.substring(username.length() - 3);
        String substring1 = substring.substring(0, 1);
        LoginUser loginUser = null;
        try {
            if ("@".equals(substring1)) {
                Employees employees = new Employees();
                employees.setLoginAccount(username);
                employees.setPassword(password);
                employees.setTeamLevelType(teamLevelType);
                loginUser = settingsService.selectLogin(employees);

            } else {
                loginUser = teamService.findCreateAccount(username, password,teamLevelType);
            }
            //移除当前团队的导出限制按钮权限
            /*if (loginUser!=null){
                Set<String> teamExport=teamService.selectCloseTeamExport(TokenInformation.getCreateid(loginUser));
                loginUser.getPermissions().removeAll(teamExport);
            }*/
        } catch (PassException e) {
            recordLogininforS(null, username, Constants.LOGIN_FAIL, e.getMessage(), null, null, internalIp, browserFingerprint);
//            如果登录失败则增加限制次数
            Integer numbers = redisService.getCacheObject(UserConstants.SAAS_VERIFY_LOGIN_ERROR_NUMBER + username, Integer.class);
//            Long times = redisService.getCacheObject(UserConstants.VERIFY_LOGIN_ERROR_TIME + username);
            if (numbers == null) {
                numbers = 1;
            } else {
                numbers = numbers + 1;
                if (numbers == 5) {
                    Date date = new Date();
                    redisService.setCacheObject(UserConstants.SAAS_VERIFY_LOGIN_ERROR_TIME + username, date.getTime(), 30L, TimeUnit.MINUTES);
                }
            }
            redisService.setCacheObject(UserConstants.SAAS_VERIFY_LOGIN_ERROR_NUMBER + username, numbers, 30L, TimeUnit.MINUTES);
            int size = 5 - numbers;
            if (size == 0) {
                if(informationDetailReturnFlag){
                    throw new GlobalException(e.getMessage() + "该账号多次登录失败，已被限制；请" + 30 + "分钟后再试");
                }else{
                    throw new GlobalException("登录异常,请检查登录信息！");
                }
            }
            if(informationDetailReturnFlag){
                throw new GlobalException(e.getMessage() + "    剩余登录次数：" + size + "次");
            }else{
                throw new GlobalException("登录异常,请检查登录信息！");
            }
        } catch (GlobalException e) {
            if(informationDetailReturnFlag){
                throw new GlobalException(e.getMessage());
            }else{
                throw new GlobalException("登录异常,请检查登录信息！");
            }
        }

        //todo:根据用户名和密码查询team_create对于的机构状态 3黑名单限制登录 和限制子账号登录
        //判断是否是主账号登录 默认情况下为黑名单
        Integer cooperation = CooperationEnum.COOPERATION_IN.getCode();
        if (username.contains("@")) {
            //根据用户名查询机构状态
            cooperation = teamService.getTeamEmployeesCooperation(username);
        }else {
            cooperation = teamService.getTeamCreateCooperation(username);
        }
        if (cooperation.equals( CooperationEnum.COOPERATION_BLACKLIST.getCode())) {
            throw new GlobalException("登录异常,该用户已被限制登录！");
        }
//        登录成功删除redis中登录限制缓存
        redisService.deleteObject(UserConstants.SAAS_VERIFY_LOGIN_ERROR_TIME + username);
        redisService.deleteObject(UserConstants.SAAS_VERIFY_LOGIN_ERROR_NUMBER + username);
        Integer type = (Integer) loginUser.getAccountInfo().get(UserConstants.ACCOUNT_TYPE);
        Integer teamId = (Integer) loginUser.getAccountInfo().get(UserConstants.TEAM_ID);
        recordLogininforS(loginUser.getUserid(), username, Constants.LOGIN_SUCCESS, "登录成功", type, teamId.longValue(), internalIp, browserFingerprint);
        return loginUser;
    }

    /**
     * 根据登录用户查询修改密码时间（判断是否已90天未修改密码）
     *
     * @return
     */
    public Map<String, Object> selectPasswordTime() {
        Map<String, Object> map = new HashMap<>();
        int type = TokenInformation.getType();
//        账号类型，账号类型，0-团队主账号，1-员工账号,2-资产端账号
        if (type == 0) {
            Create idCreate = teamService.findIdCreate(TokenInformation.getUserid());
            if (ObjectUtils.isEmpty(idCreate)) {
                throw new GlobalException("团队主账号信息查询错误");
            }
            if (ObjectUtils.isEmpty(idCreate.getUpdatePasswordTime())) {
                Create create = new Create();
                create.setId(idCreate.getId());
                create.setUpdatePasswordTime(new Date());
                teamService.updateCreatePassword(create);
                map.put("state", 2); //1-提示，2-不提示
                map.put("content", "");
            } else {
                //常用偏移，结果：2017-03-04 22:33:23
                Date date = new Date();
                Date updatePasswordTime = idCreate.getUpdatePasswordTime();
                long betweenDay = DateUtil.between(updatePasswordTime, date, DateUnit.DAY);
//                如果修改密码时间距离当前时间多于90天则提醒修改密码
                if (betweenDay > 90) {
                    map.put("state", 1); //1-展示，2-不展示
                    map.put("content", "您的密码长期未修改，为了信息安全请前往修改密码！");
                } else {
                    map.put("state", 2); //1-展示，2-不展示
                    map.put("content", "");
                }
            }
        } else if (type == 1) {
            Employees employees = settingsService.selectLoginDate(TokenInformation.getUserid().longValue());
            if (ObjectUtils.isEmpty(employees)) {
                throw new GlobalException("员工信息查询错误");
            }
            if (ObjectUtils.isEmpty(employees.getUpdatePasswordTime())) {
                Employees employees1 = new Employees();
                employees1.setId(employees.getId());
                employees1.setUpdatePasswordTime(new Date());
                settingsService.updatePasswordTime(employees1);
                map.put("state", 2); //1-展示，2-不展示
                map.put("content", "");
            } else {
                //常用偏移，结果：2017-03-04 22:33:23
                Date date = new Date();
                Date updatePasswordTime = employees.getUpdatePasswordTime();
                long betweenDay = DateUtil.between(updatePasswordTime, date, DateUnit.DAY);
//                如果修改密码时间距离当前时间多于90天则提醒修改密码
                if (betweenDay > 90) {
                    map.put("state", 1); //1-展示，2-不展示
                    map.put("content", "您已90天未修改密码，请修改新的密码");
                } else {
                    map.put("state", 2); //1-展示，2-不展示
                    map.put("content", "");
                }
            }
        } else {
            throw new GlobalException("登录账号类型错误");
        }
        return map;
    }

    /**
     * 退出
     *
     * @param loginName
     */
    public void logout(String loginName) {
        recordLogininfor(null, loginName, Constants.LOGOUT, "退出成功", null, null);
    }

    /**
     * 记录登录信息
     *
     * @param userId   用户id
     * @param username 用户名
     * @param status   状态
     * @param message  消息内容
     * @param teamId   团队id
     * @return
     */
    public void recordLogininfor(Long userId, String username, String status, String message, Integer type, Long teamId) {
        String sta = "1";
        // 日志状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            sta = "0";
        }

        TerminalTypeEnum terminalType = TerminalTypeEnum.WEB;
        String ua = ServletUtils.getRequest().getHeader("User-Agent");
        final UserAgent userAgent = UserAgentUtil.parse(ua);
        final String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        TeamLogininfor logininfor = new TeamLogininfor();
        logininfor.setLoginType(type);
        logininfor.setAccessTime(new Date());
        logininfor.setUserId(userId);
        logininfor.setUserName(username);
        logininfor.setStatus(sta);
        logininfor.setMsg(message);
        logininfor.setIpaddr(ip);
        logininfor.setTerminalType(terminalType.getInfo());
        logininfor.setTeamId(teamId);
        String terminalVersion = userAgent.getBrowser().toString();
        if (StringUtils.isNotEmpty(userAgent.getVersion())) {
            terminalVersion = terminalVersion + " " + userAgent.getVersion();
        }
        logininfor.setTerminalVersion(terminalVersion);
        logininfor.setOs(userAgent.getOs().toString());
        try {
            // 获取计算机名
            String name = InetAddress.getLocalHost().getHostName();
            System.err.println("计算机名称：" + name);
            logininfor.setDevicename(name);
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        disposeAsyncLogService.saveTeamLog(logininfor);
    }

    /**
     * 记录登录信息
     *
     * @param userId             用户id
     * @param username           用户名
     * @param status             状态
     * @param message            消息内容
     * @param teamId             团队id
     * @param internalIp         内网IP
     * @param browserFingerprint 浏览器指纹
     * @return
     */
    public void recordLogininforS(Long userId, String username, String status, String message, Integer type, Long teamId, String internalIp, String browserFingerprint) {
        String sta = "1";
        // 日志状态
        if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            sta = "0";
        }

        TerminalTypeEnum terminalType = TerminalTypeEnum.WEB;
        String ua = ServletUtils.getRequest().getHeader("User-Agent");
        final UserAgent userAgent = UserAgentUtil.parse(ua);
        final String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        TeamLogininfor logininfor = new TeamLogininfor();
        logininfor.setLoginType(type);
        logininfor.setAccessTime(new Date());
        logininfor.setUserId(userId);
        logininfor.setUserName(username);
        logininfor.setStatus(sta);
        logininfor.setMsg(message);
        logininfor.setIpaddr(ip);
        logininfor.setTerminalType(terminalType.getInfo());
        logininfor.setTeamId(teamId);
        logininfor.setInternalIp(internalIp);
        logininfor.setBrowserFingerprint(browserFingerprint + "M" + userId + "T" + DateUtils.dateTimeNow());
        String terminalVersion = userAgent.getBrowser().toString();
        if (StringUtils.isNotEmpty(userAgent.getVersion())) {
            terminalVersion = terminalVersion + " " + userAgent.getVersion();
        }
        logininfor.setTerminalVersion(terminalVersion);
        logininfor.setOs(userAgent.getOs().toString());
        try {
            // 获取计算机名
            String name = InetAddress.getLocalHost().getHostName();
            System.err.println("计算机名称：" + name);
            logininfor.setDevicename(name);
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        disposeAsyncLogService.saveTeamLog(logininfor);
    }

    /**
     * 下载外访单数据查询
     *
     * @param caseId
     * @return
     */
    public VisitingEntityPojo exportPDF(int caseId, int id) {
        VisitingEntityPojo visitingEntityPojo = new VisitingEntityPojo();
        CaseManage caseManage1 = collectionService.selectCaseManageId(new Long((long) caseId));
        if (!ObjectUtils.isEmpty(caseManage1)) {
            visitingEntityPojo.setContractNo(caseManage1.getContractNo());
            visitingEntityPojo.setEntrustingPartyName(caseManage1.getEntrustingPartyName());
            visitingEntityPojo.setProductName(caseManage1.getProductName());
            visitingEntityPojo.setClientName(FieldEncryptUtil.decrypt(caseManage1.getClientName()));
            if (!ObjectUtils.isEmpty(caseManage1.getClientIdcard())) {
                visitingEntityPojo.setClientIdcard(DataMaskingUtils.idMasking(FieldEncryptUtil.decrypt(caseManage1.getClientIdcard())));
            } else {
                visitingEntityPojo.setClientIdcard(caseManage1.getClientIdcard());
            }
            visitingEntityPojo.setClientMoney(caseManage1.getClientMoney());
        }
        InfoLoan infoLoan = collectionService.selectInfoLoan(new Long((long) caseId));  //根据案件id查询案件贷款信息
        if (!ObjectUtils.isEmpty(infoLoan)) {
            visitingEntityPojo.setAmountCalledBack(infoLoan.getAmountCalledBack());
            visitingEntityPojo.setRemainingDue(infoLoan.getRemainingDue());
        }
        InfoBase infoBase = collectionService.selectInfoBase(new Long((long) caseId));  //根据案件id查询案件详情
        if (!ObjectUtils.isEmpty(infoBase)) {
            visitingEntityPojo.setResidentialAddress(FieldEncryptUtil.decrypt(infoBase.getResidentialAddress()));
            visitingEntityPojo.setHomeAddress(FieldEncryptUtil.decrypt(infoBase.getHomeAddress()));
            visitingEntityPojo.setClientCensusRegister(FieldEncryptUtil.decrypt(infoBase.getClientCensusRegister()));
            visitingEntityPojo.setWorkingAddress(FieldEncryptUtil.decrypt(infoBase.getWorkingAddress()));
            visitingEntityPojo.setPlaceOfWork(FieldEncryptUtil.decrypt(infoBase.getPlaceOfWork()));
            visitingEntityPojo.setClientSex(infoBase.getClientSex());
        }
        Criteria criteria = new Criteria();
        criteria.setCaseId(new Long((long) caseId));
        List<InfoContact> infoContact = collectionService.selectInfoContact(criteria);  //根据案件id查询案件联系人信息
        if (!ObjectUtils.isEmpty(infoContact)) {
            for (InfoContact infoContact1 : infoContact) {
                if (ObjectUtils.isEmpty(infoContact1.getContactPhone())) {
                    continue;
                } else {
                    infoContact1.setContactPhone(DataMaskingUtils.phoneMasking(infoContact1.getContactPhone()));
                }
            }
            visitingEntityPojo.setInfoContact(infoContact);
        }
        List<UrgeRecord> urgeRecords = agCollectionService.reminderAuthoritys(new Long((long) caseId));  //根据案件id查询催收记录
        if (!ObjectUtils.isEmpty(urgeRecords)) {
            for (UrgeRecord urgeRecord : urgeRecords) {
                if (ObjectUtils.isEmpty(urgeRecord.getContactMode())) {
                    continue;
                } else {
                    urgeRecord.setContactMode(DataMaskingUtils.phoneMasking(urgeRecord.getContactMode()));
                }
            }
            visitingEntityPojo.setUrgeRecords(urgeRecords);
        }
        OutsideRecord outsideRecords = recordService.selectOutsideRecordIdBy(new Long((long) caseId), new Long((long) id));  //根据案件id以及申请id查询外访记录
        if (!ObjectUtils.isEmpty(outsideRecords)) {
            String formatDate = DateUtil.formatDate(outsideRecords.getOutsideStart());
            System.out.println("formatDate:" + formatDate);
            visitingEntityPojo.setOutsideStart(formatDate);
            visitingEntityPojo.setOdvNames(outsideRecords.getOdvName());
            visitingEntityPojo.setOdvName(outsideRecords.getOdvNames());
        }
        return visitingEntityPojo;
    }

    /**
     * 判断是否是今天第一次登录
     *
     * @return
     */
    public Map<String, Object> complianceAdvocacy() {
        Map<String, Object> map = new HashMap<>();
        int type = TokenInformation.getType();
        if (!ObjectUtils.isEmpty(type)) {
            String compliancePersuade = null;
            if (type == 0) {   //账号类型，账号类型，0-团队主账号，1-员工账号
//                根据团队id获取团队信息
                Create idCreate = teamService.findIdCreate(TokenInformation.getUserid());
                if (ObjectUtils.isEmpty(idCreate)) {
                    throw new GlobalException("查询团队信息失败");
                } else {

                    if (ObjectUtils.isEmpty(idCreate.getLoginDate())) {
                        compliancePersuade = redisMessageService.getCompliancePersuade();
                    } else {
//                        long between = DateUtil.between(new Date(), idCreate.getLoginDate(), DateUnit.DAY);
//
//                        if (between == 0) {
//                            compliancePersuade = null;
//                        } else {
//                            compliancePersuade = redisMessageService.getCompliancePersuade();
//                        }
                        Date beginDay = DateUtil.beginOfDay(new Date());  //当前时间的每天开始时间
                        long time = beginDay.getTime();
                        long time1 = idCreate.getLoginDate().getTime();  //账号最后登录时间
                        if (time > time1) {
                            compliancePersuade = redisMessageService.getCompliancePersuade();
                        } else {
                            compliancePersuade = null;
                        }
                    }
//                    Create create = new Create();
//                    create.setId(idCreate.getId());
//                    create.setLoginDate(new Date());
//                    teamService.updateCreateDate(create);
                }
            } else {
                Employees employees1 = settingsService.selectLoginDate(new Long(TokenInformation.getUserid()));
                if (ObjectUtils.isEmpty(employees1)) {
                    throw new GlobalException("查询用户信息失败");
                } else {
                    if (ObjectUtils.isEmpty(employees1.getLoginDate())) {
                        compliancePersuade = redisMessageService.getCompliancePersuade();
                    } else {
//                        long between = DateUtil.between(employees1.getLoginDate(), new Date(), DateUnit.DAY);
                        Date beginDay = DateUtil.beginOfDay(new Date());  //当前时间的每天开始时间
                        long time = beginDay.getTime();
                        System.err.println("账号开始登录时间（long）:" + time);
                        System.err.println("账号最后登录时间:" + employees1.getLoginDate().toString());
                        long time1 = employees1.getLoginDate().getTime();  //账号最后登录时间
                        System.err.println("账号最后登录时间（long）:" + time1);

                        if (time > time1) {
                            compliancePersuade = redisMessageService.getCompliancePersuade();
                            System.err.println("合规宣导:" + compliancePersuade);
                        } else {
                            compliancePersuade = null;
                            System.err.println("为空");
                        }
//                        System.err.println(between);
//                        if (between == 0) {
//                            compliancePersuade = null;
//                        } else {
//                            compliancePersuade = redisMessageService.getCompliancePersuade();
//                        }
                    }
//                    Employees employees2 = new Employees();  //修改最后登录时间
//                    employees2.setId(employees1.getId());
//                    employees2.setLoginDate(new Date());
//                    settingsService.updateEmployeesDate(employees2);
                }
            }
            map.put("propaganda", compliancePersuade);
        }
        return map;
    }

    /**
     * 修改最后一次登录时间
     */
    public void lastLoginTime() {
        int type = TokenInformation.getType();
        if (!ObjectUtils.isEmpty(type)) {
            if (type == 0) {   //账号类型，账号类型，0-团队主账号，1-员工账号
//                根据团队id获取团队信息
                Create idCreate = teamService.findIdCreate(TokenInformation.getUserid());
                if (ObjectUtils.isEmpty(idCreate)) {
                    throw new GlobalException("查询团队信息失败");
                } else {
                    Create create = new Create();
                    create.setId(idCreate.getId());
                    create.setLoginDate(new Date());
                    teamService.updateCreateDate(create);
                }
            } else {
                Employees employees1 = settingsService.selectLoginDate(new Long(TokenInformation.getUserid()));
                if (ObjectUtils.isEmpty(employees1)) {
                    throw new GlobalException("查询用户信息失败");
                } else {
                    Employees employees2 = new Employees();  //修改最后登录时间
                    employees2.setId(employees1.getId());
                    employees2.setLoginDate(new Date());
                    settingsService.updateEmployeesDate(employees2);
                }
            }
        }
    }

    /**
     * 获取菜单数据权限
     *
     * @param userId 用户Id
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(Long userId) {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        /*if (TokenInformation.getType() == 0) {
           //perms.add("*:*:*");
            perms=SecurityUtils.getLoginUser().getPermissions();
        } else {
            perms.addAll(menuService.selectMenuPermsByUserId(userId));
        }*/
        perms = SecurityUtils.getLoginUser().getPermissions();
        Set<String> teamExport = teamService.selectCloseTeamExport(TokenInformation.getCreateid());
        perms.removeAll(teamExport);
        return perms;
    }


}
