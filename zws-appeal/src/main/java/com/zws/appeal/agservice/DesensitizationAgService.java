package com.zws.appeal.agservice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.zws.appeal.enums.InfoSecurityEnum;
import com.zws.appeal.mapper.TeamMapper;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.domain.Desensitization;
import com.zws.appeal.domain.State;
import com.zws.appeal.pojo.StateDesensitization;
import com.zws.appeal.pojo.staticConst.StaticConstantClass;
import com.zws.appeal.utils.DesensitizationRedis;
import com.zws.appeal.utils.TokenInformation;
import com.zws.system.api.domain.Legal;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据脱敏AgService
 *
 * <AUTHOR>
 * @date 2024/1/31 16:52
 */
@Component
public class DesensitizationAgService {

    @Autowired
    private DesensitizationRedis desensitizationRedis;
    @Autowired
    private RedisService redisService;
    @Resource
    private TeamMapper teamMapper;

    /**
     * 获取信息脱敏类对象
     * 返回 null 表示未开启脱敏
     *
     * @return
     */
    public Desensitization getDesensitization() {
        return getDesensitization(SecurityUtils.getLoginUser());
    }

    /**
     * 获取信息脱敏类对象
     * 返回 null 表示未开启脱敏
     *
     * @return
     */
    public Desensitization getDesensitization(LoginUser loginUser) {
        StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid(loginUser));
        State state = stateDesensitization.getState();
        Desensitization desensitization = null;
        if (state.getInformationStatus() == 1) {
            desensitization = stateDesensitization.getDesensitization();
        }
        return desensitization;
    }


    /**
     * 获取姓名是否开启脱敏
     *
     * @return true-开启，false-关闭
     */
    public boolean checkNameDim() {
        Desensitization desensitization = getDesensitization();
        if (desensitization == null) {
            return false;
        }
        if (desensitization.getDname() == 1) {
            return true;
        }
        return false;
    }


    /**
     * 获取电话号码是否开启脱敏
     *
     * @return true-开启，false-关闭
     */
    public boolean checkNumberDim() {
        StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
        State state = stateDesensitization.getState();
        if (state.getInformationStatus() == 0) {
            return false;
        }
        Desensitization desensitization = stateDesensitization.getDesensitization();
        if (desensitization.getNumbers() == 1) {
            return true;
        }
        return false;
    }

    /**
     * 获取身份证是否开启脱敏
     *
     * @return true-开启，false-关闭
     */
    public boolean checkCardIdDim() {
        StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
        State state = stateDesensitization.getState();
        if (state.getInformationStatus() == 0) {
            return false;
        }
        Desensitization desensitization = stateDesensitization.getDesensitization();
        if (desensitization.getCardId() == 1) {
            return true;
        }
        return false;
    }


    /**
     * 查询信息是否脱敏
     * @param infoSecurity  信息 null-查询总开关是否开启
     * @return true-脱敏，false-不脱敏
     */
    public boolean selectInfoSecurity(InfoSecurityEnum infoSecurity){
        Legal legal=new Legal();
        legal.setLegalType(0);
        legal.setLegalKey("main_switch");//主开关
        List<Legal> legals = teamMapper.selectLegalList(legal);
        if (infoSecurity == null) {
            if (!ObjectUtils.isEmpty(legals)) {
                //System.out.println("查询脱敏状态0耗时："+timer.intervalMs("1"));
                return legals.get(0).getState() == 0;
            }
        }else{
            if (!ObjectUtils.isEmpty(legals)&&legals.get(0).getState() == 0) {
                Legal legal1=new Legal();
                legal1.setLegalType(0);
                legal1.setLegalKey(infoSecurity.getKey());//主开关
                List<Legal> legals1 = teamMapper.selectLegalList(legal1);
                if (!ObjectUtils.isEmpty(legals1)) {
                    //System.out.println("查询脱敏状态0耗时："+timer.intervalMs("1"));
                    return legals1.get(0).getState() == 0;
                }
            }
        }
        return false;
    }

}
