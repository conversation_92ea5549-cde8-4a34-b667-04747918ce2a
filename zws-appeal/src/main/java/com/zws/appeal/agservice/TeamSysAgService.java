package com.zws.appeal.agservice;

import com.alibaba.fastjson2.JSON;
import com.zws.appeal.domain.ApproveProce;
import com.zws.appeal.service.MyApprovalService;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.exception.ApprovalAuthException;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.domain.Employees;
import com.zws.appeal.pojo.DataPermis;
import com.zws.appeal.service.ITeamDeptService;
import com.zws.appeal.service.SettingsService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 团队-用户、角色、部门 相关AgService
 *
 * <AUTHOR>
 * @date 2024/1/29 16:19
 */
@Component
public class TeamSysAgService {

    private static final Logger log = LoggerFactory.getLogger(TeamSysAgService.class);
    @Autowired
    private MyApprovalService myApprovalService;
    @Autowired
    private ITeamDeptService teamDeptService;
    @Autowired
    private SettingsService settingsService;

    /**
     * 获取数据权限
     *
     * @return
     */
    public DataPermis getDataPermis() {
        DataPermis permis = new DataPermis();
        if (TokenInformation.getType() == UserConstants.ACCOUNT_TYPE_0) {
            //List<Integer> deptIds = teamDeptService.selectProgenyDept(0);
            //团队主账号不限制数据
            return new DataPermis();
        }
        if (TokenInformation.isDepartmentHead()) {
            //部门负责人-可以查看本部门 及以下 部门 以及本人的信息
            //当前用户的部门
            Integer deptId = TokenInformation.getDeptId();
            if (deptId != null) {
                List<Integer> deptIds = teamDeptService.selectProgenyDept(deptId);
                deptIds.add(deptId);
                permis.setDeptIds(deptIds);
            }
        } else {
            //非部门负责人，只能查看本人信息
            List<Integer> employs = new ArrayList<>();
            employs.add(TokenInformation.getUserid());
            permis.setEmployeesIds(employs);
        }
        return permis;
    }

    /**
     * 查询创建人ID 集合
     * 团队主账号： 查看所有员工+自己
     *
     * @return
     */
    public List<Integer> getEmployeesIds() {
        Integer accountType = SecurityUtils.getAccountType();
        List<Integer> ids = new ArrayList<>();
        if (UserConstants.ACCOUNT_TYPE_0 == accountType) {
            //不包含的的团队主账号   查询主账号的所有子账号
            Employees employees = new Employees();
            employees.setCreateId(SecurityUtils.getUserId().intValue());
            List<Employees> employees1 = settingsService.selectDeptFuzzy(employees);
            if (employees1.size() > 0) {
                ids = employees1.stream().map(Employees::getId).collect(Collectors.toList());
                ids.add(SecurityUtils.getUserId().intValue());
            }
        }
        return ids;
    }

    /**
     * 查询创建人ID 集合
     * 团队主账号： 查看所有员工+自己
     * 其他账号 ：查看自己
     *
     * @return
     */
    public List<Integer> getCreateIds() {
        Integer accountType = SecurityUtils.getAccountType();
        List<Integer> ids = new ArrayList<>();
        if (UserConstants.ACCOUNT_TYPE_0 == accountType) {
            //不包含的的团队主账号   查询主账号的所有子账号
            Employees employees = new Employees();
            employees.setCreateId(SecurityUtils.getUserId().intValue());
            List<Employees> employees1 = settingsService.selectDeptFuzzy(employees);
            if (employees1.size() > 0) {
                ids = employees1.stream().map(Employees::getId).collect(Collectors.toList());
            }
        }
        ids.add(SecurityUtils.getUserId().intValue());
        return ids;
    }

    /**
     * 判断当前用户是否有审批权限(主账号或者部门负责人)
     *
     * @param loginUser 当前登录信息
     * @return
     */
    public boolean checkApprovalAuthority(LoginUser loginUser) {
        if (TokenInformation.getType(loginUser) == UserConstants.ACCOUNT_TYPE_0) {
            return true;
        }
        if (TokenInformation.isDepartmentHead(loginUser)) {
            return true;
        }
        return false;
    }

    /**
     * 获取当前用户的审批流程顺序
     * --所有流程都是统一的按照部门负责人、团队主账号才有审批权限、并且只有一级审批
     *
     * @param loginUser 当前登录用户信息
     * @return 审批流程顺序
     */
    public Integer getApprovalSort(LoginUser loginUser) throws ApprovalAuthException {
        boolean approvalAuthority = checkApprovalAuthority(loginUser);
        if (!approvalAuthority) {
            throw new ApprovalAuthException();
        }
        int sort = 1;
        return sort;
    }
    /**
     * 筛选重复项
     *
     * @param ids 审批ids
     * @param sort 审核顺序
     * @param approveCode 批准代码
     * @throws ApprovalAuthException 审批权限异常
     */
    public void filterDuplicates(List<Long> ids,Integer sort,Integer approveCode) throws ApprovalAuthException {
        List<ApproveProce> approveProces = myApprovalService.selectApplyRecordByIdsAndSort(ids, sort, approveCode);
        List<Long> collect = approveProces.stream().map(ApproveProce::getApplyId).collect(Collectors.toList());
        log.info("{}筛选重复项审批{}", approveCode, JSON.toJSONString(collect));
        ids.removeAll(collect);
        if (ObjectUtils.isEmpty(ids)) {
            throw new GlobalException("该节点已经审批,请刷新页面");
        }
    }
}
