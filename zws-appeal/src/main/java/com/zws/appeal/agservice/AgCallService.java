package com.zws.appeal.agservice;

import cn.hutool.core.util.StrUtil;
import com.zws.common.core.callcenter.pojo.CalloutParam;
import com.zws.common.core.callcenter.pojo.HangupParam;
import com.zws.common.core.callcenter.pojo.SipPwdParam;
import com.zws.common.core.callcenter.service.YueCaiCallCenter;
import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.enums.call.BindStatusEnum;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.htcall.HtCallAesECB;
import com.zws.appeal.config.CallConfig;
import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.domain.Employees;
import com.zws.appeal.domain.InfoContact;
import com.zws.appeal.domain.call.CallOutRecord;
import com.zws.appeal.domain.call.CallSip;
import com.zws.appeal.mapper.AgCallMapper;
import com.zws.appeal.pojo.call.AssignSipForm;
import com.zws.appeal.pojo.call.CallOutForm;
import com.zws.appeal.pojo.call.UpdateSipPwdForm;
import com.zws.appeal.pojo.teamApplication.CreateWorkOrderUtils;
import com.zws.appeal.service.CollectionService;
import com.zws.appeal.service.SettingsService;
import com.zws.appeal.service.TeamCaseService;
import com.zws.appeal.service.call.ICallOutRecordService;
import com.zws.appeal.service.call.ICallSipService;
import com.zws.appeal.utils.DataMaskingUtils;
import com.zws.appeal.utils.DecryptUtils;
import com.zws.appeal.utils.TokenInformation;
import com.zws.system.api.RemoteMessageService;
import com.zws.system.api.pojo.CallMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 呼叫相关服务
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/25 10:24
 */
@Slf4j
@Component
public class AgCallService {

    @Autowired
    private ICallSipService callSipService;
    @Autowired
    private SettingsService settingsService;
    @Autowired
    private YueCaiCallCenter yueCaiCallCenter;
    @Autowired
    private CollectionService collectionService;
    @Autowired
    private ICallOutRecordService callOutRecordService;
    @Autowired
    private RemoteMessageService remoteMessageService;
    @Autowired
    private TeamCaseService teamCaseService;
    @Autowired
    private CallConfig callConfig;
    @Autowired
    private DesensitizationAgService desensitizationAgService;
    @Autowired
    private AgCallMapper agCallMapper;

    /**
     * 分配SIP 坐席
     *
     * @param form
     */
    public void assignSip(AssignSipForm form) {
        if (form.getSipNumbers().size() > form.getEmployeeIds().size()) {
            throw new ServiceException("选择的坐席账号不能大于员工账号数量");
        }
        Integer teamId = TokenInformation.getCreateid();
        List<String> sipNumbers = form.getSipNumbers();//  0
        List<Integer> employeeIds = form.getEmployeeIds();// 1

        for (int i = 0; i < employeeIds.size(); i++) {
            Integer employeeId = employeeIds.get(i);//员工
            if (i > sipNumbers.size() - 1) {
                //取消员工的已分配坐席
                CallSip cancelSip = new CallSip();
                cancelSip.setOdvId(employeeId.longValue());
                cancelSip.setBindStatus(BindStatusEnum.UNBOUND.getCode());
                cancelSip.setUpdateTime(DateUtils.getNowDate());
                cancelSip.setUpdateBy(TokenInformation.getUsername());
                callSipService.cancelSipByOdvId(cancelSip);

                //更新员工列表
                Employees employees = new Employees();
                employees.setId(employeeId);
                employees.setSipNumber("");
                employees.setSipPassword("");
                settingsService.updateEmployeesSip(employees);
                continue;
            }
            String sipNumber = sipNumbers.get(i);//坐席账号

            //取消员工的已分配坐席
            CallSip cancelSip = new CallSip();
            cancelSip.setOdvId(employeeId.longValue());
            cancelSip.setBindStatus(BindStatusEnum.UNBOUND.getCode());
            cancelSip.setUpdateTime(DateUtils.getNowDate());
            cancelSip.setUpdateBy(TokenInformation.getUsername());
            callSipService.cancelSipByOdvId(cancelSip);
            //分配
            CallSip sip = callSipService.getBySipNumber(sipNumber);
            if (sip == null) {
                continue;
            }
            //更新SIP
            sip.setOdvId(employeeId.longValue());
            sip.setBindStatus(BindStatusEnum.BIND.getCode());
            sip.setBindTime(DateUtils.getNowDate());
            sip.setTeamId(teamId.longValue());
            callSipService.updateCallSip(sip);

            //更新员工列表
            Employees employees = new Employees();
            employees.setId(employeeId);
            employees.setSipNumber(sipNumber);
            employees.setSipPassword(sip.getSipPassword());
            settingsService.updateEmployeesSip(employees);
        }


    }

    /**
     * 重置SIP坐席密码
     *
     * @param form
     */
    public void updateSipPwd(UpdateSipPwdForm form) {
        List<Integer> employeeIds = form.getEmployeeIds();
        String sipNewPassword = form.getSipPassword();
        for (Integer employeeId : employeeIds) {
            Employees employee = settingsService.selectEmployeesId(employeeId);

            String sipPassword = employee.getSipPassword();
            String sipNumber = employee.getSipNumber();
            if (StringUtils.isEmpty(sipNumber)) {
                //没有分配SIP坐席号
                continue;
            }
            SipPwdParam param = new SipPwdParam();
            param.setSip(sipNumber);
            param.setOldPwd(sipPassword);
            param.setNewPwd(sipNewPassword);
            param.setServiceHostUrl(callConfig.getHostUrl());
            try {
                yueCaiCallCenter.setSipPassword(param,callConfig.getApiFlag());

                CallSip sip = new CallSip();
                sip.setSipPassword(sipNewPassword);
                sip.setSipNumber(sipNumber);
                callSipService.updateCallSip(sip);

                //更新员工列表
                /*Employees employees=new Employees();
                employees.setId(employeeId);
                employees.setSipPassword(sipNewPassword);
                settingsService.updateEmployeesSip(employees);*/
            } catch (Exception e) {
                //TODO 修改失败
                throw new ServiceException(employee.getEmployeeName() + "," + e.getMessage());
            }
        }
    }

    /**
     * 获取当前登录账号的SIP坐席号
     * 如果是未分配的则返回null
     *
     * @return
     */
    private String getUserSipNumber() {
        Integer employeeId = TokenInformation.getUserid();
        Employees employee = settingsService.selectEmployeesId(employeeId);
        if (employee == null) {
            return null;
        }
        //String sipPassword=   employee.getSipPassword();
        String sipNumber = employee.getSipNumber();
        if (StringUtils.isEmpty(sipNumber)) {
            return null;
        }
        return sipNumber;
    }


    /**
     * 给联系人拨打电话
     *
     * @param form
     */
    public CalloutParam callOutByContact(CallOutForm form) {
        if (form.getContactId() == null) {
            throw new ServiceException("联系人ID 不能为空");
        }
        Long contactId = form.getContactId();//联系人ID
        InfoContact infoContact = collectionService.selectInfoContactById(contactId);
        if (infoContact == null) {
            throw new ServiceException("联系人信息查询失败");
        }
        DecryptUtils.dataDecrypt(infoContact);
        String phone = infoContact.getContactPhone();
        Long caseId = infoContact.getCaseId();
        CalloutParam calloutParam = callOut(caseId, contactId, phone, "联系人ID:" + contactId);
        return calloutParam;
    }

    /**
     * 给案件 本人打电话
     *
     * @param form
     */
    public CalloutParam callOutByCase(CallOutForm form) {
        if (form.getCaseId() == null) {
            throw new ServiceException("案件ID不能为空");
        }
        Long caseId = form.getCaseId();
        CaseManage caseManage = collectionService.selectCaseManageId(caseId);
        if (caseManage == null) {
            throw new ServiceException("案件信息查询失败");
        }
        DecryptUtils.dataDecrypt(caseManage);
        String phone = caseManage.getClientPhone();
        CalloutParam calloutParam = callOut(caseId, null, phone, "案件ID:" + caseId);
        return calloutParam;
    }

    /**
     * 给指定 电话号码 打电话
     *
     * @param form
     */
    public CalloutParam callOutByPhone(CallOutForm form) {
        if (form.getPhoneNumber() == null) {
            throw new ServiceException("电话号码不能为空");
        }
        String phone = form.getPhoneNumber();
        return getCalloutParam(phone);
    }

    /**
     * 给手动拨打功能加案件Id方法
     *
     * @param phone
     */
    private CalloutParam getCalloutParam(String phone) {
        String phoneEncrypt = FieldEncryptUtil.encrypt(phone);
        Integer teamId = TokenInformation.getCreateid();
        List<Long> caseIds = agCallMapper.selectCaseIdByPhone(phoneEncrypt, teamId);
        Long caseId = null;
        if (caseIds != null && caseIds.size() == 1) {
            //刚好匹配到1个案件
            caseId = caseIds.get(0);
        }
        CalloutParam calloutParam = callOut(caseId, null, phone, "手动电话:" + phone);
        return calloutParam;
    }

    /**
     * 给工单来电话号码 打电话
     *
     * @param form
     */
    public CalloutParam callOutByWorkOrder(CallOutForm form) {
        if (form.getWorkOrderId() == null) {
            throw new ServiceException("工单ID不能为空");
        }
        CreateWorkOrderUtils workOrder = teamCaseService.getWorkOrderById(form.getWorkOrderId());
        if (workOrder == null) {
            throw new ServiceException("工单信息查询为空");
        }
        DecryptUtils.dataDecrypt(workOrder); //解密
        String phone = workOrder.getCallNumber();
        Long caseId = workOrder.getCaseId();
        CalloutParam calloutParam = callOut(caseId, null, phone, "工单ID:" + form.getWorkOrderId());
        return calloutParam;
    }

    /**
     * 呼叫挂断
     */
    public void callOutHangup() {
        String sipNumber = getUserSipNumber();
        if (StringUtils.isEmpty(sipNumber)) {
            throw new ServiceException("当前账号未分配坐席");
        }
        HangupParam param = new HangupParam();
        param.setSipNumber(sipNumber);
        param.setKey("123456");
        param.setServiceHostUrl(callConfig.getHostUrl());
        yueCaiCallCenter.hangup(param);

        CallMessage callMessage = new CallMessage();
        callMessage.setUserId(TokenInformation.getUserid().longValue());
        callMessage.setIdentification(TokenInformation.getType());
        callMessage.setMsg("电话挂机");
        remoteMessageService.pushCallEnd(callMessage, SecurityConstants.INNER);
    }


    /**
     * 点击外呼
     *
     * @param phone     客户号码(外呼号码) 不能为空
     * @param contactId 联系人ID
     * @param caseId    案件id
     * @param remarks   备注
     */
    public CalloutParam callOut(Long caseId, Long contactId, String phone, String remarks) {
        CallMessage callMessage = new CallMessage();
        callMessage.setUserId(TokenInformation.getUserid().longValue());
        callMessage.setIdentification(TokenInformation.getType());
        try {

            callMessage.setMsg("呼叫中...");

            //remoteMessageService.pushOnCall(callMessage, SecurityConstants.INNER);
            //log.info("消息中心推送【呼叫中...】结束");

            String sipNumber = getUserSipNumber();
//            if(StringUtils.isEmpty(sipNumber))  throw new ServiceException("当前账号未分配坐席");
            if (StringUtils.isEmpty(phone)) {
                throw new ServiceException("外呼号码不能为空");
            }
            Integer teamId = TokenInformation.getCreateid();
            Integer odvId = TokenInformation.getUserid();
            CalloutParam param = new CalloutParam();
            //保存呼叫记录
            CallOutRecord outRecord = new CallOutRecord();
            outRecord.setCallOutTo(phone);
            outRecord.setContactId(contactId);
            outRecord.setCaseId(caseId);
            outRecord.setServiceHost(callConfig.getHostUrl());
            outRecord.setTeamId(TokenInformation.getCreateid().longValue());
            outRecord.setOdvId(TokenInformation.getUserid().longValue());
            outRecord.setRemarks(remarks);
            long outRecordId = callOutRecordService.insert(outRecord);


            param.setExt(sipNumber);

            //是否加密
            boolean hasencrypt = false;
            param.setHasencrypt(hasencrypt);
            if (hasencrypt) {
                String phoneEnc = HtCallAesECB.encrypt(phone);
                param.setPhone(phoneEnc);
            } else {
                param.setPhone(phone);
            }

            //判断是否脱敏
            if (desensitizationAgService.checkNumberDim()) {
                String callNumber = DataMaskingUtils.phoneMasking(phone);
                param.setShowPhone(callNumber);
            } else {
                param.setShowPhone(phone);
            }


            param.setApiCallid(StrUtil.toString(outRecordId));

            String business = StrUtil.format("{}_{}_{}_{}_{}_{}",
                    outRecordId, teamId, odvId, caseId, contactId, phone);
            param.setBusiness(business);
            // 账号类型:团队id:催员id   (任意类型没有值时用字符串的null代替)
            //String relationID= StrUtil.format("{}:{}:{}",TokenInformation.getType(),
            //       TokenInformation.getCreateid(),TokenInformation.getUserid());
            param.setCustomerId(StrUtil.toString(contactId));
            param.setUID(StrUtil.toString(caseId));
            param.setRelationID(business);
            param.setHideNumber("y");
            param.setKey("123456");
            //param.setServiceHostUrl(callConfig.getHostUrl());
            //param.setCallbackDomain(callConfig.getCallbackDomain()+"caseManage/call/callback");//TODO 回调地址
//            yueCaiCallCenter.callout(param);

            callMessage.setCallStartTime(DateUtils.getNowDate().getTime());
            callMessage.setMsg("通话中");
            //remoteMessageService.pushCallIng(callMessage, SecurityConstants.INNER);
            //log.info("消息中心推送【通话中】结束");


            return param;
        } catch (Exception e) {
            callMessage.setMsg("电话挂机");
            ///remoteMessageService.pushCallEnd(callMessage, SecurityConstants.INNER);
            log.info("消息中心推送【电话挂机】结束");
            throw new ServiceException(e.getMessage());
        }

    }


}
