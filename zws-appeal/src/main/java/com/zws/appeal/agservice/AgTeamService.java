package com.zws.appeal.agservice;

import cn.hutool.core.date.DateUtil;
import com.zws.common.core.callcenter.service.YueCaiCallCenter;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.config.CallConfig;
import com.zws.appeal.domain.*;
import com.zws.appeal.domain.record.*;
import com.zws.appeal.enums.TeamExportEnum;
import com.zws.appeal.pojo.*;
import com.zws.appeal.pojo.staticConst.StaticConstantClass;
import com.zws.appeal.service.*;
import com.zws.appeal.service.call.ICallSipService;
import com.zws.appeal.utils.DesensitizationRedis;
import com.zws.appeal.utils.TimeUtils;
import com.zws.appeal.utils.TokenInformation;
import com.zws.system.api.RemoteCaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@Transactional //开启事务注解
public class AgTeamService {

    @Autowired
    private TeamService teamService;
    @Autowired
    private SettingsService settingsService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private DesensitizationRedis desensitizationRedis;
    @Autowired
    private ApplicationService applicationService;
    @Autowired
    private YueCaiCallCenter caiCallCenter;
    @Autowired
    private RecordService recordService;
    @Autowired
    private CaseService caseService;
    @Autowired
    private ICallSipService iCallSipService;
    @Autowired
    private EvaluationFormService evaluationFormService;

    @Autowired
    private CallConfig callConfig;
    @Autowired
    private RemoteCaseService remoteCaseService;

    /**
     * 创建团队以及团队的标签
     *
     * @param createLabel
     */
    public void addCreateLabel(CreateLabel createLabel) {
        Create create = new Create();
        CreatePojo createUtils = createLabel.getCreateUtils();
        if (!ObjectUtils.isEmpty(createUtils.getMargin())) {
//            createUtils.getMargin().matches("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
            BigDecimal bigDecimal = new BigDecimal(createUtils.getMargin());
            if (bigDecimal.compareTo(BigDecimal.ZERO) == -1) {
                throw new GlobalException("保证金不可为负数");
            }
            create.setMargin(bigDecimal);
        }
        if (!ObjectUtils.isEmpty(createUtils.getCommissionRatio())) {
            if (createUtils.getCommissionRatio().compareTo(BigDecimal.ZERO) <= 0) {
                throw new GlobalException("佣金比不能为负数");
            }
            create.setCommissionRatio(createUtils.getCommissionRatio());
        }
        if (createUtils.getTeamType() == null) {
            throw new GlobalException("机构类型不能为空");
        }
        create.setTeamType(createUtils.getTeamType());
        create.setCname(createUtils.getCname());
        create.setCategory(createUtils.getCategory());
        create.setAccount(createUtils.getAccount());
        create.setNumbers(createUtils.getNumbers());
        create.setCooperation(createUtils.getCooperation());
        create.setStarts(createUtils.getStarts());
        create.setComplete(createUtils.getComplete());
        create.setContact(createUtils.getContact());
        create.setMachine(createUtils.getMachine());
        create.setEmail(createUtils.getEmail());
        create.setDescribes(createUtils.getDescribes());
//        取出团队的主键id
        int id = teamService.insertCreate(create);
        State state = createLabel.getState();
        if (ObjectUtils.isEmpty(state)) {
            state = new State();
        }
        if (state.getWhitelistStatus() == null) {
            state.setWhitelistStatus(0);
        }
        if (state.getInformationStatus() == null) {
            state.setInformationStatus(0);
        }
        if (state.getExportSettingStatus() == null) {
            state.setExportSettingStatus(0);
        }
        if (state.getPushAppStatus() == null) {
            state.setExportSettingStatus(0);
        }
        state.setCreateId(id);
        teamService.insertState(state);
//        添加团队标签
        List<Label> labels = createLabel.getLabels();
        for (Label label : labels) {
            label.setCreateId(id);
//            如果标签状态为null，则修改默认为0
            if (ObjectUtils.isEmpty(label.getStateLabel())) {
                label.setStateLabel(0);
            }
//            如果标签内容为null，则修改默认为“未标签”
            if (ObjectUtils.isEmpty(label.getLabelContent())) {
                label.setLabelContent("未标签");
            }
        }
        teamService.insertLabel(labels);
//        添加白名单信息
        if (state.getWhitelistStatus() == 1) {
            White white = createLabel.getWhite();
            if (ObjectUtils.isEmpty(white.getWhitelistName()) || ObjectUtils.isEmpty(white.getAddressIp())) {
                throw new GlobalException("白名单状态已开启，请设置白名单必填项");
            }
            white.setCreateId(id);
            white.setCreateName(create.getCname());
            teamService.insertWhite(white);
        }
//        添加脱敏开关状态
        if (state.getInformationStatus() == 1) {
            Desensitization desensitization = createLabel.getDesensitization();
            desensitization.setCreateId(id);
            teamService.insertDesensitization(desensitization);
        } else {
            Desensitization desensitization = new Desensitization();
            desensitization.setCreateId(id);
            teamService.insertDesensitizationDefault(desensitization);
        }
//        添加文件信息
        if (!ObjectUtils.isEmpty(createLabel.getFiles())) {
            List<Files> files = createLabel.getFiles();
            for (Files file : files) {
                file.setCreateId(id);
            }
            teamService.insertFile(files);
        }
//        添加水印字段默认值
        Watermark watermark = new Watermark();
        watermark.setCreateId(id);
        teamService.insertWatermark(watermark);
//        添加审批设置默认值
        teamService.insertApprovalSettings(id);
//        添加审批步骤默认值
        teamService.insertApprovalSteps(id, create.getCname());


        //添加导出开关
        /*if (!ObjectUtils.isEmpty(createLabel.getTeamExports())) {
            if (state.getExportSettingStatus() == 1) {
                TeamExport teamExport = createLabel.getTeamExport();
                teamExport.setCreateId(id);
                teamService.insertTeamExport(teamExport);
            } else {
                TeamExport teamExport = new TeamExport();
                teamExport.setCreateId(id);
                teamService.insertTeamExportDefault(teamExport);
            }
        } else {
            TeamExport teamExport = new TeamExport();
            teamExport.setCreateId(id);
            teamService.insertTeamExportDefault(teamExport);
        }*/

        //添加导出开关
        for (TeamExportEnum value : TeamExportEnum.values()) {
            TeamExport teamExport = new TeamExport();
            teamExport.setCreateId(id);
            teamExport.setPerms(value.getPerms());
            teamExport.setMenuName(value.getMenuName());
            teamExport.setButtonName(value.getButtonName());
            teamExport.setRemark(value.getRemark());
            teamService.insertTeamExportDefault(teamExport);
        }


//        try {
//            //
//            //创建完团队后 调用呼叫中心接口 创建入网企业  2022-8-24 19:46:34 -廖智涛
//            CompanyParam param = new CompanyParam();
//            param.setCompanyName(create.getCname());
//            param.setServiceHostUrl(callConfig.getHostUrl());
//            String companyNum = caiCallCenter.createCompany(param);
//            //更新团队的入网企业编号
//            create.setCompanyNum(companyNum);
//            teamService.updateCompanyNum(create);
//        } catch (Exception e) {
//            log.error("创建入网企业失败", e);
//        }
        //远程调用新增本月机构的费用管理数据
        if (createLabel.getCreateUtils().getCooperation() == 0) {
            remoteCaseService.addCost((long) id, createUtils.getCname());
        }

    }

    /**
     * 根据团队id查询对应的团队信息以及标签
     *
     * @param id
     * @return
     */
    public CreateLabel selectCreateLabelId(int id) {
//        根据团队id获取团队信息
        Create idCreate = teamService.findIdCreate(id);
//        根据团队id获取团队标签信息
        List<Label> idLabel = teamService.findIdLabel(id);
//        根据团队id获取团队文件信息
        List<Files> files = teamService.findFiles(id);
        CreateLabel createLabel = new CreateLabel();
        createLabel.setCreate(idCreate);
        createLabel.setLabels(idLabel);
        createLabel.setFiles(files);
        return createLabel;
    }

    /**
     * 根据团队id查询对应的团队
     *
     * @param createId
     * @return
     */
    public CreateLabel selectCreateId(int createId) {
//        根据团队id获取状态控制表信息
        State state = teamService.findState(createId);
//        根据团队id获取数据脱敏详情状态表信息
        Desensitization desensitization = teamService.selectDesensitization(createId);
//        根据团队id获取水印设置字段
        Watermark watermark = teamService.selectWatermark(createId);

        CreateLabel createLabel = new CreateLabel();
        //根据团队id查询导出设置字段
        //List<TeamExport>  teamExports = teamService.selectTeamExports(createId);
        //createLabel.setTeamExports(teamExports);


        createLabel.setState(state);
        createLabel.setDesensitization(desensitization);
        createLabel.setWatermark(watermark);


        return createLabel;
    }

    /**
     * 月回款目标进行更新和存入历史记录表
     *
     * @param create
     */
    public void updateTargets(Create create) {
        teamService.updateCreates(create);
        Targets targets = new Targets();
        targets.setCreateId(create.getId());
        targets.setCollectionTargets(create.getCollectionTargets());
        targets.setFounder(SecurityUtils.getUsername());
        targets.setCreationTime(new Date());
        teamService.insertTargets(targets);
    }

    /**
     * 团队信息修改以及上传新的文件
     *
     * @param createLabel
     */
    public void updateFiles(CreateLabel createLabel) {
        Create create = createLabel.getCreate();
//        修改团队信息
        teamService.updateCreate(create);
//        新文件上传
        List<Files> files = createLabel.getFiles();
//        取出团队id
        Integer id = create.getId();
        for (Files file1 : files) {
            file1.setCreateId(id);
            file1.setFounder(SecurityUtils.getUsername());
            file1.setCreationTime(new Date());
            file1.setDeleteLogo(0);
            files.add(file1);
        }
        teamService.insertFile(files);
    }

    /**
     * 团队审批设置流程信息修改/删除/新增
     *
     * @param approvalSettingsPojo
     */
    public void ApprovalSteps(ApprovalSettingsPojo approvalSettingsPojo) {
        Integer approveCode = approvalSettingsPojo.getApproveCode();  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
        switch (approveCode) {
            case 0:
                List<RepaymentRecord> list = applicationService.selectRepaymentRecord(TokenInformation.getCreateid());
                if (!ObjectUtils.isEmpty(list)) {
                    throw new GlobalException("该团队还有未走完调诉端审批流程的回款审核案件，无法修改审批流程");
                }
                break;
            case 1:
                List<ReductionRecord> reductionRecords = applicationService.selectReductionRecord(TokenInformation.getCreateid());
                if (!ObjectUtils.isEmpty(reductionRecords)) {
                    throw new GlobalException("该团队还有未走完调诉端审批流程的减免审核案件，无法修改审批流程");
                }
                break;
            case 2:
                List<StagingRecord> stagingRecords = applicationService.selectStagingRecord(TokenInformation.getCreateid());
                if (!ObjectUtils.isEmpty(stagingRecords)) {
                    throw new GlobalException("该团队还有未走完调诉端审批流程的分期还款审核案件，无法修改审批流程");
                }
                break;
            case 3:
                List<ApplyRecord> applyRecord = applicationService.selectApplyRecord(1, TokenInformation.getCreateid());
                if (!ObjectUtils.isEmpty(applyRecord)) {
                    throw new GlobalException("该团队还有未走完调诉端审批流程的留案审核案件，无法修改审批流程");
                }
                break;
            case 4:
                List<ApplyRecord> applyRecords = applicationService.selectApplyRecord(0, TokenInformation.getCreateid());
                if (!ObjectUtils.isEmpty(applyRecords)) {
                    throw new GlobalException("该团队还有未走完调诉端审批流程的停催审核案件，无法修改审批流程");
                }
                break;
            case 5:
                List<ApplyRecord> applyRecord1 = applicationService.selectApplyRecord(2, TokenInformation.getCreateid());
                if (!ObjectUtils.isEmpty(applyRecord1)) {
                    throw new GlobalException("该团队还有未走完调诉端审批流程的退案审核案件，无法修改审批流程");
                }
                break;
            case 7:
                List<OutsideRecord> outsideRecords = applicationService.selectOutsideRecord(TokenInformation.getCreateid());
                if (!ObjectUtils.isEmpty(outsideRecords)) {
                    throw new GlobalException("该团队还有未走完调诉端审批流程的外访审核案件，无法修改审批流程");
                }
                break;
            case 8:
                List<RetrievalRecord> retrievalRecords = applicationService.selectRetrievalRecord(TokenInformation.getCreateid());
                if (!ObjectUtils.isEmpty(retrievalRecords)) {
                    throw new GlobalException("该团队还有未走完调诉端审批流程的资料调取审核案件，无法修改审批流程");
                }
                break;
        }
        if (!ObjectUtils.isEmpty(approvalSettingsPojo.getRemarks()) && approvalSettingsPojo.getRemarks().length() > 50) {
            throw new GlobalException("长度不能超过50字，请重新输入");
        }
//        删除的id集合
        List<Integer> ids = approvalSettingsPojo.getIds();
        if (!ObjectUtils.isEmpty(ids)) {
            teamService.deleteApprovalSteps(ids);
        }
//        修改信息集合
        List<ApprovalSteps> list = new ArrayList<>();
//        新增信息集合
        List<ApprovalSteps> arrayList = new ArrayList<>();
//        新增以及修改的信息集合
        List<ApprovalSteps> approvalSteps = approvalSettingsPojo.getApprovalSteps();
        if (!ObjectUtils.isEmpty(approvalSteps)) {
            for (ApprovalSteps approvalSteps1 : approvalSteps) {
                if (!ObjectUtils.isEmpty(approvalSteps1.getId())) {
                    if (approvalSteps1.getId() == 0) {
                        continue;
                    } else {
//                        如果有主键id  走批量修改路线
                        list.add(approvalSteps1);
                    }
                } else {
//                    主键id为空   走批量新增路线
                    arrayList.add(approvalSteps1);
                }
            }
            if (!ObjectUtils.isEmpty(list)) {
//                批量修改
                teamService.updateApprovalSteps(list);
            }
            if (!ObjectUtils.isEmpty(arrayList)) {
//                批量新增
                teamService.insertApprovalStepsPojo(arrayList);
            }
            ApprovalSettings approvalSettings = new ApprovalSettings();
            approvalSettings.setModifier(SecurityUtils.getUsername());
            approvalSettings.setModifyTime(new Date());
            approvalSettings.setApproveCode(approvalSteps.get(0).getApproveCode());
            approvalSettings.setCreateId(approvalSteps.get(0).getCreateId());
            if (!ObjectUtils.isEmpty(approvalSettingsPojo.getRemarks())) {
                approvalSettings.setRemarks(approvalSettingsPojo.getRemarks());
            }
            teamService.updateApprovalSettingsDate(approvalSettings);
        }
        if (!ObjectUtils.isEmpty(approvalSettingsPojo.getIds()) || !ObjectUtils.isEmpty(approvalSettingsPojo.getApprovalSteps())) {
            ApprovalSteps approvalSteps1 = new ApprovalSteps();
            approvalSteps1.setApproveCode(approveCode);
            List<ApprovalSteps> approvalSteps2 = settingsService.selectApprovalSteps(approvalSteps1);
            if (ObjectUtils.isEmpty(approvalSteps2)) {
                throw new GlobalException("审批流程不可全部删除，需要留至少一个审批流程");
            }
        }
    }

    /**
     * 人工通过员工实名认证
     *
     * @param authentication
     */
    public void manualCertification(List<Authentication> authentication) {
        if (!ObjectUtils.isEmpty(authentication)) {
            for (Authentication authentications : authentication) {
                Certification certification = new Certification();
                certification.setCreateId(authentications.getCreateId());
                certification.setUserId(authentications.getUserId());
                certification.setState(3);  //验证状态(0-未验证,1-已验证,2-验证失败,3-人工通过)
                if (ObjectUtils.isEmpty(authentications.getId())) {
                    teamService.addCertification(certification);
                } else {
                    certification.setId(authentications.getId());
                    teamService.updateCertification(certification);
                }
            }
        }
    }

    /**
     * 返回部门列表树类型信息
     *
     * @param
     * @return
     */
    public List<TreeType> DeptTreeType(int createId) {
        List<Dept> depts = settingsService.selectDept(createId);   //部门全查
        List<TreeType> treeTypes = new ArrayList<>();
        for (Dept dept : depts) {
            if (dept.getParentId().equals(0)) {    //如果为顶级部门
                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept.getId());
                treeType.setName(dept.getDeptName());
                Boolean aBoolean = ChildNode(dept.getId());
                if (aBoolean) {
                    List<TreeType> listr = listr(depts, dept);
                    treeType.setChildren(listr);
                }

                treeTypes.add(treeType);
            }
        }
        List<TreeType> list = new ArrayList<>();
        TreeType treeType1 = new TreeType();

        Create idCreate = teamService.findIdCreate(createId);
        if (ObjectUtils.isEmpty(idCreate)) {
            return new ArrayList<TreeType>();
        }
        treeType1.setChildren(treeTypes);
        treeType1.setName(idCreate.getCname());
        treeType1.setId("Create:" + idCreate.getId());
        list.add(treeType1);

        return list;
    }

    /**
     * 判断是否有子列表
     *
     * @param parentId
     * @return
     */
    public Boolean ChildNode(int parentId) {
        List<Dept> deptList = settingsService.selectDeptParentId(parentId);
        return !ObjectUtils.isEmpty(deptList);
    }

    /**
     * 迭代查询子列表
     *
     * @param depts
     * @param dept
     * @return
     */
    public List<TreeType> listr(List<Dept> depts, Dept dept) {
        List<TreeType> list = new ArrayList<>();
        for (Dept dept1 : depts) {

            if (dept1.getParentId().equals(dept.getId())) {

                TreeType treeType = new TreeType();
                treeType.setId("Dept:" + dept1.getId());
                treeType.setName(dept1.getDeptName());
                List<TreeType> childrens = listr(depts, dept1);

                treeType.setChildren(childrens);
                list.add(treeType);

            }
        }

        return list;
    }

    /**
     * 修改设置状态并更新redis中的数据
     *
     * @param state
     */
    public void modifyState(State state) {
        teamService.updateStates(state);
        StateDesensitization desensitizationCreate = desensitizationRedis.getDesensitizationCreate(StaticConstantClass.DESENSITIZATION + state.getCreateId(), state.getCreateId());
        StateDesensitization stateDesensitization = new StateDesensitization();
        stateDesensitization.setDesensitization(desensitizationCreate.getDesensitization());
        stateDesensitization.setState(state);
        redisService.setCacheObject(StaticConstantClass.DESENSITIZATION + state.getCreateId(), stateDesensitization, CacheConstants.EXPIRATION, TimeUnit.MINUTES);
        redisService.setCacheObject(UserConstants.SAAS_SECURITY_VERIFICATION_SWITCH + state.getCreateId(), state.getSecurityVerificationStatus());
    }

    /**
     * 修改脱敏状态并更新redis中的数据
     *
     * @param desensitization
     */
    public void modifyDesensitization(Desensitization desensitization) {
        teamService.updateDesensitization(desensitization);
        StateDesensitization desensitizationCreate = desensitizationRedis.getDesensitizationCreate(StaticConstantClass.DESENSITIZATION + desensitization.getCreateId(), desensitization.getCreateId());
        StateDesensitization stateDesensitization = new StateDesensitization();
        stateDesensitization.setDesensitization(desensitization);
        stateDesensitization.setState(desensitizationCreate.getState());
        redisService.setCacheObject(StaticConstantClass.DESENSITIZATION + desensitization.getCreateId(), stateDesensitization, CacheConstants.EXPIRATION, TimeUnit.MINUTES);
    }

    /**
     * 批量修改合作状态写入修改记录表
     *
     * @param create
     */
    public void modifyCooperationStatus(List<Create> create) {
        List<Category> list = new ArrayList<>();   //记录表集合
        for (Create create1 : create) {
            if (ObjectUtils.isEmpty(create1.getId())) {
                throw new GlobalException("团队id不能为空");
            }
            if (ObjectUtils.isEmpty(create1.getCooperation())) {
                throw new GlobalException("团队合作状态不能为空");
            }
            Create idCreate = teamService.findIdCreate(create1.getId());
            if (ObjectUtils.isEmpty(idCreate)) {
                throw new GlobalException("存在错误的团队信息，请联系管理员");
            } else {
                Integer cooperation = idCreate.getCooperation();
                if (ObjectUtils.isEmpty(cooperation)) {
                    throw new GlobalException("查询团队合作状态为空");
                } else {
                    if (create1.getCooperation().equals(cooperation)) {
                        continue;
                    } else {
                        Category category1 = new Category();
                        category1.setTeamId(new Long(create1.getId()));
                        category1.setOperationBy(SecurityUtils.getUsername());
                        category1.setOperationById(SecurityUtils.getUserId());
                        category1.setOperationTime(new Date());
                        category1.setStatusBefore(idCreate.getCooperation());
                        category1.setStatusAfter(create1.getCooperation());
                        category1.setDeleteLogo("0");
                        list.add(category1);
                    }
                }
            }
        }
        teamService.updateCreateCooperation(create);
        if (!ObjectUtils.isEmpty(list)) {
            teamService.insertCategory(list);
        }

        for (Create create1 : create) {
            //远程调用新增本月机构的费用管理数据
            if (create1.getCooperation() == 0) {
                remoteCaseService.addCost(Long.valueOf(create1.getId()), create1.getCname());
            }
        }
    }

    /**
     * 修改团队信息判断团队合作状态是否改变，并写入修改历史记录表
     *
     * @param create
     */
    public void updateCreateState(Create create) {
        List<Category> list = new ArrayList<>();   //记录表集合
        if (create.getTeamType() == null) {
            throw new GlobalException("机构类型不能为空");
        }
        if (create.getEmail().length() > 50) {
            throw new GlobalException("联系人邮箱字数限制50字");
        }
        if (ObjectUtils.isEmpty(create.getCooperation())) {
            throw new GlobalException("团队状态不能为空");
        }
        if (ObjectUtils.isEmpty(create.getId())) {
            throw new GlobalException("团队id不能为空");
        }
        Create idCreate = teamService.findIdCreate(create.getId());
        if (ObjectUtils.isEmpty(idCreate)) {
            throw new GlobalException("查询团队信息失败");
        }
        if (ObjectUtils.isEmpty(idCreate.getCooperation())) {
            throw new GlobalException("查询团队状态不能为空");
        }
        if (!create.getCooperation().equals(idCreate.getCooperation())) {
            Category category1 = new Category();
            category1.setTeamId(new Long(create.getId()));
            category1.setOperationBy(SecurityUtils.getUsername());
            category1.setOperationById(SecurityUtils.getUserId());
            category1.setOperationTime(new Date());
            category1.setStatusBefore(idCreate.getCooperation());
            category1.setStatusAfter(create.getCooperation());
            category1.setDeleteLogo("0");
            list.add(category1);
        }
        teamService.updateCreate(create);
        if (!ObjectUtils.isEmpty(list)) {
            teamService.insertCategory(list);
        }

         //远程调用新增本月机构的费用管理数据
         if (create.getCooperation() == 0) {
             remoteCaseService.addCost(Long.valueOf(create.getId()), create.getCname());
         }

    }

    /**
     * 定时执行机构数据统计
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void scheduledTasks() {
        List<Create> creates = teamService.selectCreateByType();  //查询合作中的团队信息
        if (!ObjectUtils.isEmpty(creates)) {
            for (Create row : creates) {
                Date date = new Date();
//                一天的开始，结果：2017-03-01 00:00:00
                Date beginOfDay = DateUtil.beginOfDay(date);
//                一天的结束，结果：2017-03-01 23:59:59
                Date endOfDay = DateUtil.endOfDay(date);

                Map<String, Object> map = new HashMap<>();
                map.put("updateTime1", beginOfDay);
                map.put("updateTime2", endOfDay);
                map.put("teamId", row.getId());
                map.put("examineState", "已通过");
                BigDecimal bigDecimal = recordService.selectRepaymentRecordIdByTime(map);  //每天回款金额统计

                Map<String, Object> map1 = new HashMap<>();
                map1.put("allocatedTime2", endOfDay);
                map1.put("createId", row.getId());
                BigDecimal bigDecimal1 = caseService.selectCaseManagesByTime(map1);  //统计每天团队总的委托金额
                int count = caseService.selectCaseManagesByTimeCount(map1);  //查询统计每天团队的总案件数量

                Map<String, Object> map2 = new HashMap<>();
                map2.put("createTime1", beginOfDay);
                map2.put("createTime2", endOfDay);
                map2.put("createId", row.getId());
                int i = recordService.selectUrgeRecordCount(map2);  //统计团队每天的催记量

                TimeIntervalPojo timeIntervalPojo = new TimeIntervalPojo();
                timeIntervalPojo.setTeamId(new Long(row.getId()));
                timeIntervalPojo.setRecordTime1(beginOfDay);
                timeIntervalPojo.setRecordTime2(endOfDay);
                int number = iCallSipService.selectUnassignedSipNumber(timeIntervalPojo);//查询团队sip坐席信息

                int size = teamService.selectCallRecord(timeIntervalPojo);  //查询通话量

                Map<String, Object> map3 = new HashMap<>();
                Date monthBegins = TimeUtils.getMonthBegins();
                map3.put("updateTime1", monthBegins);  //当月月初
                map3.put("updateTime2", date);  //当前时间
                map3.put("teamId", row.getId());
                map3.put("examineState", "已通过");
//                查询每个团队当月已通过的回款申请回款金额统计
                BigDecimal bigDecimal2 = recordService.selectRepaymentByTime(map3);

                Evaluate evaluate = new Evaluate();
                evaluate.setTeamId(new Long(row.getId()));  //团队id
                evaluate.setTeamType(row.getCategory());  //团队类别
                evaluate.setCollectedPrincipal(bigDecimal);  //累计回款本金
                evaluate.setEntrustedAmount(bigDecimal1);  //原始不良本金余额
                evaluate.setReminderQuantity(i);  //催记量
                evaluate.setNumberSeats(number);  //坐席数量
                evaluate.setCallVolume(size);  //通话量
                evaluate.setCaseNumber(count);  //案件户数
                evaluate.setDelFlag(BaseConstant.DelFlag_Being);
                evaluate.setRecordTime(new Date());  //记录时间
                evaluate.setMonthCollectionTotal(bigDecimal2);  //月回款总额
                BigDecimal bigDecimal3 = row.getCollectionTargets() == null ? BigDecimal.ZERO : row.getCollectionTargets();
                evaluate.setMonthCollectionTarget(bigDecimal3);  //月回款目标
                if (bigDecimal2.compareTo(BigDecimal.ZERO) == 0 || bigDecimal3.compareTo(BigDecimal.ZERO) == 0) {
                    evaluate.setMonthCollectionRate(BigDecimal.ZERO);   //月回款率
                } else {
                    BigDecimal divide = bigDecimal2.multiply(new BigDecimal(100)).divide(bigDecimal3, 2, BigDecimal.ROUND_HALF_UP);
                    evaluate.setMonthCollectionRate(divide);   //月回款率
                }
                TimeIntervalPojo timeIntervalPojo1 = new TimeIntervalPojo();
                timeIntervalPojo1.setRecordTime1(beginOfDay);
                timeIntervalPojo1.setRecordTime2(endOfDay);
                timeIntervalPojo1.setTeamId(row.getId().longValue());
//                根据时间区间以及团队id查询已生成的记录信息
                List<Long> list = teamService.selectEvaluateTeamId(timeIntervalPojo1);
                if (ObjectUtils.isEmpty(list) || list.isEmpty()) {
//                    未生成则生成新的记录
                    teamService.addEvaluate(evaluate);
                } else {
//                    已生成则更新记录信息
                    evaluate.setDelFlag(null);
                    evaluate.setId(list.get(0));
                    teamService.updateEvaluate(evaluate);
                }
            }
        }

    }

    /**
     * 写入机构评价记录
     *
     * @param evaluationPojo
     */
    public void agencyRating(EvaluationPojo evaluationPojo) {
        TimeIntervalPojo timeIntervalPojo = new TimeIntervalPojo();
        timeIntervalPojo.setRecordTime1(evaluationPojo.getRecordTime1());
        timeIntervalPojo.setRecordTime2(evaluationPojo.getRecordTime2());
        timeIntervalPojo.setStartTime(evaluationPojo.getStartTime());
        timeIntervalPojo.setEndTime(evaluationPojo.getEndTime());
        timeIntervalPojo.setTeamId(evaluationPojo.getTeamId());
//        根据时间区间以及团队id查询该团队的评价信息
        Evaluate evaluate = evaluationFormService.selectEvaluate(timeIntervalPojo);
        EvaluationForm evaluationForm = new EvaluationForm();
        evaluationForm.setTeamId(evaluate.getTeamId());
        evaluationForm.setCollectionRate(evaluate.getCollectionRate());
        evaluationForm.setReminderQuantity(evaluate.getReminderQuantity());
        evaluationForm.setNumberSeats(evaluate.getNumberSeats());
        evaluationForm.setCallVolume(evaluate.getCallVolume());
        evaluationForm.setCountryCoverage(evaluate.getCountryCoverage());
        evaluationForm.setServiceQuality(evaluationPojo.getServiceQuality());
        evaluationForm.setServiceDeductPoints(evaluationPojo.getServiceDeductPoints());
        evaluationForm.setServiceRemarks(evaluationPojo.getServiceRemarks());
        evaluationForm.setInformationSafety(evaluationPojo.getInformationSafety());
        evaluationForm.setInformationDeductPoints(evaluationPojo.getInformationDeductPoints());
        evaluationForm.setInformationRemarks(evaluationPojo.getInformationRemarks());
        evaluationForm.setComplianceManagement(evaluationPojo.getComplianceManagement());
        evaluationForm.setComplianceDeductPoints(evaluationPojo.getComplianceDeductPoints());
        evaluationForm.setComplianceRemarks(evaluationPojo.getComplianceRemarks());
        evaluationForm.setPersonalScore(evaluationPojo.getPersonalScore());
        evaluationForm.setPersonalDeductPoints(evaluationPojo.getPersonalDeductPoints());
        evaluationForm.setPersonalRemarks(evaluationPojo.getPersonalRemarks());
        evaluationForm.setComprehensiveScore(evaluationPojo.getComprehensiveScore());
        evaluationForm.setGenerationDate(new Date());
        //结果 2017/03/01
        String format = DateUtil.format(evaluationPojo.getRecordTime1(), "yyyy/MM/dd");
        String formatEnd = DateUtil.format(evaluationPojo.getRecordTime2(), "yyyy/MM/dd");
        evaluationForm.setEvaluationTime(format + "——" + formatEnd);
        evaluationForm.setDelFlag(BaseConstant.DelFlag_Being);
        evaluationFormService.insertEvaluationForm(evaluationForm);
    }

    /**
     * 写入机构评价参数验证
     *
     * @param evaluationPojo
     */
    public void inspectParameter(EvaluationPojo evaluationPojo) {
        if (ObjectUtils.isEmpty(evaluationPojo.getRecordTime1()) || ObjectUtils.isEmpty(evaluationPojo.getRecordTime2())) {
            throw new GlobalException("请输入正确的时间区间");
        }
        if (ObjectUtils.isEmpty(evaluationPojo.getTeamId())) {
            throw new GlobalException("团队id不能为空");
        }
        if (ObjectUtils.isEmpty(evaluationPojo.getServiceQuality())) {
            throw new GlobalException("回款率达成-评分不能为空");
        }
        if (ObjectUtils.isEmpty(evaluationPojo.getInformationSafety())) {
            throw new GlobalException("合规作业-评分不能为空");
        }
        if (ObjectUtils.isEmpty(evaluationPojo.getComplianceManagement())) {
            throw new GlobalException("案件投诉-评分不能为空");
        }
        if (ObjectUtils.isEmpty(evaluationPojo.getPersonalScore())) {
            throw new GlobalException("个人信息安全-评分不能为空");
        }
        if (ObjectUtils.isEmpty(evaluationPojo.getComprehensiveScore())) {
            throw new GlobalException("综合评分不能为空");
        }

        BigDecimal serviceQuality = evaluationPojo.getServiceQuality();  //回款率达成-评分
        if (serviceQuality.compareTo(BigDecimal.ZERO) < 0 || serviceQuality.compareTo(new BigDecimal(40)) > 0) {
            throw new GlobalException("回款率达成-评分不能小于零并不能大于四十");
        }
        BigDecimal informationSafety = evaluationPojo.getInformationSafety();  //合规作业-评分
        if (informationSafety.compareTo(BigDecimal.ZERO) < 0 || informationSafety.compareTo(new BigDecimal(20)) > 0) {
            throw new GlobalException("合规作业-评分不能小于零并不能大于二十");
        }
        BigDecimal complianceManagement = evaluationPojo.getComplianceManagement();  //案件投诉-评分
        if (complianceManagement.compareTo(BigDecimal.ZERO) < 0 || complianceManagement.compareTo(new BigDecimal(20)) > 0) {
            throw new GlobalException("案件投诉-评分不能小于零并不能大于二十");
        }
        BigDecimal personalScore = evaluationPojo.getPersonalScore();  //个人信息安全-评分
        if (personalScore.compareTo(BigDecimal.ZERO) < 0 || personalScore.compareTo(new BigDecimal(20)) > 0) {
            throw new GlobalException("个人信息安全-评分不能小于零并不能大于二十");
        }
        BigDecimal comprehensiveScore = evaluationPojo.getComprehensiveScore();  //综合评分
        if (comprehensiveScore.compareTo(BigDecimal.ZERO) < 0 || comprehensiveScore.compareTo(new BigDecimal(100)) > 0) {
            throw new GlobalException("综合评分不能小于零并不能大于一百");
        }
    }

    public void updateTeamExport(TeamExport teamExport) {
        teamService.updateTeamExport(teamExport);
    }
}
