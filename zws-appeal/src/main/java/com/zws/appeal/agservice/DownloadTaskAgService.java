package com.zws.appeal.agservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.FileConstant;
import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.appeal.domain.*;
import com.zws.appeal.domain.call.CallDownloadTask;
import com.zws.appeal.domain.call.CallDownloadTaskRecord;
import com.zws.appeal.domain.call.CallRecord;
import com.zws.appeal.enums.ExportUrgeDiaryFields;
import com.zws.appeal.pojo.*;
import com.zws.appeal.pojo.call.CallRecordParam;
import com.zws.appeal.pojo.staticConst.StaticConstantClass;
import com.zws.appeal.service.CaseService;
import com.zws.appeal.service.CollectionService;
import com.zws.appeal.service.SendRecordService;
import com.zws.appeal.service.call.ICallDownloadTaskRecordService;
import com.zws.appeal.service.call.ICallDownloadTaskService;
import com.zws.appeal.service.call.ICallRecordService;
import com.zws.appeal.utils.*;
import com.zws.system.api.RemoteCaseService;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 下载任务
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/23 17:37
 */
@Slf4j
@Component
public class DownloadTaskAgService {

    @Autowired
    private ICallRecordService callRecordService;
    @Autowired
    private ICallDownloadTaskService callDownloadTaskService;
    @Autowired
    private ICallDownloadTaskRecordService taskRecordService;
    @Autowired
    private RemoteCaseService remoteCaseService;
    @Autowired
    private SendRecordService sendRecordService;
    @Autowired
    private AgCaseService agCaseService;
    @Autowired
    private FileUpload fileUpload;
    @Autowired
    private AgTeamCaseService agTeamCaseService;
    @Autowired
    private CaseService caseService;
    @Autowired
    private CollectionService collectionService;
    @Autowired
    private DesensitizationAgService desensitizationAgService;
    @Autowired
    private DesensitizationRedis desensitizationRedis;

    /**
     * 生成下载录音任务
     *
     * @param param
     * @return 录音任务名
     */
    public String createTask(CallRecordParam param) {

        List<CallRecord> records = callRecordService.selectList(param.getParam());
        if (records.size() == 0) {
            throw new ServiceException("选择的记录数为0无法生成下载任务,请重新选择");
        }
        String taskName = callDownloadTaskService.createTaskName();
        CallDownloadTask task = new CallDownloadTask();
        task.setTaskName(taskName);
        task.setTeamId(TokenInformation.getCreateid().longValue());
        task.setOperationType(TokenInformation.getType());
        long taskId = callDownloadTaskService.insert(task);

        List<CallDownloadTaskRecord> taskRecords = new ArrayList<>();
        for (CallRecord record : records) {
            CallDownloadTaskRecord taskRecord = new CallDownloadTaskRecord();
            taskRecord.setTaskId(taskId);
            taskRecord.setRecordId(record.getId());
            taskRecord.setDownload(UserConstants.EXCEPTION);
            taskRecords.add(taskRecord);
        }
        taskRecordService.batchInsert(taskRecords);

        //执行下载任务
        R r = remoteCaseService.executeWorkTask(taskId, SecurityConstants.INNER);
        if (r.getCode() != R.SUCCESS) {
            throw new ServiceException(r.getMsg());
        }
        return taskName;
    }

    /**
     * 下载短信任务
     *
     * @param param
     * @return
     */
    public String downloadSms(SendRecords param) throws IOException {
        List<SendRecords> sendRecordList = sendRecordService.selectSendRecords(param);
        for (SendRecords sendRecord : sendRecordList) {
            if (sendRecord.getSendNum() != null && sendRecord.getSendNum() > 0) {
                List<ReplyRecord> replyRecords = sendRecordService.selectReplayById(sendRecord.getId());
                sendRecord.setReplyRecords(replyRecords);
            }
        }

        File tempFile = FileDownloadUtils.getTempExcelFile();
        FileOutputStream out = new FileOutputStream(tempFile);
        ExcelUtil<SendRecords> util = new ExcelUtil<>(SendRecords.class);
        util.exportExcel(out, sendRecordList, "短信发送记录");
        out.close();

        FileInputStream inputStream = new FileInputStream(tempFile);
        MultipartFile multipartFile = new MockMultipartFile(tempFile.getName(), tempFile.getName(),
                ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
        inputStream.close();
        FileDownloadUtils.deletedTempFile(tempFile);
        try {
            MultipartFile[] files = new MultipartFile[]{multipartFile};
            String fileName = "SmsSendRecord";
            String[] lists = new String[]{".xlsx"};
            Map<String, Object> fileMap = fileUpload.uploadFile(files, fileName, lists);
            List<String> urls = (List<String>) fileMap.get("fileUrl");
            String url = urls.get(0);
            return url;
        } catch (Exception e) {
            log.error("保存失败文件错误", e);
            throw new ServiceException("导出文件异常：" + e.getMessage());
        }
    }

    /**
     * 导出案件任务
     *
     * @param param
     * @return
     */
    public String downloadCase(ExportReminder param) throws IOException {
        List<CaseManage> caseManages = agCaseService.dataDesensitization(param);
        StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid(param.getLoginUser()));
        State state = stateDesensitization.getState();
        boolean flag;
        if (state.getInformationStatus() == 1) {
            Desensitization desensitization = stateDesensitization.getDesensitization();
            flag = desensitization.getNumbers() == 1;
        } else {
            flag = false;
        }
        /*List<DownloadCasePojo> pojos=new ArrayList<>();
        for (CaseManage temp:caseManages) {
            DownloadCasePojo tempPojo=new DownloadCasePojo();
            tempPojo.setCaseId(temp.getCaseId());
            tempPojo.setLoginAccount(temp.getLoginAccount());
            pojos.add(tempPojo);
        }*/
        List<ExportTeamCaseVo> exportTeamCaseVoList = new ArrayList<>();
        for (CaseManage caseManage : caseManages) {
            // todo 换实体
            ExportTeamCaseVo exportTeamCaseVo = BeanUtil.copyProperties(caseManage, ExportTeamCaseVo.class);
            List<InfoContact> infoContactList = collectionService.selectInfoContactListByCaseId(exportTeamCaseVo.getCaseId());
            List<String> ownerlist = new ArrayList<>();
            List<String> otherlist = new ArrayList<>();
            infoContactList.forEach(infoContact -> {
                String contactPhone = infoContact.getContactPhone();
                if (infoContact.getContactRelation().equals("本人")) {
                    if (!StrUtil.isBlankIfStr(contactPhone)) {
                        if (flag) {
                            contactPhone = DataMaskingUtils.phoneMasking(contactPhone);
                        }
                        ownerlist.add(contactPhone);
                    }
                } else {
                    if (!StrUtil.isBlankIfStr(contactPhone)) {
                        if (flag) {
                            contactPhone = DataMaskingUtils.phoneMasking(contactPhone);
                        }
                        otherlist.add(contactPhone);
                    }
                }
            });
            exportTeamCaseVo.setClientPhone(String.join(BaseConstant.comma, ownerlist));
            exportTeamCaseVo.setContactPhone(String.join(BaseConstant.comma, otherlist));
            //exportTeamCaseVo.setClientIdcard(exportTeamCaseVo.getClientIdcard()+"【"+exportTeamCaseVo.getClientCensusRegister()+"】");
            exportTeamCaseVoList.add(exportTeamCaseVo);
        }

        File tempFile = FileDownloadUtils.getTempExcelFile();
        FileOutputStream out = new FileOutputStream(tempFile);
        ExcelUtil<ExportTeamCaseVo> util = new ExcelUtil<>(ExportTeamCaseVo.class);
        util.exportExcel(out, exportTeamCaseVoList, "案件记录");
        out.close();

        FileInputStream inputStream = new FileInputStream(tempFile);
        MultipartFile multipartFile = new MockMultipartFile(tempFile.getName(), tempFile.getName(),
                ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
        inputStream.close();
        FileDownloadUtils.deletedTempFile(tempFile);
        try {
            MultipartFile[] files = new MultipartFile[]{multipartFile};
            String fileName = "CaseManage";
            String[] lists = new String[]{".xlsx"};
            Map<String, Object> fileMap = fileUpload.uploadFile(files, fileName, lists);
            List<String> urls = (List<String>) fileMap.get("fileUrl");
            String url = urls.get(0);
            return url;
        } catch (Exception e) {
            log.error("保存失败文件错误", e);
            throw new ServiceException("导出文件异常：" + e.getMessage());
        }
    }


    /**
     * 导出团队案件任务
     *
     * @param teamCaseUtils
     * @return
     */
    public String downloadTeamCase(TeamCaseUtils teamCaseUtils) throws IOException {
        teamCaseUtils.setClientName(FieldEncryptUtil.encrypt(teamCaseUtils.getClientName()));
        teamCaseUtils.setClientIdcard(FieldEncryptUtil.encrypt(teamCaseUtils.getClientIdcard()));
        teamCaseUtils.setClientPhone(FieldEncryptUtil.encrypt(teamCaseUtils.getClientPhone()));

        // 将当前操作对象传入
        List<CaseManage> caseManages = agTeamCaseService.selectExportTeamCase(teamCaseUtils, teamCaseUtils.getLoginUser());
        StateDesensitization stateDesensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid(teamCaseUtils.getLoginUser()));
        State state = stateDesensitization.getState();
        boolean flag;
        if (state.getInformationStatus() == 1) {
            Desensitization desensitization = stateDesensitization.getDesensitization();
            flag = desensitization.getNumbers() == 1;
        } else {
            flag = false;
        }
        List<ExportTeamCaseVo> exportTeamCaseVoList = new ArrayList<>();
//        List<CaseManage> caseManages = agCaseService.dataDesensitization(param);
        for (CaseManage caseManage : caseManages) {
            // todo 换实体
            ExportTeamCaseVo exportTeamCaseVo = BeanUtil.copyProperties(caseManage, ExportTeamCaseVo.class);
            List<InfoContact> infoContactList = collectionService.selectInfoContactListByCaseId(exportTeamCaseVo.getCaseId());
            List<String> ownerlist = new ArrayList<>();
            List<String> otherlist = new ArrayList<>();
            infoContactList.forEach(infoContact -> {
                String contactPhone = infoContact.getContactPhone();
                if (infoContact.getContactRelation().equals("本人")) {
                    if (!StrUtil.isBlankIfStr(contactPhone)) {
                        if (flag) {
                            contactPhone = DataMaskingUtils.phoneMasking(contactPhone);
                        }
                        ownerlist.add(contactPhone);
                    }
                } else {
                    if (!StrUtil.isBlankIfStr(contactPhone)) {
                        if (flag) {
                            contactPhone = DataMaskingUtils.phoneMasking(contactPhone);
                        }
                        otherlist.add(contactPhone);
                    }
                }
            });
            exportTeamCaseVo.setClientPhone(String.join(BaseConstant.comma, ownerlist));
            exportTeamCaseVo.setContactPhone(String.join(BaseConstant.comma, otherlist));
            //exportTeamCaseVo.setClientIdcard(exportTeamCaseVo.getClientIdcard()+"【"+exportTeamCaseVo.getClientCensusRegister()+"】");
            exportTeamCaseVoList.add(exportTeamCaseVo);
        }


        File tempFile = FileDownloadUtils.getTempExcelFile();
        FileOutputStream out = new FileOutputStream(tempFile);
        ExcelUtil<ExportTeamCaseVo> util = new ExcelUtil<>(ExportTeamCaseVo.class);
        util.exportExcel(out, exportTeamCaseVoList, "案件记录");
        out.close();

        FileInputStream inputStream = new FileInputStream(tempFile);
        MultipartFile multipartFile = new MockMultipartFile(tempFile.getName(), tempFile.getName(),
                ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
        inputStream.close();
        FileDownloadUtils.deletedTempFile(tempFile);
        try {
            MultipartFile[] files = new MultipartFile[]{multipartFile};
            String fileName = "CaseManage";
            String[] lists = new String[]{".xlsx"};
            Map<String, Object> fileMap = fileUpload.uploadFile(files, fileName, lists);
            List<String> urls = (List<String>) fileMap.get("fileUrl");
            String url = urls.get(0);
            return url;
        } catch (Exception e) {
            log.error("保存失败文件错误", e);
            throw new ServiceException("导出文件异常：" + e.getMessage());
        }
    }

    /**
     * 导出团队催记
     *
     * @param param
     * @return
     */
    public String downloadTeamUrgeRecord(TeamExportUrgeParam param) throws Exception {
        if (ObjectUtils.isEmpty(param.getCreateTime1()) || ObjectUtils.isEmpty(param.getCreateTime2())) {
            throw new ServiceException("请选择催记开始截止时间");
        }
        TeamCaseUtils teamCaseUtils = param.getQueryParams();
        //当前登录人信息
        LoginUser loginUser = teamCaseUtils.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("登录信息获取异常");
        }
        List<ExportDataUtils> exportDataUtils = agTeamCaseService.selectTeamUrgeRecord(param);
        //脱敏
        Desensitization desensitization = desensitizationAgService.getDesensitization(loginUser);
        if (desensitization != null) {
            for (ExportDataUtils temp : exportDataUtils) {
                if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(temp.getClientName())) {
                    temp.setClientName(DataMaskingUtils.nameMasking(temp.getClientName()));
                    temp.setLiaison(DataMaskingUtils.nameMasking(temp.getLiaison()));
                }
                if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(temp.getContactMode())) {
                    temp.setContactMode(DataMaskingUtils.phoneMasking(temp.getContactMode()));
                }
                if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(temp.getClientIdcard())) {
                    temp.setClientIdcard(DataMaskingUtils.idMasking(temp.getClientIdcard()));
                }
            }
        }

        File tempFile = FileDownloadUtils.getTempExcelFile();
        FileOutputStream out = new FileOutputStream(tempFile);
        String fileName = "导出催记(时间)" + FileConstant.getExcelSuffix();
        if (ObjectUtils.isEmpty(exportDataUtils)) {
//            List<ExportDataUtils> list = new ArrayList<>();
//            list.add(new ExportDataUtils());
            exportDataUtils.add(new ExportDataUtils());
        }
        Map<String, String> fieldHeaderMap = ExportUrgeDiaryFields.getFieldHeaderMap(param.getHeaders());
        ExportUtils.customExcelHeader(out, fieldHeaderMap, exportDataUtils, fileName, "");
        out.close();
        String url = uploadFile(tempFile, "TeamUrgeRecord", FileConstant.getExcelSuffix());
        return url;
    }

    /**
     * 导出团队催记
     *
     * @param exportReminder
     * @return
     */
    public String downloadUrgeRecord(ExportReminder exportReminder) throws Exception {
        if (ObjectUtils.isEmpty(exportReminder.getCreateTime1()) || ObjectUtils.isEmpty(exportReminder.getCreateTime2())) {
            throw new ServiceException("请选择催记开始截止时间");
        }
        //当前登录人信息
        LoginUser loginUser = exportReminder.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("登录信息获取异常");
        }
        Object queryParams = exportReminder.getQueryParams();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(JSON.toJSONString(queryParams));
        if (jsonNode.has("ids") && jsonNode.get("ids").isArray()) {
            List<Long> ids = new ArrayList<>();
            for (JsonNode idNode : jsonNode.get("ids")) {
                ids.add(Long.parseLong(idNode.asText()));
            }
            exportReminder.setIds(ids);
        }
        if (jsonNode.has("settlementStatus")){
            exportReminder.setSettlementStatus(jsonNode.get("settlementStatus").asInt());
        }
        if (jsonNode.has("caseId")){
            exportReminder.setCaseId(jsonNode.get("caseId").asText());
        }
        List<ExportDataUtils> exportDataUtils = caseService.selectUrgeRecord(exportReminder);
        //脱敏
        Desensitization desensitization = desensitizationAgService.getDesensitization(loginUser);
        if (desensitization != null) {
            for (ExportDataUtils temp : exportDataUtils) {
                if (desensitization.getDname() == 1 && !ObjectUtils.isEmpty(temp.getClientName())) {
                    temp.setClientName(DataMaskingUtils.nameMasking(temp.getClientName()));
                    temp.setLiaison(DataMaskingUtils.nameMasking(temp.getLiaison()));
                }
                if (desensitization.getNumbers() == 1 && !ObjectUtils.isEmpty(temp.getContactMode())) {
                    temp.setContactMode(DataMaskingUtils.phoneMasking(temp.getContactMode()));
                }
                if (desensitization.getCardId() == 1 && !ObjectUtils.isEmpty(temp.getClientIdcard())) {
                    temp.setClientIdcard(DataMaskingUtils.idMasking(temp.getClientIdcard()));
                }
            }
        }

//        if (ObjectUtils.isEmpty(exportDataUtils)) {
//            List<ExportDataUtils> list = new ArrayList<>();
//            list.add(new ExportDataUtils());
//            Map<String, String> fieldHeaderMap = ExportUrgeDiaryFields.getFieldHeaderMap(exportReminder.getHeaders());
//            ExportUtils.customExcelHeader(response, fieldHeaderMap, list, "导出催记(时间).xlsx", "");
//        } else {
//            Map<String, String> fieldHeaderMap = ExportUrgeDiaryFields.getFieldHeaderMap(exportReminder.getHeaders());
//            ExportUtils.customExcelHeader(response, fieldHeaderMap, exportDataUtils, "导出催记(时间).xlsx", "");
//        }

        File tempFile = FileDownloadUtils.getTempExcelFile();
        FileOutputStream out = new FileOutputStream(tempFile);
        String fileName = "导出催记(时间)" + FileConstant.getExcelSuffix();
        if (ObjectUtils.isEmpty(exportDataUtils)) {
            exportDataUtils.add(new ExportDataUtils());
        }
        Map<String, String> fieldHeaderMap = ExportUrgeDiaryFields.getFieldHeaderMap(exportReminder.getHeaders());
        ExportUtils.customExcelHeader(out, fieldHeaderMap, exportDataUtils, fileName, "");
        out.close();
        String url = uploadFile(tempFile, "UrgeRecord", FileConstant.getExcelSuffix());
        return url;
    }


    /**
     * 上传保存文件
     *
     * @param tempFile
     * @param portfolioName 文件夹名
     * @param fileSuffix    文件格式
     * @return 返回文件的访问url
     * @throws IOException
     */
    private String uploadFile(File tempFile, String portfolioName, String fileSuffix) throws IOException {
        if (tempFile == null) {
            throw new ServiceException("文件不能为空");
        }
        FileInputStream inputStream = new FileInputStream(tempFile);
        MultipartFile multipartFile = new MockMultipartFile(tempFile.getName(), tempFile.getName(),
                ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
        inputStream.close();
        FileDownloadUtils.deletedTempFile(tempFile);
        try {
            MultipartFile[] files = new MultipartFile[]{multipartFile};
            String[] lists = new String[]{fileSuffix};
            Map<String, Object> fileMap = fileUpload.uploadFile(files, portfolioName, lists);
            List<String> urls = (List<String>) fileMap.get("fileUrl");
            String url = urls.get(0);
            return url;
        } catch (Exception e) {
            log.error("保存失败文件错误", e);
            throw new ServiceException("导出文件异常：" + e.getMessage());
        }
    }

}
