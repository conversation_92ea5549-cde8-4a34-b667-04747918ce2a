package com.zws.appeal.scheduled;

import com.zws.appeal.service.CensusAssetsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 催收端定时任务
 *
 * @Author: 马博新
 * @DATE: Created in 2023/5/26 11:20
 */
@Component
public class ScheduledTasksTeam {

    @Autowired
    private CensusAssetsService censusAssetsService;

    /**
     * 定时任务
     * 统计数据概览数据
     */
//    @Scheduled(cron = "0 5 0 * * ?")
    public void getStatisticsTask() {
        censusAssetsService.getStatisticsTask();
    }

    /**
     * 批次维度_数据概览
     */
//    @Scheduled(cron = "0 5 0 * * ?")
    public void autoCensusAssets() {
        censusAssetsService.autoCensusAssets();
    }

    /**
     * 数据概览_员工维度
     */
//    @Scheduled(cron = "0 5 0 * * ?")
    public void getStatisticsEmployeeTask() {
        censusAssetsService.getStatisticsEmployeeTask();
    }


}
