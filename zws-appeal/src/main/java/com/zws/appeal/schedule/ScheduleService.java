package com.zws.appeal.schedule;

import cn.hutool.core.util.StrUtil;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.core.domain.ScheduleVo;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 异步进度
 *
 * <AUTHOR>
 * @date 2024/1/6 9:40
 */
@Service
@Slf4j
public class ScheduleService implements IScheduleService {

    @Autowired
    private RedisService redisService;

    @Override
    public ScheduleVo getSchedule(String scheduleNo) {
        String redisKey = CacheConstants.ASYN_SCHEDULE + scheduleNo;
        if (!redisService.hasKey(redisKey)) {
            throw new ServiceException("停留时间太长，请重新操作");
        }
        ScheduleVo scheduleVo = redisService.getCacheObject(redisKey, ScheduleVo.class);
        return scheduleVo;
    }

    @Override
    public void setSchedule(ScheduleVo schedule) {
        if (schedule == null || StrUtil.isEmpty(schedule.getScheduleNo())) {
            return;
        }
        String redisKey = CacheConstants.ASYN_SCHEDULE + schedule.getScheduleNo();
        redisService.setCacheObject(redisKey, schedule);
        if (schedule.getSchedule() == 100) {
            redisService.expire(redisKey, 30L, TimeUnit.MINUTES);
        }
        //log.info("设置异步进度，编号：{},进度：{}",schedule.getScheduleNo(),schedule.getSchedule());
    }
}
