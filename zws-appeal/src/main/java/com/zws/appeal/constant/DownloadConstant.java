package com.zws.appeal.constant;

/**
 * 下载模板常量
 * <AUTHOR>
 * @date ：Created in 2023/4/11
 */
public class DownloadConstant {

    /**导入物流模板_sheet1*/
    public static  final String[] logisticsTemplateSheet1=new String[] {"函件单号","公司编码","快递单号","收件地址"};
    public static  final String[] logisticsTemplateSheet3=new String[] {"文书单号","公司编码","快递单号","收件地址"};

    /**导入物流模板_sheet2*/
    public static  final String[] logisticsTemplateSheet2=new String[] {"公司名称","公司编码","说明"};

    /**
     * redis 物流模板文件缓存
     */
    public final static String LOGISTICS_TEMPLATE_FILE="logistics_template_file:";

    /**
     * redis 实时查询快递上次查询时间
     */
    public final static String LAST_QUERY_TIME_NUMBERS="last_query_time_numbers:";

    /**
     * 快递公司编码常量
     */
    public static  final String[] companyCode=new String[] {
        "ems"
        ,"youzhengguonei"
        ,"yuantong"
        ,"yunda"
        ,"zhongtong"
        ,"shentong"
        ,"shunfeng"
        ,"huitongkuaidi"
        ,"zhaijisong"
        ,"tiantian"
        ,"debangwuliu"
        ,"guotongkuaidi"
        ,"zengyisudi"
        ,"suer"
        ,"ztky"
        ,"zhongtiewuliu"
        ,"ganzhongnengda"
        ,"youshuwuliu"
        ,"quanfengkuaidi"
        ,"jd"
    };


    /** 业务类别数组 */
    public static  final String[] BUSINESS_CATEGORY = new String[] {"合同纠纷","民间纠纷","劳动争议","婚姻家事","交通事故","知识产权","其他"};

    /** 客户来源数组 */
    public static  final String[] CUSTOMER_SOURCE = new String[] {"自己开发","第三方流量平台","客户主动咨询","老客户业务","他人推荐","律所调配","其他"};

    /**
     * 导入客户公海池模板
     */
    public static  final String[] CUSTOMER_TEMPLATE = new String[] {"业务摘要","客户姓名","客户电话","联系人姓名",
            "联系人电话","业务类别","客户单位名称","客户来源","来源说明","所在地区-省","所在地区-市","联系人地址"};
}
