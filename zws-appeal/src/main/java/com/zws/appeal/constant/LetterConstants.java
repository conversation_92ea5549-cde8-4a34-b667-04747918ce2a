package com.zws.appeal.constant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 函件模块常量
 * <AUTHOR>
 * @date ：Created in 2023/1/9 10:32
 */
public class LetterConstants {

    /**
     * redis- 函件校验文件缓存
     */
    public final static String RK_LETTER_VERIFY_FILE="letter_verify_file:";

    /**
     *脱敏函件链接
     */
    //public final static String URL="http://119.23.251.172:9204/letter/item/getFile?id=";
    //public final static String URL="http://47.97.187.230:9204/letter/item/getFile?id=";


    /**
     * 模糊搜索 特殊字符 ("_"、"%"模糊搜索时会失效，变成搜索全部 )
     */
    public final static List<String>likeArr =new ArrayList<>(Arrays.asList(new String[]{"_","%"}));

}
