package com.zws.appeal.loginjournal;

import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.ip.AddressUtils;
import com.zws.appeal.domain.TeamLogininfor;
import com.zws.appeal.service.TeamLogininforService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;


/**
 * 异步保存日志
 *
 * <AUTHOR>
 * @date ：Created in 2022/4/13 18:39
 */
@Service
public class DisposeAsyncLogService {

    @Autowired
    private TeamLogininforService logininforService;

    /**
     * 异步 保存催收端登录日志
     *
     * @param logininfor
     */
    @Async
    public void saveTeamLog(TeamLogininfor logininfor) {
        String ip = logininfor.getIpaddr();
        if (StringUtils.isNotEmpty(ip)) {
            logininfor.setLoginArea(AddressUtils.getRealAddressByIP(ip));
        }
        logininforService.saveLogInInfor(logininfor);
    }

}
