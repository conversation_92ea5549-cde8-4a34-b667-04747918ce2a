package com.zws.appeal.controller.letterDoc.letter.service.impl;

import cn.hutool.core.date.DateUtil;
import com.zws.appeal.controller.letterDoc.letter.service.IMessageService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.enums.MessageFormats;
import com.zws.common.redis.service.RedisMessageService;
import com.zws.system.api.RemoteMessageService;
import com.zws.system.api.domain.TeamUserMessage;
import com.zws.system.api.domain.UserMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@Primary
public class MessageTemplateServiceImpl implements IMessageService {


    @Autowired
    private RedisMessageService redisMessageService;
    @Autowired
    private RemoteMessageService remoteMessageService;


    /**
     * 推送消息提醒
     *
     * @param messageFormats 消息类型
     * @param identification 账号类型
     * @param userId         用户id\员工id\团队id
     * @param createId
     */
    @Override
    public void pushReminder(MessageFormats messageFormats, Integer identification, Long userId, Integer createId) {
        if (!redisMessageService.getMessagePush(messageFormats)) {
            return;
        }

        if (identification == UserConstants.ACCOUNT_TYPE_2) {//给资产端用户推送
            UserMessage userMessage = new UserMessage();
            userMessage.setUserId(userId);
            userMessage.setMessageTitle(messageFormats.getMessageTitle());
            userMessage.setMessageType(messageFormats.getMessageType().getCode());
            userMessage.setMessageContent(messageFormats.getFormat());
            userMessage.setReminderMode(messageFormats.getReminderMode());
            userMessage.setDelFlag(BaseConstant.DelFlag_Being);
            userMessage.setCreateTime(DateUtil.date());
            remoteMessageService.pushAssetUserMessage(userMessage, SecurityConstants.INNER);
        } else {
            List<TeamUserMessage> teamUserMessages = new ArrayList<>();
            TeamUserMessage tum = new TeamUserMessage();
            tum.setReminderMode(messageFormats.getReminderMode());
            tum.setMessageType(messageFormats.getMessageType().getCode());
            tum.setMessageTitle(messageFormats.getMessageTitle());
            tum.setMessageContent(messageFormats.getFormat());
            tum.setDelFlag(Integer.parseInt(BaseConstant.DelFlag_Being));
            tum.setMessageFrom(0);
            tum.setCreateId(new Long(createId));
            tum.setIdentification(identification);
            tum.setUserId(userId);
            tum.setCreateTime(new Date());
            teamUserMessages.add(tum);
            remoteMessageService.pussTeamUserMessage(teamUserMessages, SecurityConstants.INNER);
        }
    }
}
