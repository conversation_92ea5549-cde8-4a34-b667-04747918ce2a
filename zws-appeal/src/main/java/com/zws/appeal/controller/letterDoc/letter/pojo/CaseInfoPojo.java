package com.zws.appeal.controller.letterDoc.letter.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 默认配置参数 作为替换模板内容的值
 * <AUTHOR>
 */
@Data
public class CaseInfoPojo {

//    ----------  基本信息 ----------
    /**CaseInfoBase 主键id*/
    private Long id;
    /**
     * 借款人名称
     */
    private String clientName;
    /**
     *证件类型
     */
    private String clientIdType;
    /**
     * 证件号码
     */
    private String clientIdcard;
    /**
     *手机号
     */
    private String clientPhone;
    /**
     *银行卡号
     */
    private String bankCardNumber;
    /**
     * 户籍地址
     */
    private String registeredAddress;

    /**
     * 性别
     */
    private String clientSex;
    /**
     *年龄
     */
    private String clientAge;
    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clientBirthday;
    /**
     *担保人
     */
    private String securityName;
    /**
     *担保人证件类型
     */
    private String securityIdType;
    /**
     *担保人证件号码
     */
    private String securityIdNum;
    /**
     *担保人电话
     */
    private String securityPhone;
    /**
     * 单位名称
     */
    private String placeOfWork;
    /**
     *单位地址
     */
    private String workingAddress;
    /**
     *居住地址
     */
    private String residentialAddress;
    /**
     * 家庭住址
     */
    private String homeAddress;
    /**
     *开户行
     */
    private String bankName;
    /**
     * 职业
     */
    private String occupation;
    /**
     * QQ
     */
    private String qq;

    /**
     * 微信
     */
    private String weixin;

    /**
     * 邮箱
     */
    private String mailbox;
    /**
     * 资产编号
     */
    private String assetNo;
    /**
     * 婚姻状况
     */
    private String maritalStatus;

//    -------------------------------  案件信息 -----------------------------------------

    /**
     * 初始债权总额
     */
    private BigDecimal entrustMoney;
    /**贷款金额 */
    private BigDecimal loanMoney;
    /** 初始本息余额*/
    private BigDecimal ycInterestBalance;
    /** 初始利息余额*/
    private BigDecimal interestMoney;
    /** 初始本金余额*/
    private BigDecimal residualPrincipal;
    /** 初始费用*/
    private BigDecimal serviceFee;
    /** 垫付费用*/
    private BigDecimal ycDisbursement;
    /** 逾期日期*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date overdueStart;
    /** 借据号*/
    private String contractNo;
    /** 合同号*/
    private String ycContractNo;
    /** 五级分类*/
    private String ycFiveLevel;
    /** 币种*/
    private String ycCurrencies;

    /** 贷款用途*/
    private String ycPurpose;
    /** 贷款利率*/
    private BigDecimal ycLendingRate;
    /** 还款渠道*/
    private String ycRepaymentMethod;
    /** 诉讼状态*/
    private String ycLitigationStatus;
    /** 是否失信被执行人 0-否，1-是*/
    private String ycIsDishonest;
    /** 是否被限制高消费 0-否，1-是*/
    private String ycIsLimitConsumption;
    /**逾期天数 */
    private Integer ycOverdueDays;
    /** 核销日期*/
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ycWriteDate;
    /** 罚息利率*/
    private BigDecimal ycDefaultRate;
    /** 贷款本金*/
    private BigDecimal loanPrincipal;
    /** 每月应还*/
    private BigDecimal repaymentMonthly;
    /** 最后还款金额*/
    private BigDecimal amountFinalRepayment;
    /** 案件地区*/
    private String caseRegion;
    /** 贷款期数*/
    private Integer loanPeriods;
    /** 已还期数*/
    private Integer alreadyPeriods;
    /** 未还期数*/
    private Integer notPeriods;

    /** 每月还款日*/
    private Integer repaymentDate;
    /** 最后还款日期*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date amountFinalDate;
    /** 债权机构*/
    private String loanInstitution;
    /** 账期（M1-Mn）*/
    private String accountPeriod;
    /** 放款分(支)行*/
    private String ycLoanBank;
    /** 业务类型*/
    private String ycBusinessType;
    /** 产品类型(导入的数据)*/
    private String productType;
    /** 合同金额*/
    private BigDecimal ycContractMoney;
    /** 贷款期限*/
    private String ycLoanTerm;
    /** 贷款发放日*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ycLoanIssuanceDate;
    /**贷款到期日*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ycLoanMaturityDate;
    /** 基准日后还款金额*/
    private BigDecimal ycAbdRepayment;
    /** 基准日后本金还款*/
    private BigDecimal ycAbdPrincipal;
    /** 基准日后利息还款*/
    private BigDecimal ycAbdInterest;
    /** 退案日期*/
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date returnCaseDate;
    /** 剩余应还本金*/
    private BigDecimal syYhPrincipal;
    /** 剩余应还利息*/
    private BigDecimal syYhInterest;
    /** 剩余应还费用*/
    private BigDecimal syYhFees;
    /** 剩余应还(剩余应还债权总额)*/
    private BigDecimal remainingDue;
    /** 剩余应还罚息*/
    private BigDecimal syYhDefault;

}
