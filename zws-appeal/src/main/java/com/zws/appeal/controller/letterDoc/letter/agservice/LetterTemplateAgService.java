package com.zws.appeal.controller.letterDoc.letter.agservice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.itextpdf.text.Image;
import com.zws.common.core.domain.R;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.*;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.pdf.PdfUtils;
import com.zws.common.core.utils.pdf.po.PdfFile;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.domain.SysFile;
import com.zws.appeal.controller.letterDoc.law.mapper.LawSignMapper;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterDocService;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterTemplateService;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterVariableService;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.imageio.stream.ImageInputStream;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.List;

/**
 * 函件模板 agservice
 * <AUTHOR>
 * @date ：Created in 2023/11/23 22:25
 */
@Slf4j
@Component
public class LetterTemplateAgService {


    @Autowired
    private RemoteFileService remoteFileService;
    @Autowired
    private ILetterTemplateService templateService;
    @Autowired
    private ILetterDocService docService;
    @Autowired
    private ILetterVariableService letterVariableService;
    @Autowired
    private LawSignMapper lawSignMapper;
    /**
     * 生成模板预览
     * @param template
     * @return
     */
    public PdfFile createTemplatePreview(LetterTemplate template) {
//        List<Stamp> stamp = template.getPositionList();
//        for (Stamp stamp1 : stamp){
//            if (stamp1.getBottom()==null && stamp1.getLeft() == null){
////                template.setPositionList(PdfUtils.getPosition(template.getPositionList()));
//                return createTemplatePdf(template);
//            }
//            else {
//                template.setPositionList(PdfUtils.getPosition(template.getPositionList()));
//                return createPreview(template);
//            }
//        }
        if (template.getPositionList() != null && template.getPositionList().size() > 0) {
            template.setPositionList(PdfUtils.getPosition(template.getPositionList()));
        } else {
            return createTemplatePdf(template);
        }
        return createPreview(template);
    }

    /**
     * 生成模板预览
     * @param template
     * @return
     */
    public PdfFile createTemplatePreviews(LetterTemplate template) {
        if (template.getPositionList() != null && template.getPositionList().size() > 0) {
            template.setPositionList(PdfUtils.getPosition(template.getPositionList()));
        }
        return createPreview(template);
    }

    /**
     * 创建不盖章的函件pdf文件
     * @param template
     * @return
     */
    public PdfFile createTemplatePdf(LetterTemplate template) {
        boolean unique= templateService.checkUniqueName(template);
        if (unique){
        }else{
            throw new ServiceException("模版名称已存在！");
        }
        template.setPositionList(null);
        return createPreview(template);
    }


    /**
     * 生成预览
     *
     * @param template  函件模板
     * @return 返回预览url
     */
    public PdfFile createPreview(LetterTemplate template) {
        PdfFile pdfFile = new PdfFile();
        File file = null;
        try {
            pdfFile = createPDFFie(template);
            if (template.getPositionList() == null) {
                pdfFile.setUrl(fileUpload(pdfFile.getFile()));
            } else {
                //插入签章后上传文件
                file = PdfUtils.createPdfBySignPic(pdfFile.getFile(), template.getPositionList());
                pdfFile.setUrl(fileUpload(file));
            }
        } finally {
            try {
                if (pdfFile != null && pdfFile.getFile() != null) {
                    Files.deleteIfExists(pdfFile.getFile().toPath());
                }
                if (file != null) {
                    Files.deleteIfExists(file.toPath());
                }
            } catch (IOException e) {
                log.error("删除临时pdf文件失败" + e.getMessage());
            }
        }
        return pdfFile;
    }


    /**
     * 替换参数 替换实际数据的预览文书
     *
     * @param letterTemplate
     * @param
     * @return
     */
    public PdfFile createPreviewByVariable(HashMap<String, Object> caseInfoMap, LetterTemplate letterTemplate, int times) {
        //模板内容
        String content = templateService.getContent(letterTemplate, false);
        List<String> variables = getTemplateVariable(letterTemplate.getTemplateVariable());
        for (String variable : variables) {
            Object value = caseInfoMap.get(variable);
            if (StrUtil.equals(variable, "出生日期")) {
                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
            }
            if (StrUtil.equals(variable, "逾期日期")) {
                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
            }
            if (StrUtil.equals(variable, "最后还款日期")) {
                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
            }
            if (StrUtil.equals(variable, "贷款发放日")) {
                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
            }
            if (StrUtil.equals(variable, "贷款到期日")) {
                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
            }
            if (StrUtil.equals(variable, "退案日期")) {
                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
            }
            if (StrUtil.equals(variable, "核销日期")) {
                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
            }
            if (StrUtil.equals(variable, "自增编号")) {
                value = PinyinUtil.getFirstLetter(letterTemplate.getTemplateName(), ",").toUpperCase().replace(",", "") + "-0000" + times;
            }
            if (value == null) {
                value = "--";
            }

            //模板 替换
            content = content.replace(StrUtil.format("[{}]", variable), value.toString());
            log.error(content);
        }
        if (StringUtils.isEmpty(letterTemplate.getPageHeader())) {
            letterTemplate.setCutFooter("");
        }
        if (StringUtils.isEmpty(letterTemplate.getPageFooter())) {
            letterTemplate.setCutFooter("");
        }

        if (letterTemplate.getPositionList()==null) {
            File pdfTxtFile = PdfUtils.createHtmlPdf(content, letterTemplate.getCutHeader(), letterTemplate.getCutFooter());
            try {
                FileInputStream inputStream = new FileInputStream(pdfTxtFile);
                MultipartFile multipartFile = new MockMultipartFile(pdfTxtFile.getName(), pdfTxtFile.getName(),
                        ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
                R<SysFile> r = remoteFileService.upload(multipartFile);
                if (r.getCode() == R.SUCCESS) {
                    PdfFile file = new PdfFile();
                    file.setUrl(r.getData().getUrl());
                    file.setPages(letterTemplate.getPreviewPages());
//                    file.setContent(content);
                    return file;
                } else {
                    throw new ServiceException(r.getMsg());
                }
            } catch (Exception e) {
                log.error("生成预览失败", e);
                throw new ServiceException("生成预览失败，请联系管理员");
            } finally {
                try {
                    if (pdfTxtFile != null) {
                        Path path = pdfTxtFile.toPath();
                        Files.deleteIfExists(path);
                    }
                } catch (IOException e) {
                    log.error("删除临时pdf文件失败" + e.getMessage());
                    e.printStackTrace();
                }
            }
        } else {
            File pdfTxtFile = PdfUtils.createHtmlPdf(content, letterTemplate.getCutHeader(), letterTemplate.getCutFooter());
            File pdfFile = null;
            try {

                pdfFile = PdfUtils.createPdfBySignPic(pdfTxtFile, letterTemplate.getPositionList());

                FileInputStream inputStream = new FileInputStream(pdfFile);
                MultipartFile multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(),
                        ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
                R<SysFile> r = remoteFileService.upload(multipartFile);
                if (r.getCode() == R.SUCCESS) {
                    PdfFile file = new PdfFile();
                    file.setUrl(r.getData().getUrl());
                    file.setPages(letterTemplate.getPreviewPages());
//                    file.setContent(content);
                    return file;
                } else {
                    throw new ServiceException(r.getMsg());
                }
            } catch (Exception e) {
                log.error("生成预览失败", e);
                throw new ServiceException("生成预览失败，请联系管理员");
            } finally {
                try {
                    if (pdfFile != null) {
                        Path path = pdfFile.toPath();
                        Files.deleteIfExists(path);
                    }
                } catch (IOException e) {
                    log.error("删除临时pdf文件失败" + e.getMessage());
                    e.printStackTrace();
                }
            }
        }
    }

    public List<String> getTemplateVariable(String templateVariable) {
        if (StringUtils.isEmpty(templateVariable)) {
            return new ArrayList<>();
        }
        return Arrays.asList(templateVariable.split(","));
    }
    /**
     * 生成待参数的预览
     * @param doc           文书数据
     * @param templateId    模板ID
     * @param isHideInfo    是否脱敏
     * @return
     */
    public String createPreview(LetterDoc doc, Long templateId, boolean isHideInfo) {
        //模板内容
        LetterTemplate letterTemplate = templateService.getById(templateId);
        String content = getHtmlContent(letterTemplate);

        String data = doc.getItemData();
        JSONObject jsonObject = JSONUtil.parseObj(data);
        List<String> variables = SplitUtils.strSplitComma(letterTemplate.getTemplateVariable());


        for (String variable : variables) {
            String value = jsonObject.getStr(variable);
            if (value == null) {
                continue;
            }
            //判断是否需要脱敏
            if (isHideInfo) {
                if (variable.indexOf("姓名") > -1) {
                    value = DataMaskingUtils.nameMasking(value);
                } else if (variable.indexOf("电话") > -1 || variable.indexOf("手机") > -1) {
                    value = DataMaskingUtils.phoneMasking(value);
                } else if (variable.indexOf("身份证") > -1 || variable.indexOf("证件") > -1) {
                    value = DataMaskingUtils.idMasking(value);
                }
            }
            content = content.replace(StrUtil.format("[{}]", variable), value);
        }
        //页眉
        if (StringUtils.isNotEmpty(letterTemplate.getPageHeader()) && StringUtils.isNotEmpty(letterTemplate.getScaleHeader())) {
            String cutUrl= getCutUrl(letterTemplate.getScaleHeader(),166.7f);
            letterTemplate.setCutHeader(cutUrl);
        } else if (StringUtils.isEmpty(letterTemplate.getPageHeader())) {
            letterTemplate.setCutHeader("");
        }

        //页脚
        if (StringUtils.isNotEmpty(letterTemplate.getPageFooter()) && StringUtils.isNotEmpty(letterTemplate.getScaleFooter())) {
            String cutUrl= getCutUrl(letterTemplate.getScaleHeader(),100f);
            letterTemplate.setCutFooter(cutUrl);
        } else if (StringUtils.isEmpty(letterTemplate.getPageFooter())) {
            letterTemplate.setCutFooter("");
        }

        File pdfFile = PdfUtils.createHtmlPdf(content, letterTemplate.getCutHeader(), letterTemplate.getCutFooter());
        doc.setPreviewPages(letterTemplate.getPreviewPages());

        try {
            FileInputStream inputStream = new FileInputStream(pdfFile);
            MultipartFile multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(),
                    ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
            R<SysFile> r = remoteFileService.upload(multipartFile);
            if (r.getCode() == R.SUCCESS) {
                return r.getData().getUrl();
            } else {
                throw new ServiceException(r.getMsg());
            }
        } catch (Exception e) {
            log.error("生成预览失败", e);
            throw new ServiceException("生成预览失败，请联系管理员");
        } finally {
            try {
                if (pdfFile != null) {
                    Path path = pdfFile.toPath();
                    Files.deleteIfExists(path);
                }
            } catch (IOException e) {
                log.error("删除临时pdf文件失败" + e.getMessage());
            }
        }
    }


    /**
     * 生成模板的PDF文件
     *
     * @param template
     * @return
     */
    private PdfFile createPDFFie(LetterTemplate template) {
        String content = getHtmlContent(template);
        PdfFile pdfFile = new PdfFile();

        //页眉
        if (StringUtils.isNotEmpty(template.getPageHeader()) && StringUtils.isNotEmpty(template.getScaleHeader())) {
            String cutUrl= getCutUrl(template.getScaleHeader(),166.7f);
            template.setCutHeader(cutUrl);
        } else if (StringUtils.isEmpty(template.getPageHeader())) {
            template.setCutHeader("");
        }

        //页脚
        if (StringUtils.isNotEmpty(template.getPageFooter()) && StringUtils.isNotEmpty(template.getScaleFooter())) {
            String cutUrl= getCutUrl(template.getScaleHeader(),100f);
            template.setCutFooter(cutUrl);
        } else if (StringUtils.isEmpty(template.getPageFooter())) {
            template.setCutFooter("");
        }

        File file = PdfUtils.createHtmlPdf(content, template.getCutHeader(), template.getCutFooter());
        pdfFile.setFile(file);

        PDDocument pdDocument = null;
        try {
            pdDocument = PDDocument.load(file);
            int pages = pdDocument.getNumberOfPages();
            if (pages > 10) {
                throw new ServiceException("PDF文件页数上限为10页");
            }
            pdfFile.setPages(pages);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (pdDocument != null) {
                // 关闭资源
                try {
                    pdDocument.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return pdfFile;
    }

    /**
     * 获取裁剪后的 图片url
     * @param imgUrl    原图片url
     * @param finalHeight 需要保持的高度
     * @return
     */
    private String getCutUrl(String imgUrl,float finalHeight)  {
        try {
            Image image = Image.getInstance(imgUrl);
            if (image.getHeight() > finalHeight) {
                float heightPic = image.getHeight();
                float widthPic = image.getWidth();
                Rectangle rectangle= new Rectangle(0, (int) (heightPic - finalHeight), (int) widthPic, (int) finalHeight);
                String cutPicture = cutPictureByStream(imgUrl, rectangle);
                return cutPicture;
            } else {
                return imgUrl;
            }
        }catch (Exception e){
            log.error("获取裁剪后的 图片url 异常：",e);
            return null;
        }
    }

    /**
     * 上传文件
     * @param file
     * @return
     */
    public String fileUpload(File file) {
        String url = null;
        try {
            System.err.println("PDF文件路径：" + file.getAbsolutePath());
            FileInputStream inputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                    ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
            R<SysFile> r = remoteFileService.upload(multipartFile);
            if (r.getCode() == R.SUCCESS) {
                //pdfFile.setUrl(r.getData().getUrl());
                url = r.getData().getUrl();
            } else {
                throw new ServiceException(r.getMsg());
            }
        } catch (FeignException e) {
            log.error("生成预览失败,相关服务器未启动", e);
            throw new ServiceException("生成预览失败，相关服务器未启动，请联系管理员");
        } catch (Exception e) {
            log.error("生成预览失败", e);
            throw new ServiceException("生成预览失败，请联系管理员");
        }
        return url;
    }


    /**
     * 生成HTNL内容
     * @param template 函件模板
     * @return
     */
    public String getHtmlContent(LetterTemplate template) {
        //正文内容
        String bodyContent = template.getBodyContent();
        if (bodyContent == null) {
            bodyContent = "";
        }
        String b = "";
        StringBuilder builder = new StringBuilder();
        String content = "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<meta charset=\"UTF-8\"></meta>" +
                "\t<body style=\" font-family:宋体 ; " + b + " \">\t\t\t\t\t\t\t\n" +
                "\t<div>\n" +
                "\t\t{}\n" +
                "\t</div>\n";
        String content2 =
//                "\t<div style=\"margin-top: 30px;margin-bottom: 20px;float: left;width: 100%;\">\n" +
//                        "\t</div>\t \n" +
                        "\t<style>\t \n" +
                        "\tdiv{display:block !important;}\t \n" +
                        "\tspan{font-family:宋体 !important;}\t \n" +
                        "\t</style>\t \n" +
                        "\t</body>\n" +
                        "</html>";
        builder.append(content);
        builder.append(content2);
        String cu = null;
        cu = StrUtil.format(builder.toString(), bodyContent);
        log.info("HTML :{}",cu);
        return cu;
    }

    public String getBlankContent() {
        String bodyContent = "";
        String tailContent = "";
        String b = "";
        StringBuilder builder = new StringBuilder();
        String content = "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<meta charset=\"UTF-8\"></meta>" +
                "\t<body style=\" font-family:宋体 ; " + b + " \">\t\t\t\t\t\t\t\n" +
                "\t<div>\n" +
                "\t\t \n" +
                "\t</div>\n";
        String content2 =
                "\t<div style=\"margin-top: 30px;margin-bottom: 20px;float: left;width: 100%;\">\n" +
                        "\t\t \n" +
                        "\t</div>\t \n" +
                        "\t<style>\t \n" +
                        "\tdiv{display:block !important;}\t \n" +
                        "\tspan{font-family:宋体 !important;}\t \n" +
                        "\t</style>\t \n" +
                        "\t</body>\n" +
                        "</html>";
        builder.append(content);
        builder.append(content2);

        //String cu= StrUtil.format(builder.toString(),bodyContent,tailContent);
        //String cu= StrUtil.format(builder.toString(),bodyContent);
        String cu = builder.toString();
        return cu;
    }

    /**
     * 根据输入流进行裁剪
     * @param imgUrl 图片url
     * @rectangle 裁剪区域-矩形对象，表示矩形区域的x，y，width，height
     * @return 返回裁剪后的图片文件对象
     */
    public String cutPictureByStream(String imgUrl, Rectangle rectangle) {
        if (StrUtil.isEmpty(imgUrl)){
            throw new ServiceException("图片路径为空，无法裁剪");
        }
        File tempFile = null;
        ImageOutputStream outPut = null;
        String url =null;
        try (InputStream inputStream = URLUtil.url(imgUrl).openStream()) {
            //将原图等比例缩放后，再进行裁剪 downloadFile = PdfUtils.getScalePicByStream(path);
            //downloadFile =FileDownloadUtil.downloadTempFile(path);
            ImageInputStream inPut = ImageIO.createImageInputStream(inputStream);
            FileUtil.mkdir(FileDownloadUtils.tempFilePath);
            File file = FileUtil.file(FileDownloadUtils.tempFilePath);
            if (!file.exists()) {
                file.mkdir();
            }
            String filename = IdUtils.fastSimpleUUID() + ".png";
            tempFile = new File(file.getAbsoluteFile() + "/" + filename);
            outPut = ImgUtil.getImageOutputStream(tempFile);
            ImgUtil.cut(inPut, outPut, rectangle);
            url = fileUpload(tempFile);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (outPut != null) {
                    outPut.close();
                }
                if (tempFile != null) {
                    FileDownloadUtils.deletedTempFile(tempFile);
                }
            } catch (IOException e) {
                log.error("图片裁剪异常：",e);
            }
        }
        return url;
    }

}
