package com.zws.appeal.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.system.api.domain.WorkFollowUp;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 工单跟进
 *
 * <AUTHOR>
 * @date 2022-08-15
 */
@Data
public class WorkOrderFollowUpResp {

    /**
     * 是否属于本团队以及本催收员-(0：不属于；1：属于)
     */
    private Integer button;

    /**
     * 投诉至
     */
    private String complaintLevel;

    /**
     * 渠道来源
     */
    private String channelSource;

    /**
     * 问题类型
     */
    private String questionType;

    /**
     * 工单状态
     */
    private String orderStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 问题内容
     */
    private String questionContent;

    /**
     * 跟进内容
     */
    private List<WorkFollowUp> workFollowUp;

    /**
     * 发起人
     */
    private String nickName;

    /**
     * 催收员
     */
    private String employeeName;

    /**
     * 委案团队名称
     */
    private String cname;

    /**
     * 来电号码
     */
    private String callNumber;
}
