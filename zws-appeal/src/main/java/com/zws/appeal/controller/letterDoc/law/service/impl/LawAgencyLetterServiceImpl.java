package com.zws.appeal.controller.letterDoc.law.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.zws.appeal.controller.letterDoc.law.domain.LawAgencyLetter;
import com.zws.appeal.controller.letterDoc.law.mapper.LawAgencyLetterMapper;
import com.zws.appeal.controller.letterDoc.law.mapper.LawAgencyMapper;
import com.zws.appeal.controller.letterDoc.law.service.ILawAgencyLetterService;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/12/11 19:48
 */
@Service
@Transactional
public class LawAgencyLetterServiceImpl implements ILawAgencyLetterService {

    @Autowired
    private LawAgencyLetterMapper baseMapper;

    @Autowired
    private LawAgencyMapper lawAgencyMapper;

    @Autowired
    private ILetterTemplateService templateService;

    @Override
    public void insert(Long agencyId, List<Long> letterTemplateIds) {
        if (ArrayUtil.isEmpty(letterTemplateIds)){
            return;
        }
        //删除 不在 这这其中的
        baseMapper.deleteByNotInList(agencyId,letterTemplateIds);
        List<Long> ids = baseMapper.selectLetterTemplateIds(agencyId);
        for (Long letterTemplateId:letterTemplateIds ) {
            if (!ids.contains(letterTemplateId)){
                //不在数据库中的可以 重新添加
                LawAgencyLetter temp=new LawAgencyLetter();
                temp.setLawAgencyId(agencyId);
                temp.setLetterTemplateId(letterTemplateId);
                baseMapper.insert(temp);
            }
        }
    }

    @Override
    public List<LawAgencyLetter> selectByAgencyId(Long agencyId) {
        LawAgencyLetter param=new LawAgencyLetter();
        param.setLawAgencyId(agencyId);
        return this.baseMapper.selectList(param.getLawAgencyId());
    }

    /**
     * 根据法院id查询文书信息
     *
     * @param courtId
     * @return
     */
    @Override
    public List<LetterTemplate> selectByAgencyId1(Long courtId) {
        //        根据法院id查询所有文书管理id
        List<Long> list = lawAgencyMapper.selectCourtId(courtId);
        if (ObjectUtils.isEmpty(list) || list.size() == 0) {
            return new ArrayList<>();
        }
//        根据文书管理主键id查询文书管理信息
        LetterTemplate template = new LetterTemplate();
        template.setTemplateIds(list);
        List<LetterTemplate> LetterTemplates = templateService.selectList1(template);
        return LetterTemplates;
    }
}
