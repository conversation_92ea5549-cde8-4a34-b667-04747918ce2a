package com.zws.appeal.controller;

import cn.hutool.core.util.IdUtil;
import com.zws.appeal.mapper.CaseMapper;
import com.zws.common.core.domain.ScheduleVo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.IdUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.agservice.AgTeamCaseService;
import com.zws.appeal.agservice.DownloadTaskAgService;
import com.zws.appeal.agservice.ExportAgService;
import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.enums.CsExportClassEnum;
import com.zws.appeal.enums.ExportTeamUrgeDiaryFields;
import com.zws.appeal.pojo.*;
import com.zws.appeal.pojo.teamApplication.*;
import com.zws.appeal.pojo.teamParameters.*;
import com.zws.appeal.schedule.IScheduleService;
import com.zws.appeal.service.CensusAssetsService;
import com.zws.appeal.utils.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 团队案件Controller
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/teamCase")
public class TeamCaseController extends BaseController {

    @Autowired
    private AgTeamCaseService agTeamCaseService;
    @Autowired
    private IScheduleService scheduleService;
    @Autowired
    private CensusAssetsService censusAssetsService;
    @Autowired
    private ExportAgService exportAgService;
    @Autowired
    private CaseMapper caseMapper;


    /**
     * 我的团队/团队案件全查以及根据字段条件查询
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectTeamCase", method = RequestMethod.GET)
    public TableDataInfo selectList(TeamCaseUtils teamCaseUtils) {
        teamCaseUtils.setClientName(FieldEncryptUtil.encrypt(teamCaseUtils.getClientName()));
        teamCaseUtils.setClientIdcard(FieldEncryptUtil.encrypt(teamCaseUtils.getClientIdcard()));
        teamCaseUtils.setClientPhone(FieldEncryptUtil.encrypt(teamCaseUtils.getClientPhone()));
        List<CaseManage> caseManages = agTeamCaseService.selectTeamCase(teamCaseUtils, SecurityUtils.getLoginUser());



        //如果案件资产端标签不为空，则覆盖标签内容为资产端标签内容
        List<Map<String, String>> resultList = caseMapper.getAssetKeyVal();
        Map<String, String> map = new HashMap<>();

        for (Map<String, String> entry : resultList) {
            String key = entry.get("key");
            String value = entry.get("value");
            map.put(key, value);
        }
        for (CaseManage caseManage : caseManages) {
            //覆盖标签内容为资产端标签内容
            if (!ObjectUtils.isEmpty(caseManage.getLabelAsset())) {
                String s = map.get(caseManage.getLabelAsset());
                if (!ObjectUtils.isEmpty(s)) {
                    caseManage.setLabelContent(s);
                    caseManage.setCode(Integer.valueOf(caseManage.getLabelAsset()));
                    caseManage.setLabel(caseManage.getLabelAsset());
                }
            }else {
                caseManage.setLabel("*" + caseManage.getLabel());
            }
        }
        return getDataTable(caseManages);
    }

    /**
     * 指定分案查询数据以及可分配案件/案件总金额
     *
     * @param teamCaseUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCases", method = RequestMethod.POST)
    public AjaxResult selectMapWithCase(@RequestBody TeamCaseUtils teamCaseUtils) {
        if (teamCaseUtils.getCondition()) {
            //搜索结果
            teamCaseUtils.setCaseIds(null);
            System.out.println("搜索结果");
        } else {
            //本页
            System.out.println("本页");
            TeamCaseUtils teamCaseUtils1 = new TeamCaseUtils();
            teamCaseUtils1.setCaseIds(teamCaseUtils.getCaseIds());
            teamCaseUtils1.setCondition(teamCaseUtils.getCondition());
            teamCaseUtils = teamCaseUtils1;
        }
        teamCaseUtils.analysiStringDeptId();
        Map<String, Object> map = agTeamCaseService.selectId(teamCaseUtils, SecurityUtils.getLoginUser());
        return AjaxResult.success("查询成功", map);
    }

    /**
     * 根据字段查询该团队的案件总金额以及总案件量
     *
     * @param teamCaseUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCaseManagesMoney", method = RequestMethod.POST)
    public AjaxResult selectMapWithCaseManagesMoney(@RequestBody TeamCaseUtils teamCaseUtils) {
        Map<String, Object> map = agTeamCaseService.selectCaseManagesMoney(teamCaseUtils, SecurityUtils.getLoginUser());
        return AjaxResult.success("查询成功", map);
    }


    /**
     * 我的团队/团队承诺户全查以及根据字段条件查询
     *
     * @param urgeRecordUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectUrgeRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithUrgeRecordId(UrgeRecordUtils urgeRecordUtils) {
//        urgeRecordUtils.setClientName(FieldEncryptUtil.encrypt(urgeRecordUtils.getClientName()));
//        urgeRecordUtils.setClientIdcard(FieldEncryptUtil.encrypt(urgeRecordUtils.getClientIdcard()));
//        urgeRecordUtils.setClientPhone(FieldEncryptUtil.encrypt(urgeRecordUtils.getClientPhone()));
        List<CreateUrgeRecordIdUtils> createUrgeRecordIdUtils = agTeamCaseService.selectUrgeRecordId(urgeRecordUtils, SecurityUtils.getLoginUser());
        return getDataTable(createUrgeRecordIdUtils);
    }

    /**
     * 我的团队/团队资料调取申请全查以及根据字段条件查询
     *
     * @param teamRetrievalRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRetrievalRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithRetrievalRecordId(TeamRetrievalRecord teamRetrievalRecord) {
        teamRetrievalRecord.setClientName(FieldEncryptUtil.encrypt(teamRetrievalRecord.getClientName()));
        teamRetrievalRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamRetrievalRecord.getClientIdcard()));
        List<CreateRetrievalRecordUtils> createRetrievalRecordUtils = agTeamCaseService.selectRetrievalRecordId(teamRetrievalRecord, SecurityUtils.getLoginUser());
        return getDataTable(createRetrievalRecordUtils);
    }

    /**
     * 我的团队/团队资料调取申请全查以及根据字段条件查询(各状态数量)
     *
     * @param teamRetrievalRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRetrievalRecordIdNumber", method = RequestMethod.GET)
    public AjaxResult selectListWithRetrievalRecordIdNumber(TeamRetrievalRecord teamRetrievalRecord) {
        teamRetrievalRecord.setClientName(FieldEncryptUtil.encrypt(teamRetrievalRecord.getClientName()));
        teamRetrievalRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamRetrievalRecord.getClientIdcard()));

        List<Map<String, Object>> maps = agTeamCaseService.selectRetrievalRecordIdNumber(teamRetrievalRecord, SecurityUtils.getLoginUser());
        return AjaxResult.success("查询成功", maps);
    }

    /**
     * 我的团队/团队回款申请全查以及根据字段条件查询
     *
     * @param teamRepaymentRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRepaymentRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithRepaymentRecordId(TeamRepaymentRecord teamRepaymentRecord) {
        teamRepaymentRecord.setClientName(FieldEncryptUtil.encrypt(teamRepaymentRecord.getClientName()));
        teamRepaymentRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamRepaymentRecord.getClientIdcard()));
        List<CreateRepaymentRecordUtils> createRepaymentRecordUtils = agTeamCaseService.selectRepaymentRecordId(teamRepaymentRecord, SecurityUtils.getLoginUser());
        return getDataTable(createRepaymentRecordUtils);
    }

    /**
     * 我的团队/团队回款申请全查以及根据字段条件查询(各状态数量)
     *
     * @param teamRepaymentRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRepaymentRecordIdNumber", method = RequestMethod.GET)
    public AjaxResult selectListWithRepaymentRecordIdNumber(TeamRepaymentRecord teamRepaymentRecord) {
        teamRepaymentRecord.setClientName(FieldEncryptUtil.encrypt(teamRepaymentRecord.getClientName()));
        teamRepaymentRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamRepaymentRecord.getClientIdcard()));
        List<Map<String, Object>> maps = agTeamCaseService.selectRepaymentRecordIdNumber(teamRepaymentRecord, SecurityUtils.getLoginUser());
        return AjaxResult.success("查询成功", maps);
    }

    /**
     * 我的团队/团队减免申请全查以及根据字段条件查询
     *
     * @param teamReductionRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectReductionRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithReductionRecordId(TeamReductionRecord teamReductionRecord) {
        teamReductionRecord.setClientName(FieldEncryptUtil.encrypt(teamReductionRecord.getClientName()));
        teamReductionRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamReductionRecord.getClientIdcard()));
        List<CreateReductionRecordUtils> createReductionRecordUtils = agTeamCaseService.selectReductionRecordId(teamReductionRecord, SecurityUtils.getLoginUser());
        return getDataTable(createReductionRecordUtils);
    }


    /**
     * 我的团队/团队减免申请全查以及根据字段条件查询(各状态数量)
     *
     * @param teamReductionRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectReductionRecordIdNumber", method = RequestMethod.GET)
    public AjaxResult selectListWithReductionRecordIdNumber(TeamReductionRecord teamReductionRecord) {
        teamReductionRecord.setClientName(FieldEncryptUtil.encrypt(teamReductionRecord.getClientName()));
        teamReductionRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamReductionRecord.getClientIdcard()));
        List<Map<String, Object>> maps = agTeamCaseService.selectReductionRecordIdNumber(teamReductionRecord, SecurityUtils.getLoginUser());
        return AjaxResult.success("查询成功", maps);
    }

    /**
     * 我的团队/团队分期还款申请全查以及根据字段条件查询
     *
     * @param teamStagingRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectStagingRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithStagingRecordId(TeamStagingRecord teamStagingRecord) {
        teamStagingRecord.setClientName(FieldEncryptUtil.encrypt(teamStagingRecord.getClientName()));
        teamStagingRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamStagingRecord.getClientIdcard()));
        List<CreateStagingRecordUtils> createStagingRecordUtils = agTeamCaseService.selectStagingRecordId(teamStagingRecord, SecurityUtils.getLoginUser());
        return getDataTable(createStagingRecordUtils);
    }

    /**
     * 我的团队/团队分期还款申请全查以及根据字段条件查询(各状态数量)
     *
     * @param teamStagingRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectStagingRecordIdNumber", method = RequestMethod.GET)
    public AjaxResult selectListWithStagingRecordIdNumber(TeamStagingRecord teamStagingRecord) {
        teamStagingRecord.setClientName(FieldEncryptUtil.encrypt(teamStagingRecord.getClientName()));
        teamStagingRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamStagingRecord.getClientIdcard()));
        List<Map<String, Object>> maps = agTeamCaseService.selectStagingRecordIdNumber(teamStagingRecord, SecurityUtils.getLoginUser());
        return AjaxResult.success("查询成功", maps);
    }

    /**
     * 我的团队/团队停催申请全查以及根据字段条件查询
     *
     * @param teamApplyRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApplyRecordStopUrging", method = RequestMethod.GET)
    public TableDataInfo selectListWithApplyRecordStopUrging(TeamApplyRecord teamApplyRecord) {
        teamApplyRecord.setClientName(FieldEncryptUtil.encrypt(teamApplyRecord.getClientName()));
        teamApplyRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamApplyRecord.getClientIdcard()));
        teamApplyRecord.setApplyState(0);  //申请的案件状态，0-停催，1-留案，2-退案
        List<CreateApplyRecordUtils> createApplyRecordUtils = agTeamCaseService.selectApplyRecordId(teamApplyRecord, SecurityUtils.getLoginUser());
        return getDataTable(createApplyRecordUtils);
    }

    /**
     * 我的团队/团队停催申请全查以及根据字段条件查询(各状态数量)
     *
     * @param teamApplyRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApplyRecordIdStopNumber", method = RequestMethod.GET)
    public AjaxResult selectListWithApplyRecordIdStopNumber(TeamApplyRecord teamApplyRecord) {
        teamApplyRecord.setClientName(FieldEncryptUtil.encrypt(teamApplyRecord.getClientName()));
        teamApplyRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamApplyRecord.getClientIdcard()));
        teamApplyRecord.setApplyState(0);  //申请的案件状态，0-停催，1-留案，2-退案
        List<Map<String, Object>> maps = agTeamCaseService.selectApplyRecordIdNumber(teamApplyRecord, SecurityUtils.getLoginUser());
        return AjaxResult.success("查询成功", maps);
    }

    /**
     * 我的团队/团队留案申请全查以及根据字段条件查询
     *
     * @param teamApplyRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApplyRecordKeepCase", method = RequestMethod.GET)
    public TableDataInfo selectListWithApplyRecordKeepCase(TeamApplyRecord teamApplyRecord) {
        teamApplyRecord.setClientName(FieldEncryptUtil.encrypt(teamApplyRecord.getClientName()));
        teamApplyRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamApplyRecord.getClientIdcard()));
        teamApplyRecord.setApplyState(1);  //申请的案件状态，0-停催，1-留案，2-退案
        List<CreateApplyRecordUtils> createApplyRecordUtils = agTeamCaseService.selectApplyRecordId(teamApplyRecord, SecurityUtils.getLoginUser());
        return getDataTable(createApplyRecordUtils);
    }

    /**
     * 我的团队/团队留案申请全查以及根据字段条件查询(各状态数量)
     *
     * @param teamApplyRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApplyRecordIdKeepNumber", method = RequestMethod.GET)
    public AjaxResult selectListWithApplyRecordIdKeepNumber(TeamApplyRecord teamApplyRecord) {
        teamApplyRecord.setClientName(FieldEncryptUtil.encrypt(teamApplyRecord.getClientName()));
        teamApplyRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamApplyRecord.getClientIdcard()));
        teamApplyRecord.setApplyState(1);  //申请的案件状态，0-停催，1-留案，2-退案
        List<Map<String, Object>> maps = agTeamCaseService.selectApplyRecordIdNumber(teamApplyRecord, SecurityUtils.getLoginUser());
        return AjaxResult.success("查询成功", maps);
    }

    /**
     * 我的团队/团队退案申请全查以及根据字段条件查询
     *
     * @param teamApplyRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApplyRecordWithdrawal", method = RequestMethod.GET)
    public TableDataInfo selectListWithApplyRecordWithdrawal(TeamApplyRecord teamApplyRecord) {
        teamApplyRecord.setClientName(FieldEncryptUtil.encrypt(teamApplyRecord.getClientName()));
        teamApplyRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamApplyRecord.getClientIdcard()));
        teamApplyRecord.setApplyState(2);  //申请的案件状态，0-停催，1-留案，2-退案
        List<CreateApplyRecordUtils> createApplyRecordUtils = agTeamCaseService.selectApplyRecordId(teamApplyRecord, SecurityUtils.getLoginUser());
        return getDataTable(createApplyRecordUtils);
    }

    /**
     * 我的团队/团队退案申请全查以及根据字段条件查询(各状态数量)
     *
     * @param teamApplyRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApplyRecordIdWithdrawalNumber", method = RequestMethod.GET)
    public AjaxResult selectListWithApplyRecordIdWithdrawalNumber(TeamApplyRecord teamApplyRecord) {
        teamApplyRecord.setClientName(FieldEncryptUtil.encrypt(teamApplyRecord.getClientName()));
        teamApplyRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamApplyRecord.getClientIdcard()));
        teamApplyRecord.setApplyState(2);  //申请的案件状态，0-停催，1-留案，2-退案
        List<Map<String, Object>> maps = agTeamCaseService.selectApplyRecordIdNumber(teamApplyRecord, SecurityUtils.getLoginUser());
        return AjaxResult.success("查询成功", maps);
    }

    /**
     * 我的团队/团队协催申请全查以及根据字段条件查询
     *
     * @param teamAssistRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAssistRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithAssistRecordId(TeamAssistRecord teamAssistRecord) {
        teamAssistRecord.setClientName(FieldEncryptUtil.encrypt(teamAssistRecord.getClientName()));
        teamAssistRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamAssistRecord.getClientIdcard()));
        List<CreateAssistRecordUtils> createAssistRecordUtils = agTeamCaseService.selectAssistRecordId(teamAssistRecord, SecurityUtils.getLoginUser());
        return getDataTable(createAssistRecordUtils);
    }

    /**
     * 我的团队/团队外访申请全查以及根据字段条件查询
     *
     * @param teamOutsideRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectOutsideRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithOutsideRecordId(TeamOutsideRecord teamOutsideRecord) {
        teamOutsideRecord.setClientName(FieldEncryptUtil.encrypt(teamOutsideRecord.getClientName()));
        teamOutsideRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamOutsideRecord.getClientIdcard()));
        List<CreateOutsideRecordUtils> createOutsideRecordUtils = agTeamCaseService.selectOutsideRecordId(teamOutsideRecord, SecurityUtils.getLoginUser());
        return getDataTable(createOutsideRecordUtils);
    }

    /**
     * 我的团队/团队外访申请全查以及根据字段条件查询(各状态数量)
     *
     * @param teamOutsideRecord
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectOutsideRecordIdNumber", method = RequestMethod.GET)
    public AjaxResult selectListWithOutsideRecordIdNumber(TeamOutsideRecord teamOutsideRecord) {
//        teamOutsideRecord.setClientName(FieldEncryptUtil.encrypt(teamOutsideRecord.getClientName()));
//        teamOutsideRecord.setClientIdcard(FieldEncryptUtil.encrypt(teamOutsideRecord.getClientIdcard()));
        List<Map<String, Object>> maps = agTeamCaseService.selectOutsideRecordIdNumber(teamOutsideRecord, SecurityUtils.getLoginUser());
        return AjaxResult.success("查询成功", maps);
    }

    /**
     * 我的团队/团队工单全查以及根据字段条件查询
     *
     * @param teamWorkOrder
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectWorkOrder", method = RequestMethod.GET)
    public TableDataInfo selectListWithWorkOrder(TeamWorkOrder teamWorkOrder) {
//        teamWorkOrder.setClientName(FieldEncryptUtil.encrypt(teamWorkOrder.getClientName()));
//        teamWorkOrder.setClientIdcard(FieldEncryptUtil.encrypt(teamWorkOrder.getClientIdcard()));
        List<CreateWorkOrderUtils> createWorkOrderUtils = agTeamCaseService.selectWorkOrder(teamWorkOrder, SecurityUtils.getLoginUser());
        return getDataTable(createWorkOrderUtils);
    }

    /**
     * 我的团队/团队工单全查以及根据字段条件查询(各状态数量)
     *
     * @param teamWorkOrder
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectWorkOrderNumber", method = RequestMethod.GET)
    public AjaxResult selectListWithWorkOrderNumber(TeamWorkOrder teamWorkOrder) {
        teamWorkOrder.setClientName(FieldEncryptUtil.encrypt(teamWorkOrder.getClientName()));
        teamWorkOrder.setClientIdcard(FieldEncryptUtil.encrypt(teamWorkOrder.getClientIdcard()));
        List<Map<String, Object>> maps = agTeamCaseService.selectWorkOrderNumber(teamWorkOrder, SecurityUtils.getLoginUser());
        return AjaxResult.success("查询成功", maps);
    }

    /**
     * 返回团队部门列表树类型信息
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/DeptTreeType", method = RequestMethod.GET)
    public AjaxResult selectListWithDeptTreeType() {
        List<TreeType> treeTypes = agTeamCaseService.DeptTreeType();
        return AjaxResult.success("查询成功", treeTypes);
    }

    /**
     * 返回团队部门列表树类型信息-(屏蔽当前登录人信息)
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/DeptTreeTypeById", method = RequestMethod.GET)
    public AjaxResult selectListWithDeptTreeTypeById() {
        List<TreeType> treeTypes = agTeamCaseService.DeptTreeTypeById();
        return AjaxResult.success("查询成功", treeTypes);
    }

    /**
     * 指定分案写入催收员
     *
     * @param specifyRules
     * @return
     */
    @Log(title = "团队管理-指定分案", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions("dispose:teamCase:updateCase")
    @RequestMapping(value = "/updateCase", method = RequestMethod.PUT)
    public AjaxResult editCase(@RequestBody SpecifyRules specifyRules) {
        agTeamCaseService.updateCase(specifyRules);
        return AjaxResult.success("查询成功");
    }

    /**
     * 退案（批量勾选）
     *
     * @param teamCaseUtils
     * @return
     */
    @Log(title = "团队管理-申请退案", businessType = BusinessType.INSERT)
    @RequiresLogin
    //@RequiresPermissions(value = {"dispose:teamCase:insertRetreatMyPromise", "dispose:teamCase:insertRetreatTeamPromise", "dispose:teamCase:insertRetreat"}, logical = Logical.OR)
    @RequestMapping(value = "/insertRetreat", method = RequestMethod.POST)
    public AjaxResult caseWithdrawal(@RequestBody TeamCaseUtils teamCaseUtils) {
        agTeamCaseService.caseWithdrawal(teamCaseUtils, SecurityUtils.getLoginUser());
        return AjaxResult.success("添加成功");
    }

    /**
     * 留案（批量勾选）
     *
     * @param teamCaseUtils
     * @return
     */
    @Log(title = "团队管理-申请留案", businessType = BusinessType.INSERT)
    @RequiresLogin
    //@RequiresPermissions(value = {"dispose:teamCase:insertKeepMyPromise", "dispose:teamCase:insertKeepTeamPromise", "dispose:teamCase:insertKeep"}, logical = Logical.OR)
    @RequestMapping(value = "/insertKeep", method = RequestMethod.POST)
    public AjaxResult caseKeepCase(@RequestBody TeamCaseUtils teamCaseUtils) {
        agTeamCaseService.caseKeepCase(teamCaseUtils, SecurityUtils.getLoginUser());
        return AjaxResult.success("添加成功");
    }

    /**
     * 停催（批量勾选）
     *
     * @param teamCaseUtils
     * @return
     */
    @Log(title = "团队管理-申请停催", businessType = BusinessType.INSERT)
    @RequiresLogin
    //@RequiresPermissions("dispose:teamCase:insertStop")
    @RequestMapping(value = "/insertStop", method = RequestMethod.POST)
    public AjaxResult caseStopUrging(@RequestBody TeamCaseUtils teamCaseUtils) {
        agTeamCaseService.caseStopUrging(teamCaseUtils, SecurityUtils.getLoginUser());
        return AjaxResult.success("添加成功");
    }

    /**
     * 标记案件(我的团队)
     *
     * @param teamCaseUtils
     * @return
     */
    @Log(title = "团队管理-标记案件", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions("dispose:teamCase:selectMarkCase")
    @RequestMapping(value = "/selectMarkCase", method = RequestMethod.POST)
    public AjaxResult markCase(@RequestBody TeamCaseUtils teamCaseUtils) {
        agTeamCaseService.selectMarkCase(teamCaseUtils, SecurityUtils.getLoginUser());
        return AjaxResult.success("标记成功");
    }

    /**
     * 统计数据概览数据
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/getStatisticsTask", method = RequestMethod.GET)
    public AjaxResult getStatisticsTask() {
        censusAssetsService.getStatisticsTask();
        return AjaxResult.success();
    }

    /**
     * 统计数据概览数据
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/autoCensusAssets", method = RequestMethod.GET)
    public AjaxResult autoCensusAssets() {
        censusAssetsService.autoCensusAssets();
        return AjaxResult.success();
    }

    /**
     * 统计数据概览数据
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/getStatisticsEmployeeTask", method = RequestMethod.GET)
    public AjaxResult getStatisticsEmployeeTask() {
        censusAssetsService.getStatisticsEmployeeTask();
        return AjaxResult.success();
    }


    /**
     * 指定分案返回预览数据
     *
     * @param specifyRules
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/specifyDivisionalData", method = RequestMethod.POST)
    public AjaxResult specifyDivisionalData(@RequestBody SpecifyRules specifyRules) {
        ScheduleVo scheduleVo = new ScheduleVo();
        scheduleVo.setScheduleNo(IdUtil.fastSimpleUUID());
        scheduleVo.setRemarks("处理中,请稍等");
        scheduleVo.setNormal(true);
        agTeamCaseService.specifyDivisionalDataPreview(specifyRules, scheduleVo);
        return AjaxResult.success("查询成功", scheduleVo);
    }


    /**
     * 规则分案（预览分案结果）
     *
     * @param specifyRules
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/ruleSplitPreview", method = RequestMethod.POST)
    public AjaxResult ruleSplitPreview(@RequestBody SpecifyRules specifyRules) {
        System.err.println("团队分案-规则分案预览");
        ScheduleVo scheduleVo = new ScheduleVo();
        scheduleVo.setScheduleNo(IdUtils.fastSimpleUUID());
        scheduleVo.setSchedule(0);
        scheduleVo.setRemarks("处理中，请稍等");
        scheduleVo.setNormal(true);
        agTeamCaseService.tramRuleSplitPreview(specifyRules, scheduleVo);
        return AjaxResult.success(scheduleVo);
    }

    /**
     * 规则分案(写入分案记录，修改案件催收员)
     *
     * @param specifyRules
     * @return
     */
    @Log(title = "团队管理-规则分案", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions("dispose:teamCase:writeRuleDivision")
    @RequestMapping(value = "/writeRuleDivision", method = RequestMethod.POST)
    public AjaxResult writeRuleDivision(@RequestBody SpecifyRules specifyRules) {
        agTeamCaseService.writeRuleDivision(specifyRules);
        return AjaxResult.success("分案成功");
    }


    /**
     * 返回团队催记勾选字段
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectTickField", method = RequestMethod.GET)
    public AjaxResult selectTickField() {
        List<String> list = new ArrayList<>();
        ExportTeamUrgeDiaryFields[] values = ExportTeamUrgeDiaryFields.values();
        for (ExportTeamUrgeDiaryFields exportTeamUrgeDiaryFields : values) {
            String info = exportTeamUrgeDiaryFields.getInfo();
            list.add(info);
        }
        return AjaxResult.success("查询成功", list);
    }


    /**
     * 根据案件id/查询条件查询催收记录
     *
     * @param exportReminder
     * @return
     */
    @Log(title = "团队管理-导出团队催收记录", businessType = BusinessType.EXPORT)
    @RequiresLogin
    // todo 修改权限
    //@RequiresPermissions("dispose:case:selectUrgeRecord")
    @RequestMapping(value = "/selectUrgeRecord", method = RequestMethod.POST)
    public void selectUrgeRecord(HttpServletResponse response, @RequestBody @Validated ExportReminder exportReminder) throws Exception {
        if (ObjectUtils.isEmpty(exportReminder.getCreateTime1()) || ObjectUtils.isEmpty(exportReminder.getCreateTime2())) {
            throw new GlobalException("请选择催记日期");
        }
    }

    /**
     * 导出案件
     *
     * @param teamCaseUtils
     * @param response
     * @throws UnsupportedEncodingException
     */
    @Log(title = "团队案件-导出案件", businessType = BusinessType.EXPORT)
    @RequiresLogin
    @RequestMapping(value = "/exportTeamCase", method = RequestMethod.POST)
    public AjaxResult exportTeamCase(@RequestBody TeamCaseUtils teamCaseUtils, HttpServletResponse response) throws UnsupportedEncodingException {

        teamCaseUtils.setClientName(FieldEncryptUtil.encrypt(teamCaseUtils.getClientName()));
        teamCaseUtils.setClientIdcard(FieldEncryptUtil.encrypt(teamCaseUtils.getClientIdcard()));
        teamCaseUtils.setClientPhone(FieldEncryptUtil.encrypt(teamCaseUtils.getClientPhone()));
        teamCaseUtils.setLoginUser(SecurityUtils.getLoginUser());
        Object[] params = new Object[]{teamCaseUtils};
        String fileName = exportAgService.exportTask(CsExportClassEnum.CASE_EXPORT,
                DownloadTaskAgService.class, "downloadTeamCase",
                params, "团队案件导出");
        return AjaxResult.success("操作成功", fileName);

    }


    /**
     * 团队管理-导出催记
     *
     * @param param 搜索参数
     * @return
     */
    @Log(title = "团队管理-导出催记", businessType = BusinessType.EXPORT)
    @RequiresLogin
    @RequestMapping(value = "/exportUrgeRecord", method = RequestMethod.POST)
    public AjaxResult selectUrgeRecord(@RequestBody @Validated TeamExportUrgeParam param) {
        if (ObjectUtils.isEmpty(param.getCreateTime1()) || ObjectUtils.isEmpty(param.getCreateTime2())) {
            return AjaxResult.error("请选择催记日期");
        }
        TeamCaseUtils teamCaseUtils = param.getQueryParams();
        if (param.getCondition()) {
            //搜索结果
            teamCaseUtils.setCaseIds(null);
            System.out.println("搜索结果");
        } else {
            //本页
            System.out.println("本页");
            TeamCaseUtils teamCaseUtils1 = new TeamCaseUtils();
            teamCaseUtils1.setCaseIds(param.getCaseIds());
            teamCaseUtils1.setCondition(param.getCondition());
            param.setQueryParams(teamCaseUtils1);
        }

        param.getQueryParams().analysiStringDeptId();
        param.getQueryParams().setLoginUser(SecurityUtils.getLoginUser());
        Object[] params = new Object[]{param};
        String fileName = exportAgService.exportTask(CsExportClassEnum.URGE_EXPORT,
                DownloadTaskAgService.class, "downloadTeamUrgeRecord",
                params, "团队催记导出");
        return AjaxResult.success("操作成功", fileName);
    }


}
