package com.zws.appeal.controller.letterDoc.letter.controller;


import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;
import com.zws.appeal.controller.letterDoc.letter.service.ZipDownloadService;
import com.zws.common.core.web.domain.AjaxResult;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 案件压缩包下载
 * <AUTHOR>
 * @date ：Created in 2024/01/03 16:34
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("/zip")
public class ZipDownloadController {
 @Autowired
 private ZipDownloadService zipDownloadService;

    /**
     * 批量压缩下载
     */
    @PostMapping("/download")
    public AjaxResult ZipDownload(@RequestBody List<Long> ids){
        log.info("批量压缩下载");
        zipDownloadService.ZipDownload(ids);
        return AjaxResult.success("批量压缩下载");
    }

     /**
     * 保全文书压缩下载
     */
    @PostMapping("/downloadDocument")
    public AjaxResult ZipDownloadDocument(@RequestBody LetterDoc letterDoc){
        log.info("保全文书压缩下载");
        zipDownloadService.ZipDownloadDocument(letterDoc);
        return AjaxResult.success("保全文书压缩下载");
    }

    /**
     * 调价函压缩下载
     */
    @PostMapping("/downloadMediate")
    public void ZipDownloadMediate(HttpServletResponse response, @RequestBody LetterDoc letterDoc) throws Exception {
        log.info("压缩包下载");
        zipDownloadService.ZipDownloadMediate(response,letterDoc);
//        return AjaxResult.success("调价函压缩下载");
    }

//    @PostMapping("/downloadWordDocumentsMediate")
//    public void downloadWordDocumentsMediate(HttpServletResponse response, @RequestBody LetterDoc letterDoc) throws Exception {
//        log.info("word压缩包下载");
//        zipDownloadService.downloadWordDocumentsMediate(response,letterDoc);
//    }

}
