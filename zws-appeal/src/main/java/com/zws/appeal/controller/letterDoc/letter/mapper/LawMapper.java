package com.zws.appeal.controller.letterDoc.letter.mapper;

import com.zws.appeal.controller.letterDoc.law.domain.LawSign;
import com.zws.common.core.domain.Option;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 律所、签章相关接口
 * <AUTHOR>
 * @date 2024/1/14 15:02
 */
public interface LawMapper {

    /**
     * 获取律所(机构)的选项
     *
     * @return
     */
    List<Option> selectLawOptions();

    /**
     * 获取代理律师签章
     *
     * @param lawId    律所id
     * @param signType 签章类型（0-公司章，1-个人章）
     * @return
     */
//    List<Option> selectAttorneyOptions(@Param("lawId") Integer lawId, @Param("signType") Integer signType);
    List<Option> selectAttorneyOptions();

    /**
     * 获取签章图片
     *
     * @param id 主键id
     * @return
     */
    List<Option> selectSignatureOptions(@Param("id") Long id);

    /**
     * id查询律所(机构)名称
     *
     * @param lawId
     * @return
     */
    String getLawName(@Param("lawId") Integer lawId);

    /**
     * id查询代理律师名称
     *
     * @param signId
     * @return
     */
    String getAttorneyName(@Param("signId") Integer signId);

    /**
     * 获取签章识别码
     *
     * @param signId
     * @return
     */
    String getSignCode(@Param("signId") Long signId);

    /**
     *获取图片base64
     * @param id 签章主键id
     * @return
     */
    LawSign getLawSignById(@Param("id") Long id);

    int updateByPrimaryKeySelective(LawSign record);
}


