package com.zws.appeal.controller;

import com.zws.common.core.domain.AdministrativeNo;
import com.zws.common.core.utils.IDCardUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.appeal.domain.*;
import com.zws.system.api.domain.Legal;
import com.zws.appeal.pojo.AllocatedRecordUtils;
import com.zws.appeal.pojo.DictDataLinkage;
import com.zws.appeal.service.DropDownService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.Map;


/**
 * 案件下拉列表
 *
 * <AUTHOR>
 * @date 2022-08-12
 */

@RestController
@CrossOrigin
@RequestMapping(value = "/dropDown")
public class DropDownController extends BaseController {

    @Autowired
    private DropDownService dropDownService;


    /**
     * 资产方信息全查
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAssetOwner", method = RequestMethod.GET)
    public AjaxResult selectListWithAssetOwner() {
        List<AssetOwner> assetOwners = dropDownService.selectAssetOwner();
        return AjaxResult.success("查询成功", assetOwners);
    }

    /**
     * 产品信息全查
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAssetProduct", method = RequestMethod.GET)
    public AjaxResult selectListWithAssetProduct() {
        List<AssetProduct> assetProducts = dropDownService.selectAssetProduct();
        return AjaxResult.success("查询成功", assetProducts);
    }

    /**
     * 获取地区-省份
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/getProvinces", method = RequestMethod.GET)
    public AjaxResult selectListWithProvinces() throws IOException {
        //获取市级城市工具类
        List<AdministrativeNo> administrativeNos = IDCardUtils.readSource();
        return AjaxResult.success(administrativeNos);
    }

    /**
     * 跟进状态以及催收状态全查
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectDictData", method = RequestMethod.GET)
    public AjaxResult selectMapWithDictData() {
        Map<String, Object> map = dropDownService.selectDictData();
        return AjaxResult.success("查询成功", map);
    }

    /**
     * 跟进状态以及催收状态联动查询
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectDictDataLinkage", method = RequestMethod.GET)
    public AjaxResult selectListWithDictDataLinkage() {
        List<DictDataLinkage> list = dropDownService.selectDictDataLinkage();
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 联系渠道查询
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectDictDataContact", method = RequestMethod.GET)
    public AjaxResult selectListWithDictDataContact() {
        List<String> list = dropDownService.selectDictDataContact();
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 根据登录人团队id查询该团队所有员工id以及名称
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectEmployees", method = RequestMethod.GET)
    public AjaxResult selectListWithEmployees() {
        List<Employees> employees = dropDownService.selectEmployees();
        return AjaxResult.success("查询成功", employees);
    }

    /**
     * 根据登录人团队id查询该团队委案批次号
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCaseManage", method = RequestMethod.GET)
    public AjaxResult selectListWithCaseManage() {
        List<String> list = dropDownService.selectCaseManage();
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 根据登录人团队id查询该团队标签信息
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectLabel", method = RequestMethod.GET)
    public AjaxResult selectListWithLabel() {
        List<Legal> labels = dropDownService.selectLabel();
        return AjaxResult.success("查询成功", labels);
    }

    /**
     * 查询该团队的客户户籍
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRegisteredResidence", method = RequestMethod.GET)
    public AjaxResult selectListWithRegisteredResidence() {
        List<String> list = dropDownService.selectRegisteredResidence();
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 委案日志查询/按条件查询
     *
     * @param allocatedRecordUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAllocatedRecord", method = RequestMethod.GET)
    public TableDataInfo selectListWithAllocatedRecord(AllocatedRecordUtils allocatedRecordUtils) {
        startPage();
        List<AllocatedRecord> allocatedRecords = dropDownService.selectAllocatedRecord(allocatedRecordUtils);
        return getDataTable(allocatedRecords);
    }

    /**
     * 获取工单类型
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/workOrderType", method = RequestMethod.GET)
    public AjaxResult selectArrayWithWorkOrderType() {
        String[] str = new String[]{"咨询还款", "核实入账", "信用记录消除", "还款账户核实", "结清证明", "拒绝联系", "来电投诉", "其他"};
        return AjaxResult.success(str);
    }

    /**
     * 获取工单状态
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/workOrderStatus", method = RequestMethod.GET)
    public AjaxResult selectArrayWithWorkOrderStatus() {
        String[] str = new String[]{"未处理", "已处理"};
        return AjaxResult.success(str);
    }

}
