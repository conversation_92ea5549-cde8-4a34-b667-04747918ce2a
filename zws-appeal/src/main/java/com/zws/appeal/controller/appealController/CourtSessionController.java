package com.zws.appeal.controller.appealController;

import cn.hutool.core.util.URLUtil;
import cn.hutool.poi.excel.ExcelReader;
import com.zws.appeal.agservice.AgCaseService;
import com.zws.appeal.domain.appeal.CourtSession;
import com.zws.appeal.pojo.appeal.FilePojo;
import com.zws.appeal.pojo.appeal.LawInforPojo;
import com.zws.appeal.service.appeal.ICourtSessionService;
import com.zws.appeal.task.CourtSessionTask;
import com.zws.appeal.task.FilingCaseRegisterTask;
import com.zws.appeal.utils.FileUpload;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.threadpool.PoolManageMent;
import com.zws.common.core.threadpool.TaskManager;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 立案开庭
 * <AUTHOR>
 * @date 2024/6/19 18:16
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/session")
public class CourtSessionController extends BaseController {

    @Autowired
    private ICourtSessionService iCourtSessionService;
    @Autowired
    private AgCaseService agCaseService;
    @Autowired
    private RedisService redisService;

    @Autowired
    private FileUpload fileUpload;

    /**
     * 查询
     * @param lawInforPojo
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo getSessionList(LawInforPojo lawInforPojo){
        startPage();
        List<LawInforPojo> list = iCourtSessionService.getSessionList(lawInforPojo);
        return getDataTable(list);
    }


    /**
     * 预约开庭
     * @param courtSession
     * @return
     */
    @PostMapping("/insert")
    public AjaxResult insertCourtSession(@RequestBody CourtSession courtSession){
        iCourtSessionService.insertCourtSession(courtSession);
        return AjaxResult.success();
    }

    /**
     * 下载开庭立案导入模板
     * @param response
     * @throws IOException
     */
    @PostMapping("/getTemplate")
    public void getTemplate(HttpServletResponse response) throws IOException {
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("预约开庭导入模板.xlsx", "utf-8"));
        ExcelUtil<CourtSession> util = new ExcelUtil<CourtSession>(CourtSession.class);
        util.exportExcel(response, new ArrayList<>(), "预约开庭导入模板");
    }

    /**
     * 上传文件
     * @param file
     * @return
     */
    @PostMapping("/upload")
    public AjaxResult upload(MultipartFile file) throws Exception {
        MultipartFile[] files = new MultipartFile[]{file};
        String fileName = "excel";
//        将Excel文件上传到minio
        String[] list = new String[]{".xlsx", ".xls"};
        Map<String, Object> map = fileUpload.uploadFile(files, fileName, list);
        return AjaxResult.success("上传成功", map);
    }

    /**
     * 批量导入
     * @param fileUrl
     * @return
     */
    @PostMapping("/importData")
    public AjaxResult importData(@RequestBody FilePojo filePojo) throws UnsupportedEncodingException {
        Optional.ofNullable(filePojo)
                .orElseThrow(() -> new ServiceException("文件路径不存在,请重新上传！"));
        String fileUrl = filePojo.getFileUrl();
        if (StringUtils.isEmpty(fileUrl)){ return AjaxResult.error("文件路径不存在,请重新上传"); }

        List<CourtSession> list1 = null;

        String encode = URLUtil.encode(fileUrl);
        try (InputStream inputStream = URLUtil.url(encode).openStream()){
            ExcelUtil<CourtSession> util = new ExcelUtil<CourtSession>(CourtSession.class);
            list1 = util.importExcel(URLUtil.url(encode).openStream());
            if (ObjectUtils.isEmpty(list1)) {
                return AjaxResult.error("文件信息为空,请重新编辑后上传");
            }

            ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(inputStream);
            List<Map<String, Object>> readAll = reader.read(0, 1,1);
            Map<String, Object> map = readAll.get(0);
            List<String> list = getExeclHeader();
            int size = map.size();
            if (size != list.size()) {
                return AjaxResult.error("请下载正确模板");
            }
            for (String key : map.keySet()) {
                if (!list.contains(key)) {
                    return AjaxResult.error("请下载正确模板");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        CourtSessionTask workTask = new CourtSessionTask(loginUser,list1,filePojo);
        TaskManager.addTask(workTask);
        return AjaxResult.success();
    }

    /**
     * 批量登记导入表头
     * @return
     */
    public static List<String> getExeclHeader(){
        List<String> list = new ArrayList<>(12);
        list.add("案件ID");
        list.add("开庭律师");
        list.add("承办律师");
        list.add("开庭时间");
        list.add("开庭城市");
        list.add("开庭法院");
        list.add("应到处所");
        list.add("补贴金额");
        list.add("开庭方式");
        list.add("服务内容");
        list.add("开庭次数");
        list.add("备注");
        return list;
    }

}
