package com.zws.appeal.controller.letterDoc.letter.service.impl;

import cn.hutool.core.util.StrUtil;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.Option;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterVariable;
import com.zws.appeal.controller.letterDoc.letter.mapper.LetterVariableMapper;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterVariableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 函件变量 service
 * <AUTHOR>
 * @date ：Created in 2023/11/23 20:42
 */
@Slf4j
@Service
public class LetterVariableServiceImpl implements ILetterVariableService {


    @Autowired
    private LetterVariableMapper baseMapper;

    @Override
    public Long insert(LetterVariable record) {
        if (!checkUniqueName(record)){
            throw new ServiceException("参数名称已被使用，请换一个");
        }
        record.setId(null);
        record.setCreateBy(SecurityUtils.getUsername());
        record.setCreateById(SecurityUtils.getUserId());
        record.setCreateTime(new Date());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        record.setTeamId(SecurityUtils.getTeamId());
        baseMapper.insert(record);
        return record.getId();
    }

    @Override
    public void updateById(LetterVariable record) {
        if (!checkUniqueName(record)){
            throw new ServiceException("参数名称已被使用，请换一个");
        }
        record.setCreateBy(null);
        record.setCreateById(null);
        record.setCreateTime(null);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        this.baseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void deleteById(Long id) {
        LetterVariable record=new LetterVariable();
        record.setId(id);
        record.setDelFlag(BaseConstant.DelFlag_Being);
        this.updateById(record);
    }

    @Override
    public LetterVariable getById(Long id) {
        return this.baseMapper.selectByPrimaryKey(id);
    }

    @Override
    public boolean checkUniqueName(LetterVariable variable) {
        if (variable.getVariableName()==null){
            return true;
        }
        LetterVariable temp=new LetterVariable();
        temp.setVariableName(variable.getVariableName());
        temp.setTeamId(SecurityUtils.getTeamId());
        List<LetterVariable> letterVariables = this.selectList(temp);
        if (letterVariables.size()>1){
            return false;
        }
        if (letterVariables.size()>0 && !letterVariables.get(0).getId().equals(variable.getId())){
            return  false;
        }
        return true;
    }

    @Override
    public List<LetterVariable> selectList(LetterVariable record) {
        record.setTeamId(SecurityUtils.getTeamId());
        return this.baseMapper.selectList(record);
    }

    @Override
    public List<Option> getVariableNameVoList() {
        LetterVariable variable=new LetterVariable();
//        variable.setStatus(BaseConstant.STATUS_OPEN);
        variable.setTeamId(SecurityUtils.getTeamId());
        List<LetterVariable> list= selectList(variable);
        List<Option> options=new ArrayList<>();
        for (LetterVariable temp:list) {
            options.add(new Option(temp.getVariableName(), StrUtil.format("[{}]",temp.getVariableName())));
        }
        return options;
    }

    @Override
    public List<Option> getOpenOptions() {
        LetterVariable variable=new LetterVariable();
        variable.setStatus(BaseConstant.STATUS_OPEN);
        variable.setTeamId(SecurityUtils.getTeamId());
        List<LetterVariable> list= selectList(variable);
        List<Option> options=new ArrayList<>();
        for (LetterVariable temp:list) {
            options.add(new Option(temp.getVariableName(), StrUtil.format("[{}]",temp.getVariableName())));
        }
        return options;
    }
}
