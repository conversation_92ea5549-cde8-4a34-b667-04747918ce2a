package com.zws.appeal.controller.letterDoc.letter.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zws.appeal.constant.LetterConstants;
import com.zws.appeal.controller.letterDoc.law.enums.ApproveStatusEnum;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterTemplateService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.R;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.DataMaskingUtils;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.pdf.PdfUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;
import com.zws.appeal.controller.letterDoc.letter.domain.RetrievalFileSeparate;
import com.zws.appeal.controller.letterDoc.letter.mapper.LetterDocMapper;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterDocService;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.domain.SysFile;
import com.zws.system.api.model.LoginUser;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Date;
import java.util.List;

/**
 * 函件-文书
 * <AUTHOR>
 * @date ：Created in 2023/12/12 16:37
 */
@Slf4j
@Service
public class LetterDocServiceImpl implements ILetterDocService {

    @Autowired
    private LetterDocMapper baseMapper;
    @Autowired
    private ILetterTemplateService letterTemplateService;
    @Autowired
    private RemoteFileService remoteFileService;


    @Override
    public List<Integer> selectLetterIdByIds(List<Long> ids) {
        return this.baseMapper.selectLetterIdByIds(ids);
    }

    @Override
    public LetterDoc selectByRandomId(String randomId) {
        return baseMapper.selectByRandomId(randomId);
    }

    @Override
    public List<LetterDoc> selectSimpleList(LetterDoc document) {
        if (StringUtils.isNotEmpty(document.getSerialNo())) {
            if (LetterConstants.likeArr.contains(document.getSerialNo())) {
                document.setSerialNo("//" + document.getSerialNo());
            }
        }
        document.setTeamId(TokenInformation.getCreateid().longValue());
        return this.baseMapper.selectSimpleList(document);
    }
    @Override
    public String exportSignPreviewUrl(HttpServletResponse response, List<LetterDoc> letters,LoginUser loginUser) {
       /* if (letters.size()==0){
            throw  new ServiceException("没有可导出的律师函,请等待审批通过");
        }*/
        //机构ID
        Integer createId = TokenInformation.getCreateid(loginUser);
        String folder = UUID.fastUUID().toString();
        FileUtil.mkdir(FileDownloadUtils.tempFilePath + folder);

        File zipFile = null;
        String fileUrl = null;
        try {
            for (LetterDoc let : letters) {
                //广州银城派互联网科技有限公司(正式环境id：34) 跟 广州金信投资服务有限公司正式环境id：35) 特殊处理文件名称
                String fileName = "";
                if (createId == 34 || createId == 351 || 1 == 1) {
                    //上传函件时候的年月日+变量里面的合同号，如果没有合同号就是乱码
                    String timeStr = DateUtils.parseDateToStr("yyyy-MM-dd", let.getCreateTime());
                    JSONObject jsonObject = JSONUtil.parseObj(let.getItemData());
                    String hth = jsonObject.getStr("合同号", UUID.fastUUID().toString().substring(0, 6));
                    fileName = StrUtil.format("{}_{}", timeStr, hth);
                }
                System.out.println("fileName:" + fileName);
//                String url = let.getPreviewUrl();
                String url = let.getSignPreviewUrl();
                if(ObjectUtils.isEmpty(url)){
                    url = let.getPreviewUrl();
                }
//                if (let.getStatus() == ApproveStatusEnum.PASS.getCode()) {
//                    //审核通过、已签章之后是下载签章文件
//                    url = let.getSignPreviewUrl();
//                }

                FileDownloadUtils.downloadTempFile(url, fileName, folder);
            }

            System.err.println("下载完成，开始压缩");
            if (ObjectUtils.isEmpty(letters) || letters.isEmpty()) {
                FileUtil.mkdir(FileDownloadUtils.tempFilePath + folder + "/" + "通过数量为空");
            }
            zipFile = FileDownloadUtils.zipFolder(folder);

            fileUrl = "";

            if (response != null) {
                try {
                    FileInputStream istram = new FileInputStream(zipFile);
                    OutputStream outputStream = response.getOutputStream();
                    int read;
                    while ((read = istram.read()) != -1) {
                        outputStream.write(read);
                    }
                    outputStream.flush();
                } catch (Exception e) {
                    log.error("导出函件异常", e);
                    throw new ServiceException("导出函件异常,请联系管理员");
                }
            } else {
                //上传文件服务
                fileUrl = uploadUtils(zipFile);
            }
        } catch (ServiceException e) {
            log.error("生成文书压缩包异常："+e.getMessage());
            throw new ServiceException(e.getMessage());
        } finally {
            //清空临时文件夹
            FileDownloadUtils.deleteFileUtils(FileDownloadUtils.tempFilePath + folder);
            //删除压缩包文件
            FileDownloadUtils.deletedTempFile(zipFile);
        }
        return fileUrl;
    }

    public String uploadUtils(File file) {
        String url = null;
        try {
            System.err.println("文件路径：" + file.getAbsolutePath());

            FileInputStream inputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                    ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
            R<SysFile> r = remoteFileService.upload(multipartFile);
            if (r.getCode() == R.SUCCESS) {
                //pdfFile.setUrl(r.getData().getUrl());
                url = r.getData().getUrl();
            } else {
                throw new ServiceException(r.getMsg());
            }
        } catch (FeignException e) {
            log.error("文件上传失败,相关服务器未启动", e);
            throw new ServiceException("文件上传失败，相关服务器未启动，请联系管理员");
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new ServiceException("文件上传失败，请联系管理员");
        }
        return url;
    }

    @Override
    public int save(LetterDoc document,LoginUser loginUser) {
        document.setCreateBy(TokenInformation.getUsername(loginUser));
        document.setCreateById(TokenInformation.getUserid(loginUser).longValue());
        document.setCreateTime(DateUtils.getNowDate());
        document.setDelFlag(BaseConstant.DelFlag_Being);
        document.setTeamId(TokenInformation.getCreateid(loginUser).longValue());
        document.setUpdateBy(TokenInformation.getUsername(loginUser));
        document.setUpdateById(TokenInformation.getCreateid(loginUser).longValue());
        document.setUpdateTime(DateUtils.getNowDate());
        return this.baseMapper.insert(document);
    }

    @Override
    public Long insert(LetterDoc record) {
        record.setId(null);
        record.setCreateBy(SecurityUtils.getUsername());
        record.setCreateById(SecurityUtils.getUserId());
        record.setCreateTime(new Date());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        record.setTeamId(SecurityUtils.getTeamId());
        baseMapper.insert(record);
        return record.getId();
    }

    @Override
    public void updateById(LetterDoc record) {
        record.setCreateBy(null);
        record.setCreateById(null);
        record.setCreateTime(null);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        this.baseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void deleteById(Long id) {
        LetterDoc record=new LetterDoc();
        record.setId(id);
        record.setDelFlag(BaseConstant.DelFlag_Being);
        this.updateById(record);
    }

    @Override
    public LetterDoc getById(Long id) {
        return this.baseMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<LetterDoc> selectList(LetterDoc record, LoginUser loginUser) {
        record.setTeamId(TokenInformation.getCreateid(loginUser).longValue());
        return this.baseMapper.selectList(record);
    }


    @Override
    public List<LetterDoc> getLetterDoc(Long caseId, Integer code) {
        return  this.baseMapper.getLetterDoc(caseId,code);
    }

    @Override
    public List<LetterDoc> getLetterDocs(Long caseId, Integer code) {
        return  this.baseMapper.getLetterDocs(caseId,code);
    }

    @Override
    public String selectDataManagementByCaseId(Long caseId) {
        return this.baseMapper.selectDataManagementByCaseId(caseId);
    }

    @Override
    public List<RetrievalFileSeparate> getArchivalData(String clientIdcard) {
        return this.baseMapper.getArchivalData(clientIdcard);
    }

    @Override
    public LetterDoc selectByPreviewUrl(String previewUrl) {
        return this.baseMapper.selectByPreviewUrl(previewUrl);
    }

    @Override
    public String createPreview(LetterDoc document, Integer templateId, boolean isHideInfo) {
        if (templateId == null) {
            templateId = getTemplateId(document.getId().intValue());
        }

        //模板内容
        LetterTemplate letterTemplate = letterTemplateService.getById(Long.valueOf(templateId));
        String content = letterTemplateService.getContent(letterTemplate, false);
        String data = document.getItemData();
        JSONObject jsonObject = JSONUtil.parseObj(data);
        List<String> variables = letterTemplateService.getTemplateVariable(templateId);
        for (String variable : variables) {
            String value = jsonObject.getStr(variable);
            if (value == null) {
                continue;
            }
            //判断是否需要脱敏
            if (isHideInfo) {
                if (variable.indexOf("姓名") > -1) {
                    value = DataMaskingUtils.nameMasking(value);
                } else if (variable.indexOf("电话") > -1 || variable.indexOf("手机") > -1) {
                    value = DataMaskingUtils.phoneMasking(value);
                } else if (variable.indexOf("身份证") > -1 || variable.indexOf("证件") > -1) {
                    value = DataMaskingUtils.idMasking(value);
                }
            }

            content = content.replace(StrUtil.format("[{}]", variable), value);
        }
        //页眉
        if (StringUtils.isNotEmpty(letterTemplate.getPageHeader()) && StringUtils.isNotEmpty(letterTemplate.getScaleHeader())) {
            float height = PdfUtils.getImageHeight(letterTemplate.getScaleHeader());
            //如果过大则进行裁剪方法;否则不进行更改尺寸
            if (height > 166.7) {
                String cutPicture = letterTemplateService.cutPictureByStream(letterTemplate.getScaleHeader(), null);
                letterTemplate.setCutHeader(cutPicture);
            } else {
                letterTemplate.setCutHeader(letterTemplate.getPageHeader());
            }

        } else if (StringUtils.isEmpty(letterTemplate.getPageHeader())) {
            letterTemplate.setCutFooter("");
        }
        //页脚
        if (StringUtils.isNotEmpty(letterTemplate.getPageFooter()) && StringUtils.isNotEmpty(letterTemplate.getScaleFooter())) {
            float height = PdfUtils.getImageHeight(letterTemplate.getScaleFooter());
            if (height > 100) {
                String cutPicture = letterTemplateService.cutPictureByStream(null, letterTemplate.getScaleFooter());
                letterTemplate.setCutFooter(cutPicture);
            } else {
                letterTemplate.setCutFooter(letterTemplate.getPageFooter());
            }
        } else if (StringUtils.isEmpty(letterTemplate.getPageFooter())) {
            letterTemplate.setCutFooter("");
        }

        File pdfFile = PdfUtils.createHtmlPdf(content, letterTemplate.getCutHeader(), letterTemplate.getCutFooter());

        try {
            System.err.println("PDF文件路径：" + pdfFile.getAbsolutePath());
            FileInputStream inputStream = new FileInputStream(pdfFile);
            MultipartFile multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(),
                    ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
            R<SysFile> r = remoteFileService.upload(multipartFile);
            if (r.getCode() == R.SUCCESS) {
                return r.getData().getUrl();
            } else {
                throw new ServiceException(r.getMsg());
            }
        } catch (Exception e) {
            log.error("生成预览失败", e);
            throw new ServiceException("生成预览失败，请联系管理员");
        } finally {
            try {
                if (pdfFile != null) {
                    Path path = pdfFile.toPath();
                    Files.deleteIfExists(path);
                }
            } catch (IOException e) {
                log.error("删除临时pdf文件失败" + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    @Override
    public Integer getTemplateId(Integer id) {
        return this.baseMapper.getTemplateIdById(id.longValue());
    }

}
