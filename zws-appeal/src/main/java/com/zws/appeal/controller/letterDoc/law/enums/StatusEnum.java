package com.zws.appeal.controller.letterDoc.law.enums;


import com.zws.common.core.domain.Option;

import java.util.ArrayList;
import java.util.List;

/**
 * 状态枚举
 * <AUTHOR>
 * @date ：Created in 2022/12/23 16:50
 */
public enum StatusEnum {

    /**
     * 0-启用
     */
    NORMAL(0,"启用"),
    /**
     * 1-禁用
     */
    DISABLED(1,"禁用"),

    /**
     * 律函列表导入状态-导入中
     */
    IMPORT_ING(0,"导入中"),
    /**
     * 律函列表导入状态-导入失败
     */
    IMPORT_FAIL(1,"导入失败"),
    /**
     * 律函列表导入状态-导入成功
     */
    IMPORT_SUCCESS(2,"导入成功")
    ;

    private int code;
    private String info;

    StatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    public static List<Option> getOptions(){
        StatusEnum[] values= StatusEnum.values();
        List<Option> options=new ArrayList<>();
        for (StatusEnum temp: values) {
            if (temp.getInfo().equals("启用") || temp.getInfo().equals("禁用")){
                options.add(new Option(temp.code,temp.info));
            }
        }
        return options;
    }

    public int getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
