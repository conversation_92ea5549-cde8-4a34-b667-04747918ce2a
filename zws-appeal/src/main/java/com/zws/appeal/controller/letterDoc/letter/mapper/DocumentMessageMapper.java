package com.zws.appeal.controller.letterDoc.letter.mapper;

import com.zws.appeal.controller.letterDoc.letter.domain.DocumentMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 律函批次 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-04 20:38
 */
@Mapper
public interface DocumentMessageMapper {

    /**
     * 查询列表
     * @param message
     * @return
     */
    List<DocumentMessage> selectList(DocumentMessage message);

    /**
     * 查询导出压缩文件
     * @param ids
     * @return
     */
    List<DocumentMessage> selectExportZip(@Param("ids") List<Integer> ids);
    /**
     * 查询用户审批过的函件批次ID
     * @param message
     * @return
     */
    List<Integer> selectedApproveProceLetterId(DocumentMessage message);

    /**
     * 统计函件批次的函件审批状态
     * @param id 函件批次id
     * @return key-proce(审批状态)，value-num(数量)
     */
    List<Map<String,Object>>  countLetterProce(Integer id);

    /**
     * 查询函件批次总函件量
     * @param id
     * @return
     */
    long selectCount(Integer id);

    int deleteByPrimaryKey(Integer id);

    int insert(DocumentMessage record);

    DocumentMessage selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DocumentMessage record);

    int updateByPrimaryKey(DocumentMessage record);
}
