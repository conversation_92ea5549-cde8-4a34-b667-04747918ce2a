package com.zws.appeal.controller.letterDoc.letter.service;

import com.zws.common.core.domain.Option;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterVariable;

import java.util.List;

/**
 * 函件变量
 * <AUTHOR>
 * @date ：Created in 2023/11/23 20:25
 */
public interface ILetterVariableService {

    Long insert(LetterVariable record);

    void updateById(LetterVariable record);

    void deleteById(Long id);

    LetterVariable getById(Long id);

    /**
     * 检查名称是否唯一
     * @param variable
     * @return true-唯一，false-已被使用
     */
    boolean checkUniqueName(LetterVariable variable);

    /**
     * 查询列表
     * @param record
     * @return
     */
    List<LetterVariable> selectList(LetterVariable record);

    /**
     * 获取所有变量 在模板中的 显示文本
     *  客户名称  在模板中显示为 [客户名称]
     * code 值为 变量名 比如：客户名称
     * info 值为 在模板中显示的文本 比如：[客户名称]
     * @return
     */
    List<Option> getVariableNameVoList();

    List<Option> getOpenOptions();
}
