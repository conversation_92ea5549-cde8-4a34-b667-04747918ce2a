package com.zws.appeal.controller.letterDoc.letter.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 资料调取存储解压后单个文件信息表
 *
 * @Author: 马博新
 * @DATE: Created in 2023/5/23 15:15
 */
@Data
public class RetrievalFileSeparate implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 案件ID
     */
    private Long caseId;

    /**
     * 文件信息id（存放压缩包信息表主键id）
     */
    private Long fileId;

    /**
     * 文件起初名字
     */
    private String firstName;

    /**
     * 文件修改后的名字
     */
    private String modifyName;

    /**
     * 文件url地址
     */
    private String fileUrl;

    /**
     * 文件ip地址
     */
    private String fileIp;

    /**
     * 文件端口
     */
    private String filePort;

    /**
     * 文件路径
     */
    private String fileRoute;

    /**
     * 文件过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fileExpirationTime;

    /**
     * 创建人
     */
    private String founder;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creationTime;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 修改人
     */
    private String modifiedBy;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifiedTime;

    /**
     * 是否为最外层(1-最外层，0-子文件)
     */
    private Integer outermostLayer;

    /**
     * 勾选标识(非数据库字段) 0勾选  1未勾选
     */
    private Integer tickSign;

    /**
     * 客户身份证号码
     */
    private String clientIdcard;
}
