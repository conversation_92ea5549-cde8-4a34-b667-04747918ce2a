package com.zws.appeal.controller.letterDoc.law.domain;

import lombok.Data;

import java.util.Date;

/**
 * 团队签章表
 * <AUTHOR>
 * @date 2023年11月22日21:45:51
 */
@Data
public class LawTeamSign {

    private Long id;
    /**
     *  团队id
     */
    private Integer teamId;
    /**
     * 签章id
     */
    private Long lawSignId;

    private Date createTime;

    private Long createById;

    private String createBy;

    private String updateBy;

    private Long updateById;

    private Date updateTime;

    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getTeamId() {
        return teamId;
    }

    public void setTeamId(Integer teamId) {
        this.teamId = teamId;
    }

    public Long getLawSignId() {
        return lawSignId;
    }

    public void setLawSignId(Long lawSignId) {
        this.lawSignId = lawSignId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateById() {
        return createById;
    }

    public void setCreateById(Long createById) {
        this.createById = createById;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    public Long getUpdateById() {
        return updateById;
    }

    public void setUpdateById(Long updateById) {
        this.updateById = updateById;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag == null ? null : delFlag.trim();
    }
}
