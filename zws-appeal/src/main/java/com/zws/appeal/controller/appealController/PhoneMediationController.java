package com.zws.appeal.controller.appealController;

import cn.hutool.core.bean.BeanUtil;
import com.zws.appeal.agservice.AgCaseService;
import com.zws.appeal.domain.MessageTemplatePojo;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.pojo.SendRecordsPojos;
import com.zws.appeal.pojo.appeal.PhoneMediationPojo;
import com.zws.appeal.service.CaseService;
import com.zws.appeal.service.appeal.IPhoneMediationService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * 诉前调解
 * <AUTHOR>
 * @date 2024/6/18 13:54
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/phone")
public class PhoneMediationController extends BaseController {

    @Autowired
    private IPhoneMediationService iPhoneMediationService;
    @Autowired
    private CaseService caseService;
    @Autowired
    private AgCaseService agCaseService;
    @Autowired
    private RedisService redisService;

    /**
     * 查询
     * @param phoneMediationPojo
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo getPhoneMediationList(PhoneMediationPojo phoneMediationPojo){
        startPage();
        List<PhoneMediationPojo> list = iPhoneMediationService.getPhoneMediation(phoneMediationPojo);
        return getDataTable(list);
    }

    /**
     * 添加调节号
     * @param phoneMediationPojo
     * @return
     */
    @PostMapping("/addNum")
    public AjaxResult addNum(@RequestBody PhoneMediationPojo phoneMediationPojo){
        iPhoneMediationService.addNum(phoneMediationPojo);
        return AjaxResult.success();
    }

    /**
     * 添加司法公示链接
     * @param phoneMediationPojo
     * @return
     */
    @PostMapping("/addLink")
    public AjaxResult addLink(@RequestBody PhoneMediationPojo phoneMediationPojo){
        iPhoneMediationService.addLink(phoneMediationPojo);
        return AjaxResult.success();
    }

    /**
     * 查询短信模板下拉框
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAllSmsTemplate", method = RequestMethod.GET)
    public AjaxResult selectAllSmsTemplate() {
        return AjaxResult.success("查询成功", caseService.selectAllSmsTemplate());
    }

    /**
     * 批量发送生成短信模板短信
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/previewTemplate", method = RequestMethod.POST)
    public AjaxResult previewTemplate(@RequestBody Object pojo) {
        Map<String, Object> map = BeanUtil.beanToMap(pojo);
        SendRecordsPojos sendRecordsPojos = agCaseService.previewTemplate(map);
        return AjaxResult.success("查询成功", sendRecordsPojos);
    }

    /**
     * 批量发送短信
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/sendAppealMessages", method = RequestMethod.POST)
    public AjaxResult sendAppealMessages(@RequestBody  Object pojo) throws Exception {
        String key = CacheConstants.BATCH_SEND_MESSAGE + TokenInformation.getUserid();
        if (redisService.hasKey(key)) {
            throw new GlobalException("短信后台发送中，请勿重复提交");
        }
        redisService.setCacheObject(key, true, 30L, TimeUnit.MINUTES);
        ThreadEntityPojo threadEntityPojo = new ThreadEntityPojo();
        threadEntityPojo.setType(TokenInformation.getType());
        threadEntityPojo.setUser(TokenInformation.getUsername());
        threadEntityPojo.setUserId(TokenInformation.getUserid());
        threadEntityPojo.setCreateId(TokenInformation.getCreateid());
        threadEntityPojo.setTeamLevelType(TokenInformation.getTeamLevelType());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Map<String, Object> map = BeanUtil.beanToMap(pojo);
        agCaseService.sendCwMessage(map, threadEntityPojo, loginUser);
        return AjaxResult.success("短信提交发送成功");
    }

    /**
     * 调诉端单条/案件详情 联系人发送短信
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/sendAppealMessage", method = RequestMethod.POST)
    public AjaxResult sendAppealMessage(@RequestBody Object pojo) throws Exception {
        Map<String, Object> map = BeanUtil.beanToMap(pojo);
        agCaseService.sendCwuMessage(map);
        return AjaxResult.success("短信提交发送成功");
    }



}
