package com.zws.appeal.controller.letterDoc.letter.enums;


/**
 * 文书模板
 * <AUTHOR>
 * @date ：Created in 2023/12/12 22:53
 */
public enum DocTypes {
    //0-诉讼文书，1-调解函，2-保全文书
    /**
     * 0-诉讼文书
     */
    LITIGATION_DOC(0,"诉讼文书"),
    /**
     * 1-调解函
     */
    MEDIATION_LETTER(1,"调解函"),
    /**
     * 2-保全文书
     */
    KEEP_INTACT_DOC(2,"保全文书"),

    ;

    private final Integer code;
    private final String info;

    DocTypes(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static DocTypes valueOfCode(Integer code) {
        if (code==null) {
            return null;
        }
        DocTypes[] fields = DocTypes.values();
        for (DocTypes temp : fields) {
            if (temp.code.equals(code)) {
                return temp;
            }
        }
        return null;
    }


}
