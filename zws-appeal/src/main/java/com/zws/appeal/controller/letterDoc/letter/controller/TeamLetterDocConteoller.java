package com.zws.appeal.controller.letterDoc.letter.controller;

import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.redis.service.RedisService;
import com.zws.appeal.controller.letterDoc.letter.agservice.LetterDocAgService;
import com.zws.appeal.controller.letterDoc.letter.agservice.LetterTemplateAgService;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;
import com.zws.appeal.controller.letterDoc.letter.domain.RetrievalFileSeparate;
import com.zws.appeal.controller.letterDoc.letter.enums.DocTypes;
import com.zws.appeal.controller.letterDoc.letter.pojo.BatchDoc;
import com.zws.appeal.controller.letterDoc.letter.pojo.SaveDoc;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterDocService;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 团队文书管理
 * <AUTHOR>
 * @date ：Created in 2023/12/12 17:50
 */
@CrossOrigin
@RestController
@RequestMapping("/team/letter/doc")
public class TeamLetterDocConteoller extends BaseController {

    @Autowired
    private ILetterDocService docService;
    @Autowired
    private LetterTemplateAgService templateAgService;
    @Autowired
    private LetterDocAgService docAgService;
    @Autowired
    private RedisService redisService;

    /**
     * 列表
     * @param doc
     * @return
     */
    @GetMapping("/list")
    private TableDataInfo list(LetterDoc doc){
        List<LetterDoc> letterDocs = docService.selectList(doc, SecurityUtils.getLoginUser());
        return getDataTable(letterDocs);
    }

    /**
     * 预览文书
     * @param batchDoc
     * @return
     */
    @PostMapping("/previewDoc")
    public AjaxResult previewDoc(@RequestBody @Validated BatchDoc batchDoc){
        SaveDoc saveDoc = docAgService.previewDoc(batchDoc);
        return AjaxResult.success(saveDoc);
    }

    /**
     * 批量生成文书
     * @param saveDoc
     * @return
     */
    @PostMapping("/batchAddLitigationDoc")
    public AjaxResult batchAddLitigationDoc(@RequestBody SaveDoc saveDoc){
        docAgService.batchSaveDoc(saveDoc,DocTypes.LITIGATION_DOC);
        return AjaxResult.success();
    }

    /**
     * 批扣生成调解函
     * @param saveDoc
     * @return
     */
    @PostMapping("/batchAddMediationLetter")
    public AjaxResult batchAddMediationLetter(@RequestBody SaveDoc saveDoc){
        docAgService.batchSaveDoc(saveDoc,DocTypes.MEDIATION_LETTER);
//        docAgService.batchSaveDoc(saveDoc,DocTypes.MEDIATION_LETTER);
        return AjaxResult.success();
    }

    /**
     * 批扣生成保全材料
     * @param saveDoc
     * @return
     */
    @PostMapping("/batchAddKeepIntactDoc")
    public AjaxResult batchAddKeepIntactDoc(@RequestBody SaveDoc saveDoc){
        docAgService.batchSaveDoc(saveDoc,DocTypes.KEEP_INTACT_DOC);
        return AjaxResult.success();
    }


    /**
     * 查询 保全文书记录
     * @param caseId
     * @return
     */
    @GetMapping("/getLitigationDoc")
    public TableDataInfo getLitigationDoc(@RequestParam("caseId") Long caseId){
        startPage();
//        List<LetterDoc> docs = docAgService.getLetterDoc(caseId,DocTypes.LITIGATION_DOC);
        List<LetterDoc> docs = docAgService.getLetterDocs(caseId,DocTypes.LITIGATION_DOC);
        return getDataTable(docs);
    }

    /**
     * 查询 记录保全材料记录
     * @param caseId
     * @return
     */
    @GetMapping("/getKeepIntactDoc")
    public TableDataInfo getKeepIntactDoc(@RequestParam("caseId") Long caseId){
        startPage();
        List<LetterDoc> docs = docAgService.getLetterDoc(caseId,DocTypes.KEEP_INTACT_DOC);
        return getDataTable(docs);
    }

    /**
     * 查询证据材料（资产端(档案查询) 催收端可以查询）
     * 获取档案资料列表
     *
     * @param caseId
     * @return
     */
    @GetMapping("/retrieval/getArchivalList")
    public TableDataInfo getArchivalData(@RequestParam("caseId") Long caseId) {
        startPage();
        List<RetrievalFileSeparate> list = docAgService.getArchivalData(caseId);
        return getDataTable(list);
    }

    /**
     * 批量下载
     */
    @PostMapping("/downloadMediate")
    public void downloadMediate(HttpServletResponse response,@RequestBody List<String> urls) throws Exception {
        docAgService.ZipDownloadMediate(response,urls);
    }


}
