package com.zws.appeal.controller.letterDoc.letter.controller;

import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.Option;
import com.zws.common.core.domain.SelectTreeVo;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterClassify;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterClassifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 团队文书业务分类
 * <AUTHOR>
 * @date ：Created in 2023/11/23 20:45
 */
@CrossOrigin
@RestController
@RequestMapping("document/template/classify")
public class TeamLetterClassifyController extends BaseController {

    @Autowired
    private ILetterClassifyService letterClassifyService;



    /**
     * 分页列表
     * @param classify
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo list(LetterClassify classify){
        startPage();
        List<LetterClassify> classifyList=letterClassifyService.selectList(classify);
        return getDataTable(classifyList);
    }

    /**
     * 创建、新增类型
     * @param classify
     * @return
     */
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody LetterClassify classify){
        letterClassifyService.insert(classify);
        return AjaxResult.success();
    }

    /**
     * 编辑类型
     * @param classify
     * @return
     */
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody LetterClassify classify){
        letterClassifyService.updateById(classify);
        return AjaxResult.success();
    }

    /**
     * 编辑状态
     * @param classify
     * @return
     */
    @PostMapping("/editStatus")
    public AjaxResult editStatus(@Validated @RequestBody LetterClassify classify){
        LetterClassify entity=new LetterClassify();
        entity.setStatus(classify.getStatus());
        entity.setId(classify.getId());
        letterClassifyService.updateById(entity);
        return AjaxResult.success();
    }

    /**
     * 获取全部 启用的模板类型
     * @return
     */
    @GetMapping("/getAll")
    public AjaxResult getAll(){
        LetterClassify classify=new LetterClassify();
        classify.setStatus(BaseConstant.STATUS_OPEN);
        classify.setTeamId(SecurityUtils.getTeamId());
        List<LetterClassify> list= this.letterClassifyService.selectList(classify);
        return AjaxResult.success(list);
    }

    /**
     * 获取选择树结构 的模板类型
     * @return
     */
    @GetMapping("/getSelectTree")
    public AjaxResult getSelectTree(){
        LetterClassify classify=new LetterClassify();
        classify.setStatus(BaseConstant.STATUS_OPEN);
        List<LetterClassify> list= this.letterClassifyService.selectList(classify);
        List<SelectTreeVo> vos=new ArrayList<>();

        for (LetterClassify temp:list) {
            SelectTreeVo vo=new SelectTreeVo();
            vo.setValue(temp.getClassifyName());
            vo.setLabel(temp.getClassifyName());
            List<SelectTreeVo> childrens =new ArrayList<>();
            if (StringUtils.isNotEmpty(temp.getClassifyLabel())){
                String[] labels=  temp.getClassifyLabel().split(";");
                for (String label:labels) {
                    childrens.add(new SelectTreeVo(label));
                }
            }
            vo.setChildren(childrens);
            vos.add(vo);
        }
        return AjaxResult.success(vos);
    }

    /**
     * 获取模板类型选项
     * @return
     */
    @GetMapping("/getOptions")
    public AjaxResult getOptions(){
        LetterClassify classify=new LetterClassify();
//        classify.setStatus(BaseConstant.STATUS_OPEN);
        List<LetterClassify> list= this.letterClassifyService.selectList(classify);
        List<Option> options=new ArrayList<>();
        for (LetterClassify temp: list) {
            options.add(new Option(temp.getId(),temp.getClassifyName()));
        }
        return AjaxResult.success(options);
    }

    /**
     * 获取开启模板类型选项
     * @return
     */
    @GetMapping("/getOpenOptions")
    public AjaxResult getOpenOptions(){
        LetterClassify classify=new LetterClassify();
        classify.setStatus(BaseConstant.STATUS_OPEN);
        List<LetterClassify> list= this.letterClassifyService.selectList(classify);
        List<Option> options=new ArrayList<>();
        for (LetterClassify temp: list) {
            options.add(new Option(temp.getId(),temp.getClassifyName()));
        }
        return AjaxResult.success(options);
    }

}
