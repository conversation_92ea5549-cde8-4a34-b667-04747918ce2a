package com.zws.appeal.controller;

import com.zws.common.core.domain.ScheduleVo;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.appeal.schedule.IScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 异步进度接口类
 *
 * <AUTHOR>
 * @date 2024/1/7 9:13
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/schedule")
public class ScheduleController {

    @Autowired
    private IScheduleService scheduleService;

    /**
     * 查询分案进度
     *
     * @param vo
     * @return
     */
    @GetMapping("/scheduleAllocation")
    public AjaxResult scheduleAllocation(ScheduleVo vo) {
        ScheduleVo schedule = scheduleService.getSchedule(vo.getScheduleNo());
        return AjaxResult.success(schedule);
    }


}
