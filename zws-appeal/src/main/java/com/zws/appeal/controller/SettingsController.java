package com.zws.appeal.controller;

import cn.hutool.core.bean.BeanUtil;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.domain.Option;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.domain.ZwsLoginUser;
import com.zws.common.enums.ZwsPlatformEnum;
import com.zws.common.security.annotation.Logical;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.common.security.annotation.RequiresPermissions;
import com.zws.appeal.agservice.AgSettingsService;
import com.zws.appeal.agservice.AgTarefasAgendadas;
import com.zws.appeal.agservice.AgTeamService;
import com.zws.appeal.domain.Error;
import com.zws.appeal.domain.*;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.pojo.ApprovalSettingsPojo;
import com.zws.appeal.pojo.ReturnInformation;
import com.zws.appeal.pojo.StateDesensitization;
import com.zws.appeal.pojo.TreeType;
import com.zws.appeal.pojo.staticConst.StaticConstantClass;
import com.zws.appeal.service.MenuService;
import com.zws.appeal.service.SettingsService;
import com.zws.appeal.service.TeamService;
import com.zws.appeal.utils.DesensitizationRedis;
import com.zws.appeal.utils.Log;
import com.zws.appeal.utils.SplitUtils;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.standardMenu.domain.dto.ZwsTeamMenuTemplateDto;
import com.zws.standardMenu.service.ZwsStandardMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;


/**
 * 设置Controller
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/settings")
public class SettingsController extends BaseController {

    @Autowired
    private SettingsService settingsService;
    @Autowired
    private AgSettingsService agSettingsService;
    @Autowired
    private MenuService menuService;
    @Autowired
    private AgTeamService agTeamService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private DesensitizationRedis desensitizationRedis;
    @Autowired
    private AgTarefasAgendadas agTarefasAgendadas;
    @Resource
    private ZwsStandardMenuService zwsStandardMenuService;

    /**
     * 查询审批设置
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApprovalSettings", method = RequestMethod.GET)
    public AjaxResult selectList() {
        List<ApprovalSettings> list = agSettingsService.insertApprovalSettings();
        return AjaxResult.success("查询成功", list);
    }


    /**
     * 根据团队id查找团队审批设置的角色信息
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRoles", method = RequestMethod.GET)
    public AjaxResult selectListWithRoles() {
        List<Role> roles = teamService.selectRole(TokenInformation.getCreateid());
        return AjaxResult.success(roles);
    }

    /**
     * 根据角色信息查询员工或者团队
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectEmployeesRoles", method = RequestMethod.GET)
    public AjaxResult selectListWithEmployeesRoles(Employees employees) {
        employees.setCreateId(TokenInformation.getCreateid());
        List<ReturnInformation> list = agSettingsService.selectRole(employees);
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 根据团队id获取团队标签信息
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/findIdLabel", method = RequestMethod.GET)
    public AjaxResult selectListWithFindIdLabel() {
        List<Label> idLabel = teamService.findIdLabel(TokenInformation.getCreateid());
        return AjaxResult.success("查询成功", idLabel);
    }

    /**
     * 根据字段查询员工信息
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectEmployees", method = RequestMethod.GET)
    public TableDataInfo selectListWithEmployees(Employees employees) {
        List<Long> subDepartmentIds = agSettingsService.getSubDepartmentIds(employees.getDepartmentId());
        employees.setDepartmentIds(subDepartmentIds);  //部门id集合
        employees.setDepartmentId(null);

        startPage();
        List<Employees> employees1 = agSettingsService.selectDepartment(employees);
        return getDataTable(employees1);
    }

    /**
     * 根据用户id查询用户登陆日志
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectEmployeesLogin", method = RequestMethod.GET)
    public TableDataInfo selectListWithEmployees(TeamLogininfor teamLogininfor) {
        String name = SplitUtils.handleString(teamLogininfor.getName());
        teamLogininfor.setName(name);
        String ipaddr = SplitUtils.handleString(teamLogininfor.getIpaddr());
        teamLogininfor.setIpaddr(ipaddr);
        startPage();
        List<TeamLogininfor> teamLogininfors = settingsService.selectTeamLogininfor(teamLogininfor);
        return getDataTable(teamLogininfors);

    }

    /**
     * 角色信息全查/根据名称字段查询（分页）
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/findRole", method = RequestMethod.GET)
    public TableDataInfo selectListWithRoleByPage(Role role) {
        startPage();
        List<Role> roles = settingsService.selectRolees(role);
        return getDataTable(roles);
    }

    /**
     * 查询外访审批设置具体流程数据
     *
     * @param approvalSteps
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApprovalSteps", method = RequestMethod.GET)
    public AjaxResult selectListWithApprovalSteps(ApprovalSteps approvalSteps) {
        List<ApprovalSteps> approvalSteps1 = settingsService.selectApprovalSteps(approvalSteps);
        return AjaxResult.success("查询成功", approvalSteps1);
    }


    /**
     * 角色信息全查/根据名称字段查询(不分页)
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRole", method = RequestMethod.GET)
    public AjaxResult selectListWithRole(Role role) {
        List<Role> roles = settingsService.selectRoleeDisable(role);
        return AjaxResult.success("查询成功", roles);
    }

    /**
     * 根据角色信息查询员工或者团队
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectEmployeesRole", method = RequestMethod.GET)
    public AjaxResult selectListWithEmployeesRole(Employees employees) {
        List<ReturnInformation> list = agSettingsService.selectRolees(employees);
        return AjaxResult.success("查询成功", list);
    }


    /**
     * 返回部门列表树类型信息
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectDeptTreeType", method = RequestMethod.GET)
    public AjaxResult selectListWithDeptTreeType() {
        List<TreeType> treeType = agSettingsService.DeptTreeType();
        return AjaxResult.success("查询成功", treeType);
    }

    /**
     * 菜单信息全查/根据id查询/根据名称查询/根据状态查询
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectMenu", method = RequestMethod.GET)
    public AjaxResult selectListWithMenu(Menu menu) {
        List<Menu> menus = menuService.selectMenus(menu);
        return AjaxResult.success("查询成功", menus);
    }


    /**
     * 获取路由信息
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectMenuRoute", method = RequestMethod.GET)
    public AjaxResult selectListWithMenuRoute() {
        int userid = TokenInformation.getUserid();
        PageUtils.clearPage();
        List<Menu> menus = menuService.selectMenuTreeByUserId(new Long((long) userid));
        if(CollectionUtils.isEmpty(menus)){
            return  AjaxResult.success("查询失败,菜单为空",new ArrayList<>());
        }
        return AjaxResult.success("查询成功", menuService.buildMenus(menus));
    }


    /**
     * 根据团队id查询团队设置状态（页面限制）
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRestrictedState", method = RequestMethod.GET)
    public AjaxResult infoWithRestrictedState() {
        StateDesensitization desensitization = desensitizationRedis.getDesensitization(StaticConstantClass.DESENSITIZATION + TokenInformation.getCreateid());
        if (!ObjectUtils.isEmpty(desensitization)) {
            State state = desensitization.getState();
//        State state = settingsService.selectState();
            if (ObjectUtils.isEmpty(state)) {
                return AjaxResult.success("查询成功", 0);
            } else {
                return AjaxResult.success("查询成功", state.getRestrictedState());
            }
        } else {
            return AjaxResult.success("查询成功", 0);
        }
    }

    /**
     * 添加部门
     *
     * @param dept
     * @return
     */
    @RequiresPermissions("system:dept:add")
    @Log(title = "系统设置-添加部门", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertDept", method = RequestMethod.POST)
    public AjaxResult addDept(@Validated @RequestBody Dept dept) {
        settingsService.insertDept(dept);
        return AjaxResult.success("添加成功");
    }


    /**
     * 添加角色
     *
     * @param role
     * @return
     */
    @RequiresPermissions("system:role:add")
    @Log(title = "系统设置-添加角色", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertRole", method = RequestMethod.POST)
    public AjaxResult addRole(@Validated @RequestBody Role role) {
        settingsService.insertRole(role);
        return AjaxResult.success("添加成功");
    }

    /**
     * 批量添加员工信息
     *
     * @param employees
     * @return
     */
    @Log(title = "系统设置-批量添加员工", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertEmployeesBatch", method = RequestMethod.POST)
    public AjaxResult addEmployeesBatch(@Validated @RequestBody List<Employees> employees) {
        int i = settingsService.insertEmployeesBatch(employees);
        return AjaxResult.success("添加成功", i);
    }

    /**
     * 新增菜单信息
     *
     * @param
     * @return
     */
    @Log(title = "系统设置-新增菜单信息", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequiresPermissions("system:menu:add")
    @RequestMapping(value = "/insertMenu", method = RequestMethod.POST)
    public AjaxResult addMenu(@RequestBody Menu menu) {
        if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuNameUnique(menu))) {
            return AjaxResult.error("新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        } else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath())) {
            return AjaxResult.error("新增菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        }else if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuPermsUnique(menu))) {
            return AjaxResult.error("新增菜单'" + menu.getPerms() + "'失败，权限标识已存在");
        }
        return toAjax(menuService.insertMenu(menu));
    }


    /**
     * 添加员工信息
     *
     * @param employees
     * @return
     */
    @RequiresPermissions("system:user:add")
    @Log(title = "系统设置-添加员工", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertEmployees", method = RequestMethod.POST)
    public AjaxResult addEmployees(@Validated @RequestBody Employees employees) {
        int number = agSettingsService.employeeId();
        employees.setEmployeesWorking(number);
        settingsService.insertEmployees(employees);
        //更新部门是否有负责人
        settingsService.updateDeptHaveHead(employees.getDepartmentId());
        return AjaxResult.success("添加成功");
    }


    /**
     * 修改菜单信息
     *
     * @param
     * @return
     */
    @Log(title = "系统设置-修改菜单信息", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequiresPermissions("system:menu:edit")
    @RequestMapping(value = "/updateMenu", method = RequestMethod.PUT)
    public AjaxResult editMenu(@Validated @RequestBody Menu menu) {
        if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuNameUnique(menu))) {
            return AjaxResult.error("修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        } else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath())) {
            return AjaxResult.error("修改菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        } else if (menu.getMenuId().equals(menu.getParentCode())) {
            return AjaxResult.error("修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
        }
        return toAjax(menuService.updateMenu(menu));
    }

    /**
     * 修改部门信息
     *
     * @param dept
     * @return
     */
    @RequiresPermissions("system:dept:edit")
    @Log(title = "系统设置-修改部门信息", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updateDept", method = RequestMethod.PUT)
    public AjaxResult editDept(@Validated @RequestBody Dept dept) {
        if (dept.getParentId() != null) {
            if (dept.getId().intValue() == dept.getParentId().intValue()) {
                return AjaxResult.error("上级部门错误");
            }
        }

        settingsService.updateDept(dept);
        return AjaxResult.success("修改成功");
    }


    /**
     * 修改角色信息
     *
     * @param role
     * @return
     */
    @RequiresPermissions(value = {"system:role:setedit", "system:role:setpower", "system:role:status"}, logical = Logical.OR)
    @Log(title = "系统设置-修改角色信息", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updateRole", method = RequestMethod.PUT)
    public AjaxResult editRole(@Validated @RequestBody Role role) {
        agSettingsService.updateRoleOrMenu(role);
        return AjaxResult.success("修改成功");
    }

    /**
     * 修改员工信息
     *
     * @param employees
     * @return
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "系统设置-修改员工信息", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updateEmployees", method = RequestMethod.PUT)
    public AjaxResult editEmployees(@Validated @RequestBody Employees employees) {
        settingsService.updateEmployees(employees);
        //更新部门是否有负责人
        settingsService.updateDeptHaveHead(employees.getDepartmentId());
        return AjaxResult.success("已修改成功，该账户需重新登录系统即可更新！");
    }

    /**
     * 批量修改员工账号状态/修改角色
     *
     * @param
     * @return
     */
    @RequiresPermissions(value = {"system:role:edit", "system:set:status"}, logical = Logical.OR)
    @Log(title = "系统设置-批量修改员工账号状态/修改角色", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updateAccountStatus", method = RequestMethod.PUT)
    public AjaxResult editAccountStatus(@RequestBody List<Employees> employees) {
        settingsService.updateAccountStatus(employees);
        return AjaxResult.success("修改成功");
    }

    /**
     * 批量重置员工账号密码
     *
     * @param
     * @return
     */
    @RequiresPermissions("system:set:password")
    @Log(title = "系统设置-批量重置员工账号密码", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updatePassword", method = RequestMethod.PUT)
    public AjaxResult editPassword(@RequestBody List<Employees> employees) {
        if (ObjectUtils.isEmpty(employees)) {
            throw new GlobalException("员工信息不能为空");
        }
        settingsService.updatePassword(employees);
        return AjaxResult.success("修改成功");
    }

    /**
     * 修改标签信息
     *
     * @return
     */
    @Log(title = "系统管理-修改标签信息", businessType = BusinessType.UPDATE)
    @RequiresPermissions(value = {"system:label:edit", "system:label:status"}, logical = Logical.OR)
    @RequestMapping(value = "/label", method = RequestMethod.PUT)
    public AjaxResult editLabel(@RequestBody List<Label> label) {
        teamService.updateLabel(label);
        return AjaxResult.success("修改成功");
    }


    /**
     * 团队审批设置流程信息修改/删除/新增
     *
     * @param approvalSettingsPojo
     * @return
     */
    @RequiresPermissions("system:approve:set")
    @Log(title = "系统设置-审批设置流程信息修改", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updateApprovalSteps", method = RequestMethod.POST)
    public AjaxResult editApprovalSteps(@RequestBody ApprovalSettingsPojo approvalSettingsPojo) {
        List<ApprovalSteps> approvalSteps = approvalSettingsPojo.getApprovalSteps();
        for (ApprovalSteps approvalSteps1 : approvalSteps) {
            approvalSteps1.setCreateId(TokenInformation.getCreateid());
        }
        agTeamService.ApprovalSteps(approvalSettingsPojo);
        return AjaxResult.success("修改成功");
    }

    /**
     * 登陆人修改密码
     *
     * @param passWord
     * @return
     */
    @Log(title = "个人中心-修改密码", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updatePassWord", method = RequestMethod.PUT)
    public AjaxResult editPassword(@RequestBody PassWord passWord, HttpServletRequest request) {
        if (ObjectUtils.isEmpty(passWord.getPrimaryPassWord())) {
            throw new GlobalException("原密码不能为空");
        }
        if (ObjectUtils.isEmpty(passWord.getNewPassword())) {
            throw new GlobalException("新密码不能为空");
        }
        agSettingsService.updatePassWord(passWord, request);
        return AjaxResult.success("修改成功");
    }

    /**
     * 删除菜单信息
     *
     * @param
     * @return
     */
    @Log(title = "系统设置-删除菜单信息", businessType = BusinessType.DELETE)
    @RequiresLogin
    @RequiresPermissions("system:menu:remove")
    @RequestMapping(value = "/deleteMenu/{menuId}", method = RequestMethod.DELETE)
    public AjaxResult removeMenu(@PathVariable("menuId") Long menuId) {
        if (menuService.hasChildByMenuId(menuId, TokenInformation.getCreateid())) {
            return AjaxResult.error("存在子菜单,不允许删除");
        }
        if (menuService.checkMenuExistRole(menuId)) {
            return AjaxResult.error("菜单已分配,不允许删除");
        }
        return toAjax(menuService.deleteMenu(menuId.intValue(), TokenInformation.getCreateid()));
    }

    /**
     * 根据id删除部门信息
     *
     * @param id
     * @return
     */
    @RequiresPermissions("system:dept:detele")
    @Log(title = "系统设置-删除部门信息", businessType = BusinessType.DELETE)
    @RequiresLogin
    @RequestMapping(value = "/deleteDept/{id}", method = RequestMethod.PUT)
    public AjaxResult removeDept(@RequestBody @PathVariable("id") int id) {
        settingsService.deleteDept(id);
        return AjaxResult.success("删除成功");
    }

    /**
     * 根据id删除角色信息
     *
     * @param id
     * @return
     */
    @RequiresPermissions("system:role:delete")
    @Log(title = "系统设置-删除角色信息", businessType = BusinessType.DELETE)
    @RequiresLogin
    @RequestMapping(value = "/deleteRole/{id}", method = RequestMethod.PUT)
    public AjaxResult removeRole(@RequestBody @PathVariable("id") int id) {
        agSettingsService.deleteRoleAndUserRole(id);
        return AjaxResult.success("删除成功");
    }

    /**
     * 根据id删除员工信息
     *
     * @param id
     * @return
     */
    @RequiresPermissions("system:user:delete")
    @Log(title = "系统设置-删除员工信息", businessType = BusinessType.DELETE)
    @RequiresLogin
    @RequestMapping(value = "/deleteEmployees/{id}", method = RequestMethod.PUT)
    public AjaxResult removeEmployees(@RequestBody @PathVariable("id") int id) {
        agSettingsService.deleteEmployees(id);
        //取消员工的坐席分配
        //
        return AjaxResult.success("删除成功");
    }


    /**
     * 下载员工信息表模板
     *
     * @param response
     * @throws IOException
     */
    @Log(title = "系统设置-下载员工信息表模板", businessType = BusinessType.EXPORT)
    @RequiresLogin
    @PostMapping("/export")
    public void export(HttpServletResponse response) throws IOException {
//        设置下载模板名字
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("员工信息导入模板.xlsx", "utf-8"));
        ExcelUtil<Employees> util = new ExcelUtil<Employees>(Employees.class);
        util.exportExcel(response, new ArrayList<>(), "员工信息表");
    }

    /**
     * excel表格导入员工信息
     *
     * @param file
     * @return
     * @throws Exception
     */
    @RequiresPermissions("system:user:import")
    @Log(title = "系统设置-excel表格导入员工信息", businessType = BusinessType.IMPORT)
    @RequiresLogin
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<Employees> util = new ExcelUtil<Employees>(Employees.class);
        List<Employees> userList = util.importExcel(file.getInputStream());
        Error error = agSettingsService.insertEmployeess(userList);
        return AjaxResult.success(error);
    }


    /**
     * 根据团队id查询团队设置状态返回水印设置字符串（水印设置）
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/watermark", method = RequestMethod.GET)
    public AjaxResult watermark(Long caseId) {
        Map<String, Object> watermark = agSettingsService.watermark(caseId);
        return AjaxResult.success("查询成功", watermark);
    }

    /**
     * 根据团队id查询团队设置状态返回导出开关（导出设置）
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/exportSwtich", method = RequestMethod.GET)
    public AjaxResult exportSwtich() {
        Map<String, Object> tokenMap = new HashMap<>();
        //默认都开启-已经改了不从这里获取
        tokenMap.put("exportStatus", 1);
        return AjaxResult.success("查询成功", tokenMap);
    }


    /**
     * 获取响应的用户选项
     * @param
     * @return
     */
    @GetMapping("/user/getOption")
    public AjaxResult getOption(){
        Employees employees=new Employees();
        employees.setCreateId(TokenInformation.getCreateid());
        List<Employees> es= settingsService.selectEmployeesRole(employees);

        List<Option> options=new ArrayList<>();
        //options.add(new Option(TokenInformation.getCreateid(),optionAgService.getLawName()));
        for (Employees temp:es) {
            options.add(new Option(temp.getId(),temp.getEmployeeName()));
        }
        return AjaxResult.success(options);
    }

    /**
     * 加载对应角色菜单列表树
     */
    @RequiresLogin
    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
    public AjaxResult roleMenuTreeselect(@PathVariable("roleId") Long roleId) {
        int userId = TokenInformation.getUserid();
        List<Menu> menus = menuService.selectMenuList(new Long((long) userId));
//        Set<String> teamExport = teamService.selectCloseTeamExport(TokenInformation.getCreateid());
//        List<Menu> menus2 = new ArrayList<>();
//        for (Menu temp : menus) {
//            if (!teamExport.contains(temp.getPerms())) {
//                menus2.add(temp);
//            }
//        }

        AjaxResult ajax = AjaxResult.success();
        ajax.put("checkedKeys", menuService.selectMenuListByRoleId(roleId));
        ajax.put("menus", menuService.buildMenuTreeSelect(menus));
        return ajax;
    }

//----------------------------------机构菜单模板------------------------------------------------------------


    /**
     * 机构模板菜单信息全查/根据id查询/根据名称查询/根据状态查询
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectMenuTemplate", method = RequestMethod.GET)
    public AjaxResult selectMenuTemplate(TeamMenuTemplate menuTemplate) {
        List<TeamMenuTemplate> menus = menuService.selectMenuTemplate(menuTemplate);
        return AjaxResult.success("查询成功", menus);
    }

    /**
     * 新增机构菜单信息
     *
     * @param
     * @return
     */
    @Log(title = "机构-新增菜单信息", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertMenuTemplate", method = RequestMethod.POST)
    public AjaxResult insertMenuTemplate(@RequestBody TeamMenuTemplate menuTemplate) {
//        if (UserConstants.NOT_UNIQUE.equals(menuService.checkTemplateMenuNameUnique(menuTemplate))) {
//            return AjaxResult.error("新增菜单'" + menuTemplate.getMenuName() + "'失败，菜单名称已存在");
//        } else if (UserConstants.YES_FRAME.equals(menuTemplate.getIsFrame()) && !StringUtils.ishttp(menuTemplate.getPath())) {
//            return AjaxResult.error("新增菜单'" + menuTemplate.getMenuName() + "'失败，地址必须以http(s)://开头");
//        }else if (UserConstants.NOT_UNIQUE.equals(menuService.checkTemplateMenuPermsUnique(menuTemplate))) {
//            return AjaxResult.error("新增菜单'" + menuTemplate.getPerms() + "'失败，权限标识已存在");
//        }
//        return toAjax(menuService.insertMenuTemplate(menuTemplate));
        ZwsTeamMenuTemplateDto templateDto = new ZwsTeamMenuTemplateDto();
        BeanUtil.copyProperties(menuTemplate, templateDto);
        ZwsLoginUser zwsLoginUser = new ZwsLoginUser();
        zwsLoginUser.setUsername(SecurityUtils.getUsername());
        //机构菜单类型（0 催收端 1调诉端 2调执端）
        templateDto.setType("1");
        zwsStandardMenuService.addMenuTemplate(templateDto,zwsLoginUser);
        return AjaxResult.success();
    }

    /**
     * 机构-修改菜单信息
     *
     * @param
     * @return
     */
    @Log(title = "机构-修改菜单信息", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/updateMenuTemplate", method = RequestMethod.PUT)
    public AjaxResult updateMenuTemplate(@Validated @RequestBody TeamMenuTemplate menuTemplate) {
//        menuTemplate.setUpdateBy(TokenInformation.getUsername());
//        if (UserConstants.NOT_UNIQUE.equals(menuService.checkTemplateMenuNameUnique(menuTemplate))) {
//            return AjaxResult.error("修改菜单'" + menuTemplate.getMenuName() + "'失败，菜单名称已存在");
//        } else if (UserConstants.YES_FRAME.equals(menuTemplate.getIsFrame()) && !StringUtils.ishttp(menuTemplate.getPath())) {
//            return AjaxResult.error("修改菜单'" + menuTemplate.getMenuName() + "'失败，地址必须以http(s)://开头");
//        } else if (menuTemplate.getMenuCode().equals(menuTemplate.getParentCode())) {
//            return AjaxResult.error("修改菜单'" + menuTemplate.getMenuName() + "'失败，上级菜单不能选择自己");
//        }else if (UserConstants.NOT_UNIQUE.equals(menuService.checkTemplateMenuPermsUnique(menuTemplate))) {
//            return AjaxResult.error("修改菜单'" + menuTemplate.getPerms() + "'失败，权限标识已存在");
//        }
//        return toAjax(menuService.updateMenuTemplate(menuTemplate));
        ZwsTeamMenuTemplateDto templateDto = new ZwsTeamMenuTemplateDto();
        BeanUtil.copyProperties(menuTemplate, templateDto);
        ZwsLoginUser zwsLoginUser = new ZwsLoginUser();
        zwsLoginUser.setUsername(SecurityUtils.getUsername());
        //机构菜单类型（0 催收端 1调诉端 2调执端）
        templateDto.setType("1");
        zwsStandardMenuService.updateMenuTemplate(templateDto,zwsLoginUser);
        return AjaxResult.success();
    }

    /**
     * 删除菜单信息
     *
     * @param
     * @return
     */
    @Log(title = "机构-删除菜单信息", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/deleteMenuTemplate/{menuId}", method = RequestMethod.DELETE)
    public AjaxResult deleteMenuTemplate(@PathVariable("menuId") Long menuId) {
//        if (menuService.hasChildByMenuIdTemplate(menuId)) {
//            return AjaxResult.error("存在子菜单,不允许删除");
//        }
//        return toAjax(menuService.deleteMenuTemplate(menuId));
        zwsStandardMenuService.deleteMenuTemplate(menuId, ZwsPlatformEnum.TS);
        return AjaxResult.success();
    }


}
