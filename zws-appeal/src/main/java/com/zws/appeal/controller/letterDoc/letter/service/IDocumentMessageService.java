package com.zws.appeal.controller.letterDoc.letter.service;


import com.zws.appeal.controller.letterDoc.letter.domain.DocumentMessage;
import com.zws.system.api.model.LoginUser;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 律函批次 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-04 20:38
 */
public interface IDocumentMessageService {


    /**
     * 新建发函
     * @param message
     */
    List<Long>  insert(DocumentMessage message, List<Map<String, Object>> row,LoginUser loginUser);

    /**
     * 查询列表
     * @param message
     * @param bo true 律函审批查询 ; false 列表展示
     * @return
     */
    List<DocumentMessage> selectList(DocumentMessage message,boolean bo);

    /**
     * 查询导出文件信息
     * @param ids
     * @return
     */
    List<DocumentMessage> selectExportZip(List<Integer> ids);

    /**
     * 主键查询
     * @param id
     * @return
     */
    DocumentMessage getById(Integer id);

    /**
     * 校验文件内容
     * @param message
     * @return
     */
    Map<String,Object> verifyFile(DocumentMessage message);

    /**
     * 根据主键选择更新
     * @param message
     * @return
     */
    int updateById(DocumentMessage message);

    /**
     * 更新函件批次的审批状态
     */
    void updateLetterMessageProce(List<Integer> ids);

    /**
     * 生成函件批次的压缩包
     * @param ids
     */
    @Async
    void createZipFile(List<Integer> ids, LoginUser  loginUser) ;

    @Async
    void createZipFile(Integer id,LoginUser loginUser) ;
    /**
     * 校验压缩包中的pdf文件
     * @param message
     * @return
     */
    Map<String, Object> verifyZipFile(DocumentMessage message);

    /**
     * 创建定时删除任务
     * @param key
     */
    public void createTask(String key);

    /**
     * 根据压缩包文件(批量新建发函)
     * @param message 函件批次对象
     * @param path 解压文件物理路径
     */
    @Async
    void addByZip(DocumentMessage message, String path,LoginUser loginUser);
}
