package com.zws.appeal.controller.letterDoc.letter;

import java.io.*;

public class BinaryToFile {
    public static void saveBinaryStreamToFile(InputStream binaryStream, String destinationFilePath) throws IOException {
        // 检查文件夹路径是否存在，如果不存在则创建
        File directory = new File(destinationFilePath).getParentFile();
        if (!directory.exists() && !directory.mkdirs()) {
            throw new IOException("Failed to create directories for path: " + directory.getAbsolutePath());
        }

        // 创建文件输出流，指定要写入的文件路径
        FileOutputStream fileOutputStream = new FileOutputStream(destinationFilePath);

        try (BufferedOutputStream bufferedOut = new BufferedOutputStream(fileOutputStream)) {
            byte[] buffer = new byte[1024]; // 缓冲区大小可根据实际需求调整
            int bytesRead;

            // 从输入流读取二进制数据并写入到文件输出流
            while ((bytesRead = binaryStream.read(buffer)) != -1) {
                bufferedOut.write(buffer, 0, bytesRead);
            }

            // 刷新缓冲区并确保所有数据都被写入到磁盘
            bufferedOut.flush();
        } finally {
            // 关闭打开的资源
            binaryStream.close();
            fileOutputStream.close();
        }
    }

    // 示例用法
//    public static void main(String[] args) throws IOException {
//        // 假设这是你的二进制输入流来源
//        InputStream binaryData = ...; // 可能来自网络、数据库或其他源
//
//        // 目标文件路径，包括文件名
//        String destinationPath = "D:/java/test/somefile.jpg";
//
//        // 调用方法保存二进制流到指定文件夹
//        saveBinaryStreamToFile(binaryData, destinationPath);
//
//        System.out.println("二进制流已成功保存到 " + destinationPath);
//    }
}
