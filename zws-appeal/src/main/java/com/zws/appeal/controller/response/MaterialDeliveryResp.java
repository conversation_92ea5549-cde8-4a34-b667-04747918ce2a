package com.zws.appeal.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.appeal.domain.appeal.FreezeRecord;
import com.zws.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 材料寄送-列表查询
 *
 * @Author：liuxifeng
 * @Date：2024/6/20 15:48
 * @Describe：编辑描述
 */
@Data
public class MaterialDeliveryResp {

    /**
     * 物流主键id
     */
    private Long id;

    private List<Long>ids;
    /**
     * 案件ID
     */
    private Long caseId;
    /**
     * 客户姓名
     */
    private String clientName;

    /**
     * 标的额
     */
    private Long remainingDue;

    /**
     * 客户身份证号
     */
    private String clientIdCard;

    /**
     * 户籍地
     */
    private String clientCensusRegister;

    /**
     * 寄送状态
     */
    private String sentStatus;

    /**
     * 寄送材料数量
     */
    private String sentCount;

    /**
     * 寄送法院
     */
    private String sentCourt;

    /**
     * 寄送单号
     */
    private String expressNumber;

    /**
     * 操作时间
     */
    private String creatTime;
    /**
     * 操作人
     */
    private String creatBy;

    /**
     * 案件导入时的阶段
     */
    private String stage;
    /**
     *是否诉保（0未保全、1已保全）
     */
    private Integer isFreeze;

    /**
     * 保全开始时间(取最新一条保全记录)
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startFreeze;

    /**
     * 保全结束时间(取最新一条保全记录)
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endFreeze;

}
