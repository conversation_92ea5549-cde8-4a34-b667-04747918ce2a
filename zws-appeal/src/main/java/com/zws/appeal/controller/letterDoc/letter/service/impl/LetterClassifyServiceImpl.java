package com.zws.appeal.controller.letterDoc.letter.service.impl;

import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterClassify;
import com.zws.appeal.controller.letterDoc.letter.mapper.LetterClassifyMapper;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterClassifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 函件类型
 * <AUTHOR>
 * @date ：Created in 2023/11/23 20:28
 */
@Service
public class LetterClassifyServiceImpl implements ILetterClassifyService {

    @Autowired
    private LetterClassifyMapper baseMapper;

    @Override
    public Long insert(LetterClassify record) {
        record.setId(null);
        record.getClassifyName();
        int i = baseMapper.selectIsExist(record.getClassifyName());
        if(i>0) {
            throw new GlobalException("该类型名称已被创建，请重新输入");
        }

        record.setCreateBy(SecurityUtils.getUsername());
        record.setCreateById(SecurityUtils.getUserId());
        record.setCreateTime(new Date());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        record.setTeamId(SecurityUtils.getTeamId());
        baseMapper.insert(record);
        return record.getId();
    }

    @Override
    public void updateById(LetterClassify record) {
//        record.getClassifyName();
//        int i = baseMapper.selectIsExist(record.getClassifyName());
//        if(i>0) throw new GlobalException("该类型名称已被创建，请重新输入");
        LetterClassify infor = this.baseMapper.selectByPrimaryKey(record.getId());
        List<String> list = this.baseMapper.selectAllclassifyName();
        list.remove(infor.getClassifyName());
        if (list.size()>0&& list!= null && list.contains(record.getClassifyName())){
            throw new ServiceException("该类型名称已被创建，请重新输入");
        }
        record.setCreateBy(null);
        record.setCreateById(null);
        record.setCreateTime(null);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        this.baseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void deleteById(Long id) {
        LetterClassify record=new LetterClassify();
        record.setId(id);
        record.setDelFlag(BaseConstant.DelFlag_Being);
        this.updateById(record);
    }

    @Override
    public LetterClassify getById(Long id) {
        return this.baseMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<LetterClassify> selectList(LetterClassify record) {
        record.setTeamId(SecurityUtils.getTeamId());
        return this.baseMapper.selectList(record);
    }
}
