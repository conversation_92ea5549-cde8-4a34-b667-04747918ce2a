package com.zws.appeal.controller;

import com.zws.common.core.domain.R;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.common.security.annotation.RequiresPermissions;
import com.zws.appeal.agservice.AgMessageService;
import com.zws.appeal.domain.Message.MessageCenter;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.service.MessageService;
import com.zws.appeal.utils.FileUpload;
import com.zws.appeal.utils.Log;
import com.zws.system.api.RemoteFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 消息控制器
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/message")
public class MessageController extends BaseController {

    @Autowired
    private MessageService messageService;
    @Autowired
    private FileUpload fileUpload;
    @Autowired
    private AgMessageService agMessageService;
    @Autowired
    private RemoteFileService remoteFileService;

    /**
     * 查询团队所有发布消息信息
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectMessageCenter", method = RequestMethod.GET)
    public TableDataInfo selectList(MessageCenter messageCenter) {
        startPage();
        List<MessageCenter> messageCenters = messageService.selectMessageCenter(messageCenter);
        return getDataTable(messageCenters);
    }

    /**
     * 根据id修改消息标题/消息内容
     *
     * @param messageCenter
     * @return
     */
    @Log(title = "消息中心-修改消息标题/消息内容", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequiresPermissions("message:system:edit")
    @RequestMapping(value = "/updateMessageCenter", method = RequestMethod.PUT)
    public AjaxResult edit(@RequestBody MessageCenter messageCenter) {
        agMessageService.updateMessageCenter(messageCenter);
        return AjaxResult.success("修改成功");
    }

    /**
     * 根据id删除消息内容
     *
     * @param messageCenter
     * @return
     */
    @Log(title = "消息中心-删除消息内容", businessType = BusinessType.DELETE)
    @RequiresLogin
    @RequiresPermissions("message:system:delete")
    @RequestMapping(value = "/deleteMessageCenter", method = RequestMethod.PUT)
    public AjaxResult remove(MessageCenter messageCenter) {
        messageService.deleteMessageCenter(messageCenter);
        return AjaxResult.success("修改成功");
    }

    /**
     * 上传消息中心发布消息文件到minio
     *
     * @param file
     * @return
     * @throws Exception
     */
    @Log(title = "消息中心-上传消息中心发布消息文件", businessType = BusinessType.IMPORT)
    @RequiresLogin
    @PostMapping("/uploadCenter")
    public AjaxResult uploadCenter(MultipartFile file) throws Exception {
        MultipartFile[] files = new MultipartFile[]{file};
        String fileName = "MessageCenter";
//        将文件上传到minio
        String[] list = new String[]{};  //文件后缀限制
        Map<String, Object> map = fileUpload.uploadFile(files, fileName, list);
        return AjaxResult.success("上传成功", map);
    }

    /**
     * 若依上传文件接口
     *
     * @param file
     * @return
     */
    @PostMapping("/upload")
    public R upload(MultipartFile file) {
        return remoteFileService.upload(file);
    }
}
