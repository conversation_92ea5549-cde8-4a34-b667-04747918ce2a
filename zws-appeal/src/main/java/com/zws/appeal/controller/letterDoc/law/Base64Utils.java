package com.zws.appeal.controller.letterDoc.law;

import com.zws.common.core.utils.IdUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import org.apache.http.entity.ContentType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Decoder;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

public class Base64Utils {

    /**
     * base64转文件MultipartFile对象
     *
     * @param base64Code
     * @return
     * @throws Exception
     */
    public static MultipartFile decoderBase64File(String base64Code)
            throws Exception {
        byte[] buffer = new BASE64Decoder().decodeBuffer(base64Code);
        InputStream inputStream = new ByteArrayInputStream(buffer);
        String fileName= IdUtils.fastSimpleUUID()+".pdf";
        MultipartFile file = new MockMultipartFile(fileName,fileName, ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
        return file;
    }


    /**
     * 将base64字符解码保存文件
     *
     * @param base64Code base64字符串
     * @throws Exception
     */
    public static File base64PdfFile(String base64Code) throws Exception {
        File file = new File(FileDownloadUtils.tempFilePath+ IdUtils.fastSimpleUUID()+".pdf");
        if (!file.getParentFile().exists()) {
            file.mkdirs();
        }
        byte[] buffer = new BASE64Decoder().decodeBuffer(base64Code);
        FileOutputStream out = new FileOutputStream(file);
        out.write(buffer);
        out.close();
        return file;
    }


    /**
     * 网络图片转base64格式
     */
    public static String getImgUrlToBase64(String imgUrl) {
        byte[] buffer = null;
        InputStream inputStream = null;
        try (
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();){
            // 创建URL
            URL url = new URL(imgUrl);
            // 创建链接
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);
            inputStream = conn.getInputStream();
            // 将内容读取内存中
            buffer = new byte[1024];
            int len = -1;
            while ((len = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
            buffer = outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    // 关闭inputStream流
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        // 对字节数组Base64编码
        Base64.Encoder encode = Base64.getEncoder();
        return encode.encodeToString(buffer);
    }
}
