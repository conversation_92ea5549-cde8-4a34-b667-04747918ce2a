package com.zws.appeal.controller.letterDoc.law.service;

import com.zws.appeal.controller.letterDoc.law.domain.LawAgencyLetter;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2023/12/11 19:45
 */
public interface ILawAgencyLetterService {

    /**
     * 写入
     * @param agencyId
     * @param letterTemplateIds
     */
    void insert(Long agencyId,List<Long> letterTemplateIds);

    /**
     * 查询机构的文书模板
     * @param agencyId
     * @return
     */
    List<LawAgencyLetter> selectByAgencyId(Long agencyId);

    /**
     * 根据法院id查询文书信息
     *
     * @param courtId
     * @return
     */
    List<LetterTemplate> selectByAgencyId1(Long courtId);
}
