package com.zws.appeal.controller.appealController;

import cn.hutool.core.util.URLUtil;
import cn.hutool.poi.excel.ExcelReader;
import com.zws.appeal.agservice.AgCaseService;
import com.zws.appeal.agservice.AgLawsuitService;
import com.zws.appeal.domain.MessageTemplatePojo;
import com.zws.appeal.domain.appeal.CourtSession;
import com.zws.appeal.domain.appeal.JudgeInfor;
import com.zws.appeal.pojo.Option;
import com.zws.appeal.pojo.SendRecordsPojos;
import com.zws.appeal.pojo.appeal.FilePojo;
import com.zws.appeal.pojo.appeal.FilingCasePojo;
import com.zws.appeal.pojo.appeal.JudgePojo;
import com.zws.appeal.pojo.appeal.LawInforPojo;
import com.zws.appeal.service.appeal.IJudgeResultService;
import com.zws.appeal.task.JudgeResultTask;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.threadpool.PoolManageMent;
import com.zws.common.core.threadpool.TaskManager;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 判决与结果
 * <AUTHOR>
 * @date 2024/6/25 11:46
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/judge")
public class JudgeResultController extends BaseController {

    @Autowired
    private IJudgeResultService iJudgeResultService;
    @Autowired
    private AgCaseService agCaseService;
    @Autowired
    private RedisService redisService;
    /**
     * 查询
     * @param judgePojo
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo getJudgeList(JudgePojo judgePojo){
        startPage();
        List<JudgePojo> list = iJudgeResultService.getJudgeList(judgePojo);
        return getDataTable(list);
    }

    /**
     * 新增判决
     * @param judgeInfor
     * @return
     */
    @PostMapping("/addJudge")
    public AjaxResult addJudge(@RequestBody JudgeInfor judgeInfor){
        iJudgeResultService.addJudge(judgeInfor);
        return AjaxResult.success();
    }

    /**
     * 下载新增判决导入模板
     * @param response
     * @throws IOException
     */
    @PostMapping("/getTemplate")
    public void getTemplate(HttpServletResponse response) throws IOException {
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("判决登记导入模板.xlsx", "utf-8"));
        ExcelUtil<JudgeInfor> util = new ExcelUtil<JudgeInfor>(JudgeInfor.class);
        util.exportExcel(response, new ArrayList<>(), "判决登记导入模板");
    }

    /**
     * 批量导入
     * @param fileUrl
     * @return
     */
    @PostMapping("/importData")
    public AjaxResult importData(@RequestBody FilePojo filePojo) throws UnsupportedEncodingException {
        Optional.ofNullable(filePojo)
                .orElseThrow(() -> new ServiceException("文件路径不存在,请重新上传！"));
        String fileUrl = filePojo.getFileUrl();
        List<JudgeInfor> list1 = null;
        String encode = URLUtil.encode(fileUrl);
        try (InputStream inputStream = URLUtil.url(encode).openStream()){
            ExcelUtil<JudgeInfor> util = new ExcelUtil<JudgeInfor>(JudgeInfor.class);
            list1 = util.importExcel(URLUtil.url(encode).openStream());
            if (ObjectUtils.isEmpty(list1)) {
                return AjaxResult.error("文件信息为空,请重新编辑后上传");
            }

            ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(inputStream);
            List<Map<String, Object>> readAll = reader.read(0, 1,1);
            Map<String, Object> map = readAll.get(0);
            List<String> list = getExeclHeader();
            int size = map.size();
            if (size != list.size()) {
                return AjaxResult.error("请下载正确模板");
            }
            for (String key : map.keySet()) {
                if (!list.contains(key)) {
                    return AjaxResult.error("请下载正确模板");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
//        iJudgeResultService.insertCourtBach(list1);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        JudgeResultTask judgeResultTask = new JudgeResultTask(loginUser,list1,filePojo);
        TaskManager.addTask(judgeResultTask);
        return AjaxResult.success();
    }

    /**
     * 批量导入判决与结果表头
     * @return
     */
    public static List<String> getExeclHeader(){
        List<String> list = new ArrayList<>(10);
        list.add("案件ID");
        list.add("判决状态");
        list.add("判决文书");
        list.add("判决结果");
        list.add("判决金额");
        list.add("客户满意度");
        list.add("快递单号");
        list.add("判决时间");
        list.add("判决描述");
        return list;
    }

    /**
     * 判决状态下拉框
     * @return
     */
    @GetMapping("/getCaseStatus")
    public AjaxResult getCaseStatus(){
        String[] str = new String[]{"审理判决","判决审理","诉讼撤案","停止诉讼","诉讼结案","申请执行立案"};
        List<String> list1 = Arrays.asList(str);
        List<Option> list = new ArrayList<>(str.length);
        for (String s : list1) {
            Option option = new Option();
            option.setCode(s);
            option.setInfo(s);
            list.add(option);
        }
        return AjaxResult.success(list);
    }

    /**
     * 获取判决文书下拉框
     * @return
     */
    @GetMapping("/getJudgeLetter")
    public AjaxResult getJudgeLetter(){
        String[] str = new String[]{"仲裁裁决","一审判决","二审判决","管辖异议","民事调解书","仲裁调解书","仲裁调解裁定书","一审调解裁定书","二审调解裁定书","仲裁裁定书","一审裁定书","二审裁定书","执行裁定书","终本裁定"};
        List<String> list1 = Arrays.asList(str);
        List<Option> list = new ArrayList<>(str.length);
        for (String s : list1) {
            Option option = new Option();
            option.setCode(s);
            option.setInfo(s);
            list.add(option);
        }
        return AjaxResult.success(list);
    }

    /**
     * 获取判决结果下拉框
     * @return
     */
    @GetMapping("/getJudgeResult")
    public AjaxResult getJudgeResult(){
        String[] str = new String[]{"胜诉","败诉","中和判决"};
        List<String> list1 = Arrays.asList(str);
        List<Option> list = new ArrayList<>(str.length);
        for (String s : list1) {
            Option option = new Option();
            option.setCode(s);
            option.setInfo(s);
            list.add(option);
        }
        return AjaxResult.success(list);
    }

    /**
     * 客户满意度下拉框
     * @return
     */
    @GetMapping("/getCustSatisfy")
    public AjaxResult getCustSatisfy(){
        String[] str = new String[]{"满意","不满意","未回访","客户无反馈"};
        List<String> list1 = Arrays.asList(str);
        List<Option> list = new ArrayList<>(str.length);
        for (String s : list1) {
            Option option = new Option();
            option.setCode(s);
            option.setInfo(s);
            list.add(option);
        }
        return AjaxResult.success(list);
    }

    /**
     * 申请执行立案
     * @param pojo
     * @return
     */
    @PostMapping("/addAplayCase")
    public AjaxResult addAplayCase(@RequestBody FilingCasePojo pojo){
        iJudgeResultService.addAplayCase(pojo);
        return AjaxResult.success();
    }

    /**
     * 获取该机构员工信息
     * @return
     */
    @GetMapping("/getEmployees")
    public AjaxResult getEmployees(){
        List<Option> list = iJudgeResultService.getEmployees();
        return AjaxResult.success(list);
    }

    /**
     * 批量结案
     */
    @PostMapping("/batchConcludeCase")
    public AjaxResult batchConcludeCase(@RequestBody JudgePojo judgePojo){
        int i = iJudgeResultService.batchConcludeCase(judgePojo);
        return AjaxResult.success();
    }
}
