package com.zws.appeal.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 我的审批（外访）查询返回实体类
 */
@Data
public class MyOutsideRecordResp implements Comparable<MyOutsideRecordResp> {

    /**
     * 该案件是否属于本团队(0-不属于,1-属于)
     */
    private Integer button;

    /**
     * 案件id
     */
    private Long caseId;

    /**
     * 申请表id
     */
    private Long applyIds;

    /**
     * 申请人
     */
    private String createBy;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 外访地址
     */
    private String outsideAddress;

    /**
     * 催收员，多个以逗号分隔(外访人员）
     */
    private String odvName;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 处理状态,0-已同意，1-未同意，2-待处理
     */
    private Integer approveStart;

    /**
     * 审核状态
     */
    private String state;

    /**
     * 案件状态 0-未分配，1-已分配，2-停催，3-留案，4-退案 5-回收案件，6-案件结清
     */
    private String caseState;

    /**
     * 委案批号
     */
    private String entrustingCaseBatchNum;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 客户姓名
     */
    private String clientName;

    /**
     * 客户身份证号
     */
    private String clientIdcard;
    /**
     * 证件类型
     */
    private String clientIdType;
    /**
     * 客户户籍
     */
    private String clientCensusRegister;

    /**
     * 委按日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entrustingCaseDate;

    /**
     * 退按日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date returnCaseDate;

    /**
     * 标签内容
     */
    private String labelContent;

    /**
     * 标签表id
     */
    private Integer labelId;

    /**
     * 标签颜色（0-黑色，1-粉色，2-橙色，3-绿色，4-蓝色，5-紫色，6-黄色）
     */
    private Integer label;

    /**
     * 外访时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date outsideStart;

    @Override
    public int compareTo(@NotNull MyOutsideRecordResp o) {
        return (int) (o.getCreateTime().getTime() - this.getCreateTime().getTime());
    }
}
