package com.zws.appeal.controller;

import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.appeal.agservice.ApproveAgService;
import com.zws.appeal.enums.ApproveEnum;
import com.zws.appeal.pojo.ApprovalRecord;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 远程调用服务接口
 *
 * <AUTHOR>
 * @date 2024/5/8 23:30
 */
@Slf4j
@RestController
@RequestMapping("/dispose")
public class FeignController extends BaseController {

    @Autowired
    private ApproveAgService approveAgService;

    /**
     *
     * 远程调用函件审批操作通过
     * @param id 审核记录ID
     * @return
     */
    @RequestMapping(value = "/updateSignRecordPass", method = RequestMethod.POST)
    public AjaxResult updateSignRecordPass(Long id,@RequestBody LoginUser loginUser) {
        List<Long> ids= new ArrayList<>();
        ids.add(id);
        ApprovalRecord approvalRecord=new ApprovalRecord();
        approvalRecord.setIds(ids);
        approvalRecord.setApproveStart(0);
        approvalRecord.setApproveCode(ApproveEnum.LAWAPPROVE.getCode());
        approveAgService.signRecordHandle(approvalRecord, loginUser);
        return AjaxResult.success("审批成功");
    }


}
