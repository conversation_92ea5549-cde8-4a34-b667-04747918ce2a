package com.zws.appeal.controller.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 分配坐席 提交表单
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/25 9:07
 */
@Data
public class AssignSipFormRequest {

    /**
     * 选择的sip账号
     */
    @NotEmpty(message = "请选择坐席账号")
    private List<String> sipNumbers;
    /**
     * 选择的员工id
     */
    @NotEmpty(message = "请选择员工账号")
    private List<Integer> employeeIds;

}
