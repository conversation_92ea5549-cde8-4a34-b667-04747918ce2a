package com.zws.appeal.controller.letterDoc.letter.controller;

import com.zws.common.core.web.domain.AjaxResult;
import com.zws.appeal.controller.letterDoc.letter.service.LetterStageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 文书调诉阶段
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/stage")
public class LetterStageController {

    @Autowired
    private LetterStageService appealStageService;
    /**
     * 调诉阶段数量查询
     */
    @GetMapping("/selectAppealStage")
    public AjaxResult selectAppealStage(){
        List<Map<String, Integer>> list =appealStageService.selectAppealStage();
        return AjaxResult.success("调诉阶段数量查询",list);
    }
    /**
     * 调诉阶段总数查询
     */
    @GetMapping("/selectTageTotal")
    public AjaxResult tageTotal(){
        Integer tageTotal=appealStageService.tageTotal();
        return AjaxResult.success("调诉阶段总数查询",tageTotal);
    }
}
