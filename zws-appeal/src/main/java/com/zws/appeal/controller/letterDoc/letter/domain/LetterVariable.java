package com.zws.appeal.controller.letterDoc.letter.domain;

import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 函件变量
 * <AUTHOR>
 * @date    2023年11月23日17:34:14
 */
@Data
public class LetterVariable extends BaseEntity {
    private Long id;
    /**
     *  参数名称
     */
    @NotEmpty(message = "参数名称不能为空")
    private String variableName;
    /**
     * 状态，0-正常(启用)，1-禁用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
    /**
     * 团队、机构ID
     */
    private Long teamId;

    private List<String> variableNames;

    private Long createById;

    private Long updateById;

    private String delFlag;


}
