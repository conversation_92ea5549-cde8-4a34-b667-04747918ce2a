package com.zws.appeal.controller.appealController;

import com.zws.appeal.domain.appeal.RegisterRecord;
import com.zws.appeal.enums.appeal.DisposeWays;
import com.zws.appeal.enums.appeal.RegisterEnum;
import com.zws.appeal.pojo.appeal.StageFieldPojo;
import com.zws.appeal.service.appeal.RegisterRecordService;
import com.zws.common.core.domain.Option;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 保全、调执、电催登记
 * <AUTHOR>
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/stage/register")
public class RegisterRecordController extends BaseController {

    @Autowired
    private RegisterRecordService recordService;


    /**
     * 查询 保全登记
     * @return
     */
    @GetMapping("/getSaveRecord")
    public TableDataInfo getSaveRecord(@RequestParam("caseId")Long caseId,@RequestParam("webSide")Integer webSide){
        List<RegisterRecord> list = recordService.getRecordByType(caseId, RegisterEnum.BAOQUAN.getCode(),webSide);
        return getDataTable(list);
    }

    /**
     * 查询 调诉登记
     * @param caseId
     * @return
     */
    @GetMapping("/getAppealRecord")
    public TableDataInfo getAppealRecord(@RequestParam("caseId") Long caseId,@RequestParam("webSide")Integer webSide){
        List<RegisterRecord> list = recordService.getRecordByType(caseId, DisposeWays.SUSON.getCode(),webSide);
        return getDataTable(list);
    }

    /**
     * 查询 调执登记
     * @param caseId
     * @return
     */
    @GetMapping("/getPhoneUrgRecord")
    public TableDataInfo getPhoneUrgRecord(@RequestParam("caseId")Long caseId,@RequestParam("webSide")Integer webSide){
        List<RegisterRecord> list = recordService.getRecordByType(caseId, RegisterEnum.DIANCUI.getCode(),webSide);
        return getDataTable(list);
    }

    /**
     * 查询 执行登记
     */
    @GetMapping("/getPursueUrgRecord")
    public TableDataInfo getPursueUrgRecord(@RequestParam("caseId")Long caseId,@RequestParam("webSide")Integer webSide){
        List<RegisterRecord> list = recordService.getRecordByType(caseId,RegisterEnum.ZHIXING.getCode(),webSide);
        return getDataTable(list);
    }

    /**
     * 查询调诉执记录（调执、调诉）
     * @param caseId
     * @param webSide
     * @return
     */
    @GetMapping("/getAppMed")
    public TableDataInfo getAppMed(@RequestParam("caseId")Long caseId,@RequestParam("webSide")Integer webSide){
        List<RegisterRecord> list = recordService.getAppMed(caseId,RegisterEnum.DIANCUI.getCode(),RegisterEnum.SUSON.getCode(),webSide);
        return getDataTable(list);
    }

    /**
     * 添加登记记录
     * @param record
     * @return
     */
    @PostMapping("/addRegisterRecord")
    public AjaxResult addRegisterRecord(@RequestBody RegisterRecord record){
        recordService.addRegisterRecord(record);
        return AjaxResult.success();
    }


    /**
     * 获取各个调执阶段的案件数量（范围：所有调诉机构）
     * @return
     */
    @GetMapping("/getStageCount")
    public AjaxResult getStageCount(Long teamId){
        List<Option> list =recordService.getStageCount(teamId);
        return AjaxResult.success(list);
    }

    /**
     * （团队内 查询）
     * @param teamId
     * @return
     */
    @GetMapping("/getStageCountByTeam")
    public AjaxResult getStageCountByTeam(@RequestParam("teamId") Long teamId,@RequestParam("employeeId") Long employeeId){
        if (employeeId==0L){
            List<Option> list =recordService.getStageCount(teamId);
            return AjaxResult.success(list);
        }
        List<Option> list =recordService.getStageCountByEmployee(teamId,employeeId);
        return AjaxResult.success(list);
    }

    /**
     *我的诉讼案件
     * @param teamId
     * @param employeeId
     * @return
     */
    @GetMapping("/getStageCountByMyCase")
    public AjaxResult getStageCountByMyCase(@RequestParam("teamId") Long teamId,@RequestParam("employeeId") Long employeeId){
        if (employeeId==0L){
            //主账号默认返回 各个阶段为0
            List<Option> list =recordService.getStageCountByTeam();
            return AjaxResult.success(list);
        }
        List<Option> list =recordService.getStageCountByEmployee(teamId,employeeId);
        return AjaxResult.success(list);
    }

    /**
     * 获取 各个阶段名称
     * @Param stageType 阶段类型（保全、调诉）
     */
    @GetMapping("/getStage")
    public AjaxResult getStage(@RequestParam("stageType") Integer stageType){
        List<Option> list = recordService.getStage(stageType);
        return AjaxResult.success(list);
    }

    /**
     * 获取阶段下的 表单参数
     * @param disposeWay 字段管理分类 0催收 1调执 2诉讼 3保全 4调执
     */
    @GetMapping("/getFormParam")
    public AjaxResult getFormParam(Integer disposeWay,String stageTwoName){
        if (disposeWay==null || StringUtils.isEmpty(stageTwoName)){ throw new ServiceException("查询条件不能为空"); }
        List<StageFieldPojo> list = recordService.getFormParam(disposeWay,stageTwoName);
        return AjaxResult.success(list);
    }


}
