package com.zws.appeal.controller.letterDoc.letter.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import com.zws.common.core.web.domain.BaseEntity;
import com.zws.appeal.controller.letterDoc.letter.enums.DocTypes;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 律函-文书
 * <AUTHOR>
 * @date 2023年12月12日11:49:30
 */
@Data
public class LetterDoc extends BaseEntity {
    private Long id;

    /**
     * 案件id
     */
    private Long caseId;
    /**
     * 案件集合
     */
    private List<Long> ids;
    /**
     * 搜索结果全选
     */
    private String condition;
    /**
     * 机构id
     */
    private Long lawAgencyId;
    /**
     * 律函模板id
     */
    private Long letterTemplateId;
    /**
     *  序列号
     */
    @Excel(name = "文书单号",sort=1)
    private String serialNo;
    /**
     *  函件数据
     */
    private String itemData;
    /**
     * 预览url
     */
    private String previewUrl;
    /**
     * 签章后的预览url
     */
    private String signPreviewUrl;
    /**
     *  PDF页数
     */
    private Integer previewPages;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 文件类型
     *  0-诉讼文书，1-调价函，2-保全文书
     * @see DocTypes
     */
    private Integer docType;
    /**
     *  状态，0-待审核，1-审核中，2-已通过，3-未通过
     */
    private Integer status;
    /**
     *  签章状态，0-待签章，1-签章成功，2-签章异常
     */
    private Integer signStatus;
    /**
     *  签章错误信息
     */
    private String signErrMsg;
    /**
     * 签章时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signTime;

    private Long teamId;

    private Long createById;

    private Long updateById;

    private String delFlag;

    /**
     * 客户姓名（非数据库字段）
     */
    private String clientName;

    /**
     * 关联文书模版名称
     */
    @Excel(name = "模板名称",sort=2)
    private String templateName;

    /**
     * 关联文书模版类型
     */
    private String classifyLabel;
    /**
     * 文书的样式
     */
    private String content;

    /**
     * 函件批次id
     */
    private Integer letterId;

    /**
     * 审核进程 0-待审核，1-审核中，2-已结束
     */
    private Integer proce;

    /**
     * 审核进程顺序
     */
    private Integer proceSort;

    /**
     * 最后审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date examineTime;

    /**
     * 最后审核人
     */
    private String examineBy;

    /**
     * 最后审核人id
     */
    private Integer examineById;

    /**
     * 0:Excel类型  1:Zip类型
     * 批量导入的源文件类型
     */
    private Integer sourceFileType;
    /**
     * 生成签章文件后脱敏文件url
     */
    private String desensitizeUrl;

    /**
     * 辨别真伪网址
     * (脱敏文件链接)
     */
    private String fileLink;

    /**
     * 链接访问数量
     */
    private Integer visitNum;

    /**
     * 脱敏文件链接_拼接的随机字符串
     */
    private String randomId;

    /**
     * 邮寄状态:待邮寄0、已邮寄1、已签收2 、已拒收3
     */
    private Integer mailingStatus;

    /**
     * 发送状态(0-发送中,1-发送失败,2-发送成功)
     */
    private Integer sendStatus;

    private String deliveryWay;

    private List<String> deliveryWayList;

    /**
     * 短信发送状态集合
     * 非数据库字段
     */
    private List<Integer> sendStatusList;


}
