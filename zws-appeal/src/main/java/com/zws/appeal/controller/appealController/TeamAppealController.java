package com.zws.appeal.controller.appealController;


import cn.hutool.core.bean.BeanUtil;
import com.zws.appeal.controller.request.MaterialDeliveryRequest;
import com.zws.appeal.controller.response.MaterialDeliveryCountResp;
import com.zws.appeal.controller.response.MaterialDeliveryResp;
import com.zws.appeal.domain.DeliveryRanking;
import com.zws.appeal.pojo.Option;
import com.zws.appeal.pojo.TreeType;
import com.zws.appeal.pojo.appeal.*;
import com.zws.appeal.service.appeal.ITeamAppealService;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 调诉团队模块
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/team")
public class TeamAppealController extends BaseController {

    @Autowired
    private ITeamAppealService teamAppealService;

    /**
     * 团队调解案件数量排名
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/DeptTreeType", method = RequestMethod.GET)
    public AjaxResult selectListWithDeptTreeType(PhoneMediationPojo phoneMediationPojo) {
        List<TreeType> treeTypes = teamAppealService.DeptTreeType(phoneMediationPojo);
        return AjaxResult.success("查询成功", treeTypes);
    }

    /**
     * 团队-网上立案、立案开庭、判决与结果
     * 案件数量排名
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/DeptTreeWithDisposeStage", method = RequestMethod.GET)
    public AjaxResult DeptTreeWithDisposeStage(PhoneMediationPojo phoneMediationPojo) {
        List<TreeType> treeTypes = teamAppealService.DeptTreeWithDisposeStage(phoneMediationPojo);
        return AjaxResult.success("查询成功", treeTypes);
    }

    /**
     * 团队-诉讼执行
     * 案件数量排名
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/DeptTreeWithExecute", method = RequestMethod.GET)
    public AjaxResult DeptTreeWithExecute(PhoneMediationPojo phoneMediationPojo) {
        List<TreeType> treeTypes = teamAppealService.DeptTreeWithExecute(phoneMediationPojo);
        return AjaxResult.success("查询成功", treeTypes);
    }

    /**
     * 团队诉讼保全
     * 案件数量排名
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/DeptTreeWithSaveStage", method = RequestMethod.GET)
    public AjaxResult DeptTreeWithSaveStage(PhoneMediationPojo phoneMediationPojo) {
        List<TreeType> treeTypes = teamAppealService.DeptTreeWithSaveStage(phoneMediationPojo);
        return AjaxResult.success("查询成功", treeTypes);
    }


    /**
     * 查询调解案件
     * @param phoneMediationPojo
     * @return
     */
    @GetMapping("/phoneMediationList")
    public TableDataInfo getPhoneMediationList(PhoneMediationPojo phoneMediationPojo){
        startPage();
        List<PhoneMediationPojo> list = teamAppealService.getPhoneMediation(phoneMediationPojo, SecurityUtils.getLoginUser());
        return getDataTable(list);
    }

    /**
     * 调解案件统计 剩余应还债权金额 计算
     * @return
     */
    @PostMapping("/selectMediationWithMoney")
    public AjaxResult selectMediationWithMoney(@RequestBody PhoneMediationPojo phoneMediationPojo){
        Map<String,Object> map = teamAppealService.selectMediationWithMoney(phoneMediationPojo, SecurityUtils.getLoginUser());
        return AjaxResult.success(map);
    }

    /**
     * 网上立案列表查询
     * 查询不同的小阶段、传参数不同处理
     * @return
     */
    @GetMapping("/selectFillingList")
    public TableDataInfo selectList(FilingCasePojo pojo){
        startPage();
        List<FilingCasePojo> list = teamAppealService.selectFillingList(pojo, SecurityUtils.getLoginUser());
        return getDataTable(list);
    }

    /**
     * 网上立案统计 剩余应还债权金额 计算
     * @return
     */
    @PostMapping("/selectFillingWithMoney")
    public AjaxResult selectWithMoney(@RequestBody FilingCasePojo pojo){
        Map<String,Object> map = teamAppealService.selectWithMoney(pojo,SecurityUtils.getLoginUser());
        return AjaxResult.success(map);
    }

    /**
     * 立案开庭查询
     * @param lawInforPojo
     * @return
     */
    @GetMapping("/getSessionList")
    public TableDataInfo getSessionList(LawInforPojo lawInforPojo){
        startPage();
        List<LawInforPojo> list = teamAppealService.getSessionList(lawInforPojo,SecurityUtils.getLoginUser());
        return getDataTable(list);
    }

    /**
     * 立案开庭统计剩余应还债权金额 计算
     * @param lawInforPojo
     * @return
     */
    @PostMapping("/selectSessionWithMoney")
    public AjaxResult selectSessionWithMoney(@RequestBody LawInforPojo lawInforPojo){
        Map<String,Object> map = teamAppealService.selectSessionWithMoney(lawInforPojo,SecurityUtils.getLoginUser());
        return AjaxResult.success(map);
    }

    /**
     * 立案开庭批量导出
     * @param response
     * @param
     */
    @PostMapping("/exportWithLawInfoCase")
    public void exportWithLawInfoCase(HttpServletResponse response, @RequestBody LawInforPojo lawInforPojo) throws UnsupportedEncodingException {
        List<LawInforPojo> list = teamAppealService.getSessionList(lawInforPojo,SecurityUtils.getLoginUser());
        List<CaseFilingExport> list1 = new ArrayList<>();
        for (LawInforPojo inforPojo : list) {
            CaseFilingExport lawInfoExport = new CaseFilingExport();
            BeanUtil.copyProperties(inforPojo,lawInfoExport);
            list1.add(lawInfoExport);
        }
        ExcelUtil util=new ExcelUtil(CaseFilingExport.class);
        String fileName = "立案开庭.xlsx";
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, list1, "立案开庭");
    }

    /**
     * 诉讼保全列表查询
     * @param pojo
     * @return
     */
    @GetMapping("/selectFreezeList")
    public TableDataInfo selectFreezeList(FreezeCasePojo pojo){
        startPage();
        List<FreezeCasePojo> list  = teamAppealService.selectFreezeList(pojo,SecurityUtils.getLoginUser());
        return getDataTable(list);
    }

    /**
     * 材料寄送统计列表查询
     * @param pojo
     * @return
     */
    @GetMapping("/selectMaterialDeliveryList")
    public TableDataInfo selectMaterialDeliveryList(MaterialDeliveryRequest pojo){
        startPage();
        List<MaterialDeliveryResp> list  = teamAppealService.selectMaterialDeliveryList(pojo,SecurityUtils.getLoginUser());
        return getDataTable(list);
    }

    /**
     * 获取快递状态统计
     * @return
     */
    @GetMapping("/getSum")
    public AjaxResult getSum(MaterialDeliveryRequest pojo){
        List<Option> list = teamAppealService.getSum(SecurityUtils.getLoginUser(),pojo);
        return AjaxResult.success(list);
    }

    /**
     * 团队送达排名
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/materialDeliveryRanking", method = RequestMethod.GET)
    public AjaxResult materialDeliveryRanking() {
        List<DeliveryRanking> treeTypes = teamAppealService.materialDeliveryRanking();
        return AjaxResult.success("查询成功", treeTypes);
    }

    /**
     * 材料寄送统计批量导出
     * @param response
     * @param
     */
    @PostMapping("/exportWithMaterialDelivery")
    public void exportWithMaterialDelivery(HttpServletResponse response, @RequestBody MaterialDeliveryRequest materialDeliveryRequest) throws UnsupportedEncodingException {
        List<MaterialDeliveryResp> list = teamAppealService.selectMaterialDeliveryList(materialDeliveryRequest,SecurityUtils.getLoginUser());
        List<LawInfoExport> list1 = new ArrayList<>();
        for (MaterialDeliveryResp materialDeliveryResp : list) {
            LawInfoExport lawInfoExport = new LawInfoExport();
            BeanUtil.copyProperties(materialDeliveryResp,lawInfoExport);
            list1.add(lawInfoExport);
        }
        ExcelUtil util=new ExcelUtil(LawInfoExport.class);
        String fileName = "材料寄送统计.xlsx";
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, list1, "材料寄送统计");
    }

    /**
     * 材料寄送统计统计剩余应还债权金额 计算
     * @param pojo
     * @return
     */
    @PostMapping("/selectMaterialDeliveryMoney")
    public AjaxResult selectMaterialDeliveryMoney(@RequestBody MaterialDeliveryRequest pojo){
        MaterialDeliveryCountResp materialDeliveryCountResp = teamAppealService.selectMaterialDeliveryMoney(pojo,SecurityUtils.getLoginUser());
        return AjaxResult.success(materialDeliveryCountResp);
    }

    /**
     * 诉讼保全统计剩余应还债权金额 计算
     * @param pojo
     * @return
     */
    @PostMapping("/selectFreezeWithMoney")
    public AjaxResult selectFreezeWithMoney(@RequestBody FreezeCasePojo pojo){
        Map<String,Object> map = teamAppealService.selectFreezeWithMoney(pojo,SecurityUtils.getLoginUser());
        return AjaxResult.success(map);
    }

    /**
     * 诉讼保全批量导出
     * @param response
     * @param
     */
    @PostMapping("/exportFreezeCase")
    public void exportFreezeCase(HttpServletResponse response, @RequestBody  FreezeCasePojo pojo) throws UnsupportedEncodingException {
        List<FreezeCasePojo> list  = teamAppealService.selectFreezeList(pojo,SecurityUtils.getLoginUser());
        ExcelUtil util=new ExcelUtil(FreezeExport.class);
        List<FreezeExport> list1 = new ArrayList<>();
        for (FreezeCasePojo freezeCasePojo : list) {
            FreezeExport freezeExport = new FreezeExport();
            BeanUtil.copyProperties(freezeCasePojo,freezeExport);
            list1.add(freezeExport);
        }
        String fileName = "诉讼保全.xlsx";
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, list1, "诉讼保全");
    }

    /**
     * 判决与结果查询
     * @param judgePojo
     * @return
     */
    @GetMapping("/getJudgeList")
    public TableDataInfo getJudgeList(JudgePojo judgePojo){
        startPage();
        List<JudgePojo> list = teamAppealService.getJudgeList(judgePojo,SecurityUtils.getLoginUser());
        return getDataTable(list);
    }

    /**
     * 判决与结果统计剩余应还债权金额 计算
     * @param judgePojo
     * @return
     */
    @PostMapping("/selectJudgeWithMoney")
    public AjaxResult selectJudgeWithMoney(@RequestBody JudgePojo judgePojo){
        Map<String,Object> map = teamAppealService.selectJudgeWithMoney(judgePojo,SecurityUtils.getLoginUser());
        return AjaxResult.success(map);
    }

    /**
     * 判决与结果批量导出
     * @param response
     * @param
     */
    @PostMapping("/exportWithJudgeCase")
    public void exportWithJudgeCase(HttpServletResponse response, @RequestBody JudgePojo judgePojo) throws UnsupportedEncodingException {
        List<JudgePojo> list = teamAppealService.getJudgeList(judgePojo,SecurityUtils.getLoginUser());
        ExcelUtil util=new ExcelUtil(JudgeExport.class);
        List<JudgeExport> list1 = new ArrayList<>();
        for (JudgePojo pojo : list) {
            JudgeExport judgeExport = new JudgeExport();
            BeanUtil.copyProperties(pojo,judgeExport);
            list1.add(judgeExport);
        }
        String fileName = "判决与结果.xlsx";
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, list1, "判决与结果");
    }

    /**
     * 诉讼执行列表查询
     * @return
     */
    @GetMapping("/selectExecuteList")
    public TableDataInfo selectExecuteList(ExecuteCasePojo pojo){
        startPage();
        List<ExecuteCasePojo> list = teamAppealService.selectExecuteList(pojo,SecurityUtils.getLoginUser());
        return getDataTable(list);
    }

    /**
     * 诉讼执行统计剩余应还债权金额 计算
     * @param pojo
     * @return
     */
    @PostMapping("/selectExecuteMoney")
    public AjaxResult selectExecuteMoney(@RequestBody ExecuteCasePojo pojo){
        Map<String,Object> map = teamAppealService.selectExecuteMoney(pojo,SecurityUtils.getLoginUser());
        return AjaxResult.success(map);
    }

    /**
     * 诉讼执行批量导出
     * @param response
     * @param
     */
    @PostMapping("/exportWithExecuteCase")
    public void exportWithExecuteCase(HttpServletResponse response, @RequestBody  ExecuteCasePojo pojo) throws UnsupportedEncodingException {
        List<ExecuteCasePojo> list = teamAppealService.selectExecuteList(pojo,SecurityUtils.getLoginUser());
        ExcelUtil util=new ExcelUtil(ExecuteExport.class);
        List<ExecuteExport> list1 = new ArrayList<>();
        for (ExecuteCasePojo executeCasePojo : list) {
            ExecuteExport executeExport = new ExecuteExport();
            BeanUtil.copyProperties(executeCasePojo,executeExport);
            list1.add(executeExport);
        }
        String fileName = "诉讼执行.xlsx";
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, list1, "诉讼执行");
    }

}
