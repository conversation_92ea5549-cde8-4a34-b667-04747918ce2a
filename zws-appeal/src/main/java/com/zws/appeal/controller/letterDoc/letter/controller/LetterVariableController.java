package com.zws.appeal.controller.letterDoc.letter.controller;

import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterVariable;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterVariableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文书参数
 * <AUTHOR>
 * @date ：Created in 2023/11/23 20:48
 *
 */
@CrossOrigin
@RestController
@RequestMapping("/letter/variable")
public class LetterVariableController extends BaseController {

    @Autowired
    private ILetterVariableService variableService;

    /**
     * 查询列表
     * @param params
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo list(LetterVariable params){
        startPage();
        params.setTeamId(BaseConstant.ZCD_TEAM_ID);
        List<LetterVariable> variables = this.variableService.selectList(params);
        return getDataTable(variables);
    }

    /**
     * 查询全部开启状态列表
     * @param params
     * @return
     */
    @GetMapping("/listAll")
    public TableDataInfo listAll(LetterVariable params){
        params.setStatus(BaseConstant.STATUS_OPEN);
        params.setTeamId(BaseConstant.ZCD_TEAM_ID);
        List<LetterVariable> variables = this.variableService.selectList(params);
        return getDataTable(variables);
    }


    /**
     * 新增
     * @param record
     * @return
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody @Validated LetterVariable record){
        record.setTeamId(BaseConstant.ZCD_TEAM_ID);
        this.variableService.insert(record);
        return AjaxResult.success();
    }

    /**
     * 编辑
     * @param record
     * @return
     */
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody @Validated LetterVariable record){
        this.variableService.updateById(record);
        return AjaxResult.success();
    }

    /**
     * 编辑状态
     * @param record
     * @return
     */
    @PostMapping("/editStatus")
    public AjaxResult editStatus(@RequestBody LetterVariable record){
        LetterVariable temp=new LetterVariable();
        temp.setStatus(record.getStatus());
        temp.setId(record.getId());
        this.variableService.updateById(temp);
        return AjaxResult.success();
    }

    /**
     * 删除
     * @param record
     * @return
     */
    @PostMapping("/remove")
    public AjaxResult remove(@RequestBody LetterVariable record){
        this.variableService.deleteById(record.getId());
        return AjaxResult.success();
    }



}
