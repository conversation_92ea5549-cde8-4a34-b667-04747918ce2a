package com.zws.appeal.controller.letterDoc.letter.mapper;

import com.zws.appeal.controller.letterDoc.letter.domain.LetterVariable;

import java.util.List;

public interface LetterVariableMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LetterVariable record);


    LetterVariable selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LetterVariable record);

    /**
     * 查询列表
     * @param record
     * @return
     */
    List<LetterVariable> selectList(LetterVariable record);
}
