package com.zws.appeal.controller.letterDoc.law.service;


import com.zws.appeal.controller.letterDoc.law.domain.LawAgency;

import java.util.List;
import java.util.Map;

/**
 * 法院机构 业务层
 * <AUTHOR>
 * @date    2023年12月10日17:06:15
 *
 */
public interface ILawAgencyService {

    /**
     * 新增
     * @param record
     */
    Long insert(LawAgency record);

    /**
     * 主键 选择更新
     * @param record
     */
    void updateById(LawAgency record);

    /**
     * 主键删除
     * @param id
     */
    void deleteById(Long id);

    /**
     * 主键id查询
     * @param id
     * @return
     */
    LawAgency getById(Long id);


    /**
     * 条件查询
     * @param record
     * @return
     */
    List<LawAgency> selectList(LawAgency record);

    /**
     * 条件查询
     * @param record
     * @return
     */
    List<LawAgency> selectLists(LawAgency record);

    /**
     * 获取机构名称下拉框
     * @return
     */
    List<LawAgency> getCourtNameOptions();

    LawAgency selectCourtManageById(Long id);

    /**
     * 获取机构 id、法院名称
     * @param teamId
     * @return
     */
    Map<String,Long> selectWithLawAgency(Long teamId);
}
