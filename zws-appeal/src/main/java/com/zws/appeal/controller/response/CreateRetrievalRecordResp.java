package com.zws.appeal.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 团队申请资料调取查询返回实体类
 *
 * @Author: 马博新
 * @DATE: Created in 2023/2/10 11:24
 */
@Data
public class CreateRetrievalRecordResp implements Serializable {

    /**
     * 申请表id
     */
    private Long id;

    /**
     * 案件id
     */
    private Long caseId;

    /**
     * 客户姓名
     */
    private String clientName;

    /**
     * 客户身份证号
     */
    private String clientIdcard;

//    /**
//     * 证件类型
//     */
//    private String clientIdType;

    /**
     * 委案批号
     */
    private String entrustingCaseBatchNum;

    /**
     * 产品名称
     */
    private String productName;

//    /**
//     * 委托金额
//     */
//    private BigDecimal clientMoney;

//    /**
//     * 委按日期
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date entrustingCaseDate;

//    /**
//     * 催收员名称
//     */
//    private String odvName;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyDate;

    /**
     * 审核状态
     */
    private String examineState;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date examineTime;

    /**
     * 该案件是否属于本团队(0-不属于,1-属于)
     */
    private Integer button;

    /**
     * 查看文件链接
     */
    private String path;

    /**
     * 文件是否过期（0-未过期，1-已过期）
     */
    private Integer fileExpiration;

    /**
     * 能否下载（1-是，2-否）
     */
    private Integer canDownload;

    /**
     * 生成随机查看码
     */
    private String random;
}
