package com.zws.appeal.controller.letterDoc.law.service.impl;

import com.zws.appeal.controller.letterDoc.law.domain.LawAgency;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.controller.letterDoc.law.mapper.LawAgencyMapper;
import com.zws.appeal.controller.letterDoc.law.service.ILawAgencyLetterService;
import com.zws.appeal.controller.letterDoc.law.service.ILawAgencyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 法院机构 业务层
 * <AUTHOR>
 * @date 2023年12月10日17:09:45
 */
@Service
@Transactional
public class LawAgencyServiceImpl implements ILawAgencyService {

    @Autowired
    private LawAgencyMapper lawAgencyMapper;
    @Autowired
    private ILawAgencyLetterService lawAgencyLetterService;

    @Override
    public Long insert(LawAgency record) {
        record.setId(null);
        record.setCreateBy(SecurityUtils.getUsername());
        record.setCreateById(SecurityUtils.getUserId());
        record.setCreateTime(new Date());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        record.setTeamId(SecurityUtils.getTeamId());
        lawAgencyMapper.insert(record);
//        lawAgencyLetterService.insert(record.getId(),record.getLetterTemplateIds());
        lawAgencyLetterService.insert(record.getId(),record.getIds());
        return record.getId();
    }

    @Override
    public void updateById(LawAgency record) {
        record.setCreateBy(null);
        record.setCreateById(null);
        record.setCreateTime(null);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        this.lawAgencyMapper.updateByPrimaryKeySelective(record);
//        lawAgencyLetterService.insert(record.getId(),record.getLetterTemplateIds());
        lawAgencyLetterService.insert(record.getId(),record.getIds());
    }

    @Override
    public void deleteById(Long id) {
        LawAgency record=new LawAgency();
        record.setId(id);
        record.setDelFlag(BaseConstant.DelFlag_Delete);
        this.updateById(record);
    }

    @Override
    public LawAgency getById(Long id) {
        return this.lawAgencyMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<LawAgency> selectList(LawAgency lawAgency) {
        lawAgency.setTeamId(SecurityUtils.getTeamId());
        List<LawAgency> lawAgencies = lawAgencyMapper.selectList(lawAgency);
        if (!ObjectUtils.isEmpty(lawAgencies)) {
            for (LawAgency row : lawAgencies) {
//                查询法院关联文书数量
                List<Long> list = lawAgencyMapper.selectCourtId(row.getId());
                row.setNumber(list.size());
                row.setIds(list);
            }
        }
        return lawAgencies;
    }

    @Override
    public List<LawAgency> selectLists(LawAgency record) {
        List<LawAgency> lawAgencies = lawAgencyMapper.selectList(record);
        if (!ObjectUtils.isEmpty(lawAgencies)) {
            for (LawAgency row : lawAgencies) {
//                查询法院关联文书数量
                List<Long> list = lawAgencyMapper.selectCourtId(row.getId());
                row.setNumber(list.size());
                row.setIds(list);
            }
        }
        return lawAgencies;
    }

    /**
     * 获取机构名称下拉框
     * @return
     */
    @Override
    public List<LawAgency> getCourtNameOptions() {
        LawAgency lawAgency = new LawAgency();
        lawAgency.setTeamId(SecurityUtils.getTeamId());
        List<LawAgency> list = lawAgencyMapper.getCourtNameOptions(lawAgency.getTeamId());
        return list;
    }

    @Override
    public LawAgency selectCourtManageById(Long id) {
        LawAgency lawAgency = lawAgencyMapper.selectByPrimaryKey(id);
        if (!ObjectUtils.isEmpty(lawAgency)) {
//            查询法院关联文书数量
            List<Long> list = lawAgencyMapper.selectCourtId(lawAgency.getId());
            lawAgency.setNumber(list.size());
            lawAgency.setIds(list);
        }
        return lawAgency;
    }

    @Override
    public Map<String,Long> selectWithLawAgency(Long teamId) {
        List<LawAgency> agency = lawAgencyMapper.selectWithLawAgency(teamId);
        Map<String, Long> collect = agency.stream().collect(Collectors.toMap(LawAgency::getCourtName, LawAgency::getId, (k1, k2) -> k2));
        return collect;
    }
}
