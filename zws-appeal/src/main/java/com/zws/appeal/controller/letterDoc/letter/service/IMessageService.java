package com.zws.appeal.controller.letterDoc.letter.service;

import com.zws.common.core.enums.MessageFormats;
import org.springframework.scheduling.annotation.Async;

/**
 * 消息提醒相关服务
 */
public interface IMessageService {



    /**
     * 推送消息提醒
     *
     * @param messageFormats 消息类型
     * @param identification 账号类型
     * @param userId         用户id\员工id\团队id
     */
    @Async
    void pushReminder(MessageFormats messageFormats, Integer identification, Long userId, Integer createId);


}
