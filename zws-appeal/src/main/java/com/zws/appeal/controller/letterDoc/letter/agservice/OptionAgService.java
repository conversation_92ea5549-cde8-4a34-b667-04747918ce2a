package com.zws.appeal.controller.letterDoc.letter.agservice;

import com.zws.appeal.controller.letterDoc.law.enums.SignTypeEnum;
import com.zws.common.core.domain.Option;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.controller.letterDoc.letter.mapper.LawMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/14 2:01
 */
@Slf4j
@Component
public class OptionAgService {

    @Autowired
    private LawMapper lawMapper;

    /**
     * 律所选项
     * @return
     */
    public List<Option> getLawOption(){
        return lawMapper.selectLawOptions();
    }

    /**
     * 获取公司章
     * @return
     */
    public List<Option> getCompanyOption(){
//        Long tenantId= SecurityUtils.getTeamId();
//        log.info("团队ID："+tenantId);
//        return lawMapper.selectAttorneyOptions(tenantId.intValue(), SignTypeEnum.COMPANY.getCode());
        return lawMapper.selectAttorneyOptions();
    }

    /**
     * 获取代理律师选项
     * @return
     */
    public List<Option> getAttorneyOption(){
        Long tenantId= SecurityUtils.getTeamId();
        log.info("团队ID："+tenantId);
        return lawMapper.selectAttorneyOptions();
    }


    /**
     *根据主键id获取签章图片
     * @return
     */
    public List<Option> getCompanySignature(Long id) throws IOException {
        List<Option> option = lawMapper.selectSignatureOptions(id);
//        for (Option option1 : option) {
//            if (option1.getInfo()!=null) {
//                option1.setInfo(PDFFileUtils.getImgBase(option1.getInfo()));
//            }
//        }
        return option;
    }

    /**
     * 获取当前律所名称
     * @return
     */
    public String getLawName(){
        Long tenantId= SecurityUtils.getTeamId();
        return   lawMapper.getLawName(tenantId.intValue());
    }

    /**
     * 获取团队名称
     * @param tenantId
     * @return
     */
    public String getLawName(Integer tenantId){
        return   lawMapper.getLawName(tenantId);
    }

}
