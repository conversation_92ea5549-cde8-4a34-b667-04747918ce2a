package com.zws.appeal.controller.letterDoc.letter.service;

import com.zws.appeal.controller.letterDoc.law.domain.LawSign;
import com.zws.common.core.domain.Option;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;

/**
 * 函件模板
 * <AUTHOR>
 * @date ：Created in 2023/11/23 20:26
 */
public interface ILetterTemplateService {

    Long insert(LetterTemplate record);

    void updateById(LetterTemplate record);

    void deleteById(Long id);

    LetterTemplate getById(Long id);

    /**
     * 检查名称是否唯一
     * @param template
     * @return true-未使用，false-已使用
     */
    boolean checkUniqueName(LetterTemplate template);
    /**
     * 查询列表
     * @param record
     * @return
     */
    List<LetterTemplate> selectList(LetterTemplate record);
    /**
     * 获取选项
     * @return
     */
    List<Option> getOptions(LetterTemplate record);

    List<LetterTemplate> selectList1(LetterTemplate template);

    /**
     * 获取模板内容
     * @param template
     * @param isPreview 是否预览
     * @return
     */
    String getContent(LetterTemplate template,boolean isPreview);

    /**
     * 获取案件基础信息 作为预览文件替换值
     * @param caseId
     * @return
     */
    HashMap<String, Object> getCaseInfo(Long caseId);

    /**
     * 根据文件流裁剪
     * @param headerPath
     * @param footerPath
     * @return
     */
    public  String cutPictureByStream(String headerPath,String footerPath);


    int updateEditStatus(LetterTemplate entity);

    InputStream getPdfBase64(Long id) throws IOException;

    LetterTemplate selectListById(LetterTemplate template);

    LetterTemplate getByIdA(Long id);

    InputStream createTemplatePdf(String url) throws IOException;

    /**
     * 获取模板的变量
     * @param id 模板id
     * @return
     */
    List<String> getTemplateVariable(Integer id);

    /**
     * 获取签章图片base64
     * @param signId 主键id
     * @return
     */
    LawSign getSignPicBase64(Integer signId);

    /**
     * 更新签章图片对象
     * @param lawSign
     * @return
     */
    int updateLawSignById(LawSign lawSign);
}
