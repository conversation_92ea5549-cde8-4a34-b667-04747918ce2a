package com.zws.appeal.controller.letterDoc.letter.controller;



import com.alibaba.fastjson2.TypeReference;
import com.zws.appeal.constant.LetterConstants;
import com.zws.appeal.controller.letterDoc.letter.domain.DocumentMessage;
import com.zws.appeal.controller.letterDoc.letter.service.IDocumentMessageService;
import com.zws.appeal.enums.ProceEnum;
import com.zws.appeal.service.approve.ThreadServie;
import com.zws.appeal.utils.FileUpload;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;

import com.zws.common.redis.service.RedisService;

import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 函件批次
 *
 * <AUTHOR>
 * @since 2023-01-04 20:38
 */
@CrossOrigin
@RestController
@RequestMapping("/document/message")
public class DocumentMessageController extends BaseController {

    @Autowired
    private IDocumentMessageService documentMessageService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ThreadServie threadServie;


    @GetMapping("/list")
    public TableDataInfo list(DocumentMessage message) {
        List<DocumentMessage> list = documentMessageService.selectList(message,false);
        if (message.getPageNum()!=null && message.getPageSize()!=null){
            return PageUtils.getDataTable(list,message.getPageNum(),message.getPageSize());
        }
        return getDataTable(list);
    }

    /**
     * 校验文件
     *
     * @param message 需要模板id、文件路径
     * @return
     */
    @GetMapping("/verifyFile")
    public AjaxResult verifyFile(DocumentMessage message) {
//        if (StringUtils.isEmpty(message.getDeliveryWay())){return AjaxResult.error("寄送方式不能为空");}
        Map<String, Object> param = documentMessageService.verifyFile(message);
        return AjaxResult.success(param);
    }

    /**
     * 新建文书
     *
     * @param message
     * @return
     */
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody DocumentMessage message) {
        String key = LetterConstants.RK_LETTER_VERIFY_FILE + message.getVerifyId();
        if (!redisService.hasKey(key)) {
            throw new ServiceException("未校验文件或者数据已过期，请重新导入文件");
        }
        List<Map<String, Object>> rows = redisService.getCacheList(key, new TypeReference<Map<String, Object>>() {});
        List<Long> ids = documentMessageService.insert(message,rows,SecurityUtils.getLoginUser());
        //签章
        threadServie.createSignFile(ids,"1", SecurityUtils.getLoginUser());
        return AjaxResult.success();
    }

//    @GetMapping("/test/{id}")
//    public AjaxResult test(@PathVariable("id")Long id){
//        List<Long> ids = new ArrayList<>();
//        ids.add(id);
//        threadServie.createSignFile(ids,"1");
//        return AjaxResult.success();
//    }

    /**
     * 校验zip压缩包文件
     * @param message
     * @return
     */
    @PostMapping("/verifyZipFile")
    public AjaxResult verifyZipFile(@RequestBody DocumentMessage message) {
        if (StringUtils.isEmpty(message.getSourceFileUrl())){return AjaxResult.error("源文件路径不能为空");}
//        if (!message.getSourceFileUrl().toLowerCase().endsWith(".zip")){return AjaxResult.error("文件格式仅支持.zip");}
        String substring = StringUtils.substring(message.getSourceFileUrl(), message.getSourceFileUrl().lastIndexOf(".") + 1);
        if (!substring.startsWith("zip")) {
            return AjaxResult.error("文件格式仅支持.zip");
        }
        Map<String, Object> param = documentMessageService.verifyZipFile(message);
        return AjaxResult.success(param);
    }


    /**
     * 新建文书(压缩包pdf)
     * @param message
     * @return
     */
    @PostMapping("/addZip")
    public AjaxResult addZip(@Validated @RequestBody DocumentMessage message) {
        if (StringUtils.isEmpty(message.getDeliveryWay())){return AjaxResult.error("寄送方式不能为空");}
        String key = LetterConstants.RK_LETTER_VERIFY_FILE + message.getVerifyId();
        if (!redisService.hasKey(key)) {
            throw new ServiceException("未校验文件或者数据已过期，请重新导入文件");
        }
        String cachePath = redisService.getCacheObject(key, String.class);
        redisService.deleteObject(key);
        documentMessageService.addByZip(message,cachePath,SecurityUtils.getLoginUser());
        return AjaxResult.success();
    }

//    /**
//     * 上传zip压缩文件
//     * @param file
//     * @return
//     */
//    @PostMapping("/upload")
//    public AjaxResult uploadZip(MultipartFile[] file) {
//        String[] list = new String[]{".zip",".pdf",".xlsx",".xls"};
//
//        MultipartFile multipartFile = file[0];
//        String originalFilename = multipartFile.getOriginalFilename();
//        if (originalFilename.toLowerCase().endsWith(".xlsx") || originalFilename.toLowerCase().endsWith(".xls")){
//            long size = multipartFile.getSize();
//            double fileSize;
//            fileSize = (double) size / 1048576;
//            if (fileSize > 1) { return AjaxResult.error("Excel文件过大，请重新选择上传文件"); }
//        }
//
//        Map<String, Object> map = fileUpload.uploadFile(file, "analysisZipFile", list,null);
//        return AjaxResult.success(map);
//    }


    /**
     * 导出
     *
     * @param response
     */
    @PostMapping("/export")
    public AjaxResult export(HttpServletResponse response, @RequestBody DocumentMessage message) throws IOException {
        //导出律师函：导出勾选（复选框）的批次的已经通过的函件，以文档打包的形式导出（律所函以pdf的格式导出-导出不能编辑）；
        List<DocumentMessage> list= documentMessageService.selectExportZip(message.getIds());
        List<Map<String,String>> maps=new ArrayList<>();
        for (DocumentMessage temp:list) {
            Map<String,String> map=new HashMap<>();
            if (temp.getStatus()== ProceEnum.REVIEWED_ING.getCode()){
                throw new ServiceException(temp.getBatchNum()+",批次案件审批中不可导出");
            }

            System.out.println(temp.getStatus());
            System.out.println(temp.getSourceFileUrl());
            System.out.println(temp.getZipFileUrl());
            String url=null;
            url=temp.getZipFileUrl();
            if(ObjectUtils.isEmpty(url)){
                url=temp.getNotZipFileUrl();
            }
//            if (temp.getStatus().equals(ProceEnum.TO_BE_REVIEWED.getCode())){
//                url=temp.getNotZipFileUrl();
//            }
//            if (temp.getStatus().equals(ProceEnum.END_REVIEW.getCode())){
//                if (StringUtils.isEmpty(temp.getZipFileUrl())){
//                    //异步执行生成压缩文件(如果 调用方与被调用方不能在同一个类 @Async会失效)
//                    documentMessageService.createZipFile(temp.getId());
//                    throw new ServiceException(temp.getBatchNum()+"未生成压缩文件,暂无法导出,请稍后再试");
//                }
//                url=temp.getZipFileUrl();
//            }
            map.put("fileName",temp.getBatchNum()+".zip");
            map.put("fileUrl",url);
            maps.add(map);
        }
        return AjaxResult.success(maps);
    }

    /**
     * 获取律函的状态
     *
     * @return
     */
    @GetMapping("/getProceOptions")
    public AjaxResult getProceOptions() {
        return AjaxResult.success(ProceEnum.getOptions());
    }

}
