package com.zws.appeal.controller;

import cn.hutool.core.date.DateUtil;
import com.zws.common.core.constant.FileConstant;
import com.zws.common.core.enums.OrganizationTypeEnum;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresPermissions;
import com.zws.appeal.agservice.AgSettingsService;
import com.zws.appeal.agservice.AgTeamService;
import com.zws.appeal.domain.*;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.pojo.*;
import com.zws.appeal.service.EvaluationFormService;
import com.zws.appeal.service.RecordService;
import com.zws.appeal.service.SettingsService;
import com.zws.appeal.service.TeamService;
import com.zws.appeal.utils.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

/**
 * 调诉端(团队)Controller
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@CrossOrigin   //注解跨域
@RestController
@RequestMapping(value = "/create")
public class TeamController extends BaseController {

    @Autowired
    private TeamService teamService;
    @Autowired
    private AgTeamService agTeamService;
    @Autowired
    private SettingsService settingsService;
    @Autowired
    private AgSettingsService agSettingsService;
    @Autowired
    private EvaluationFormService evaluationFormService;
    @Autowired
    private RecordService recordService;


    /**
     * 根据团队id查询对应的状态控制表/数据脱敏详情状态表/水印设置字段表
     *
     * @return
     */
    @RequestMapping(value = "/selectId/{createId}", method = RequestMethod.GET)
    public AjaxResult info(@PathVariable("createId") int createId) {
        CreateLabel createLabel = agTeamService.selectCreateId(createId);
        return AjaxResult.success(createLabel);
    }

    /**
     * 创建团队以及标签设置和白名单设置
     *
     * @param createLabel
     * @return
     */
    @RequiresPermissions("team:list:add")
    @Log(title = "机构管理（创建团队以及标签设置和白名单设置）", businessType = BusinessType.INSERT)
    @RequestMapping(value = "/team", method = RequestMethod.POST)
    public AjaxResult add(@Validated @RequestBody CreateLabel createLabel) {
        agTeamService.addCreateLabel(createLabel);
        return AjaxResult.success("添加成功");
    }

    /**
     * 修改团队信息
     *
     * @return
     */
    @Log(title = "机构管理（修改团队信息）", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/create", method = RequestMethod.PUT)
    public AjaxResult edit(@RequestBody Create create) {
        if (ObjectUtils.isEmpty(create)) {
            return AjaxResult.error("团队信息不能为空");
        }
        agTeamService.updateCreateState(create);
        return AjaxResult.success("已修改成功，该账户需重新登录系统即可更新！");
    }

    /**
     * 根据id批量删除白名单
     *
     * @param white
     * @return
     */
    @Log(title = "机构管理（批量删除白名单）", businessType = BusinessType.DELETE)
    @RequestMapping(value = "/deleteWhite", method = RequestMethod.PUT)
    public AjaxResult remove(@RequestBody List<White> white) {
        teamService.deleteWhite(white);
        return AjaxResult.success("删除成功");
    }

    /**
     * 查询团队信息(全查)
     *
     * @return
     */
    @RequestMapping(value = "/select", method = RequestMethod.GET)
    public AjaxResult selectList() {
        List<Create> allCreate = teamService.findAllCreate();
        return AjaxResult.success("查询成功", allCreate);
    }

    /**
     * 根据条件查询团队合作状态修改记录表
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/selectCooperation", method = RequestMethod.GET)
    public TableDataInfo selectListWithCooperation(Cooperation cooperation) {
        startPage();
        List<Category> list = teamService.selectCooperation(cooperation);
        return getDataTable(list);
    }

    /**
     * 根据字段查询员工信息
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/selectEmployees", method = RequestMethod.GET)
    public TableDataInfo selectListWithEmployees(Employees employees) {
        startPage();
        List<Employees> employees1 = settingsService.selectDeptFuzzyByCreateId(employees);
        return getDataTable(employees1);
    }


    /**
     * 返回部门列表树类型信息
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/selectDeptTreeType/{createId}", method = RequestMethod.GET)
    public AjaxResult selectListWithDeptTreeType(@PathVariable("createId") int createId) {
        List<TreeType> treeType = agTeamService.DeptTreeType(createId);
        return AjaxResult.success("查询成功", treeType);
    }

    /**
     * 根据团队id/字段模糊查询团队白名单信息
     *
     * @return
     */
    @RequestMapping(value = "/selectWhite", method = RequestMethod.GET)
    public TableDataInfo selectListWithWhite(White white) {
        startPage();
        List<White> whites = teamService.selectWhiteVague(white);
        return getDataTable(whites);
    }

    /**
     * 根据团队id查询对应的团队信息/标签信息/文件信息
     *
     * @return
     */
    @RequestMapping(value = "/selectOne/{id}", method = RequestMethod.GET)
    public AjaxResult infoWithCreateLabel(@PathVariable("id") int id) {
        CreateLabel createLabel = agTeamService.selectCreateLabelId(id);
        return AjaxResult.success(createLabel);
    }

    /**
     * 根据字段条件查询团队表信息
     *
     * @param query
     * @return
     */
    @RequestMapping(value = "/selectState", method = RequestMethod.GET)
    public TableDataInfo selectListWithCreate(Query query) {
        startPage();
        List<Create> accurate = teamService.findAccurate(query);
        return getDataTable(accurate);
    }

    /**
     * 分组查询合作状态数量
     *
     * @return
     */
    @RequestMapping(value = "/selectGrouping", method = RequestMethod.GET)
    public AjaxResult selectMapWithGrouping() {
        Map map = teamService.selectCount();
        return AjaxResult.success("查询成功", map);
    }


    /**
     * 根据团队id查询审批设置
     *
     * @return
     */
    @RequestMapping(value = "/selectApprovalSettings/{createId}", method = RequestMethod.GET)
    public AjaxResult selectListWithApprovalSettings(@PathVariable("createId") int createId) {
        List<ApprovalSettings> list = teamService.selectApprovalSettings(createId);
        return AjaxResult.success(list);
    }


    /**
     * 根据时间区间分组查询每个团队的评价信息
     *
     * @return
     */
    @RequestMapping(value = "/selectEvaluate", method = RequestMethod.GET)
    public TableDataInfo selectListWithEvaluate(TimeIntervalPojo timeIntervalPojo) {
        if (ObjectUtils.isEmpty(timeIntervalPojo.getRecordTime1()) || ObjectUtils.isEmpty(timeIntervalPojo.getRecordTime2())) {
            throw new GlobalException("请输入正确的时间区间");
        }
        startPage();
        List<Evaluate> evaluates = teamService.selectEvaluate(timeIntervalPojo);
        return getDataTable(evaluates);
    }

    /**
     * 编辑机构安全设置-导出设置-按钮开关
     *
     * @param teamExport
     * @return
     */
    @RequestMapping(value = "/updateTeamExport", method = RequestMethod.PUT)
    public AjaxResult editTeamExport(@RequestBody TeamExport teamExport) {
        agTeamService.updateTeamExport(teamExport);
        return AjaxResult.success("修改成功");
    }

    /**
     * 根据时间区间以及团队id获取回款率评分
     *
     * @return
     */
    @RequestMapping(value = "/selectTeamIdEvaluate", method = RequestMethod.GET)
    public AjaxResult selectTeamIdEvaluate(TimeIntervalPojo timeIntervalPojo) {
        if (ObjectUtils.isEmpty(timeIntervalPojo.getTeamId())) {
            throw new GlobalException("团队id不能为空");
        }
        if (ObjectUtils.isEmpty(timeIntervalPojo.getRecordTime1()) || ObjectUtils.isEmpty(timeIntervalPojo.getRecordTime2())) {
            throw new GlobalException("请输入正确的时间区间");
        }
        //一天的开始，结果：2017-03-01 00:00:00
        Date beginOfDay = DateUtil.beginOfDay(timeIntervalPojo.getRecordTime1());
        timeIntervalPojo.setRecordTime1(beginOfDay);
        //一天的结束，结果：2017-03-01 23:59:59
        Date endOfDay = DateUtil.endOfDay(timeIntervalPojo.getRecordTime2());
        timeIntervalPojo.setRecordTime2(endOfDay);

        Map<String, Object> map3 = new HashMap<>();
//        Date monthBegins = TimeUtils.getMonthBegins();
        map3.put("updateTime1", beginOfDay);
        map3.put("updateTime2", endOfDay);
        map3.put("teamId", timeIntervalPojo.getTeamId());
        map3.put("examineState", "已通过");
//        查询每个团队当月已通过的回款申请回款金额统计
        BigDecimal bigDecimal1 = recordService.selectRepaymentByTime(map3);
//        查询统计时间段内委案时的剩余应还债权总额
        map3.put("examineState", null);
        BigDecimal bigDecimal2 = recordService.selectHistoryMoney(map3);
        BigDecimal bigDecimal = BigDecimal.ZERO;  //月回款率
        if (bigDecimal1.compareTo(BigDecimal.ZERO) == 0 || bigDecimal2.compareTo(BigDecimal.ZERO) == 0) {
            bigDecimal = BigDecimal.ZERO;
        } else {
            bigDecimal = bigDecimal1.divide(bigDecimal2, 10, BigDecimal.ROUND_HALF_UP);
        }
//        BigDecimal bigDecimal = teamService.selectTeamIdEvaluate(timeIntervalPojo);
        BigDecimal fraction = BigDecimal.ZERO;  //分数
        if (bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == 0) {
            fraction = BigDecimal.ZERO;
        } else if (bigDecimal.compareTo(new BigDecimal(100)) >= 0) {
            fraction = new BigDecimal(40);
        } else {
            BigDecimal multiply = bigDecimal.multiply(new BigDecimal(40));
            fraction = multiply.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return AjaxResult.success(fraction);
    }

    /**
     * 写入机构评价记录
     *
     * @return
     */
    @RequiresPermissions("team:list:create")
    @Log(title = "机构管理（写入机构评价记录）", businessType = BusinessType.INSERT)
    @RequestMapping(value = "/insertEvaluationForm", method = RequestMethod.POST)
    public AjaxResult addEvaluationForm(@RequestBody EvaluationPojo evaluationPojo) {
//        验证参数
        agTeamService.inspectParameter(evaluationPojo);
//        写入机构评价
        agTeamService.agencyRating(evaluationPojo);
        return AjaxResult.success();
    }

    /**
     * 根据条件查询机构评价记录
     *
     * @return
     */
    @RequestMapping(value = "/selectEvaluationForm", method = RequestMethod.GET)
    public TableDataInfo selectListWithEvaluationForm(EvaluationFormPojo evaluationFormPojo) {
        startPage();
        List<EvaluationForm> evaluationForms = evaluationFormService.selectEvaluationForm(evaluationFormPojo);
        return getDataTable(evaluationForms);
    }

    /**
     * 导出机构评价记录
     *
     * @param evaluationFormPojo
     */
    @RequiresPermissions("team:list:export")
    @Log(title = "机构管理（导出机构评价记录）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public void export(EvaluationFormPojo evaluationFormPojo, HttpServletResponse response) throws UnsupportedEncodingException {
        List<EvaluationForm> evaluationForms = evaluationFormService.selectEvaluationForm(evaluationFormPojo);
        ExcelUtil<EvaluationForm> util = new ExcelUtil<EvaluationForm>(EvaluationForm.class);
        String fileName = "机构评价记录" + FileConstant.getExcelSuffix();
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, evaluationForms, "机构评价记录");
    }

    /**
     * 根据团队id查找团队审批设置的角色信息
     *
     * @return
     */
    @RequestMapping(value = "/selectRole/{createId}", method = RequestMethod.GET)
    public AjaxResult selectListWithRole(@PathVariable("createId") int createId) {
        List<Role> roles = teamService.selectRole(createId);
        return AjaxResult.success(roles);
    }

    /**
     * 根据团队id以及审批类型查询审批设置流程详情
     *
     * @return
     */
    @RequestMapping(value = "/selectApprovalSteps", method = RequestMethod.GET)
    public AjaxResult selectListWithApprovalSettings(ApprovalSteps approvalSteps) {
        List<ApprovalSteps> approvalSteps1 = teamService.selectApprovalSteps(approvalSteps);
        return AjaxResult.success(approvalSteps1);
    }

    /**
     * 根据角色信息查询员工或者团队
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/selectEmployeesRole", method = RequestMethod.GET)
    public AjaxResult selectListWithEmployeesRole(Employees employees) {
        List<ReturnInformation> list = agSettingsService.selectRole(employees);
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 根据团队id查询团队员工实名认证信息
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/selectAuthentication", method = RequestMethod.GET)
    public TableDataInfo selectListWithAuthentication(Authentication authentication) {
        startPage();
        List<Authentication> authentications = teamService.selectAuthentication(authentication);
        return getDataTable(authentications);
    }

    /**
     * 人工通过员工实名认证
     *
     * @param authentication
     * @return
     */
    @Log(title = "机构管理（人工通过员工实名认证）", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/manualCertification", method = RequestMethod.POST)
    public AjaxResult manualCertification(@RequestBody List<Authentication> authentication) {
        agTeamService.manualCertification(authentication);
        return AjaxResult.success("认证成功");
    }


    /**
     * 根据字典表类型查询字典表标签
     *
     * @return
     */
    @RequestMapping(value = "/selectDictData", method = RequestMethod.GET)
    public AjaxResult selectListWithDictData() {
        List<String> list = teamService.selectDictData();
        return AjaxResult.success(list);
    }

    /**
     * 验证创建团队的团队信息
     *
     * @param create
     * @return
     */
    @RequestMapping(value = "/verification", method = RequestMethod.POST)
    public AjaxResult verification(@Validated @RequestBody Create create) {
        teamService.yanzheng(create);
        return AjaxResult.success("信息验证成功");
    }

    /**
     * 新增团队文件信息
     *
     * @param files
     * @return
     */
    @Log(title = "机构管理（新增团队文件信息）", businessType = BusinessType.INSERT)
    @RequestMapping(value = "/addFiles", method = RequestMethod.POST)
    public AjaxResult addFiles(@RequestBody List<Files> files) {
        teamService.insertFile(files);
        return AjaxResult.success("添加成功");
    }

    /**
     * 新增团队白名单信息
     *
     * @param white
     * @return
     */
    @Log(title = "机构管理（新增团队白名单信息）", businessType = BusinessType.INSERT)
    @RequestMapping(value = "/addWhite", method = RequestMethod.POST)
    public AjaxResult addWhite(@RequestBody White white) {
        teamService.insertWhite(white);
        return AjaxResult.success("添加成功");
    }

//-------------------------------------------------------------------------------------------------------


    /**
     * 机构类型枚举查询
     *
     * @return
     */
    @RequestMapping(value = "/OrganizationType", method = RequestMethod.GET)
    public AjaxResult OrganizationType() {
        OrganizationTypeEnum[] values = OrganizationTypeEnum.values();
        List<EnumerationPojo> list = new ArrayList<>();
        for (OrganizationTypeEnum row : values) {
            EnumerationPojo statePojo = new EnumerationPojo(row.getCode(), row.getInfo());
            list.add(statePojo);
        }
        return AjaxResult.success(list);
    }

    /**
     * 批量修改团队状态
     *
     * @return
     */
    @RequiresPermissions("team:list:setstatus")
    @Log(title = "机构管理（批量修改团队状态）", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/cooperation", method = RequestMethod.PUT)
    public AjaxResult editCooperation(@RequestBody List<Create> create) {
        agTeamService.modifyCooperationStatus(create);
        return AjaxResult.success("修改成功");
    }

    /**
     * 批量重置团队密码
     *
     * @return
     */
    @RequiresPermissions("team:list:resetpwd")
    @Log(title = "机构管理（批量重置团队密码）", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/password", method = RequestMethod.PUT)
    public AjaxResult editPassword(@RequestBody List<Create> creates) {
        if (ObjectUtils.isEmpty(creates)) {
            throw new GlobalException("重置信息不能为空");
        }
        teamService.updateCreatePassword(creates);
        return AjaxResult.success("重置成功");
    }

    /**
     * 修改标签信息
     *
     * @return
     */
    @Log(title = "机构管理（修改标签信息）", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/label", method = RequestMethod.PUT)
    public AjaxResult editLabel(@RequestBody List<Label> label) {
        teamService.updateLabel(label);
        return AjaxResult.success("修改成功");
    }

    /**
     * 修改白名单信息
     *
     * @return
     */
    @Log(title = "机构管理（修改白名单信息）", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/updateWhite", method = RequestMethod.PUT)
    public AjaxResult editWhite(@RequestBody White white) {
        teamService.updateWhite(white);
        return AjaxResult.success("修改成功");
    }

    /**
     * 修改白名单状态
     *
     * @return
     */
    @Log(title = "机构管理（修改白名单状态）", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/updateState", method = RequestMethod.PUT)
    public AjaxResult editState(@RequestBody White white) {
        teamService.updateState(white);
        return AjaxResult.success("修改成功");
    }

    /**
     * 月回款目标进行更新和存入历史记录表
     *
     * @param create
     * @return
     */
    @RequiresPermissions("team:list:SetTarget")
    @Log(title = "机构管理（更新月回款目标）", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/updateTargets", method = RequestMethod.PUT)
    public AjaxResult editTargets(@RequestBody Create create) {
        agTeamService.updateTargets(create);
        return AjaxResult.success("修改成功");
    }

    /**
     * 修改设置状态（0:关闭,1:启用）
     *
     * @param state
     * @return
     */
    @Log(title = "机构管理（修改设置状态）", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/updateStates", method = RequestMethod.PUT)
    public AjaxResult editState(@RequestBody State state) {
        agTeamService.modifyState(state);
        return AjaxResult.success("修改成功");
    }

    /**
     * 修改水印设置字段
     *
     * @param watermark
     * @return
     */
    @Log(title = "机构管理（修改水印设置字段）", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/updateWatermark", method = RequestMethod.PUT)
    public AjaxResult editWatermark(@RequestBody Watermark watermark) {
        teamService.updateWatermark(watermark);
        return AjaxResult.success("修改成功");
    }

    /**
     * 脱敏状态设置修改
     *
     * @param desensitization
     * @return
     */
    @Log(title = "机构管理（脱敏状态设置修改）", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/updateDesensitization", method = RequestMethod.PUT)
    public AjaxResult editDesensitization(@RequestBody Desensitization desensitization) {
        agTeamService.modifyDesensitization(desensitization);
        return AjaxResult.success("修改成功");
    }

    /**
     * 团队审批设置流程信息修改/删除/新增
     *
     * @param approvalSettingsPojo
     * @return
     */
    @Log(title = "机构管理（团队审批设置流程信息修改）", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/updateApprovalSteps", method = RequestMethod.POST)
    public AjaxResult editApprovalSteps(@RequestBody ApprovalSettingsPojo approvalSettingsPojo) {
        agTeamService.ApprovalSteps(approvalSettingsPojo);
        return AjaxResult.success("修改成功");
    }


    /**
     * 获取团队 导出设置 列表
     *
     * @param teamId
     * @return
     */
    @GetMapping("/getTeamExportList")
    public TableDataInfo selectListWithTeamExport(Integer teamId) {
        startPage();
        List<TeamExport> teamExports = teamService.selectTeamExports(teamId);
        return getDataTable(teamExports);
    }


}
