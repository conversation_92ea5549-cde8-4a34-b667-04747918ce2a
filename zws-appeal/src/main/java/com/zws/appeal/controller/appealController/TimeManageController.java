package com.zws.appeal.controller.appealController;


import com.zws.appeal.service.appeal.ITimeMangeService;
import com.zws.common.core.domain.TimeManage;
import com.zws.common.core.web.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 时效管理
 */
@RestController
@RequestMapping("/time/manage")
public class TimeManageController {

    @Autowired
    private ITimeMangeService timeService;

    /**
     * 新增案件跟进记录
     *
     * @param timeManage
     * @return
     */
//    @RequestMapping(value = "/insertTimeRecord", method = RequestMethod.POST)
//    public AjaxResult addInfoContact(@RequestBody TimeManage timeManage) {
//        timeService.insertInfoContact(timeManage);
//        return AjaxResult.success("添加成功");
//    }

    /**
     * 定时更新剩余时效天数
     * @return
     */
    @PostMapping("/testUpdatesCensusTime")
    public AjaxResult testUpdatesCensusTime(){
        timeService.updateTimeBatch();
        return AjaxResult.success();
    }

    /**
     * 处理时效表历史数据
     * @return
     */
    @PostMapping("/addCaseToTime")
    public AjaxResult addCaseToTime(){
        timeService.addCaseToTime();
        return AjaxResult.success();
    }
}
