package com.zws.appeal.controller.request;

import com.zws.appeal.domain.call.CallRecord;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 通话记录查询参数
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/22 9:46
 */
@Data
public class CallRecordParamRequest extends CallRecord {


    /**
     * 呼叫时间-开始
     */
    private Date callTime1;
    /**
     * 呼叫时间-截止
     */
    private Date callTime2;
    /**
     * 通话时长-开始
     */
    private Integer agentDuration1;
    /**
     * 通话时长-截止
     */
    private Integer agentDuration2;

    /**
     * 选中的通话记录id集合
     */
    private List<Long> ids;

    /**
     * 是否查询全部
     * true 全部查询
     * false 查询本页
     */
    private Boolean allQuery;

    public boolean getAllQuery() {
        return allQuery == null ? false : allQuery;
    }

    public CallRecordParamRequest getParam() {
        CallRecordParamRequest queryParam = new CallRecordParamRequest();
        if (this.getAllQuery()) {
            queryParam = this;
            queryParam.setIds(null);
        } else {
            queryParam.setIds(this.getIds());
        }
        return queryParam;
    }
}
