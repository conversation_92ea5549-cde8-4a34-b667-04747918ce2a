package com.zws.appeal.controller.letterDoc.law.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 法院机构
 *
 * <AUTHOR>
 * @date 2023年12月10日16:39:05
 */
@Data
public class LawAgency extends BaseEntity {

    private Long id;
    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 法院名称
     */
    @NotNull(message = "法院名称不能为空")
    private String courtName;

    /**
     * 地址
     */
    @NotNull(message = "地址不能为空")
    private String address;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 机构所属辖区-省
     */
    private String jurisdictionEconomize;
    /**
     * 机构所属辖区-市
     */
    private String jurisdictionMarket;

    private Long createById;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private Long updateById;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String delFlag;

    /**
     * 关联文书主键id集合
     */
    @NotNull(message = "关联文书主键id集合不能为空")
    private List<Long> ids;

    /**
     * 法院关联文书数量
     */
    private Integer number;
}
