package com.zws.appeal.controller.letterDoc.letter.controller;

import com.zws.appeal.controller.letterDoc.law.enums.ApproveStatusEnum;
import com.zws.appeal.controller.letterDoc.law.enums.StatusEnum;
import com.zws.common.core.domain.Option;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.appeal.controller.letterDoc.letter.agservice.OptionAgService;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 选项管理
 * <AUTHOR>
 * @date 2024/1/14 15:28
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/option")
public class OptionController extends BaseController {

    @Autowired
    private OptionAgService optionAgService;


    /**
     * 获取状态选项
     *
     * @return
     */
    @GetMapping("/getStatusOption")
    public AjaxResult getStatusOption() {
        return AjaxResult.success(StatusEnum.getOptions());
    }

    /**
     * 获取审核状态选项
     * @return
     */
    @GetMapping("/getApproveStatusOptions")
    public AjaxResult getApproveStatusOptions(){
        return AjaxResult.success(ApproveStatusEnum.getOptions());
    }




    /**
     * 获取签章机构选项
     *
     * @return
     */
    @GetMapping("/getLawOption")
    public AjaxResult getLawOption() {
        List<Option> options= optionAgService.getLawOption();
        return AjaxResult.success(options);
    }

    /**
     * 获取公司章 选项
     * @return
     */
    @GetMapping("/getCompanyOption")
    private AjaxResult getCompanyOption(){
        List<Option> options= optionAgService.getCompanyOption();
        return AjaxResult.success(options);
    }

    /**
     * 获取代理律师 选项
     * @return
     */
    @GetMapping("/getAttorneyOption")
    private AjaxResult getAttorneyOption(){
        List<Option> options= optionAgService.getAttorneyOption();
        return AjaxResult.success(options);
    }

    /**
     * 根据主键id获取签章图片
     * @param template
     * @return
     */
    @GetMapping("/getCompanySignature")
    private AjaxResult getCompanySignature(LetterTemplate template) throws IOException {
        if (template.getLawId()==null){ return AjaxResult.error("律所ID不能为空");}
        List<Option> options = new ArrayList<>();
        if (template.getSignId()==null){
            options = optionAgService.getCompanySignature(Long.valueOf(template.getLawId().toString()));
        }else {
            options = optionAgService.getCompanySignature(Long.valueOf(template.getSignId().toString()));
        }
        return AjaxResult.success(options);
    }

    /**
     * 获取当前租户的
     * @return
     */
    @GetMapping("/getLawName")
    public AjaxResult getLawName(){
        String lawName=optionAgService.getLawName();
        return AjaxResult.success("操作成功",lawName);
    }



}
