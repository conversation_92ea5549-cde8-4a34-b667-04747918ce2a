package com.zws.appeal.controller.letterDoc.law.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 签章
 *
 * <AUTHOR>
 * @date 2023年11月22日21:36:51
 */
@Data
public class LawSign {
    private Long id;

    /**
     * 勾选id集合(非数据库字段)
     */
    private List<Long> ids;

    /**
     * 律所id、团队id
     */
    private Long createId;

    /**
     * 签章名称，唯一
     */
    @Excel(sort = 1, name = "签章名称")
    @NotEmpty(message = "签章名称 不能为空")
    private String signName;
    /**
     * 签章类型，0-公司章，1-个人章
     */
    @Excel(sort = 2, name = "签章类型", readConverterExp = "0=公司章,1=个人章")
    @NotNull(message = "签章类型 不能为空")
    private Integer signType;
    /**
     * 签章ID(识别码)
     */
    @Excel(sort = 3, name = "签章ID")
//    @NotNull(message = "签章识别码 不能为空")
    private String signCode;
    /**
     * 状态，0-启用，1-禁用
     */
    @Excel(sort = 4, name = "状态", readConverterExp = "0=启用,1=禁用")
    @NotNull(message = "状态 不能为空")
    private Integer status;
    /**
     * 签章图片
     */
    private String signPic;

    /**
     * 签章图片base64
     */
    private String signPicStr;

    /**
     * 备注
     */
    @Excel(sort = 5, name = "备注")
    private String remark;
    /**
     * 盖章的空白pdf路径
     */
    private String blankPdf;

    private String createBy;

    private Long createById;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String updateBy;

    private Long updateById;

    @Excel(sort = 6, name = "修改时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String delFlag;


}
