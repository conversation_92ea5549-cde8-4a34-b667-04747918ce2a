package com.zws.appeal.controller.letterDoc.letter.mapper;

import com.zws.appeal.controller.letterDoc.letter.domain.TemplateApprove;
import com.zws.standardmain.domain.entity.ZwsApproveRecord;
import com.zws.system.api.domain.DocumentTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TemplateApproveMapper {

    /**
     * 根据审批id查询审批记录
     * @param approveId
     * @return
     */
    ZwsApproveRecord selectApproveByApproveId(Long approveId);

    /**
     * 删除旧模板
     * @param id
     */
    void remove(Long id);

    /**
     * 添加新模板
     * @param templateApprove
     */
    void add(TemplateApprove templateApprove);

    @Select("select * from document_template where id = #{id}")
    DocumentTemplate selectById(Long id);

    void removeTemplateApprove(Long id);
}
