package com.zws.appeal.controller.letterDoc.law.mapper;


import com.zws.appeal.controller.letterDoc.law.domain.LawSign;

import java.util.List;

public interface LawSignMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LawSign record);
    int updateByPrimaryKeySelective(LawSign record);
    LawSign selectByPrimaryKey(Long id);

    /**
     * 签章ID 查询
     * @param signCode
     * @return
     */
    LawSign getBySignCode(String signCode);

    /**
     * 条件查询列表
     * @param record
     * @return
     */
    List<LawSign> selectList(LawSign record);

}
