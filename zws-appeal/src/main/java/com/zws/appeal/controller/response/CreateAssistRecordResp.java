package com.zws.appeal.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 团队申请协催查询返回实体类
 */
@Data
public class CreateAssistRecordResp implements Serializable {

    /**
     * 该案件是否属于本团队(0-不属于,1-属于)
     */
    private Integer button;

    /**
     * 申请表id
     */
    private Long id;

    /**
     * 案件id
     */
    private Long caseId;

    /**
     * 客户姓名
     */
    private String clientName;

    /**
     * 客户身份证号
     */
    private String clientIdcard;
    /**
     * 证件类型
     */
    private String clientIdType;
    /**
     * 委托方
     */
    private String entrustingPartyName;

    /**
     * 委案批号
     */
    private String entrustingCaseBatchNum;

    /**
     * 催收员名称
     */
    private String odvName;

    /**
     * 委托金额
     */
    private BigDecimal clientMoney;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 协催人
     */
    private String helper;

    /**
     * 协催状态 （0-待协催，1-持续协催，2-完成协催，3-终止协催）
     */
    private String state;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyDate;

}
