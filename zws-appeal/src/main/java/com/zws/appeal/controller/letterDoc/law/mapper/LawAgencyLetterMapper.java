package com.zws.appeal.controller.letterDoc.law.mapper;

import com.zws.appeal.controller.letterDoc.law.domain.LawAgencyLetter;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LawAgencyLetterMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LawAgencyLetter record);

    int insertSelective(LawAgencyLetter record);

    LawAgencyLetter selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LawAgencyLetter record);

    int updateByPrimaryKey(LawAgencyLetter record);

    /**
     * 删除不在选择列表中的
     * @param agencyId
     * @param letterTemplateIds
     * @return
     */
    int deleteByNotInList(@Param("agencyId") Long agencyId, @Param("letterTemplateIds") List<Long> letterTemplateIds);

    /**
     * 机构ID 查询文书模板ID
     * @param agencyId
     * @return
     */
    List<Long> selectLetterTemplateIds(Long agencyId);

    /**
     * 条件查询
     * @param record
     * @return
     */
    List<LawAgencyLetter> selectList(Long lawAgencyId);

}
