package com.zws.appeal.controller.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 创建团队 Pojo
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@Data
public class CreateTeamFormRequest implements Serializable {

    /**
     * 创建团队表主键
     */
    private Integer id;

    /**
     * 团队名称
     */
    @NotNull(message = "团队名称不能为空")
    private String cname;

    /**
     * 团队类别
     */
    @NotNull(message = "团队类型必须选择")
    private String category;

    /**
     * 登录账号
     */
    @NotNull(message = "登录账号不能为空")
    private String account;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 子账号个数
     */
    @NotNull(message = "子账号个数不能为空")
    private Integer numbers;

    /**
     * 合作状态
     */
    @NotNull(message = "合作状态不能为空")
    private Integer cooperation;

    /**
     * 保证金
     */
    private String margin;

    /**
     * 合同开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date starts;

    /**
     * 合同到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date complete;

    /**
     * 联系人手机号
     */
    @NotNull(message = "手机号不能为空")
    private String contact;

    /**
     * 座机号码
     */
    @NotNull(message = "座机号码不能为空")
    private String machine;

    /**
     * 联系人邮箱
     */
    @NotNull(message = "邮箱不能为空")
    private String email;

    /**
     * 团队描述
     */
    private String describes;

    /**
     * 创建人
     */
    private String founder;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creationtime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 月回款目标
     */
    private BigDecimal collectionTargets;

    /**
     * 删除标志（0:未删除,1:已删除）
     */
    private Integer deleteLogo;

    /**
     * 登录标识
     */
    private String loginId;

    /**
     * 佣金比
     */
    private BigDecimal commissionRatio;

    /**
     * 机构类型
     */
    private Integer teamType;
}
