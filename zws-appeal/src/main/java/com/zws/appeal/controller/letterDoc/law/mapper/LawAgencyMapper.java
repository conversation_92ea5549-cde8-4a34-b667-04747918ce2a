package com.zws.appeal.controller.letterDoc.law.mapper;


import com.zws.appeal.controller.letterDoc.law.domain.LawAgency;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface LawAgencyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LawAgency record);


    LawAgency selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LawAgency record);

    /**
     * 条件查询列表
     * @param record
     * @return
     */
    List<LawAgency> selectList(LawAgency record);

    /**
     * 根据法院id查询关联文书主键id
     *
     * @param courtId
     * @return
     */
    List<Long> selectCourtId(Long courtId);

    /**
     * 获取机构名称下拉框
     * @return
     */
    List<LawAgency> getCourtNameOptions(Long teamId);

    /**
     * 获取本机构的  主键id、法院名称
     * @param teamId 机构id
     * @return
     */
    List<LawAgency> selectWithLawAgency(Long teamId);
}