package com.zws.appeal.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.zws.appeal.agservice.*;
import com.zws.appeal.enums.ExpenseItemEnum;
import com.zws.appeal.mapper.CaseMapper;
import com.zws.appeal.mapper.CaseReorganizationRecordMapper;
import com.zws.appeal.pojo.vo.RecombinationRecordVo;
import com.zws.appeal.pojo.vo.StagingRecordVo;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.R;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.appeal.domain.*;
import com.zws.appeal.domain.record.*;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.enums.CsExportClassEnum;
import com.zws.appeal.pojo.*;
import com.zws.appeal.pojo.work.WorkOrderFollowUp;
import com.zws.appeal.service.*;
import com.zws.appeal.utils.FileUpload;
import com.zws.appeal.utils.Log;
import com.zws.appeal.utils.SaasFileDownloadUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.dispose.pojo.ApplicationUtils;
import com.zws.system.api.RemoteCaseService;
import com.zws.system.api.domain.WorkFollowUp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 收集Controller
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/collection")
public class CollectionController extends BaseController {

    @Autowired
    private AgCollectionService agCollectionService;
    @Autowired
    private CollectionService collectionService;
    @Autowired
    private RecordService recordService;
    @Autowired
    private FileUpload fileUpload;
    @Autowired
    private RemoteCaseService remoteCaseService;
    @Autowired
    private SendRecordService sendRecordService;
    @Autowired
    private TemplateService templateService;
    @Autowired
    private ExportAgService exportAgService;
    @Autowired
    private SettingsService settingsService;
    @Autowired
    private TeamSysAgService teamSysAgService;
    @Autowired
    private AgLawsuitService agLawsuitService;
    @Autowired
    private CaseReorganizationRecordMapper caseReorganizationRecordMapper;
    @Autowired
    private CaseMapper caseMapper;


    /**
     * 联系人关系
     */
    @RequiresLogin
    @RequestMapping(value = "/select", method = RequestMethod.GET)
    public AjaxResult selectArrayWithContact() {
        String arr[] = {"本人", "夫妻", "父母", "兄弟姐妹", "朋友", "同事", "同学", "其他亲属/家人", "其他"};
        return AjaxResult.success("查询成功", arr);
    }

    /**
     * 根据字段查询该催收员的案件/该催收员案件全查
     *
     * @param manageQueryParam
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCaseManage", method = RequestMethod.GET)
    public TableDataInfo selectListWithCaseManage(ManageQueryParam manageQueryParam) {
        startPage();
        List<CaseManage> caseManages = agCollectionService.dataDesensitization(manageQueryParam);

        //如果案件资产端标签不为空，则覆盖标签内容为资产端标签内容
        List<Map<String, String>> resultList = caseMapper.getAssetKeyVal();
        Map<String, String> map = new HashMap<>();

        for (Map<String, String> entry : resultList) {
            String key = entry.get("key");
            String value = entry.get("value");
            map.put(key, value);
        }
        for (CaseManage caseManage : caseManages) {
            //覆盖标签内容为资产端标签内容
            if (!ObjectUtils.isEmpty(caseManage.getLabelAsset())) {
                String s = map.get(caseManage.getLabelAsset());
                if (!ObjectUtils.isEmpty(s)) {
                    caseManage.setLabelContent(s);
                    caseManage.setCode(Integer.valueOf(caseManage.getLabelAsset()));
                    caseManage.setLabel(caseManage.getLabelAsset());
                }
            }else {
                caseManage.setLabel("*" + caseManage.getLabel());
            }
        }
        return getDataTable(caseManages);
    }

    /**
     * 该催收员案件全查（计算案件总量，案件总金额，当月案件回款总金额）
     *
     * @param manageQueryParam
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCaseManageMoney", method = RequestMethod.GET)
    public AjaxResult selectMapWithCaseManageMoney(ManageQueryParam manageQueryParam) {
        Map<String, Object> map = recordService.selectCaseManageMoney(manageQueryParam);
        return AjaxResult.success("查询成功", map);
    }

    /**
     * 根据团队id以及案件id集合/查询条件统计金额以及可分配案件数量
     *
     * @param manageQueryParam
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/myCollectionCase", method = RequestMethod.POST)
    public AjaxResult selectMapWithCollectionCase(@RequestBody Object pojo) {
        Map<String, Object> map = BeanUtil.beanToMap(pojo);
        Map<String, Object> map1 = agLawsuitService.selectMapWithCollectionCase(map);
        return AjaxResult.success("查询成功", map1);

//        Map<String, Object> map = recordService.selectCaseManageMoneySize(manageQueryParam);
//        return AjaxResult.success("查询成功", map);
    }

    /**
     * 根据条件查询统计催员所有案件金额以及总数量
     *
     * @param manageQueryParam
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectMoneySizeById", method = RequestMethod.POST)
    public AjaxResult selectMapWithMoneySizeById(@RequestBody ManageQueryParam manageQueryParam) {
        Map<String, Object> map = recordService.selectMoneySizeById(manageQueryParam);
        return AjaxResult.success("查询成功", map);
    }


    /**
     * 根据案件id查询案件所有信息以及共债信息
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCase/{caseId}", method = RequestMethod.GET)
    public AjaxResult selectMapWithCaseDetails(@PathVariable("caseId") Long caseId) {
        Map<String, Object> map = agCollectionService.caseDetails(caseId);
        return AjaxResult.success("查询成功", map);
    }

    /**
     * 根据案件id查询案件所有信息以及共债信息(不脱敏数据)
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/cis/selectCase/{caseId}", method = RequestMethod.GET)
    public AjaxResult selectMapWithCaseDetailsCis(@PathVariable("caseId") Long caseId) {
        Map<String, Object> map = agCollectionService.caseDetailsCis(caseId);
        return AjaxResult.success("查询成功", map);
    }

    /**
     * 根据案件id查询案件申请资料
     *
     * @param caseId
     * @return
     */
    @RequestMapping(value = "/selectCaseMaterials/{caseId}", method = RequestMethod.GET)
    public R<List<CaseMaterialsVo>> selectCaseMaterials(@PathVariable("caseId") Long caseId) {
        List<CaseMaterialsVo> caseMaterialsVos = agCollectionService.selectCaseMaterials(caseId);
        return R.ok(caseMaterialsVos);
    }
    /**
     * 根据案件id查询案件还款计划表
     *
     * @param infoPlan
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRepaymentPlan", method = RequestMethod.GET)
    public TableDataInfo selectListWithRepaymentPlan(InfoPlan infoPlan) {
        if (ObjectUtils.isEmpty(infoPlan.getCaseId())) {
            throw new GlobalException("案件id不能为空");
        }
        startPage();
        List<InfoPlan> infoPlans = collectionService.selectInfoPlan(infoPlan);
        return getDataTable(infoPlans);
    }
    /**
     * 根据案件id查询案件还款计划表上方的综述
     *
     * @param infoPlan
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRepaymentPlanReview", method = RequestMethod.GET)
    public AjaxResult selectRepaymentPlanReview(InfoPlan infoPlan) {
        if (ObjectUtils.isEmpty(infoPlan.getCaseId())) {
            throw new GlobalException("案件id不能为空");
        }
        List<InfoPlan> infoPlans = collectionService.selectInfoPlan(infoPlan);
        CaseReorganizationRecord caseReorganizationRecord = caseReorganizationRecordMapper.selectRepaymentPlanReview(infoPlan.getCaseId());
        Map<String, String> map = new HashMap<>();
        if (ObjectUtils.isEmpty(caseReorganizationRecord)){
            // 还款期数
            map.put("stagingNum", null);
            // 分期后还款总金额
            map.put("totalRepayment", null);
            // 分期后实际还款总金额
            map.put("totalRepaymentAmount", null);
        }else {
            BigDecimal allYchMoney = infoPlans.stream().map(InfoPlan::getYchMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 还款期数
            map.put("stagingNum", String.valueOf(caseReorganizationRecord.getStagingNum()));
            // 重组后还款总金额
            map.put("totalRepayment", String.valueOf(caseReorganizationRecord.getTotalRepayment()));
            // 分期后实际还款总金额
            map.put("totalRepaymentAmount", String.valueOf(allYchMoney));
        }
        return AjaxResult.success(map);
    }


    /**
     * 根据案件id查询案件共债信息列表
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectQueryJointDebt", method = RequestMethod.GET)
    public TableDataInfo selectListWithQueryJointDebt(Long caseId) {
        if (ObjectUtils.isEmpty(caseId)) {
            throw new GlobalException("案件id不能为空");
        }
        startPage();
        List<InfoLoanPojo> infoLoanPojos = agCollectionService.queryJointDebt(caseId);
        if (infoLoanPojos.size() > 1) {
            return getDataTable(infoLoanPojos);
        } else {
            return getDataTable(new ArrayList<>());
        }
    }


    /**
     * 根据案件id查询协催记录
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAssistRecord/{caseId}", method = RequestMethod.GET)
    public TableDataInfo selectListWithAssistRecord(@PathVariable("caseId") Long caseId) {
        startPage();
        List<AssistRecord> assistRecords = agCollectionService.expeditingAuthority(caseId);
        return getDataTable(assistRecords);
    }


    /**
     * 根据案件id查询投诉记录
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectComplaintRecord/{caseId}", method = RequestMethod.GET)
    public TableDataInfo selectListWithComplaintRecord(@PathVariable("caseId") Long caseId) {
        startPage();
        List<ComplaintRecord> complaintRecords = agCollectionService.complaintAuthority(caseId);
        return getDataTable(complaintRecords);
    }


    /**
     * 根据案件id查询诉讼记录
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectLawsuitRecord/{caseId}", method = RequestMethod.GET)
    public TableDataInfo selectListWithLawsuitRecord(@PathVariable("caseId") Long caseId) {
        startPage();
        List<LawsuitRecord> lawsuitRecords = agCollectionService.litigationAuthority(caseId);
        return getDataTable(lawsuitRecords);
    }


    /**
     * 根据案件id查询便签记录
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectNoteRecord/{caseId}", method = RequestMethod.GET)
    public TableDataInfo selectListWithNoteRecord(@PathVariable("caseId") Long caseId) {
        startPage();
        List<NoteRecord> noteRecords = agCollectionService.noteAuthority(caseId);
        return getDataTable(noteRecords);
    }


    /**
     * 根据案件id查询减免记录
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectReductionRecord/{caseId}", method = RequestMethod.GET)
    public TableDataInfo selectListWithReductionRecord(@PathVariable("caseId") Long caseId) {
        startPage();
        List<ReductionRecord> reductionRecords = agCollectionService.reliefAuthority(caseId);
        return getDataTable(reductionRecords);
    }


    /**
     * 根据案件id查询回款记录
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRepaymentRecord/{caseId}", method = RequestMethod.GET)
    public TableDataInfo selectListWithRepaymentRecord(@PathVariable("caseId") Long caseId) {
        startPage();
        List<RepaymentRecord> repaymentRecords = agCollectionService.collectionAuthority(caseId);
        return getDataTable(repaymentRecords);
    }


    /**
     * 根据案件id查询外访记录
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectOutsideRecord/{caseId}", method = RequestMethod.GET)
    public TableDataInfo selectListWithOutsideRecord(@PathVariable("caseId") Long caseId) {
        startPage();
        List<OutsideRecord> outsideRecords = agCollectionService.foreignVisitAuthority(caseId);
        return getDataTable(outsideRecords);
    }


    /**
     * 根据申请id以及内容类型查询外访内容
     *
     * @param outsideId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectOutsideResourceById", method = RequestMethod.GET)
    public TableDataInfo selectListWithOutsideResourceById(Long outsideId, int resourceType) {
        startPage();
        List<OutsideResource> outsideResources = recordService.selectOutsideResourceById(outsideId, resourceType);
        return getDataTable(outsideResources);
    }


    /**
     * 根据案件id查询催收记录
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectUrgeRecord", method = RequestMethod.GET)
    public TableDataInfo selectListWithUrgeRecord(@RequestParam("caseId") Long caseId,@RequestParam ("urgeTpye")String urgeTpye,@RequestParam("webSide")Integer webSide) {
        startPage();
        List<UrgeRecord> urgeRecords = agCollectionService.reminderAuthority(caseId,urgeTpye,webSide);
        return getDataTable(urgeRecords);
    }

    /**
     * 根据案件id查询便签/投诉记录的上一条数据
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectNoteComplaint/{caseId}", method = RequestMethod.GET)
    public AjaxResult selectMapWithNoteComplaint(@PathVariable("caseId") Long caseId) {
        Map<String, Object> map = agCollectionService.selectNoteComplaint(caseId);
        return AjaxResult.success("查询成功", map);
    }

    /**
     * 根据协催人id以及其他条件查询案件详情表和协催申请表
     *
     * @param applicationUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAssistRecordHelperId", method = RequestMethod.GET)
    public TableDataInfo selectListWithAssistRecordHelperId(ApplicationUtils applicationUtils) {
        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
        applicationUtils.setClientPhone(FieldEncryptUtil.encrypt(applicationUtils.getClientPhone()));
        startPage();
        List<InheritanceCollection> inheritanceCollections = agCollectionService.expeditingRegistration(applicationUtils);
        return getDataTable(inheritanceCollections);
    }


    /**
     * 根据协催人id以及其他条件查询案件详情表和协催申请表统计数量
     *
     * @param applicationUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAssistRecordNumber", method = RequestMethod.GET)
    public AjaxResult selectListWithAssistRecordNumber(ApplicationUtils applicationUtils) {
        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
        applicationUtils.setClientPhone(FieldEncryptUtil.encrypt(applicationUtils.getClientPhone()));
        List<ExpeditingCount> list = recordService.selectAssistRecordNumber(applicationUtils);
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 根据条件查询短信发送记录
     *
     * @param sendRecords
     * @return
     */
    @GetMapping("/selectSendRecords")
    public TableDataInfo selectListWithSendRecords(SendRecords sendRecords) {
        //修改结束时间为一天的结束
        if (!ObjectUtils.isEmpty(sendRecords.getSendTime2())) {
            DateTime dateTime = DateUtil.endOfDay(sendRecords.getSendTime2());  //一天的结束，结果：2017-03-01 23:59:59
            sendRecords.setSendTime2(dateTime);
        }

        DataPermis dataPermis = teamSysAgService.getDataPermis();
        sendRecords.setDeptIds(dataPermis.getDeptIds());
        sendRecords.setEmployeesIds(dataPermis.getEmployeesIds());
        sendRecords.setCreatedByIds(teamSysAgService.getEmployeesIds());


        startPage();
        List<SendRecords> sendRecord = sendRecordService.selectSendRecords(sendRecords);
        return getDataTable(sendRecord);
    }


    /**
     * 根据id查询回复记录 （催收端）
     */
    @RequiresLogin
    @RequestMapping(value = "/selectReplayById/{id}", method = RequestMethod.GET)
    public TableDataInfo selectListWithReplayById(@PathVariable("id") Long id) {
        List<ReplyRecord> replyRecords = sendRecordService.selectReplayById(id);
        return getDataTable(replyRecords);
    }


    /**
     * 查询发送状态数量统计（催收端）
     */
    @RequiresLogin
    @GetMapping(value = "/selectRecordNumber")
    public AjaxResult selectListWithRecordNumber(SendRecords sendRecords) {

        DataPermis dataPermis = teamSysAgService.getDataPermis();
        sendRecords.setDeptIds(dataPermis.getDeptIds());
        sendRecords.setEmployeesIds(dataPermis.getEmployeesIds());
        sendRecords.setCreatedByIds(teamSysAgService.getEmployeesIds());
        List<Map<String, Integer>> maps = sendRecordService.selectRecordNumber(sendRecords);
        return AjaxResult.success(maps);
    }

    /**
     * 协催状态下拉查询
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAssistRecordType", method = RequestMethod.GET)
    public AjaxResult selectArrayWithAssistRecordType() {
        String[] str = new String[]{"持续协催", "完成协催", "终止协催"};
        return AjaxResult.success("查询成功", str);
    }

    /**
     * 根据登录人id以及其他条件查询我的工单
     *
     * @param workOrderRequest
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectWorkOrder", method = RequestMethod.GET)
    public TableDataInfo selectListWithWorkOrder(WorkOrderRequest workOrderRequest) {
        workOrderRequest.setClientName(FieldEncryptUtil.encrypt(workOrderRequest.getClientName()));
        workOrderRequest.setClientIdcard(FieldEncryptUtil.encrypt(workOrderRequest.getClientIdcard()));
        workOrderRequest.setClientPhone(FieldEncryptUtil.encrypt(workOrderRequest.getClientPhone()));
        startPage();
        List<WorkOrderExtends> workOrderExtends = agCollectionService.selectWorkOrder(workOrderRequest);
        return getDataTable(workOrderExtends);
    }


    /**
     * 根据登录人id以及其他条件查询我的工单统计数量
     *
     * @param workOrderRequest
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectWorkOrderNumber", method = RequestMethod.GET)
    public AjaxResult selectListWithWorkOrderNumber(WorkOrderRequest workOrderRequest) {
        workOrderRequest.setClientName(FieldEncryptUtil.encrypt(workOrderRequest.getClientName()));
        workOrderRequest.setClientIdcard(FieldEncryptUtil.encrypt(workOrderRequest.getClientIdcard()));
        workOrderRequest.setClientPhone(FieldEncryptUtil.encrypt(workOrderRequest.getClientPhone()));
        List<Map<String, Object>> maps = recordService.selectWorkOrderNumber(workOrderRequest);
        return AjaxResult.success("查询成功", maps);
    }


    /**
     * 工单类型下拉查询
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectWorkOrderType", method = RequestMethod.GET)
    public AjaxResult selectListWithWorkOrderType() {
        List<DictDataOrder> dictDataOrders = recordService.selectDictData();
        return AjaxResult.success("查询成功", dictDataOrders);
    }


    /**
     * 返回审核状态的key和value
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/enumeration", method = RequestMethod.GET)
    public AjaxResult selectListWithEnumeration() {
        List<AuditStatus> list = new ArrayList<>();
        list.add(new AuditStatus("待审核", "已提交，待审核"));
        list.add(new AuditStatus("审核中", "审核中"));
        list.add(new AuditStatus("未通过", "未通过"));
        list.add(new AuditStatus("已通过", "已通过"));
        list.add(new AuditStatus("已撤销", "已撤销"));
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 返回协催状态的key和value
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/assistInUrging", method = RequestMethod.GET)
    public AjaxResult selectListWithAssistInUrging() {
        List<AuditStatus> list = new ArrayList<>();
        list.add(new AuditStatus("0", "待协催"));
        list.add(new AuditStatus("1", "持续协催"));
        list.add(new AuditStatus("2", "完成协催"));
        list.add(new AuditStatus("3", "终止协催"));
        return AjaxResult.success("查询成功", list);
    }


    /**
     * 查询短信标签下拉框
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAllSmsSignature", method = RequestMethod.GET)
    public AjaxResult selectListWithAllSmsSignature() {
        return AjaxResult.success("查询成功", remoteCaseService.selectAllSmsSignature());
    }


    /**
     * 查询短信模板下拉框
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAllSmsTemplate", method = RequestMethod.GET)
    public AjaxResult selectListWithAllSmsTemplate() {
        return AjaxResult.success("查询成功", remoteCaseService.selectAllSmsTemplate());
    }

    /**
     * 返回外访状态的key和value
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/foreignCountry", method = RequestMethod.GET)
    public AjaxResult selectListWithForeignCountry() {
        List<AuditStatus> list = new ArrayList<>();
        list.add(new AuditStatus("待审核", "已提交，待审核"));
        list.add(new AuditStatus("审核中", "审核中"));
        list.add(new AuditStatus("未通过", "未通过"));
        list.add(new AuditStatus("已通过", "已通过，待外访"));
        list.add(new AuditStatus("已签到", "已签到"));
        list.add(new AuditStatus("已完成", "已完成"));
        list.add(new AuditStatus("已撤销", "已撤销"));
        return AjaxResult.success("查询成功", list);
    }


    /**
     * 根据登录人id以及其他条件查询案件详情表和回款申请表
     *
     * @param applicationUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRepaymentRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithRepaymentRecordId(ApplicationUtils applicationUtils) {
        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
        startPage();
        List<InheritanceCollection> inheritanceCollections = agCollectionService.repaymentRecordIdDesensitization(applicationUtils);
        return getDataTable(inheritanceCollections);
    }

    /**
     * 根据登录人id以及其他条件查询案件详情表和资料调取申请表
     *
     * @param applicationUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRetrievalRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithRetrievalRecordId(ApplicationUtils applicationUtils) {
        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
        startPage();
        List<InheritanceCollection> inheritanceCollections = agCollectionService.retrievalRecordIdDesensitization(applicationUtils);
        return getDataTable(inheritanceCollections);
    }


    /**
     * 还款方式下拉查询
     *
     * @param repaymentSetup
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRepaymentSetup", method = RequestMethod.GET)
    public AjaxResult selectListWithRepaymentSetup(RepaymentSetup repaymentSetup) {
        List<RepaymentSetup> repaymentSetups = recordService.selectRepaymentSetup(repaymentSetup);
        List<StatePojo> list = new ArrayList<>();
        repaymentSetups.forEach(temp -> {
            list.add(new StatePojo(temp.getId().toString(), temp.getRepaymentMethod()));
        });
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 根据申请人id以及其他条件查询案件详情表和减免申请表
     *
     * @param applicationUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectReductionRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithReductionRecordId(ApplicationUtils applicationUtils) {
        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
        startPage();
        List<InheritanceCollection> inheritanceCollections = agCollectionService.reductionRecordIdDesensitization(applicationUtils);
        return getDataTable(inheritanceCollections);
    }

    /**
     * 根据申请id查询减免申请凭证文件信息
     *
     * @param id
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectReductionFile/{id}", method = RequestMethod.GET)
    public AjaxResult selectListWithReductionFile(@PathVariable("id") Long id) {
        List<ReductionFile> reductionFiles = recordService.selectReductionFile(id);
        return AjaxResult.success("修改成功", reductionFiles);
    }

    /**
     * 根据申请人id以及其他条件查询案件详情表和外访申请表
     *
     * @param outsideRecordUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectOutsideRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithOutsideRecordId(OutsideRecordUtils outsideRecordUtils) {
        outsideRecordUtils.setClientName(FieldEncryptUtil.encrypt(outsideRecordUtils.getClientName()));
        outsideRecordUtils.setClientIdcard(FieldEncryptUtil.encrypt(outsideRecordUtils.getClientIdcard()));
        startPage();
        List<OutsideRecordCollection> outsideRecordCollections = agCollectionService.selectExternalVisit(outsideRecordUtils);
        return getDataTable(outsideRecordCollections);
    }

    /**
     * 根据申请人id以及其他条件查询案件详情表和停催申请表
     *
     * @param applicationUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectSuspensionId", method = RequestMethod.GET)
    public TableDataInfo selectListWithSuspensionId(ApplicationUtils applicationUtils) {
        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
        startPage();
        applicationUtils.setApplyState(0);   //申请的案件状态，0-停催，1-留案，2-退案
        List<InheritanceCollection> inheritanceCollections = agCollectionService.applyStateDesensitization(applicationUtils);
        return getDataTable(inheritanceCollections);
    }


    /**
     * 根据申请人id以及其他条件查询案件详情表和留案申请表
     *
     * @param applicationUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectKeepCaseId", method = RequestMethod.GET)
    public TableDataInfo selectListWithKeepCaseId(ApplicationUtils applicationUtils) {
        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
        startPage();
        applicationUtils.setApplyState(1);   //申请的案件状态，0-停催，1-留案，2-退案
        List<InheritanceCollection> inheritanceCollections = agCollectionService.applyStateDesensitization(applicationUtils);
        return getDataTable(inheritanceCollections);
    }


    /**
     * 根据申请人id以及其他条件查询案件详情表和退案申请表
     *
     * @param applicationUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectWithdrawalId", method = RequestMethod.GET)
    public TableDataInfo selectListWithWithdrawalId(ApplicationUtils applicationUtils) {
        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
        startPage();
        applicationUtils.setApplyState(2);   //申请的案件状态，0-停催，1-留案，2-退案
        List<InheritanceCollection> inheritanceCollections = agCollectionService.applyStateDesensitization(applicationUtils);
        return getDataTable(inheritanceCollections);
    }


    /**
     * 根据申请人id以及其他条件查询案件详情表和协催申请表
     *
     * @param applicationUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAssistRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithAssistRecordId(ApplicationUtils applicationUtils) {
        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
        startPage();
        List<InheritanceCollection> inheritanceCollections = agCollectionService.assistRecordIdDesensitization(applicationUtils);
        return getDataTable(inheritanceCollections);
    }


    /**
     * 根据协催申请id查询协催记录
     *
     * @param id
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAssistDetails/{id}", method = RequestMethod.GET)
    public TableDataInfo selectListWithAssistDetails(@PathVariable("id") Long id) {
        startPage();
        List<AssistDetails> assistDetails = recordService.selectAssistDetails(id);
        return getDataTable(assistDetails);
    }

    /**
     * 根据申请人id以及其他条件查询案件详情表和催收承诺户申请表
     *
     * @param applicationUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectUrgeRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithUrgeRecordId(ApplicationUtils applicationUtils) {
//        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
//        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
//        applicationUtils.setClientPhone(FieldEncryptUtil.encrypt(applicationUtils.getClientPhone()));
        startPage();
        List<InheritanceCollection> inheritanceCollections = agCollectionService.urgeRecordIdDesensitization(applicationUtils);
        return getDataTable(inheritanceCollections);
    }

    /**
     * 根据催收员id查询催收员标签
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectStaffLabel", method = RequestMethod.GET)
    public AjaxResult selectListWithStaffLabel() {
        List<StaffLabel> staffLabels = collectionService.selectStaffLabel();
        return AjaxResult.success("查询成功", staffLabels);
    }

    /**
     * 获取还款方式的登记必填字段
     *
     * @param id
     * @return
     */
    @RequiresLogin
    @GetMapping("/getRegisterPayment")
    public AjaxResult infoWithRegisterPayment(Long id) {
        RepaymentSetup entity = recordService.selectRepaymentSetupId(id);
        if (entity != null) {
            String[] strAll = entity.getRegisterPayment().split(";");
            return AjaxResult.success(strAll);
        }
        return AjaxResult.error("id错误,获取还款方式信息失败");
    }

    /**
     * 根据申请id查询回款申请凭证信息
     *
     * @param id
     * @return
     */
    @RequiresLogin
    //@RequiresPermissions(value = {"team:apply:proof", "collection:myapply:repayment:proof"}, logical = Logical.OR)
    @RequestMapping(value = "/selectRepaymentRecordById/{id}", method = RequestMethod.GET)
    public AjaxResult infoWithRepaymentRecordById(@PathVariable("id") Long id) {
        RepaymentRecord repaymentRecord = recordService.selectRepaymentRecordById(id);
        return AjaxResult.success("查询成功", repaymentRecord);
    }

    /**
     * 根据申请id查询回款申请结清证明的Url
     *
     * @param id
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRepaymentRecordUrl", method = RequestMethod.POST)
    public AjaxResult infoWithRepaymentRecordUrl(Long id, HttpServletResponse response) throws IOException {
        RepaymentRecord repaymentRecord = recordService.selectRepaymentRecordUrl(id);
        if (!ObjectUtils.isEmpty(repaymentRecord) && !ObjectUtils.isEmpty(repaymentRecord.getSettleCertificateUrl())) {
            File tempFile = SaasFileDownloadUtils.downloadTempFile(repaymentRecord.getSettleCertificateUrl());
//        获取下载文件的输入流
            FileInputStream fis = new FileInputStream(tempFile);
//        获取ServletOutputStream对象
            ServletOutputStream out = response.getOutputStream();
            int len = 0;
            byte[] buffer = new byte[1024];
            while ((len = fis.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
            out.close();
            fis.close();
            return AjaxResult.success("下载成功");
        } else {
            throw new GlobalException("该结清证明还未下发");
        }
    }

    /**
     * 根据工单id查询工单详情以及工单跟进内容-(催收管理/我的工单)
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectWorkOrderFollowUp", method = RequestMethod.GET)
    public AjaxResult infoWithWorkOrderFollowUp(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            return AjaxResult.error("请选择案件");
        }
        WorkOrderFollowUp workOrderFollowUp = agCollectionService.selectWorkOrderFollowUp(id);
        return AjaxResult.success("查询成功", workOrderFollowUp);
    }

    /**
     * 根据案件id以及联系人/联系人号码查询联系人信息
     *
     * @param criteria
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectContact", method = RequestMethod.GET)
    public AjaxResult infoWithContact(Criteria criteria) {
        OutboundVisitButton outboundVisitButton = agCollectionService.selectInfoContact(criteria);
        return AjaxResult.success("查询成功", outboundVisitButton);
    }

    /**
     * 根据案件id查询案件共债信息（统计共债案件总欠款金额）
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectQueryAmountMoney", method = RequestMethod.GET)
    public AjaxResult infoWithQueryAmountMoney(Long caseId) {
        if (ObjectUtils.isEmpty(caseId)) {
            throw new GlobalException("案件id不能为空");
        }
        ArrearsTotalPojo arrearsTotalPojo = agCollectionService.queryAmountMoney(caseId);
        return AjaxResult.success("查询成功", arrearsTotalPojo);
    }

    /**
     * 查询上一条案件信息
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCaseManagePrevious", method = RequestMethod.POST)
    public AjaxResult infoWithCaseManagePrevious(@RequestBody CaseBeating caseBeating) {
        CaseBeating caseBeating1 = agCollectionService.selectCaseManagePrevious(caseBeating);
        caseBeating1.setManageQueryParam(caseBeating.getManageQueryParam());
        return AjaxResult.success("查询成功", caseBeating1);
    }

    /**
     * 查询下一条案件信息
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCaseManageNext", method = RequestMethod.POST)
    public AjaxResult infoWithCaseManageNext(@RequestBody CaseBeating caseBeating) {
        CaseBeating caseBeating1 = agCollectionService.selectCaseManageNext(caseBeating);
        caseBeating1.setManageQueryParam(caseBeating.getManageQueryParam());
        return AjaxResult.success("查询成功", caseBeating1);
    }


    /**
     * 根据工单id查询工单详情以及工单跟进内容-(团队管理/团队工单)
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectTeamWorkOrderFollowUp", method = RequestMethod.GET)
    public AjaxResult infoWithTeamWorkOrderFollowUp(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            return AjaxResult.error("请选择案件");
        }
        WorkOrderFollowUp workOrderFollowUp = agCollectionService.selectTeamWorkOrderFollowUp(id);
        return AjaxResult.success("查询成功", workOrderFollowUp);
    }

    /**
     * 根据工单id写入跟进内容
     *
     * @param
     * @return
     */
    @Log(title = "催收管理-写入跟进内容", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertWorkOrderFollowUp", method = RequestMethod.POST)
    public AjaxResult addWorkOrderFollowUp(@RequestBody WorkFollowUp workFollowUp) {
        if (ObjectUtils.isEmpty(workFollowUp)) {
            return AjaxResult.error("请选择工单");
        } else {
            if (ObjectUtils.isEmpty(workFollowUp.getId())) {
                return AjaxResult.error("工单id不能为空");
            }
            if (ObjectUtils.isEmpty(workFollowUp.getWorkFollowContent())) {
                return AjaxResult.error("工单跟进内容不能为空");
            }
        }
        agCollectionService.insertWorkOrderFollowUp(workFollowUp);
        return AjaxResult.success("操作成功");
    }

    /**
     * 写入案件便签
     *
     * @param noteRecord
     * @return
     */
    @Log(title = "催收管理-写入案件便签", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertNote", method = RequestMethod.POST)
    public AjaxResult addNote(@RequestBody NoteRecord noteRecord) {
        agCollectionService.insertNote(noteRecord);
        return AjaxResult.success("添加成功");
    }

    /**
     * 写入案件投诉记录
     *
     * @param complaintRecord
     * @return
     */
    @Log(title = "催收管理-写入案件投诉", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertComplaint", method = RequestMethod.POST)
    public AjaxResult addComplaint(@RequestBody ComplaintRecord complaintRecord) {
        agCollectionService.insertComplaint(complaintRecord);
        return AjaxResult.success("添加成功");
    }

    /**
     * 新增案件联系人信息
     *
     * @param infoContact
     * @return
     */
    @Log(title = "催收管理-案件新增联系人", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertInfoContact", method = RequestMethod.POST)
    public AjaxResult addInfoContact(@RequestBody InfoContact infoContact) {
        collectionService.insertInfoContact(infoContact);
        return AjaxResult.success("添加成功");
    }

    /**
     * 新增员工案件标签信息
     *
     * @param staffLabel
     * @return
     */
    @Log(title = "催收管理-新增员工案件标签信息", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertStaffLabel", method = RequestMethod.POST)
    public AjaxResult addStaffLabel(@RequestBody StaffLabel staffLabel) {
        collectionService.insertStaffLabel(staffLabel);
        return AjaxResult.success("添加成功");
    }

    /**
     * 写入回款申请表
     *
     * @param repaymentRecord
     * @return
     */
    @Log(title = "催收管理-写入回款申请", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertPayment", method = RequestMethod.POST)
    public AjaxResult addPayment(@RequestBody RepaymentRecord repaymentRecord) {
        agCollectionService.caseRepaymentRecord(repaymentRecord);
        return AjaxResult.success("添加成功");
    }

    /**
     * 写入减免申请表并保存上传文件信息
     *
     * @param reductionRecord
     * @return
     */
    @Log(title = "催收管理-写入减免申请", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertReduction", method = RequestMethod.POST)
    public AjaxResult addReduction(@RequestBody ReductionRecord reductionRecord) {
        agCollectionService.insertReduction(reductionRecord);
        return AjaxResult.success("添加成功");
    }

    /**
     * 写入资料调取申请表
     *
     * @param retrievalRecord
     * @return
     */
    @Log(title = "催收管理-写入资料调取申请", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertRetrievalRecord", method = RequestMethod.POST)
    public AjaxResult addRetrievalRecord(@RequestBody RetrievalRecord retrievalRecord) {
        agCollectionService.insertRetrievalRecord(retrievalRecord);
        return AjaxResult.success("添加成功");
    }

    /**
     * 写入协催申请表
     *
     * @param manageQueryParam
     * @return
     */
    @Log(title = "催收管理-写入协催申请", businessType = BusinessType.INSERT)
    @RequiresLogin
    //@RequiresPermissions(value = {"dispose:collection:insertAssistRecord", "dispose:collection:insertAssistRecordMyPromise", "dispose:collection:insertAssistRecordTeamPromise"}, logical = Logical.OR)
    @RequestMapping(value = "/insertAssistRecord", method = RequestMethod.POST)
    public AjaxResult addAssistRecord(@RequestBody ManageQueryParam manageQueryParam) {
        agCollectionService.insertAssistRecord(manageQueryParam);
        return AjaxResult.success("添加成功");
    }

    /**
     * 写入协催申请记录详情表
     *
     * @param assistDetails
     * @return
     */
    //@RequiresPermissions("collection:myassisturged:register")
    @Log(title = "催收管理-写入协催记录详情", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertAssistDetails", method = RequestMethod.POST)
    public AjaxResult addAssistDetails(@RequestBody AssistDetails assistDetails) {
        agCollectionService.collaborativeWriting(assistDetails);
        return AjaxResult.success("添加成功");
    }

    /**
     * 写入外访申请记录表
     *
     * @param outsideRecord
     * @return
     */
    @Log(title = "催收管理-写入外访申请", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertOutsideRecord", method = RequestMethod.POST)
    public AjaxResult addOutsideRecord(@RequestBody OutsideRecord outsideRecord) {
        agCollectionService.insertOutsideRecord(outsideRecord);
        return AjaxResult.success("添加成功");
    }

    /**
     * 修改联系人状态
     *
     * @param infoContact
     * @return
     */
    @Log(title = "催收管理-案件修改联系人状态", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updateInfoContact", method = RequestMethod.PUT)
    public AjaxResult editInfoContact(@RequestBody InfoContact infoContact) {
        collectionService.updateInfoContact(infoContact);
        return AjaxResult.success("修改成功");
    }

    /**
     * 根据申请id批量撤销回款申请
     *
     * @param ids
     * @return
     */
    @Log(title = "催收管理-撤销回款申请", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions(value = {"case:manage:recovery:repay", "team:apply:remove:return"}, logical = Logical.OR)
    @RequestMapping(value = "/updateRepaymentRecord", method = RequestMethod.PUT)
    public AjaxResult editRepaymentRecord(@RequestBody List<Long> ids) {
        agCollectionService.updateRepaymentRecord(ids);
        return AjaxResult.success("撤销成功");
    }

    /**
     * 根据申请id批量撤销减免申请
     *
     * @param ids
     * @return
     */
    @Log(title = "催收管理-撤销减免申请", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions(value = {"case:manage:recovery:reduce", "team:apply:remove:mitigate"}, logical = Logical.OR)
    @RequestMapping(value = "/updateReductionRecord", method = RequestMethod.PUT)
    public AjaxResult editReductionRecord(@RequestBody List<Long> ids) {
        agCollectionService.updateReductionRecord(ids);
        return AjaxResult.success("撤销成功");
    }

    /**
     * 根据申请id批量撤销分期还款申请
     *
     * @param ids
     * @return
     */
    @Log(title = "催收管理-撤销分期还款申请", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions(value = {"case:manage:recovery:stage", "team:apply:remove:stages"}, logical = Logical.OR)
    @RequestMapping(value = "/updateStagingRecord", method = RequestMethod.PUT)
    public AjaxResult editStagingRecord(@RequestBody List<Long> ids) {
        agCollectionService.updateStagingRecord(ids);
        return AjaxResult.success("撤销成功");
    }

    /**
     * 根据申请id批量撤销外访申请
     *
     * @param ids
     * @return
     */
    @Log(title = "催收管理-撤销外访申请", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions(value = {"case:manage:recovery:outsize", "team:apply:remove:outsize"}, logical = Logical.OR)
    @RequestMapping(value = "/updateOutsideRecord", method = RequestMethod.PUT)
    public AjaxResult editOutsideRecord(@RequestBody List<Long> ids) {
        agCollectionService.updateOutsideRecord(ids);
        return AjaxResult.success("撤销成功");
    }

    /**
     * 根据申请id批量撤销协催申请
     *
     * @param ids
     * @return
     */
    @Log(title = "催收管理-撤销协催申请", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updateAssistRecord", method = RequestMethod.PUT)
    public AjaxResult editAssistRecord(@RequestBody List<Long> ids) {
        recordService.updateAssistRecords(ids);
        return AjaxResult.success("撤销成功");
    }

    /**
     * 根据申请id批量撤销退案申请
     *
     * @param ids
     * @return
     */
    @Log(title = "催收管理-撤销退案申请", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions(value = {"case:manage:recovery:back", "team:apply:remove:back"}, logical = Logical.OR)
    @RequestMapping(value = "/updateWithdrawal", method = RequestMethod.PUT)
    public AjaxResult editWithdrawal(@RequestBody List<Long> ids) {
        int applyState = 2;//申请的案件状态，0-停催，1-留案，2-退案
        agCollectionService.updateApplyRecord(ids, applyState);
        return AjaxResult.success("撤销成功");
    }

    /**
     * 根据申请id批量撤销留案申请
     *
     * @param ids
     * @return
     */
    @Log(title = "催收管理-撤销留案申请", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions(value = {"case:manage:recovery:stay", "team:apply:remove:stay"}, logical = Logical.OR)
    @RequestMapping(value = "/updateKeepCase", method = RequestMethod.PUT)
    public AjaxResult editKeepCase(@RequestBody List<Long> ids) {
        int applyState = 1;//申请的案件状态，0-停催，1-留案，2-退案
        agCollectionService.updateApplyRecord(ids, applyState);
        return AjaxResult.success("撤销成功");
    }

    /**
     * 根据申请id批量撤销停催申请
     *
     * @param ids
     * @return
     */
    @Log(title = "催收管理-撤销停催申请", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions(value = {"case:manage:recovery:stop", "team:apply:remove:stop"}, logical = Logical.OR)
    @RequestMapping(value = "/updateStopUrging", method = RequestMethod.PUT)
    public AjaxResult editStopUrging(@RequestBody List<Long> ids) {
        int applyState = 0;//申请的案件状态，0-停催，1-留案，2-退案
        agCollectionService.updateApplyRecord(ids, applyState);
        return AjaxResult.success("撤销成功");
    }

    /**
     * 根据申请id批量撤销资料调取申请
     *
     * @param ids
     * @return
     */
    @Log(title = "催收管理-撤销资料调取申请", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions(value = {"collection:apply:recovery:data", "team:apply:remove:data"}, logical = Logical.OR)
    @RequestMapping(value = "/updateRetrievalRecord", method = RequestMethod.PUT)
    public AjaxResult editRetrievalRecord(@RequestBody List<Long> ids) {
        agCollectionService.updateRetrievalRecord(ids);
        return AjaxResult.success("撤销成功");
    }

    /**
     * 根据主键id集合批量处理工单
     *
     * @param ids
     * @return
     */
    @Log(title = "催收管理-处理工单", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updateWorkOrder", method = RequestMethod.PUT)
    public AjaxResult editWorkOrder(@RequestBody List<Long> ids) {
        agCollectionService.updateWorkOrder(ids);
        return AjaxResult.success("修改成功");
    }

    /**
     * 完成外访修改状态
     *
     * @param outsideRecord
     * @return
     */
    //@RequiresPermissions(value = {"collect:myapply.finish:outsize", "team:apply:finish:outsize"}, logical = Logical.OR)
    @Log(title = "催收管理-完成外访修改状态", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updateVisitStatus", method = RequestMethod.POST)
    public AjaxResult editVisitStatus(@RequestBody OutsideRecord outsideRecord) {
        recordService.updateVisitStatus(outsideRecord);
        return AjaxResult.success("修改成功");
    }

    /**
     * 写入催收记录
     *
     * @param urgeRecord
     * @return
     */
    @Log(title = "催收管理-写入案件催记", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertUrgeRecord", method = RequestMethod.POST)
    public AjaxResult editUrgeRecord(@RequestBody UrgeRecord urgeRecord) {
        agCollectionService.updateFollowUpStatus(urgeRecord);
        return AjaxResult.success("添加成功");
    }

    /**
     * 删除员工案件标签信息
     *
     * @param id
     * @return
     */
    @Log(title = "催收管理-删除员工案件标签信息", businessType = BusinessType.DELETE)
    @RequiresLogin
    @RequestMapping(value = "/deleteStaffLabel/{id}", method = RequestMethod.DELETE)
    public AjaxResult removeStaffLabel(@PathVariable("id") int id) {
        collectionService.deleteStaffLabel(id);
        return AjaxResult.success("删除成功");
    }

    /**
     * 退案（批量勾选）
     *
     * @param manageQueryParam
     * @return
     */
    @Log(title = "催收管理-申请退案", businessType = BusinessType.INSERT)
    @RequiresLogin
    //@RequiresPermissions("dispose:collection:insertRetreat")
    @RequestMapping(value = "/insertRetreat", method = RequestMethod.POST)
    public AjaxResult caseWithdrawal(@RequestBody Object manageQueryParam) {
        Map<String, Object> map = BeanUtil.beanToMap(manageQueryParam);
        agCollectionService.caseWithdrawal(map);
        return AjaxResult.success("添加成功");
    }

    /**
     * 留案（批量勾选）
     *
     * @param manageQueryParam
     * @return
     */
    @Log(title = "催收管理-申请留案", businessType = BusinessType.INSERT)
    @RequiresLogin
    //@RequiresPermissions("dispose:collection:insertKeep")
    @RequestMapping(value = "/insertKeep", method = RequestMethod.POST)
    public AjaxResult caseKeepCase(@RequestBody Object manageQueryParam) {
        Map<String, Object> map = BeanUtil.beanToMap(manageQueryParam);
        agCollectionService.caseKeepCase(map);
        return AjaxResult.success("添加成功");
    }

    /**
     * 停催（批量勾选）
     *
     * @param manageQueryParam
     * @return
     */
    @Log(title = "催收管理-申请停催", businessType = BusinessType.INSERT)
    @RequiresLogin
    //@RequiresPermissions("dispose:collection:insertStop")
    @RequestMapping(value = "/insertStop", method = RequestMethod.POST)
    public AjaxResult caseStopUrging(@RequestBody Object manageQueryParam) {
        Map<String, Object> map = BeanUtil.beanToMap(manageQueryParam);
        agCollectionService.caseStopUrging(map);
        return AjaxResult.success("添加成功");
    }

//    ------------------------------------------------------------------------------------------------------------

    /**
     * 验证该案件是否属于本团队
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/intercept/{caseId}", method = RequestMethod.GET)
    public AjaxResult intercept(@PathVariable("caseId") Long caseId) {
        agCollectionService.intercept(caseId);
        return AjaxResult.success("验证成功");
    }


    /**
     * 根据申请人id以及其他条件查询案件详情表和分期申请表
     *
     * @param applicationUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectStagingRecordId", method = RequestMethod.GET)
    public TableDataInfo selectListWithStagingRecordId(ApplicationUtils applicationUtils) {
        applicationUtils.setClientName(FieldEncryptUtil.encrypt(applicationUtils.getClientName()));
        applicationUtils.setClientIdcard(FieldEncryptUtil.encrypt(applicationUtils.getClientIdcard()));
        startPage();
        List<InheritanceCollection> inheritanceCollections = agCollectionService.stagingRecordIdDesensitization(applicationUtils);
        return getDataTable(inheritanceCollections);
    }


    /**
     * 写入分期还款申请表
     *
     * @param stagingRecord
     * @return
     */
    @Log(title = "催收管理-写入分期还款申请", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertAmortization", method = RequestMethod.POST)
    public AjaxResult insertAmortization(@RequestBody StagingRecord stagingRecord) {
        agCollectionService.insertAmortization(stagingRecord);
        return AjaxResult.success("添加成功");
    }


    /**
     * 标记案件
     *
     * @param manageQueryParam
     * @return
     */
    @Log(title = "催收管理-标记案件", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions("dispose:collection:MarkCase")
    @RequestMapping(value = "/MarkCase", method = RequestMethod.POST)
    public AjaxResult MarkCase(@RequestBody ManageQueryParam manageQueryParam) {

        agCollectionService.MarkCase(manageQueryParam);
        return AjaxResult.success("标记成功");
    }


    /**
     * 上传回款凭证文件到minio
     *
     * @param file
     * @return
     * @throws Exception
     */
    @Log(title = "催收管理-上传回款凭证", businessType = BusinessType.IMPORT)
    @RequiresLogin
    @PostMapping("/uploadPayment")
    public AjaxResult uploadPayment(MultipartFile file) throws Exception {
        MultipartFile[] files = new MultipartFile[]{file};
        String fileName = "ReceiptVoucher";
//        将Excel文件上传到minio
        String[] list = new String[]{".jpg", ".png"};  //文件后缀限制
        Map<String, Object> map = fileUpload.uploadFile(files, fileName, list);
        return AjaxResult.success("上传成功", map);
    }

    /**
     * 上传减免凭证文件到minio
     *
     * @param file
     * @return
     * @throws Exception
     */
    @Log(title = "催收管理-上传减免凭证", businessType = BusinessType.IMPORT)
    @RequiresLogin
    @PostMapping("/uploadReduction")
    public AjaxResult uploadReduction(MultipartFile[] file) throws Exception {
        String fileName = "ExemptionVoucher";
//        将Excel文件上传到minio
        String[] list = new String[]{".jpg", ".png", ".xls", ".xlsx", ".docx", ".doc", ".pdf"};  //文件后缀限制
        Map<String, Object> map = fileUpload.uploadFile(file, fileName, list);
        return AjaxResult.success("上传成功", map);
    }


    /**
     * 催收端批量发送生成短信模板短信
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/previewTemplate", method = RequestMethod.POST)
    public AjaxResult previewTemplate(@RequestBody MessageTemplatePojo messageTemplatePojo) {
        SendRecordsPojos sendRecordsPojos = agCollectionService.previewTemplate(messageTemplatePojo);
        return AjaxResult.success("查询成功", sendRecordsPojos);
    }


    /**
     * 催收端批量发送短信
     *
     * @return
     */
    @Log(title = "催收管理-发送短信", businessType = BusinessType.OTHER)
    @RequiresLogin
    @RequestMapping(value = "/sendCuiShouMessage", method = RequestMethod.POST)
    public AjaxResult sendCuiShouMessage(@RequestBody MessageTemplatePojo messageTemplatePojo) throws Exception {

        throw new ServiceException("请线下联系运营人员开通坐席！");
    }


    /**
     * 查询案件短信记录
     *
     * @param caseId 案件ID
     * @return
     */
    @GetMapping("/getSmsSendRecord/{caseId}")
    public TableDataInfo selectListWithSendRecords(@PathVariable("caseId") Long caseId) {
        SendRecords sendRecords = new SendRecords();
        sendRecords.setCaseId(caseId);
        startPage();
        List<SendRecords> sendRecord = sendRecordService.selectSendRecords(sendRecords);
        return getDataTable(sendRecord);
    }


    /**
     * 导出短信记录
     *
     * @param sendRecords
     * @return
     */
    @Log(title = "短信管理-导出短信记录", businessType = BusinessType.EXPORT)
    @PostMapping("/exportPage")
    public AjaxResult exportPage(@RequestBody SendRecords sendRecords) {
        //修改结束时间为一天的结束
        if (!ObjectUtils.isEmpty(sendRecords.getSendTime2())) {
            DateTime dateTime = DateUtil.endOfDay(sendRecords.getSendTime2());  //一天的结束，结果：2017-03-01 23:59:59
            sendRecords.setSendTime2(dateTime);
        }
        smsExportLimitation(sendRecords);

        DataPermis dataPermis = teamSysAgService.getDataPermis();
        sendRecords.setDeptIds(dataPermis.getDeptIds());
        sendRecords.setEmployeesIds(dataPermis.getEmployeesIds());
        sendRecords.setCreatedByIds(teamSysAgService.getEmployeesIds());
        Object[] params = new Object[]{sendRecords};
        String fileName = exportAgService.exportTask(CsExportClassEnum.CS_SMS_RECORD_EXPORT,
                DownloadTaskAgService.class, "downloadSms",
                params, "导出短信记录");
        return AjaxResult.success("操作成功", fileName);
    }

    /**
     * 短信导出限制
     *
     * @param sendRecords
     */
    public void smsExportLimitation(SendRecords sendRecords) {
        //导出必须要有 ‘发起时间’ 或者‘回复时间’ 的搜索
        boolean sendTime = false;
        boolean recvtime = false;
        if (sendRecords.getSendTime1() != null && sendRecords.getSendTime2() != null) {
            sendTime = true;
        }
        if (sendRecords.getRecvtime1() != null && sendRecords.getRecvtime2() != null) {
            recvtime = true;
        }
        if (!sendTime && !recvtime) {
            throw new ServiceException("导出时发起时间或者回复时间范围不能为空");
        }


        if (sendTime) {
            long betweenDay = DateUtil.between(DateUtil.beginOfDay(sendRecords.getSendTime1()),
                    DateUtil.endOfDay(sendRecords.getSendTime2()), DateUnit.DAY);
            System.out.println("相差天数：" + betweenDay);
            if (betweenDay >= 7) {
                throw new ServiceException("导出时发起时间范围不能大于7天");
            }
        }
        if (recvtime) {
            long betweenDay = DateUtil.between(
                    DateUtil.beginOfDay(sendRecords.getRecvtime1()),
                    DateUtil.endOfDay(sendRecords.getRecvtime2()), DateUnit.DAY);
            System.out.println("相差天数：" + betweenDay);
            if (betweenDay >= 7) {
                throw new ServiceException("导出时回复时间范围不能大于7天");
            }
        }
    }

    /**
     *获取案件详情 记录类别Tab
     * @param caseId
     * @return
     */
    @GetMapping("/selectWithTab")
    public AjaxResult selectWithTab(Long caseId){
        if (caseId==null){ return AjaxResult.error("案件ID不能为空"); }
        List<Option> options = collectionService.selectWithTab(caseId);
        return AjaxResult.success(options);
    }

    /**
     * 海德v.1.1.9需求申请分期
     *
     * @param stagingRecordVo
     * @return
     */
    @Log(title = "催收管理-申请分期-查询初始还款计划", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/initialRepaymentPlan", method = RequestMethod.POST)
    public AjaxResult initialRepaymentPlan(@RequestBody StagingRecordVo stagingRecordVo) {
        if (StrUtil.isBlankIfStr(stagingRecordVo.getStagingNum())) throw new ServiceException("还款期数不能为空！");
        if (StrUtil.isBlankIfStr(stagingRecordVo.getTotalRepayment())) throw new ServiceException("分期后还款总金额不能为空！");
        if (StrUtil.isBlankIfStr(stagingRecordVo.getRepaymentDate())) throw new ServiceException("每期还款时间不能为空！");
        List<InfoPlanVo> infoPlanList = agCollectionService.initialRepaymentPlan(stagingRecordVo);
        return AjaxResult.success(infoPlanList);
    }

    /**
     * 申请案件重组（暂时不用，使用回旧审批）
     *
     * @param recordVo
     * @return
     */
    @Log(title = "催收管理-申请案件重组-查询初始还款计划", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/initialRepaymentPlan2", method = RequestMethod.POST)
    public AjaxResult initialRepaymentPlan2(@RequestBody RecombinationRecordVo recordVo) {
        if (StrUtil.isBlankIfStr(recordVo.getStagingNum())) throw new ServiceException("分期期数不能为空！");
        if (StrUtil.isBlankIfStr(recordVo.getTotalRepayment())) throw new ServiceException("分期后还款总金额不能为空！");
        if (StrUtil.isBlankIfStr(recordVo.getRepaymentDate())) throw new ServiceException("每期还款时间不能为空！");
        List<InfoPlanVo> infoPlanList = agCollectionService.initialRepaymentPlan2(recordVo);
        return AjaxResult.success(infoPlanList);
    }

    /**
     * 根据案件id查询重组记录
     *
     * @param caseId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectReorganizationRecord/{caseId}", method = RequestMethod.GET)
    public TableDataInfo selectReorganizationRecord(@PathVariable("caseId") Long caseId) {
        startPage();
        List<ReorganizationVo> records = agCollectionService.reorganizationAuthority(caseId);
        return getDataTable(records);
    }

    @RequestMapping(value = "/getRepaymentMoney/{caseId}", method = RequestMethod.GET)
    public AjaxResult getRepaymentMoney(@PathVariable("caseId") Long caseId) {
        InfoLoan infoLoan = recordService.selectInfoLoanByCaseId(caseId);
        BigDecimal amountCalledBack = infoLoan.getAmountCalledBack();
        Map<String, Object> hashMap = new HashMap<>();

        if (ObjectUtils.isEmpty(amountCalledBack) || (amountCalledBack.compareTo(BigDecimal.ZERO) == 0)){
            hashMap.put("amountCalledBack",0);
            hashMap.put("principalCalledBack",0);
            return AjaxResult.success(hashMap);
        }
        OffsetParam param4 = new OffsetParam();
        param4.setRepaymentAmount(amountCalledBack);
        param4.setSyYhPrincipal(infoLoan.getSyYhPrincipal());
        param4.setSyYhInterest(infoLoan.getSyYhInterest());
        param4.setSyYhFees(infoLoan.getSyYhFees());
        param4.setSyyhwsDisbursement(infoLoan.getSyyhwsDisbursement());
        OffsetOrderCalculate calculate4 = executeOffsetting(param4, getDefaultOffsetOrder());

        hashMap.put("amountCalledBack",amountCalledBack);
        if (calculate4.getOffsetPrincipal()!= null){
            BigDecimal ycAbdPrincipal = infoLoan.getYcAbdPrincipal();
            if (ycAbdPrincipal == null){
                ycAbdPrincipal = BigDecimal.ZERO;
            }
            hashMap.put("principalCalledBack",calculate4.getOffsetPrincipal().add(ycAbdPrincipal));
        }else {
            hashMap.put("principalCalledBack",0);
        }

        return AjaxResult.success(hashMap);
    }

    /**
     * 获取默认的冲减顺序
     * @return
     */
    private List<String> getDefaultOffsetOrder() {
        List<String> list=new ArrayList<>();
        //财信回款冲减顺序：我司垫付费用—本金—垫付费用—利息
        list.add(ExpenseItemEnum.ADVANCE_FEES.getCode());
        list.add(ExpenseItemEnum.PRINCIPAL.getCode());
        list.add(ExpenseItemEnum.FEES.getCode());
        list.add(ExpenseItemEnum.INTEREST.getCode());
        return list;
    }

    /**
     * 执行冲减
     * @param offsetParam   冲减参数(剩余应还本金、剩余应还利息、剩余应还费用、冲减金额)
     * @param offsetOrders 冲减顺序
     */
    public OffsetOrderCalculate executeOffsetting(OffsetParam offsetParam, List<String> offsetOrders){
        OffsetOrderCalculate calculate = new OffsetOrderCalculate();
        //剩余冲减的金额
        BigDecimal syRepaymentMoney = offsetParam.getRepaymentAmount();
        //案件ID获取冲减顺序

        //初始冲减后余额=冲减前的余额,冲减金额=0
        calculate.setSyYhPrincipal(offsetParam.getSyYhPrincipal());
        calculate.setSyYhInterest(offsetParam.getSyYhInterest());
        calculate.setSyYhFees(offsetParam.getSyYhFees());
        calculate.setSyyhwsDisbursement(offsetParam.getSyyhwsDisbursement());
        calculate.setOffsetPrincipal(BigDecimal.ZERO);
        calculate.setOffsetInterest(BigDecimal.ZERO);
        calculate.setOffsetFees(BigDecimal.ZERO);
        calculate.setOffsetWsDisbursement(BigDecimal.ZERO);

        for (String expenseItem : offsetOrders) {
            //如果冲减余额小于等于0 则跳出
            if (syRepaymentMoney.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            ExpenseItemEnum expenseItemEnum = ExpenseItemEnum.getByCode(expenseItem);
            switch (expenseItemEnum) {
                case PRINCIPAL:
                    //本金
                    if (syRepaymentMoney.compareTo(offsetParam.getSyYhPrincipal()) > 0) {
                        //如果剩余冲减余额 大于 剩余应还本金,
                        // 剩余应还本金归零,冲减的本金就是剩余应还本金
                        syRepaymentMoney = syRepaymentMoney.subtract(offsetParam.getSyYhPrincipal());
                        calculate.setOffsetPrincipal(offsetParam.getSyYhPrincipal());
                        calculate.setSyYhPrincipal(BigDecimal.ZERO);
                    } else {
                        //如果剩余冲减余额 小于等于 剩余应还本金,
                        // 冲减后剩余应还本金=剩余回款金额减冲减余额,冲减余额归零,冲减本金=剩余冲减余额
                        BigDecimal syYhPrincipal = offsetParam.getSyYhPrincipal().subtract(syRepaymentMoney);

                        calculate.setOffsetPrincipal(syRepaymentMoney);
                        calculate.setSyYhPrincipal(syYhPrincipal);
                        syRepaymentMoney = BigDecimal.ZERO;
                    }
                    break;
                case INTEREST:
                    //利息
                    if (syRepaymentMoney.compareTo(offsetParam.getSyYhInterest()) > 0) {
                        //如果剩余回款余额 大于 剩余应还利息,
                        syRepaymentMoney = syRepaymentMoney.subtract(offsetParam.getSyYhInterest());
                        calculate.setSyYhInterest(BigDecimal.ZERO);
                        calculate.setOffsetInterest(offsetParam.getSyYhInterest());
                    } else {
                        //如果剩余回款余额 小于等于 剩余应还利息
                        BigDecimal syYhInterest = offsetParam.getSyYhInterest().subtract(syRepaymentMoney);

                        calculate.setSyYhInterest(syYhInterest);
                        calculate.setOffsetInterest(syRepaymentMoney);
                        syRepaymentMoney = BigDecimal.ZERO;
                    }
                    break;
                case FEES:
                    //费用
                    if (syRepaymentMoney.compareTo(offsetParam.getSyYhFees()) > 0) {
                        //如果剩余回款余额 大于 剩余应还费用
                        syRepaymentMoney = syRepaymentMoney.subtract(offsetParam.getSyYhFees());
                        calculate.setOffsetFees(offsetParam.getSyYhFees());
                        calculate.setSyYhFees(BigDecimal.ZERO);
                    } else {
                        //如果剩余回款余额 小于等于 剩余应还利息
                        BigDecimal  syYhFees = offsetParam.getSyYhFees().subtract(syRepaymentMoney);
                        calculate.setSyYhFees(syYhFees);
                        calculate.setOffsetFees(syRepaymentMoney);
                        syRepaymentMoney = BigDecimal.ZERO;
                    }
                    break;
                case ADVANCE_FEES:
                    //我司垫付费用
                    if (syRepaymentMoney.compareTo(offsetParam.getSyyhwsDisbursement()) > 0) {
                        syRepaymentMoney = syRepaymentMoney.subtract(offsetParam.getSyyhwsDisbursement());
                        calculate.setOffsetWsDisbursement(offsetParam.getSyyhwsDisbursement());
                        calculate.setSyyhwsDisbursement(BigDecimal.ZERO);
                    } else {
                        BigDecimal  syyhwsDisbursement = offsetParam.getSyyhwsDisbursement().subtract(syRepaymentMoney);
                        calculate.setSyyhwsDisbursement(syyhwsDisbursement);
                        calculate.setOffsetWsDisbursement(syRepaymentMoney);
                        syRepaymentMoney = BigDecimal.ZERO;
                    }
                    break;
            }
        }
        return calculate;

    }

}
