package com.zws.appeal.controller;

import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.appeal.agservice.AgBrokerageTeamService;
import com.zws.appeal.domain.BrokerageTeam;
import com.zws.appeal.domain.DetailsBrokerage;
import com.zws.appeal.domain.RecoveryAsset;
import com.zws.appeal.domain.RiskTeam;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.pojo.AggregatedData;
import com.zws.appeal.service.BrokerageTeamService;
import com.zws.appeal.utils.FileDownloadUtil;
import com.zws.appeal.utils.Log;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 佣金结算
 *
 * @Author: 马博新
 * @DATE: Created in 2023/4/27 14:29
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/brokerageTeam")
public class BrokerageTeamController extends BaseController {

    @Autowired
    private AgBrokerageTeamService agBrokerageTeamService;
    @Autowired
    private BrokerageTeamService brokerageTeamService;

    /**
     * 根据结佣日期（年/月）查询本机构的结佣详情表数据
     */
    @RequiresLogin
    @RequestMapping(value = "/selectDetailsBrokerage", method = RequestMethod.GET)
    public AjaxResult info(BrokerageTeam brokerageTeam) {
        if (ObjectUtils.isEmpty(brokerageTeam) || brokerageTeam.getSettlementDate() == null) {
            return AjaxResult.error("查询日期不能为空");
        }
        DetailsBrokerage detailsBrokerage = agBrokerageTeamService.selectDetailsBrokerage(brokerageTeam);
        return AjaxResult.success(detailsBrokerage);
    }



    /**
     * 根据佣金详情表id查询机构风险奖罚设置
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRiskTeam", method = RequestMethod.GET)
    public AjaxResult infoWithRiskTeam(Long detailsId) {
        if (detailsId == null) {
            return AjaxResult.error("佣金详情表id不能为空");
        }
        List<RiskTeam> riskTeams = brokerageTeamService.selectRiskTeam(detailsId);
        return AjaxResult.success(riskTeams);
    }

    /**
     * 根据佣金详情表id查询统计回收合计以及风险奖惩合计
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/statisticsMoney", method = RequestMethod.GET)
    public AjaxResult infoWithStatisticsMoney(Long detailsId) {
        if (detailsId == null) {
            return AjaxResult.error("佣金详情表id不能为空");
        }
        AggregatedData aggregatedData = agBrokerageTeamService.statisticsMoney(detailsId);
        return AjaxResult.success(aggregatedData);
    }

    /**
     * 根据佣金详情表id查询资产回收情况
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRecoveryAsset", method = RequestMethod.GET)
    public TableDataInfo selectListWithRecoveryAsset(Long detailsId) {
        if (detailsId == null) {
            throw new GlobalException("佣金详情表id不能为空");
        }
        startPage();
        List<RecoveryAsset> recoveryAssets = brokerageTeamService.selectRecoveryAsset(detailsId);
        return getDataTable(recoveryAssets);
    }

    /**
     * 有异议（驳回佣金结算信息）
     *
     * @param map 请求参数
     * @return
     */
    @Log(title = "佣金结算-有异议，驳回佣金结算信息", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/rejectObjection", method = RequestMethod.POST)
    public AjaxResult rejectObjection(@RequestBody Map<String, Object> map) {
        Integer detailsId = (Integer) map.get("detailsId");
        String rejectReason = (String) map.get("rejectReason");
        if (detailsId == null) {
            return AjaxResult.error("佣金详情表id不能为空");
        }
        if (rejectReason == null) {
            return AjaxResult.error("驳回原因不能为空");
        }
        if (rejectReason.length() > 220) {
            return AjaxResult.error("驳回原因长度不能超过220字符");
        }
        agBrokerageTeamService.rejectObjection(detailsId.longValue(), rejectReason);
        return AjaxResult.success();
    }

    /**
     * 点击确认提交结算佣金
     *
     * @param map 请求参数
     * @return
     */
    @Log(title = "佣金结算-点击确认提交结算佣金", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    public AjaxResult confirm(@RequestBody Map<String, Object> map) {
        Integer detailsId = (Integer) map.get("detailsId");
        if (detailsId == null) {
            return AjaxResult.error("佣金详情表id不能为空");
        }
        agBrokerageTeamService.confirm(detailsId.longValue());
        return AjaxResult.success();
    }

    /**
     * 导出--(查询返回文件名称)
     *
     * @param detailsId 佣金详情表id
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/exportFileName", method = RequestMethod.GET)
    public AjaxResult exportFileName(Long detailsId) throws Exception {
        Map<String, Object> map = agBrokerageTeamService.exportInformation(detailsId);
        String fileName = (String) map.get("fileName");
        return AjaxResult.success("操作成功", fileName + ".pdf");
    }

    /**
     * 导出
     *
     * @param detailsId 佣金详情表id
     * @return
     */
    @Log(title = "佣金结算-导出", businessType = BusinessType.EXPORT)
    @RequiresLogin
    @RequestMapping(value = "/exportInformation")
    public AjaxResult exportInformation(Long detailsId, HttpServletResponse response) throws Exception {
        if (detailsId == null) {
            return AjaxResult.error("佣金详情表id不能为空");
        }
        Map<String, Object> map = agBrokerageTeamService.exportInformation(detailsId);
        String fileUrl = (String) map.get("fileUrl");
        String fileName = (String) map.get("fileName");
        File file = FileDownloadUtil.downloadTempFile(fileUrl);
        if (!file.exists()) {
            throw new GlobalException("文件路径错误，未找到该文件");
        }

        ServletOutputStream out = null;
        ByteArrayOutputStream baos = null;
        try {
            InputStream inStream = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            int len;
            baos = new ByteArrayOutputStream();
            while ((len = inStream.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
//            获取文件类型
            String name = file.getName();
            String substring = name.substring(name.indexOf(".") + 1);
            String newName = fileName + "." + substring;
            System.out.println("文件名字:" + newName);
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(newName, "utf-8"));
            out = response.getOutputStream();
            out.write(baos.toByteArray());
            inStream.close();
            log.info("IO流输出完毕:" + new Date());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            baos.flush();
            baos.close();
            out.flush();
            out.close();
        }
//        删除本地文件
        FileDownloadUtil.deletedTempFile(file);
        return AjaxResult.success("操作成功");
    }

}
