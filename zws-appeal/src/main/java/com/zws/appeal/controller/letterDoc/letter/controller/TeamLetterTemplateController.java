package com.zws.appeal.controller.letterDoc.letter.controller;

import cn.hutool.core.util.StrUtil;
import com.zws.common.core.domain.Option;
import com.zws.common.core.domain.R;
import com.zws.common.core.enums.letter.LetterTypeEnum;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.signature.Stamp;
import com.zws.common.core.utils.pdf.PdfUtils;
import com.zws.common.core.utils.pdf.po.PdfFile;
import com.zws.common.core.utils.poi.ExportUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.domain.SysFile;
import com.zws.appeal.controller.letterDoc.letter.FileUtils;
import com.zws.appeal.controller.letterDoc.letter.agservice.LetterTemplateAgService;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterDocService;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.poi.poifs.filesystem.DirectoryEntry;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

/**
 * 团队文书模板
 * <AUTHOR>
 * @date ：Created in 2023/12/11 15:40
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("/document/template")
public class TeamLetterTemplateController extends BaseController {

    @Autowired
    private ILetterTemplateService templateService;
    @Autowired
    private LetterTemplateAgService templateAgService;
    @Autowired
    private RemoteFileService remoteFileService;
//    @Autowired
//    private RemoteSaasService remoteSaasService;
    @Autowired
    private ILetterDocService iLetterDocService;

    /**
     * 列表
     * @param template
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo list(LetterTemplate template){
        startPage();
        List<LetterTemplate> list=templateService.selectList(template);
        return getDataTable(list);
    }

    /**
     * 创建
     *
     * @param template
     * @return
     */
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody LetterTemplate template){
        template.setType(LetterTypeEnum.WEN_SHU.getCode());
        template.setTeamId(SecurityUtils.getTeamId());
        templateService.insert(template);
        return AjaxResult.success();
    }

    /**
     * 编辑
     * @param template
     * @return
     */
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody LetterTemplate template){
        template.setType(LetterTypeEnum.WEN_SHU.getCode());
        templateService.updateById(template);
        return AjaxResult.success();
    }

    /**
     * 编辑状态
     *
     * @param template
     * @return
     */
    @PostMapping("/editStatus")
    public AjaxResult editStatus(@RequestBody LetterTemplate template){
        LetterTemplate entity=new LetterTemplate();
        entity.setId(template.getId());
        entity.setStatus(template.getStatus());
        templateService.updateEditStatus(entity);
        return AjaxResult.success();
    }


    /**
     * 获取模板详情
     * @param id
     * @return
     */
    @GetMapping("/getDetails")
    public AjaxResult getDetails(Long id){
        LetterTemplate template= templateService.getById(id);
        if (template==null){
            return AjaxResult.error("ID错误,查询失败");
        }
        return AjaxResult.success(template);
    }

    /**
     * 详情 获取预览
     * @param id
     * @return
     */
    @GetMapping("/getPreview")
    public AjaxResult getPreview(Long id){
        LetterTemplate template= templateService.getById(id);
        if (template==null){
            return AjaxResult.error("ID错误,查询失败");
        }
        String url=template.getPreviewUrl();
        Integer total= template.getPreviewPages();
        Map<String,Object> params=new HashMap<>(2);
        params.put("url",url);
        params.put("total",total);
        return AjaxResult.success("操作成功",params);
    }

    /**
     * 创建、编辑时 生成预览
     * @return
     */
    @PostMapping("/createTemplatePreview")
    public AjaxResult createTemplatePreview(@RequestBody LetterTemplate template){
        PdfFile pdfFile = new PdfFile();
        List<Stamp> stamp = template.getPositionList();
        boolean isE=false;
        for (Stamp stamp1 : stamp) {
            if (stamp1.getBottom()==null && stamp1.getLeft() == null){
                isE=true;
            }
        }

        if(template.getPositionList()==null || isE){
            pdfFile = templateAgService.createTemplatePdf(template);
        }
        else {
            pdfFile = templateAgService.createTemplatePreviews(template);
        }
        Map<String,Object> params=new HashMap<>(2);
        params.put("url",pdfFile.getUrl());
        params.put("total",pdfFile.getPages());
        return AjaxResult.success("操作成功",params);
    }

    /**
     * 创建不盖章的pdf文件
     * @param template
     * @return
     */
    @PostMapping("/createTemplatePdf")
    public AjaxResult createTemplatePdf(@RequestBody LetterTemplate template){
        PdfFile pdfFile= templateAgService.createTemplatePdf(template);
        Map<String,Object> params=new HashMap<>(2);
        params.put("url",pdfFile.getUrl());
        params.put("total",pdfFile.getPages());
        return AjaxResult.success("操作成功",params);
    }

    /**
     * 判断模板名称是否唯一
     * @param template
     * @return
     */
    @GetMapping("/checkUniqueName")
    public AjaxResult checkUniqueName(LetterTemplate template){
        boolean unique= templateService.checkUniqueName(template);
        if (unique){
            return AjaxResult.success();
        }else{
            return AjaxResult.error("名称重复，请换一个");
        }
    }

//    /**
//     * 获取响应的用户选项
//     * @param
//     * @return
//     */
//    @GetMapping("/user/getOption")
//    public AjaxResult getOption(){
//        R<List<Option>> r = remoteSaasService.getUserOption(SecurityConstants.INNER);
//        return AjaxResult.success(r.getData());
//    }

    /**
     * 获取模板选项
     * 0:Excel类型模板 1:Zip类型模板
     * @return
     */
    @GetMapping("/getOptions")
    public AjaxResult getOptions(Integer type){
        LetterTemplate entity=new LetterTemplate();
        entity.setTeamId(SecurityUtils.getTeamId());
        entity.setType(LetterTypeEnum.WEN_SHU.getCode());
        entity.setSourceFileType(type);
        List<Option> options= templateService.getOptions(entity);
        return AjaxResult.success(options);
    }

    /**
     * 下载模板变量
     */
    @PostMapping("/downloadTemplateVariable")
    public void downloadTemplate(HttpServletResponse response, @RequestBody LetterTemplate template) throws IOException {
        LetterTemplate entity= templateService.getById(template.getId()) ;
        if (entity==null){
            throw  new ServiceException("查询信息失败");
        }
        String templateVariable= entity.getTemplateVariable();
        String[] variables= templateVariable.split(",");
        ExportUtils.customExcelHeaderText(response, Arrays.asList(variables), entity.getTemplateName()+"_导入模板.xlsx","");
    }

    /**
     * 页眉页脚上传 进行缩放，再进行上传。
     * @param file
     * @return 原图存储路径、图片缩放后的存储路径
     */
    @PostMapping("/upload")
    public AjaxResult uploadSignPic(MultipartFile[] file) {
        //String[] list = new String[]{".jpg", ".png", ".jpeg"};
        HashMap<String, Object> map = new HashMap<>();
        R<SysFile> upload = remoteFileService.upload(file[0]);
//        文件url路径
        map.put("fileUrl", upload.getData().getUrl());

        MultipartFile multipartFile = null;
        File scaleFile = PdfUtils.getScalePicByMultipartFile(file[0]);
        try (FileInputStream inputStream  = new FileInputStream(scaleFile)){
            multipartFile = new MockMultipartFile(scaleFile.getName(), scaleFile.getName(),
                    ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
            MultipartFile[] file2 = new MultipartFile[1];
            file2[0] = multipartFile;
//            Map<String, Object>  map2 = fileUpload.uploadFile(file2, "scalePicFile", list);
            R<SysFile> upload2 = remoteFileService.upload(file[0]);
            map.put("scaleFileUrl",upload2.getData().getUrl());
            map.put("",null);
        } catch (IOException e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }finally {
            if (scaleFile!=null){ FileUtils.deletedTempFile(scaleFile); }
        }
        return AjaxResult.success("上传成功", map);
    }

    /**
     * pdf转word文件流
     * @param letterDoc
     * @param response
     */
    @PostMapping("/getWord")
    public void getWord(HttpServletResponse response,@RequestBody LetterDoc letterDoc) throws Exception {
        LetterDoc letterDoc1 = iLetterDocService.selectByPreviewUrl(letterDoc.getPreviewUrl());
        if (StrUtil.isBlankIfStr(letterDoc1.getContent())) {
            throw new ServiceException("文书的样式未保存，请重新生成文书");
        }
        byte b[] = letterDoc1.getContent().getBytes("UTF-8");
        ByteArrayInputStream bai = new ByteArrayInputStream(b);
        POIFSFileSystem poifsFileSystem = new POIFSFileSystem();
        DirectoryEntry directoryEntry = poifsFileSystem.getRoot();
        directoryEntry.createDocument("WordDocument", bai);
        // 设置响应头
        response.setContentType("application/msword");
        response.setHeader("Content-Disposition", "attachment; filename=" + letterDoc1.getSerialNo() + ".docx");
        // 获取响应输出流
        OutputStream outputStream = response.getOutputStream();
        // 将Word文档写入到响应流中
        poifsFileSystem.writeFilesystem(outputStream);
        // 关闭流
        outputStream.flush();
        outputStream.close();
        bai.close();
    }
}
