package com.zws.appeal.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 团队申请回款查询返回实体类
 */
@Data
public class CreateRepaymentRecordResp implements Serializable {

    /**
     * 申请表id
     */
    private Long id;

    /**
     * 案件id
     */
    private Long caseId;

    /**
     * 客户姓名
     */
    private String clientName;

    /**
     * 客户身份证号
     */
    private String clientIdcard;

    /**
     * 证件类型
     */
    private String clientIdType;

    /**
     * 委案批号
     */
    private String entrustingCaseBatchNum;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 委托金额
     */
    private BigDecimal clientMoney;

    /**
     * 委按日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entrustingCaseDate;

    /**
     * 催收员名称
     */
    private String odvName;

    /**
     * 登记时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 还款金额
     */
    private BigDecimal repaymentMoney;

    /**
     * 回款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date repaymentDate;

    /**
     * 还款类型
     */
    private String repaymentType;

    /**
     * 审核状态
     */
    private String examineState;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 该案件是否属于本团队(0-不属于,1-属于)
     */
    private Integer button;
}
