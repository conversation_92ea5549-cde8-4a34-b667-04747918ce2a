package com.zws.appeal.controller.request;

import com.zws.appeal.domain.call.CallDownloadTask;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 录音下载任务 查询参数
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/24 11:57
 */
@Data
public class CallDownloadTaskParamRequest extends CallDownloadTask {

    /**
     * 创建时间-开始
     */
    private Date createTime1;
    /**
     * 创建时间-截止
     */
    private Date createTime2;
    /**
     * 部门id集合
     */
    private List<Integer> deptIds;

    /**
     * 员工id集合
     */
    private List<Integer> employeesIds;


    /**
     * 子账号创建人id
     */
    private List<Integer> createdByIds;




}
