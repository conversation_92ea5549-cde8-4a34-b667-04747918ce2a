package com.zws.appeal.controller.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.annotation.Excel;
import com.zws.common.core.exception.GlobalException;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 生成评价表实体类
 *
 * @Author: 马博新
 * @DATE: Created in 2023/5/30 10:18
 */
@Data
public class EvaluationParamRequest {


    /**
     * 日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recordTime1;

    /**
     * 日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recordTime2;

    /**
     * 日期开始-(查询用)
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 日期结束-(查询用)
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 团队id
     */
    private Long teamId;

//---------------------------------------------------------------------------------------------------------------------

    /**
     * 回款率达成-评分
     */
    @Excel(name = "回款率达成评分")
    private BigDecimal serviceQuality;

    /**
     * 回款率达成-扣分
     */
    @Excel(name = "回款率达成扣分")
    private BigDecimal serviceDeductPoints;

    /**
     * 回款率达成-备注
     */
    @Excel(name = "回款率达成备注")
    private String serviceRemarks;

    /**
     * 合规作业-评分
     */
    @Excel(name = "合规作业评分")
    private BigDecimal informationSafety;

    /**
     * 合规作业-扣分
     */
    @Excel(name = "合规作业扣分")
    private BigDecimal informationDeductPoints;

    /**
     * 合规作业-备注
     */
    @Excel(name = "合规作业备注")
    private String informationRemarks;

    /**
     * 案件投诉-评分
     */
    @Excel(name = "案件投诉评分")
    private BigDecimal complianceManagement;

    /**
     * 案件投诉-扣分
     */
    @Excel(name = "案件投诉扣分")
    private BigDecimal complianceDeductPoints;

    /**
     * 案件投诉-备注
     */
    @Excel(name = "案件投诉备注")
    private String complianceRemarks;

    /**
     * 个人信息安全-评分
     */
    @Excel(name = "个人信息安全评分")
    private BigDecimal personalScore;

    /**
     * 个人信息安全-扣分
     */
    @Excel(name = "个人信息安全扣分")
    private BigDecimal personalDeductPoints;

    /**
     * 个人信息安全-备注
     */
    @Excel(name = "个人信息安全备注")
    private String personalRemarks;

    /**
     * 综合评分
     */
    @Excel(name = "综合评分")
    private BigDecimal comprehensiveScore;


    public void setServiceRemarks(String serviceRemarks) {
        if (serviceRemarks.length() > 300) {
            throw new GlobalException("(回款率达成)备注-字数限制300字");
        }
        this.serviceRemarks = serviceRemarks;
    }

    public void setInformationRemarks(String informationRemarks) {
        if (informationRemarks.length() > 300) {
            throw new GlobalException("(合规作业)备注-字数限制300字");
        }
        this.informationRemarks = informationRemarks;
    }

    public void setComplianceRemarks(String complianceRemarks) {
        if (complianceRemarks.length() > 300) {
            throw new GlobalException("(案件投诉)备注-字数限制300字");
        }
        this.complianceRemarks = complianceRemarks;
    }

    public void setPersonalRemarks(String personalRemarks) {
        if (personalRemarks.length() > 300) {
            throw new GlobalException("(个人信息安全)备注-字数限制300字");
        }
        this.personalRemarks = personalRemarks;
    }
}
