package com.zws.appeal.controller.response;

import com.zws.appeal.domain.appeal.FreezeRecord;
import com.zws.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 材料寄送-统计查询
 *
 * @Author：huang<PERSON>uf
 * @Date：2024/7/1 10:48
 */
@Data
public class MaterialDeliveryCountResp {
    /**
     * 案件数量
     */
    private Long size;

    /**
     * 标的额
     */
    private String money;

    /**
     * 判决金额
     */
    private String concludeCaseAmount;



}
