package com.zws.appeal.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 团队工单查询返回实体类
 */
@Data
public class CreateWorkOrderResp implements Serializable {

    /**
     * 申请表id
     */
    private Long id;

    /**
     * 案件id
     */
    private Long caseId;

    /**
     * 客户姓名
     */
    private String clientName;

    /**
     * 客户身份证号
     */
    private String clientIdcard;
    /**
     * 证件类型
     */
    private String clientIdType;
    /**
     * 客户电话
     */
    private String clientPhone;

    /**
     * 工单类型
     */
    private String questionType;

    /**
     * 催收员名称
     */
    private String odvName;

    /**
     * 渠道来源
     */
    private String channelSource;

    /**
     * 工单状态
     */
    private String orderStatus;

    /**
     * 来电号码
     */
    private String callNumber;

    /**
     * 创建者id
     */
    private Long createById;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 处理人id
     */
    private Long handlerId;

    /**
     * 处理人
     */
    private String handler;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processingTime;

    /**
     * 问题内容
     */
    private String questionContent;

    /**
     * 该案件是否属于本团队(0-不属于,1-属于)
     */
    private Integer button;

    /**
     * 委案批号
     */
    private String entrustingCaseBatchNum;

    /**
     * uid
     */
    private String uid;

}
