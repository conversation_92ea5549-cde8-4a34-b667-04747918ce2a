package com.zws.appeal.controller.letterDoc.letter.controller;

import com.zws.common.core.domain.Option;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterVariable;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterVariableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 团队文书参数设置
 * <AUTHOR>
 * @date ：Created in 2023/11/23 20:48
 */
@CrossOrigin
@RestController
@RequestMapping("document/template/variable")
public class TeamLetterVariableController extends BaseController {

    @Autowired
    private ILetterVariableService letterVariableService;



    /**
     * 列表
     * @param variable
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo list(LetterVariable variable){
        startPage();
        List<LetterVariable> list=this.letterVariableService.selectList(variable);
        return getDataTable(list);
    }

    /**
     * 新增、创建
     * @param variable
     * @return
     */
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody LetterVariable variable){
        this.letterVariableService.insert(variable);
        return AjaxResult.success();
    }

    /**
     * 新增、创建
     * @param variable
     * @return
     */
    @PostMapping("/edit")
    public AjaxResult edit( @RequestBody LetterVariable variable){
        this.letterVariableService.updateById(variable);
        return AjaxResult.success();
    }

    /**
     * 编辑状态
     * @param variable
     * @return
     */
    @PostMapping("/editStatus")
    public AjaxResult editStatus(@RequestBody LetterVariable variable){
        LetterVariable entity=new LetterVariable();
        entity.setStatus(variable.getStatus());
        entity.setId(variable.getId());
        this.letterVariableService.updateById(entity);
        return AjaxResult.success();
    }

    /**
     * 获取变量 选项
     * @return
     */
    @GetMapping("/getOptions")
    public AjaxResult getOptions(){
        List<Option> options=this.letterVariableService.getVariableNameVoList();
        return AjaxResult.success(options);
    }

    /**
     * 获取开启的参数
     */
    @GetMapping("/getOpenOptions")
    public AjaxResult getOpenOptions(){
        List<Option> options=this.letterVariableService.getOpenOptions();
        return AjaxResult.success(options);
    }
}
