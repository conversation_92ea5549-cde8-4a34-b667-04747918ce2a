package com.zws.appeal.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.util.Date;

/**
 * 我的审批（资料调取）查询返回实体类
 *
 * @Author: 马博新
 * @DATE: Created in 2023/2/10 14:54
 */
@Data
public class MyRetrievalRecordResp implements Comparable<MyRetrievalRecordResp> {

    /**
     * 该案件是否属于本团队(0-不属于,1-属于)
     */
    private Integer button;

    /**
     * 主键id（申请id）
     */
    private Long id;

    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 案件ID
     */
    private Long caseId;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyDate;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 申请人id
     */
    private Long applicantId;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 审核状态
     */
    private String examineState;

    /**
     * 审核时间-最后审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date examineTime;

    /**
     * 审核人
     */
    private String examineBy;

    /**
     * 审核人ID
     */
    private Long examineById;

    /**
     * 审核进程，0-待审核，1-催收端审核中，2-催收端审核完成，3-资产端审核中，4-资产端审核完成，5-审核结束，6-撤销
     */
    private Integer proce;

    /**
     * 审核顺序
     */
    private Integer proceSort;

    /**
     * 删除标记,0表示存在
     */
    private Integer delFlag;

    /**
     * 委案批次号
     */
    private String entrustingCaseBatchNum;

    /**
     * 委案日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entrustingCaseDate;

    /**
     * 退案日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date returnCaseDate;

    /**
     * 催收员ID
     */
    private Long odvId;

    /**
     * 催收员名字
     */
    private String odvName;

    /**
     * 操作人类型（0-主账号;1-员工账号;2-资产端账号）
     */
    private Integer operationType;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 客户姓名
     */
    private String clientName;

    /**
     * 客户身份证号
     */
    private String clientIdcard;

    /**
     * 审核数据历史表id
     */
    private Long approveId;

    /**
     * 申请id,根据审核类型查找不同的申请表
     */
    private Long applyId;

    /**
     * 处理状态,0-已同意，1-未同意，2-待处理
     */
    private Integer approveStart;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 标签内容
     */
    private String labelContent;

    /**
     * 标签表id
     */
    private Integer labelId;

    /**
     * 标签颜色（0-黑色，1-粉色，2-橙色，3-绿色，4-蓝色，5-紫色，6-黄色）
     */
    private Integer label;

    /**
     * 查看文件链接
     */
    private String path;

    /**
     * 文件是否过期（0-未过期，1-已过期）
     */
    private Integer fileExpiration;

    /**
     * 能否下载（1-是，2-否）
     */
    private Integer canDownload;

    /**
     * 生成随机查看码
     */
    private String random;

    @Override
    public int compareTo(@NotNull MyRetrievalRecordResp o) {
        return (int) (o.getApplyDate().getTime() - this.getApplyDate().getTime());
    }
}
