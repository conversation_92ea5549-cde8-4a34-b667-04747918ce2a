package com.zws.appeal.controller.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 实名认证查询实体类
 */
@Data
public class AuthenticationRequest implements Serializable {

    /**
     * 验证表主键id
     */
    private Long id;

    /**
     * 团队id
     */
    private Long createId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 所属公司
     */
    private String createName;

    /**
     * 所属账号
     */
    private String loginAccount;

    /**
     * 成员姓名
     */
    private String employeeName;

    /**
     * 工号
     */
    private Integer employeesWorking;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 身份证号码
     */
    private String identityCard;

    /**
     * 邮箱
     */
    private String mailbox;

    /**
     * 验证状态(0-未验证,1-已验证,2-验证失败,3-人工通过)
     */
    private Integer state;

    /**
     * 模糊查询字段
     */
    private String value;
}
