package com.zws.appeal.controller;

import com.zws.common.core.web.domain.AjaxResult;
import com.zws.system.api.RemoteCaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文件预览
 *
 * <AUTHOR>
 * @date 2024/5/8 17:32
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("/filePreview")
public class FilePreviewController {

    @Autowired
    private  RemoteCaseService remoteCaseService;

    /**
     * 获取文件预览地址
     * @return
     */
    @GetMapping("/getFileOnlinePreviewHost")
    public AjaxResult getFileOnlinePreviewHost() {
        return remoteCaseService.getFileOnlinePreviewHost();
    }

}
