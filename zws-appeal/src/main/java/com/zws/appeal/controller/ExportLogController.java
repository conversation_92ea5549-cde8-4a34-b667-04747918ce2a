package com.zws.appeal.controller;

import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.appeal.agservice.TeamSysAgService;
import com.zws.appeal.domain.log.ExportLog;
import com.zws.appeal.enums.CsExportClassEnum;
import com.zws.appeal.pojo.DataPermis;
import com.zws.appeal.service.IExportLogService;
import com.zws.appeal.utils.TokenInformation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导出日志
 *
 * <AUTHOR>
 * @date 2024年1月23日16:42:36
 */
@Slf4j
@CrossOrigin
@RestController
@EnableTransactionManagement
@RequestMapping("/asset/exportLog")
public class ExportLogController extends BaseController {

    @Autowired
    private IExportLogService exportLogService;
    @Autowired
    private TeamSysAgService teamSysAgService;

    /**
     * 查询列表
     *
     * @param record 查询参数
     * @return
     */
    @GetMapping("/getList")
    public TableDataInfo selectList(ExportLog record) {
        //查询参数处理
        record.setCreateTime1(DateUtils.beginOfDay(record.getCreateTime1()));
        record.setCreateTime2(DateUtils.endOfDay(record.getCreateTime2()));
        record.setTeamId(TokenInformation.getCreateid().longValue());
        DataPermis dataPermis = teamSysAgService.getDataPermis();
        record.getParams().put("deptIds", dataPermis.getDeptIds());
        record.getParams().put("employeesIds", dataPermis.getEmployeesIds());
        startPage();
        List<ExportLog> exportLogs = exportLogService.selectList(record);
        return getDataTable(exportLogs);
    }

    /**
     * 获取导出列表-tab
     *
     * @return
     */
    @GetMapping("/getExportClass")
    public AjaxResult selectListWithExportClass() {
        CsExportClassEnum[] values = CsExportClassEnum.values();
        List<Map<String, Object>> pojoList = new ArrayList<>();
        for (CsExportClassEnum temp : values) {
            Map<String, Object> map = new HashMap<>();
            map.put("code", temp.getCode());
            map.put("info", temp.getInfo());
            pojoList.add(map);
        }
        return AjaxResult.success(pojoList);
    }


}
