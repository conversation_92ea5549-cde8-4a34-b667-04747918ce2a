package com.zws.appeal.controller.letterDoc.letter.domain;

import com.zws.common.core.web.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 函件分类
 * <AUTHOR>
 * @date 2023年11月23日16:20:45
 */
@Data
public class LetterClassify extends BaseEntity {
    private Long id;
    /**
     *  类型名称
     */
    @NotEmpty(message = "名称不能为空")
    private String classifyName;
    /**
     * 类型标签，分号（;）分隔
     */
    private String classifyLabel;
    /**
     * 状态，0-正常(启用)，1-禁用
     */
    private Integer status;
    /**
     * 团队、机构id
     */
    private Long teamId;

    private List<String> classifyNames;

    private Long createById;

    private Long updateById;

    private String delFlag;


}
