package com.zws.appeal.controller.letterDoc.letter.mapper;

import com.zws.common.core.domain.Option;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;
import com.zws.appeal.controller.letterDoc.letter.pojo.CaseInfoPojo;

import java.math.BigDecimal;
import java.util.List;

public interface LetterTemplateMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LetterTemplate record);

    LetterTemplate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LetterTemplate record);

    /**
     * 查询列表
     * @param record
     * @return
     */
    List<LetterTemplate> selectList(LetterTemplate record);

    /**
     * 查询简单字段的列表
     * @param record
     * @return
     */
    List<LetterTemplate> selectSmallList(LetterTemplate record);

    /**
     *  获取模板选项
     * @return
     */
    List<Option> selectOptions(LetterTemplate record);

    /**
     * 获取用户基础信息
     * @param caseId
     * @return
     */
    CaseInfoPojo getCaseInfo(Long caseId);

    /**
     * 根据案件id查询案件累计已还金额
     *
     * @param caseId 案件id
     * @return
     */
    BigDecimal selectAmountCalledBack(Long caseId);
}
