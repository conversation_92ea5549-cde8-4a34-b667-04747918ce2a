package com.zws.appeal.controller.letterDoc.letter.service;

import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


public interface ZipDownloadService {
    /**
     * 批量压缩下载
     */
    void ZipDownload(List<Long> ids);
    /**
     * 保全文书压缩下载
     */
    void ZipDownloadDocument(LetterDoc letterDoc);
    /**
     * 调价函压缩下载
     */
    void ZipDownloadMediate(HttpServletResponse response,LetterDoc letterDoc) throws Exception;

    /**
     * 压缩下载Word文书
     */
//    void downloadWordDocumentsMediate(HttpServletResponse response,LetterDoc letterDoc) throws Exception;

}
