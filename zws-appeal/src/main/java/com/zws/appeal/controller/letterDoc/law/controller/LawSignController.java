package com.zws.appeal.controller.letterDoc.law.controller;

import com.zws.appeal.controller.letterDoc.law.domain.LawSign;
import com.zws.appeal.controller.letterDoc.law.enums.SignTypeEnum;
import com.zws.appeal.controller.letterDoc.law.enums.StatusEnum;
import com.zws.appeal.controller.letterDoc.law.service.ILawSignService;
import com.zws.common.core.domain.Option;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 签章管理
 * <AUTHOR>
 * @date ：Created in 2023/11/22 22:37
 */
@CrossOrigin
@RestController
@RequestMapping("/law/sign")
public class LawSignController extends BaseController {

    @Autowired
    private ILawSignService lawSignService;

    /**
     * 查询列表
     * @param params 参数
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo list(LawSign params){
        if (ObjectUtils.isEmpty(params.getCreateId())) {
            throw new GlobalException("机构id不能为空");
        }
        startPage();
        List<LawSign> lawSigns = lawSignService.selectList(params);
        return getDataTable(lawSigns);
    }

    /**
     * 新增
     * @param record
     * @return
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody @Validated LawSign record){
        if (record.getSignCode()!= null && record.getSignPic() == null){
            throw new ServiceException("签章图片不能为空！");
        }
        if (record.getSignPic() == null && record.getSignCode() == null){
            throw new ServiceException("签章id和签章图片不能为空");
        }
        this.lawSignService.insert(record);
        return AjaxResult.success();
    }

    /**
     * 编辑
     * @param record
     * @return
     */
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody @Validated LawSign record){
        if (record.getSignCode()!=null && record.getSignPic()==null){
            throw new ServiceException("签章图片不能为空！");
        }
        if (record.getSignPic() == null && record.getSignCode() == null){
            throw new ServiceException("签章id和签章图片不能为空");
        }
        this.lawSignService.updateById(record);
        return AjaxResult.success();
    }
    /**
     * 删除
     * @param record
     * @return
     */
    @PostMapping("/remove")
    public AjaxResult remove(@RequestBody LawSign record){
        this.lawSignService.deleteById(record.getId());
        return AjaxResult.success();
    }

    /**
     * 导出
     * @param record
     * @return
     */
    @PostMapping("/export")
    public void export (HttpServletResponse response,@RequestBody LawSign record){
        List<LawSign> lawSigns = lawSignService.selectList(record);
        ExcelUtil<LawSign> excelUtil=new ExcelUtil<>(LawSign.class);
        excelUtil.exportExcel(response,lawSigns,"签章列表");
    }
    /**
     * 获取签章类型选择
     * @return
     */
    @GetMapping("/getSignTypeOptions")
    public AjaxResult getSignTypeOptions(){
        return AjaxResult.success(SignTypeEnum.getOptions());
    }

    /**
     * 获取状态选项
     * @return
     */
    @GetMapping("/getStatusOptions")
    public AjaxResult getStatusOptions(){
        List<Option> options=new ArrayList<>();
        options.add(new Option(StatusEnum.NORMAL.getCode(), StatusEnum.NORMAL.getInfo()));
        options.add(new Option(StatusEnum.DISABLED.getCode(),StatusEnum.DISABLED.getInfo()));
        return AjaxResult.success(options);
    }
}
