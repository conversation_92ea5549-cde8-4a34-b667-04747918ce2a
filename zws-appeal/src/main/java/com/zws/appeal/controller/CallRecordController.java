package com.zws.appeal.controller;

import cn.hutool.core.util.StrUtil;
import com.zws.common.core.callcenter.enums.AnswerEnum;
import com.zws.common.core.constant.FileConstant;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.appeal.agservice.DownloadTaskAgService;
import com.zws.appeal.domain.call.CallRecord;
import com.zws.appeal.pojo.call.CallRecordParam;
import com.zws.appeal.service.call.ICallRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通话记录
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/25 14:58
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/call/record")
public class CallRecordController extends BaseController {


    @Autowired
    private ICallRecordService callRecordService;
    @Autowired
    private DownloadTaskAgService downloadTaskAgService;

    /**
     * 列表
     *
     * @return
     */
    @RequiresLogin
    @GetMapping("/list")
    public TableDataInfo selectList(CallRecordParam param) {
        startPage();
        List<CallRecord> list = callRecordService.selectList(param);
        return getDataTable(list);
    }

    /**
     * 获取接通状态-选项
     *
     * @return
     */
    @RequiresLogin
    @GetMapping("/getAnswerStatus")
    public AjaxResult selectListWithAnswerStatus() {
        AnswerEnum[] enums = AnswerEnum.values();
        List<Map<String, Object>> pojoList = new ArrayList<>();
        for (AnswerEnum temp : enums) {
            Map<String, Object> map = new HashMap<>();
            map.put("code", temp.getCode());
            map.put("info", temp.getInfo());
            pojoList.add(map);
        }
        return AjaxResult.success(pojoList);
    }

    /**
     * 获取呼叫标识(呼叫类型) 选项
     *
     * @return
     */
    @RequiresLogin
    @GetMapping("/getCallroters")
    public AjaxResult selectListWithCallroters() {
        List<Map<String, Object>> pojoList = new ArrayList<>();
        //0呼入、1呼出、 3磋商、4内呼

        Map<String, Object> map = new HashMap<>();
        map.put("code", "0");
        map.put("info", "呼入");
        pojoList.add(map);

        Map<String, Object> map1 = new HashMap<>();
        map1.put("code", "1");
        map1.put("info", "呼出");
        pojoList.add(map1);
        return AjaxResult.success(pojoList);
    }

    /**
     * 创建下载录音任务
     *
     * @return
     */
//    @Log(title = "通话记录（创建下载录音任务）", businessType = BusinessType.INSERT)
    @RequiresLogin
    @PostMapping("/createDownloadTask")
    public AjaxResult addDownloadTask(@RequestBody CallRecordParam param) {
        String taskName = downloadTaskAgService.createTask(param);
        String msg = StrUtil.format("已自动为你创建任务，任务名称为：{}，请于次日（24小时后）在录音下载任务页面进行下载！", taskName);
        return AjaxResult.success(msg);
    }

    /**
     * 导出
     *
     * @param response
     * @param param
     * @return
     */
    //导出记录加了操作日志会报错
    //@Log(title = "通话记录（导出）", businessType = BusinessType.EXPORT)
    @RequiresLogin
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody CallRecordParam param) throws UnsupportedEncodingException {

        List<CallRecord> list = callRecordService.selectList(param.getParam());
        ExcelUtil util = new ExcelUtil(CallRecord.class);
        String fileName = "通话记录" + FileConstant.getExcelSuffix();
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, list, "通话记录");
    }


}
