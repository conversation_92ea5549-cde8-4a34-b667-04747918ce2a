package com.zws.appeal.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.IdUtil;
import com.zws.appeal.domain.MessageTemplatePojo;
import com.zws.appeal.pojo.SendRecordsPojos;
import com.zws.appeal.utils.*;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.ScheduleVo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.threadpool.PoolManageMent;
import com.zws.common.core.threadpool.TaskManager;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.agservice.AgCaseService;
import com.zws.appeal.agservice.ExportAgService;
import com.zws.appeal.agservice.DownloadTaskAgService;
import com.zws.appeal.agservice.TeamSysAgService;
import com.zws.appeal.domain.CaseManage;
import com.zws.appeal.domain.ExcelBatch;
import com.zws.appeal.domain.TemplateAllocationExcelVo;
import com.zws.appeal.domain.division.DivisionalConditions;
import com.zws.appeal.domain.record.ReductionFile;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.enums.CsExportClassEnum;
import com.zws.appeal.enums.ExportUrgeDiaryFields;
import com.zws.appeal.pojo.ExportReminder;
import com.zws.appeal.pojo.SpecifyRules;
import com.zws.appeal.pojo.UrlData;
import com.zws.appeal.schedule.IScheduleService;
import com.zws.appeal.service.CaseService;
import com.zws.appeal.service.MyApprovalService;
import com.zws.appeal.task.TemplateAllocationTask;
import com.zws.system.api.RemoteCaseService;
import com.zws.system.api.domain.Legal;
import com.zws.system.api.domain.StatePojo;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 案件管理
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/case")
public class CaseController extends BaseController {

    @Autowired
    private CaseService caseService;
    @Autowired
    private AgCaseService agCaseService;
    @Autowired
    private FileUpload fileUpload;
    @Autowired
    private MyApprovalService myApprovalService;
    @Autowired
    private IScheduleService scheduleService;
    @Autowired
    private ExportAgService exportAgService;
    @Autowired
    private TeamSysAgService teamSysAgService;
    @Autowired
    private RemoteCaseService remoteCaseService;

    /**
     * 根据字段查询该团队的案件/该团队案件全查（分页）
     *
     * @param exportReminder
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCase", method = RequestMethod.GET)
    public TableDataInfo selectList(ExportReminder exportReminder) {
        startPage();
        exportReminder.setLoginUser(SecurityUtils.getLoginUser());
        List<CaseManage> caseManages = agCaseService.dataDesensitization(exportReminder);
        return getDataTable(caseManages);
    }

    /**
     * 指定分案写入催收员（批量）
     *
     * @param specifyRules
     * @return
     */
    @Log(title = "案件管理-指定分案给催员", businessType = BusinessType.OTHER)
    @RequiresLogin
    //@RequiresPermissions(value = {"dispose:case:updateCase", "dispose:case:updateDistribution"}, logical = Logical.OR)
    @RequestMapping(value = "/updateCase", method = RequestMethod.PUT)
    public AjaxResult edit(@RequestBody SpecifyRules specifyRules) {
        agCaseService.updateCase(specifyRules);
        return AjaxResult.success("操作成功");
    }

    /**
     * 根据字段查询该团队的案件/该团队案件全查（分页）
     *
     * @param exportReminder
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCasess", method = RequestMethod.GET)
    public TableDataInfo selectListWithCaseManages(ExportReminder exportReminder) {
        startPage();
        List<CaseManage> caseManages = caseService.selectCaseManages(exportReminder);
        return getDataTable(caseManages);
    }

    /**
     * 根据字段查询该团队的案件总金额以及总案件量
     *
     * @param exportReminder
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCaseManagesMoney", method = RequestMethod.POST)
    public AjaxResult selectMapWithCaseManagesMoney(@RequestBody ExportReminder exportReminder) {
        Map<String, Object> map = caseService.selectCaseManagesMoney(exportReminder);
        return AjaxResult.success("查询成功", map);
    }

    /**
     * 指定分案查询数据以及可分配案件/案件总金额
     *
     * @param exportReminder
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCases", method = RequestMethod.POST)
    public AjaxResult selectMapWithCase(@RequestBody ExportReminder exportReminder) {
        exportReminder.analysiStringDeptId();
        Map<String, Object> map = agCaseService.selectId(exportReminder);
        return AjaxResult.success("查询成功", map);
    }

    /**
     * 指定分案预览数据
     *
     * @param specifyRules
     * @return
     */
    @Log(title = "指定分案（预览分案结果）", businessType = BusinessType.OTHER)
    @RequiresLogin
    @RequestMapping(value = "/specifyDivisionalData", method = RequestMethod.POST)
    public AjaxResult specifyDivisionalData(@RequestBody SpecifyRules specifyRules) {
        ScheduleVo scheduleVo = new ScheduleVo();
        scheduleVo.setScheduleNo(IdUtil.fastSimpleUUID());
        scheduleVo.setNormal(true);
        scheduleVo.setRemarks("处理中，请稍等");
        agCaseService.specifyDivisionalDataPreview(specifyRules, scheduleVo);
        return AjaxResult.success("查询成功", scheduleVo);
    }


    /**
     * 规则分案（预览分案结果）
     *
     * @param divisionalConditions
     * @return
     */
    @Log(title = "规则分案（预览分案结果）", businessType = BusinessType.OTHER)
    @RequiresLogin
    @RequestMapping(value = "/ruleSplitPreview", method = RequestMethod.POST)
    public AjaxResult ruleSplitPreview(@RequestBody DivisionalConditions divisionalConditions) {
        log.info("规则分案（预览分案结果）-开始");
        TimeInterval timer = DateUtil.timer();
        timer.start("1");

        ScheduleVo scheduleVo = new ScheduleVo();
        scheduleVo.setScheduleNo(IdUtil.fastSimpleUUID());
        scheduleVo.setSchedule(0);
        scheduleVo.setNormal(true);
        scheduleVo.setRemarks("处理中，请稍等...");
        scheduleService.setSchedule(scheduleVo);
        agCaseService.ruleSplitPreview(divisionalConditions, scheduleVo);
        //前端不需要这些，清空掉避免影响传输效率
        /*for (DivisionPersonnel temp:divisionPersonnels ) {
            temp.setList(new ArrayList<>());
        }*/
        long t = timer.intervalMs("1");
        log.info("规则分案（预览分案结果）耗时:" + t + " ms");
        log.info("规则分案（预览分案结果）-结束");
        return AjaxResult.success(scheduleVo);
    }


    /**
     * 规则分案(写入分案记录，修改案件催收员)
     *
     * @param divisionalConditions
     * @return
     */
    @Log(title = "案件管理-规则分案", businessType = BusinessType.OTHER)
    @RequiresLogin
    //@RequiresPermissions("dispose:case:writeRuleDivision")
    @RequestMapping(value = "/writeRuleDivision", method = RequestMethod.POST)
    public AjaxResult writeRuleDivision(@RequestBody DivisionalConditions divisionalConditions) {
        agCaseService.writeRuleDivision(divisionalConditions);
        return AjaxResult.success("分案成功");
    }

    /**
     * 回收案件
     *
     * @param exportReminder
     * @return
     */
    @Log(title = "案件管理-回收案件", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions("dispose:case:updateRecovery")
    @RequestMapping(value = "/updateRecovery", method = RequestMethod.PUT)
    public AjaxResult updateRecovery(@RequestBody ExportReminder exportReminder) {
        exportReminder.setMethodNo(0);
        exportReminder.setLoginUser(SecurityUtils.getLoginUser());
        agCaseService.updateRecovery(exportReminder);
        return AjaxResult.success("操作成功");
    }

    /**
     * 下载分案导入模板
     *
     * @param response
     * @throws IOException
     */
    @Log(title = "案件管理-下载分案导入模板", businessType = BusinessType.EXPORT)
    @RequiresLogin
    @PostMapping("/export")
    public void export(HttpServletResponse response) throws IOException {
//        设置下载模板名字
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("分案导入模板.xlsx", "utf-8"));
        ExcelUtil<TemplateAllocationExcelVo> util = new ExcelUtil<TemplateAllocationExcelVo>(TemplateAllocationExcelVo.class);
        util.exportExcel(response, new ArrayList<>(), "分案导入模板");
    }

    /**
     * 下载案件批量操作模板
     *
     * @param response
     * @throws IOException
     */
    @Log(title = "案件管理-下载案件批量操作模板", businessType = BusinessType.EXPORT)
    @RequiresLogin
    @PostMapping("/exports")
    public void exports(HttpServletResponse response) throws IOException {
//        设置下载模板名字
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("案件批量操作模板.xlsx", "utf-8"));
        ExcelUtil<ExcelBatch> util = new ExcelUtil<ExcelBatch>(ExcelBatch.class);
        util.exportExcel(response, new ArrayList<>(), "案件批量操作模板");
    }

    /**
     * excel表格导入分案信息
     * 模板分案
     *
     * @param urlData
     * @return
     * @throws Exception
     */
    @Log(title = "案件管理-excel表格导入分案信息", businessType = BusinessType.IMPORT)
    @RequiresLogin
    //@RequiresPermissions(value = {"dispose:case:importData", "dispose:case:importDataTeam"}, logical = Logical.OR)
    @PostMapping("/importData")
    public AjaxResult importData(@RequestBody UrlData urlData, HttpServletResponse response) throws Exception {

        LoginUser loginUser = SecurityUtils.getLoginUser();

        PoolManageMent pool = PoolManageMent.getInstance();
        pool.init();
        TemplateAllocationTask workTask = new TemplateAllocationTask(urlData, loginUser);
        TaskManager.addTask(workTask);

        /*
        //        根据文件路径下载文件
        File tempFile = FileDownloadUtils.downloadTempFile(urlData.getFileUrl());
        ExcelUtil<CaseManage> util = new ExcelUtil<CaseManage>(CaseManage.class);
        List<CaseManage> userList = util.importExcel(new FileInputStream(tempFile));
        if (ObjectUtils.isEmpty(userList)) {
            throw new GlobalException("文件内容不能为空");
        }
        MultipartFile multipartFiles = FileDownloadUtils.fileConvertMultipartFile(tempFile);
        ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(multipartFiles.getInputStream());
        List<Map<String, Object>> readAll = reader.read(0, 0, 1);
        Map<String, Object> map = readAll.get(0);
        List<String> list = agCaseService.excelHeader();
        int size = map.size();
        if (size != list.size()) {
            throw new GlobalException("请下载正确模板");
        }
        for (String key : map.keySet()) {
            System.out.println(key);
            if (!list.contains(key)) throw new GlobalException("请下载正确模板");
        }

        List<Map<String, Object>> mapList = agCaseService.insertCaseManage(userList,loginUser);

        if (!ObjectUtils.isEmpty(mapList)) {
            MultipartFile multipartFile = FileDownloadUtil.generateMultipartFile(mapList);
            MultipartFile[] files = new MultipartFile[]{multipartFile};
            String fileName = "excelError";
//            将Excel文件上传到minio
            String[] lists = new String[]{".xlsx"};
            Map<String, Object> map1 = fileUpload.uploadFile(files, fileName, lists);
////            设置下载模板名字
//            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("分案导入错误反馈.xlsx", "utf-8"));
//            ExcelUtil<ErrorMessage> utils = new ExcelUtil<ErrorMessage>(ErrorMessage.class);
//            utils.exportExcel(response, arrayList, "分案导入错误反馈");

            return AjaxResult.success("导入成功", map1);
        } else {
            return AjaxResult.success("导入成功");
        }*/
        return AjaxResult.success("模版分案文件已导入成功，后台执行分案中，如需查看结果请前往导入日志页面查看！");
    }

    /**
     * 上传excel文件到minio11111
     *
     * @param file
     * @return
     * @throws Exception
     */
    @Log(title = "案件管理-上传excel文件", businessType = BusinessType.IMPORT)
    @RequiresLogin
    @PostMapping("/upload")
    public AjaxResult upload(MultipartFile file) throws Exception {
        try {
            String fileName = file.getOriginalFilename();
            if (fileName != null && ObjectUtils.nullSafeEquals("xlsx", fileName.substring(fileName.lastIndexOf(".") + 1))) {
                ExcelSecurityValidatorUtils excelSecurityValidatorUtils = new ExcelSecurityValidatorUtils();
                excelSecurityValidatorUtils.validateExcel(file);
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

        MultipartFile[] files = new MultipartFile[]{file};
        String fileName = "excel";
//        将Excel文件上传到minio
        String[] list = new String[]{".xlsx", ".xls"};
        Map<String, Object> map = fileUpload.uploadFile(files, fileName, list);
        return AjaxResult.success("上传成功", map);
    }


    /**
     * excel表格导入案件批量操作信息(批量申请停催)
     *
     * @param urlData
     * @return
     * @throws Exception
     */
    @Log(title = "案件管理-excel表格导入案件批量操作信息", businessType = BusinessType.IMPORT)
    @RequiresLogin
    @PostMapping("/importDataStop")
    public AjaxResult importDataStop(@RequestBody UrlData urlData) throws Exception {
//        根据文件路径下载文件
        File tempFile = SaasFileDownloadUtils.downloadTempFile(urlData.getFileUrl());

        ExcelUtil<ExcelBatch> util = new ExcelUtil<ExcelBatch>(ExcelBatch.class);
        List<ExcelBatch> userList = util.importExcel(new FileInputStream(tempFile));
        Map<String, Object> map = agCaseService.insertExcelBatch(userList);
        return AjaxResult.success("导入成功", map);
    }

    /**
     * 写入案件申请记录表(批量申请停催)
     *
     * @param urlData
     * @return
     */
    @Log(title = "案件管理-批量申请停催", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertStopUrging", method = RequestMethod.POST)
    public AjaxResult addStopUrging(@RequestBody UrlData urlData) {
        int i = caseService.insertApplyRecord(urlData.getKey());
        return AjaxResult.success("操作成功", i);
    }

    /**
     * excel表格导入案件批量操作信息(批量申请退案)
     *
     * @param urlData
     * @return
     * @throws Exception
     */
    @Log(title = "案件管理-excel表格导入案件批量操作信息", businessType = BusinessType.IMPORT)
    @RequiresLogin
    @PostMapping("/importDataBack")
    public AjaxResult importDataBack(@RequestBody UrlData urlData) throws Exception {
//        根据文件路径下载文件
        File tempFile = SaasFileDownloadUtils.downloadTempFile(urlData.getFileUrl());
        //TODO  缺少上传文件信息存入数据表
        ExcelUtil<ExcelBatch> util = new ExcelUtil<ExcelBatch>(ExcelBatch.class);
        List<ExcelBatch> userList = util.importExcel(new FileInputStream(tempFile));
        Map<String, Object> map = agCaseService.insertWithdrawal(userList);
        return AjaxResult.success("导入成功", map);
    }

    /**
     * 写入案件申请记录表(批量申请退案)
     *
     * @param urlData
     * @return
     */
    @Log(title = "案件管理-批量申请退案", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertDataBack", method = RequestMethod.POST)
    public AjaxResult addDataBack(@RequestBody UrlData urlData) {
        int i = caseService.insertApplyRecordDataBack(urlData.getKey());
        return AjaxResult.success("操作成功", i);
    }

    /**
     * excel表格导入案件批量操作信息(批量申请留案)
     *
     * @param urlData
     * @return
     * @throws Exception
     */
    @Log(title = "案件管理-excel表格导入案件批量操作信息", businessType = BusinessType.IMPORT)
    @RequiresLogin
    @PostMapping("/importKeepCase")
    public AjaxResult importKeepCase(@RequestBody UrlData urlData) throws Exception {
//        根据文件路径下载文件
        File tempFile = SaasFileDownloadUtils.downloadTempFile(urlData.getFileUrl());
        //TODO  缺少上传文件信息存入数据表
        ExcelUtil<ExcelBatch> util = new ExcelUtil<ExcelBatch>(ExcelBatch.class);
        List<ExcelBatch> userList = util.importExcel(new FileInputStream(tempFile));
        Map<String, Object> map = agCaseService.insertKeepCase(userList);
        return AjaxResult.success("导入成功", map);
    }

    /**
     * 写入案件申请记录表(批量申请留案)
     *
     * @param urlData
     * @return
     */
    @Log(title = "案件管理-批量申请留案", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequestMapping(value = "/insertKeepCase", method = RequestMethod.POST)
    public AjaxResult addKeepCase(@RequestBody UrlData urlData) {
        int i = caseService.insertApplyRecordKeepCase(urlData.getKey());
        return AjaxResult.success("添加成功", i);
    }

    /**
     * 退案（批量勾选）
     *
     * @param exportReminder
     * @return
     */
    @Log(title = "案件管理-申请退案", businessType = BusinessType.INSERT)
    @RequiresLogin
    //@RequiresPermissions("dispose:case:insertRetreat")
    @RequestMapping(value = "/insertRetreat", method = RequestMethod.POST)
    public AjaxResult insertRecord(@RequestBody ExportReminder exportReminder) {
        agCaseService.caseWithdrawal(exportReminder);
        return AjaxResult.success("退案申请提交成功，如案件有审核中的申请将过滤！");
    }

    /**
     * 留案（批量勾选）
     *
     * @param exportReminder
     * @return
     */
    @Log(title = "案件管理-申请留案", businessType = BusinessType.INSERT)
    @RequiresLogin
    //@RequiresPermissions("dispose:case:insertKeep")
    @RequestMapping(value = "/insertKeep", method = RequestMethod.POST)
    public AjaxResult caseKeepCase(@RequestBody ExportReminder exportReminder) {
        agCaseService.caseKeepCase(exportReminder);
        return AjaxResult.success("留案申请提交成功，如案件有审核中的申请将过滤！");
    }

    /**
     * 停催（批量勾选）
     *
     * @param exportReminder
     * @return
     */
    @Log(title = "案件管理-申请停催", businessType = BusinessType.INSERT)
    @RequiresLogin
    //@RequiresPermissions("dispose:case:insertStop")
    @RequestMapping(value = "/insertStop", method = RequestMethod.POST)
    public AjaxResult insertStopUrging(@RequestBody ExportReminder exportReminder) {
        agCaseService.caseStopUrging(exportReminder);
        return AjaxResult.success("停催申请提交成功，如案件有审核中的申请将过滤！");
    }

    /**
     * 标记案件
     *
     * @param exportReminder
     * @return
     */
    @Log(title = "案件管理-标记案件", businessType = BusinessType.UPDATE)
    @RequiresLogin
    //@RequiresPermissions("dispose:case:selectMarkCase")
    @RequestMapping(value = "/selectMarkCase", method = RequestMethod.POST)
    public AjaxResult selectMarkCase(@RequestBody ExportReminder exportReminder) {
        agCaseService.selectMarkCase(exportReminder);
        return AjaxResult.success("标记成功");
    }

    /**
     * 标记案件（标签合并资产端后）
     */
    @Log(title = "案件管理-标记案件", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/editMarkCase", method = RequestMethod.POST)
    public AjaxResult editMarkCase(@RequestBody ExportReminder exportReminder) {
        agCaseService.editMarkCase(exportReminder);
        return AjaxResult.success("标记成功");
    }

    /**
     * 根据团队id以及案件id查询统计金额以及可分配案件数量
     *
     * @param exportReminder
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectCaseStatistics", method = RequestMethod.GET)
    public AjaxResult selectMapWithCaseStatistics(ExportReminder exportReminder) {
        Map<String, Object> map = agCaseService.selectStatistics(exportReminder);
        return AjaxResult.success("查询成功", map);
    }

    /**
     * 根据案件id/查询条件查询催收记录
     *
     * @param exportReminder
     * @return
     */
    @Log(title = "案件管理-查询导出催收记录", businessType = BusinessType.EXPORT)
    @RequiresLogin
    //@RequiresPermissions("dispose:case:selectUrgeRecord")
    @RequestMapping(value = "/selectUrgeRecord", method = RequestMethod.POST)
    public AjaxResult selectUrgeRecord(HttpServletResponse response, @RequestBody @Validated ExportReminder exportReminder) throws Exception {
        if (ObjectUtils.isEmpty(exportReminder.getCreateTime1()) || ObjectUtils.isEmpty(exportReminder.getCreateTime2())) {
            throw new GlobalException("请选择催记日期");
        }

        exportReminder.setLoginUser(SecurityUtils.getLoginUser());
        exportReminder.setCreateId(TokenInformation.getCreateid());
        Object[] params = new Object[]{exportReminder};
        String fileName = exportAgService.exportTask(CsExportClassEnum.URGE_EXPORT,
                DownloadTaskAgService.class, "downloadUrgeRecord",
                params, "催记导出");
        return AjaxResult.success("操作成功", fileName);
    }

    /**
     * 返回催记勾选字段
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectTickField", method = RequestMethod.GET)
    public AjaxResult selectListWithTickField() {
        List<String> list = new ArrayList<>();
        ExportUrgeDiaryFields[] values = ExportUrgeDiaryFields.values();
        for (ExportUrgeDiaryFields exportUrgeDiaryFields : values) {
            String info = exportUrgeDiaryFields.getInfo();
            list.add(info);
        }
        return AjaxResult.success("查询成功", list);
    }


    /**
     * 根据减免申请表id查询减免申请凭证信息
     *
     * @param id
     * @return
     */
    //@RequiresPermissions("case:aduit:lookOver")
    @RequiresLogin
    @RequestMapping(value = "/selectStagingRecord/{id}", method = RequestMethod.GET)
    public AjaxResult selectStagingRecord(@PathVariable("id") int id) {
        List<ReductionFile> reductionFiles = myApprovalService.selectReductionFile(id);
        return AjaxResult.success("查询成功", reductionFiles);
    }



    /**
     * 导出案件
     *
     * @param exportReminder
     * @param response
     * @throws UnsupportedEncodingException
     */
    @Log(title = "案件管理-导出案件", businessType = BusinessType.EXPORT)
    @RequestMapping(value = "/exportCase", method = RequestMethod.POST)
    public AjaxResult exportCase(@RequestBody ExportReminder exportReminder, HttpServletResponse response) {

        exportReminder.setCreateId(TokenInformation.getCreateid());
        exportReminder.setLoginUser(SecurityUtils.getLoginUser());
        Object[] params = new Object[]{exportReminder};
        String fileName = exportAgService.exportTask(CsExportClassEnum.CASE_EXPORT,
                DownloadTaskAgService.class, "downloadCase",
                params, "案件导出");
        return AjaxResult.success("操作成功", fileName);

    }

    /**
     * 查询短信模板下拉框
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectAllSmsTemplate", method = RequestMethod.GET)
    public AjaxResult selectAllSmsTemplate() {
        return AjaxResult.success("查询成功", caseService.selectAllSmsTemplate());
    }

//    /**
//     * 调诉端批量发送生成短信模板短信
//     *
//     * @return
//     */
//    @RequiresLogin
//    @RequestMapping(value = "/previewTemplate", method = RequestMethod.POST)
//    public AjaxResult previewTemplate(@RequestBody MessageTemplatePojo smsCustomPojo) {
//        if (smsCustomPojo.getListOrDetails() == null) throw new GlobalException("必要参数不能为空");
//        SendRecordsPojos sendRecordsPojos = agCaseService.previewTemplate(smsCustomPojo);
//        return AjaxResult.success("查询成功", sendRecordsPojos);
//    }
    /**
     * 远程调用资产端标签列表
     */
    @RequestMapping(value = "/setup/legal/getLabel", method = RequestMethod.GET)
    public AjaxResult getLabel() {
        R<List<Legal>> label = remoteCaseService.getLabel();
        return AjaxResult.success("操作成功", label.getData());
    }

    /**
     * 远程调用资产端标签下拉框
     */
    @RequestMapping(value = "/start/getLabels", method = RequestMethod.GET)
    public AjaxResult getLabels() {
        R<List<StatePojo>> label = remoteCaseService.getLabels();
        return AjaxResult.success("操作成功", label.getData());
    }


//------------------------------------------------------勾选案件创建预测试外呼任务-------------------------------------------

    /**
     * 根据勾选案件查找对应有预测试外呼坐席的催员id集合-（案件管理）
     *
     * @param exportReminder 搜索条件
     */
    @RequestMapping(value = "/verifyCaseList", method = RequestMethod.POST)
    public AjaxResult verifyCaseList(@RequestBody ExportReminder exportReminder) {
        if (ObjectUtils.isEmpty(exportReminder)) return AjaxResult.error("必要参数不能为空");
        exportReminder.setCreateId(TokenInformation.getCreateid());
        exportReminder.setLoginUser(SecurityUtils.getLoginUser());
        List<Integer> list = agCaseService.verifyCaseList(exportReminder);
        return AjaxResult.success("操作成功", list);
    }

    /**
     * 创建预测试外呼任务-（案件管理）
     *
     * @param exportReminder 搜索条件
     */
    @Log(title = "案件管理-创建预测试外呼任务", businessType = BusinessType.INSERT)
    @RequestMapping(value = "/caseSubmitTaskData", method = RequestMethod.POST)
    public AjaxResult caseSubmitTaskData(@RequestBody ExportReminder exportReminder) {
        if (ObjectUtils.isEmpty(exportReminder)) return AjaxResult.error("必要参数不能为空");
        if (ObjectUtils.isEmpty(exportReminder.getIntelligenceTask())) return AjaxResult.error("任务配置信息不能为空");
        exportReminder.setCreateId(TokenInformation.getCreateid());
        exportReminder.setLoginUser(SecurityUtils.getLoginUser());
        Integer integer = agCaseService.caseSubmitTaskData(exportReminder);
        return AjaxResult.success("操作成功", integer);
    }

    /**
     * 预测试外呼创建任务统计案件数量-（案件管理）
     *
     * @param exportReminder 搜索条件
     */
    @RequestMapping(value = "/selectCaseNumber", method = RequestMethod.POST)
    public AjaxResult selectCaseNumber(@RequestBody ExportReminder exportReminder) {
        if (ObjectUtils.isEmpty(exportReminder)) return AjaxResult.error("必要参数不能为空");
        exportReminder.setCreateId(TokenInformation.getCreateid());
        exportReminder.setLoginUser(SecurityUtils.getLoginUser());
//        根据搜索条件查询案件id
        List<Long> longList = caseService.selectCaseIdList(exportReminder);
        return AjaxResult.success("操作成功", longList.size());
    }




//------------------------------------------------------勾选案件创建AI语言通知任务-------------------------------------------


    /**
     * 创建AI语言通知任务-（案件管理）
     *
     * @param exportReminder 搜索条件
     */
    @Log(title = "案件管理-创建AI语言通知任务", businessType = BusinessType.INSERT)
    @RequestMapping(value = "/AiVoiceCaseSubmitTaskData", method = RequestMethod.POST)
    public AjaxResult AiVoiceCaseSubmitTaskData(@RequestBody ExportReminder exportReminder) {
        if (ObjectUtils.isEmpty(exportReminder)) return AjaxResult.error("必要参数不能为空");
        if (ObjectUtils.isEmpty(exportReminder.getAiVoiceTask())) return AjaxResult.error("任务配置信息不能为空");
        exportReminder.setCreateId(TokenInformation.getCreateid());
        exportReminder.setLoginUser(SecurityUtils.getLoginUser());
        Integer integer = agCaseService.AiVoiceCaseSubmitTaskData(exportReminder);
        return AjaxResult.success("操作成功", integer);
    }

    /**
     * 创建AI语言通知任务 并执行-（案件管理）
     *
     * @param exportReminder 搜索条件
     */
    @Log(title = "案件管理-创建AI语言通知任务", businessType = BusinessType.INSERT)
    @RequestMapping(value = "/AiVoiceCaseSubmitTaskDataAndExecute", method = RequestMethod.POST)
    public AjaxResult AiVoiceCaseSubmitTaskDataAndExecute(@RequestBody ExportReminder exportReminder) {
        if (ObjectUtils.isEmpty(exportReminder)) return AjaxResult.error("必要参数不能为空");
        if (ObjectUtils.isEmpty(exportReminder.getAiVoiceTask())) return AjaxResult.error("任务配置信息不能为空");
        exportReminder.setCreateId(TokenInformation.getCreateid());
        exportReminder.setLoginUser(SecurityUtils.getLoginUser());
        Integer integer = agCaseService.AiVoiceCaseSubmitTaskDataAndExecute(exportReminder);
        return AjaxResult.success("操作成功", integer);
    }




}
