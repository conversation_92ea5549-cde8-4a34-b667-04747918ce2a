package com.zws.appeal.controller.letterDoc.letter.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.standardmain.domain.vo.ZwsApproveRecordVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/5/6 下午7:52
 */
@Data
public class TemplateApprove extends ZwsApproveRecordVo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 邮件模板主题
     */
    private String templateName;

    /**
     * 模板类型
     */
    private String templateType;

    /**
     * 处理状态
     */
    private Integer handleStatus;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /**
     * 审核状态(0、审核中 1、已通过 2、未通过)
     */
    private Integer approveStatus;

    /**
     * 审批模板编码
     */
    private String approveTemplateCode;

    /**
     * 添加人id
     */
    private Long createdById;

    /**
     * 添加人
     */
    private String createdBy;

    /**
     * 添加时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private List<String> templateTypes;

    private String previewUrl;

    private Integer previewPages;
}
