package com.zws.appeal.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.appeal.domain.TeamBatchDimension;
import com.zws.appeal.domain.TeamDataOverview;
import com.zws.appeal.domain.TeamEmployeeDimension;
import com.zws.appeal.pojo.Option;
import com.zws.appeal.service.CensusAssetsService;
import com.zws.appeal.utils.PageUtilsSaas;
import com.zws.appeal.utils.TimeUtils;
import com.zws.appeal.utils.TokenInformation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据概览接口
 *
 * <AUTHOR>
 * @DATE: Created in 2023/5/25
 */
@CrossOrigin
@RestController
@RequestMapping("/data/outline")
public class CensusAssetsController extends BaseController {


    @Autowired
    private CensusAssetsService censusAssetsService;


    /**
     * 统计委案批次维度
     */
    @RequiresLogin
    @GetMapping("/getStatisticsBatch")
    public TableDataInfo selectListWithStatisticsBatch(@RequestParam("teamId") Long teamId) {
        List<TeamBatchDimension> list = censusAssetsService.getStatisticsBatch(teamId);
        return getDataTable(list);
    }


    /**
     * 查询批次维度列表
     *
     * @param batchNum
     * @return
     */
    @RequiresLogin
    @GetMapping("/getCensusBatchList")
    public TableDataInfo selectListWithCensusBatchList(@RequestParam(required = false) List<String> batchNum) {
        Integer createId = TokenInformation.getCreateid();
        startPage();
        List<TeamBatchDimension> list = censusAssetsService.getCensusBatchList(Long.valueOf(createId), batchNum);
        return getDataTable(list);
    }

    /**
     * 获取团队所有分案批次_下拉框
     */
    @RequiresLogin
    @GetMapping("/getBatchNum")
    public AjaxResult selectListWithBatchNum() {
        Integer createId = TokenInformation.getCreateid();
        List<Option> list = censusAssetsService.getBatchNum(Long.valueOf(createId));
        return AjaxResult.success(list);
    }

//-------------------------员工维度-----------------------------------------

    /**
     * 查询接口_员工维度数据概览
     * 主账号可以查询全部员工账号
     * 员工账号只查询自己的账号
     */
    @RequiresLogin
    @GetMapping("/getCensusEmployeeList")
    public TableDataInfo selectListWithCensusEmployee(TeamEmployeeDimension dimension) {

        dimension.setCreateTime1(TimeUtils.getBeginOfDay(dimension.getCreateTime1()));
        dimension.setCreateTime2(TimeUtils.getEndOfDay(dimension.getCreateTime2()));
        int type = TokenInformation.getType();
        if (type == 0) {
            List<TeamEmployeeDimension> list = censusAssetsService.getCensusEmployeeList(dimension);
            if (ObjectUtils.isEmpty(list)) {
                return getDataTable(new ArrayList<>());
            }
            Set<Long> set = list.stream().map(TeamEmployeeDimension::getEmployeeId).collect(Collectors.toSet());
            List<Long> employeeIds = ListUtil.toList(set);
            if (list != null && list.size() > 0) {
                List<Long> ids = ListUtil.page(dimension.getPageNum() - 1, dimension.getPageSize(), employeeIds);
                list = censusAssetsService.statisticsUtil(list, ids);
            }
//            查询所有员工案件的所属资产包
            for (TeamEmployeeDimension row : list) {
                Map<String, Object> map = new HashMap<>();
                map.put("employeeId", row.getEmployeeId());
                map.put("teamId", row.getTeamId());
                if (ObjectUtil.isNotNull(row.getCreateTime())) {
                    map.put("startData", TimeUtils.thisMonthBegins(row.getCreateTime()));
                    map.put("endData", TimeUtils.thisMonthEnds(row.getCreateTime()));
                }
                List<String> strings = censusAssetsService.selectAssetsPackName(map);
                String join = "";
                if (!ObjectUtils.isEmpty(strings)) {
                    join = StringUtils.join(strings, ",");
                }
                row.setAssetPackName(join);
            }
            return PageUtilsSaas.getDataTable(list, set.size());
        }

        List<TeamEmployeeDimension> list = censusAssetsService.getCensusEmployeeList(dimension);
        if (ObjectUtils.isEmpty(list)) {
            return getDataTable(new ArrayList<>());
        }
        list = censusAssetsService.statisticsUtil(list, null);
        for (TeamEmployeeDimension item : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("employeeId", item.getEmployeeId());
            map.put("teamId", item.getTeamId());
            map.put("startData", TimeUtils.thisMonthBegins(item.getCreateTime()));
            map.put("endData", TimeUtils.thisMonthEnds(item.getCreateTime()));
            List<String> strings = censusAssetsService.selectAssetsPackName(map);
            String join = "";
            if (!ObjectUtils.isEmpty(strings)) {
                join = StringUtils.join(strings, ",");
            }
            item.setAssetPackName(join);
        }
        return PageUtilsSaas.getDataTable(list, 1);
    }




    /**
     * 数据概览_员工维度
     * 统计接口，每个员工 每月存一次（每天更新该数据）
     */
    @RequiresLogin
    @GetMapping("/getStatisticsEmployee")
    public TableDataInfo selectListWithStatisticsEmployee(@RequestParam("teamId") Long teamId) {
        //统计该团队下的所有成员当月的 数据维度，进行存储/更新
//        Integer createId = TokenInformation.getCreateid();
        List<TeamEmployeeDimension> list = censusAssetsService.getStatisticsEmployee(teamId);
        return getDataTable(list);
    }


    /**
     * 获取成员名称下拉框
     *
     * @return
     */
    @RequiresLogin
    @GetMapping("/getEmployee")
    public AjaxResult selectListWithEmployee() {
        Integer createId = TokenInformation.getCreateid();
        List<Option> list = censusAssetsService.getEmployeeOption(Long.valueOf(createId));
        return AjaxResult.success(list);
    }


    /**
     * 统计数据概览
     *
     * @return
     */
    @RequiresLogin
    @GetMapping("/getStatistics")
    public TableDataInfo selectListWithStatistics(@RequestParam("teamId") Long teamId) {
        List<TeamDataOverview> list = censusAssetsService.getStatistics(teamId);
        return getDataTable(list);
    }

    /**
     * 查询数据概览接口
     * 区分主账号 查询全部成员数据、员工账号自己成员数据
     */
    @RequiresLogin
    @GetMapping("/selectStatistics")
    public AjaxResult infoWithStatistics() {
        Integer createId = TokenInformation.getCreateid();
        startPage();
        TeamDataOverview overview = censusAssetsService.selectStatistics(Long.valueOf(createId));
        return AjaxResult.success(overview);
    }

    /**
     * 员工维度_数据概览_导出Excel
     *
     * @param response
     * @param dimension
     * @throws UnsupportedEncodingException
     */
    @RequiresLogin
    @PostMapping("/exportEmployeeDimension")
    public void exportEmployeeDimension(HttpServletResponse response, @RequestBody(required = false) TeamEmployeeDimension dimension) throws UnsupportedEncodingException {

        if (dimension == null) {
            dimension = new TeamEmployeeDimension();
        }
        dimension.setCreateTime1(TimeUtils.getBeginOfDay(dimension.getCreateTime1()));
        dimension.setCreateTime2(TimeUtils.getEndOfDay(dimension.getCreateTime2()));
        Long teamId = Long.valueOf(TokenInformation.getCreateid());
        dimension.setTeamId(teamId);
        List<TeamEmployeeDimension> list = censusAssetsService.exportEmployeeDimension(dimension);
        for (TeamEmployeeDimension item : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("employeeId", item.getEmployeeId());
            map.put("teamId", item.getTeamId());
            map.put("startData", TimeUtils.thisMonthBegins(item.getCreateTime()));
            map.put("endData", TimeUtils.thisMonthEnds(item.getCreateTime()));
            List<String> strings = censusAssetsService.selectAssetsPackName(map);
            String join = "";
            if (!ObjectUtils.isEmpty(strings)) {
                join = StringUtils.join(strings, ",");
            }
            item.setAssetPackName(join);
        }

        ExcelUtil util = new ExcelUtil(TeamEmployeeDimension.class);
        String fileName = "员工维度_数据概览.xlsx";
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, list, "数据概览");
    }
}
