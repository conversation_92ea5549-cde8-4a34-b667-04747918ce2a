package com.zws.appeal.controller.letterDoc.law.enums;


import com.zws.common.core.domain.Option;

import java.util.ArrayList;
import java.util.List;

/**
 * 律所-签章类型枚举
 */
public enum SignTypeEnum {

    /**
     * 0-公司章
     */
    COMPANY(0,"公司章"),
    /**
     * 1-个人章
     */
    PERSONAL(1,"个人章")

    ;

    private Integer code;
    private String info;

    SignTypeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    public static List<Option> getOptions(){
        SignTypeEnum[] values= SignTypeEnum.values();
        List<Option> options=new ArrayList<>();
        for (SignTypeEnum temp: values) {
            options.add(new Option(temp.code,temp.info));
        }
        return options;
    }



    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }


}
