package com.zws.appeal.controller.letterDoc.law.enums;

import com.zws.common.core.domain.Option;

import java.util.ArrayList;
import java.util.List;

/**
 * 审核状态枚举
 * <AUTHOR>
 * @date 2024/1/14 15:30
 */
public enum ApproveStatusEnum {

    //0-待审核，1-审核中，2-已通过，3-未通过
    /**
     * 待审核
     */
    TO_BE_REVIEWED(0,"待审核"),
    /**
     * 审核中
     */
    REVIEWED_ING(1,"审核中"),
    /**
     * 已通过
     */
    PASS(2,"已通过"),
    /**
     * 未通过
     */
    NOT_PASS(3,"未通过"),


    ;
    ApproveStatusEnum(int code,String info){
        this.code=code;
        this.info=info;
    }

    private int code;
    private String info;

    public int getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static ApproveStatusEnum valueOfCode(Integer code){
        if (code==null) {
            return null;
        }
        ApproveStatusEnum[] enums=  ApproveStatusEnum.values();
        for (ApproveStatusEnum temp:enums) {
            if(temp.code==code) {
                return temp;
            }
        }
        return null;
    }
    public static ApproveStatusEnum valueOfInfo(String info){
        ApproveStatusEnum[] enums=  ApproveStatusEnum.values();
        for (ApproveStatusEnum temp:enums) {
            if(temp.getInfo().equals(info)) {
                return temp;
            }
        }
        return null;
    }

    public static List<Option> getOptions(){
        ApproveStatusEnum[] values= ApproveStatusEnum.values();
        List<Option> options=new ArrayList<>();
        for (ApproveStatusEnum temp: values) {
            options.add(new Option(temp.code,temp.info));
        }
        return options;
    }
}
