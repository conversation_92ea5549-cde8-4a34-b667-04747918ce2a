package com.zws.appeal.controller.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 材料寄送查询实体类
 */
@Data
public class MaterialDeliveryRequest implements Serializable {

    /**
     * 物流主键id
     */
    private Long id;

    private List<Long>ids;

    /**
     * 案件ID
     */
    private Long caseId;
    /**
     * 客户姓名
     */
    private String clientName;

    /**
     * 标的额
     */
    private Long remainingDue;

    /**
     * 客户身份证号
     */
    private String clientIdCard;

    /**
     * 户籍地
     */
    private String clientCensusRegister;

    /**
     * 寄送状态
     */
    private String sentStatus;

    /**
     * 寄送材料数量
     */
    private String sentCount;

    /**
     * 寄送法院
     */
    private String sentCourt;

    /**
     * 寄送单号
     */
    private String expressNumber;

    /**
     * 操作时间
     */
    private String creatTime;
    /**
     * 操作人
     */
    private String creatBy;

    /**
     * 是否搜索结果全选
     * true 搜索结果全选
     * false 本页选中
     */
    private Boolean allQuery;

    /**
     * 案件ids
     *
     */
    private List<Long> caseIds;

    /**
     * 团队Id
     */
    private Long teamId;
    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 员工Id
     */
    private Long userId;

    /**
     * 部门id-拼接字符串
     */
    private String stringDeptId;


    /**
     * 部门id集合
     */
    private List<Integer> deptIds;

    /**
     * 团队ID集合
     */
    private List<Long> teamIds;

    /**
     * 解密密钥
     */
    private String decryptKey;

    /**
     * web端类型 1催收 2调解 3调诉
     */
    private Integer webSide;

    /**
     * 案件导入时的阶段
     */
    private String stage;

    /**
     * 0-我的材料寄送，1-材料寄送统计
     */
    private Integer type;

}
