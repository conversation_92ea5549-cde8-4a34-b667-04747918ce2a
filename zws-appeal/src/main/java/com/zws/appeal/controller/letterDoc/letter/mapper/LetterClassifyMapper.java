package com.zws.appeal.controller.letterDoc.letter.mapper;

import com.zws.appeal.controller.letterDoc.letter.domain.LetterClassify;

import java.util.List;

public interface LetterClassifyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LetterClassify record);

    LetterClassify selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LetterClassify record);

    /**
     * 查询列表
     * @param record
     * @return
     */
    List<LetterClassify> selectList(LetterClassify record);

    int selectIsExist(String classifyName);

    /**
     * 获取全部的类型名称
     * @return
     */
    List<String> selectAllclassifyName();
}
