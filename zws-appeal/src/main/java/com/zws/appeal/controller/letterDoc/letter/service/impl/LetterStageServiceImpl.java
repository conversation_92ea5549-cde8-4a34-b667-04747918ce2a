package com.zws.appeal.controller.letterDoc.letter.service.impl;


import com.zws.appeal.controller.letterDoc.letter.mapper.LetterStageMapper;
import com.zws.appeal.controller.letterDoc.letter.service.LetterStageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class LetterStageServiceImpl implements LetterStageService {
@Autowired
private LetterStageMapper appealStageMapper;
    /**
     * 调诉阶段数量查询
     */
    @Override
    public List<Map<String, Integer>> selectAppealStage() {
       List<Map<String, Integer>> resultMet = appealStageMapper.selectAppealStage();
        int AllCase = 0;
        //获取所有调诉机构的id
       List<Long> ids = appealStageMapper.selectCategoryId();
        //根据机构id查询该机构的总案件数
        for (Long id : ids) {
            AllCase += appealStageMapper.selectAllCase(id);
        }
       int sum = AllCase;

        return resultMet;
    }
    /**
     * 调诉阶段数量查询
     */
    @Override
    public Integer tageTotal() {
        Integer integer=appealStageMapper.tageTotal();
        return integer;
    }


}
