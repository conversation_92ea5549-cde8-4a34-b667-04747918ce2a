package com.zws.appeal.controller.appealController;

import com.zws.appeal.domain.appeal.CaseCostRecord;
import com.zws.appeal.service.appeal.ICaseCostRecordService;
import com.zws.common.core.domain.Option;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 案件收费记录
 * <AUTHOR>
 * @date ：Created in 2023/12/5 13:58
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("/cost/record")
public class CaseCostRecordController extends BaseController {

    @Autowired
    private ICaseCostRecordService caseCostRecordService;
    //@Autowired
    //private ITeamDictTypeService teamDictTypeService;

    /**
     * 列表
     * @param params
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo list(CaseCostRecord params){
        startPage();
        List<CaseCostRecord> records = caseCostRecordService.selectList(params);
        return getDataTable(records);
    }

    /**
     * 案件ID 查询收费记录列表
     * @param caseId 案件id
     * @return
     */
    @GetMapping("/listByCaseId")
    public TableDataInfo listByCaseId(@PathVariable("caseId") Long caseId){
        startPage();
        CaseCostRecord params=new CaseCostRecord();
        params.setCaseId(caseId);
        List<CaseCostRecord> records = caseCostRecordService.selectList(params);
        return getDataTable(records);
    }

    /**
     *  ID获取详细信息
     * @param id
     * @return
     */
    @GetMapping("/getById")
    public AjaxResult getById(@RequestParam("id") Long id){
        CaseCostRecord costRecord = caseCostRecordService.getById(id);
        return AjaxResult.success(costRecord);
    }

    /**
     * 新建
     * @param record
     * @return
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody @Validated CaseCostRecord record){
        caseCostRecordService.insert(record);
        return AjaxResult.success();
    }

    /**
     * 开庭缴费(批量)
     * @param record
     * @return
     */
    @PostMapping("/addList")
    public AjaxResult addList(@RequestBody @Validated CaseCostRecord record){
        caseCostRecordService.insertList(record);
        return AjaxResult.success();
    }

    /**
     * 编辑
     * @param record 编辑的内容
     * @return
     */
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody @Validated CaseCostRecord record){
        caseCostRecordService.updateById(record);
        return AjaxResult.success();
    }

    /**
     * 删除
     * @param record 主键id
     * @return
     */
    @PostMapping("/remove")
    public AjaxResult remove(@RequestBody  CaseCostRecord record){
        caseCostRecordService.deleteById(record.getId());
        return AjaxResult.success();
    }


    /**
     * 获取款项类别的选项列表
     * @return
     */
    @GetMapping("/getCostTypeOption")
    public AjaxResult getCostTypeOption(){

        String[] str = new String[]{"诉讼费","保全费","开庭费"};
        List<String> list1 = Arrays.asList(str);
        List<Option> list = new ArrayList<>(str.length);
        for (String s : list1) {
            Option option = new Option();
            option.setCode(s);
            option.setInfo(s);
            list.add(option);
        }

        //String dictType="cost_type"; todo 后续改为可配置
        //List<Option> options= teamDictTypeService.selectOptionByType(dictType);
        return AjaxResult.success(list);
    }

    /**
     * 获取支付方式的选项列表
     * @return
     */
    @GetMapping("/getPayMethodOption")
    public AjaxResult getPayMethodOption(){

        String[] str = new String[]{"微信","支付宝","银联"};
        List<String> list1 = Arrays.asList(str);
        List<Option> list = new ArrayList<>(str.length);
        for (String s : list1) {
            Option option = new Option();
            option.setCode(s);
            option.setInfo(s);
            list.add(option);
        }
        //String dictType="pay_method";todo 后续改为可配置
        //List<Option> options= teamDictTypeService.selectOptionByType(dictType);
        return AjaxResult.success(list);
    }
}
