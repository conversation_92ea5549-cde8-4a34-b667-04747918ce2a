package com.zws.appeal.controller.letterDoc.law.controller;

import com.zws.appeal.controller.letterDoc.law.domain.LawAgency;
import com.zws.appeal.controller.letterDoc.law.domain.LawAgencyLetter;
import com.zws.appeal.controller.letterDoc.law.service.ILawAgencyLetterService;
import com.zws.appeal.controller.letterDoc.law.service.ILawAgencyService;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterTemplateService;
import com.zws.common.core.domain.AdministrativeNo;
import com.zws.common.core.domain.Option;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.IDCardUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.zws.common.core.utils.PageUtils.startPage;

/**
 * 法院管理
 * <AUTHOR>
 * @date ：Created in 2023/12/11 14:21
 */
@CrossOrigin
@RestController
@RequestMapping("/court/manage")
public class LawAgencyController extends BaseController {

    @Autowired
    private ILawAgencyService lawAgencyService;
    @Autowired
    private ILawAgencyLetterService lawAgencyLetterService;
    @Autowired
    private ILetterTemplateService templateService;

    /**
     * 查询启用的文书模板信息
     *
     * @param template
     * @return
     */
    @GetMapping("/listNotPaging")
    public AjaxResult listNotPaging(LetterTemplate template) {
        template.setStatus(0);
        List<LetterTemplate> list = templateService.selectList1(template);
        return AjaxResult.success(list);
    }

    /**
     * 列表
     * @param lawAgency
     * @return
     */
    @GetMapping("/list")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public TableDataInfo list(LawAgency lawAgency) {
        startPage();
        List<LawAgency> lawAgencies = lawAgencyService.selectList(lawAgency);
        return getDataTable(lawAgencies);
    }


    /**
     * 新增
     * @param record
     * @return
     */
    @PostMapping("/insert")
    public AjaxResult add(@RequestBody @Validated LawAgency record){
        record.setTeamId(SecurityUtils.getTeamId());
        lawAgencyService.insert(record);
        return AjaxResult.success();
    }

    /**
     * 根据主键id查询法院信息
     *
     * @param id
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/listById", method = RequestMethod.GET)
    public AjaxResult listById(Long id) {
        if (id == null) {
            throw new GlobalException("法院主键id不能为空");
        }
        LawAgency lawAgency = lawAgencyService.selectCourtManageById(id);
        return AjaxResult.success("操作成功", lawAgency);
    }

    /**
     * 编辑
     * @param record
     * @return
     */
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody @Validated LawAgency record){
        record.setTeamId(SecurityUtils.getTeamId());
        lawAgencyService.updateById(record);
        return AjaxResult.success();
    }

    /**
     * 删除
     * @param record
     * @return
     */
    @PostMapping("/delete")
    public AjaxResult remove(@RequestBody LawAgency record){
        lawAgencyService.deleteById(record.getId());
        return AjaxResult.success();
    }

    /**
     * 机构分类选项
     * @return
     */
    @GetMapping("/getAgencyTypeOptions")
    public AjaxResult getAgencyTypeOptions() throws IOException {
        List<Option> options=new ArrayList<>();
        options.add(new Option("法院","法院"));
        options.add(new Option("律所","律所"));
        return AjaxResult.success(options);
    }


    /**
     * 获取城市选项-使用label 展示，label 传值，二级联动
     * @return
     */
    @GetMapping("/getCityOptions")
    public AjaxResult getCityOptions() throws IOException {
        List<AdministrativeNo> administrativeNos = IDCardUtils.readSourceTxt();
        return AjaxResult.success(administrativeNos);
    }

    /**
     * 获取合作机构文书
     * @param agencyId
     * @return
     */
    @GetMapping("/getAgencyLetters")
    public AjaxResult getAgencyLetters(Long agencyId){
        List<LawAgencyLetter> lawAgencyLetters = this.lawAgencyLetterService.selectByAgencyId(agencyId);
        return AjaxResult.success(lawAgencyLetters);
    }

    /**
     * 获取机构名称下拉框
     * @return
     */
    @GetMapping("/getCourtNameOptions")
    public AjaxResult getCourtNameOptions(){
        List<LawAgency> list = lawAgencyService.getCourtNameOptions();
        ArrayList<Option> list1 = new ArrayList<>();
        for (LawAgency lawAgency : list) {
            Option option = new Option();
            option.setCode(lawAgency.getId());
            option.setInfo(lawAgency.getCourtName());
            list1.add(option);
        }
        return AjaxResult.success(list1);
    }

    /**
     * 根据法院id查询文书模板信息
     *
     * @param courtId
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectByCourtId", method = RequestMethod.GET)
    public AjaxResult selectByCourtId(Long courtId) {
        if (courtId == null) {
            throw new GlobalException("法院主键id不能为空");
        }
        List<LetterTemplate> documentTemplates = this.lawAgencyLetterService.selectByAgencyId1(courtId);
        return AjaxResult.success("操作成功", documentTemplates);
    }

}
