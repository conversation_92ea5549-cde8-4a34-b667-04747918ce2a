package com.zws.appeal.controller.letterDoc.law.mapper;


import com.zws.appeal.controller.letterDoc.law.domain.LawTeamSign;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface LawTeamSignMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LawTeamSign record);

    int insertSelective(LawTeamSign record);

    LawTeamSign selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LawTeamSign record);

    int updateByPrimaryKey(LawTeamSign record);
}