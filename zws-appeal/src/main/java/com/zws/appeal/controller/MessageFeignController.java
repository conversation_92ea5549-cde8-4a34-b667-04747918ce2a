package com.zws.appeal.controller;

import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresPermissions;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.utils.Log;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.RemoteMessageService;
import com.zws.system.api.domain.TeamMessageCenter;
import com.zws.system.api.domain.TeamUserMessage;
import com.zws.system.api.domain.dto.MessageDeleteDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 消息Feign代理Controller
 *
 * <AUTHOR>
 * @date 2024-03-08
 */

@RestController
@CrossOrigin
@RequestMapping(value = "/team")
public class MessageFeignController extends BaseController {

    @Autowired
    private RemoteMessageService remoteMessageService;

    /**
     * 消息列表
     *
     * @param param
     * @return
     */
    @GetMapping("/getList")
    public TableDataInfo selectList(TeamUserMessage param, @RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize) {
        startPage();
        param.setUserId((long) TokenInformation.getUserid());
        param.setIdentification(TokenInformation.getType());
        TableDataInfo tableDataInfo = remoteMessageService.getList(param, pageNum, pageSize);
        return tableDataInfo;
    }

    /**
     * 添加用户消息
     *
     * @param teamUserMessages
     * @return
     */
    @Log(title = "消息中心-添加用户消息", businessType = BusinessType.INSERT)
    @GetMapping("/insert")
    public AjaxResult add(@RequestBody List<TeamUserMessage> teamUserMessages) {
        R r = remoteMessageService.insertUserMessage(teamUserMessages, SecurityConstants.INNER);
        if (r.getCode() == R.SUCCESS) {
            return AjaxResult.success();
        } else {
            throw new GlobalException("添加失败");
        }
    }

    /**
     * 删除信息
     *
     * @param ids
     * @return
     */
    @Log(title = "消息中心-删除信息", businessType = BusinessType.DELETE)
    @RequiresPermissions("message:home:delete")
    @PostMapping("/delete")
    public AjaxResult remove(@RequestBody List<Long> ids) {
        MessageDeleteDto messageDeleteDto = new MessageDeleteDto();
        messageDeleteDto.setIds(ids);
        messageDeleteDto.setUserId(SecurityUtils.getUserId());
        messageDeleteDto.setType(SecurityUtils.getAccountType());
        R r = remoteMessageService.delete(messageDeleteDto, SecurityConstants.INNER);
        if (r.getCode() == R.SUCCESS) {
            return AjaxResult.success();
        } else {
            throw new GlobalException("删除失败！");
        }
    }

    /**
     * 获取导航信息
     * 获取当前最新的5条信息
     *
     * @return
     */
    @GetMapping("/getNavMessage")
    public AjaxResult selectListWithNavMessage() {
        R<List<TeamUserMessage>> r = remoteMessageService.getNavMessage();
        if (r.getCode() == R.SUCCESS) {
            return AjaxResult.success(r.getData());
        } else {
            //throw new GlobalException(r.getMsg());
            return AjaxResult.error(r.getMsg());
        }
    }

    /**
     * 获取用户信息-系统公告详情
     *
     * @return
     */
    @GetMapping("/getUserNotice")
    public AjaxResult infoWithUserNotice(Long id) {
        R<TeamMessageCenter> r = remoteMessageService.getUserNotice(id, SecurityConstants.INNER);
        if (r.getCode() == R.SUCCESS) {
            return AjaxResult.success(r.getData());
        } else {
            throw new GlobalException(r.getMsg());
        }
    }

    /**
     * 获取未读信息数量
     *
     * @return
     */
    @GetMapping("/getUnreadQuantity")
    public AjaxResult infoWithUnreadQuantity() {
        R<Integer> r = remoteMessageService.getUnreadQuantity();
        if (r.getCode() == R.SUCCESS) {
            return AjaxResult.success(r.getData());
        } else {
            throw new GlobalException("添加失败");
        }
    }

    /**
     * 标记已读
     *
     * @return
     */
    @Log(title = "消息中心-标记已读", businessType = BusinessType.UPDATE)
    @RequiresPermissions("message:home:read")
    @PostMapping("/markRead")
    public AjaxResult markRead(@RequestBody List<Long> ids) {
        R r = remoteMessageService.markRead(ids, SecurityConstants.INNER);
        if (r.getCode() == R.SUCCESS) {
            return AjaxResult.success();
        } else {
            throw new GlobalException("添加失败");
        }
    }


}
