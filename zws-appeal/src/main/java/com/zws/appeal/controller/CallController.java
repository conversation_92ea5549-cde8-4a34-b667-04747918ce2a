package com.zws.appeal.controller;

import com.zws.common.core.callcenter.pojo.CalloutParam;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.enums.call.BindStatusEnum;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.common.security.annotation.RequiresPermissions;
import com.zws.appeal.agservice.AgCallService;
import com.zws.appeal.domain.Employees;
import com.zws.appeal.domain.call.CallSip;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.pojo.call.AssignSipForm;
import com.zws.appeal.pojo.call.CallOutForm;
import com.zws.appeal.pojo.call.UpdateSipPwdForm;
import com.zws.appeal.service.SettingsService;
import com.zws.appeal.service.call.ICallSipService;
import com.zws.appeal.utils.Log;
import com.zws.appeal.utils.TokenInformation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 呼叫中心 相关接口
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/24 21:01
 */
@CrossOrigin
@RestController
@RequestMapping(value = "/call")
public class CallController {

    @Autowired
    private ICallSipService callSipService;
    @Autowired
    private AgCallService agCallService;
    @Autowired
    private SettingsService settingsService;


    /**
     * 重置坐席密码
     *
     * @param form
     * @return
     */
    @RequiresPermissions("system:seat:password")
    @Log(title = "呼叫中心（重置坐席密码）", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @PostMapping("/updateSipPwd")
    public AjaxResult editSipPwd(@RequestBody UpdateSipPwdForm form) {
        agCallService.updateSipPwd(form);
        return AjaxResult.success();
    }


    /**
     * 获取未分配的SIP账号
     *
     * @param allocate 是否分配  0-已分配，1-未分配 null-全部
     * @return
     */
    @RequiresLogin
    @GetMapping("/getUnassignedSipList")
    public AjaxResult selectListWithUnassignedSip(Integer allocate) {
        BindStatusEnum bindStatus = null;
        if (allocate != null) {
            switch (allocate) {
                case 0:
                    bindStatus = BindStatusEnum.BIND;
                    break;
                case 1:
                    bindStatus = BindStatusEnum.UNBOUND;
                    break;
            }
        }

        List<CallSip> sipList = callSipService.selectUnassignedSip(bindStatus);
        return AjaxResult.success(sipList);
    }


    /**
     * 判断当前用户是否分配SIP坐席
     *
     * @return
     */
    @RequiresLogin
    @GetMapping("/checkUserSip")
    public AjaxResult checkUserSip() {
        boolean isSip = false;
        boolean isPreTestSip = false;
        Map<String, Object> map = new HashMap<>();
        int accountType = TokenInformation.getType();
        if (accountType == UserConstants.ACCOUNT_TYPE_1) {
            Employees employees = settingsService.selectEmployeesId(TokenInformation.getUserid());
            if (employees == null) {
                isSip = false;
            } else {
                isSip = StringUtils.isNotEmpty(employees.getSipNumber());
                map.put("account", employees.getSipNumber());
                map.put("password", employees.getSipPassword());
                Integer seatsType = employees.getSeatsType();
                if (seatsType == null) seatsType = 1;
                if (seatsType == 2) isPreTestSip = true;
            }
        }
        map.put("isSip", isSip);
        map.put("isPreTestSip", isPreTestSip);
        return AjaxResult.success(map);
    }


    /**
     * 分配坐席
     *
     * @return
     */
    @RequiresPermissions("system:user:seat")
    @Log(title = "呼叫中心（分配坐席）", businessType = BusinessType.OTHER)
    @RequiresLogin
    @PostMapping("/assignSip")
    public AjaxResult assignSip(@RequestBody AssignSipForm form) {
        agCallService.assignSip(form);
        return AjaxResult.success();
    }


    /**
     * 点击外呼-联系人
     *
     * @return
     */
    @Log(title = "呼叫中心（点击外呼-联系人）", businessType = BusinessType.OTHER)
    @RequiresLogin
    @PostMapping("/callOutByContact")
    public AjaxResult callOutByContact(@RequestBody CallOutForm form) {
        //点击外呼-联系人
        CalloutParam calloutParam = agCallService.callOutByContact(form);
        return AjaxResult.success(calloutParam);
    }

    /**
     * 点击外呼-案件
     *
     * @return
     */
    @Log(title = "呼叫中心（点击外呼-案件）", businessType = BusinessType.OTHER)
    @RequiresLogin
    @PostMapping("/callOutByCase")
    public AjaxResult callOutByCase(@RequestBody CallOutForm form) {
        //点击外呼-案件
        CalloutParam calloutParam = agCallService.callOutByCase(form);
        return AjaxResult.success(calloutParam);
    }


    /**
     * 点击外呼-输入电话号码
     *
     * @return
     */
    @Log(title = "呼叫中心（点击外呼-输入电话号码）", businessType = BusinessType.OTHER)
    @RequiresLogin
    @PostMapping("/callOutByPhoneNumber")
    public AjaxResult callOutByPhoneNumber(@RequestBody CallOutForm form) {
        //点击外呼-输入电话号码
        CalloutParam calloutParam = agCallService.callOutByPhone(form);
        return AjaxResult.success(calloutParam);
    }


    /**
     * 点击外呼-工单来电号码
     *
     * @return
     */
    @Log(title = "呼叫中心（点击外呼-工单来电号码）", businessType = BusinessType.OTHER)
    @RequiresLogin
    @PostMapping("/callOutByWorkOrderPhoneNumber")
    public AjaxResult callOutByWorkOrderPhoneNumber(@RequestBody CallOutForm form) {
        //点击外呼-输入电话号码
        CalloutParam calloutParam = agCallService.callOutByWorkOrder(form);
        return AjaxResult.success(calloutParam);
    }

    /**
     * 呼叫挂断
     *
     * @return
     */
    @Log(title = "呼叫中心（点击外呼-呼叫挂断）", businessType = BusinessType.OTHER)
    @RequiresLogin
    @PostMapping("/callOutHangup")
    public AjaxResult callOutHangup() {
        agCallService.callOutHangup();
        return AjaxResult.success();
    }

}
