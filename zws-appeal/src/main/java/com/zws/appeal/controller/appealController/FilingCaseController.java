package com.zws.appeal.controller.appealController;

import cn.hutool.core.bean.BeanUtil;
import com.zws.appeal.agservice.AgCaseService;
import com.zws.appeal.agservice.AgLawsuitService;
import com.zws.appeal.domain.MessageTemplatePojo;
import com.zws.appeal.domain.appeal.FilingCase;
import com.zws.appeal.pojo.SendRecordsPojos;
import com.zws.appeal.pojo.appeal.FilingCasePojo;
import com.zws.appeal.pojo.appeal.FilingCaseQueryPojo;
import com.zws.appeal.service.appeal.IFilingCaseService;
import com.zws.common.core.domain.Option;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.annotation.RequiresLogin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 网上立案模块
 *
 * @Author：liuxifeng
 * @Date：2024/6/18 15:10
 * @Describe：网上立案
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/filing-case")
public class FilingCaseController extends BaseController {


    @Autowired
    private AgLawsuitService agLawsuitService;
    @Autowired
    private IFilingCaseService filingCaseService;
    @Autowired
    private AgCaseService agCaseService;
    @Autowired
    private RedisService redisService;
    /**
     * 列表查询
     * 查询不同的小阶段、传参数不同处理
     * @return
     */
    @GetMapping("/selectList")
    public TableDataInfo selectList(FilingCasePojo pojo){
        startPage();
        List<FilingCasePojo> list = filingCaseService.selectList(pojo);
        return getDataTable(list);
    }

    /**
     * 统计 剩余应还债权金额 计算
     * @return
     */
    @PostMapping("/selectWithMoney")
    public AjaxResult selectWithMoney(@RequestBody FilingCasePojo pojo){
        Map<String,Object> map = filingCaseService.selectWithMoney(pojo);
        return AjaxResult.success(map);
    }

    /**
     * 批量立案 生成立案信息记录 唯一的
     * @param pojo
     * @return
     */
    @PostMapping("/batchFilingCase")
    public AjaxResult batchFilingCase(@RequestBody FilingCaseQueryPojo pojo){
        int i = agLawsuitService.batchFilingCase(pojo);
        return AjaxResult.success();
    }


    /**
     * 下载登记模板文件 表头
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) throws IOException {
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("批量登记模板.xlsx", "utf-8"));
        ExcelUtil<FilingCase> util = new ExcelUtil<FilingCase>(FilingCase.class);
        util.exportExcel(response, new ArrayList<>(), "批量登记模板");
    }

    /**
     * 批量立案登记
     *
     * @param pojo
     * @return
     */
    @PostMapping("/batchRegister")
    public AjaxResult batchRegister(@RequestBody FilingCasePojo pojo) {
        agLawsuitService.batchRegister(pojo);
        return AjaxResult.success();
    }


    /**
     * 案件转移 选择参数
     * 1调执 2诉讼 3保全 4执行
     */
    @GetMapping("/selectWithStage")
    public AjaxResult selectWithStage(Integer type,Long caseId){
        List<Option> list = filingCaseService.selectWithStage(type,caseId);
        return AjaxResult.success(list);
    }

    /**
     * 案件转移 选择参数
     * 返回诉讼执行+执行回款 相关小阶段
     * @param type
     * @return
     */
    @GetMapping("/selectWithLawsuitStage")
    public AjaxResult selectWithLawsuitStage(){
        List<Option> list = filingCaseService.selectWithLawsuitStage();
        return AjaxResult.success(list);
    }

    /**
     * 调执-诉讼-保全流程 ==>案件转移
     * @param pojo
     * @return
     */
    @PostMapping("/batchTransfer")
    public AjaxResult batchTransfer(@RequestBody Object pojo){
        Map<String, Object> map = BeanUtil.beanToMap(pojo);
        agLawsuitService.batchTransfer(map);
        return AjaxResult.success();
    }

    /**
     * 审核状态登记 更改阶段 批量更改
     *
     * @param filingCase
     * @return
     */
    @PostMapping("/updateWithCheck")
    public AjaxResult updateWithCheck(@RequestBody FilingCasePojo filingCase){
        int i = filingCaseService.updateWithCheck(filingCase);
        return AjaxResult.success();
    }

}
