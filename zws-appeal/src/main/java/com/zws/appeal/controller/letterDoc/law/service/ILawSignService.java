package com.zws.appeal.controller.letterDoc.law.service;


import com.zws.appeal.controller.letterDoc.law.domain.LawSign;

import java.util.List;

/**
 * 签章
 * <AUTHOR>
 * @date ：Created in 2023/11/22 21:58
 */
public interface ILawSignService {

    /**
     * 新增
     * @param record
     */
    Long insert(LawSign record);

    /**
     * 主键 选择更新
     * @param record
     */
    void updateById(LawSign record);

    /**
     * 主键删除
     * @param id
     */
    void deleteById(Long id);

    /**
     * 主键id查询
     * @param id
     * @return
     */
    LawSign getById(Long id);


    /**
     * 条件查询
     * @param record
     * @return
     */
    List<LawSign> selectList(LawSign record);



}
