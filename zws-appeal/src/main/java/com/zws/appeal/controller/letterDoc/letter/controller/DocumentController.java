package com.zws.appeal.controller.letterDoc.letter.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.sun.jmx.snmp.tasks.ThreadService;
import com.zws.appeal.controller.letterDoc.law.enums.ApproveStatusEnum;
import com.zws.appeal.controller.letterDoc.letter.domain.DocumentMessage;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;
import com.zws.appeal.controller.letterDoc.letter.service.IDocumentMessageService;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterDocService;
import com.zws.appeal.domain.ApproveProce;
import com.zws.appeal.domain.letter.LogisticsInfo;
import com.zws.appeal.enums.ApproveEnum;
import com.zws.appeal.enums.SourceTypeEnum;
import com.zws.appeal.service.MyApprovalService;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文书管理
 *
 * <AUTHOR>
 * @since 2023-01-04 20:38
 */
@CrossOrigin
@RestController
@RequestMapping("/document/item")
public class DocumentController extends BaseController {

    @Autowired
    private ILetterDocService documentService;
    @Autowired
    private MyApprovalService myApprovalService;
    @Autowired
    private IDocumentMessageService documentMessageService;

    /**
     * 列表
     *
     * @param letterDoc
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo list(LetterDoc letterDoc) {
        startPage();
        List<LetterDoc> list = documentService.selectSimpleList(letterDoc);
        return getDataTable(list);
    }

    /**
     * 导出文书Excel
     *
     * @param letterDoc
     * @return
     */
    @PostMapping("/exportExcel")
    public void export(@RequestBody LetterDoc letterDoc, HttpServletResponse response) throws Exception {
        //勾选、根据条件查询结果导出
        List<LetterDoc> list = documentService.selectSimpleList(letterDoc);
        ExcelUtil<LetterDoc> util = new ExcelUtil<>(LetterDoc.class);
        String fileName = URLEncoder.encode("导出文书.xls", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName);
        util.exportExcel(response, list, "文书");
    }

    /**
     * 访问脱敏后签章文件
     *
     * @return
     */
    @GetMapping("/getFile")
    public void getFile(HttpServletResponse response, HttpServletRequest request) throws IOException {

        String id = request.getParameter("id");
        LetterDoc document = documentService.selectByRandomId(id);
        if (document == null || StringUtils.isEmpty(document.getDesensitizeUrl())) {
            //return AjaxResult.error("文件不存在");
            throw new ServiceException("文件不存在");
        }
        if (document.getVisitNum() == null || document.getVisitNum() == 0) {
            document.setVisitNum(1);
            documentService.updateById(document);
        } else {
            document.setVisitNum(document.getVisitNum() + 1);
            documentService.updateById(document);
        }
        if (document.getSourceFileType() != null && document.getSourceFileType().equals(SourceTypeEnum.Zip_Type.getCode())) {
            response.sendRedirect(document.getSignPreviewUrl());
        } else {
            response.sendRedirect(document.getDesensitizeUrl());
        }
        //return AjaxResult.success(Document.getDesensitizeUrl());
    }




    /**
     * 导出
     *
     * @param response
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody LetterDoc letterDoc) throws IOException {
        //判断文书批次，只有文书批次状态为待审核、已审核才能导出
        Integer letterId = letterDoc.getLetterId();
        DocumentMessage documentMessage = documentMessageService.getById(letterId);
        if (documentMessage == null) {
            throw new ServiceException("参数错误，请清除缓存后再试一下");
        }
        if (documentMessage.getStatus() == ApproveStatusEnum.REVIEWED_ING.getCode()) {
            throw new ServiceException("批次案件审批中不可导出");
        }
        //导出律师函：导出勾选（复选框）的批次的已经通过的文书，以文档打包的形式导出（律所函以pdf的格式导出-导出不能编辑）；
        LetterDoc param = new LetterDoc();
        //param.setStatus(ApproveStatusEnum.PASS.getCode());
        param.setIds(letterDoc.getIds());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<LetterDoc> letters = documentService.selectList(param, loginUser);
        documentService.exportSignPreviewUrl(response, letters,loginUser);
    }

    /**
     * 预览(没有签章)
     *
     * @param id
     * @return
     */
    @GetMapping("/getPreview")
    public AjaxResult getPreview(Integer id) {
        LetterDoc document = documentService.getById(Long.valueOf(id));
        String url = document.getPreviewUrl();
        if (document.getSignPreviewUrl() != null && document.getSignPreviewUrl() != "") {
            url = document.getSignPreviewUrl();
        }
        Integer total = document.getPreviewPages() == null ? 1 : document.getPreviewPages();
        Map<String, Object> params = new HashMap<>(2);
        params.put("url", url);
        params.put("total", total);
        return AjaxResult.success("操作成功", params);

    }

//    /**
//     * 签章文件(已签章)
//     *
//     * @param id
//     * @return
//     */
//    @GetMapping("/getSignFile")
//    public AjaxResult getSignFile(Integer id) {
//        LetterDoc document = documentService.getById(Long.valueOf(id));
//        String url = document.getSignPreviewUrl();
//        if (url == null) {
//            //重新生成签章
//            List<Integer> ids = new ArrayList<>();
//            ids.add(document.getId());
//            threadServie.createSignFileById(ids,"1");
//            throw new ServiceException("生成签章中，请稍后再试");
//        }
//
//        Integer total = document.getPreviewPages() == null ? 1 : document.getPreviewPages();
//        Map<String, Object> params = new HashMap<>(2);
//        params.put("url", url);
//        params.put("total", total);
//        return AjaxResult.success("操作成功", params);
//    }




    @PostMapping("/getSignFileTest")
    public AjaxResult getSignFileTest(String s) {
//        String s1 = SymmetricAlgorithm.AES.getValue();
//        byte[] key = s1.getBytes();
        byte[] key = SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded();
        //构建
        SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, key);
        //加密
        byte[] encrypt = aes.encrypt(s);
        //解密
        byte[] decrypt = aes.decrypt(encrypt);
        //加密为16进制表示
        String encryptHex = aes.encryptHex(s);
        System.out.println(encryptHex);
        //解密为字符串
        String decryptStr = aes.decryptStr(encryptHex, CharsetUtil.CHARSET_UTF_8);
        System.out.println(decryptStr);

        return AjaxResult.success();
    }

}
