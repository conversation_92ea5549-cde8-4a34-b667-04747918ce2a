package com.zws.appeal.controller;

import cn.hutool.core.date.DateUtil;
import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.enums.call.TaskStatusEnum;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.appeal.agservice.TeamSysAgService;
import com.zws.appeal.domain.call.CallDownloadTask;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.pojo.DataPermis;
import com.zws.appeal.pojo.call.CallDownloadTaskParam;
import com.zws.appeal.service.call.ICallDownloadTaskService;
import com.zws.appeal.utils.Log;
import com.zws.appeal.utils.TokenInformation;
import com.zws.system.api.RemoteCaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 通话记录-下载任务
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/24 11:53
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("/call/downloadTask")
public class CallDownloadTaskController extends BaseController {

    @Autowired
    private ICallDownloadTaskService downloadTaskService;
    @Autowired
    private RemoteCaseService remoteCaseService;
    @Autowired
    private TeamSysAgService teamSysAgService;

    /**
     * 列表
     *
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo selectList(CallDownloadTaskParam param) {
        if (!ObjectUtils.isEmpty(param.getCreateTime1())) {
            //一天的开始，结果：2017-03-01 00:00:00
            Date beginOfDay = DateUtil.beginOfDay(param.getCreateTime1());
            param.setCreateTime1(beginOfDay);
        }
        if (!ObjectUtils.isEmpty(param.getCreateTime2())) {
            //一天的结束，结果：2017-03-01 23:59:59
            Date endOfDay = DateUtil.endOfDay(param.getCreateTime2());
            param.setCreateTime2(endOfDay);
        }
        // 数据权限过滤
        DataPermis dataPermis = teamSysAgService.getDataPermis();
        Integer teamId = TokenInformation.getCreateid();
        param.setTeamId(teamId.longValue());
        param.setDeptIds(dataPermis.getDeptIds());
//        param.setCreatedByIds(teamSysAgService.getEmployeesIds());
        param.setEmployeesIds(dataPermis.getEmployeesIds());

        startPage();
        List<CallDownloadTask> taskList = downloadTaskService.selectList(param);
        return getDataTable(taskList);
    }

    /**
     * 获取任务状态-选项
     *
     * @return
     */
    @GetMapping("/getTaskStatus")
    public AjaxResult selectListWithTaskStatus() {
        TaskStatusEnum[] enums = TaskStatusEnum.values();
        List<Map<String, Object>> pojoList = new ArrayList<>();
        for (TaskStatusEnum temp : enums) {
            Map<String, Object> map = new HashMap<>();
            map.put("code", temp.getCode());
            map.put("info", temp.getInfo());
            pojoList.add(map);
        }
        return AjaxResult.success(pojoList);
    }

    /**
     * 执行下载录音的 工作任务
     *
     * @param taskId 录音下载任务id
     * @return
     */
    @Log(title = "通话记录-下载任务（执行下载录音的 工作任务）", businessType = BusinessType.EXPORT)
    @PostMapping("/executeWorkTask")
    public AjaxResult executeWorkTask(Long taskId) {
        R r = remoteCaseService.executeWorkTask(taskId, SecurityConstants.INNER);
        if (r.getCode() != R.SUCCESS) {
            return AjaxResult.error(r.getMsg());
        }
        return AjaxResult.success();
    }

}
