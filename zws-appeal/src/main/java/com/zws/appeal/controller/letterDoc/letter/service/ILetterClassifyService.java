package com.zws.appeal.controller.letterDoc.letter.service;

import com.zws.appeal.controller.letterDoc.letter.domain.LetterClassify;

import java.util.List;

/**
 * 函件类型
 * <AUTHOR>
 * @date ：Created in 2023/11/23 20:15
 */
public interface ILetterClassifyService {

    Long insert(LetterClassify record);

    void updateById(LetterClassify record);

    void deleteById(Long id);

    LetterClassify getById(Long id);

    /**
     * 查询列表
     * @param record
     * @return
     */
    List<LetterClassify>  selectList(LetterClassify record);


}
