package com.zws.appeal.controller.letterDoc.letter.service.impl;

import cn.hutool.core.compress.ZipReader;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.*;
import cn.hutool.cron.CronUtil;
import cn.hutool.cron.task.Task;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;

import com.zws.appeal.constant.ExpressApiConstants;
import com.zws.appeal.constant.LetterConstants;
import com.zws.appeal.controller.letterDoc.law.enums.ApproveStatusEnum;
import com.zws.appeal.controller.letterDoc.law.enums.StatusEnum;
import com.zws.appeal.controller.letterDoc.letter.domain.DocumentMessage;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;
import com.zws.appeal.controller.letterDoc.letter.mapper.DocumentMessageMapper;
import com.zws.appeal.controller.letterDoc.letter.service.IDocumentMessageService;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterDocService;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterTemplateService;
import com.zws.appeal.domain.ApprovalSteps;
import com.zws.appeal.enums.ApproveEnum;
import com.zws.appeal.enums.ProceEnum;
import com.zws.appeal.enums.SourceTypeEnum;
import com.zws.appeal.service.TeamService;
import com.zws.appeal.service.approve.ThreadServie;
import com.zws.appeal.utils.DateToCronUtils;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.R;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.DateUtils;
import com.zws.common.core.utils.IdUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.pdf.PdfUtils;

import com.zws.common.redis.service.RedisService;

import com.zws.system.api.RemoteFileService;
import com.zws.system.api.domain.SysFile;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 律函批次 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-04 20:38
 */
@Slf4j
@Service
public class DocumentMessageServiceImpl implements IDocumentMessageService {


    @Autowired
    private ILetterTemplateService iLetterTemplateService;
    @Autowired
    private ILetterDocService iLetterDocService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private DocumentMessageMapper baseMapper;
    @Autowired
    private RemoteFileService remoteFileService;


    @Override
    public List<Long>  insert(DocumentMessage message, List<Map<String, Object>> rows,LoginUser loginUser) {

        message.setQuantity(rows.size());
        message.setStatus(ApproveStatusEnum.TO_BE_REVIEWED.getCode());
        message.setProce(ApproveStatusEnum.TO_BE_REVIEWED.getCode());
        message.setProceSort(0);
        message.setImportStatus(StatusEnum.IMPORT_ING.getCode());

        message.setCreateBy(TokenInformation.getUsername());
        message.setCreateById(TokenInformation.getUserid());
        message.setCreateTime(DateUtils.getNowDate());
        message.setDelFlag(BaseConstant.DelFlag_Being);
        message.setTenantId(TokenInformation.getCreateid());
        message.setUpdateBy(TokenInformation.getUsername());
        message.setUpdateById(TokenInformation.getCreateid());
        message.setUpdateTime(DateUtils.getNowDate());
        message.setSourceFileType(SourceTypeEnum.Excel_Type.getCode());
        this.baseMapper.insert(message);

        DocumentMessage documentMessage = new DocumentMessage();
        documentMessage.setId(message.getId());
        try {
            Integer templateId = message.getTemplateId();
            List<LetterDoc> letters = new ArrayList<>();
            for (int i = 0; i < rows.size(); i++) {
                Map<String, Object> row = rows.get(i);
                String data = JSONUtil.toJsonStr(row);
                LetterDoc document = new LetterDoc();
                document.setLetterId(message.getId());
                document.setItemData(data);
                document.setStatus(ApproveStatusEnum.TO_BE_REVIEWED.getCode());
//                document.setStatus(ApproveStatusEnum.PASS.getCode());
                document.setSerialNo(createSerialNo(message.getId(), i + 1));
                document.setProce(ProceEnum.TO_BE_REVIEWED.getCode());
                document.setProceSort(0);
                LetterTemplate template = iLetterTemplateService.getById(Long.valueOf(templateId));
                document.setPreviewPages(template.getPreviewPages());
                String previewUrl = iLetterDocService.createPreview(document, templateId, false);
                document.setPreviewUrl(previewUrl);
                document.setSourceFileType(SourceTypeEnum.Excel_Type.getCode());
                document.setMailingStatus(ExpressApiConstants.state_0);
                //生成预览
                iLetterDocService.save(document,loginUser);
                System.out.println("letter:"+document);
                letters.add(document);
            }
            String noZipFileUrl = iLetterDocService.exportSignPreviewUrl(null, letters,loginUser);
            log.info("文书批次：{}：noZipFileUrl:{}",message.getId(),noZipFileUrl);
            documentMessage.setNotZipFileUrl(noZipFileUrl);
            documentMessage.setImportStatus(StatusEnum.IMPORT_SUCCESS.getCode());
            //documentService.saveBatch(letters);
            //签章
            List<Long> ids =letters.stream().map(LetterDoc::getId).distinct().collect(Collectors.toList());
            return ids;
        } catch (Exception e) {
            documentMessage.setImportStatus(StatusEnum.IMPORT_FAIL.getCode());
            this.baseMapper.updateByPrimaryKeySelective(documentMessage);
            log.error(e.getMessage());
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        } finally {
            this.baseMapper.updateByPrimaryKeySelective(documentMessage);
        }

    }

    @Override
    public List<DocumentMessage> selectList(DocumentMessage message, boolean bo) {

        ApproveEnum approveEnum = ApproveEnum.DOCUMENT;
        ApprovalSteps approvalSteps = teamService.getApprovalSteps(approveEnum);
        if (bo && approvalSteps == null) {
            System.err.println("不在审核流程中");
            //不在审核流程中,查询已经审核过的历史
            DocumentMessage tempParam = new DocumentMessage();
            tempParam.setCreateById(TokenInformation.getUserid());
            tempParam.setTenantId(TokenInformation.getCreateid());
            List<Integer> ids = this.baseMapper.selectedApproveProceLetterId(tempParam);
            if (ids.size() == 0) {
                return new ArrayList<>();
            }
            message.setIds(ids);
        }
        message.setTenantId(TokenInformation.getCreateid());
        if (message.getCreateTime1() != null) {
            message.setCreateTime1(DateUtil.beginOfDay(message.getCreateTime1()));
        }
        if (message.getCreateTime2() != null) {
            message.setCreateTime2(DateUtil.endOfDay(message.getCreateTime2()));
        }

        if (message.getUpdateTime1() != null) {
            message.setUpdateTime1(DateUtil.beginOfDay(message.getUpdateTime1()));
        }
        if (message.getUpdateTime2() != null) {
            message.setUpdateTime2(DateUtil.endOfDay(message.getUpdateTime2()));
        }

        if (LetterConstants.likeArr.contains(message.getBatchNum())) {
            message.setBatchNum("\\" + message.getBatchNum());
        }
        if (LetterConstants.likeArr.contains(message.getTemplateName())) {
            message.setTemplateName("\\" + message.getTemplateName());
        }
        if (LetterConstants.likeArr.contains(message.getCreateBy())) {
            message.setCreateBy("\\" + message.getCreateBy());
        }
        message.setApproveCode(ApproveEnum.DOCUMENT.getCode());
        message.setApproveSort(approvalSteps==null?0:approvalSteps.getSort());
        message.setUserId(TokenInformation.getUserid());

        return this.baseMapper.selectList(message);

    }

    @Override
    public List<DocumentMessage> selectExportZip(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ServiceException("请选择需要导出的");
        }
        List<DocumentMessage> list = this.baseMapper.selectExportZip(ids);
        return list;


    }

    @Override
    public DocumentMessage getById(Integer id) {
        return this.baseMapper.selectByPrimaryKey(id);
    }

    @Override
    public Map<String, Object> verifyFile(DocumentMessage message) {
        List<String> variables = iLetterTemplateService.getTemplateVariable(message.getTemplateId());

        String fileUrl = message.getSourceFileUrl();
        //下载文件
        File tempFile = FileDownloadUtils.downloadTempFile(fileUrl);
        //读取文件
        ExcelReader reader = ExcelUtil.getReader(tempFile);

        int colCount = reader.getColumnCount();
        log.info("导入函件,总列数:{}", colCount);
        int rowCount = reader.getRowCount();
        log.info("导入函件,总行数:{}", rowCount);
        //单次导入上限
        int upLimt = 10000;


        if (rowCount > (upLimt + 1)) {
            throw new ServiceException("单次导入上限" + upLimt + "，请分多次导入");
        }

        if (colCount != variables.size()) {
            throw new ServiceException("模板文件错误，请使用正确的模板文件");
        }

        //失败数据信息集合
        List<String> errorDataList = new ArrayList<>();
        //校验成功的数据
        List<Map<String, Object>> successDataList = new ArrayList<>();
        //读取所有行
        List<Map<String, Object>> workTaskRows = reader.readAll();

        if (workTaskRows.size() == 0) {
            throw new ServiceException("模板数据为空,请填充数据后导入");
        }
        Map<String, Object> firstLine = workTaskRows.get(0);
        for (String title : firstLine.keySet()) {
            if (!variables.contains(title)) {
                throw new ServiceException("模板文件错误，请使用正确的模板文件导入数据");
            }
        }


        for (int i = 0; i < workTaskRows.size(); i++) {
            Map<String, Object> row = workTaskRows.get(i);
            boolean isSuccess = true;
            //遍历变量
            for (String variable : variables) {
                Object value = row.get(variable);
                if (ObjectUtil.isEmpty(value)) {
                    errorDataList.add(StrUtil.format("第{}行'{}'数据为空", i + 1, variable));
                    isSuccess = false;
                    break;
                }
            }
            if (isSuccess) {
                //都验证通过
                successDataList.add(row);
            }
        }
        String verifyId = IdUtil.simpleUUID();
        if (ObjectUtil.isNotEmpty(successDataList)) {
            try {
                String key = LetterConstants.RK_LETTER_VERIFY_FILE + verifyId;
                redisService.deleteObject(key);
                redisService.setCacheList(key, successDataList);
                redisService.expire(key, 30L, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("Redis异常", e.getMessage());
                throw new ServiceException("服务连接超时,请联系管理员");
            } finally {
                if (tempFile != null && FileUtil.exist(tempFile)) {
                    FileUtil.del(tempFile);
                }
            }
        }

        Map<String, Object> param = new HashMap<>(3);
        //校验文件id
        param.put("verifyId", verifyId);
        //成功数量
        param.put("successNum", successDataList.size());
        //错误数据
        param.put("errorDataList", errorDataList);
        return param;
    }

    @Override
    public int updateById(DocumentMessage message) {
        message.setUpdateTime(DateUtils.getNowDate());
        return this.baseMapper.updateByPrimaryKeySelective(message);
    }


    @Override
    public void updateLetterMessageProce(List<Integer> ids) {
        System.err.println("更新批次状态");
        for (Integer id : ids) {
            DocumentMessage message = new DocumentMessage();
            message.setId(id);
            List<Map<String, Object>> dataList = this.baseMapper.countLetterProce(id);
            long total = this.baseMapper.selectCount(id);

            for (Map<String, Object> temp : dataList) {
                Long proce = (Long) temp.get("proce");
                Long num = (Long) temp.get("num");
                if (ProceEnum.TO_BE_REVIEWED.getCode() == proce) {
                    if (num == total) {
                        //待审核的数量等于总数量，就是整个批次都还没开始审核
                        message.setProce(ProceEnum.TO_BE_REVIEWED.getCode());
                        message.setStatus(ProceEnum.TO_BE_REVIEWED.getCode());
                    } else {
                        //否则就是审核中
                        message.setProce(ProceEnum.REVIEWED_ING.getCode());
                        message.setStatus(ProceEnum.REVIEWED_ING.getCode());
                    }
                }
                if(ProceEnum.REVIEWED_ING.getCode() == proce){
                    //审核中
                    message.setProce(ProceEnum.REVIEWED_ING.getCode());
                    message.setStatus(ProceEnum.REVIEWED_ING.getCode());
                }
                if (ProceEnum.END_REVIEW.getCode() == proce) {
                    if (num == total) {
                        //已审核的数量 等于总数量 ,就是 全部已审核完成
                        message.setProce(ProceEnum.END_REVIEW.getCode());
                        message.setStatus(ProceEnum.END_REVIEW.getCode());
                    }
                }
            }
            message.setUpdateTime(DateUtils.getNowDate());
            this.baseMapper.updateByPrimaryKeySelective(message);
        }
    }

    @Override
    public void createZipFile(List<Integer> ids, LoginUser loginUser) {
        for (Integer id : ids) {
            createZipFile(id,loginUser);
        }
    }

    @Async
    @Override
    public void createZipFile(Integer id,LoginUser loginUser) {
        //判断是否审核完成
        System.err.println("开始压缩");
        DocumentMessage documentMessage = this.baseMapper.selectByPrimaryKey(id);
        if (documentMessage == null) {
            return;
        }
        System.err.println("状态:" + documentMessage.getStatus());
        LetterDoc document = new LetterDoc();
        document.setLetterId(id);
//        document.setStatus(ApproveStatusEnum.PASS.getCode());
        List<LetterDoc> letters = iLetterDocService.selectList(document,loginUser);

        try {
            DocumentMessage temp = new DocumentMessage();
            temp.setId(documentMessage.getId());
            String fileUrl = iLetterDocService.exportSignPreviewUrl(null, letters,loginUser);
            temp.setZipFileUrl(fileUrl);
            this.baseMapper.updateByPrimaryKeySelective(temp);
        } catch (Exception e) {
            log.error("生成压缩包文件失败", e);
        }
    }

    @Override
    public Map<String, Object> verifyZipFile(DocumentMessage message) {
        File file = null;
        ZipReader zipReader = null;
        File tempFile = null;
        File file1 = null;
        String verifyId = null;
        String key = null;
        int invalidTotal = 0;
        int validTotal = 0;
        List<String> list = new ArrayList<>();
        try {
            //下载
            file = FileDownloadUtils.downloadTempFile(message.getSourceFileUrl());
            System.out.println(file.getAbsolutePath());

            //解压
            zipReader = ZipReader.of(file, Charset.forName("GBK"));
            FileUtil.mkdir(FileDownloadUtils.tempFilePath);
            tempFile = FileUtil.file(FileDownloadUtils.tempFilePath + "\\" + IdUtils.fastUUID());
            if (!tempFile.exists()) {
                tempFile.mkdir();
            }
            file1 = zipReader.readTo(tempFile);
            //取出全部文件
            List<File> loopFiles = FileUtil.loopFiles(file1);
            System.out.println("文件数量：" + loopFiles.size());
            for (File f : loopFiles) {
                //获取文件后缀名 不带“.”
                String extName = FileUtil.extName(f);
                if (!extName.toLowerCase().equals("pdf")) {
                    list.add(f.getName() + " 校验失败,文件格式错误");
                    invalidTotal = ++invalidTotal;
                    FileUtil.del(f);
                    continue;
                }
                validTotal = ++validTotal;
            }
            verifyId = IdUtils.fastUUID();
            if (validTotal > 0) {
                key = LetterConstants.RK_LETTER_VERIFY_FILE + verifyId;
                System.out.println("缓存key:" + key);
                redisService.setCacheObject(key, file1.getAbsolutePath());
                redisService.expire(key, 30L, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            if (e.getMessage().contains("MALFORMED")) {
                log.error("解析压缩包异常:" + e.getMessage());
                e.printStackTrace();
                throw new ServiceException("解析压缩包异常:被压缩文件或压缩包命名不支持中文");
            } else {
                log.error("解析压缩包异常:" + e.getMessage());
                e.printStackTrace();
                throw new ServiceException(e.getMessage());
            }
        } finally {
            if (zipReader != null) {
                zipReader.close();
            }
            if (FileUtil.exist(file)) {
                FileUtil.del(file);
            }
            /*if (file1!=null){FileUtil.clean(file1);}
            if (FileUtil.isEmpty(file1)){FileUtil.del(file1);}
            if (tempFile.exists()) { FileUtil.del(tempFile);}*/
        }
        Map<String, Object> param = new HashMap<>(3);
        //校验文件id
        param.put("verifyId", verifyId);
        //有效文件数量
        param.put("successNum", validTotal);
        //无效文件
        param.put("errorDataList", list);
        //创建定时任务删除解压文件
        if (StringUtils.isNotEmpty(key)) {
            createTask(key);
        }
        return param;
    }

    /**
     * 创建定时任务删除解压文件
     *
     * @param key
     */
    @Override
    public void createTask(String key) {
        if (key != null && redisService.hasKey(key)) {
            String id = IdUtil.fastUUID();
            //1分钟后开始定时任务
            String cron = DateToCronUtils.getCron(DateUtil.offsetMinute(new Date(), 1));
            CronUtil.schedule(id, cron, new Task() {
                @Override
                public void execute() {
                    Console.log("定时删除任务开始执行");
                    if (redisService.hasKey(key)) {
                        String cachePath = redisService.getCacheObject(key, String.class);
                        redisService.deleteObject(key);
                        //判断解压文件物理路径，文件夹是否还存在
                        if (FileUtil.exist(cachePath)) {
                            if (FileUtil.isDirectory(cachePath)) {
                                FileUtil.clean(cachePath);
                                FileUtil.del(cachePath);
                            } else {
                                FileUtil.del(cachePath);
                            }
                        }
                        CronUtil.remove(id);
                    } else {
                        CronUtil.remove(id);
                    }
                }
            });
        }
    }

    public static byte[] inputStream2byte(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buff = new byte[100];
        int rc = 0;
        while ((rc = inputStream.read(buff, 0, 100)) > 0) {
            byteArrayOutputStream.write(buff, 0, rc);
        }
        return byteArrayOutputStream.toByteArray();
    }


    private DocumentMessage createLetterMessage(DocumentMessage message, int size) {
        message.setQuantity(size);
        message.setStatus(ApproveStatusEnum.TO_BE_REVIEWED.getCode());
        message.setProce(ApproveStatusEnum.TO_BE_REVIEWED.getCode());
        message.setProceSort(0);
        message.setImportStatus(StatusEnum.IMPORT_ING.getCode());

        message.setCreateBy(TokenInformation.getUsername());
        message.setCreateById(TokenInformation.getUserid());
        message.setCreateTime(DateUtils.getNowDate());
        message.setDelFlag(BaseConstant.DelFlag_Being);
        message.setTenantId(TokenInformation.getCreateid());
        message.setUpdateBy(TokenInformation.getUsername());
        message.setUpdateById(TokenInformation.getCreateid());
        message.setUpdateTime(DateUtils.getNowDate());
        message.setSourceFileType(SourceTypeEnum.Zip_Type.getCode());
        this.baseMapper.insert(message);

        DocumentMessage DocumentMessage = new DocumentMessage();
        DocumentMessage.setSourceFileType(SourceTypeEnum.Zip_Type.getCode());
        DocumentMessage.setId(message.getId());

        return DocumentMessage;
    }


    /**
     * 上传pdf缓存数据
     *
     * @param file
     * @return url
     */
    private String uploadByFile(File file) {
        String previewUrl = null;
        String fileName = IdUtils.fastSimpleUUID() + ".pdf";
        try (InputStream inputStream = new FileInputStream(file)) {
            MultipartFile multipartFile = new MockMultipartFile(fileName, fileName,
                    ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
            R<SysFile> r = remoteFileService.upload(multipartFile);
            if (r.getCode() == R.SUCCESS) {
                previewUrl = r.getData().getUrl();
                System.out.println("上传pdf路径:" + previewUrl);
            } else {
                throw new ServiceException(r.getMsg());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            e.printStackTrace();
        }
        return previewUrl;
    }


    @Override
    public void addByZip(DocumentMessage message, String cachePath,LoginUser loginUser) {
        List<File> loopFiles = null;
        DocumentMessage DocumentMessage = null;
        try {
            loopFiles = PdfUtils.getFileList(cachePath);
            DocumentMessage = createLetterMessage(message, loopFiles.size());
            //Integer templateId = message.getTemplateId();
            for (int i = 0; i < loopFiles.size(); i++) {
                File file = loopFiles.get(i);
                int pdfPage = PdfUtils.getPdfPage(file);
                String previewUrl = uploadByFile(file);

                LetterDoc document = new LetterDoc();
                document.setLetterId(DocumentMessage.getId());
                document.setStatus(ApproveStatusEnum.TO_BE_REVIEWED.getCode());
                document.setSerialNo(createSerialNo(DocumentMessage.getId(), i + 1));
                document.setProce(ProceEnum.TO_BE_REVIEWED.getCode());
                document.setProceSort(0);
                document.setPreviewPages(pdfPage);
                document.setPreviewUrl(previewUrl);
                document.setSourceFileType(SourceTypeEnum.Zip_Type.getCode());
                document.setMailingStatus(ExpressApiConstants.state_0);
                iLetterDocService.save(document,loginUser);
            }
        } catch (Exception e) {
            DocumentMessage.setImportStatus(StatusEnum.IMPORT_FAIL.getCode());
            this.baseMapper.updateByPrimaryKeySelective(DocumentMessage);
            log.info(e.getMessage());
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        } finally {
            if (FileUtil.exist(cachePath)) {
                FileUtil.clean(cachePath);
                FileUtil.del(cachePath);
            }
        }
        if (DocumentMessage != null) {
            DocumentMessage.setImportStatus(StatusEnum.IMPORT_SUCCESS.getCode());
            this.baseMapper.updateByPrimaryKeySelective(DocumentMessage);
        }
    }


    /**
     * 生成函件函件单号
     *
     * @param LetterMessageId
     * @param order
     * @return
     */
    private String createSerialNo(Integer LetterMessageId, int order) {
        //规则：(年份)zws+4位批次ID+该批次数据排序从001开始+四位随机码，如：（2022）zws2179-0001-5872；
        int year = DateUtil.year(DateUtils.getNowDate());
        String messageIdStr = NumberUtil.decimalFormat("0000", LetterMessageId);
        String orderStr = NumberUtil.decimalFormat("000", order);
        int c = RandomUtil.randomInt(1000, 10000);
        return StrUtil.format("({})zws-ws{}-{}-{}", year, messageIdStr, orderStr, c);
    }
}
