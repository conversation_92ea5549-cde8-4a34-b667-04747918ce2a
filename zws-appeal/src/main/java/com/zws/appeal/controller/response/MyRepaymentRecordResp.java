package com.zws.appeal.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 我的审批（回款）查询返回实体类
 */
@Data
public class MyRepaymentRecordResp implements Comparable<MyRepaymentRecordResp> {

    /**
     * 该案件是否属于本团队(0-不属于,1-属于)
     */
    private Integer button;

    /**
     * 案件id
     */
    private Long caseId;

    /**
     * 申请表id
     */
    private Long applyIds;

    /**
     * 申请人
     */
    private String registrar;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 还款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date repaymentDate;

    /**
     * 还款金额
     */
    private BigDecimal repaymentMoney;

    /**
     * 还款方式
     */
    private String repaymentMode;

    /**
     * 还款类型（部分还款、结清还款）
     */
    private String repaymentType;

    /**
     * 审核状态
     */
    private String examineState;

    /**
     * 审核时间-最后审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 审核人-最后审核人
     */
    private String updateBy;

    /**
     * 还款凭证
     */
    private String repaymentProof;

    /**
     * 审核数据历史表id
     */
    private Long approveId;

    /**
     * 申请id,根据审核类型查找不同的申请表
     */
    private Long applyId;

    /**
     * 处理状态,0-已同意，1-未同意，2-待处理
     */
    private Integer approveStart;

//    /**
//     * 审核时间(历史记录表)
//     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date approveTime;

//    /**
//     * 审核人(历史记录表)
//     */
//    private String reviewer;

    /**
     * 案件状态 0-未分配，1-已分配，2-停催，3-留案，4-退案 5-回收案件，6-案件结清
     */
    private String caseState;

    /**
     * 委案批号
     */
    private String entrustingCaseBatchNum;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 客户姓名
     */
    private String clientName;

    /**
     * 客户身份证号
     */
    private String clientIdcard;

    /**
     * 证件类型
     */
    private String clientIdType;

    /**
     * 客户户籍
     */
    private String clientCensusRegister;

    /**
     * 委托金额
     */
    private BigDecimal clientMoney;

    /**
     * 标签内容
     */
    private String labelContent;

    /**
     * 标签表id
     */
    private Integer labelId;

    /**
     * 标签颜色（0-黑色，1-粉色，2-橙色，3-绿色，4-蓝色，5-紫色，6-黄色）
     */
    private Integer label;

    @Override
    public int compareTo(@NotNull MyRepaymentRecordResp o) {
        return (int) (o.getCreateTime().getTime() - this.getCreateTime().getTime());
    }
}
