package com.zws.appeal.controller.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 更新SIP密码 提交表单
 *
 * <AUTHOR>
 * @date ：Created in 2022/8/25 10:49
 */
@Data
public class UpdateSipPwdFormRequest {

    /**
     * 选择的员工id
     */
    @NotEmpty(message = "请选择员工账号")
    private List<Integer> employeeIds;
    /**
     * 修改后的SIP密码
     */
    @NotEmpty(message = "密码不能为空")
    @Size(min = 5, max = 10, message = "密码长度不能小于5大于10")
    private String sipPassword;

}
