package com.zws.appeal.controller;

import com.pingan.radosgw.sdk.client.OBSClient;
import com.pingan.radosgw.sdk.client.OBSClientBuilder;
import com.pingan.radosgw.sdk.service.s3.model.PutObjectResult;
import com.zws.appeal.config.ObsConfig;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.appeal.config.MultipartConfig;
import com.zws.appeal.domain.Files;
import com.zws.appeal.service.TeamService;
import com.zws.appeal.utils.SaasFileDownloadUtils;
import com.zws.appeal.utils.SaasFileUpload;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.*;

/**
 * 上传Controller
 *
 * <AUTHOR>
 * @date 2022-08-12
 */

@CrossOrigin   //注解跨域
@RestController
@RequestMapping(value = "/file")
public class ShangController {

    @Autowired
    private TeamService teamService;
    @Autowired
    private ObsConfig obsConfig;
    @Autowired
    private MultipartConfig multipartConfig;


    @ApiOperation(value = "删除文件")
    @DeleteMapping("/minioDelete/{fileName}")
    public AjaxResult remove(@PathVariable("fileName") String fileName) {
        try {
            OBSClient obsClient = new OBSClientBuilder().build(obsConfig.getEndpoint(), obsConfig.getAk(), obsConfig.getSk(), obsConfig.getUserAgent());
            obsClient.deleteObject(obsConfig.getBucketName(), fileName);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("删除失败");
        }
        return AjaxResult.success("删除成功");
    }

    /**
     * 根据团队id查询该团队凭证文件信息
     *
     * @return
     */
    @GetMapping("/select")
    public AjaxResult selectListWithFiles(int createId) {
        List<Files> files = teamService.findFiles(createId);
        return AjaxResult.success("查询成功", files);
    }

    /**
     * 查看文件大小
     *
     * @param fileS
     * @returna
     */
    private static String formatFileSize(long fileS) {
        DecimalFormat df = new DecimalFormat("#.00");
        String fileSizeString = "";
        String wrongSize = "0B";
        if (fileS == 0) {
            return wrongSize;
        }
        if (fileS < 1024) {
            fileSizeString = df.format((double) fileS) + " B";
        } else if (fileS < 1048576) {
            fileSizeString = df.format((double) (fileS >> 10)) + " KB";
        } else if (fileS < 1073741824) {
            fileSizeString = df.format((double) (fileS >> 20)) + " MB";
        } else if (fileS > 1073741824) {
            fileSizeString = df.format((double) (fileS >> 30)) + " GB";
        }
        return fileSizeString;
    }

    /**
     * 根据团队id删除该团队凭证文件信息（假删除）
     *
     * @return
     */
    @PutMapping("/delete/{id}")
    public AjaxResult removeFiles(@RequestBody @PathVariable("id") int id) {
        teamService.deleteFiles(id);
        return AjaxResult.success("删除成功");
    }


    @ApiOperation("文件上传（多）")
    @PostMapping("/minioUpload")
    public AjaxResult upload(@RequestBody MultipartFile[] file) {  //多文件上传
        if (file == null || file.length == 0) {
            return AjaxResult.error("上传文件不能为空");
        }
        List<String> orgFileNameList = new ArrayList<>(file.length);
        List<String> listToString = new ArrayList<>(file.length);
        List<String> url = new ArrayList<>(file.length);
        for (MultipartFile multipartFile : file) {
            String orgfileName = multipartFile.getOriginalFilename();  //获得文件的原始名称；
            orgFileNameList.add(orgfileName);
//            文件后缀截取
            String substring = orgfileName.substring(orgfileName.lastIndexOf("."));
//            文件格式以及大小限制验证
            SaasFileUpload.update(multipartFile, multipartConfig.getList(), multipartConfig.getMax_file_size());

            Date date = new Date();
//            日期路径 即年/月/日 如2018/08/08
            String format = DateFormatUtils.format(date, "yyyy/MM/dd");
            String toString = UUID.randomUUID().toString();
//            单独文件夹
            String fileName = "credentials";
//            随机生成新的文件名
            String subToString = toString + substring;
            listToString.add(subToString);
//            生成新的路径名
            String surl = fileName + "/" + format + "/" + subToString;

            String endpoint = obsConfig.getEndpoint();
            String bucketName = obsConfig.getBucketName();

            InputStream in = null;
            try { //文件上传
                in = multipartFile.getInputStream();
                OBSClient obsClient = new OBSClientBuilder().build(endpoint, obsConfig.getAk(), obsConfig.getSk(), obsConfig.getUserAgent());
                PutObjectResult putObjectResult = obsClient.putObject(bucketName, surl, in);
                System.out.println(putObjectResult.getETag());
                url.add(obsClient.getSignedUrl(obsConfig.getBucketName(), surl, "",99*60*60*24*365L));
            } catch (Exception e) {
                return AjaxResult.error("文件上传失败");
            } finally {
                try {
                    if (in != null) {
                        in.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        Map<String, Object> map = new HashMap<String, Object>();
//        文件原名称
        map.put("firstName", orgFileNameList);
//        修改后的文件名称
        map.put("modifyName", listToString);
//        文件url路径
        map.put("fileUrl", url);
        return AjaxResult.success("文件上传成功", map);
    }

    /**
     * 下载分案模板
     *
     * @return
     */
    @GetMapping("/allocationTemplate")
    public void downloadAllocationTemplate(HttpServletResponse response, int id) throws Exception {
        Files filesId = teamService.findFilesId(id);
//        文件原名称
        String fileName = filesId.getFirstName();
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
        File tempFile = SaasFileDownloadUtils.downloadTempFile(filesId.getFileUrl());
//设置content-disposition响应头控制浏览器以下载的形式打开文件
//        response.setHeader("content-disposition", "attachment;filename=" + fileName);
        //获取要下载的文件输入流
        InputStream in = new FileInputStream(tempFile);
        int len = 0;
        //创建数据缓冲区
        byte[] buffer = new byte[1024 * 1024];
        //通过response对象获取OutputStream流
        OutputStream out = response.getOutputStream();
        //将FileInputStream流写入到buffer缓冲区
        while ((len = in.read(buffer)) > 0) {
            //使用OutputStream将缓冲区的数据输出到客户端浏览器
            out.write(buffer, 0, len);
        }
        in.close();
        out.close();
    }


}
