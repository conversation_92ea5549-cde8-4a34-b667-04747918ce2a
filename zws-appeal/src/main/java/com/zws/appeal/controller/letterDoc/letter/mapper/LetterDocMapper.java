package com.zws.appeal.controller.letterDoc.letter.mapper;

import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;
import com.zws.appeal.controller.letterDoc.letter.domain.RetrievalFileSeparate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LetterDocMapper {


    /**
     * 函件id 查询函件批次id集合
     * @param ids
     * @return
     */
    List<Integer> selectLetterIdByIds(@Param("ids") List<Long> ids);


    /**
     * 根据随机Id查询函件
     * @param randomId
     * @return
     */
    LetterDoc selectByRandomId(String randomId);
    /**
     * 查询简单列表集合(联查) 不包含数据、url
     * @param letter
     * @return
     */
    List<LetterDoc> selectSimpleList(LetterDoc letter);

    int deleteByPrimaryKey(Long id);

    int insert(LetterDoc record);

    LetterDoc selectByPrimaryKey(Long id);

    LetterDoc selectByCaseId(Long id);

    List<Long> getTemplateId(Long id);

    /**
     * 查询函件的使用模板的id
     * @param id
     * @return
     */
    Integer getTemplateIdById(Long id);

    LetterDoc getPreviewUrl(@Param("id") Long id,@Param("templateId") Long templateId);

    int updateByPrimaryKeySelective(LetterDoc record);

    /**
     * 查询列表
     * @param record
     * @return
     */
    List<LetterDoc>  selectList(LetterDoc record);

    /**
     * 获取材料
     * @param caseId
     * @param code
     * @return
     */
    List<LetterDoc> getLetterDoc(@Param("caseId") Long caseId, @Param("code") Integer code);

    /**
     * 获取文书
     * @param caseId
     * @param code
     * @return
     */
    List<LetterDoc> getLetterDocs(@Param("caseId") Long caseId, @Param("code") Integer code);

    /**
     * 根据案件id查询案件身份证号码
     * @param caseId
     * @return
     */
    String selectDataManagementByCaseId(Long caseId);

    /**
     * 获取档案资料列表
     *
     * @param clientIdcard
     * @return
     */
    List<RetrievalFileSeparate> getArchivalData(String clientIdcard);

    /**
     * 获取全部文书
     * @return
     */
    List<LetterDoc> selectAllList();

    LetterDoc selectByPreviewUrl(String previewUrl);
}
