package com.zws.appeal.controller.letterDoc.letter;

import java.io.File;

public class FileUtils {

    /**
     * 删除临时文件
     * @param tempFile
     */
    public static void deletedTempFile(File tempFile){
        if (tempFile != null) {
            if(tempFile.exists()){
                tempFile.delete();
                System.out.println("成功-删除文件："+tempFile.getPath());
                tempFile = null;
            }else{
                System.out.println("失败-删除文件："+tempFile.getPath());
            }
            tempFile = null;
        }
    }
}
