package com.zws.appeal.controller.letterDoc.letter;

import org.apache.commons.codec.binary.Base64;
import org.apache.http.entity.ContentType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

@Component
public class PDFFileUtils {

    /**
     * 方法名:          PDFToBase64
     * 方法功能描述:     将pdf文件转换为Base64编码
     *
     * @param: String sourceFile:源文件路径
     * @return: String  base64字符串
     * @Author: zhouhu
     * @Create Date:   2019年07月03日
     */
    public static String PDFToBase64(File file) {
        BASE64Encoder encoder = new BASE64Encoder();
        FileInputStream fin = null;
        BufferedInputStream bin = null;
        ByteArrayOutputStream baos = null;
        BufferedOutputStream bout = null;

        try {
//            fin = new FileInputStream(file);
            URL url = new URL(file.getPath());
            URLConnection connection = url.openConnection();
            InputStream imgIn = connection.getInputStream();
            bin = new BufferedInputStream(fin);
            baos = new ByteArrayOutputStream();
            bout = new BufferedOutputStream(baos);
            byte[] buffer = new byte[1024];
            int len = bin.read(buffer);
            while (len != -1) {
                bout.write(buffer, 0, len);
                len = bin.read(buffer);
            }
            //刷新此输出流并强制写出所有缓冲的输出字节
            bout.flush();
            byte[] bytes = baos.toByteArray();
            return encoder.encodeBuffer(bytes).trim();

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                fin.close();
                bin.close();
                bout.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    static BASE64Encoder encoder = new sun.misc.BASE64Encoder();

    //pdf转base64
    public static String getPDFBinary(File file) {
        FileInputStream fin = null;
        BufferedInputStream bin = null;
        ByteArrayOutputStream baos = null;
        BufferedOutputStream bout = null;
        try {
            // 建立读取文件的文件输出流
            fin = new FileInputStream(file);
            // 在文件输出流上安装节点流（更大效率读取）
            bin = new BufferedInputStream(fin);
            // 创建一个新的 byte 数组输出流，它具有指定大小的缓冲区容量
            baos = new ByteArrayOutputStream();
            // 创建一个新的缓冲输出流，以将数据写入指定的底层输出流
            bout = new BufferedOutputStream(baos);
            byte[] buffer = new byte[1024];
            int len = bin.read(buffer);
            while (len != -1) {
                bout.write(buffer, 0, len);
                len = bin.read(buffer);
            }
            // 刷新此输出流并强制写出所有缓冲的输出字节，必须这行代码，否则有可能有问题
            bout.flush();
            byte[] bytes = baos.toByteArray();
            // sun公司的API
            String trim = encoder.encodeBuffer(bytes).trim();
            return trim.replaceAll("\\r","").replaceAll("\\n","");
            // apache公司的API
//             return Base64.encodeBase64String(bytes);

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                fin.close();
                bin.close();
                // 关闭 ByteArrayOutputStream 无效。此类中的方法在关闭此流后仍可被调用，而不会产生任何 IOException
                // IOException
                // baos.close();
                bout.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    /**
     * base64转文件
     *
     * @param base64Code
     * @return
     * @throws Exception
     */
    public static MultipartFile decoderBase64File(String base64Code, String name)
            throws Exception {
        byte[] buffer = new BASE64Decoder().decodeBuffer(base64Code);
        InputStream inputStream = new ByteArrayInputStream(buffer);
        MultipartFile file = new MockMultipartFile(ContentType.APPLICATION_OCTET_STREAM.toString(), name, "", inputStream);
        return file;
    }

    /**
     * 将图片转换成Base64编码
     * @param imgFile 待处理图片地址
     * @return
     */
    public static String getImgBase(String imgFile) {

        // 将图片文件转化为二进制流
        InputStream in = null;
        byte[] data = null;
        // 读取图片字节数组
        try {
//            in = new FileInputStream(imgFile);
            URL url = new URL(imgFile);
            URLConnection connection = url.openConnection();
            InputStream imgIn = connection.getInputStream();
            data = new byte[imgIn.available()];
            imgIn.read(data);
            imgIn.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 图片头
        //String imghead = "data:image/jpeg;base64,";
        return Base64.encodeBase64String(data);
    }

    /**
     * 本地图片转换Base64的方法
     * @param imgPath
     */
    public static String ImageToBase64(String imgPath) {
        InputStream in = null;
        byte[] data = null;
        // 读取图片字节数组
        try {
            in = new FileInputStream(imgPath);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 对字节数组Base64编码
        BASE64Encoder encoder = new BASE64Encoder();
        // 返回Base64编码过的字节数组字符串
        return encoder.encode(data);
    }

    //base64字符串转化成图片
    /**
     *
     * @param imgStr base64编码字符串 （必传）
     * @param userid 用户的userid （可传可不传）
     * @param type   转换用于什么（可传可不传）
     * @return
     */
    public static String Base64GetImg(String imgStr,Integer userid,String type)
    {   //对字节数组字符串进行Base64解码并生成图片
        if (imgStr == null) //图像数据为空
        {
            return "图像数据为空!";
        }
        BASE64Decoder decoder = new BASE64Decoder();
        try
        {
            //Base64解码
            //byte[] b = decoder.decodeBuffer(imgStr);
            byte[] b = null;
            if (imgStr.indexOf("data:image/jpeg;base64,") != -1) {
                b = decoder.decodeBuffer(imgStr.replaceAll("data:image/jpeg;base64,", ""));
            } else {
                if (imgStr.indexOf("data:image/png;base64,") != -1) {
                    b = decoder.decodeBuffer(imgStr.replaceAll("data:image/png;base64,", ""));
                } else {
                    b = decoder.decodeBuffer(imgStr.replaceAll("data:image/jpg;base64,", ""));
                }
            }
            for(int i=0;i<b.length;++i)
            {
                if(b[i]<0)
                {//调整异常数据
                    b[i]+=256;
                }
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");// 设置日期格式
            String format = df.format(new Date());
            File file = new File("F:\\ceshi\\"+type);
            if (!file.exists()) { // 如果文件夹不存在
                file.mkdir(); // 创建文件夹
            }
            //生成jpeg图片
            String name = UUID.randomUUID().toString().substring(4, 20);
            String imgFilePath = "F:\\ceshi\\"+type+"\\"+format+userid+"-"+name+".jpg";//新生成的图片
            OutputStream out = new FileOutputStream(imgFilePath);
            out.write(b);
            out.flush();
            out.close();
            return imgFilePath;
        }
        catch (Exception e)
        {
            return "图像数据转换失败";
        }
    }

    public static String pdf(String imgFilePath){
        String[] res = imgFilePath.split("\\.");
        String pos = res[res.length - 1];
        byte[] data = null;
        // 读取图片字节数组
        try {
//            InputStream in = new FileInputStream(imgFilePath);
            URL url = new URL(imgFilePath);
            URLConnection connection = url.openConnection();
            InputStream imgIn = connection.getInputStream();
            data = new byte[imgIn.available()];
            imgIn.read(data);
            imgIn.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        String data1 = Base64Utils.encodeToString(data);
        System.out.println(data1);
        return data1;
    }

}
