package com.zws.appeal.controller;

import com.zws.common.core.domain.R;
import com.zws.system.api.RemoteDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * system 模块远程调用
 *
 * <AUTHOR>
 * @date 2024/5/8 19:26
 */
@CrossOrigin
@RestController
@RequestMapping(value = "/system")
public class SystemController {

    @Autowired
    private RemoteDictService remoteDictService;

    /**
     * 获取system模块的字典数据
     * @param dictType
     * @return
     */
    @GetMapping(value = "/dict/data/type/{dictType}")
    public R dictType(@PathVariable("dictType") String dictType){
        System.out.println("dictType:"+dictType);
        return remoteDictService.dictType(dictType);
    }


}
