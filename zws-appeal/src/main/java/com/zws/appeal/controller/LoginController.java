package com.zws.appeal.controller;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.constant.UserConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.SmsPhoneAndMsg;
import com.zws.common.core.exception.CaptchaException;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.IdUtils;
import com.zws.common.core.utils.JwtUtils;
import com.zws.common.core.utils.RsaUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.common.security.auth.AuthUtil;
import com.zws.common.security.service.TokenService;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.agservice.AgLoginService;
import com.zws.appeal.domain.InfoContact;
import com.zws.appeal.domain.log.TeamOperLog;
import com.zws.appeal.domain.record.UrgeRecord;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.enums.OperationTypeEnum;
import com.zws.appeal.pojo.EnumerationPojo;
import com.zws.appeal.pojo.ExportPdfPojo;
import com.zws.appeal.pojo.VisitingEntityPojo;
import com.zws.appeal.pojo.vo.SmsLoginVo;
import com.zws.appeal.service.AsyncLoggerService;
import com.zws.appeal.service.MenuService;
import com.zws.appeal.service.SettingsService;
import com.zws.appeal.utils.Log;
import com.zws.appeal.utils.LoginVo;
import com.zws.appeal.utils.SplitUtils;
import com.zws.appeal.utils.TokenInformation;
import com.zws.system.api.RemoteCaseService;
import com.zws.system.api.domain.SysUser;
import com.zws.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 登录管理
 *
 * <AUTHOR>
 * @date 2022-08-12
 */

@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/sign")
public class LoginController extends BaseController {

    @Autowired
    private AgLoginService agLoginService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private AsyncLoggerService asyncLoggerService;
    @Autowired
    private MenuService menuService;
    @Autowired
    private SettingsService settingsService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private RemoteCaseService remoteCaseService;


    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @RequiresLogin
    @GetMapping("getInfo")
    public AjaxResult info() {
        Integer userid = TokenInformation.getUserid();
        Set<String> menuPermission = agLoginService.getMenuPermission(userid.longValue());
        AjaxResult ajax = AjaxResult.success();
        ajax.put("permissions", menuPermission);
        return ajax;
    }

    /**
     * 根据登录用户查询修改密码时间（判断是否已90天未修改密码）
     */
    @RequiresLogin
    @RequestMapping(value = "/selectPasswordTime", method = RequestMethod.GET)
    public AjaxResult infoWithPasswordTime() {
        Map<String, Object> map = agLoginService.selectPasswordTime();
        return AjaxResult.success("操作成功", map);
    }

    /**
     * 根据用户id查询操作日志
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectTeamOperLog", method = RequestMethod.GET)
    public TableDataInfo selectListWithTeamOperLog(TeamOperLog teamOperLog) {
        String replaceName = SplitUtils.handleString(teamOperLog.getOperName());
        teamOperLog.setOperName(replaceName);
        startPage();
        List<TeamOperLog> teamOperLogs = asyncLoggerService.selectTeamOperLog(teamOperLog);
        return getDataTable(teamOperLogs);
    }

    /**
     * 操作类型枚举查询
     *
     * @return
     */
    @RequestMapping(value = "/operationType", method = RequestMethod.GET)
    public AjaxResult selectListWithOperationType() {
        OperationTypeEnum[] values = OperationTypeEnum.values();
        List<EnumerationPojo> list = new ArrayList<>();
        for (OperationTypeEnum row : values) {
            EnumerationPojo statePojo = new EnumerationPojo(row.getCode(), row.getInfo());
            list.add(statePojo);
        }
        return AjaxResult.success(list);
    }

    /**
     * 查询是否是每天第一次登录
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectComplianceAdvocacy", method = RequestMethod.GET)
    public AjaxResult selectMapWithComplianceAdvocacy() {
        Map<String, Object> map = agLoginService.complianceAdvocacy();
        return AjaxResult.success("查询成功", map);
    }

    /**
     * 获取验证码
     */
    @RequestMapping(value = "/code", method = RequestMethod.GET)
    public AjaxResult code() throws IOException {
        AjaxResult capcha = agLoginService.createCapcha();
        return capcha;
    }

    /**
     * 登录账户验证
     */
//    @PassToken
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public R<?> loginVo(@RequestBody LoginVo loginVo) {

        try {
            //        校验验证码
            agLoginService.checkCapcha(loginVo.getCode(), loginVo.getUuid());
        } catch (CaptchaException e) {
            return R.fail(e.getMessage());
        }

        String usernameP = loginVo.getUsername();
        String passwordP = loginVo.getPassword();
        String username = RsaUtils.decryptByPrivateKey(usernameP);
        String password = RsaUtils.decryptByPrivateKey(passwordP);
        LoginUser loginUser = agLoginService.LoginUser(username, password, loginVo.getInternalIp(), loginVo.getBrowserFingerprint(),loginVo.getTeamLevelType());
        Map<String, Object> tokenMap = new HashMap<>();
        Map<String, Object> accountInfo = loginUser.getAccountInfo();
        Integer teamId = (Integer) accountInfo.get(UserConstants.TEAM_ID);
        Integer status = settingsService.getSecurityVerificationStatus(teamId);
        tokenMap.put("securityVerification", status);

        //判断是否需要双重验证
        if (status != null && status == 0) {
            //开启双重验证
            SysUser sysUser = loginUser.getSysUser();
            String phonenumber = sysUser.getPhonenumber();
            String smsUid = IdUtils.simpleUUID();
            tokenMap.put("phonenumber", DesensitizedUtil.mobilePhone(phonenumber));
            tokenMap.put("smsUid", smsUid);
            redisService.setCacheObject(UserConstants.SECURITY_VERIFICATION_UID + smsUid, loginUser, 15L, TimeUnit.MINUTES);
        } else {
            tokenMap = tokenService.createToken(loginUser);
        }
        //多端登录限制
        loginUser.getAccountInfo().put(UserConstants.ACCOUNT_TERMINAL, UserConstants.ACCOUNT_TERMINAL_SAAS);
        return R.ok(tokenMap);
    }

    /**
     * 发送短信验证码
     *
     * @param smsLoginVo
     * @return
     */
    @PostMapping("/sendSmsCode")
    public R<?> sendSmsCode(@RequestBody SmsLoginVo smsLoginVo) {
        String smsUid = smsLoginVo.getSmsUid();
        if (!redisService.hasKey(UserConstants.SECURITY_VERIFICATION_UID + smsUid)) {
            throw new ServiceException("请重新输入用户名密码登录后再试");
        }
        LoginUser userInfo = redisService.getCacheObject(UserConstants.SECURITY_VERIFICATION_UID + smsUid, LoginUser.class);
        if (userInfo == null) {
            throw new ServiceException("请重新输入用户名密码登录后再试");
        }
        SysUser sysUser = userInfo.getSysUser();
        String phonenumber = sysUser.getPhonenumber();
        //生成随机验证码
        String smsCode = StrUtil.toString(RandomUtil.randomInt(100000, 999999));

        //TODO 发送短信
        SmsPhoneAndMsg smsPhoneAndMsg = new SmsPhoneAndMsg();
        smsPhoneAndMsg.setPhone(phonenumber);
        smsPhoneAndMsg.setMsg(smsCode);
        smsPhoneAndMsg.setName("催收管理系统");
        smsPhoneAndMsg.setLoginAccount(sysUser.getUserName());
        try {
            R r = remoteCaseService.sendSmsCommission(smsPhoneAndMsg, SecurityConstants.INNER);
            if (r.getCode() != 200) {
                return R.fail(r.getMsg());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new GlobalException("短信调用发送失败，请联系管理员");
        }
        redisService.setCacheObject(UserConstants.SECURITY_VERIFICATION_SMS_CODE + smsUid, smsCode, 3L, TimeUnit.MINUTES);

        Map<String, Object> smsMap = new HashMap<>();
        smsMap.put("uid", smsUid);
        return R.ok(smsMap);
    }

    /**
     * 短信验证码登录
     *
     * @param smsLoginVo
     * @return
     */
    @PostMapping("/smsLogin")
    public R<?> smsLogin(@RequestBody SmsLoginVo smsLoginVo) {
        String uid = smsLoginVo.getUid();
        String code = smsLoginVo.getCode();
        String smsCode = redisService.getCacheObject(UserConstants.SECURITY_VERIFICATION_SMS_CODE + uid, String.class);
        if (!StringUtils.equals(smsCode, code)) {
            return R.fail("验证码错误");
        }
        LoginUser userInfo = redisService.getCacheObject(UserConstants.SECURITY_VERIFICATION_UID + uid, LoginUser.class);
        redisService.deleteObject(UserConstants.SECURITY_VERIFICATION_SMS_CODE + uid);
        redisService.deleteObject(UserConstants.SECURITY_VERIFICATION_UID + uid);
        return R.ok(tokenService.createToken(userInfo));
    }




    /**
     * 退出登录
     *
     * @param request
     * @return
     */
//    @Log(title = "退出登录", businessType = BusinessType.FORCE)
    @DeleteMapping("/logout")
    public R<?> logout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            agLoginService.logout(username);
        }
        return R.ok();
    }

    /**
     * 刷新令牌有效期
     *
     * @param request
     * @return
     */
    @PostMapping("/refresh")
    public R<?> refresh(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    /**
     * 下载外访单
     *
     * @param
     * @param request
     * @param response
     */
    @Log(title = "催收管理-下载外访单", businessType = BusinessType.EXPORT)
    @RequiresLogin
    @PostMapping("/exportPDF")
    public void exportPDF(@RequestBody ExportPdfPojo exportPdfPojo, HttpServletRequest request, HttpServletResponse response) {
        VisitingEntityPojo visitingEntityPojo = agLoginService.exportPDF(exportPdfPojo.getCaseId(), exportPdfPojo.getId());
        Document document = new Document();
        try {
            response.setContentType("application/pdf");
            response.addHeader("Content-Disposition", "attachment;filename=" +
                    new String(("外访单" + ".pdf").getBytes(), "iso-8859-1"));

            PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
            document.open();

            List<String> list = new ArrayList<>();
            if (!ObjectUtils.isEmpty(visitingEntityPojo.getInfoContact())) {
                for (InfoContact infoContact : visitingEntityPojo.getInfoContact()) {  //联系人信息
                    String contactName = infoContact.getContactName() == null ? "" : infoContact.getContactName();
                    String contactRelation = infoContact.getContactRelation() == null ? "" : infoContact.getContactRelation();
                    String contactPhone = infoContact.getContactPhone() == null ? "" : infoContact.getContactPhone();
                    String pengy =
                            "<tr>" +
                                    "<td style=\"text-align: left; width: 110px;\">" + contactName + "</td>" +
                                    "<td style=\"text-align: left;\">" + contactRelation + "</td>" +
                                    "<td style=\"text-align: left;\">" + contactPhone + "</td>" +
                                    "</tr>";
                    list.add(pengy);
                }
            }
            String contactInformation = null;
            if (!ObjectUtils.isEmpty(list)) {
                for (String lists : list) {
                    contactInformation = contactInformation + lists;
                }
            }

            List<String> lists = new ArrayList<>();
            if (!ObjectUtils.isEmpty(visitingEntityPojo.getUrgeRecords())) {
                for (UrgeRecord urgeRecord : visitingEntityPojo.getUrgeRecords()) {   //催收记录信息
                    Date createTime = urgeRecord.getCreateTime();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String format = simpleDateFormat.format(createTime);
                    String liaison = urgeRecord.getLiaison() == null ? "" : urgeRecord.getLiaison();
                    String contactMode = urgeRecord.getContactMode() == null ? "" : urgeRecord.getContactMode();
                    String odvName = urgeRecord.getOdvName() == null ? "" : urgeRecord.getOdvName();
                    String content = urgeRecord.getContent() == null ? "" : urgeRecord.getContent();
                    String str =
                            "<tr>" +
                                    "<td style=\"text-align: left; width: 110px;\">" + format + "</td>" +
                                    "<td style=\"text-align: left; width: 110px;\">" + liaison + "</td>" +
                                    "<td style=\"text-align: left; width: 110px;\">" + contactMode + "</td>" +
                                    "<td style=\"text-align: left; width: 110px;\">" + odvName + "</td>" +
                                    "<td style=\"text-align: left; width: 110px;\">" + content + "</td>" +
                                    "</tr>";
                    lists.add(str);
                }
            }
            String collectionRecord = null;
            if (!ObjectUtils.isEmpty(lists)) {
                for (String list1 : lists) {
                    collectionRecord = collectionRecord + list1;
                }
            } else {
                collectionRecord = "<tr>" +
                        "<td style=\"text-align: left; width: 110px;\">" + " " + "</td>" +
                        "<td style=\"text-align: left; width: 110px;\">" + " " + "</td>" +
                        "<td style=\"text-align: left; width: 110px;\">" + " " + "</td>" +
                        "<td style=\"text-align: left; width: 110px;\">" + " " + "</td>" +
                        "<td style=\"text-align: left; width: 110px;\">" + " " + "</td>" +
                        "</tr>";
            }
            Date date = new Date();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String format = simpleDateFormat.format(date);
            //在下面，body中设置了style，设置了默认字体为宋体，这样导出时的html语言就默认带有了字体，汉字才会导出成功
            String content = "<html><body style=\"font-family: 宋体, SimHei; text-align: center;\">" +
//                    "<div style=\"width:800px; margin: 10px auto;padding: 15px;border: 1px solid #000;\">" +
                    "<h1>外访单</h1>" +
                    "<hr style=\"background-color: black;\"/>" +
                    "<table style=\"width: 100%;\">" +
                    "<tr>" +
                    "<td style=\"text-align: left; width: 110px;\">外访日期：" + visitingEntityPojo.getOutsideStart() + "</td>" +
                    "<td style=\"text-align: left; width: 110px;\">外访单领取签字：</td>" +
                    "</tr>" +
                    "<tr>" +
                    "<td style=\"text-align: left; width: 300px;\">外访人员：" + visitingEntityPojo.getOdvNames() + "</td>" +
                    "<td style=\"text-align: left; width: 110px;\">外访单回收签字：</td>" +
                    "</tr>" +
                    "</table>" +

                    "<hr/>" +
                    "<p style=\"text-align: left;padding-left: 30px;\"><b>案件信息</b></p>" +
                    "<hr/>" +
                    "<table style=\"width: 100%;\">" +
                    "<tr>" +
                    "<td style=\"text-align: left; width: 250px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; align-items:center;\">合同编号：" + visitingEntityPojo.getContractNo() + "</td>" +
                    "<td style=\"text-align: left; width: 250px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; align-items:center;\">资产方：" + visitingEntityPojo.getEntrustingPartyName() + "</td>" +
                    "<td style=\"text-align: left; width: 250px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; align-items:center;\">产品名称：" + visitingEntityPojo.getProductName() + "</td>" +
                    "</tr>" +
                    "<tr>" +
                    "<td style=\"text-align: left; width: 250px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; align-items:center;\">" + "客户姓名：" + visitingEntityPojo.getClientName() + "</td>" +
                    "<td style=\"text-align: left; width: 250px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; align-items:center;\">" + "性别：" + visitingEntityPojo.getClientSex() + "</td>" +
                    "<td style=\"text-align: left; width: 250px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; align-items:center;\">" + "催收员：" + visitingEntityPojo.getOdvName() + "</td>" +
                    "</tr>" +
                    "<tr>" +
                    "<td style=\"text-align: left; width: 250px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; align-items:center;\">" + "身份证：" + visitingEntityPojo.getClientIdcard() + "</td>" +
                    "<td style=\"text-align: left; width: 250px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; align-items:center;\">" + "委托金额：" + visitingEntityPojo.getClientMoney() + "</td>" +
                    "<td style=\"text-align: left; width: 250px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; align-items:center;\">" + "累计还款金额：" + visitingEntityPojo.getAmountCalledBack() + "</td>" +
                    "</tr>" +
                    "<tr>" +
                    "<td style=\"text-align: left; width: 250px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; align-items:center;\">" + "剩余应还：" + visitingEntityPojo.getRemainingDue() + "</td>" +
                    "</tr>" +
                    "</table>" +

                    "<hr/>" +
                    "<p style=\"text-align: left;padding-left: 30px;\"><b>地址信息</b></p>" +
                    "<hr/>" +
                    "<table style=\"width: 100%;\">" +
                    "<tr>" +
                    "<th style=\"text-align: left; width: 100px;\">" + "地址类型" + "</th>" +
                    "<th style=\"text-align: left; width: 500px;\">" + "地址信息" + "</th>" +
                    "</tr>" +
                    "<tr>" +
                    "<td style=\"text-align: left; width: 110px;\">" + "户籍地址：" + "</td>" +
                    "<td style=\"text-align: left;\">" + visitingEntityPojo.getClientCensusRegister() + "</td>" +
                    "</tr>" +
                    "<tr>" +
                    "<td style=\"text-align: left; width: 110px;\">" + "家庭住址：" + "</td>" +
                    "<td style=\"text-align: left;\">" + visitingEntityPojo.getHomeAddress() + "</td>" +
                    "</tr>" +
                    "<tr>" +
                    "<td style=\"text-align: left; width: 110px;\">" + "居住地址：" + "</td>" +
                    "<td style=\"text-align: left;\">" + visitingEntityPojo.getResidentialAddress() + "</td>" +
                    "</tr>" +
                    "<tr>" +
                    "<td style=\"text-align: left; width: 110px;\">" + "单位地址：" + "</td>" +
                    "<td style=\"text-align: left;\">" + visitingEntityPojo.getWorkingAddress() + "</td>" +
                    "</tr>" +
                    "<tr>" +
                    "<td style=\"text-align: left; width: 110px;\">" + "单位名称：" + "</td>" +
                    "<td style=\"text-align: left;\">" + visitingEntityPojo.getPlaceOfWork() + "</td>" +
                    "</tr>" +
                    "</table>" +

                    "<hr/>" +
                    "<p style=\"text-align: left;padding-left: 30px;\"><b>电话信息</b></p>" +
                    "<hr/>" +
                    "<table style=\"margin-left: 20px; width: 100%;\">" +
                    "<tr>" +
                    "<th style=\"text-align: left; width: 100px;\">" + "联系人" + "</th>" +
                    "<th style=\"text-align: left; width: 100px;\">" + "关系" + "</th>" +
                    "<th style=\"text-align: left; width: 100px;\">" + "联系电话" + "</th>" +
                    "</tr>" +
                    "<br/>" +

                    contactInformation +
//                    "<tr>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "莉莉" + "</td>" +
//                    "<td style=\"text-align: left;\">" + "姐妹" + "</td>" +
//                    "<td style=\"text-align: left;\">" + "12312341234" + "</td>" +
//                    "</tr>" +
//                    "<tr>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "莉莉" + "</td>" +
//                    "<td style=\"text-align: left;\">" + "姐妹" + "</td>" +
//                    "<td style=\"text-align: left;\">" + "12312341234" + "</td>" +
//                    "</tr>" +
//                    "<tr>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "莉莉" + "</td>" +
//                    "<td style=\"text-align: left;\">" + "姐妹" + "</td>" +
//                    "<td style=\"text-align: left;\">" + "12312341234" + "</td>" +
//                    "</tr>" +
//                    "<tr>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "莉莉" + "</td>" +
//                    "<td style=\"text-align: left;\">" + "姐妹" + "</td>" +
//                    "<td style=\"text-align: left;\">" + "12312341234" + "</td>" +
//                    "</tr>" +
                    "</table>" +

                    "<hr/>" +
                    "<p style=\"text-align: left;padding-left: 30px;\"><b>催收信息</b></p>" +
                    "<hr/>" +
                    "<table style=\"width: 100%;\">" +

                    "<tr>" +
                    "<th style=\"text-align: left; width: 180px;\">" + "日期" + "</th>" +
                    "<th style=\"text-align: left; width: 150px;\">" + "联系人" + "</th>" +
                    "<th style=\"text-align: left; width: 150px;\">" + "联系电话" + "</th>" +
                    "<th style=\"text-align: left; width: 100px;\">" + "催收员" + "</th>" +
                    "<th style=\"text-align: left; width: 110px;\">" + "沟通内容" + "</th>" +
                    "</tr>" +
                    collectionRecord +
//                    "<tr>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "2021-1-11 12:12:12" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "刘德华（本人）" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "158****0025" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "呼呼" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "重点去跟进，承诺还款 欧耶............" + "</td>" +
//                    "</tr>" +
//                    "<tr>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "2021-1-11 12:12:12" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "刘德华（本人）" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "158****0025" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "呼呼" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "重点去跟进，承诺还款 欧耶123" + "</td>" +
//                    "</tr>" +
//                    "<tr>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "2021-1-11 12:12:12" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "刘德华（本人）" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "158****0025" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "呼呼" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "重点去跟进，承诺还款 欧耶" + "</td>" +
//                    "</tr>" +
//                    "<tr>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "2021-1-11 12:12:12" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "刘德华（本人）" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "158****0025" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "呼呼" + "</td>" +
//                    "<td style=\"text-align: left; width: 110px;\">" + "重点去跟进，承诺还款 欧耶" + "</td>" +
//                    "</tr>" +

                    "</table>" +

                    "<hr/>" +
                    "<p style=\"text-align: left;padding-left: 30px;\"><b>外访过程记录</b></p>" +
                    "<hr/>" +
                    "<div style=\"border: 1px solid #000;width: 100%;height: 300px; position: relative;margin-top: 20px; margin-bottom: 20px;\">" +
                    "<p style=\"text-align: right; display: block; margin-top: 215px; margin-right: 80px;\">" + "记录人:" + "</p>" +
                    "<p style=\"text-align: right; display: block; margin-right: 80px;\">" + "日期:" + "</p>" +
                    "</div>" +
                    "<hr/>" +

                    "<table style=\"width: 100%;\">" +
                    "<tr>" +
                    "<td style=\"text-align: left; width: 110px;\">" + "债务人签字：" + "</td>" +
                    "<td style=\"text-align: left; width: 110px;\">" + "主管确认：" + "</td>" +
                    "</tr>" +
                    "</table>" +
                    "<p style=\"text-align: right;\">" + format + "</p>" +
//                    "</div>" +
                    "</body>" +
                    "</html>";
//			String content = "";

            byte b[] = content.getBytes("utf-8");  //这里是必须要设置编码的，不然导出中文就会乱码。
            ByteArrayInputStream bais = new ByteArrayInputStream(b);//将字节数组包装到流中

            XMLWorkerHelper.getInstance().parseXHtml(writer, document, bais, Charset.forName("UTF-8"));

            bais.close();
            document.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }







    /**
     * 修改最后一次登录时间
     *
     * @param
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/lastLoginTime", method = RequestMethod.GET)
    public AjaxResult lastLoginTime() {
        agLoginService.lastLoginTime();
        return AjaxResult.success();
    }



    /**
     * 根据路由地址查询路由是否存在
     *
     * @return 用户信息
     */
    @RequiresLogin
    @GetMapping("getPath")
    public AjaxResult getPath(String path) {
        if (ObjectUtils.isEmpty(path)) {
            throw new GlobalException("路径不能为空");
        }
        List<String> list = menuService.selectMenuPermsPath(path);
        if (ObjectUtils.isEmpty(list) || list.isEmpty()) {
            return AjaxResult.success(1);
        } else {
            return AjaxResult.success(2);
        }
    }

}
