package com.zws.appeal.controller.appealController;

import com.zws.appeal.agservice.AgCaseService;
import com.zws.appeal.domain.MessageTemplatePojo;
import com.zws.appeal.domain.appeal.RefundRecord;
import com.zws.appeal.pojo.SendRecordsPojos;
import com.zws.appeal.pojo.appeal.ExecuteCasePojo;
import com.zws.appeal.pojo.appeal.RefundRecordPojo;
import com.zws.appeal.service.appeal.IExecuteCaseService;
import com.zws.appeal.service.appeal.IRefundRecordService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.annotation.RequiresLogin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 案件执行模块
 *
 * @Author：liuxifeng
 * @Date：2024/6/25 16:11
 * @Describe：诉讼执行模块 、 执行回款模块
 * 调解流程可以进入 执行流程
 * 诉讼流程可以进入 执行流程
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/execute-case")
public class ExecuteController extends BaseController {


    @Autowired
    private IRefundRecordService refundRecordService;
    @Autowired
    private IExecuteCaseService executeCaseService;
    @Autowired
    private AgCaseService agCaseService;
    @Autowired
    private RedisService redisService;


    /**
     * 列表查询
     * @return
     */
    @GetMapping("/selectList")
    public TableDataInfo selectList(ExecuteCasePojo pojo){
        startPage();
        List<ExecuteCasePojo> list = executeCaseService.selectList(pojo);
        return getDataTable(list);
    }

    /**
     * 诉讼执行-列表统计
     * @param pojo
     * @return
     */
    @PostMapping("/selectWithMoney")
    public AjaxResult selectWithMoney(@RequestBody ExecuteCasePojo pojo){
        Map<String,Object> map = executeCaseService.selectWithMoney(pojo);
        return AjaxResult.success(map);
    }

    /**
     * 批量导出
     * @param response
     * @param pojo
     * @throws UnsupportedEncodingException
     */
    @PostMapping("/exportWithExecuteCase")
    public void exportWithExecuteCase(HttpServletResponse response, @RequestBody ExecuteCasePojo pojo) throws UnsupportedEncodingException {
        List<ExecuteCasePojo> list= executeCaseService.selectList(pojo);
        ExcelUtil util=new ExcelUtil(ExecuteCasePojo.class);
        String fileName = "诉讼执行.xlsx";
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, list, "诉讼执行");
    }


    /**
     * 批量执行
     * @param pojo
     * @return
     */
    @PostMapping("/batchExecute")
    public AjaxResult batchExecute(@RequestBody ExecuteCasePojo pojo){
        int i = executeCaseService.batchExecute(pojo);
        return AjaxResult.success();
    }


//==================执行回款模块=================================



    /**
     * 列表查询
     * @return
     */
    @GetMapping("/selectWithRefund")
    public TableDataInfo selectWithRefund(RefundRecordPojo pojo){
        startPage();
        List<RefundRecordPojo> list = refundRecordService.selectWithRefund(pojo);
        return getDataTable(list);
    }

    /**
     * 诉讼执行-列表统计
     * @param pojo
     * @return
     */
    @PostMapping("/selectWithRefundMoney")
    public AjaxResult selectWithRefundMoney(@RequestBody RefundRecordPojo pojo){
        Map<String,Object> map = refundRecordService.selectWithRefundMoney(pojo);
        return AjaxResult.success(map);
    }

    /**
     * 批量导出
     * @param response
     * @param pojo
     * @throws UnsupportedEncodingException
     */
    @PostMapping("/exportWithRefundRecord")
    public void exportWithRefundRecord(HttpServletResponse response, @RequestBody RefundRecordPojo pojo) throws UnsupportedEncodingException {
        List<RefundRecordPojo> list= refundRecordService.selectWithRefund(pojo);
        ExcelUtil util=new ExcelUtil(RefundRecordPojo.class);
        String fileName = "执行回款.xlsx";
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, list, "执行回款");
    }


    /**
     * 下载模板文件 表头
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) throws IOException {
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("导入模板.xlsx", "utf-8"));
        ExcelUtil<RefundRecord> util = new ExcelUtil<RefundRecord>(RefundRecord.class);
        util.exportExcel(response, new ArrayList<>(), "导入模板");
    }

    /***
     *回款登记
     */
    @PostMapping("/batchRegisterRefund")
    public AjaxResult batchRegisterRefund(@RequestBody RefundRecordPojo pojo){
        int i = refundRecordService.batchRegisterRefund(pojo);
        return AjaxResult.success();
    }


    /**
     * 批量导入回款登记Excel
     * @param
     * @throws IOException
     */
    @PostMapping("/importWithExcel")
    public AjaxResult importWithExcel(@RequestBody RefundRecordPojo pojo)  {
        refundRecordService.importWithExcel(pojo);
        return AjaxResult.success();
    }

}
