package com.zws.appeal.controller.letterDoc.letter.service;

import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;
import com.zws.appeal.controller.letterDoc.letter.domain.RetrievalFileSeparate;
import com.zws.system.api.model.LoginUser;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 函件-文书 业务层
 * <AUTHOR>
 * @date ：Created in 2023/12/12 15:33
 */
public interface ILetterDocService {

    /**
     * 函件id 查询批次id集合
     * @param ids
     * @return
     */
    List<Integer> selectLetterIdByIds(List<Long> ids);

    /**
     * 根据随机ID查询函件
     * @param uuid
     * @return
     */
    LetterDoc selectByRandomId(String uuid);

    /**
     * 查询展示的列表数据
     * @param letter
     * @return
     */
    List<LetterDoc> selectSimpleList(LetterDoc letter);

    /**
     * 导出签章文件
     * 以文档打包的形式导出（律所函以pdf的格式导出-导出不能编辑）；
     * 已签章的导出签章文件、未签章的导出预览文件
     * @param letters   函件数据
     * @return 返回保存的文件url
     */
    String exportSignPreviewUrl(HttpServletResponse response, List<LetterDoc> letters,LoginUser loginUser);
    /**
     * 批量插入数据
     * @param letter
     * @return
     */
    int save(LetterDoc letter,LoginUser loginUser);

    Long insert(LetterDoc record);

    void updateById(LetterDoc record);

    void deleteById(Long id);

    LetterDoc getById(Long id);

    /**
     * 查询列表
     * @param record
     * @return
     */
    List<LetterDoc> selectList(LetterDoc record, LoginUser loginUser);


    List<LetterDoc> getLetterDoc(Long caseId, Integer code);

    List<LetterDoc> getLetterDocs(Long caseId, Integer code);

    String selectDataManagementByCaseId(Long caseId);

    List<RetrievalFileSeparate> getArchivalData(String clientIdcard);

    LetterDoc selectByPreviewUrl(String previewUrl);

    /**
     * 生成预览
     * @param letter
     * @param templateId 模板id，为空时letter 的id不能为空
     * @param isHideInfo 信息是否脱敏，true脱敏，false-不脱敏
     * @return
     */
    String createPreview(LetterDoc letter,Integer templateId,boolean isHideInfo);

    /**
     * 获取函件量使用的模板id
     * @param id
     * @return
     */
    Integer getTemplateId(Integer id);
}
