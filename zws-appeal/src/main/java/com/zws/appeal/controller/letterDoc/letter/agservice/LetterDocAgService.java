package com.zws.appeal.controller.letterDoc.letter.agservice;

import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.zws.common.core.constant.LetterConstant;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.signature.Stamp;
import com.zws.common.core.utils.*;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.pdf.po.PdfFile;
import com.zws.common.redis.service.RedisService;
import com.zws.appeal.controller.letterDoc.letter.FileUtils;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;
import com.zws.appeal.controller.letterDoc.letter.domain.RetrievalFileSeparate;
import com.zws.appeal.controller.letterDoc.letter.enums.DocTypes;
import com.zws.appeal.controller.letterDoc.letter.pojo.BatchDoc;
import com.zws.appeal.controller.letterDoc.letter.pojo.PdfFilePojo;
import com.zws.appeal.controller.letterDoc.letter.pojo.SaveDoc;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterDocService;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 函件文书agservice
 * <AUTHOR>
 * @date ：Created in 2023/12/12 22:06
 */
@Component
public class LetterDocAgService {

    @Autowired
    private ILetterTemplateService templateService;
    @Autowired
    private LetterTemplateAgService templateAgService;
    @Autowired
    private ILetterDocService docService;
    @Autowired
    private RedisService redisService;

    /**
     * 获取模板数据
     * @param caseId            案件id
     * @param letterTemplateId  模板id
     * @return
     */
    public Map<String,Object> getDocDate(Long caseId,Long letterTemplateId){
        LetterTemplate letterTemplate = templateService.getById(letterTemplateId);
        List<String> variables = SplitUtils.strSplitComma(letterTemplate.getTemplateVariable());
        Map<String,Object> params=new HashMap<>();
        //TOOD 查询获取案件信息转为模板数据

        return params;
    }

    /**
     * 生成预览文书
     * @param batchDoc
     */
    public SaveDoc previewDoc(BatchDoc batchDoc){
        List<Long> caseIds = batchDoc.getCaseIds();
        List<Long> letterTemplateIds = batchDoc.getLetterTemplateIds();
        Map<String,Integer> fileUrls=new HashMap<>();
        List<LetterDoc> docs=new ArrayList<>();
        List<PdfFilePojo> pdfFilePojos = new ArrayList<>();
        int times = 0;
        for (Long caseId:caseIds) {
            //caseId 获取案件的 信息 对应的 姓名、地址等实际数据 、获取
            HashMap<String, Object> caseInfoMap = templateService.getCaseInfo(caseId);
            for (Long templateId:letterTemplateIds) {
                PdfFilePojo pdfFilePojo = new PdfFilePojo();
                Map<String, Object> itemDateMap=getDocDate(caseId,templateId);
                LetterDoc doc=new LetterDoc();
                doc.setCaseId(caseId);
                doc.setLawAgencyId(batchDoc.getLawAgencyId());
                doc.setItemData(JSONUtil.toJsonStr(itemDateMap));
                doc.setLetterTemplateId(templateId);
                doc.setSerialNo(IdUtils.fastSimpleUUID());
                LetterTemplate template = templateService.getById(templateId);

                List<Stamp> array = JSON.parseArray(template.getPositionData(), Stamp.class);
                template.setPositionList(array);
                times = times+1;
                //固定替换参数预览  取该案件的默认替换参数+模板内容   实时替换
                PdfFile file=templateAgService.createPreviewByVariable(caseInfoMap,template,times);

                //TODO 生成签章 预览(含盖章图片的模板)
//                PdfFile file= templateAgService.createPreview(template);

                String fileUrl = file.getUrl();
                //String fileUrl= templateAgService.createPreview(doc,templateId,false);
                doc.setPreviewUrl(fileUrl);
//                doc.setContent(file.getContent());
                fileUrls.put(fileUrl,doc.getPreviewPages());
                pdfFilePojo.setFileUrl(fileUrl);
                pdfFilePojo.setCount(file.getPages());
//                pdfFilePojo.setContent(file.getContent());
                pdfFilePojos.add(pdfFilePojo);
                docs.add(doc);
            }
        }
        SaveDoc saveDoc=new SaveDoc();
        String uid= IdUtils.fastSimpleUUID();
        //缓存10 分钟
        redisService.setCacheObject(LetterConstant.LETTER_DOC_PREVIEWDOC_KEY+uid,JSONUtil.toJsonStr(docs),30L, TimeUnit.MINUTES);
        saveDoc.setUid(uid);
//        saveDoc.setFileUrls(fileUrls);
        saveDoc.setPdfFilePojos(pdfFilePojos);
       return saveDoc;


    }

    /**
     * 批量保存
     * @param saveDoc   保存文书的参数
     * @param docType   函件类型
     */
    public void batchSaveDoc(SaveDoc saveDoc, DocTypes docType){
        String uid=saveDoc.getUid();
        String key=LetterConstant.LETTER_DOC_PREVIEWDOC_KEY+uid;
        if (!redisService.hasKey(key)){
            throw new ServiceException("已超时，请重新选择");
        }
        String jsonStr= redisService.getCacheObject(key, String.class);
        List<LetterDoc> docs = JSONUtil.toList(jsonStr, LetterDoc.class);
        for (LetterDoc doc:docs) {
            doc.setDocType(docType.getCode());
            docService.insert(doc);
        }
        this.redisService.deleteObject(key);
    }

    /**
     * 获取文书、保全
     * @param caseId
     * @param docType
     * @return
     */
    public List<LetterDoc> getLetterDoc(Long caseId, DocTypes docType) {
        return docService.getLetterDoc(caseId,docType.getCode());
    }

    /**
     * 获取文书
     * @param caseId
     * @param docType
     * @return
     */
    public List<LetterDoc> getLetterDocs(Long caseId, DocTypes docType) {
        return docService.getLetterDocs(caseId,docType.getCode());
    }

    /**
     * 获取档案资料列表
     * @param caseId 案件id
     * @return
     */
    public List<RetrievalFileSeparate> getArchivalData(Long caseId) {
        if (caseId == null) {
            throw new ServiceException("案件id不能为空");
        }
//        根据案件id查询案件身份证号码
        PageUtils.clearPage();
        String clientIdcard = selectDataManagementByCaseId(caseId);
        if (ObjectUtils.isEmpty(clientIdcard)) {throw new GlobalException("客户身份证号查询为空");}
        PageUtils.startPage();
        List<RetrievalFileSeparate> archivalData = docService.getArchivalData(clientIdcard);
        return archivalData;
    }

    /**
     * 批量下载
     */
    public void ZipDownloadMediate(HttpServletResponse response, List<String> urls) throws Exception {
        List<File> fileList = new ArrayList<>();
        for (String url : urls) {
            String filename = url.substring(url.lastIndexOf('/') + 1);
            fileList.add(FileDownloadUtils.downloadTempFileWithFileName(url,filename));
        }
        //将files打包成zip压缩包
        dowloadToZip(fileList,response);

    }

    /**
     * 将多个文件转zip压缩包
     *
     * @param fileList
     * @param response
     * @throws Exception
     */
    public void dowloadToZip(List<File> fileList, HttpServletResponse response) throws Exception {

        int i = 0;
        if (fileList != null && fileList.size() > 0) {
            try {
                //被压缩文件流集合
                InputStream[] srcFiles = new InputStream[fileList.size()];
                //被压缩文件名称
                String[] srcFileNames = new String[fileList.size()];
                for (File entity : fileList) {
                    //获取inputStream
                    InputStream ins = new FileInputStream(entity);
                    if (ins == null) {
                        continue;
                    }
                    //塞入流数组中
                    srcFiles[i] = ins;
                    srcFileNames[i] = entity.getName();
                    i++;
                }
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(DateUtils.getTime()+".zip", "UTF-8"));
                //多个文件压缩成压缩包返回
                ZipUtil.zip(response.getOutputStream(), srcFileNames, srcFiles);
            } catch (IOException e) {
                e.printStackTrace();
            }
            finally {
                for (File file : fileList) {
                    FileUtils.deletedTempFile(file);
                }
            }
        }

    }


    /**
     *根据案件id查询案件身份证号码
     * @return
     */
    public String selectDataManagementByCaseId (Long caseId){
        return docService.selectDataManagementByCaseId(caseId);
    }
}
