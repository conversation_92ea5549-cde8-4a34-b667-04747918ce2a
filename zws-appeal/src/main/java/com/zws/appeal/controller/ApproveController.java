package com.zws.appeal.controller;

import com.zws.common.core.domain.R;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.appeal.agservice.AgCaseService;
import com.zws.appeal.agservice.ApproveAgService;
import com.zws.appeal.domain.ApproveProce;
import com.zws.appeal.enums.ApproveEnum;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.pojo.*;
import com.zws.appeal.pojo.myApproval.*;
import com.zws.appeal.service.MyApprovalService;
import com.zws.appeal.utils.Log;
import com.zws.system.api.RemoteLetterService;
import com.zws.system.api.RemoteProceService;
import com.zws.system.api.domain.dto.ReviewProgressDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 审批管理
 *
 * <AUTHOR>
 * @date 2024/2/28 19:23
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/case")
public class ApproveController extends BaseController {

    @Autowired
    private AgCaseService agCaseService;
    @Autowired
    private ApproveAgService approveAgService;
    @Autowired
    private MyApprovalService myApprovalService;
    @Autowired
    private RemoteLetterService remoteLetterService;
    @Autowired
    private RemoteProceService remoteProceService;

    /**
     * 我的审批查询（回款审批）
     *
     * @param paymentCollectionUtils
     * @return
     */
    @RequiresLogin
    @GetMapping("/selectRepaymentRecord")
    public TableDataInfo selectListWithRepaymentRecord(PaymentCollectionUtils paymentCollectionUtils) {
        startPage();
        paymentCollectionUtils.setClientName(FieldEncryptUtil.encrypt(paymentCollectionUtils.getClientName()));
        paymentCollectionUtils.setClientIdcard(FieldEncryptUtil.encrypt(paymentCollectionUtils.getClientIdcard()));
        List<myRepaymentRecordUtils> myRepaymentRecordUtils = agCaseService.judgeButtonTwo(paymentCollectionUtils);
        return getDataTable(myRepaymentRecordUtils);
    }

    /**
     * 我的审批查询（留案审批）
     *
     * @param queryCriteria
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectRetention", method = RequestMethod.GET)
    public TableDataInfo selectListWithRetention(QueryCriteria queryCriteria) {
        startPage();
        queryCriteria.setClientName(FieldEncryptUtil.encrypt(queryCriteria.getClientName()));
        queryCriteria.setClientIdcard(FieldEncryptUtil.encrypt(queryCriteria.getClientIdcard()));
        queryCriteria.setApplyState(1);  //申请的案件状态，0-停催，1-留案，2-退案
        List<myApplyRecordUtils> retention = agCaseService.judgeButtonOne(queryCriteria);
        return getDataTable(retention);
    }

    /**
     * 我的审批查询（退案审批）
     *
     * @param queryCriteria
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectWithdrawal", method = RequestMethod.GET)
    public TableDataInfo selectListWithWithdrawal(QueryCriteria queryCriteria) {
        queryCriteria.setClientName(FieldEncryptUtil.encrypt(queryCriteria.getClientName()));
        queryCriteria.setClientIdcard(FieldEncryptUtil.encrypt(queryCriteria.getClientIdcard()));
        startPage();
        queryCriteria.setApplyState(2);  //申请的案件状态，0-停催，1-留案，2-退案
        List<myApplyRecordUtils> retention = agCaseService.judgeButtonOne(queryCriteria);
        return getDataTable(retention);
    }

    /**
     * 我的审批查询（停催审批）
     *
     * @param queryCriteria
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectStopUrging", method = RequestMethod.GET)
    public TableDataInfo selectListWithStopUrging(QueryCriteria queryCriteria) {
        queryCriteria.setClientName(FieldEncryptUtil.encrypt(queryCriteria.getClientName()));
        queryCriteria.setClientIdcard(FieldEncryptUtil.encrypt(queryCriteria.getClientIdcard()));
        startPage();
        queryCriteria.setApplyState(0);  //申请的案件状态，0-停催，1-留案，2-退案
        List<myApplyRecordUtils> retention = agCaseService.judgeButtonOne(queryCriteria);
        return getDataTable(retention);
    }


    /**
     * 我的审批查询（资料调取审批）
     *
     * @param paymentCollectionUtils
     * @return
     */
    @RequiresLogin
    @GetMapping("/selectRetrievalRecord")
    public TableDataInfo selectListWithRetrievalRecord(PaymentCollectionUtils paymentCollectionUtils) {
        startPage();
        paymentCollectionUtils.setClientName(FieldEncryptUtil.encrypt(paymentCollectionUtils.getClientName()));
        paymentCollectionUtils.setClientIdcard(FieldEncryptUtil.encrypt(paymentCollectionUtils.getClientIdcard()));
        List<MyRetrievalRecordUtils> myRetrievalRecordUtils = agCaseService.judgeButtonFive(paymentCollectionUtils);
        return getDataTable(myRetrievalRecordUtils);
    }

    /**
     * 我的审批查询（减免审批）
     *
     * @param paymentCollectionUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectReductionRecord", method = RequestMethod.GET)
    public TableDataInfo selectListWithReductionRecord(PaymentCollectionUtils paymentCollectionUtils) {
        startPage();
        paymentCollectionUtils.setClientName(FieldEncryptUtil.encrypt(paymentCollectionUtils.getClientName()));
        paymentCollectionUtils.setClientIdcard(FieldEncryptUtil.encrypt(paymentCollectionUtils.getClientIdcard()));
        List<myReductionRecordUtils> myReductionRecordUtils = agCaseService.judgeButtonThree(paymentCollectionUtils);
        return getDataTable(myReductionRecordUtils);
    }

    /**
     * 我的审批查询（分期还款审批）
     *
     * @param paymentCollectionUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectStagingRecord", method = RequestMethod.GET)
    public TableDataInfo selectListWithStagingRecord(PaymentCollectionUtils paymentCollectionUtils) {
        paymentCollectionUtils.setClientName(FieldEncryptUtil.encrypt(paymentCollectionUtils.getClientName()));
        paymentCollectionUtils.setClientIdcard(FieldEncryptUtil.encrypt(paymentCollectionUtils.getClientIdcard()));
        startPage();
        List<MyStagingRecordUtils> myStagingRecordUtils = agCaseService.judgeButtonFour(paymentCollectionUtils);
        return getDataTable(myStagingRecordUtils);
    }

    /**
     * 我的审批查询（外访审批）
     *
     * @param outsideCollectionUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectOutsideRecord", method = RequestMethod.GET)
    public TableDataInfo selectListWithOutsideRecord(OutsideCollectionUtils outsideCollectionUtils) {
        outsideCollectionUtils.setClientName(FieldEncryptUtil.encrypt(outsideCollectionUtils.getClientName()));
        outsideCollectionUtils.setClientIdcard(FieldEncryptUtil.encrypt(outsideCollectionUtils.getClientIdcard()));
        startPage();
        List<MyOutsideRecordUtils> myOutsideRecordUtils = agCaseService.judgeButtonFive(outsideCollectionUtils);
        return getDataTable(myOutsideRecordUtils);
    }

    /**
     * 我的审批查询（签章审批）
     *
     * @param signCollectionUtils
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectListWithSignRecord", method = RequestMethod.GET)
    public TableDataInfo selectListWithSignRecord(SignCollectionUtils signCollectionUtils) {
        signCollectionUtils.setClientName(FieldEncryptUtil.encrypt(signCollectionUtils.getClientName()));
        signCollectionUtils.setClientIdcard(FieldEncryptUtil.encrypt(signCollectionUtils.getClientIdcard()));
        startPage();
        List<MySignRecordUtils> mySignRecordUtils = agCaseService.judgeButtonSix(signCollectionUtils);
        return getDataTable(mySignRecordUtils);
    }




    /**
     * 根据申请表id查询审核进程表数据(回款审核)
     *
     * @param approveProce
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApproveProceOne", method = RequestMethod.GET)
    public AjaxResult selectApproveProce1(ApproveProce approveProce) {
        approveProce.setApproveCode(0);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核,8-资料调取审核
        List<ApproveProce> approveProces = myApprovalService.selectApproveProce(approveProce);
        return AjaxResult.success("查询成功", approveProces);
    }

    /**
     * 根据申请表id查询审核进程表数据(减免审核)
     *
     * @param approveProce
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApproveProceTwo", method = RequestMethod.GET)
    public AjaxResult selectApproveProce2(ApproveProce approveProce) {
        approveProce.setApproveCode(1);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核,8-资料调取审核
        List<ApproveProce> approveProces = myApprovalService.selectApproveProce(approveProce);
        return AjaxResult.success("查询成功", approveProces);
    }

    /**
     * 根据申请表id查询审核进程表数据(分期审核)
     *
     * @param approveProce
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApproveProceThree", method = RequestMethod.GET)
    public AjaxResult selectApproveProce3(ApproveProce approveProce) {
        approveProce.setApproveCode(2);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核,8-资料调取审核
        List<ApproveProce> approveProces = myApprovalService.selectApproveProce(approveProce);
        return AjaxResult.success("查询成功", approveProces);
    }

    /**
     * 根据申请表id查询审核进程表数据(留案审核)
     *
     * @param approveProce
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApproveProceFour", method = RequestMethod.GET)
    public AjaxResult selectApproveProce4(ApproveProce approveProce) {
        approveProce.setApproveCode(3);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核,8-资料调取审核
        List<ApproveProce> approveProces = myApprovalService.selectApproveProce(approveProce);
        return AjaxResult.success("查询成功", approveProces);
    }

    /**
     * 根据申请表id查询审核进程表数据(停催审核)
     *
     * @param approveProce
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApproveProceFive", method = RequestMethod.GET)
    public AjaxResult selectApproveProce5(ApproveProce approveProce) {
        approveProce.setApproveCode(4);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核,8-资料调取审核
        List<ApproveProce> approveProces = myApprovalService.selectApproveProce(approveProce);
        return AjaxResult.success("查询成功", approveProces);
    }

    /**
     * 根据申请表id查询审核进程表数据(退案审核)
     *
     * @param approveProce
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApproveProceSix", method = RequestMethod.GET)
    public AjaxResult selectApproveProce6(ApproveProce approveProce) {
        approveProce.setApproveCode(5);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核,8-资料调取审核
        List<ApproveProce> approveProces = myApprovalService.selectApproveProce(approveProce);
        return AjaxResult.success("查询成功", approveProces);
    }

    /**
     * 根据申请表id查询审核进程表数据(外访审核)
     *
     * @param approveProce
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApproveProceSeven", method = RequestMethod.GET)
    public AjaxResult selectApproveProce7(ApproveProce approveProce) {
        approveProce.setApproveCode(7);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核,8-资料调取审核
        List<ApproveProce> approveProces = myApprovalService.selectApproveProce(approveProce);
        return AjaxResult.success("查询成功", approveProces);
    }

    /**
     * 根据申请表id查询审核进程表数据(资料调取审核)
     *
     * @param approveProce
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApproveProceEight", method = RequestMethod.GET)
    public AjaxResult selectApproveProce8(ApproveProce approveProce) {
        approveProce.setApproveCode(8);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核,8-资料调取审核
        List<ApproveProce> approveProces = myApprovalService.selectApproveProce(approveProce);
        return AjaxResult.success("查询成功", approveProces);
    }

    /**
     * 函件审核 审核进程数据
     * @param approveProce
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectApproveProceLawa", method = RequestMethod.GET)
    public AjaxResult selectApproveProceLawa(ApproveProce approveProce) {
        if (approveProce.getApplyId()== null){
            approveProce.setApplyId(approveProce.getId());
        }
        approveProce.setApproveCode(ApproveEnum.LAWAPPROVE.getCode());  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核,8-资料调取审核
        List<ApproveProce> approveProces = myApprovalService.selectApproveProce(approveProce);
        return AjaxResult.success("查询成功", approveProces);
    }

    /**
     * 函件批次列表页面查看 审核进程数据
     * @param approveProce
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectSingApproveProce", method = RequestMethod.GET)
    public AjaxResult selectSingApproveProce(ApproveProce approveProce) {
        R<Long> r = remoteLetterService.getSignRecordId(approveProce.getId());
        approveProce.setApplyId(r.getData());
        approveProce.setApproveCode(ApproveEnum.LAWAPPROVE.getCode());  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核,8-资料调取审核
        List<ApproveProce> approveProces = myApprovalService.selectApproveProce(approveProce);
        return AjaxResult.success("查询成功", approveProces);
    }


    /**
     * 留案审批（写入历史表并修改申请表中审核状态等）
     *
     * @param approvalRecord
     * @return
     */
    @Log(title = "案件管理-留案审批", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/WriteRetention", method = RequestMethod.POST)
    public AjaxResult WriteRetention(@RequestBody ApprovalRecord approvalRecord) {
        approvalRecord.setApproveCode(3);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        agCaseService.WriteModification(approvalRecord, SecurityUtils.getLoginUser());
        return AjaxResult.success("审批成功");
    }

    /**
     * 退案审批（写入历史表并修改申请表中审核状态等）
     *
     * @param approvalRecord
     * @return
     */
    @Log(title = "案件管理-退案审批", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/WriteBack", method = RequestMethod.POST)
    public AjaxResult WriteBack(@RequestBody ApprovalRecord approvalRecord) {
        approvalRecord.setApproveCode(5);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        agCaseService.WriteModification(approvalRecord, SecurityUtils.getLoginUser());
        return AjaxResult.success("审批成功");
    }

    /**
     * 停催审批（写入历史表并修改申请表中审核状态等）
     *
     * @param approvalRecord
     * @return
     */
    @Log(title = "案件管理-停催审批", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/WriteStopReminder", method = RequestMethod.POST)
    public AjaxResult WriteStopReminder(@RequestBody ApprovalRecord approvalRecord) {
        approvalRecord.setApproveCode(4);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        agCaseService.WriteModification(approvalRecord, SecurityUtils.getLoginUser());
        return AjaxResult.success("审批成功");
    }

    /**
     * 资料调取审批（写入历史表并修改申请表中审核状态等）
     *
     * @param approvalRecord
     * @return
     */
    @Log(title = "案件管理-资料调取审批", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/dataRetrievalApproval", method = RequestMethod.POST)
    public AjaxResult dataRetrievalApproval(@RequestBody ApprovalRecord approvalRecord) {
        approvalRecord.setApproveCode(8);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核，8-资料调取审批
        agCaseService.dataRetrievalApproval(approvalRecord);
        return AjaxResult.success("审批成功");
    }

    /**
     * 回款审批（写入历史表并修改申请表中审核状态等）
     *
     * @param approvalRecord
     * @return
     */
    @Log(title = "案件管理-回款审批", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/WriteCollectionApproval", method = RequestMethod.POST)
    public AjaxResult WriteCollectionApproval(@RequestBody ApprovalRecord approvalRecord) {
        approvalRecord.setApproveCode(0);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        agCaseService.WriteCollectionApproval(approvalRecord);
        return AjaxResult.success("审批成功");
    }

    /**
     * 减免审批（写入历史表并修改申请表中审核状态等）
     *
     * @param approvalRecord
     * @return
     */
    @Log(title = "案件管理-减免审批", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/ExemptionApproval", method = RequestMethod.POST)
    public AjaxResult ExemptionApproval(@RequestBody ApprovalRecord approvalRecord) {
        approvalRecord.setApproveCode(1);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        agCaseService.ExemptionApproval(approvalRecord);
        return AjaxResult.success("审批成功");
    }

    /**
     * 分期还款审批（写入历史表并修改申请表中审核状态等）
     *
     * @param approvalRecord
     * @return
     */
    @Log(title = "案件管理-分期还款审批", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/InstallmentApproval", method = RequestMethod.POST)
    public AjaxResult InstallmentApproval(@RequestBody ApprovalRecord approvalRecord) {
        approvalRecord.setApproveCode(2);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核
        agCaseService.InstallmentApproval(approvalRecord);
        return AjaxResult.success("审批成功");
    }

    /**
     * 外访审批（写入历史表并修改申请表中审核状态等）
     *
     * @param approvalRecord
     * @return
     */
    //@RequiresPermissions(value = {"case:aduit:pass:outsize", "case:aduit:unpass:outsize"}, logical = Logical.OR)
    @Log(title = "案件管理-外访审批", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updateOutsideRecordAs", method = RequestMethod.POST)
    public AjaxResult updateOutsideRecord(@RequestBody ApprovalRecord approvalRecord) {
        approvalRecord.setApproveCode(7);  //审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核，7-外访审核
        agCaseService.updateOutsideRecord(approvalRecord);
        return AjaxResult.success("审批成功");
    }



    /**
     *
     * 函件审批操作
     * @param approvalRecord
     * @return
     */
    @Log(title = "案件管理-签章审批", businessType = BusinessType.UPDATE)
    @RequiresLogin
    @RequestMapping(value = "/updateSignRecord", method = RequestMethod.POST)
    public AjaxResult updateSignRecord(@RequestBody ApprovalRecord<SignCollectionUtils> approvalRecord) {

        approvalRecord.setApproveCode(ApproveEnum.LAWAPPROVE.getCode());
        approveAgService.signRecordHandle(approvalRecord, SecurityUtils.getLoginUser());
        return AjaxResult.success("审批成功");
    }


    /**
     * 函件文件-获取文件预览url
     * @param id
     * @return
     */
    @RequiresLogin
    @GetMapping(value = "/sign/getPreviewUrl")
    public AjaxResult getPreviewUrl(Integer id){
        return  remoteLetterService.getPreviewUrl(id);
    }

    /**
     * 审批流程进度
     *
     * @param applyId 记录id
     * @param code
     * @return
     */
    @GetMapping("/retrieval/proceNode")
    public R<List<ReviewProgressDto>> retrievalProce(Long applyId, Integer code) {
        R<List<ReviewProgressDto>> r = remoteProceService.getReviewProgress(code, applyId);
        return r;
    }

}
