package com.zws.appeal.controller.letterDoc.law.service.impl;

import cn.hutool.json.JSONUtil;
import com.zws.appeal.controller.letterDoc.law.domain.LawSign;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.seal.SealStamp;
import com.zws.common.core.domain.seal.SignatureParam;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.signature.SealParameters;
import com.zws.common.core.signature.SignatureUtils;
import com.zws.common.core.signature.StampPosition;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.pdf.PdfUtils;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.RemoteFileService;
import com.zws.system.api.RemoteLetterService;
import com.zws.system.api.domain.SysFile;
import com.zws.appeal.controller.letterDoc.law.Base64Utils;
import com.zws.appeal.controller.letterDoc.law.mapper.LawSignMapper;
import com.zws.appeal.controller.letterDoc.law.service.ILawSignService;
import com.zws.appeal.controller.letterDoc.letter.PDFFileUtils;
import com.zws.appeal.controller.letterDoc.letter.agservice.LetterTemplateAgService;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.cms.SignerId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;

/**
 * 签章 service
 * <AUTHOR>
 * @date ：Created in 2023/11/22 22:05
 */
@Slf4j
@Service
public class LawSignServiceImpl implements ILawSignService {

    @Autowired
    private LawSignMapper baseMapper;
    @Autowired
    private LetterTemplateAgService templateAgService;
    @Autowired
    private RemoteFileService remoteFileService;
    @Autowired
    private RemoteLetterService remoteLetterService;

    @Override
    public Long insert(LawSign record) {
        if (record==null){
            return null;
        }
        //签章ID 不能重复
        LawSign lawSign = baseMapper.getBySignCode(record.getSignCode());
        if(lawSign!=null){
            throw new ServiceException("签章ID已存在，不能重复");
        }
        record.setId(null);
        record.setCreateBy(SecurityUtils.getUsername());
        record.setCreateById(SecurityUtils.getUserId());
//        record.setCreateId(SecurityUtils.getTeamId());
        record.setCreateTime(new Date());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        if(record.getSignPic()!= null && record.getSignCode()!= null){
            //创建一个空白pdf并盖章
            String imageBase64 = Base64Utils.getImgUrlToBase64(record.getSignPic());
            record.setSignPicStr(imageBase64);

            String blankPdf = createBlankPdf(record.getSignCode(),record.getSignPicStr());
            record.setBlankPdf(blankPdf);
        }
        baseMapper.insert(record);
        return record.getId();
    }

    public  String createBlankPdf(String signerId,String signPicBase64){
        File pdfFile = null;
        String pdfUrl = null;
        try{
            String blankContent = templateAgService.getBlankContent();
            pdfFile = PdfUtils.createHtmlPdf(blankContent, null, null);
            String pdfBase64 = PDFFileUtils.getPDFBinary(pdfFile);
            String pdfFileName = UUID.randomUUID().toString();


            SealParameters sealParameters = new SealParameters();
            List<StampPosition> positionList = new ArrayList<>();
            StampPosition position = new StampPosition();
            position.setBottom(200);
            position.setLeft(200);
            position.setPageNum(1);
            position.setSignerId(signerId);
            positionList.add(position);
            sealParameters.setStampPositionList(positionList);

            sealParameters.setBase64File(pdfBase64);
            sealParameters.setFileName(pdfFileName);
            sealParameters.setSignerId(signerId);
            //设置图片base64
            sealParameters.setSealImage(signPicBase64);
            log.info(" ======================== 执行签章方法 ========================"+" 参数："+ JSONUtil.toJsonStr(sealParameters));
            SealStamp sealStamp = new SealStamp();
            sealStamp.setBottom(200f);
            sealStamp.setLeft(200f);
            sealStamp.setPageNum(1);
            sealStamp.setSignCode(signerId);

            SignatureParam signatureParam = new SignatureParam();
            signatureParam.setFileBase64(pdfBase64);
            signatureParam.setSealStamps(Collections.singletonList(sealStamp));
            signatureParam.setPage(PdfUtils.getPdfPage(pdfFile));
            pdfUrl = remoteLetterService.doSignature(signatureParam);
//            String signFileBase64 = SignatureUtils.doSignature(sealParameters);
//            MultipartFile multipartFile = Base64Utils.decoderBase64File(signFileBase64);
//            R<SysFile> r = remoteFileService.upload(multipartFile);
//            if (r.getCode() == R.SUCCESS) {
//                log.info("PDF上传路径："+r.getData().getUrl());
//                pdfUrl =  r.getData().getUrl();
//            }
//            else {
//                throw new ServiceException(r.getMsg());
//            }
        }catch (Exception e){
            log.error("空白PDf生成错误", e);
            if (e.getMessage().contains("签名人未找到")){throw new ServiceException("该签章Id不存在");}
            else {
//                throw new ServiceException("空白PDf生成错误:" + e.getMessage());
                throw new ServiceException("该签章Id不存在");
            }
        }finally {
            if (pdfFile!=null){
                FileDownloadUtils.deletedTempFile(pdfFile);}
        }
        if (pdfUrl==null){
//            throw new ServiceException("空白PDf生成错误") ;
            throw new ServiceException("该签章Id不存在") ;
        }
        else {return pdfUrl;}
    }

    @Override
    public void updateById(LawSign record) {
        record.setCreateBy(null);
        record.setCreateById(null);
        record.setCreateTime(null);
        //签章ID 不能更新
        record.setSignCode(null);
        if (record.getSignPic() != null){
        String imageBase64 = Base64Utils.getImgUrlToBase64(record.getSignPic());
        record.setSignPicStr(imageBase64);
        }
        if (record.getSignPic() == null){
            record.setSignPic(null);
            record.setSignPicStr(null);
        }
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        this.baseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void deleteById(Long id) {
        LawSign record=new LawSign();
        record.setId(id);
        record.setDelFlag(BaseConstant.DelFlag_Delete);
        this.updateById(record);
    }

    @Override
    public LawSign getById(Long id) {
        return this.baseMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<LawSign> selectList(LawSign record) {
        if(record!=null){
            if (record.getCreateId()==null){
                record.setCreateId(SecurityUtils.getTeamId());
            }
        }
        return this.baseMapper.selectList(record);
    }
}
