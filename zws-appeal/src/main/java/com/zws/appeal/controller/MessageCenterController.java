package com.zws.appeal.controller;

import cn.hutool.core.date.DateUtil;
import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.security.annotation.RequiresLogin;
import com.zws.common.security.annotation.RequiresPermissions;
import com.zws.appeal.domain.Employees;
import com.zws.appeal.enums.BusinessType;
import com.zws.appeal.service.SettingsService;
import com.zws.appeal.utils.Log;
import com.zws.appeal.utils.TokenInformation;
import com.zws.system.api.RemoteMessageService;
import com.zws.system.api.domain.TeamMessageCenter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 消息中心
 *
 * <AUTHOR>
 * @date ：Created in 2022/6/4 23:33
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/message")
public class MessageCenterController {

    @Autowired
    private RemoteMessageService remoteMessageService;
    @Autowired
    private SettingsService settingsService;

    /**
     * 写入发布消息信息
     *
     * @param messageCenter
     * @return
     */
    @Log(title = "消息中心-写入发布消息信息", businessType = BusinessType.INSERT)
    @RequiresLogin
    @RequiresPermissions("message:system:publish")
    @RequestMapping(value = "/insertMessageCenter", method = RequestMethod.POST)
    public AjaxResult add(@RequestBody TeamMessageCenter messageCenter) {
        if (ObjectUtils.isEmpty(messageCenter.getMessageTitle()) || messageCenter.getMessageTitle().length() > 128) {
            throw new GlobalException("标题文本不能为空,标题长度不能超过128个字符");
        }
        String pushTimeStr = messageCenter.getPushTimeStr();
        if (messageCenter.getPushMode() == 2) {
            if (!ObjectUtils.isEmpty(pushTimeStr)) {
                Date date = DateUtil.parse(pushTimeStr, "yyyy-MM-dd HH:mm");
                messageCenter.setPushTime(date);
            } else {
                throw new GlobalException("定时推送时间不能为空");
            }
        }
        messageCenter.setFounder(TokenInformation.getUsername());
        messageCenter.setCreationtime(new Date());
        messageCenter.setCreateId(TokenInformation.getCreateid());
        R<Long> r = remoteMessageService.createMessageCenter(messageCenter, SecurityConstants.INNER);
        if (r.getCode() == R.SUCCESS) {
            return AjaxResult.success("添加成功");
        } else {
            return AjaxResult.error(r.getMsg());
        }
    }

    /**
     * 根据登陆人id查询该团队所有员工信息
     *
     * @return
     */
    @RequiresLogin
    @RequestMapping(value = "/selectUserByCreateId", method = RequestMethod.GET)
    public AjaxResult selectListWithUserByCreateId() {
        List<Employees> employees = settingsService.selectUserByCreateId();
        return AjaxResult.success("查询成功", employees);
    }

}
