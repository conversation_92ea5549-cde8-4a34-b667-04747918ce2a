package com.zws.appeal.controller.letterDoc.letter.pojo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批扣文书
 * <AUTHOR>
 * @date ：Created in 2023/12/12 20:54
 */
@Data
public class BatchDoc {

    /**
     * 选择的案件id集合
     */
    @NotEmpty(message = "案件不能为空")
    private List<Long> caseIds;

    /**
     * 选择的合作机构id
     */
    @NotNull(message = "请选择法院")
    private Long lawAgencyId;

    /**
     * 选择的文书模板id集合
     */
    @NotEmpty(message = "请选择文书")
    private List<Long> letterTemplateIds;

}
