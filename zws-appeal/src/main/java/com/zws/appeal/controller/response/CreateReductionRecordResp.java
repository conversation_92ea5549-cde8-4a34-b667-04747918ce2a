package com.zws.appeal.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 团队申请减免查询返回实体类
 */
@Data
public class CreateReductionRecordResp implements Serializable {

    /**
     * 该案件是否属于本团队(0-不属于,1-属于)
     */
    private Integer button;

    /**
     * 申请表id
     */
    private Long id;

    /**
     * 案件id
     */
    private Long caseId;

    /**
     * 客户姓名
     */
    private String clientName;

    /**
     * 客户身份证号
     */
    private String clientIdcard;
    /**
     * 证件类型
     */
    private String clientIdType;
    /**
     * 委案批号
     */
    private String entrustingCaseBatchNum;

    /**
     * 催收员名称
     */
    private String odvName;

    /**
     * 委按日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entrustingCaseDate;

    /**
     * 退按日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date returnCaseDate;

    /**
     * 减免后金额
     */
    private BigDecimal amountAfterDeduction;

    /**
     * 审核状态
     */
    private String state;

    /**
     * 审核时间
     */
    private Date updateTime;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 申请时间
     */
    private String applyDate;

    /**
     * 申请原因
     */
    private String reason;

    /**
     * 减免申请时的 剩余应还债权金额
     */
    private String remainingDue;
}
