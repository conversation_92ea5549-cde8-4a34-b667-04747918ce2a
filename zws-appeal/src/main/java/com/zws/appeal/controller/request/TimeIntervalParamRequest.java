package com.zws.appeal.controller.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zws.common.core.exception.GlobalException;
import lombok.Data;

import java.util.Date;

/**
 * 记录查询日期区间实体类
 *
 * @Author: 马博新
 * @DATE: Created in 2022/9/13 14:54
 */
@Data
public class TimeIntervalParamRequest {

    /**
     * 日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recordTime1;

    /**
     * 日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recordTime2;

    /**
     * 日期开始-(查询用)
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 日期结束-(查询用)
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 团队id
     */
    private Long teamId;


//-----------------------------------------------------------------------------------

    /**
     * 服务品质-分数
     */
    private Integer serviceQuality;

    /**
     * 服务品质-备注
     */
    private String serviceRemarks;

    /**
     * 信息安全管理-分数
     */
    private Integer informationSafety;

    /**
     * 信息安全管理-备注
     */
    private String informationRemarks;

    /**
     * 合规管理-分数
     */
    private Integer complianceManagement;

    /**
     * 合规管理-备注
     */
    private String complianceRemarks;

    /**
     * 综合评分
     */
    private Integer comprehensiveScore;

    public void setServiceRemarks(String serviceRemarks) {
        if (serviceRemarks.length() > 300) {
            throw new GlobalException("(服务品质)备注-字数限制300字");
        }
        this.serviceRemarks = serviceRemarks;
    }

    public void setInformationRemarks(String informationRemarks) {
        if (informationRemarks.length() > 300) {
            throw new GlobalException("(信息安全管理)备注-字数限制300字");
        }
        this.informationRemarks = informationRemarks;
    }

    public void setComplianceRemarks(String complianceRemarks) {
        if (complianceRemarks.length() > 300) {
            throw new GlobalException("(合规管理)备注-字数限制300字");
        }
        this.complianceRemarks = complianceRemarks;
    }

    public void setRecordTime1(Date recordTime1) {
        this.recordTime1 = recordTime1;
    }

    public void setRecordTime2(Date recordTime2) {
        this.recordTime2 = recordTime2;
    }
}
