package com.zws.appeal.controller.appealController;

import cn.hutool.core.bean.BeanUtil;
import com.zws.appeal.agservice.AgCaseService;
import com.zws.appeal.agservice.AgLawsuitService;
import com.zws.appeal.domain.MessageTemplatePojo;
import com.zws.appeal.pojo.SendRecordsPojos;
import com.zws.appeal.pojo.appeal.FreezeCasePojo;
import com.zws.appeal.pojo.appeal.StageProgressVo;
import com.zws.appeal.service.appeal.IFreezeRecordService;
import com.zws.appeal.utils.TokenInformation;
import com.zws.common.core.constant.CacheConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.domain.sms.ThreadEntityPojo;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.redis.service.RedisService;
import com.zws.common.security.annotation.RequiresLogin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 诉讼保全模块
 *
 * @Author：liuxifeng
 * @Date：2024/6/19 18:08
 * @Describe：保全冻结资产
 */
@RestController
@CrossOrigin
@RequestMapping(value = "/freeze")
public class FreezeController extends BaseController {

    @Autowired
    private IFreezeRecordService freezeRecordService;
    @Autowired
    private AgCaseService agCaseService;
    @Autowired
    private RedisService redisService;

    /**
     * 列表查询
     * @param pojo
     * @return
     */
    @GetMapping("/selectList")
    public TableDataInfo selectList(FreezeCasePojo pojo){
        startPage();
        List<FreezeCasePojo> list  = freezeRecordService.selectList(pojo);
        return getDataTable(list);
    }

    /**
     * 统计剩余应还债权金额 计算
     * @param pojo
     * @return
     */
    @PostMapping("/selectWithMoney")
    public AjaxResult selectWithMoney(@RequestBody FreezeCasePojo pojo){
        Map<String,Object> map = freezeRecordService.selectWithMoney(pojo);
        return AjaxResult.success(map);
    }

    /**
     * 批量导出
     * @param response
     * @param pojo
     * @throws UnsupportedEncodingException
     */
    @PostMapping("/exportWithFreeze")
    public void exportWithFreeze(HttpServletResponse response, @RequestBody FreezeCasePojo pojo) throws UnsupportedEncodingException {
        List<FreezeCasePojo> list= freezeRecordService.selectList(pojo);
        ExcelUtil util=new ExcelUtil(FreezeCasePojo.class);
        String fileName = "诉讼保全.xlsx";
        response.addHeader("Content-Disposition", "attachment;filename= " + URLEncoder.encode(fileName, "utf-8"));
        util.exportExcel(response, list, "诉讼保全");
    }

    /**
     * 调解流程-诉讼流程 统一申请诉保
     * @param pojo
     * @return
     */
    @PostMapping("/batchFreeze")
    public AjaxResult batchFreeze(@RequestBody Object pojo){
        Map<String, Object> map = BeanUtil.beanToMap(pojo);
        int i = freezeRecordService.batchFreezeCase(map);
        return AjaxResult.success();
    }

    /**
     * 阶段性进展
     *
     * @param caseId 案例id
     * @return
     */
    @GetMapping("/stageProgress")
    public R<StageProgressVo> stageProgress( Long caseId){
        if (caseId == null) {
            return R.fail("案件ID错误");
        }
        StageProgressVo stageProgressVo = freezeRecordService.stageProgress(caseId);
        return R.ok(stageProgressVo);
    }

}
