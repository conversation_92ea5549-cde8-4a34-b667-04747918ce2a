package com.zws.appeal.controller.letterDoc.letter.agservice;


import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zws.appeal.controller.letterDoc.letter.domain.TemplateApprove;
import com.zws.appeal.controller.letterDoc.letter.mapper.TemplateApproveMapper;
import com.zws.appeal.controller.letterDoc.letter.pojo.DocumentTemplatePojo;
import com.zws.appeal.controller.letterDoc.letter.service.IMessageService;
import com.zws.appeal.service.IReorganizationService;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.domain.R;
import com.zws.common.core.enums.MessageFormats;
import com.zws.common.core.enums.ProcessingStatus;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.utils.PageUtils;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.common.domain.ZwsDataPageVo;
import com.zws.common.domain.ZwsLoginUser;
import com.zws.common.utils.ZwsObjectTurnUtil;
import com.zws.standardmain.domain.dto.ZwsJgVerificationDto;
import com.zws.standardmain.domain.dto.operate.ZwsJgAddApproveOperateDto;
import com.zws.standardmain.domain.dto.operate.ZwsJgApprovePageOperateDto;
import com.zws.standardmain.domain.entity.ZwsApproveRecord;
import com.zws.standardmain.service.ZwsJgApproveMainCustomService;
import com.zws.system.api.RemoteLetterService;
import com.zws.system.api.domain.DocumentTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 文书模板审批
 */
@Service
@Slf4j
public class TemplateApproveMainCustomService implements ZwsJgApproveMainCustomService {


    @Autowired
    private RemoteLetterService remoteLetterService;
    @Autowired
    private IMessageService messageService;
    @Autowired
    private TemplateApproveMapper templateApproveMapper;

    private static final String APPROVE_DOCUMENT_TEMPLATE = "template";

    /**
     * 添加/修改文书模板审批
     * @param jgAddApproveOperateDto
     * @param loginUser
     */
    @Override
    public void jgAddApprove(ZwsJgAddApproveOperateDto jgAddApproveOperateDto, ZwsLoginUser loginUser) {
        DocumentTemplate template = ZwsObjectTurnUtil.parentTurnSon(
                jgAddApproveOperateDto.getApproveData(),
                DocumentTemplate.class
        );
        if (APPROVE_DOCUMENT_TEMPLATE.equals(jgAddApproveOperateDto.getApproveCode())) {
            if (template.getId() == null) {
                // 添加文书审批记录
                template.setApproveId(jgAddApproveOperateDto.getApproveId());
                template.setTeamId(loginUser.getTeamId());
                template.setStatus(0);//启用
                template.setHandlingStatus(ProcessingStatus.WAIT_PROCESS.getCode());
                remoteLetterService.add(template);

            } else {
                // 修改已通过的审批
                Integer id = template.getId();
                DocumentTemplate oldTemplate = templateApproveMapper.selectById(Long.valueOf(id));
                templateApproveMapper.remove(Long.valueOf(id));
                templateApproveMapper.removeTemplateApprove(Long.valueOf(oldTemplate.getApproveId()));
                template.setId(null);
                template.setApproveId(jgAddApproveOperateDto.getApproveId());
                template.setTeamId(loginUser.getTeamId());
                template.setStatus(0);
                template.setHandlingStatus(ProcessingStatus.WAIT_PROCESS.getCode());
                remoteLetterService.add(template);
            }

            //模板表中插入一条数据
            TemplateApprove templateApprove = new TemplateApprove();
            templateApprove.setApproveId(jgAddApproveOperateDto.getApproveId());
            templateApprove.setTemplateName(template.getTemplateName());
            templateApprove.setTemplateType(template.getClassifyLabel());// 模板类型
            templateApprove.setApproveStatus(ProcessingStatus.WAIT_PROCESS.getCode());
            templateApprove.setPreviewUrl(template.getPreviewUrl());
            templateApprove.setPreviewPages(template.getPreviewPages());
            templateApprove.setApproveTemplateCode("document_template");
            templateApprove.setCreatedById(loginUser.getUserid());
            templateApprove.setCreatedBy(loginUser.getUsername());
            templateApprove.setCreatedTime(new Date());
            templateApprove.setDelFlag(BaseConstant.DelFlag_Being);
            templateApproveMapper.add(templateApprove);
        }

        //发送消息
        ZwsApproveRecord zwsApproveRecord = templateApproveMapper.selectApproveByApproveId(template.getApproveId());
        Long teamId = template.getTeamId();
        Integer teamIdStr = null;
        if (teamId != null){
            teamIdStr = teamId.intValue();
        }
        messageService.pushReminder(MessageFormats.APPROVAL_TEMPLATE,
                zwsApproveRecord.getApplicantType(), zwsApproveRecord.getApplicantId(), teamIdStr);

    }

    /**
     * 文书模板审批通过
     * @param jgVerification
     * @param loginUser
     */
    @Override
    public void jgApproveFinalPass(ZwsJgVerificationDto jgVerification, ZwsLoginUser loginUser) {
        if (ObjectUtils.isEmpty(jgVerification.getApproveIds())){
            return;
        }
        List<Long> approveIds = jgVerification.getApproveIds();
        DocumentTemplate documentTemplate = new DocumentTemplate();
        documentTemplate.setApproveIds(approveIds);
        documentTemplate.setUserId(loginUser.getUserid());
        TableDataInfo list = remoteLetterService.listNotPaging(documentTemplate);
        //PageInfo<DocumentTemplate> list = ajaxResultR.getData();
        List<DocumentTemplate> templateList = BeanUtil.copyToList(list.getRows(), DocumentTemplate.class);
        //List<DocumentTemplate> templateList = ajaxResultR.getData();
        for (DocumentTemplate template : templateList) {
            template.setHandlingStatus(ProcessingStatus.AGREE.getCode());
            //更新记录
            remoteLetterService.edit(documentTemplate);

            //发送消息
            ZwsApproveRecord zwsApproveRecord = templateApproveMapper.selectApproveByApproveId(template.getApproveId());
            Long teamId = template.getTeamId();
            Integer teamIdStr = null;
            if (teamId != null){
                teamIdStr = teamId.intValue();
            }
            messageService.pushReminder(MessageFormats.APPROVAL_TEMPLATE,
                    zwsApproveRecord.getApplicantType(), zwsApproveRecord.getApplicantId(), teamIdStr);
        }
    }

    /**
     * 文书模板审批不通过
     * @param jgVerification
     * @param loginUser
     */
    @Override
    public void jgApproveNotPass(ZwsJgVerificationDto jgVerification, ZwsLoginUser loginUser) {
        List<Long> approveIds = jgVerification.getApproveIds();
        DocumentTemplate documentTemplate = new DocumentTemplate();
        documentTemplate.setApproveIds(approveIds);
        documentTemplate.setUserId(loginUser.getUserid());
        TableDataInfo list = remoteLetterService.listNotPaging(documentTemplate);
        //PageInfo<DocumentTemplate> list = ajaxResultR.getData();
        List<DocumentTemplate> templateList = BeanUtil.copyToList(list.getRows(), DocumentTemplate.class);
        for (DocumentTemplate template : templateList) {
            template.setHandlingStatus(ProcessingStatus.NOT_AGREE.getCode());
            //更新记录
            remoteLetterService.edit(documentTemplate);

            //发送消息
            ZwsApproveRecord zwsApproveRecord = templateApproveMapper.selectApproveByApproveId(template.getApproveId());
            Long teamId = template.getTeamId();
            Integer teamIdStr = null;
            if (teamId != null){
                teamIdStr = teamId.intValue();
            }
            messageService.pushReminder(MessageFormats.APPROVAL_TEMPLATE,
                    zwsApproveRecord.getApplicantType(), zwsApproveRecord.getApplicantId(), teamIdStr);
        }
    }

    /**
     * 文书模板审批列表
     * @param pageOperateDto
     * @return
     */
    @Override
    public ZwsDataPageVo jgQueryApproveList(ZwsJgApprovePageOperateDto pageOperateDto) {
        List<Long> approveIds = pageOperateDto.getApproveIds();
        List<Long> approveProcessIds = pageOperateDto.getApproveProcessIds();
        DocumentTemplate documentTemplate = ZwsObjectTurnUtil.parentTurnSon(pageOperateDto.getApproveData(), DocumentTemplate.class);


        //参数处理
        if (!ObjectUtils.isEmpty(documentTemplate.getExamineStateArr())){
            ArrayList<Integer> list = new ArrayList<>();
            for (String examineState : documentTemplate.getExamineStateArr()){
                if(examineState.equals("待审核")){
                    list.add(ProcessingStatus.WAIT_PROCESS.getCode());
                }
                if (examineState.equals("审核中")){
                    list.add(ProcessingStatus.UNDER_REVIEW.getCode());
                }
                if (examineState.equals("已通过")){
                    list.add(ProcessingStatus.AGREE.getCode());
                }
                if (examineState.equals("未通过")){
                    list.add(ProcessingStatus.NOT_AGREE.getCode());
                }
            }
            documentTemplate.setExamineStateList(list);
        }

        documentTemplate.setApproveIds(approveIds);
        documentTemplate.setApproveProcessIds(approveProcessIds);
        documentTemplate.setUserId(pageOperateDto.getLoginUser().getUserid());

        PageHelper.startPage(pageOperateDto.getPageNum(), pageOperateDto.getPageSize());

        //查询
        TableDataInfo list = remoteLetterService.listNotPaging(documentTemplate);
        List<DocumentTemplatePojo> pojoList = BeanUtil.copyToList(list.getRows(), DocumentTemplatePojo.class);
        PageInfo<DocumentTemplatePojo> pageInfo = new PageInfo<>(pojoList);
        pageInfo.setTotal(list.getTotal());
        ZwsDataPageVo pageDataTable = PageUtils.getPageDataTable(pojoList, pageInfo);

        return pageDataTable;

    }


}


