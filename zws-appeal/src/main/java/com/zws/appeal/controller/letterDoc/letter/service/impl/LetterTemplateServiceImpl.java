package com.zws.appeal.controller.letterDoc.letter.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.itextpdf.text.Image;
import com.zws.appeal.controller.letterDoc.law.domain.LawSign;
import com.zws.appeal.controller.letterDoc.letter.mapper.LawMapper;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.Option;
import com.zws.common.core.exception.GlobalException;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.signature.Stamp;
import com.zws.common.core.utils.FieldEncryptUtil;
import com.zws.common.core.utils.IdUtils;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.pdf.PdfUtils;
import com.zws.common.core.utils.pdf.po.PdfFile;
import com.zws.common.security.utils.SecurityUtils;
import com.zws.system.api.RemoteFileService;
import com.zws.appeal.controller.letterDoc.letter.PDFFileUtils;
import com.zws.appeal.controller.letterDoc.letter.SSLUtils;
import com.zws.appeal.controller.letterDoc.letter.agservice.LetterTemplateAgService;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;
import com.zws.appeal.controller.letterDoc.letter.mapper.LetterTemplateMapper;
import com.zws.appeal.controller.letterDoc.letter.pojo.CaseInfoPojo;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterTemplateService;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterVariableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import javax.imageio.stream.ImageInputStream;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 函件模板 service
 * <AUTHOR>
 * @date ：Created in 2023/11/23 20:33
 */
@Slf4j
@Service
public class LetterTemplateServiceImpl implements ILetterTemplateService {

    @Autowired
    private LetterTemplateMapper baseMapper;
    @Autowired
    private LetterTemplateAgService templateAgService;
    @Autowired
    private ILetterVariableService letterVariableService;
    @Autowired
    private RemoteFileService remoteFileService;
    @Autowired
    private LawMapper lawMapper;


    @Override
    public Long insert(LetterTemplate record) {

//        record = checkPosition(record);

        Set<String> variables = findTemplateVariable(record);
        String variable = String.join(",", variables);
        record.setTemplateVariable(variable);

        PdfFile pdfFile = templateAgService.createTemplatePreview(record);
        String previewUrl = pdfFile.getUrl();
        record.setPreviewUrl(previewUrl);
        record.setPreviewPages(pdfFile.getPages());
        record.setId(null);

        //record = checkPosition(record);

        record.setCreateBy(SecurityUtils.getUsername());
        record.setCreateById(SecurityUtils.getUserId());
        record.setCreateTime(new Date());
        record.setDelFlag(BaseConstant.DelFlag_Being);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        record = checkPosition(record);
//        record.setPositionData(JSONObject.toJSONString(record.getPositionList()));
        if (this.baseMapper.insert(record) > 0) {
            record.setTemplateCode(String.valueOf(record.getId()));
            this.baseMapper.updateByPrimaryKeySelective(record);
        }
        return record.getId();
    }

    /**
     * 校验签章坐标对象
     *
     * @param template
     * @return
     */
    private LetterTemplate checkPosition(LetterTemplate template) {
        if (template.getPositionList() != null && template.getPositionList().size() > 0) {
            template.getPositionList().forEach(s -> {
                if (s.getLawId() == null) {
                    throw new ServiceException("律所签章Id不能为空");
                }
                if (s.getSignId() != null) {
                    s.setSignerId(lawMapper.getSignCode(s.getSignId()));
                } else {
                    s.setSignerId(lawMapper.getSignCode(s.getLawId()));
                }
            });
            //获取图片左下角坐标,Y轴坐标需要减去图片高度
//            template.setPositionList(PdfUtils.getPosition(template.getPositionList()));
          template.setPositionData(JSONObject.toJSONString(template.getPositionList()));
            return template;
        } else {
            throw new ServiceException("签章不能为空");
        }
    }

    /**
     * 查找模板变量
     *
     * @param template
     * @return
     */
    private Set<String> findTemplateVariable(LetterTemplate template) {
        List<Option> allVariableNames = letterVariableService.getVariableNameVoList();
        Set<String> variableNames = new HashSet<>();
        String bodyContent = template.getBodyContent();
        HashMap<Integer, String> hash = new HashMap<>();
        List<Integer> list = new ArrayList<>();
        for (Option option : allVariableNames) {
            String code = (String) option.getCode();
            String info = (String) option.getInfo();
            int i = bodyContent.indexOf(info);
            if (i >= 0) {
                list.add(i);
                hash.put(i, code);
            }
        }
        if (list.size() > 0) {
            Collections.sort(list);
            list.forEach(s -> {
                variableNames.add(hash.get(s));
            });
        }
        return variableNames;
    }

    @Override
    public void updateById(LetterTemplate record) {
        record.setCreateBy(null);
        record.setCreateById(null);
        record.setCreateTime(null);


        Set<String> variables = findTemplateVariable(record);
        String variable = String.join(",", variables);
        record.setTemplateVariable(variable);

        PdfFile pdfFile = new PdfFile();
        List<Stamp> stamp = record.getPositionList();
        for (Stamp stamp1 : stamp) {
            if (stamp1.getBottom()==null && stamp1.getLeft() == null){
                pdfFile = templateAgService.createTemplatePdf(record);
            }else {
                pdfFile = templateAgService.createTemplatePreview(record);
            }
        }
//        PdfFile pdfFile = templateAgService.createTemplatePreview(record);
        String previewUrl = pdfFile.getUrl();
        record.setPreviewUrl(previewUrl);
        record.setPreviewPages(pdfFile.getPages());

        //record.setPositionData(JSONObject.toJSONString(record.getPositionList()));
        record = checkPosition(record);
        record.setUpdateBy(SecurityUtils.getUsername());
        record.setUpdateById(SecurityUtils.getUserId());
        record.setUpdateTime(new Date());
        this.baseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void deleteById(Long id) {
        LetterTemplate record=new LetterTemplate();
        record.setId(id);
        record.setDelFlag(BaseConstant.DelFlag_Being);
        this.updateById(record);
    }

    @Override
    public LetterTemplate getById(Long id) {
        return this.baseMapper.selectByPrimaryKey(id);
    }

    @Override
    public boolean checkUniqueName(LetterTemplate template) {
        if (StrUtil.isBlank(template.getTemplateName())) {
            throw new ServiceException("模板名称不能为空");
        }
        if (StringUtils.isEmpty(template.getTemplateName())) {
            return true;
        }
        LetterTemplate temp = new LetterTemplate();
        temp.setTemplateName(template.getTemplateName());
        temp.setNotId(template.getId());
        temp.setTeamId(template.getTeamId());
        return this.baseMapper.selectSmallList(temp).size() == 0;
    }

    @Override
    public List<LetterTemplate> selectList(LetterTemplate record) {
        record.setTeamId(SecurityUtils.getTeamId());
        return this.baseMapper.selectList(record);
    }

    @Override
    public List<Option> getOptions(LetterTemplate record) {
        return this.baseMapper.selectOptions(record);
    }

    @Override
    public List<LetterTemplate> selectList1(LetterTemplate template) {
        template.setTeamId(SecurityUtils.getTeamId());
        List<LetterTemplate> list = this.baseMapper.selectSmallList(template);
        for (LetterTemplate temp : list) {
            if (StringUtils.isNotEmpty(temp.getPreviewUrl())) {
                String[] split = temp.getPreviewUrl().split("/");
                temp.setFileName(split[split.length - 1]);
            }
        }
        return list;
    }

    @Override
    public String getContent(LetterTemplate template, boolean isPreview) {
        //正文内容
        String bodyContent = template.getBodyContent();
        if (bodyContent == null) {
            bodyContent = "";
        }
        //尾部内容
//        String tailContent = template.getTailContent();
//        if (tailContent == null) {
//            tailContent = "";
//        }
        //机构名称
//        String lawName = lawMapper.getLawName(TokenInformation.getCreateid());
        //lawMapper.getLawName(template.getLawId());
        //String lawName=lawMapper.getAttorneyName(template.getLawId());
//        if (lawName == null) {
//            lawName = "";
//        }
        //代理律所名称
//        String signName = "";
//        if (template.getSignId() != null) {
//            signName = lawMapper.getAttorneyName(template.getSignId());
//        }
        //当前时间
//        String dt = DateUtils.dateTimeNow("yyyy年MM月dd日");

        String b = "";
      /*  if (isPreview){
            b="font-weight: 700 !important;";
        }*/

        StringBuilder builder = new StringBuilder();
        String content = "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<meta charset=\"UTF-8\"></meta>" +
                "\t<body style=\" font-family:宋体 ; " + b + " \">\t\t\t\t\t\t\t\n" +
                "\t<div>\n" +
                "\t\t{}\n" +
                "\t</div>\n";
//                "\t<div style=\"position: relative;margin-top: 20px; margin-bottom: 20px;font-family:宋体;\">\n" +
//                "\t\t<div style=\" text-align: center;float: right;\">\n" +
//                "\t\t\t<div >{}</div>\n" ;
//        String content1 =
//                "\t\t\t<br/>\n" +
//                "\t\t\t<div style=\"\">代理律师:{}</div>\n"+
//                "\t\t\t<br/>\n" ;
        String content2 =
//                "\t\t\t<div style=\" \">{}</div>\n" +
//                "\t\t\t<br/>\n" +
//                "\t\t</div>\n" +
//                "\t</div>\n" +
                "\t<div style=\"margin-top: 30px;margin-bottom: 20px;float: left;width: 100%;\">\n" +
//                        "\t\t{}\n" +
                        "\t</div>\t \n" +
                        "\t<style>\t \n" +
                        "\tdiv{display:block !important;}\t \n" +
                        "\tspan{font-family:宋体 !important;}\t \n" +
                        "\t</style>\t \n" +
                        "\t</body>\n" +
                        "</html>";
        builder.append(content);
        //if(signName!=null && signName!=""){ builder.append(content1); }
        builder.append(content2);

        String cu = null;
        cu = StrUtil.format(builder.toString(), bodyContent);
        /*if (signName!=null && signName!=""){
             cu=StrUtil.format(builder.toString(),bodyContent,lawName,signName,dt,tailContent);
        }else {
             cu=StrUtil.format(builder.toString(),bodyContent,lawName,dt,tailContent);
        }*/

        //取消干扰的属性
        /* cu= cu.replace("align=\"justify\"","");
        cu= cu.replace("align=\"end\"","");
        cu= cu.replace("align=\"center\"","");
        cu= cu.replace("align=\"initial\"","");
        cu= cu.replace("align=\"start\"","");
        cu= cu.replace("align=\"right\"","");*/

        //log.info("PDF内容："+cu);
        return cu;
    }

    @Override
    public HashMap<String, Object> getCaseInfo(Long caseId) {
        CaseInfoPojo caseInfo = baseMapper.getCaseInfo(caseId);
        //已催收金额-计算该案件的累计已催回金额(审批通过的金额) amount_called_back
        BigDecimal repaymentSum = baseMapper.selectAmountCalledBack(caseId);

        caseInfo.setClientPhone(FieldEncryptUtil.decrypt(caseInfo.getClientPhone()));
        caseInfo.setClientIdcard(FieldEncryptUtil.decrypt(caseInfo.getClientIdcard()));
        caseInfo.setPlaceOfWork(FieldEncryptUtil.decrypt(caseInfo.getPlaceOfWork()));
        caseInfo.setClientName(FieldEncryptUtil.decrypt(caseInfo.getClientName()));
        caseInfo.setSecurityName(FieldEncryptUtil.decrypt(caseInfo.getSecurityName()));
        caseInfo.setSecurityIdNum(FieldEncryptUtil.decrypt(caseInfo.getSecurityIdNum()));
        caseInfo.setSecurityPhone(FieldEncryptUtil.decrypt(caseInfo.getSecurityPhone()));

        caseInfo.setBankCardNumber(FieldEncryptUtil.decrypt(caseInfo.getBankCardNumber()));
        caseInfo.setWorkingAddress(FieldEncryptUtil.decrypt(caseInfo.getWorkingAddress()));
        caseInfo.setResidentialAddress(FieldEncryptUtil.decrypt(caseInfo.getResidentialAddress()));


        HashMap<String, Object> map = new HashMap<>();
        //基本信息
        map.put("借款人姓名",caseInfo.getClientName());
        map.put("证件类型",caseInfo.getClientIdType());
        map.put("证件号码",caseInfo.getClientIdcard());
        map.put("手机号",caseInfo.getClientPhone());
        map.put("银行卡号",caseInfo.getBankCardNumber());
        map.put("户籍地址",caseInfo.getRegisteredAddress());
        map.put("性别",caseInfo.getClientSex());
        map.put("年龄",caseInfo.getClientAge());
        map.put("出生日期",caseInfo.getClientBirthday());
        map.put("担保人",caseInfo.getSecurityName());
        map.put("担保人证件类型",caseInfo.getSecurityIdType());
        map.put("担保人证件号码",caseInfo.getSecurityIdNum());
        map.put("担保人电话",caseInfo.getSecurityPhone());
        map.put("单位名称",caseInfo.getPlaceOfWork());
        map.put("单位地址",caseInfo.getWorkingAddress());
        map.put("居住地址",caseInfo.getResidentialAddress());
        map.put("家庭住址",caseInfo.getHomeAddress());
        map.put("开户行",caseInfo.getBankName());
        map.put("职业",caseInfo.getOccupation());
        map.put("QQ",caseInfo.getQq());
        map.put("微信",caseInfo.getWeixin());
        map.put("邮箱",caseInfo.getMailbox());
        map.put("资产编号",caseInfo.getAssetNo());
        map.put("婚姻状况",caseInfo.getMaritalStatus());


        //案件信息
        map.put("初始债权总额",caseInfo.getEntrustMoney());
        map.put("贷款金额",caseInfo.getLoanMoney());
        map.put("初始本息余额",caseInfo.getYcInterestBalance());
        map.put("初始利息余额",caseInfo.getInterestMoney());
        map.put("初始本金余额",caseInfo.getResidualPrincipal());
        map.put("初始费用",caseInfo.getServiceFee());
        map.put("垫付费用",caseInfo.getYcDisbursement());
        map.put("逾期日期",caseInfo.getOverdueStart());
        map.put("借据号",caseInfo.getContractNo());
        map.put("合同号",caseInfo.getYcContractNo());
        map.put("币种",caseInfo.getYcCurrencies());
        map.put("贷款用途",caseInfo.getYcPurpose());
        map.put("贷款利率",caseInfo.getYcLendingRate());
        map.put("逾期天数",caseInfo.getYcOverdueDays());
        map.put("罚息利率",caseInfo.getYcDefaultRate());
        map.put("贷款本金",caseInfo.getLoanPrincipal());
        map.put("每月应还",caseInfo.getRepaymentMonthly());
        map.put("贷款期数",caseInfo.getLoanPeriods());
        map.put("已还期数",caseInfo.getAlreadyPeriods());
        map.put("未还期数",caseInfo.getNotPeriods());
        map.put("每月还款日",caseInfo.getRepaymentDate());
        map.put("最后还款日期",caseInfo.getAmountFinalDate());
        map.put("债权机构",caseInfo.getLoanInstitution());
        map.put("放款分(支)行",caseInfo.getYcLoanBank());
        map.put("业务类型",caseInfo.getYcBusinessType());
        map.put("产品类型",caseInfo.getProductType());
        map.put("合同金额",caseInfo.getYcContractMoney());
        map.put("贷款期限",caseInfo.getYcLoanTerm());
        map.put("贷款发放日",caseInfo.getYcLoanIssuanceDate());
        map.put("贷款到期日",caseInfo.getYcLoanMaturityDate());
        map.put("五级分类",caseInfo.getYcFiveLevel());
        map.put("还款渠道",caseInfo.getYcRepaymentMethod());
        map.put("诉讼状态",caseInfo.getYcLitigationStatus());
        map.put("是否失信被执行人",caseInfo.getYcIsDishonest());
        map.put("是否被限制高消费",caseInfo.getYcIsLimitConsumption());
        map.put("核销日期",caseInfo.getYcWriteDate());
        map.put("最后还款金额",caseInfo.getAmountFinalRepayment());
        map.put("案件地区",caseInfo.getCaseRegion());
        map.put("账期",caseInfo.getAccountPeriod());
        map.put("基准日后还款金额",caseInfo.getYcAbdRepayment());
        map.put("基准日后本金还款",caseInfo.getYcAbdPrincipal());
        map.put("基准日后利息还款",caseInfo.getYcAbdInterest());
        map.put("退案日期",caseInfo.getReturnCaseDate());
        map.put("累计已还",repaymentSum);
        map.put("剩余应还本金",caseInfo.getSyYhPrincipal());
        map.put("剩余应还利息",caseInfo.getSyYhInterest());
        map.put("剩余应还费用",caseInfo.getSyYhFees());
        map.put("剩余应还债权总额",caseInfo.getRemainingDue());
        map.put("剩余应还罚息",caseInfo.getSyYhDefault());
        Long number = 1000000L+caseInfo.getId();
        map.put("自增编号",number.toString());
        Date nowDate = new Date();
        map.put("当前日期",DateUtil.	formatChineseDate(nowDate,false,false));
        return map;
    }

    /**
     * 根据输入流进行裁剪
     *
     * @param headerPath 缩放后的页眉
     * @param footerPath 缩放后的页脚
     * @return
     */
    @Override
    public String cutPictureByStream(String headerPath, String footerPath) {

        String path;
        boolean he = headerPath != null && headerPath != "";
        boolean fo = footerPath != null && footerPath != "";
        if (he) {
            path = headerPath;
        } else if (fo) {
            path = footerPath;
        } else {
            throw new GlobalException("页眉或页脚为空,无法裁剪");
        }

        File tempFile = null;
        String tempUrl = null;
        ImageOutputStream outPut = null;

        try (InputStream inputStream = URLUtil.url(path).openStream()) {
            //将原图等比例缩放后，再进行裁剪 downloadFile = PdfUtils.getScalePicByStream(path);
            //downloadFile =FileDownloadUtil.downloadTempFile(path);

            ImageInputStream inPut = ImageIO.createImageInputStream(inputStream);
            Image image = Image.getInstance(path);
            float heightPic = image.getHeight();
            float widthPic = image.getWidth();

            FileUtil.mkdir(FileDownloadUtils.tempFilePath);
            File file = FileUtil.file(FileDownloadUtils.tempFilePath);
            if (!file.exists()) {
                file.mkdir();
            }

            String filename = IdUtils.fastSimpleUUID() + ".png";
            tempFile = new File(file.getAbsoluteFile() + "/" + filename);

            outPut = ImgUtil.getImageOutputStream(tempFile);

            if (he) {
                ImgUtil.cut(inPut, outPut, new Rectangle(0, (int) (heightPic - 166.7f), (int) widthPic, (int) 166.7));
                tempUrl = templateAgService.fileUpload(tempFile);
            } else {
                ImgUtil.cut(inPut, outPut, new Rectangle(0, (int) (heightPic - 100), (int) widthPic, 100));
                tempUrl = templateAgService.fileUpload(tempFile);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (outPut != null) {
                    outPut.close();
                }
                if (tempFile != null) {
                    FileDownloadUtils.deletedTempFile(tempFile);
                }
            } catch (IOException e) {
                log.error(e.getMessage());
                e.printStackTrace();
            }
        }
        return tempUrl;
    }

    @Override
    public int updateEditStatus(LetterTemplate entity) {
        return baseMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public InputStream getPdfBase64(Long id) throws IOException {
        LetterTemplate letterTemplate = baseMapper.selectByPrimaryKey(id);
//        File file = new File(letterTemplate.getPreviewUrl());
//        String PdfBase64 = PDFFileUtils.PDFToBase64(file);
//        String PdfBase64 = PDFFileUtils.pdf(letterTemplate.getPreviewUrl());

        return TqOdpServiceClient(letterTemplate.getPreviewUrl());
    }

    @Override
    public LetterTemplate selectListById(LetterTemplate template) {
        return baseMapper.selectByPrimaryKey(template.getId());
    }

    @Override
    public LetterTemplate getByIdA(Long id) {
        LetterTemplate letterTemplate = this.baseMapper.selectByPrimaryKey(id);
        List<Stamp> stamps = JSONArray.parseArray(letterTemplate.getPositionData(), Stamp.class);
        List<Stamp> collect = stamps.stream().map(stamp -> {
            stamp.setSignPic(PDFFileUtils.getImgBase(stamp.getSignPic()));
            return stamp;

        }).collect(Collectors.toList());
        letterTemplate.setPositionData(JSONObject.toJSONString(collect));
        return letterTemplate;
    }

    @Override
    public InputStream createTemplatePdf(String url) throws IOException {
        return TqOdpServiceClient(url);
    }

    @Override
    public List<String> getTemplateVariable(Integer id) {
        LetterTemplate template = this.baseMapper.selectByPrimaryKey(Long.valueOf(id));
        if (template == null) {
            throw new ServiceException("模板数据查询错误");
        }
        String templateVariable = template.getTemplateVariable();
        if (StringUtils.isEmpty(templateVariable)) {
            return new ArrayList<>();
        }
        return Arrays.asList(templateVariable.split(","));
    }

    @Override
    public LawSign getSignPicBase64(Integer signId) {
        return lawMapper.getLawSignById(Long.valueOf(signId.toString()));
    }

    @Override
    public int updateLawSignById(LawSign lawSign) {
        return lawMapper.updateByPrimaryKeySelective(lawSign);
    }

    public InputStream TqOdpServiceClient(String url) throws IOException {
    HttpResponse response = HttpRequest.get(url)
            .header("Content-Type", "application/json;charset=UTF-8")
            .setSSLSocketFactory(SSLUtils.getSSLSocketFactory())
            .execute();

    return response.bodyStream();

    }


}
