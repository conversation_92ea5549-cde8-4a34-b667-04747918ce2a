package com.zws.appeal.controller.request;

import com.zws.appeal.domain.ApprovalSteps;
import lombok.Data;

import java.util.List;

/**
 * 审批设置 pojo
 *
 * <AUTHOR>
 * @date 2022-08-12
 */
@Data
public class ApprovalSettingsFormRequest {

    private List<Integer> ids;
    private List<ApprovalSteps> approvalSteps;
    /**
     * 说明
     */
    private String remarks;
    /**
     * 审核类型,0-回款审核，1-减免审核，2-分期审核，3-留案审核，4-停催审核，5-退案审核，6-分案审核,7-外访审核
     */
    private Integer approveCode;
}
