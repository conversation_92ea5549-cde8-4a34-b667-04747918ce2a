package com.zws.appeal.controller;

import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.appeal.service.CollectorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 资产包下拉
 * <AUTHOR>
 * @date 2022-08-12
 */

@Slf4j
@RestController
@CrossOrigin
@RequestMapping(value = "/collector")
public class CollectorController extends BaseController {

    @Autowired
    private CollectorService collectorService;


    /**
     * 资产包下拉框
     */
    @GetMapping("/selectProperty")
    private AjaxResult selectProperty() {
        List<String> property = collectorService.selectProperty();
        return AjaxResult.success(property);
    }


}
