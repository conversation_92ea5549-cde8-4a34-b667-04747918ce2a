
package com.zws.appeal.controller.letterDoc.letter.service.impl;

import cn.hutool.core.util.ZipUtil;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.*;
import com.zws.common.core.utils.file.FileDownloadUtils;

import com.zws.appeal.controller.letterDoc.letter.FileUtils;
import com.zws.appeal.controller.letterDoc.letter.agservice.LetterTemplateAgService;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc;
import com.zws.appeal.controller.letterDoc.letter.mapper.LetterDocMapper;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterTemplateService;
import com.zws.appeal.controller.letterDoc.letter.service.ZipDownloadService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;


import javax.servlet.http.HttpServletResponse;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;


@Slf4j
@Service
public class ZipDownloadServiceImpl implements ZipDownloadService {
    @Autowired
    private LetterDocMapper baseMapper;

    @Autowired
    private ILetterTemplateService templateService;
    @Autowired
    private LetterTemplateAgService templateAgService;

    /**
     * 批量压缩下载
     */
    @Override
    public void ZipDownload(List<Long> ids) {
        for (Long id : ids) {
            LetterDoc letterDoc = baseMapper.selectByPrimaryKey(id);
            if (Objects.isNull(letterDoc)) {
                log.info("文书未生成");
                throw new ServiceException("文书未生成");
            }
            downloadZip(letterDoc);
        }
    }
    /**
     * 保全文书压缩下载
     */
    @Override
    public void ZipDownloadDocument(LetterDoc letterDoc) {
        if(ObjectUtils.isEmpty(letterDoc.getCondition())) {
            for (Long id : letterDoc.getIds()) {
                LetterDoc letterDoc1 = baseMapper.selectByPrimaryKey(id);
                if (Objects.isNull(letterDoc1)) {
                    log.info("文书未生成");
                    throw new ServiceException("文书未生成");
                }
                //  0-诉讼文书，1-调价函，2-保全文书
                if (letterDoc1.getDocType().equals(2)) {
                    downloadZip(letterDoc1);
                } else {
                    throw new ServiceException("该文书不是保全文书");
                }
            }
        }
        if(!ObjectUtils.isEmpty(letterDoc.getCondition())){
            letterDoc.setIds(null);
            List<LetterDoc> list = baseMapper.selectAllList();
            for (LetterDoc doc : list) {
                if (Objects.isNull(doc)) {
                    log.info("文书未生成");
                    throw new ServiceException("文书未生成");
                }
                //  0-诉讼文书，1-调价函，2-保全文书
                if (doc.getDocType().equals(2)) {
                    downloadZip(doc);
                } else {
                    throw new ServiceException("该文书不是保全文书");
                }
            }
            }


    }
    /**
     * 调价函压缩下载
     */
    @Override
    public void ZipDownloadMediate(HttpServletResponse response,LetterDoc letterDoc) throws Exception {

        List<File> fileList = new ArrayList<>();

        if(ObjectUtils.isEmpty(letterDoc.getCondition())) {
            for (Long id : letterDoc.getIds()) {
                //LetterDoc letterDoc1 = baseMapper.selectByCaseId(id);
                List<Long> list = baseMapper.getTemplateId(id);
                for (Long templateId : list) {
                LetterDoc letterDoc1 =  baseMapper.getPreviewUrl(id,templateId);
                letterDoc1.setLetterTemplateId(templateId);
                if (Objects.isNull(letterDoc1)) {
                    log.info("文书未生成,案件ID:"+id);
                    continue;
                }
                String clientName = FieldEncryptUtil.decrypt(letterDoc1.getClientName());
                if (StringUtils.isEmpty(clientName)){clientName = IdUtils.simpleUUID();}
                letterDoc1.getClientName();
//                fileList.add(FileDownloadUtils.downloadTempFileByDispose(letterDoc1.getPreviewUrl(),null,clientName))
//                fileList.add(FileDownloadUtils.downloadTempFileWithFileName(letterDoc1.getPreviewUrl(),letterDoc1.getSerialNo()+"_"+clientName+".pdf"));
                fileList.add(FileDownloadUtils.downloadTempFileWithFileName(letterDoc1.getPreviewUrl(),letterDoc1.getLetterTemplateId()+"、"+letterDoc1.getTemplateName()+"_"+clientName+".pdf"));

                }
            }
        }
        if(!ObjectUtils.isEmpty(letterDoc.getCondition())){
            letterDoc.setIds(null);
            List<LetterDoc> list = baseMapper.selectAllList();
            for (LetterDoc doc : list) {
                if (Objects.isNull(doc)) {
                    log.info("文书未生成,案件ID:"+doc.getCaseId());
                }
//                fileList.add(FileDownloadUtils.downloadTempFileWithFileName(doc.getPreviewUrl(),doc.getSerialNo()+"_"+doc.getClientName()+".pdf"));
                fileList.add(FileDownloadUtils.downloadTempFileWithFileName(doc.getPreviewUrl(),doc.getLetterTemplateId()+"、"+doc.getTemplateName()+"_"+doc.getClientName()+".pdf"));
            }
        }

        //将files打包成zip压缩包
        dowloadToZip(fileList,response);

    }

//    @Override
//    public void downloadWordDocumentsMediate(HttpServletResponse response,LetterDoc letterDoc) throws Exception {
//
//        List<File> fileList = new ArrayList<>();
//
//        if(ObjectUtils.isEmpty(letterDoc.getCondition())) {
//            int times = 0;
//            for (Long id : letterDoc.getIds()) {
//                HashMap<String, Object> caseInfoMap = templateService.getCaseInfo(id);
//                //LetterDoc letterDoc1 = baseMapper.selectByCaseId(id);
//                List<Long> list = baseMapper.getTemplateId(id);
//                for (Long templateId : list) {
//                    LetterDoc letterDoc1 =  baseMapper.getPreviewUrl(id,templateId);
//                    letterDoc1.setLetterTemplateId(templateId);
//                    if (Objects.isNull(letterDoc1)) {
//                        log.info("文书未生成,案件ID:"+id);
//                        continue;
//                    }
//                    String clientName = FieldEncryptUtil.decrypt(letterDoc1.getClientName());
//                    if (StringUtils.isEmpty(clientName)){clientName = IdUtils.simpleUUID();}
//                    letterDoc1.getClientName();
//                    times = times+1;
//                    Path path = htmlToWord(caseInfoMap, templateId, letterDoc1.getLetterTemplateId() + "、" + letterDoc1.getTemplateName() + "_" + clientName + ".docx",times);
//                    fileList.add(path.toFile());
//                }
//            }
//        }
////        if(!ObjectUtils.isEmpty(letterDoc.getCondition())){
////            letterDoc.setIds(null);
////            List<LetterDoc> list = baseMapper.selectAllList();
////            for (LetterDoc doc : list) {
////                if (Objects.isNull(doc)) {
////                    log.info("文书未生成,案件ID:"+doc.getCaseId());
////                }
//////                fileList.add(FileDownloadUtils.downloadTempFileWithFileName(doc.getPreviewUrl(),doc.getSerialNo()+"_"+doc.getClientName()+".pdf"));
////                fileList.add(FileDownloadUtils.downloadTempFileWithFileName(doc.getPreviewUrl(),doc.getLetterTemplateId()+"、"+doc.getTemplateName()+"_"+doc.getClientName()+".pdf"));
////            }
////        }
//
//        //将files打包成zip压缩包
//        dowloadToZip(fileList,response);
//
//    }
//
//    public Path htmlToWord(HashMap<String, Object> caseInfoMap,Long templateId,String newFileName,int times) {
//        //获取模板信息
//        LetterTemplate template = templateService.getById(templateId);
//        //获取html信息
//        String html = template.getBodyContent();
//        //替换变量
//        html = replaceVariable(template, caseInfoMap,html,times);
//        String[] paragraphs = html.split("</p>");
//
//        try {
//            if(paragraphs.length >= 450){
//                log.info("段落大于450，开始分段");
//                // 将 HTML 内容分割为每450个段落一组
//                List<String> segments = splitHtmlIntoSegments(html, 450);
//
//                // 将每个段落组转换为 Word 文档
//                List<Path> wordFiles = new ArrayList<>();
//                for (int i = 0; i < segments.size(); i++) {
//                    String segment = segments.get(i);
//                    Path path = convertHtmlToWord(segment);
//                    wordFiles.add(path);
//                }
//
//                // 合并多个 Word 文档
//                Path path = mergeWordFiles(wordFiles);
//
//                Path oldPath = Paths.get(path.toString());
//                Path parentPath = oldPath.getParent();
//                Path newPath = parentPath.resolve(newFileName);
//                Files.move(path, newPath, StandardCopyOption.REPLACE_EXISTING);
//                log.info("合并后重命名后的 Word 文档已保存至：" + newPath);
//                return newPath;
//            } else {
//                Path path = convertHtmlToWord(html);
//                Path oldPath = Paths.get(path.toString());
//                Path parentPath = oldPath.getParent();
//                Path newPath = parentPath.resolve(newFileName);
//                Files.move(path, newPath, StandardCopyOption.REPLACE_EXISTING);
//                log.info("重命名后的 Word 文档已保存至：" + newPath);
//                return newPath;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new ServiceException("处理 HTML 内容时出错。");
//        }
//    }
//
//
//    public String replaceVariable(LetterTemplate template,HashMap<String, Object> caseInfoMap,String content,int times) {
////        String content = templateService.getContent(letterTemplate, false);
//        log.info("开始替换变量");
//        List<String> variables = templateAgService.getTemplateVariable(template.getTemplateVariable());
//        for (String variable : variables) {
//            Object value = caseInfoMap.get(variable);
//            if (StrUtil.equals(variable, "出生日期")) {
//                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
//            }
//            if (StrUtil.equals(variable, "逾期日期")) {
//                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
//            }
//            if (StrUtil.equals(variable, "最后还款日期")) {
//                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
//            }
//            if (StrUtil.equals(variable, "贷款发放日")) {
//                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
//            }
//            if (StrUtil.equals(variable, "贷款到期日")) {
//                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
//            }
//            if (StrUtil.equals(variable, "退案日期")) {
//                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
//            }
//            if (StrUtil.equals(variable, "核销日期")) {
//                value = DateUtil.format((Date) value, DateUtils.YYYY_MM_DD);
//            }
//            if (StrUtil.equals(variable, "自增编号")) {
//                value = PinyinUtil.getFirstLetter(template.getTemplateName(), ",").toUpperCase().replace(",", "") + "-0000" + times;
//            }
//            if (value == null) {
//                value = "--";
//            }
//            //判断是否需要脱敏
///*            if (isHideInfo) {
//                if (variable.indexOf("姓名") > -1) {
//                    value = DataMaskingUtils.nameMasking(value);
//                } else if (variable.indexOf("电话") > -1 || variable.indexOf("手机") > -1) {
//                    value = DataMaskingUtils.phoneMasking(value);
//                } else if (variable.indexOf("身份证") > -1 || variable.indexOf("证件") > -1) {
//                    value = DataMaskingUtils.idMasking(value);
//                }
//            }*/
//
//            //模板 替换
//            content = content.replace(StrUtil.format("[{}]", variable), value.toString());
//        }
//        return content;
//    }
//
//    // 将 HTML 内容分割为多个段落组，每个组包含指定数量的段落
//    private static List<String> splitHtmlIntoSegments(String htmlContent, int segmentSize) {
//        if (htmlContent == null || htmlContent.isEmpty()) {
//            throw new ServiceException("HTML content cannot be null or empty.");
//        }
//        List<String> segments = new ArrayList<>();
//        String[] paragraphs = htmlContent.split("</p>");
//        StringBuilder segment = new StringBuilder();
//        for (int i = 0; i < paragraphs.length; i++) {
//            segment.append(paragraphs[i]).append("</p>");
//            if ((i + 1) % segmentSize == 0 || i == paragraphs.length - 1) {
//                segments.add(segment.toString());
//                segment = new StringBuilder();
//            }
//        }
//        return segments;
//    }
//
//    // 将 HTML 内容转换为 Word 文档
//    private static Path convertHtmlToWord(String htmlContent) {
//        try {
//            log.info("开始将HTML内容转换为 Word 文档");
//            Path tempFilePath = Files.createTempFile("temp", ".docx");
//            Document doc = new Document();
//            Section sec = doc.addSection();
//            Paragraph paragraph = sec.addParagraph();
//            paragraph.appendHTML(htmlContent);
////            Document document = new Document();
////            document.loadFromHTML(htmlContent, false, FileFormat.Html);
//            doc.saveToFile(tempFilePath.toString(), FileFormat.Docx);
//            System.out.println("Word 文档已生成：" + tempFilePath.toString());
//            return tempFilePath;
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new ServiceException("生成 Word 文档失败。");
//        }
//
//    }
//
//    //合并多个 Word 文档
//    private static Path mergeWordFiles(List<Path> inputFilePaths) {
//        try {
//            log.info("开始合并 Word 文档");
//            Path tempFilePath = Files.createTempFile("temp", ".docx");
//            Document mergedDocument = new Document();
//            for (Path inputFilePath : inputFilePaths) {
//                mergedDocument.insertTextFromFile(inputFilePath.toString(), FileFormat.Docx_2013);
//            }
//            mergedDocument.saveToFile(tempFilePath.toString(), FileFormat.Docx);
//            System.out.println("Word 文档已生成：" + tempFilePath.toString());
//            return  tempFilePath;
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new ServiceException("合并 Word 文档时出错。");
//        } finally {
//            deleteTempFiles(inputFilePaths);
//        }
//    }

    // 删除临时文件
    private static void deleteTempFiles(List<Path> tempFiles) {
        for (Path tempFile : tempFiles) {
            try {
                Files.deleteIfExists(tempFile);
                log.info("临时文件已删除：" + tempFile.toString());
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException("删除临时文件时出错：" + tempFile.toString());
            }
        }
    }

    /**
     * 将多个文件转zip压缩包
     *
     * @param fileList
     * @param response
     * @throws Exception
     */
    public void dowloadToZip(List<File> fileList, HttpServletResponse response) throws Exception {

        int i = 0;
        if (fileList != null && fileList.size() > 0) {
            try {
                //被压缩文件流集合
                InputStream[] srcFiles = new InputStream[fileList.size()];
                //被压缩文件名称
                String[] srcFileNames = new String[fileList.size()];
                for (File entity : fileList) {
                    //获取inputStream
                    InputStream ins = new FileInputStream(entity);
                    if (ins == null) {
                        continue;
                    }
                    //塞入流数组中
                    srcFiles[i] = ins;
                    srcFileNames[i] = entity.getName();
                    i++;
                }
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(DateUtils.getTime()+".zip", "UTF-8"));
                //多个文件压缩成压缩包返回
                ZipUtil.zip(response.getOutputStream(), srcFileNames, srcFiles);
            } catch (IOException e) {
                e.printStackTrace();
            }
            finally {
                for (File file : fileList) {
                    FileUtils.deletedTempFile(file);
                }
            }
        }

    }


        //下载压缩方法
    private static void downloadZip(LetterDoc letterDoc) {
        try {
            //获取生成签章的url
                String signPreviewUrl = letterDoc.getSignPreviewUrl();
                // 调用下载方法
                File file = FileDownloadUtils.downloadTempFile(signPreviewUrl);
                log.info("下载成功",file);
                try {
                    ZipUtil.zip(file);
                } catch (Exception e) {
                    throw new ServiceException("压缩文件时出现异常: " + e.getMessage());
                }
        } catch (Exception e) {
            throw new ServiceException("文件下载时出现异常: " + e.getMessage());
        }
    }

}

