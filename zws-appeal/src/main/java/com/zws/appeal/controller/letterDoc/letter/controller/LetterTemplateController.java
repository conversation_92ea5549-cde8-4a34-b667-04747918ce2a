package com.zws.appeal.controller.letterDoc.letter.controller;

import cn.hutool.core.util.ObjectUtil;
import com.zws.common.core.constant.BaseConstant;
import com.zws.common.core.domain.Option;
import com.zws.common.core.exception.ServiceException;
import com.zws.common.core.utils.StringUtils;
import com.zws.common.core.utils.file.FileDownloadUtils;
import com.zws.common.core.utils.pdf.PdfUtils;
import com.zws.common.core.utils.pdf.po.PdfFile;
import com.zws.common.core.utils.poi.ExcelUtil;
import com.zws.common.core.web.controller.BaseController;
import com.zws.common.core.web.domain.AjaxResult;
import com.zws.common.core.web.page.TableDataInfo;
import com.zws.appeal.controller.letterDoc.letter.agservice.LetterTemplateAgService;
import com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate;
import com.zws.appeal.controller.letterDoc.letter.service.ILetterTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文书模版
 * <AUTHOR>
 * @date ：Created in 2023/11/23 21:45
 */
@Slf4j
@CrossOrigin
@RestController
@RequestMapping("/letter/template")
public class LetterTemplateController extends BaseController {

    @Autowired
    private ILetterTemplateService templateService;
    @Autowired
    private LetterTemplateAgService templateAgService;



    /**
     * 查询列表
     * @param params
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo list(LetterTemplate params){
        startPage();
        params.setTeamId(BaseConstant.ZCD_TEAM_ID);
        List<LetterTemplate> templates = this.templateService.selectList(params);
        return getDataTable(templates);
    }

    /**
     * 新增
     * @param record
     * @return
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody @Validated LetterTemplate record){
        record.setTeamId(BaseConstant.ZCD_TEAM_ID);
        record.setStatus(BaseConstant.STATUS_OPEN);
        this.templateService.insert(record);
        return AjaxResult.success();
    }

    /**
     * 编辑
     * @param record
     * @return
     */
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody @Validated LetterTemplate record){
        record.setTeamId(BaseConstant.ZCD_TEAM_ID);
        this.templateService.updateById(record);
        return AjaxResult.success();
    }

    /**
     * 编辑状态
     * @param record
     * @return
     */
    @PostMapping("/editStatus")
    public AjaxResult editStatus(@RequestBody LetterTemplate record){
        if (ObjectUtil.isNull(record.getStatus())){
            return AjaxResult.error("状态不能为空");
        }
        LetterTemplate temp=new LetterTemplate();
        temp.setStatus(record.getStatus());
        temp.setId(record.getId());
        this.templateService.updateById(temp);
        return AjaxResult.success();
    }

    /**
     * 删除
     * @param record
     * @return
     */
    @PostMapping("/remove")
    public AjaxResult remove(@RequestBody LetterTemplate record){
        this.templateService.deleteById(record.getId());
        return AjaxResult.success();
    }

    /**
     * 获取模板选项
     * @param  type 模板文件类型  0-Excel类型模板,1-Zip类型模板
     * @return
     */
    @GetMapping("/getOptions")
    public AjaxResult getOptions(Integer type){
        LetterTemplate temp=new LetterTemplate();
//        temp.setTeamId(BaseConstant.ZCD_TEAM_ID);
        temp.setSourceFileType(type);
        List<Option> options= templateService.getOptions(temp);
        return AjaxResult.success(options);
    }

    /**
     * 判断模板名称是否唯一
     * @param template
     * @return
     */
    @GetMapping("/checkUniqueName")
    public AjaxResult checkUniqueName(LetterTemplate template){
        template.setTeamId(BaseConstant.ZCD_TEAM_ID);
        boolean unique= templateService.checkUniqueName(template);
        if (unique){
            return AjaxResult.success();
        }else{
            return AjaxResult.error("名称重复，请换一个");
        }
    }

    /**
     * 获取模板详情
     * @param id 模板id
     * @return
     */
    @GetMapping("/getDetails")
    public AjaxResult getDetails(Long id){
        LetterTemplate template= templateService.getById(id);
        if (template==null){
            return AjaxResult.error("ID错误,查询失败");
        }
        return AjaxResult.success(template);
    }

    /**
     * 详情 获取预览
     * @param id 模板id
     * @return
     */
    @GetMapping("/getPreview")
    public AjaxResult getPreview(Long id){
        LetterTemplate template= templateService.getById(id);
        if (template==null){
            return AjaxResult.error("ID错误,查询失败");
        }
        String url=template.getPreviewUrl();
        Integer total= template.getPreviewPages();
        Map<String,Object> params=new HashMap<>(2);
        params.put("url",url);
        params.put("total",total);
        return AjaxResult.success("操作成功",params);
    }

    /**
     * 创建、编辑时 生成预览
     * @return
     */
    @PostMapping("/createTemplatePreview")
    public AjaxResult createTemplatePreview(@RequestBody LetterTemplate template){
        template.setTeamId(BaseConstant.ZCD_TEAM_ID);
        PdfFile pdfFile= templateAgService.createTemplatePreview(template);
        Map<String,Object> params=new HashMap<>(2);
        params.put("url",pdfFile.getUrl());
        params.put("total",pdfFile.getPages());
        return AjaxResult.success("操作成功",params);
    }

    /**
     * 创建不盖章的pdf文件
     * @param template
     * @return
     */
    @PostMapping("/createTemplatePdf")
    public AjaxResult createTemplatePdf(@RequestBody LetterTemplate template){
        template.setTeamId(BaseConstant.ZCD_TEAM_ID);
        PdfFile pdfFile= templateAgService.createTemplatePdf(template);
        Map<String,Object> params=new HashMap<>(2);
        params.put("url",pdfFile.getUrl());
        params.put("total",pdfFile.getPages());
        return AjaxResult.success("操作成功",params);
    }


    /**
     * 下载模板变量
     */
    @PostMapping("/downloadTemplateVariable")
    public void downloadTemplate(HttpServletResponse response, @RequestBody LetterTemplate template) throws IOException {
        LetterTemplate entity= templateService.getById(template.getId()) ;
        if (entity==null){
            throw  new ServiceException("查询信息失败");
        }
        String templateVariable= entity.getTemplateVariable();
        String[] variables= templateVariable.split(",");
        ExcelUtil.customExcelHeaderText(response, Arrays.asList(variables), entity.getTemplateName()+"_导入模板.xlsx","");
    }

    /**
     * 获取pdf页数
     * @param pdfUrl
     * @return
     */
    @GetMapping("/getPages")
    public AjaxResult getPages(@RequestParam String  pdfUrl){
        String substring = StringUtils.substring(pdfUrl, pdfUrl.lastIndexOf(".") + 1);
        if (!substring.toLowerCase().equals("pdf")){return AjaxResult.error("文件类型错误");}
        File file = null;
        int pdfPage = 0;
        try {
            file = FileDownloadUtils.downloadTempFile(pdfUrl);
            pdfPage = PdfUtils.getPdfPage(file);
        } catch (Exception e) {
            log.error("获取pdf页数 异常：",pdfPage);
        }finally {
            if (file!=null){FileDownloadUtils.deletedTempFile(file);}
        }
        return AjaxResult.success(pdfPage);
    }
}
