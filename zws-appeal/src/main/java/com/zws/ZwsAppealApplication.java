package com.zws;


import com.zws.common.security.annotation.EnableCustomConfig;
import com.zws.common.security.annotation.EnableZwsFeignClients;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 *  债卫士-调诉模块
 */
@EnableCustomConfig
@EnableZwsFeignClients
@EnableScheduling
@SpringBootApplication
@EnableAsync
@ComponentScan({"com.zws.system.api", "com.zws.common.*", "com.zws.appeal"})
@MapperScan(value = "com.zws.**.mapper")
public class ZwsAppealApplication {

    public static void main(String[] args) {
        SpringApplication.run(ZwsAppealApplication.class, args);

        System.out.println("(♥◠‿◠)ﾉﾞ 债卫士-调诉模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                "                                                                   .__   \n" +
                "__________  _  ________         _____  ______ ______   ____ _____  |  |  \n" +
                "\\___   /\\ \\/ \\/ /  ___/  ______ \\__  \\ \\____ \\\\____ \\_/ __ \\\\__  \\ |  |  \n" +
                " /    /  \\     /\\___ \\  /_____/  / __ \\|  |_> >  |_> >  ___/ / __ \\|  |__\n" +
                "/_____ \\  \\/\\_//____  >         (____  /   __/|   __/ \\___  >____  /____/\n" +
                "      \\/            \\/               \\/|__|   |__|        \\/     \\/      ");
    }

}

