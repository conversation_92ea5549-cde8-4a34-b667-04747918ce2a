server:
  port: 9308

spring:
  application:
    # 应用名称
    name: zws-appeal
  profiles:
    # 环境配置
    active: @active@
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB

  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: @nacos.server-addr@
        namespace: @nacos.namespace@
#        server-addr: localhost:8848
        username: nacos    # 新增项，Nacos账号
        password: zwsCX#Nacos    # 新增项，Nacos密码
#        namespace: gedai
      config:
        # 配置中心地址
        server-addr: @nacos.server-addr@
        namespace: @nacos.namespace@
        username: nacos    # 新增项，Nacos账号
        password: zwsCX#Nacos    # 新增项，Nacos密码
#        server-addr: localhost:8848
#        namespace: ${spring.cloud.nacos.discovery.namespace}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}


#上传文件格式以及大小
servlet:
  multipart:
    #    默认为mb
    max-file-size: 20
    max-request-size: 20
    list: .rar, .zip, .doc, .docx, .pdf


feign:
  client:
    config:
      default:
        connect-timeout: 300000
        read-timeout: 300000

