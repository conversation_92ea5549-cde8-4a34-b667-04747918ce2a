<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.LibraryMapper">
    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.Library">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="asset_manage_id" property="assetManageId"/>
        <result column="settle_state" jdbcType="VARCHAR" property="settleState"/>
        <result column="allocate_case_state" jdbcType="VARCHAR" property="allocateCaseState"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="entrusting_party_id" jdbcType="BIGINT" property="entrustingPartyId"/>
        <result column="entrusting_party_name" jdbcType="VARCHAR" property="entrustingPartyName"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>

        <result column="client_name" property="clientName"/>
        <result column="client_id_num" property="clientIdNum"/>
        <result column="client_census_register" property="clientCensusRegister"/>
        <result column="client_phone" property="clientPhone"/>
        <result column="entrust_money" property="entrustMoney"/>
        <result column="overdue_start" property="overdueStart"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , settle_state, allocate_case_state, batch_no,product_id, product_name, entrusting_party_id,
    entrusting_party_name, del_flag, create_by, create_time, update_by, update_time,client_name,client_id_num,client_census_register,client_phone,entrust_money,overdue_start
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from case_library
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectListByCaseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from case_library
        <where>
            del_flag = 0
            <if test="caseIds!=null and caseIds.size != 0 ">
                and id in
                <foreach collection="caseIds" index="index" item="caseId" open="(" separator="," close=")">
                    #{caseId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectCountByOwnerId" resultType="java.lang.Long">
        select count(id)
        from case_library
        where del_flag = 0
          and entrusting_party_id = #{ownerId}
    </select>

    <select id="selectIdentificationNumber" resultType="string">
        select client_id_num AS clientIdNum
        from case_info_base
        where del_flag = 0
          and case_id = #{caseId}
    </select>

    <select id="selectCaseId" resultType="java.lang.Long">
        select case_id AS caseId
        from case_info_base
        where del_flag = 0
          and client_id_num = #{clientIdNum}
    </select>


    <select id="selectCountByProductId" resultType="java.lang.Long">
        select count(id)
        from case_library
        where del_flag = 0
          and product_id = #{productId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from case_library
        where id = #{id,jdbcType=BIGINT}
    </delete>


    <insert id="insert" parameterType="com.zws.appeal.domain.Library" keyProperty="id"
            useGeneratedKeys="true">
        insert into case_library (id, asset_manage_id, settle_state, allocate_case_state,
                                  batch_no, product_id, product_name, entrusting_party_id,
                                  entrusting_party_name, del_flag, create_by,
                                  create_time, update_by, update_time, client_name, client_id_num,
                                  client_census_register, client_phone, entrust_money, overdue_start, client_id_type)
        values (#{id,jdbcType=BIGINT}, #{assetManageId}, #{settleState,jdbcType=VARCHAR},
                #{allocateCaseState,jdbcType=VARCHAR},
                #{batchNo,jdbcType=VARCHAR}, #{productId}, #{productName,jdbcType=VARCHAR},
                #{entrustingPartyId,jdbcType=BIGINT},
                #{entrustingPartyName,jdbcType=VARCHAR}, #{delFlag,jdbcType=CHAR}, #{createBy,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{clientName}, #{clientIdNum}, #{clientCensusRegister}, #{clientPhone}, #{entrustMoney},
                #{overdueStart}, #{clientIdType})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.appeal.domain.Library">
        insert into case_library
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="settleState != null">
                settle_state,
            </if>
            <if test="allocateCaseState != null">
                allocate_case_state,
            </if>
            <if test="batchNo != null">
                batch_no,
            </if>
            <if test="productId!=null">
                product_id,
            </if>
            <if test="productName != null">
                product_name,
            </if>
            <if test="entrustingPartyId != null">
                entrusting_party_id,
            </if>
            <if test="entrustingPartyName != null">
                entrusting_party_name,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="clientName!=null">
                client_name,
            </if>
            <if test="clientIdNum">
                client_id_num,
            </if>
            <if test="clientCensusRegister">
                client_census_register,
            </if>
            <if test="clientPhone">
                client_phone,
            </if>
            <if test="entrustMoney">
                entrust_money ,
            </if>
            <if test="overdueStart">
                overdue_start,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="settleState != null">
                #{settleState,jdbcType=VARCHAR},
            </if>
            <if test="allocateCaseState != null">
                #{allocateCaseState,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null">
                #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="productId != null">
                #{productId,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="entrustingPartyId != null">
                #{entrustingPartyId,jdbcType=BIGINT},
            </if>
            <if test="entrustingPartyName != null">
                #{entrustingPartyName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="clientName!=null">
                #{clientName},
            </if>
            <if test="clientIdNum">
                #{clientIdNum},
            </if>
            <if test="clientCensusRegister">
                #{clientCensusRegister},
            </if>
            <if test="clientPhone">
                #{clientPhone},
            </if>
            <if test="entrustMoney">
                #{clientPhone} ,
            </if>
            <if test="overdueStart">
                #{overdueStart},
            </if>
        </trim>
    </insert>
    <sql id="updateSelective">
        <set>
            <if test="assetManageId != null">
                asset_manage_id = #{assetManageId},
            </if>
            <if test="settleState != null">
                settle_state = #{settleState,jdbcType=VARCHAR},
            </if>
            <if test="allocateCaseState != null">
                allocate_case_state = #{allocateCaseState,jdbcType=VARCHAR},
            </if>
            <if test="batchNo != null">
                batch_no = #{batchNo,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="entrustingPartyId != null">
                entrusting_party_id = #{entrustingPartyId,jdbcType=BIGINT},
            </if>
            <if test="entrustingPartyName != null">
                entrusting_party_name = #{entrustingPartyName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="clientName!=null">
                client_name=#{clientName},
            </if>
            <if test="clientIdNum!=null">
                client_id_num=#{clientIdNum},
            </if>
            <if test="clientCensusRegister!=null">
                client_census_register=#{clientCensusRegister},
            </if>
            <if test="clientPhone!=null">
                client_phone=#{clientPhone},
            </if>
            <if test="entrustMoney!=null">
                entrust_money=#{entrustMoney},
            </if>
            <if test="overdueStart!=null">
                overdue_start=#{overdueStart},
            </if>
            <if test="settleAgreementUrl!=null">
                settle_agreement_url=#{settleAgreementUrl},
            </if>
        </set>
    </sql>
    <update id="updateByAssetManageIdSelective" parameterType="com.zws.appeal.domain.Library">
        update case_library
        <include refid="updateSelective"></include>
        where asset_manage_id = #{assetManageId}
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.Library">
        update case_library
        <include refid="updateSelective"></include>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.Library">
        update case_library
        set settle_state          = #{settleState,jdbcType=VARCHAR},
            allocate_case_state   = #{allocateCaseState,jdbcType=VARCHAR},
            batch_no              = #{batchNo,jdbcType=VARCHAR},
            product_name          = #{productName,jdbcType=VARCHAR},
            entrusting_party_id   = #{entrustingPartyId,jdbcType=BIGINT},
            entrusting_party_name = #{entrustingPartyName,jdbcType=VARCHAR},
            del_flag              = #{delFlag,jdbcType=CHAR},
            create_by             = #{createBy,jdbcType=VARCHAR},
            create_time           = #{createTime,jdbcType=TIMESTAMP},
            update_by             = #{updateBy,jdbcType=VARCHAR},
            update_time           = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeys">
        update case_library
        <set>
            <if test="record.settleState != null">
                settle_state = #{record.settleState,jdbcType=VARCHAR},
            </if>
            <if test="record.allocateCaseState != null">
                allocate_case_state = #{record.allocateCaseState,jdbcType=VARCHAR},
            </if>
            <if test="record.batchNo != null">
                batch_no = #{record.batchNo,jdbcType=VARCHAR},
            </if>
            <if test="record.productName != null">
                product_name = #{record.productName,jdbcType=VARCHAR},
            </if>
            <if test="record.entrustingPartyId != null">
                entrusting_party_id = #{record.entrustingPartyId,jdbcType=BIGINT},
            </if>
            <if test="record.entrustingPartyName != null">
                entrusting_party_name = #{record.entrustingPartyName,jdbcType=VARCHAR},
            </if>
            <if test="record.delFlag != null">
                del_flag = #{record.delFlag,jdbcType=CHAR},
            </if>
            <if test="record.createBy != null">
                create_by = #{record.createBy,jdbcType=VARCHAR},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateBy != null">
                update_by = #{record.updateBy,jdbcType=VARCHAR},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.clientName!=null">
                client_name=#{record.clientName},
            </if>
            <if test="record.clientIdNum">
                client_id_num=#{record.clientIdNum},
            </if>
            <if test="record.clientCensusRegister">
                client_census_register=#{record.clientCensusRegister},
            </if>
            <if test="record.clientPhone">
                client_phone=#{record.clientPhone},
            </if>
            <if test="record.entrustMoney">
                entrust_money=#{record.entrustMoney},
            </if>
            <if test="record.overdueStart">
                overdue_start=#{record.overdueStart},
            </if>
        </set>
        where
        id in
        <foreach collection="caseIds" index="index" item="caseId" open="(" separator="," close=")">
            #{caseId}
        </foreach>

    </update>
</mapper>
