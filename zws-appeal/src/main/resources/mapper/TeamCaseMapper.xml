<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.TeamCaseMapper">

    <sql id="CaseManageVo">
        <if test="teamCaseUtils.deptIds != null and teamCaseUtils.deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="teamCaseUtils.deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="teamCaseUtils.deptId!=null">
            and emp.department_id =#{teamCaseUtils.deptId}
        </if>
        <if test="teamCaseUtils.userId != null">and emp.id = #{teamCaseUtils.userId}</if>

        <if test="teamCaseUtils.allocationStatus != null and teamCaseUtils.allocationStatus.size() > 0">
            and cas.case_state in
            <foreach item="item" collection="teamCaseUtils.allocationStatus" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="teamCaseUtils.caseIds != null and teamCaseUtils.caseIds.size() > 0">
            and cas.case_id in
            <foreach item="item" collection="teamCaseUtils.caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="teamCaseUtils.caseState != null">and cas.case_state = #{teamCaseUtils.caseState}</if>
        <if test="teamCaseUtils.followUpState != null">and cas.follow_up_state = #{teamCaseUtils.followUpState}</if>
        <if test="teamCaseUtils.settlementStatus != null">and cas.settlement_status =
            #{teamCaseUtils.settlementStatus}
        </if>

        <if test="teamCaseUtils.caseId != null">and cas.case_id = #{teamCaseUtils.caseId}</if>
        <if test="teamCaseUtils.clientName != null and teamCaseUtils.clientName != ''">and cas.client_name =
            #{teamCaseUtils.clientName}
        </if>
        <if test="teamCaseUtils.clientIdcard != null and teamCaseUtils.clientIdcard != ''">and cas.client_idcard =
            #{teamCaseUtils.clientIdcard}
        </if>
        <if test="teamCaseUtils.clientPhone != null and teamCaseUtils.clientPhone != ''">and cas.client_phone =
            #{teamCaseUtils.clientPhone}
        </if>
        <if test="teamCaseUtils.entrustingCaseBatchNum != null and teamCaseUtils.entrustingCaseBatchNum != ''">and
            cas.entrusting_case_batch_num = #{teamCaseUtils.entrustingCaseBatchNum}
        </if>
        <if test="teamCaseUtils.entrustingPartyId != null">and
            cas.entrusting_party_id = #{teamCaseUtils.entrustingPartyId}
        </if>
        <if test="teamCaseUtils.clientCensusRegister != null and teamCaseUtils.clientCensusRegister != ''">
            and AES_DECRYPT(UNHEX(cas.client_census_register), #{teamCaseUtils.decryptKey}) LIKE concat
            ('%',#{teamCaseUtils.clientCensusRegister},'%')
        </if>
        <if test="teamCaseUtils.clientCensusRegisters != null and teamCaseUtils.clientCensusRegisters.size()>0">
            and (
            <foreach collection="teamCaseUtils.clientCensusRegisters" item="item" separator="or">
                AES_DECRYPT(UNHEX(cas.client_census_register), #{teamCaseUtils.decryptKey}) LIKE concat
                ('%',#{item},'%')
            </foreach>
            )
        </if>
        <if test="teamCaseUtils.clientMoney1 != null">and cas.client_money &gt;= #{teamCaseUtils.clientMoney1}</if>
        <if test="teamCaseUtils.clientMoney2 != null">and cas.client_money &lt;= #{teamCaseUtils.clientMoney2}</if>
        <if test="teamCaseUtils.syYhPrincipal1 != null">and cil.sy_yh_principal &gt;= #{teamCaseUtils.syYhPrincipal1}
        </if>
        <if test="teamCaseUtils.syYhPrincipal2 != null">and cil.sy_yh_principal &lt;= #{teamCaseUtils.syYhPrincipal2}
        </if>
        <if test="teamCaseUtils.entrustingCaseDate1 != null">and cas.entrusting_case_date &gt;=
            #{teamCaseUtils.entrustingCaseDate1}
        </if>
        <if test="teamCaseUtils.entrustingCaseDate2 != null">and cas.entrusting_case_date &lt;=
            #{teamCaseUtils.entrustingCaseDate2}
        </if>
        <if test="teamCaseUtils.returnCaseDate1 != null">and cas.return_case_date &gt;=
            #{teamCaseUtils.returnCaseDate1}
        </if>
        <if test="teamCaseUtils.returnCaseDate2 != null">and cas.return_case_date &lt;=
            #{teamCaseUtils.returnCaseDate2}
        </if>
        <if test="teamCaseUtils.followUpAst1 != null">and cas.follow_up_ast &gt;= #{teamCaseUtils.followUpAst1}</if>
        <if test="teamCaseUtils.followUpAst2 != null">and cas.follow_up_ast &lt;= #{teamCaseUtils.followUpAst2}</if>

        <if test="teamCaseUtils.area != null and teamCaseUtils.area != ''">and cas.area =
            #{teamCaseUtils.area}
        </if>
        <if test="teamCaseUtils.uid != null and teamCaseUtils.uid != ''">and cib.uid =
            #{teamCaseUtils.uid}
        </if>
        <if test="teamCaseUtils.allocatedTime1 != null">and cas.allocated_time &gt;= #{teamCaseUtils.allocatedTime1}
        </if>
        <if test="teamCaseUtils.allocatedTime2 != null">and cas.allocated_time &lt;= #{teamCaseUtils.allocatedTime2}
        </if>

        <if test="teamCaseUtils.daysNotFollowed1 != null">and #{teamCaseUtils.daysNotFollowed1} &lt;=
            TIMESTAMPDIFF(DAY,cas.follow_up_ast,NOW())
        </if>
        <if test="teamCaseUtils.daysNotFollowed2 != null">and #{teamCaseUtils.daysNotFollowed2} &gt;=
            TIMESTAMPDIFF(DAY,cas.follow_up_ast,NOW())
        </if>

        <if test="teamCaseUtils.odvName != null">
            and cas.odv_name LIKE CONCAT('%', #{teamCaseUtils.odvName}, '%')
        </if>

        <if test="teamCaseUtils.packageNameArr!=null and teamCaseUtils.packageNameArr.size()>0 ">
            and am.package_name in
            <foreach collection="teamCaseUtils.packageNameArr" item="packageName" separator="," open="(" close=")">
                #{packageName}
            </foreach>
        </if>
        <choose>
            <when test="teamCaseUtils.label == 'nolabel' and teamCaseUtils.labels == null and teamCaseUtils.assetLabels == null">
                AND cas.label IS NULL AND cas.label_asset IS NULL
            </when>
        </choose>
        <if test="teamCaseUtils.labels != null or teamCaseUtils.assetLabels != null">
            and (
            <if test="teamCaseUtils.labels != null and teamCaseUtils.labels.size() > 0">
                cas.label in
                <foreach item="item" collection="teamCaseUtils.labels" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="teamCaseUtils.assetLabels != null and teamCaseUtils.assetLabels.size() > 0">
                <if test="teamCaseUtils.labels != null and teamCaseUtils.labels.size() > 0">
                    or
                </if>
                cas.label_asset in
                <foreach item="item" collection="teamCaseUtils.assetLabels" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="teamCaseUtils.label == 'nolabel'">
                or (cas.label is null and cas.label_asset is null)
            </if>
            )
        </if>
    </sql>

    <select id="selectDept" resultType="com.zws.appeal.domain.Dept">
        select id,
               create_id   AS createId,
               parent_id   AS parentId,
               ancestors,
               dept_name   AS deptName,
               order_num   AS orderNum,
               status,
               delete_logo AS deleteLogo,
               founder,
               creationtime,
               modifier,
               modify_time AS modifyTime
        from team_dept
        where delete_logo = 0
          and create_id = #{createId}
    </select>

    <select id="selectDeptByDeptId" resultType="com.zws.appeal.domain.Dept">
        select id,
               create_id   AS createId,
               parent_id   AS parentId,
               ancestors,
               dept_name   AS deptName,
               order_num   AS orderNum,
               status,
               delete_logo AS deleteLogo,
               founder,
               creationtime,
               modifier,
               modify_time AS modifyTime
        from team_dept
        where delete_logo = 0
          and id = #{id}
    </select>

    <select id="selectDeptById" resultType="com.zws.appeal.domain.Dept">
        select id,
               create_id   AS createId,
               parent_id   AS parentId,
               ancestors,
               dept_name   AS deptName,
               order_num   AS orderNum,
               status,
               delete_logo AS deleteLogo,
               founder,
               creationtime,
               modifier,
               modify_time AS modifyTime
        from team_dept
        where id = #{id}
    </select>

    <select id="selectEmployeesId" resultType="com.zws.appeal.domain.Employees">
        select id,
               create_id     AS createId,
               department_id AS departmentId,
               departments
        from team_employees
        where delete_logo = 0
          and id = #{id}
    </select>

    <select id="selectCaseManageAndEmployees" resultType="com.zws.appeal.domain.CaseManage">
        select cas.id,
        cas.case_id AS caseId,
        cas.asset_manage_id AS assetManageId,
        cas.entrusting_case_batch_num AS entrustingCaseBatchNum,
        cas.settlement_status AS settlementStatus,
        cas.entrusting_case_date AS entrustingCaseDate,
        cas.return_case_date AS returnCaseDate,
        cas.product_name AS productName,
        cas.client_name AS clientName,
        cas.client_phone AS clientPhone,
        cas.client_idcard AS clientIdcard,
        cas.client_census_register AS clientCensusRegister,
        cas.odv_id AS odvId,
        cas.odv_name AS odvName,
        cas.case_state AS caseState,
        cas.client_money AS clientMoney,
        cas.client_overdue_start AS clientOverdueStart,
        cas.allocated_time AS allocatedTime,
        cas.follow_up_ast AS followUpAst,
        cas.follow_up_state AS followUpState,
        cas.urge_state AS urgeState,
        cas.outsourcing_team_id AS outsourcingTeamId,
        cas.outsourcing_team_name AS outsourcingTeamName,
        cas.client_id_type as clientIdType,
        cas.label_asset AS labelAsset,
        cm.id AS labelId,
        cm.label_content AS labelContent,
        cm.code AS label,
        cib.client_birthday AS clientBirthday,
        cil.sy_yh_principal AS syYhPrincipal,
        cil.sy_yh_interest AS syYhInterest,
        cil.remaining_due AS remainingDue,
        am.package_name AS packageName,
        cib.uid AS uid,
        cil.amount_final_date AS amountFinalDate,
        cib.registered_address AS registeredAddress,
        cib.home_address AS homeAddress,
        cil.entrust_money AS entrustMoney,
        cil.residual_principal AS residualPrincipal,
        cil.interest_money AS interestMoney,
        cil.service_fee AS serviceFee,
        cil.sy_yh_fees AS syYhFees,
        cil.base_overdue_days AS baseOverdueDays,
        cib.place_of_work AS placeOfWork,
        emp.login_account AS loginAccount
        from case_manage AS cas
        LEFT JOIN team_employees AS emp ON (cas.odv_id = emp.id)
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        LEFT JOIN asset_manage as am on (am.batch_num=cas.batch_num)
        LEFT JOIN case_info_loan as cil on (cil.case_id=cas.case_id)
        where
        cas.del_flag = 0 and cas.outsourcing_team_id=#{teamCaseUtils.createId} and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
        order by cas.case_id desc
    </select>


    <select id="selectCaseManageAndEmployeesSimple" resultType="com.zws.appeal.domain.CaseManage">
        select cas.id,
        cas.case_id AS caseId,
        cas.client_name AS clientName,
        cas.client_idcard AS clientIdcard,
        cas.client_money AS clientMoney
        from case_manage AS cas
        LEFT JOIN team_employees AS emp ON (cas.odv_id = emp.id)
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where
        cas.del_flag = 0 and cas.outsourcing_team_id=#{teamCaseUtils.createId} and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
        order by cas.case_id desc


    </select>


    <select id="selectCaseClientIdcard" resultType="java.lang.String">
        select
        cas.client_idcard AS clientIdcard
        from case_manage AS cas
        LEFT JOIN team_employees AS emp ON (cas.odv_id = emp.id)
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where
        cas.del_flag = 0 and cas.outsourcing_team_id=#{teamCaseUtils.createId} and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
        GROUP BY cas.client_idcard HAVING COUNT(cas.case_id) > 1
    </select>

    <select id="selectCaseCount" resultType="java.util.Map">
        select count(1) AS size,
        sum(cil.remaining_due) AS money,
        sum(cil.sy_yh_principal) AS principal
        from case_manage AS cas
        LEFT JOIN team_employees AS emp ON (cas.odv_id = emp.id)
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cas.case_id)
        where
        cas.del_flag = 0 and cas.outsourcing_team_id=#{teamCaseUtils.createId} and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
    </select>

    <select id="selectCaseManageMoneyCount" resultType="map">
        select sum(cas.client_money) AS money,
        count(1) AS num
        from case_manage AS cas
        LEFT JOIN team_employees AS emp ON (cas.odv_id = emp.id)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where
        cas.del_flag = 0 and cas.outsourcing_team_id=#{teamCaseUtils.createId} and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
        order by cas.case_id desc
    </select>

    <select id="selectTeamCaseCaseIdCount" resultType="long">
        select cas.case_id AS caseId
        from case_manage AS cas
        LEFT JOIN team_employees AS emp ON (cas.odv_id = emp.id)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where
        cas.del_flag = 0 and cas.outsourcing_team_id=#{teamCaseUtils.createId} and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
        order by cas.case_id desc
    </select>

    <select id="selectCaseManageCaseIdCount" resultType="long">
        select cas.case_id AS caseId
        from case_manage AS cas
        LEFT JOIN team_employees AS emp ON (cas.odv_id = emp.id)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where
        cas.del_flag = 0 and cas.outsourcing_team_id=#{teamCaseUtils.createId} and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
        order by cas.case_id desc
    </select>

    <select id="selectCaseManageCount" resultType="map">
        select count(1) AS quantity,
        sum(cas.client_money) AS clientMoney
        from case_manage AS cas
        LEFT JOIN team_employees AS emp ON (cas.odv_id = emp.id)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where
        cas.del_flag = 0 and cas.outsourcing_team_id=#{teamCaseUtils.createId} and cas.allocated_state = 1
        <if test="teamCaseUtils.caseStateList != null and teamCaseUtils.caseStateList.size() > 0">
            and cas.case_state in
            <foreach item="item" collection="teamCaseUtils.caseStateList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <include refid="CaseManageVo"></include>
        order by cas.case_id desc
    </select>

    <select id="selectUrgeRecordId" resultType="com.zws.appeal.pojo.CreateUrgeRecordIdUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.odv_name AS odvName,
        rep.promise_repayment_time AS promiseRepaymentTime,
        rep.promise_repayment_money AS promiseRepaymentMoney,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.remarks AS remarks,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_phone AS clientPhone,
        cm.account_period AS accountPeriod,
        cm.entrusting_party_name AS entrustingPartyName,
        cm.client_id_type as clientIdType
        from case_urge_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id = cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.odv_id = emp.id)
        where rep.del_flag = 0 and rep.urge_state in ("承诺还款","承诺分期还款")
        and rep.create_id = #{createId}

        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="userId != null">and emp.id =#{userId}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="odvName != null">and rep.odv_name LIKE CONCAT('%', #{odvName}, '%')</if>
        <if test="clientName != null and clientName != ''">
            and AES_DECRYPT(UNHEX(cm.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>
        <if test="clientIdcard != null and clientIdcard != ''">
            and AES_DECRYPT(UNHEX(cm.client_idcard), #{decryptKey}) LIKE concat ('%',#{clientIdcard},'%')
        </if>
        <if test="clientPhone != null and clientPhone != ''">
            and AES_DECRYPT(UNHEX(cm.client_phone), #{decryptKey}) LIKE concat ('%',#{clientPhone},'%')
        </if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and
            rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="entrustingPartyId != null">and cm.entrusting_party_id
            =#{entrustingPartyId}
        </if>
        <if test="promiseRepaymentMoney1 != null">and rep.promise_repayment_money &gt;=
            #{promiseRepaymentMoney1}
        </if>
        <if test="promiseRepaymentMoney2 != null">and rep.promise_repayment_money &lt;=
            #{promiseRepaymentMoney2}
        </if>
        <if test="accountPeriod != null and accountPeriod != ''">and cm.account_period
            =#{accountPeriod}
        </if>
        order by rep.create_time desc
    </select>

    <!--资料调取记录-->
    <select id="selectRetrievalRecordId" resultType="com.zws.appeal.pojo.teamApplication.CreateRetrievalRecordUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.examine_state AS examineState,
        rep.applicant AS applicant,
        rep.reason AS reason,
        rep.apply_date AS applyDate,
        rep.examine_time AS examineTime,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.can_download AS canDownload,
        rep.random AS random,
        rep.archive_file_address AS archiveFileAddress,
        rep.watermarked_file_path AS watermarkedFilePath,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard
        from case_retrieval_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.applicant_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="userId != null and userId != ''">and emp.id =#{userId}</if>
        <if test="completed == null">
            <if test="examineState != null and examineState != ''">
                and rep.examine_state = #{examineState}
            </if>
        </if>
        <if test="completed != null">
            and rep.examine_state in
            <foreach item="item" collection="completed" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant =#{applicant}</if>
        order by rep.apply_date desc
    </select>

    <select id="selectRetrievalRecordIdNumber" resultType="map">
        select count(1) AS number,
        rep.examine_state AS examineState
        from case_retrieval_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.applicant_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="userId != null and userId != ''">and emp.id =#{userId}</if>
        <!--        <if test="completed == null">-->
        <!--            <if test="examineState != null and examineState != ''">-->
        <!--                and rep.examine_state = #{examineState}-->
        <!--            </if>-->
        <!--        </if>-->
        <if test="completed != null">
            and rep.examine_state in
            <foreach item="item" collection="completed" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant =#{applicant}</if>
        group by rep.examine_state
    </select>

    <!--回款记录-->
    <select id="selectRepaymentRecordId" resultType="com.zws.appeal.pojo.teamApplication.CreateRepaymentRecordUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.repayment_money AS repaymentMoney,
        rep.repayment_date AS repaymentDate,
        rep.repayment_type AS repaymentType,
        rep.examine_state AS examineState,
        rep.create_time AS createTime,
        rep.update_time AS updateTime,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.entrusting_case_date AS entrustingCaseDate,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_money AS clientMoney,
        cm.client_id_type as clientIdType,
        rep.odv_name AS odvName
        from case_repayment_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.registrar_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="userId != null and userId != ''">and emp.id =#{userId}</if>
        <if test="completed == null">
            <if test="examineState != null and examineState != ''">
                and rep.examine_state = #{examineState}
            </if>
        </if>
        <if test="completed != null">
            and rep.examine_state in
            <foreach item="item" collection="completed" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="repaymentType != null and repaymentType != ''">and rep.repayment_type =#{repaymentType}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="entrustingPartyId != null">and cm.entrusting_party_id =#{entrustingPartyId}</if>
        <if test="clientMoney1 != null">and cm.client_money &gt;= #{clientMoney1}</if>
        <if test="clientMoney2 != null">and cm.client_money &lt;= #{clientMoney2}</if>
        <if test="repaymentMoney1 != null">and rep.repayment_money &gt;= #{repaymentMoney1}</if>
        <if test="repaymentMoney2 != null">and rep.repayment_money &lt;= #{repaymentMoney1}</if>
        <if test="repaymentDate1 != null">and rep.repayment_date &gt;= #{repaymentDate1}</if>
        <if test="repaymentDate2 != null">and rep.repayment_date &lt;= #{repaymentDate2}</if>
        <if test="createTime1 != null">and rep.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and rep.create_time &lt;= #{createTime2}</if>
        order by rep.create_time desc
    </select>

    <select id="selectRepaymentRecordIdNumber" resultType="map">
        select count(1) AS number,
        examine_state AS examineState
        from case_repayment_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.registrar_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="userId != null and userId != ''">and emp.id =#{userId}</if>

        <!--        <if test="completed == null">-->
        <!--            <if test="examineState != null and examineState != ''">-->
        <!--                and rep.examine_state = #{examineState}-->
        <!--            </if>-->
        <!--        </if>-->
        <if test="completed != null">
            and rep.examine_state in
            <foreach item="item" collection="completed" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="repaymentType != null and repaymentType != ''">and rep.repayment_type =#{repaymentType}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="entrustingPartyId != null">and cm.entrusting_party_id =#{entrustingPartyId}</if>
        <if test="clientMoney1 != null">and cm.client_money &gt;= #{clientMoney1}</if>
        <if test="clientMoney2 != null">and cm.client_money &lt;= #{clientMoney2}</if>
        <if test="repaymentMoney1 != null">and rep.repayment_money &gt;= #{repaymentMoney1}</if>
        <if test="repaymentMoney2 != null">and rep.repayment_money &lt;= #{repaymentMoney1}</if>
        <if test="repaymentDate1 != null">and rep.repayment_date &gt;= #{repaymentDate1}</if>
        <if test="repaymentDate2 != null">and rep.repayment_date &lt;= #{repaymentDate2}</if>
        <if test="createTime1 != null">and rep.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and rep.create_time &lt;= #{createTime2}</if>
        group by examine_state
    </select>

    <!--减免记录-->
    <select id="selectReductionRecordId" resultType="com.zws.appeal.pojo.teamApplication.CreateReductionRecordUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.amount_after_deduction AS amountAfterDeduction,
        rep.update_time AS updateTime,
        rep.applicant AS applicant,
        rep.apply_date AS applyDate,
        rep.reason AS reason,
        rep.state AS state,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.remaining_due as remainingDue,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_id_type as clientIdType,
        rep.odv_name AS odvName
        from case_reduction_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.applicant_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="userId != null">and emp.id =#{userId}</if>

        <if test="completed == null">
            <if test="state != null and state != ''">
                and rep.state = #{state}
            </if>
        </if>
        <if test="completed != null">
            and rep.state in
            <foreach item="item" collection="completed" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant =#{applicant}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="updateTime1 != null">and rep.update_time &gt;= #{updateTime1}</if>
        <if test="updateTime2 != null">and rep.update_time &lt;= #{updateTime2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by rep.apply_date desc
    </select>

    <select id="selectReductionRecordIdNumber" resultType="map">
        select count(1) AS number,
        state AS state
        from case_reduction_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.applicant_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="userId != null">and emp.id =#{userId}</if>

        <!--        <if test="completed == null">-->
        <!--            <if test="state != null and state != ''">-->
        <!--                and rep.state = #{state}-->
        <!--            </if>-->
        <!--        </if>-->
        <if test="completed != null">
            and rep.state in
            <foreach item="item" collection="completed" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant =#{applicant}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="updateTime1 != null">and rep.update_time &gt;= #{updateTime1}</if>
        <if test="updateTime2 != null">and rep.update_time &lt;= #{updateTime2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        group by state
    </select>

    <!--分期还款记录-->
    <select id="selectStagingRecordId" resultType="com.zws.appeal.pojo.teamApplication.CreateStagingRecordUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.staging_num AS stagingNum,
        rep.repayment_monthly AS repaymentMonthly,
        rep.repayment_date AS repaymentDate,
        rep.examine_state AS examineState,
        rep.examine_time AS examineTime,
        rep.applicant AS applicant,
        rep.apply_date AS applyDate,
        rep.reason AS reason,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_id_type as clientIdType,
        rep.odv_name AS odvName

        from case_staging_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.applicant_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="userId != null">and emp.id =#{userId}</if>

        <if test="completed == null">
            <if test="examineState != null and examineState != ''">
                and rep.examine_state = #{examineState}
            </if>
        </if>
        <if test="completed != null">
            and rep.examine_state in
            <foreach item="item" collection="completed" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant =#{applicant}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="examineTime1 != null">and rep.examine_time &gt;= #{examineTime1}</if>
        <if test="examineTime2 != null">and rep.examine_time &lt;= #{examineTime2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by rep.apply_date desc
    </select>

    <select id="selectStagingRecordIdNumber" resultType="map">
        select count(1) AS number,
        examine_state AS examineState
        from case_staging_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.applicant_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="userId != null">and emp.id =#{userId}</if>

        <!--        <if test="completed == null">-->
        <!--            <if test="examineState != null and examineState != ''">-->
        <!--                and rep.examine_state = #{examineState}-->
        <!--            </if>-->
        <!--        </if>-->
        <if test="completed != null">
            and rep.examine_state in
            <foreach item="item" collection="completed" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant =#{applicant}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="examineTime1 != null">and rep.examine_time &gt;= #{examineTime1}</if>
        <if test="examineTime2 != null">and rep.examine_time &lt;= #{examineTime2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        group by examine_state
    </select>

    <!--团队停催/留案/退案申请记录-->
    <select id="selectApplyRecordId" resultType="com.zws.appeal.pojo.teamApplication.CreateApplyRecordUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.examine_state AS examineState,
        rep.examine_time AS examineTime,
        rep.applicant AS applicant,
        rep.apply_date AS applyDate,
        rep.reason AS reason,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_id_type as clientIdType,
        rep.odv_name AS odvName
        from case_apply_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.applicant_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId} and rep.apply_state = #{applyState}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="userId != null">and emp.id =#{userId}</if>

        <if test="completed == null">
            <if test="examineState != null and examineState != ''">
                and rep.examine_state = #{examineState}
            </if>
        </if>

        <if test="completed != null">
            and rep.examine_state in
            <foreach item="item" collection="completed" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant =#{applicant}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="examineTime1 != null">and rep.examine_time &gt;= #{examineTime1}</if>
        <if test="examineTime2 != null">and rep.examine_time &lt;= #{examineTime2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by rep.apply_date desc
    </select>

    <select id="selectApplyRecordIdNumber" resultType="map">
        select count(1) AS number,
        examine_state AS examineStates
        from case_apply_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id = cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.applicant_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId} and rep.apply_state = #{applyState}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="userId != null">and emp.id =#{userId}</if>

        <!--        <if test="completed == null">-->
        <!--            <if test="examineState != null and examineState != ''">-->
        <!--                and rep.examine_state = #{examineState}-->
        <!--            </if>-->
        <!--        </if>-->

        <if test="completed != null">
            and rep.examine_state in
            <foreach item="item" collection="completed" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant =#{applicant}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="examineTime1 != null">and rep.examine_time &gt;= #{examineTime1}</if>
        <if test="examineTime2 != null">and rep.examine_time &lt;= #{examineTime2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        group by examine_state
    </select>

    <!--团队协催申请记录-->
    <select id="selectAssistRecordId" resultType="com.zws.appeal.pojo.teamApplication.CreateAssistRecordUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.helper AS helper,
        rep.state AS state,
        rep.apply_date AS applyDate,
        rep.reason AS reason,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        cm.entrusting_party_name AS entrustingPartyName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_money AS clientMoney,
        cm.client_id_type as clientIdType,
        rep.odv_name AS odvName

        from case_assist_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.applicant_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId} and rep.invalid = #{invalid}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="userId != null">and emp.id =#{userId}</if>

        <if test="examineState != null and examineState != ''">and rep.state =#{examineState}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="entrustingPartyId != null">and cm.entrusting_party_id = #{entrustingPartyId}</if>
        <if test="clientMoney1 != null">and cm.client_money &gt;= #{clientMoney1}</if>
        <if test="clientMoney2 != null">and cm.client_money &lt;= #{clientMoney2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        order by rep.apply_date desc
    </select>

    <!--团队外访申请记录-->
    <select id="selectOutsideRecordId" resultType="com.zws.appeal.pojo.teamApplication.CreateOutsideRecordUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.create_by AS createBy,
        rep.create_time AS createTime,
        rep.odv_name AS odvNames,
        rep.outside_address AS outsideAddress,
        rep.reason AS reason,
        rep.outside_start AS outsideStart,
        rep.state AS state,
        rep.examine_time AS examineTime,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,

        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_id_type as clientIdType,
        rep.odv_name AS odvName

        from case_outside_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.create_by_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="userId != null">and emp.id =#{userId}</if>

        <if test="state != null and state != ''">and rep.state_code =#{state}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="createBy != null">and cm.create_by = #{createBy}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="examineTime1 != null">and rep.examine_time &gt;= #{examineTime1}</if>
        <if test="examineTime2 != null">and rep.examine_time &lt;= #{examineTime2}</if>
        <if test="createTime1 != null">and rep.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and rep.create_time &lt;= #{createTime2}</if>
        order by rep.create_time desc
    </select>

    <select id="selectOutsideRecordIdNumber" resultType="map">
        select count(1) AS number,
        state AS state
        from case_outside_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.create_by_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="userId != null">and emp.id =#{userId}</if>

        <!--        <if test="state != null and state != ''">and rep.state_code =#{state}</if>-->
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="createBy != null">and cm.create_by = #{createBy}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="examineTime1 != null">and rep.examine_time &gt;= #{examineTime1}</if>
        <if test="examineTime2 != null">and rep.examine_time &lt;= #{examineTime2}</if>
        <if test="createTime1 != null">and rep.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and rep.create_time &lt;= #{createTime2}</if>
        group by state
    </select>

    <!--团队工单处理表-->
    <select id="selectWorkOrder" resultType="com.zws.appeal.pojo.teamApplication.CreateWorkOrderUtils">
        select wor.id,
        wor.case_id AS caseId,
        wor.question_type AS questionType,
        wor.channel_source AS channelSource,
        wor.order_status AS orderStatus,
        wor.call_number AS callNumber,
        wor.create_by_id AS createById,
        wor.create_by AS createBy,
        wor.create_time AS createTime,
        wor.handler_id AS handlerId,
        wor.handler AS handler,
        wor.processing_time AS processingTime,
        wor.question_content AS questionContent,
        wor.entrusting_case_batch_num AS entrustingCaseBatchNum,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.odv_name AS odvName,
        cm.client_phone AS clientPhone,
        cm.uid AS uid,
        cm.client_id_type as clientIdType
        from case_work_order AS wor
        LEFT JOIN case_manage AS cm ON (wor.case_id = cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (wor.odv_id = emp.id)
        where wor.del_flag = 0 and wor.team_id = #{createId}
        <if test="id!=null">
            and wor.id=#{id}
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="userId != null">and emp.id =#{userId}</if>
        <if test="odvName != null">and cm.odv_name LIKE CONCAT('%', #{odvName}, '%')</if>
        <if test="orderStatus != null">and wor.order_status =#{orderStatus}</if>
        <if test="caseId != null">and wor.case_id =#{caseId}</if>
        <if test="uid != null and uid != '' ">and cm.uid =#{uid}</if>
        <if test="clientName != null and clientName != ''">
            and AES_DECRYPT(UNHEX(cm.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>
        <if test="clientIdcard != null and clientIdcard != ''">
            and AES_DECRYPT(UNHEX(cm.client_idcard), #{decryptKey}) LIKE concat ('%',#{clientIdcard},'%')
        </if>
        <if test="clientPhone != null and clientPhone != ''">
            and AES_DECRYPT(UNHEX(cm.client_phone), #{decryptKey}) LIKE concat ('%',#{clientPhone},'%')
        </if>
        <if test="createTime1 != null">and wor.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and wor.create_time &lt;= #{createTime2}</if>
        <if test="questionType != null and questionType !=''">and wor.question_type = #{questionType}</if>
    </select>

    <select id="selectWorkOrderNumber" resultType="map">
        select count(1) AS number,
        order_status AS orderStatus
        from case_work_order AS wor
        LEFT JOIN case_manage AS cm ON (wor.case_id = cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (wor.odv_id = emp.id)
        where wor.del_flag = 0 and wor.team_id = #{createId}
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="userId != null ">and emp.id =#{userId}</if>
        <if test="odvName != null">and cm.odv_name LIKE CONCAT('%', #{odvName}, '%')</if>
        <if test="orderStatus != null">and wor.order_status =#{orderStatus}</if>
        <if test="caseId != null">and wor.case_id =#{caseId}</if>
        <if test="uid != null and uid != '' ">and cm.uid =#{uid}</if>
        <if test="clientName != null and clientName != ''">
            and AES_DECRYPT(UNHEX(cm.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>
        <if test="clientIdcard != null and clientIdcard != ''">
            and AES_DECRYPT(UNHEX(cm.client_idcard), #{decryptKey}) LIKE concat ('%',#{clientIdcard},'%')
        </if>
        <if test="clientPhone != null and clientPhone != ''">
            and AES_DECRYPT(UNHEX(cm.client_phone), #{decryptKey}) LIKE concat ('%',#{clientPhone},'%')
        </if>
        <if test="createTime1 != null">and wor.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and wor.create_time &lt;= #{createTime2}</if>
        group by order_status
    </select>
    <select id="selectTeamUrgeRecord" resultType="com.zws.appeal.pojo.ExportDataUtils">
        select urg.create_time AS createTime,
        urg.case_id AS caseId,
        urg.create_by AS createBy,
        urg.odv_id AS odvId,
        urg.liaison AS liaison,
        urg.relation AS relation,
        urg.contact_mode AS contactMode,
        urg.contact_medium AS contactMedium,
        urg.content AS content,
        urg.follow_up_state AS followUpState,
        urg.urge_state AS urgeState,
        urg.promise_repayment_money AS promiseRepaymentMoney,
        urg.promise_repayment_time AS promiseRepaymentTime,
        cas.entrusting_case_batch_num AS entrustingCaseBatchNum,
        cas.client_name AS clientName,
        cas.client_idcard AS clientIdcard,
        cas.client_money AS clientMoney,
        cas.contract_no AS contractNo,
        cas.client_id_type as clientIdType

        from case_urge_record AS urg
        LEFT JOIN case_manage AS cas ON (urg.case_id = cas.case_id)
        LEFT JOIN team_employees AS emp ON (cas.odv_id = emp.id)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where cas.outsourcing_team_id = #{teamCaseUtils.createId}
        <include refid="CaseManageVo"></include>
        <if test="createTime1 != null">and urg.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and urg.create_time &lt;= #{createTime2}</if>
        order by urg.create_time desc
    </select>

    <select id="selectApplyRecordIdNew"
            resultType="com.zws.appeal.pojo.teamApplication.CreateApplyRecordUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        zws_ar.approve_state AS examineState,
        zws_ar.examine_time AS examineTime,
        zws_ar.applicant AS applicant,
        zws_ar.apply_date AS applyDate,
        rep.reason AS reason,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        IFNULL(rep.stop_end_approval_time, rep.stop_end_time) AS stopEndTime,
        rep.stop_end_approval_time AS stopEndApprovalTime,
        rep.permanently_stop AS permanentlyStop,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_id_type as clientIdType,
        rep.odv_name AS odvName,
        rep.approve_id AS approveId
        from ${sqlDataDto.fromData}
        LEFT JOIN case_apply_record AS rep on(rep.approve_id=zws_ar.id)
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (rep.applicant_id = emp.id)
        where ${sqlDataDto.whereData} and rep.del_flag = 0 and rep.team_id = #{dto.createId}
        <if test="dto.deptIds != null and dto.deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="dto.deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="dto.userId != null">and emp.id =#{dto.userId}</if>

        <if test="dto.caseId != null">and rep.case_id =#{dto.caseId}</if>
        <if test="dto.clientName != null and dto.clientName != ''">and cm.client_name =#{dto.clientName}</if>
        <if test="dto.clientIdcard != null and dto.clientIdcard != ''">and cm.client_idcard =#{dto.clientIdcard}</if>
        <if test="dto.entrustingCaseBatchNum != null and dto.entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{dto.entrustingCaseBatchNum}
        </if>
        <if test="dto.entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{dto.entrustingCaseDate1}</if>
        <if test="dto.entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{dto.entrustingCaseDate2}</if>
        <if test="dto.returnCaseDate1 != null">and rep.return_case_date &gt;= #{dto.returnCaseDate1}</if>
        <if test="dto.returnCaseDate2 != null">and rep.return_case_date &lt;= #{dto.returnCaseDate2}</if>
        order by zws_ar.apply_date desc
    </select>


</mapper>
