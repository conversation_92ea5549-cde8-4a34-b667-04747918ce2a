<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.approve.LetterApproveMapper">


    <resultMap id="LetterItemApproveMap" type="com.zws.appeal.pojo.letter.LetterItemApprove">
        <id column="id" property="id" />
        <result column="letter_id" property="letterId" />
        <result column="serial_no" property="serialNo" />
        <result column="status" property="status" />
        <result column="sign_status" property="signStatus" />
        <result column="item_data" property="itemData" />
        <result column="preview_url" property="previewUrl" />
        <result column="sign_preview_url" property="signPreviewUrl" />
        <result column="proce" property="proce" />
        <result column="proce_sort" property="proceSort" />
        <result column="examine_time" property="examineTime" />
        <result column="examine_by" property="examineBy" />
        <result column="examine_by_id" property="examineById" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_by_id" property="createById" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_id" property="updateById" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
        <result column="template_name" property="templateName" />
        <result column="type" property="type" />

    </resultMap>


    <sql id="select_id">
        l.id,l.status,l.proce_sort,l.type
    </sql>

    <sql id="Base_Column_List">
        l.id,l.letter_id,l.serial_no,l.status,l.proce,l.examine_time,l.examine_by_id,l.examine_by,
        lt.template_name,l.proce_sort AS proceSort,
        ifnull(c.approve_start,0) AS approveStart,c.approve_time AS approveTime
    </sql>

    <sql id="Form_Sql">
        FROM letter AS l
        LEFT JOIN letter_message AS lm on (lm.id=l.letter_id)
        LEFT JOIN letter_template AS lt on (lm.template_id=lt.id)
        LEFT JOIN (
        SELECT approve_code, apply_id,approve_start, approve_time,reviewer,reviewer_id,refuse_reason , approve_sort FROM
        approve_proce WHERE approve_code=8  and  reviewer_id=#{userId}
        )AS c ON(l.id=c.apply_id)
        WHERE l.del_flag=0 and l.type = '0'
        <if test="ids!=null and ids.size()>0">
            l.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <choose>
            <when test="selectAll!=null">
                and (
                (l.proce = 0 AND l.proce_sort = #{approveSort} - 1)
                OR (
                l.proce =1
                AND( l.proce_sort = #{approveSort} - 1 OR c.reviewer_id = #{userId})
                )
                OR (
                l.proce IN (2)
                AND c.reviewer_id = #{userId}
                )
                )
            </when>
            <otherwise>
                <if test="approveSort!=null and approveStart==null">

                    <if test="approveSort==1">
                        and l.proce =0
                    </if>
                    <if test="approveSort >1 ">
                        and l.proce =1 AND( l.proce_sort = #{approveSort} - 1 )
                    </if>
                </if>
                <if test="approveStart!=null">
                    and c.approve_start=#{approveStart}
                    and c.reviewer_id=#{userId}
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="Where_Sql">
        <if test="letterId!=null">
            and l.letter_id=#{letterId}
        </if>
        <if test="letterIds!=null and letterIds.size()>0">
            and l.letter_id in
            <foreach collection="letterIds" item="letterId" open="(" close=")" separator=",">
                #{letterId}
            </foreach>
        </if>
        <if test="serialNo!=null and serialNo!=''">
            and l.serial_no LIKE concat ('%',#{serialNo},'%')
        </if>
    </sql>




    <select id="selectLetterApprove" resultMap="LetterItemApproveMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Form_Sql"></include>
        <include refid="Where_Sql"></include>
    </select>

       <select id="selectLetterApproveId" resultType="com.zws.appeal.pojo.letter.LetterItemApprove">
        SELECT
        <include refid="select_id"/>
        <include refid="Form_Sql"></include>
        <include refid="Where_Sql"></include>
    </select>
    <select id="jgQueryApproveList" resultType="com.zws.appeal.pojo.myApproval.MySignRecordUtils">
        select
            car.id,
            zws_ar.id   AS approveId,
            car.batch_num as batchNum,
            car.model_type_id as modelTypeId,
            car.model_type as modelType,
            car.model_name as modelName,
            car.registrar as registrar,
            zws_ar.create_time as createTime,
            car.model_name as productName,
            car.letter_id as letterId,
            car.registrar as createBy,
            team.cname,
            team.category,
            mes.status AS status,
            mes.quantity AS quantity,
            mes.remarks AS remarks,
            zws_ar.approve_state as examineState,
            car.update_by as updateBy,
            car.update_time as updateTime,
            zws_ap.reviewer,
            zws_ap.approve_time AS approveTime
        from ${sqlDataDto.fromData}
                 inner join case_sign_record as car  on(car.approve_id=zws_ar.id)
                 left join approve_proce as appr on(car.approve_id=appr.id)
                 left join team_create as team on(car.team_id=team.id)
                 left join letter_message as mes on(mes.id=car.letter_id)
                 LEFT JOIN team_employees AS te ON (car.registrar_id=te.id)
                 LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        WHERE ${sqlDataDto.whereData}
        <if test="queryDTO.examineStateArr!=null and queryDTO.examineStateArr.size()>0">
            and car.examine_state in
            <foreach collection="queryDTO.examineStateArr" item="examineState" separator="," open="(" close=")">
                #{examineState}
            </foreach>
        </if>
        <if test="queryDTO.batchNum!=null and queryDTO.batchNum!=''">
            and car.batch_num like concat('%',#{queryDTO.batchNum},'%')
        </if>
        <if test="queryDTO.templateName!=null and queryDTO.templateName!=''">
            and car.model_name like concat('%',#{queryDTO.templateName},'%')
        </if>
        <if test="queryDTO.classifyNamesArr!=null and queryDTO.classifyNamesArr.size()>0">
            and car.model_type in
            <foreach collection="queryDTO.classifyNamesArr" item="classifyName" separator="," open="(" close=")">
                #{classifyName}
            </foreach>
        </if>
        order by zws_ar.create_time desc


    </select>
</mapper>
