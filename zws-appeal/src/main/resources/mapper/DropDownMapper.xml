<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.DropDownMapper">

    <select id="selectAssetOwner" resultType="com.zws.appeal.domain.AssetOwner">
        select id,
               name,
               del_flag    AS delFlag,
               create_by   AS createBy,
               create_time AS createTime,
               update_by   AS updateBy,
               update_time AS updateTime
        from asset_owner
        where del_flag = 0
    </select>

    <select id="selectAssetProduct" resultType="com.zws.appeal.domain.AssetProduct">
        select id,
               owner_id         AS ownerId,
               owner_name       AS ownerName,
               name,
               case_total_num   AS caseTotalNum,
               case_total_money AS caseTotalMoney,
               batch_number     AS batchNumber,
               state,
               del_flag         AS delFlag,
               create_by        AS createBy,
               create_time      AS createTime,
               update_by        AS updateBy,
               update_time      AS updateTime
        from asset_product
        where del_flag = 0
          and state = 0
    </select>

    <select id="selectDictData" resultType="com.zws.appeal.domain.DictData">
        select dict_code   AS dictCode,
               dict_sort   AS dictSort,
               dict_label  AS dictLabel,
               dict_value  AS dictValue,
               dict_type   AS dictType,
               css_class   AS cssClass,
               list_class  AS listClass,
               is_default  AS isDefault,
               status,
               create_by   AS createBy,
               create_time AS createTime,
               update_by   AS updateBy,
               update_time AS updateTime,
               remark
        from sys_dict_data
        where status = 0
          and dict_type = #{dictType}
    </select>

    <select id="selectEmployees" resultType="com.zws.appeal.domain.Employees">
        select id,
               employee_name AS employeeName
        from team_employees
        where delete_logo = 0
          and create_id = #{createId}
    </select>

    <select id="selectCaseManage" resultType="com.zws.appeal.domain.CaseManage">
        select entrusting_batch_num AS entrustingCaseBatchNum
        from case_allocated_record
        where examine_state = '已通过'
          and team_id = #{createId}
        GROUP BY entrusting_batch_num
    </select>

    <select id="selectLabel" resultType="com.zws.appeal.domain.Label">
        select id,
               create_id     AS createId,
               label_content AS labelContent,
               state_label   AS stateLabel,
               code
        from team_label
        where create_id = #{createId}
          and state_label = 1
    </select>

    <select id="selectRegisteredResidence" resultType="com.zws.appeal.domain.CaseManage">
        select client_census_register AS clientCensusRegister
        from case_manage
        where del_flag = 0
          and outsourcing_team_id = #{createId}
        group by client_census_register
    </select>

    <select id="selectAllocatedRecord" resultType="com.zws.appeal.domain.AllocatedRecord">
        select id,
        entrusting_batch_num AS entrustingBatchNum,
        case_quantity AS caseQuantity,
        total_amount AS totalAmount,
        return_case_date AS returnCaseDate,
        entrusting_case_date AS entrustingCaseDate,
        applicant AS applicant,
        target_back_money AS targetBackMoney
        from case_allocated_record
        where team_id = #{teamId}
        and examine_state = #{examineState}
        <if test="entrustingBatchNum != null and entrustingBatchNum != ''">and entrusting_batch_num
            =#{entrustingBatchNum}
        </if>
        <if test="entrustingCaseDate1 != null">and entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="caseQuantity1 != null">and case_quantity &gt;= #{caseQuantity1}</if>
        <if test="caseQuantity2 != null">and case_quantity &lt;= #{caseQuantity2}</if>
        <if test="totalAmount1 != null">and total_amount &gt;= #{totalAmount1}</if>
        <if test="totalAmount2 != null">and total_amount &lt;= #{totalAmount2}</if>
        order by entrusting_case_date desc
    </select>

</mapper>
