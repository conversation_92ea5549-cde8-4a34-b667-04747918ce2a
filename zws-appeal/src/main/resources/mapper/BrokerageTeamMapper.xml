<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.BrokerageTeamMapper">
    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.BrokerageTeam">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="team_id" property="teamId"/>
        <result column="team_name" property="teamName"/>
        <result column="settlement_date" property="settlementDate"/>
        <result column="settlement_day" property="settlementDay"/>
        <result column="payments_number" property="paymentsNumber"/>
        <result column="payments_money" property="paymentsMoney"/>
        <result column="brokerage" property="brokerage"/>
        <result column="states" property="states"/>
        <result column="voucher_id" property="voucherId"/>
        <result column="file_url" property="fileUrl"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_id" property="createById"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_id" property="updateById"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,team_id,team_name,settlement_date,settlement_day,payments_number,payments_money,brokerage,states,voucher_id,file_url
        ,create_by,create_by_id,create_time,update_by,update_by_id,update_time,del_flag
    </sql>

    <select id="selectAccountById" resultType="com.zws.appeal.domain.BrokerageTeam">
        select bro.id AS id,
        bro.team_id AS teamId,
        cre.cname AS teamName,
        bro.settlement_date AS settlementDate,
        bro.settlement_day AS settlementDay,
        bro.payments_number AS paymentsNumber,
        bro.payments_money AS paymentsMoney,
        bro.brokerage AS brokerage,
        bro.states AS states,
        bro.create_by AS createBy,
        bro.create_time AS createTime
        from financial_team_brokerage AS bro
        LEFT JOIN team_create AS cre ON (cre.id = bro.team_id and cre.delete_logo = 0)
        where bro.del_flag = 0
        <if test="states != null">
            and bro.states = #{states}
        </if>
        <if test="teamName != null and teamName != ''">
            and cre.cname like concat('%', #{teamName}, '%')
        </if>
        <if test="settlementDate != null and settlementDate != ''">
            and bro.settlement_date = #{settlementDate}
        </if>
        order by bro.create_time desc
    </select>

    <!--    <select id="selectById" resultType="java.lang.String">-->
    <!--        select cre.cname AS cname-->
    <!--        from financial_team_brokerage AS bro-->
    <!--        LEFT JOIN team_create AS cre ON (cre.id = bro.team_id and cre.delete_logo = 0)-->
    <!--        where bro.del_flag = 0-->
    <!--        <if test="idList != null and idList.size() > 0">-->
    <!--            and bro.id in-->
    <!--            <foreach item="item" collection="idList" separator="," open="(" close=")" index="">-->
    <!--                #{item}-->
    <!--            </foreach>-->
    <!--        </if>-->
    <!--    </select>-->

    <select id="selectTeamBrokerage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from financial_team_brokerage
        where del_flag = 0
        <if test="statesList != null and statesList.size() > 0">
            and states in
            <foreach item="item" collection="statesList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="settlementDate != null and settlementDate != ''">
            and settlement_date = #{settlementDate}
        </if>
        <if test="teamId != null">
            and team_id = #{teamId}
        </if>
        <if test="id != null">
            and id = #{id}
        </if>
        order by create_time desc
    </select>

    <select id="selectFileName" resultType="com.zws.appeal.domain.BrokerageTeam">
        select bro.settlement_date AS settlementDate,
               tea.cname           AS teamName
        from financial_team_brokerage AS bro
                 LEFT JOIN team_create AS tea ON (tea.id = bro.team_id and tea.delete_logo = 0)
        where bro.del_flag = 0
          and bro.id = #{id}
    </select>

    <select id="selectDetailsBrokerage" resultType="com.zws.appeal.domain.DetailsBrokerage">
        select id AS id,
        brokerage_id AS brokerageId,
        states AS states,
        reject_reason AS rejectReason,
        reject_date AS rejectDate,
        create_by AS createBy,
        create_by_id AS createById,
        create_time AS createTime,
        generate_not AS generateNot
        from financial_brokerage_details
        where del_flag = 0
        <if test="id != null">and id = #{id}</if>
        <if test="brokerageId != null">and brokerage_id = #{brokerageId}</if>
        <if test="statesList != null and statesList.size() > 0">
            and states in
            <foreach item="item" collection="statesList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        order by create_time desc
    </select>

    <select id="selectRecoveryAsset" resultType="com.zws.appeal.domain.RecoveryAsset">
        select ass.id              AS id,
               ass.details_id      AS detailsId,
               ass.transferor_id   AS transferorId,
               own.name            AS transferorName,
               ass.batch_num       AS batchNum,
               ass.case_id         AS caseId,
               ass.repayment_money AS repaymentMoney,
               ass.repayment_date  AS repaymentDate,
               ass.service_rate    AS serviceRate,
               ass.brokerage       AS brokerage,
               ass.create_by       AS createBy,
               ass.create_by_id    AS createById,
               ass.create_time     AS createTime
        from financial_asset_recovery AS ass
                 LEFT JOIN asset_owner AS own ON (own.id = ass.transferor_id and own.del_flag = 0)
        where ass.details_id = #{detailsId}
        order by ass.create_time desc
    </select>

    <select id="selectRiskTeam" resultType="com.zws.appeal.domain.RiskTeam">
        select id                AS id,
               details_id        AS detailsId,
               award_id          AS awardId,
               reward_name       AS rewardName,
               positive_negative AS positiveNegative,
               floating_rate     AS floatingRate,
               remarks           AS remarks,
               create_by         AS createBy,
               create_by_id      AS createById,
               create_time       AS createTime
        from financial_team_risk
        where details_id = #{detailsId}
        order by create_time desc
    </select>

    <select id="selectCount" resultType="java.math.BigDecimal">
        select sum(brokerage) AS money
        from financial_asset_recovery
        where details_id = #{detailsId}
    </select>

    <update id="updateDetailsBrokerage" parameterType="com.zws.appeal.domain.DetailsBrokerage">
        update financial_brokerage_details
        <set>
            <if test="brokerageId != null">
                brokerage_id = #{brokerageId,jdbcType=BIGINT},
            </if>
            <if test="states != null">
                states = #{states,jdbcType=INTEGER},
            </if>
            <if test="rejectReason != null">
                reject_reason = #{rejectReason,jdbcType=VARCHAR},
            </if>
            <if test="rejectDate != null">
                reject_date = #{rejectDate,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createById != null">
                create_by_id = #{createById,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateById != null">
                update_by_id = #{updateById,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="generateNot != null">
                generate_not = #{generateNot},
            </if>
            <if test="failureReason != null and failureReason != ''">
                failure_reason = #{failureReason},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateBrokerageTeam" parameterType="com.zws.appeal.domain.BrokerageTeam">
        update financial_team_brokerage
        <set>
            <if test="paymentsNumber != null">
                payments_number = #{paymentsNumber},
            </if>
            <if test="voucherId != null">
                voucher_id = #{voucherId},
            </if>
            <if test="fileUrl != null">
                file_url = #{fileUrl},
            </if>
            <if test="paymentsMoney != null">
                payments_money = #{paymentsMoney},
            </if>
            <if test="brokerage != null">
                brokerage = #{brokerage},
            </if>
            <if test="states != null">
                states = #{states},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateById != null">
                update_by_id = #{updateById},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--    <insert id="insert" parameterType="com.zws.appeal.domain.BrokerageTeam" useGeneratedKeys="true"-->
    <!--            keyProperty="id">-->
    <!--        insert into financial_team_brokerage (team_id, team_name, settlement_date, settlement_day, payments_number,-->
    <!--                                              payments_money, brokerage, states, create_by, create_by_id, create_time,-->
    <!--                                              del_flag, voucher_id, file_url)-->
    <!--        values (#{teamId}, #{teamName}, #{settlementDate}, #{settlementDay}, #{paymentsNumber}, #{paymentsMoney},-->
    <!--                #{brokerage}, #{states}, #{createBy}, #{createById}, #{createTime}, #{delFlag}, #{voucherId},-->
    <!--                #{fileUrl})-->
    <!--    </insert>-->

</mapper>
