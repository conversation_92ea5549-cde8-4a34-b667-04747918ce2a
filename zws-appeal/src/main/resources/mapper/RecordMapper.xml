<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.RecordMapper">

    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.CaseManage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="phone_state" jdbcType="INTEGER" property="phoneState"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="allocated_state" jdbcType="VARCHAR" property="allocatedState"/>
        <result column="case_state" jdbcType="VARCHAR" property="caseState"/>
        <result column="entrusting_party_id" jdbcType="BIGINT" property="entrustingPartyId"/>
        <result column="entrusting_party_name" jdbcType="VARCHAR" property="entrustingPartyName"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="batch_num" jdbcType="VARCHAR" property="batchNum"/>
        <result column="entrusting_case_batch_num" jdbcType="VARCHAR" property="entrustingCaseBatchNum"/>
        <result column="outsourcing_team_id" jdbcType="BIGINT" property="outsourcingTeamId"/>
        <result column="outsourcing_team_name" jdbcType="VARCHAR" property="outsourcingTeamName"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="client_sex" jdbcType="VARCHAR" property="clientSex"/>
        <result column="client_idcard" jdbcType="VARCHAR" property="clientIdcard"/>
        <result column="client_census_register" jdbcType="VARCHAR" property="clientCensusRegister"/>
        <result column="client_phone" jdbcType="VARCHAR" property="clientPhone"/>
        <result column="client_money" jdbcType="DECIMAL" property="clientMoney"/>
        <result column="client_residual_principal" jdbcType="DECIMAL" property="clientResidualPrincipal"/>
        <result column="client_overdue_start" jdbcType="TIMESTAMP" property="clientOverdueStart"/>
        <result column="account_period" jdbcType="INTEGER" property="accountPeriod"/>
        <result column="follow_up_state" jdbcType="VARCHAR" property="followUpState"/>
        <result column="follow_up_start" jdbcType="TIMESTAMP" property="followUpStart"/>
        <result column="follow_up_ast" jdbcType="TIMESTAMP" property="followUpAst"/>
        <result column="entrusting_case_date" jdbcType="TIMESTAMP" property="entrustingCaseDate"/>
        <result column="return_case_date" jdbcType="TIMESTAMP" property="returnCaseDate"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="label_asset" jdbcType="VARCHAR" property="labelAsset"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="odv_name" jdbcType="VARCHAR" property="odvName"/>
        <result column="urge_state" jdbcType="VARCHAR" property="urgeState"/>
        <result column="allocated_time" jdbcType="TIMESTAMP" property="allocatedTime"/>
        <result column="urge_power" jdbcType="VARCHAR" property="urgePower"/>
        <result column="del_flag" jdbcType="BIT" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="client_id_type" property="clientIdType"/>
        <result column="settlement_status" property="settlementStatus"/>
        <result column="remaining_due" property="remainingDue"/>
        <result column="remaining_due" property="remainingDue"/>
        <result column="sy_yh_principal" property="syYhPrincipal"/>
        <result column="client_birthday" property="clientBirthday"/>
        <result column="mediator_id" property="mediatorId"/>
        <result column="mediator_name" property="mediatorName"/>
    </resultMap>

    <resultMap id="UrgeRecordResultMap" type="com.zws.appeal.domain.record.UrgeRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="liaison" jdbcType="VARCHAR" property="liaison"/>
        <result column="relation" jdbcType="VARCHAR" property="relation"/>
        <result column="contact_mode" jdbcType="VARCHAR" property="contactMode"/>
        <result column="follow_up_state" jdbcType="VARCHAR" property="followUpState"/>
        <result column="urge_state" jdbcType="VARCHAR" property="urgeState"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="odv_name" jdbcType="VARCHAR" property="odvName"/>
        <result column="contact_medium" jdbcType="VARCHAR" property="contactMedium"/>
        <result column="promise_repayment_time" jdbcType="TIMESTAMP" property="promiseRepaymentTime"/>
        <result column="promise_repayment_money" jdbcType="DECIMAL" property="promiseRepaymentMoney"/>
        <result column="another_time" jdbcType="TIMESTAMP" property="anotherTime"/>
        <result column="promise_by_stages" jdbcType="INTEGER" property="promiseByStages"/>
        <result column="promise_every_money" jdbcType="DECIMAL" property="promiseEveryMoney"/>
        <result column="promise_repayment_day" jdbcType="DATE" property="promiseRepaymentDay"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="urge_tpye" jdbcType="VARCHAR" property="urgeTpye"/>
    </resultMap>

    <resultMap id="StagingRecordResultMap" type="com.zws.appeal.domain.record.StagingRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate"/>
        <result column="applicant" jdbcType="VARCHAR" property="applicant"/>
        <result column="applicant_id" jdbcType="BIGINT" property="applicantId"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="team_name" jdbcType="VARCHAR" property="teamName"/>
        <result column="staging_num" jdbcType="INTEGER" property="stagingNum"/>
        <result column="repayment_date" jdbcType="DATE" property="repaymentDate"/>
        <result column="repayment_monthly" jdbcType="DECIMAL" property="repaymentMonthly"/>
        <result column="examine_state" jdbcType="VARCHAR" property="examineState"/>
        <result column="examine_time" jdbcType="TIMESTAMP" property="examineTime"/>
        <result column="examine_by" jdbcType="VARCHAR" property="examineBy"/>
        <result column="examine_by_id" jdbcType="BIGINT" property="examineById"/>
        <result column="proce" jdbcType="INTEGER" property="proce"/>
        <result column="proce_sort" jdbcType="INTEGER" property="proceSort"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
    </resultMap>

    <resultMap id="RepaymentRecordResultMap" type="com.zws.appeal.domain.record.RepaymentRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="repayment_date" jdbcType="TIMESTAMP" property="repaymentDate"/>
        <result column="repayment_money" jdbcType="DECIMAL" property="repaymentMoney"/>
        <result column="repayment_mode" jdbcType="VARCHAR" property="repaymentMode"/>
        <result column="account_number" jdbcType="VARCHAR" property="accountNumber"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="repayment_type" jdbcType="VARCHAR" property="repaymentType"/>
        <result column="examine_state" jdbcType="VARCHAR" property="examineState"/>
        <result column="registrar_id" jdbcType="BIGINT" property="registrarId"/>
        <result column="registrar" jdbcType="VARCHAR" property="registrar"/>
        <result column="repayment_proof" jdbcType="VARCHAR" property="repaymentProof"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="ReorganizationRecordResultMap" type="com.zws.appeal.domain.ReorganizationVo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate"/>
        <result column="applicant" jdbcType="VARCHAR" property="applicant"/>
        <result column="applicant_id" jdbcType="BIGINT" property="applicantId"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="approve_id" jdbcType="BIGINT" property="approveId"/>
        <result column="team_name" jdbcType="VARCHAR" property="teamName"/>
        <result column="staging_num" jdbcType="INTEGER" property="stagingNum"/>
        <result column="repayment_date" jdbcType="DATE" property="repaymentDate"/>
        <result column="repayment_monthly" jdbcType="DECIMAL" property="repaymentMonthly"/>
        <result column="approve_state" jdbcType="VARCHAR" property="examineState"/>
        <result column="examine_time" jdbcType="TIMESTAMP" property="examineTime"/>
        <result column="examine_by" jdbcType="VARCHAR" property="examineBy"/>
        <result column="examine_by_id" jdbcType="BIGINT" property="examineById"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="repayment_plan"  property="repaymentPlan"/>
        <result column="down_payment_date"  property="downPaymentDate"/>
        <result column="down_payment"  property="downPayment"/>
        <result column="final_payment"  property="finalPayment"/>
        <result column="total_repayment"  property="totalRepayment"/>
        <result column="deduction_amount"  property="deductionAmount"/>
        <result column="reason"  property="reason"/>
        <result column="settle_agreement_url"  property="settleAgreementUrl"/>
        <result column="settle_agreement_time"  property="settleAgreementTime"/>
        <result column="state"  property="state"/>
    </resultMap>

    <resultMap id="ReductionRecordResultMap" type="com.zws.appeal.domain.record.ReductionRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate"/>
        <result column="applicant" jdbcType="VARCHAR" property="applicant"/>
        <result column="applicant_id" jdbcType="BIGINT" property="applicantId"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="amount_after_deduction" jdbcType="DECIMAL" property="amountAfterDeduction"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="after_reduction_date" jdbcType="TIMESTAMP" property="afterReductionDate"/>
    </resultMap>

    <resultMap id="SettleAgreementResultMap" type="com.zws.appeal.domain.SettleAgreementInfo">
        <result column="case_id" property="caseId"/>
        <result column="protocol" property="protocolName"/>
        <result column="protocol_number" property="protocolNo"/>
        <result column="name" property="transferorName"/>
        <result column="signing_date" property="transferorSignDate"/>
        <result column="residual_principal" property="principalMoney"/>
        <result column="base_date" property="baseDate"/>
        <result column="sy_yh_interest" property="interestMoney"/>
        <!--        <result column="state" property="compoundMoney"/>-->
        <!--        <result column="del_flag" property="penaltyMoney"/>-->
        <!--        <result column="create_by" property="defaultMoney"/>-->
        <!--        <result column="create_time" property="feeMoney"/>-->
        <result column="entrust_money" property="totalMoney"/>
        <!--        <result column="update_time" property="totalMoneyUpMoney"/>-->
        <result column="staging_num" property="stagingNum"/>
        <result column="repayment_monthly" property="repaymentMonthly"/>
        <!--        <result column="update_time" property="repaymentMonthlyUpMoney"/>-->
        <result column="amount_after_deduction" property="totalReductionMoney"/>
        <!--        <result column="update_time" property="totalReductionUpMoney"/>-->
        <result column="repayment_date" property="repaymentDate"/>
        <result column="overdue_start" property="initialAmountUpMoney"/>
        <result column="amount_final_date" property="finalAmountUpMoney"/>
        <result column="account_name" property="accountName"/>
        <result column="account_number" property="accountNumber"/>
        <result column="bank_name" property="bankName"/>
        <result column="apply_date" property="planDate"/>
    </resultMap>

    <resultMap id="OutsideRecordResultMap" type="com.zws.appeal.domain.record.OutsideRecord">
        <id column="id" property="id"/>
        <result column="case_id" property="caseId"/>
        <result column="outside_start" property="outsideStart"/>
        <result column="outside_address" property="outsideAddress"/>
        <result column="longitude_atitude_start" property="longitudeAtitudeStart"/>
        <result column="reason" property="reason"/>
        <result column="odv_id" property="odvId"/>
        <result column="odv_name" property="odvName"/>
        <result column="state" property="state"/>
        <result column="outside_end" property="outsideEnd"/>
        <result column="client_name" property="clientName"/>
        <result column="outside_content" property="outsideContent"/>
        <result column="photo" property="photo"/>
        <result column="sound_recording" property="soundRecording"/>
        <result column="registrant" property="registrant"/>
        <result column="registrant_id" property="registrantId"/>
        <result column="check_address" property="checkAddress"/>
        <result column="longitude_atitude_end" property="longitudeAtitudeEnd"/>
        <result column="check_date" property="checkDate"/>
        <result column="check_photo" property="checkPhoto"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by_id" property="createById"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="examine_by" property="examineBy"/>
        <result column="examine_by_id" property="examineById"/>
        <result column="examine_time" property="examineTime"/>
        <result column="proce" property="proce"/>
        <result column="proce_sort" property="proceSort"/>
        <result column="state_code" property="stateCode"/>
        <result column="entrusting_case_batch_num" property="entrustingCaseBatchNum"/>
        <result column="entrusting_case_date" property="entrustingCaseDate"/>
        <result column="return_case_date" property="returnCaseDate"/>
        <result column="odv_ids" property="odvIds"/>
        <result column="odv_names" property="odvNames"/>
    </resultMap>

    <resultMap id="NoteRecordResultMap" type="com.zws.appeal.domain.record.NoteRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="note_date" jdbcType="TIMESTAMP" property="noteDate"/>
        <result column="registrant" jdbcType="VARCHAR" property="registrant"/>
        <result column="registrant_id" jdbcType="BIGINT" property="registrantId"/>
        <result column="note_content" jdbcType="VARCHAR" property="noteContent"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="LawsuitRecordResultMap" type="com.zws.appeal.domain.record.LawsuitRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="lawsuit_date" jdbcType="TIMESTAMP" property="lawsuitDate"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="ComplaintRecordResultMap" type="com.zws.appeal.domain.record.ComplaintRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="complaint_date" jdbcType="TIMESTAMP" property="complaintDate"/>
        <result column="registrant" jdbcType="VARCHAR" property="registrant"/>
        <result column="registrant_id" jdbcType="BIGINT" property="registrantId"/>
        <result column="complaint_content" jdbcType="VARCHAR" property="complaintContent"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="AssistRecordResultMap" type="com.zws.appeal.domain.record.AssistRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate"/>
        <result column="applicant" jdbcType="VARCHAR" property="applicant"/>
        <result column="applicant_id" jdbcType="BIGINT" property="applicantId"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="helper" jdbcType="VARCHAR" property="helper"/>
        <result column="helper_id" jdbcType="BIGINT" property="helperId"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="assist_content" jdbcType="VARCHAR" property="assistContent"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="ApplyRecordResultMap" type="com.zws.appeal.domain.ApplyRecord">
        <result column="del_flag" property="delFlag"/>
        <result column="case_id" property="caseId"/>
        <result column="apply_state" property="applyState"/>
        <result column="apply_date" property="applyDate"/>
        <result column="applicant_id" property="applicantId"/>
        <result column="team_id" property="teamId"/>
        <result column="team_name" property="teamName"/>
        <result column="examine_state" property="examineState"/>
        <result column="examine_time" property="examineTime"/>
        <result column="examine_by" property="examineBy"/>
        <result column="examine_by_id" property="examineById"/>
        <result column="proce_sort" property="proceSort"/>
    </resultMap>

    <sql id="selectCaseManageVo">
        select id,
               case_id,
               contract_no,
               allocated_state,
               case_state,
               settlement_status,
               entrusting_party_id,
               entrusting_party_name,
               product_id,
               product_name,
               batch_num,
               entrusting_case_batch_num,
               outsourcing_team_id,
               outsourcing_team_name,
               client_name,
               client_sex,
               client_idcard,
               client_census_register,
               client_phone,
               client_money,
               client_residual_principal,
               client_overdue_start,
               account_period,
               follow_up_state,
               follow_up_start,
               follow_up_ast,
               entrusting_case_date,
               return_case_date,
               area,
               label,
               odv_id,
               odv_name,
               urge_state,
               allocated_time,
               urge_power,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               mediator_id,
               mediator_name
        from case_manage
    </sql>

    <sql id="selectUrgeRecordVo">
        select id,
               case_id,
               liaison,
               relation,
               contact_mode,
               follow_up_state,
               urge_state,
               content,
               remarks,
               odv_id,
               odv_name,
               contact_medium,
               promise_repayment_time,
               promise_repayment_money,
               another_time,
               promise_by_stages,
               promise_every_money,
               promise_repayment_day,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               urge_tpye
        from case_urge_record
    </sql>

    <sql id="selectStagingRecordVo">
        select id,
               case_id,
               apply_date,
               applicant,
               applicant_id,
               reason,
               team_id,
               team_name,
               staging_num,
               repayment_date,
               repayment_monthly,
               examine_state,
               examine_time,
               examine_by,
               examine_by_id,
               proce,
               proce_sort,
               del_flag
        from case_staging_record
    </sql>

    <sql id="selectRepaymentRecordVo">
        select id,
               case_id,
               repayment_date,
               repayment_money,
               repayment_mode,
               account_number,
               order_no,
               repayment_type,
               examine_state,
               registrar_id,
               registrar,
               repayment_proof,
               proce,
               proce_sort,
               del_flag,
               create_time,
               update_by,
               update_time
        from case_repayment_record
    </sql>

    <sql id="selectReductionRecordVo">
        select id,
               case_id,
               apply_date,
               applicant,
               applicant_id,
               reason,
               amount_after_deduction,
               state,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               proce,
               proce_sort,
               remaining_due,
               after_reduction_date
        from case_reduction_record
    </sql>

    <sql id="selectOutsideRecordVo">
        select id,
               case_id,
               outside_start,
               outside_address,
               longitude_atitude_start,
               reason,
               odv_id,
               odv_name,
               state,
               outside_end,
               client_name,
               outside_content,
               photo,
               sound_recording,
               registrant,
               registrant_id,
               check_address,
               longitude_atitude_end,
               check_date,
               check_photo,
               del_flag,
               create_by_id,
               create_by,
               create_time,
               update_by,
               update_time,
               examine_by,
               examine_by_id,
               examine_time,
               proce,
               proce_sort,
               state_code,
               entrusting_case_batch_num,
               entrusting_case_date,
               return_case_date,
               odv_ids,
               odv_names
        from case_outside_record
    </sql>

    <sql id="selectNoteRecordVo">
        select id,
               case_id,
               note_date,
               registrant,
               registrant_id,
               note_content,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from case_note_record
    </sql>

    <sql id="selectLawsuitRecordVo">
        select id,
               case_id,
               lawsuit_date,
               state,
               remarks,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from case_lawsuit_record
    </sql>

    <sql id="selectComplaintRecordVo">
        select id,
               case_id,
               complaint_date,
               registrant,
               registrant_id,
               complaint_content,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from case_complaint_record
    </sql>

    <sql id="selectAssistRecordVo">
        select id,
               case_id,
               apply_date,
               applicant,
               applicant_id,
               reason,
               helper,
               helper_id,
               state,
               assist_content,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               invalid
        from case_assist_record
    </sql>

    <sql id="selectApplyRecordVo">
        select id,
               case_id,
               apply_state,
               apply_date,
               applicant,
               applicant_id,
               reason,
               team_id,
               team_name,
               examine_state,
               examine_time,
               examine_by,
               examine_by_id,
               proce,
               proce_sort
        from case_apply_record
    </sql>

    <sql id="searchValueess">
        <if test="caseIds != null and caseIds.size() > 0">
            and cm.case_id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="teamId != null">
            and cm.outsourcing_team_id = #{teamId}
        </if>
        <if test="caseId != null">
            and cl.id like  concat('%',#{caseId},'%')
        </if>
        <if test="clientName != null and clientName != ''">
            and cl.client_name like  concat('%',#{clientName},'%')
        </if>
        <if test="closedMode != null and closedMode != ''">
            and tec.closed_mode = #{closedMode}
        </if>

        <if test="amount1 != null and amount2 != null">
            and cil.remaining_due >= #{amount1} and cil.remaining_due  &lt;= #{amount2}
        </if>
        <if test="executiveCourt != null and executiveCourt != ''">
            and tec.executive_court = #{executiveCourt}
        </if>
        <if test="startDate1 != null and startDate2 != null">
            and tec.create_time>=#{startDate1} and tec.create_time&lt;=#{startDate2}
        </if>
    </sql>


    <sql id="searchValues">
        <if test="caseIds != null and caseIds.size() > 0">
            and cm.case_id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="teamId != null">
            and cm.outsourcing_team_id = #{teamId}
        </if>
        <if test="caseId != null">
            and cl.id like  concat('%',#{caseId},'%')
        </if>
        <if test="clientName != null and clientName != ''">
            and cl.client_name like  concat('%',#{clientName},'%')
        </if>
        <if test="court != null and court != ''">
            and tfr.court=#{court}
        </if>
        <if test="applyTime1 != null and applyTime2 != null">
            and tfr.create_time>=#{applyTime1} and tfr.create_time&lt;=#{applyTime2}
        </if>
        <if test="applyAmount1 != null and applyAmount2 != null">
            and tfr.freeze_amount >=#{applyAmount1} and tfr.freeze_amount &lt;=#{applyAmount2}
        </if>
        <if test="actualAmount1 != null and actualAmount2 != null">
            and tfr.actual_freeze_amount >=#{actualAmount1} and tfr.actual_freeze_amount &lt;=#{actualAmount2}
        </if>
    </sql>

    <sql id="searchValue">
        <if test="caseIds != null and caseIds.size() > 0">
            and cm.case_id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="teamId != null">
            and cm.outsourcing_team_id=#{teamId}
        </if>
        <if test="disposeStage != null and disposeStage != ''">
            and  cm.dispose_stage = #{disposeStage}
        </if>
        <if test="packageName != null and packageName != ''">
            and am.package_name like concat('%', #{packageName}, '%')
        </if>
        <if test="caseId != null">
            and cl.id like concat('%', #{caseId}, '%')
        </if>
        <if test="clientName != null and clientName != ''">
            and cl.client_name like concat('%', #{clientName}, '%')
        </if>
        <if test="filingNumber != null and filingNumber != ''">
            and tfc.filing_number like concat('%', #{filingNumber}, '%')
        </if>
        <if test="court != null and court != ''">
            and tfc.court = #{court}
        </if>
        <if test="amount1 != null and amount2 != null">
            and cil.remaining_due >= #{amount1} and cil.remaining_due  &lt;= #{amount2}
        </if>
        <if test="isFreeze != null">
            and cm.is_freeze = #{isFreeze}
        </if>

        <if test="!allQuery and ids!=null and ids.size()>0">
            and cm.case_id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

    <sql id="searchValuee">
        <if test="caseIds != null and caseIds.size() > 0">
            and cm.case_id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="teamId != null">
            and cm.outsourcing_team_id=#{teamId}
        </if>
        <if test="clientName!=null and clientName!=''">
            and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>
        <if test="caseId != null and caseId !=''">
            and cl.id = #{caseId}
        </if>
        <if test="disposeStage != null and disposeStage != ''">
            and cm.dispose_stage = #{disposeStage}
        </if>
        <if test="judgeTime1 != null ">
            and ji.judgeTime &gt;= #{judgeTime1}
        </if>
        <if test="judgeTime2 != null ">
            and ji.judgeTime &lt;= #{judgeTime2}
        </if>
        <if test="remainingDue != null and remainingDue != ''">
            and cil.remaining_due = #{remainingDue}
        </if>
        <if test="trialCourt != null and trialCourt != ''">
            and li.trial_court = #{trialCourt}
        </if>
        <if test="!allQuery and ids!=null and ids.size()>0">
            and cm.case_id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

    <select id="selectCaseManageCaseId" resultMap="BaseResultMap">
        <include refid="selectCaseManageVo"></include>
        where del_flag = 0 and case_id = #{caseId}
    </select>

    <sql id="CaseManageVo">
        <if test="caseIds != null and caseIds.size() > 0">
            and cas.case_id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test=" uid!=null and uid!=''">
            and cas.uid=#{uid}
        </if>
        <if test=" disposeStage!=null and disposeStage!=''">
            and cas.dispose_stage=#{disposeStage} and cas.odv_id=#{odvId}
        </if>
        <if test=" mediatedStage!=null and mediatedStage!=''">
            and cas.mediated_stage=#{mediatedStage} and cas.mediator_id=#{odvId}
        </if>
        <if test=" saveStage!=null and saveStage!=''">
            and tfr.save_stage=#{saveStage}
            <if test="odvId != null ">and (cas.odv_id=#{odvId} or cas.mediator_id=#{odvId})</if>
        </if>
        <if test="settlementStatus != null ">and cas.settlement_status=#{settlementStatus}</if>
        <if test="followUpState != null ">and cas.follow_up_state=#{followUpState}</if>
        <if test="urgeState != null and urgeState != ''">and cas.urge_state = #{urgeState}</if>
        <if test="caseId != null">and cas.case_id = #{caseId}</if>
        <if test="clientName != null and clientName != ''">and cas.client_name = #{clientName}</if>
        <if test="clientPhone != null and clientPhone != ''">and cas.client_phone = #{clientPhone}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cas.client_idcard = #{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and cas.entrusting_case_batch_num =
            #{entrustingCaseBatchNum}
        </if>
        <if test="entrustingPartyId != null and entrustingPartyId != ''">and cas.entrusting_party_id =
            #{entrustingPartyId}
        </if>
        <if test="clientCensusRegister != null and clientCensusRegister != ''">
            and AES_DECRYPT(UNHEX(cas.client_census_register), #{decryptKey}) LIKE concat
            ('%',#{clientCensusRegister},'%')
        </if>
        <if test="clientCensusRegisters != null and clientCensusRegisters.size()>0">
            and (
            <foreach collection="clientCensusRegisters" item="item" separator="or">
                AES_DECRYPT(UNHEX(cas.client_census_register), #{decryptKey}) LIKE concat ('%',#{item},'%')
            </foreach>
            )
        </if>
        <if test="clientMoney1 != null ">and cas.client_money &gt;= #{clientMoney1}</if>
        <if test="clientMoney2 != null ">and cas.client_money &lt;= #{clientMoney2}</if>
        <if test="entrustingCaseDate1 != null ">and cas.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null ">and cas.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null ">and cas.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null ">and cas.return_case_date &lt;= #{returnCaseDate2}</if>
        <if test="followUpAst1 != null ">and cas.follow_up_ast &gt;= #{followUpAst1}</if>
        <if test="followUpAst2 != null ">and cas.follow_up_ast &lt;= #{followUpAst2}</if>
        <if test="syYhPrincipal1 != null">and cil.sy_yh_principal &gt;= #{syYhPrincipal1}</if>
        <if test="syYhPrincipal2 != null">and cil.sy_yh_principal &lt;= #{syYhPrincipal2}</if>
        <if test="notFollowUpDays1 != null ">and DATEDIFF(NOW(), cas.follow_up_ast) &gt;= #{notFollowUpDays1}</if>
        <if test="notFollowUpDays2 != null ">and DATEDIFF(NOW(), cas.follow_up_ast) &lt;= #{notFollowUpDays2}</if>
        <if test="area != null ">and cas.area=#{area}</if>
        <if test="exclusionsInvalid != null and exclusionsInvalid==1">
            and cas.client_phone NOT IN
            (
            SELECT
            DISTINCT
            contact_phone
            FROM
            case_info_contact
            WHERE phone_state = 1
            and del_flag = 0
            )
        </if>
        <if test="exclusionsAllowMaxNum != null and exclusionsAllowMaxNum==1">
            and AES_DECRYPT(UNHEX(cas.client_phone),#{decryptKey}) NOT IN
            (
            SELECT phone_number FROM
            (
            SELECT phone_number,COUNT(1) AS countNum
            FROM sms_send_records
            WHERE DATE(send_time) = DATE(NOW())
            and template_id = #{templateId}
            GROUP BY phone_number
            ) as tt
            WHERE tt.countNum >= #{allowMaxNum}
            )
        </if>
        <choose>
            <when test="label == 'nolabel' and labels == null and assetLabels == null">
                AND label IS NULL AND label_asset IS NULL
            </when>
        </choose>
        <if test="labels != null or assetLabels != null">
            and (
            <if test="labels != null and labels.size() > 0">
                cas.label in
                <foreach item="item" collection="labels" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="assetLabels != null and assetLabels.size() > 0">
                <if test="labels != null and labels.size() > 0">
                    or
                </if>
                cas.label_asset in
                <foreach item="item" collection="assetLabels" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="label == 'nolabel'">
                or (label is null and label_asset is null)
            </if>
            )
        </if>
    </sql>

    <!--根据登录人id以及其他条件查询该催收员的所有案件-->
    <select id="selectCaseManage" resultMap="BaseResultMap">
        select cas.id,
        cic.phone_state,
        cas.case_id,
        cas.contract_no,
        cas.allocated_state,
        cas.case_state,
        cas.settlement_status,
        cas.entrusting_party_id,
        cas.entrusting_party_name,
        cas.product_id,
        cas.product_name,
        cas.batch_num,
        cas.entrusting_case_batch_num,
        cas.outsourcing_team_id,
        cas.outsourcing_team_name,
        cas.client_name,
        cas.client_sex,
        cas.client_idcard,
        cas.client_census_register,
        cas.client_phone,
        cas.client_money,
        cas.client_residual_principal,
        cas.client_overdue_start,
        cas.account_period,
        cas.follow_up_state,
        cas.follow_up_start,
        cas.follow_up_ast,
        cas.entrusting_case_date,
        cas.return_case_date,
        cas.area,
        cas.odv_id,
        cas.odv_name,
        cas.urge_state,
        cas.allocated_time,
        cas.urge_power,
        cas.del_flag,
        cas.create_by,
        cas.create_time,
        cas.update_by,
        cas.update_time,
        cas.client_id_type,
        cas.uid,
        cas.label_asset,
        cm.id AS labelId,
        cm.label_content AS labelContent,
        cib.client_birthday,
        cil.sy_yh_principal,
        cil.remaining_due,
        cm.code AS label
        from case_manage AS cas
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN case_info_base AS cib ON (cib.case_id = cas.case_id)
        LEFT JOIN case_info_loan as cil on (cil.case_id=cas.case_id)
        LEFT JOIN
        case_info_contact AS cic
        ON (
        cic.case_id = cas.case_id
        and cic.contact_phone = cas.client_phone
        and cic.del_flag = 0
        )
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{outsourcingTeamId} and cas.case_state in
        ("0","1","3","6")
        and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
        <choose>
            <when test="orderBy!=null and orderBy=='clientMoney'">
                order by cas.client_money
            </when>
            <otherwise>
                order by cas.case_id
            </otherwise>
        </choose>
        <choose>
            <when test="sortOrder!=null and sortOrder==1">
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>

    <!--根据登录人id以及其他条件查询该催收员的所有案件-->
    <select id="selectCaseManageMoney" resultType="map">
        select count(1) AS number,
        sum(cas.client_money) AS money
        from case_manage AS cas
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{outsourcingTeamId} and cas.case_state in
        ("0","1","3","6")
        and cas.allocated_state = 1
        <!--        <include refid="CaseManageVo"></include>-->
        <if test="odvId != null ">and cas.odv_id=#{odvId}</if>
        order by cas.case_id desc
    </select>

    <select id="selectCaseManageByCaseId" resultMap="BaseResultMap">
        select id,
               case_id,
               contract_no,
               allocated_state,
               case_state,
               settlement_status,
               entrusting_party_id,
               entrusting_party_name,
               product_id,
               product_name,
               batch_num,
               entrusting_case_batch_num,
               outsourcing_team_id,
               outsourcing_team_name,
               client_name,
               client_sex,
               client_idcard,
               client_census_register,
               client_phone,
               client_money,
               client_residual_principal,
               client_overdue_start,
               account_period,
               follow_up_state,
               follow_up_start,
               follow_up_ast,
               entrusting_case_date,
               return_case_date,
               area,
               odv_id,
               odv_name,
               urge_state,
               allocated_time,
               urge_power,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from case_manage
        where del_flag = 0
          and case_id = #{caseId}
    </select>

    <!--根据登录人id以及其他条件查询该催收员的所有案件的案件金额以及总数量-->
    <select id="selectCaseManageMoneySize" resultType="map">
        select count(1) AS zongshu,
        sum(cas.client_money) AS zongjine
        from case_manage AS cas
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN case_info_base AS cib ON(cib.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{outsourcingTeamId} and cas.case_state in
        ("0","1","3","6")
        and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
    </select>

    <select id="selectMoneySizeById" resultType="map">
        select count(1) AS size,
        sum(loa.remaining_due) AS money,
        sum(loa.sy_yh_principal) AS principal
        from case_manage AS cas
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN case_info_base AS cib ON(cib.case_id = cas.case_id)
        LEFT JOIN case_info_loan AS loa ON(loa.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{outsourcingTeamId} and cas.case_state in
        ("0","1","3","6")
        and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
    </select>
    <!--查询未分配以及已分配数量-->
    <select id="selectCaseStateSize" resultType="com.zws.appeal.domain.CaseManage">
        select cas.case_id AS caseId
        from case_manage AS cas
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN case_info_base AS cib ON(cib.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{outsourcingTeamId} and cas.allocated_state = 1
        <if test="caseStates != null and caseStates.size() > 0">
            and cas.case_state in
            <foreach item="item" collection="caseStates" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <include refid="CaseManageVo"></include>
    </select>

    <!--查询上一条/下一条案件信息-->
    <select id="selectCaseManagePrevious" resultMap="BaseResultMap">
        select
        cas.id,
        cas.case_id,
        cas.contract_no,
        cas.allocated_state,
        cas.case_state,
        cas.settlement_status,
        cas.entrusting_party_id,
        cas.entrusting_party_name,
        cas.product_id,
        cas.product_name,
        cas.batch_num,
        cas.entrusting_case_batch_num,
        cas.outsourcing_team_id,
        cas.outsourcing_team_name,
        cas.client_name,
        cas.client_sex,
        cas.client_idcard,
        cas.client_census_register,
        cas.client_phone,
        cas.client_money,
        cas.client_residual_principal,
        cas.client_overdue_start,
        cas.account_period,
        cas.follow_up_state,
        cas.follow_up_start,
        cas.follow_up_ast,
        cas.entrusting_case_date,
        cas.return_case_date,
        cas.area,
        cas.label,
        cas.odv_id,
        cas.odv_name,
        cas.urge_state,
        cas.allocated_time,
        cas.urge_power,
        cas.del_flag,
        cas.create_by,
        cas.create_time,
        cas.update_by,
        cas.update_time,
        cas.uid
        from case_manage AS cas
        LEFT JOIN case_info_base AS cib ON (cib.case_id = cas.case_id)
        <if test=" saveStage!=null and saveStage!=''">
            LEFT JOIN team_freeze_record AS tfr ON cas.case_id = tfr.case_id
        </if>
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{outsourcingTeamId} and cas.case_state in
        ("0","1","3","6") and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
        order by cas.case_id desc
        LIMIT #{pageNumber},#{pageSize}
    </select>
    <!--案件计数-->
    <select id="selectCaseManageCount" resultType="int">
        select
        count(1)
        from case_manage AS cas
        LEFT JOIN case_info_base AS cib ON (cib.case_id = cas.case_id)
        <if test=" saveStage!=null and saveStage!=''">
            LEFT JOIN team_freeze_record AS tfr ON cas.case_id = tfr.case_id
        </if>
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{outsourcingTeamId} and cas.case_state in
        ("0","1","3","6") and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
        order by cas.case_id desc
    </select>

    <!--催收记录-->
    <select id="selectUrgeRecord" resultMap="UrgeRecordResultMap">
        <include refid="selectUrgeRecordVo"></include>
        where del_flag = 0
        <if test="caseId != null ">and case_id=#{caseId}</if>
        <if test="createId != null ">and create_id=#{createId}</if>
        <if test="createTime != null ">and create_time &gt;= #{createTime}</if>
        <if test="urgeTpye != null">and urge_tpye = #{urgeTpye}</if>
        or(
        <if test="caseIds != null and caseIds.size() > 0">
            case_id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        and import_reminder = 0
        <!--        <if test="createId != null ">and create_id=#{createId}</if>-->
        <!--        <if test="createTime != null ">and create_time &gt;= #{createTime}</if>-->
        )
        order by create_time desc
    </select>

    <select id="selectUrgeRecords" resultMap="UrgeRecordResultMap">
        <include refid="selectUrgeRecordVo"></include>
        where del_flag = 0
        <if test="caseId != null ">and case_id=#{caseId}</if>
        <if test="createId != null ">and create_id=#{createId}</if>
        <if test="createTime != null ">and create_time &gt;= #{createTime}</if>
        or(
        <if test="caseIds != null and caseIds.size() > 0">
            case_id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        and import_reminder = 0
        <!--        <if test="createId != null ">and create_id=#{createId}</if>-->
        <!--        <if test="createTime != null ">and create_time &gt;= #{createTime}</if>-->
        )
        order by create_time desc
    </select>

    <select id="selectUrgeRecordCount" resultType="int">
        select count(1) AS number
        from case_urge_record
        where del_flag = 0 and create_id = #{createId}
        <if test="createTime1 != null">and create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and create_time &lt;= #{createTime2}</if>
    </select>

    <select id="selectUrgeRecordId" resultType="com.zws.appeal.pojo.InheritanceCollection">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.promise_repayment_time AS promiseRepaymentTime,
        rep.promise_repayment_money AS promiseRepaymentMoney,
        rep.urge_state AS urgeState,
        rep.promise_every_money AS promiseEveryMoney,
        rep.promise_repayment_day AS promiseRepaymentDay,
        rep.promise_by_stages AS promiseByStages,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.remarks AS remarks,

        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.product_name AS productName,
        cm.client_phone AS clientPhone,
        cm.account_period AS accountPeriod,
        cm.client_id_type as clientIdType
        from case_urge_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        where rep.del_flag = 0 and rep.odv_id = #{odvId} and rep.urge_state in ("承诺还款","承诺分期还款") and
        rep.operation_type
        =
        #{operationType}
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">
            and AES_DECRYPT(UNHEX(cm.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>
        <if test="clientIdcard != null and clientIdcard != ''">
            and AES_DECRYPT(UNHEX(cm.client_idcard), #{decryptKey}) LIKE concat ('%',#{clientIdcard},'%')
        </if>
        <if test="clientPhone != null and clientPhone != ''">
            and AES_DECRYPT(UNHEX(cm.client_phone), #{decryptKey}) LIKE concat ('%',#{clientPhone},'%')
        </if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="entrustingPartyId != null">and cm.entrusting_party_id =#{entrustingPartyId}</if>
        <if test="promiseRepaymentMoney1 != null">and rep.promise_repayment_money &gt;= #{promiseRepaymentMoney1}</if>
        <if test="promiseRepaymentMoney2 != null">and rep.promise_repayment_money &lt;= #{promiseRepaymentMoney2}</if>
        <if test="accountPeriod != null and accountPeriod != ''">and cm.account_period =#{accountPeriod}</if>
        order by rep.create_time desc
    </select>

    <!--分期还款记录-->
    <select id="selectStagingRecord" resultMap="StagingRecordResultMap">
        <include refid="selectStagingRecordVo"></include>
        where del_flag = 0 and case_id = #{caseId} and team_id = #{teamId} and applicant_id = #{applicantId}
    </select>

    <!--资料调取记录-->
    <select id="selectRetrievalRecord" resultType="com.zws.appeal.domain.record.RetrievalRecord">
        select id,
               team_id       AS teamId,
               case_id       AS caseId,
               apply_date    AS applyDate,
               applicant     AS applicant,
               applicant_id  AS applicantId,
               reason        AS reason,
               examine_state AS examineState,
               examine_time  AS examineTime,
               examine_by    AS examineBy,
               examine_by_id AS examineById,
               proce         AS proce,
               proce_sort    AS proceSort,
               del_flag      AS delFlag
        from case_retrieval_record
        where del_flag = 0
          and case_id = #{caseId}
          and team_id = #{teamId}
          and applicant_id = #{applicantId}
    </select>

    <select id="selectStagingRecordId" resultType="com.zws.appeal.pojo.InheritanceCollection">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.staging_num AS stagingNum,
        rep.repayment_monthly AS repaymentMonthly,
        rep.repayment_date AS repaymentDates,
        rep.examine_state AS examineState,
        rep.examine_time AS examineTime,
        rep.applicant AS applicant,
        rep.apply_date AS applyDate,
        rep.reason AS reason,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_census_register AS clientCensusRegister,
        cm.product_name AS productName,
        cm.client_id_type as clientIdType
        from case_staging_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        where rep.del_flag = 0 and rep.applicant_id = #{applicantId} and rep.operation_type = #{operationType}
        <if test="examineState != null and examineState != ''">and rep.examine_state =#{examineState}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant =#{applicant}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="updateTime1 != null">and rep.examine_time &gt;= #{updateTime1}</if>
        <if test="updateTime2 != null">and rep.examine_time &lt;= #{updateTime2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by rep.apply_date desc
    </select>

    <!--回款记录-->
    <select id="selectRepaymentRecord" resultMap="RepaymentRecordResultMap">
        <include refid="selectRepaymentRecordVo"></include>
        where del_flag = 0 and case_id = #{caseId}
        <if test="createId != null ">and team_id=#{createId}</if>
        <if test="createTime != null ">and create_time &gt;= #{createTime}</if>
        <if test="importNot != null ">and import_not is null</if>
        order by create_time desc
    </select>

    <select id="selectRepaymentRecordId" resultType="com.zws.appeal.pojo.InheritanceCollection">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.repayment_money AS repaymentMoney,
        rep.repayment_date AS repaymentDate,
        rep.repayment_type AS repaymentType,
        rep.examine_state AS examineState,
        rep.create_time AS createTime,
        rep.update_time AS updateTime,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.settle_certificate_url AS settleCertificateUrl,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_money AS clientMoney,
        cm.client_census_register AS clientCensusRegister,
        cm.client_id_type as clientIdType
        from case_repayment_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        where rep.del_flag = 0 and rep.registrar_id = #{registrarId} and rep.operation_type = #{operationType}
        <if test="examineState != null and examineState != ''">and rep.examine_state =#{examineState}</if>
        <if test="repaymentType != null and repaymentType != ''">and rep.repayment_type =#{repaymentType}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="repaymentMoney1 != null">and rep.repayment_money &gt;= #{repaymentMoney1}</if>
        <if test="repaymentMoney2 != null">and rep.repayment_money &lt;= #{repaymentMoney2}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="entrustingPartyId != null">and cm.entrusting_party_id =#{entrustingPartyId}</if>
        <if test="clientMoney1 != null">and cm.client_money &gt;= #{clientMoney1}</if>
        <if test="clientMoney2 != null">and cm.client_money &lt;= #{clientMoney2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        <if test="repaymentDate1 != null">and rep.repayment_date &gt;= #{repaymentDate1}</if>
        <if test="repaymentDate2 != null">and rep.repayment_date &lt;= #{repaymentDate2}</if>
        order by rep.create_time desc
    </select>

    <!--按条件搜索资料调取记录-->
    <select id="selectRetrievalRecordId" resultType="com.zws.appeal.pojo.InheritanceCollection">
        select ret.id AS id,
        ret.case_id AS caseId,
        ret.entrusting_case_batch_num AS entrustingCaseBatchNum,
        ret.examine_state AS examineState,
        ret.examine_time AS updateTime,
        ret.reason AS reason,
        ret.apply_date AS applyDate,
        ret.can_download AS canDownload,
        ret.random AS random,
        ret.watermarked_file_path AS watermarkedFilePath,
        ret.archive_file_address AS archiveFileAddress,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard
        from case_retrieval_record AS ret
        LEFT JOIN case_manage AS cm ON (ret.case_id = cm.case_id and cm.del_flag = 0)
        where ret.del_flag = 0 and ret.applicant_id = #{registrarId} and ret.operation_type = #{operationType}
        <if test="examineState != null and examineState != ''">and ret.examine_state =#{examineState}</if>
        <if test="caseId != null">and ret.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="applyDate1 != null">and ret.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and ret.apply_date &lt;= #{applyDate2}</if>
        <if test="updateTime1 != null">and ret.examine_time &gt;= #{updateTime1}</if>
        <if test="updateTime2 != null">and ret.examine_time &lt;= #{updateTime2}</if>
        order by ret.apply_date desc
    </select>

    <select id="selectRepaymentByTime" resultType="java.math.BigDecimal">
        select sum(bil.repayment_money) AS repaymentMoney
        from case_repayment_record AS rep
        LEFT JOIN financial_bill AS bil ON (rep.bill_id = bil.id)
        where rep.del_flag = 0 and rep.team_id = #{teamId} and rep.examine_state = #{examineState}
        <if test="updateTime1 != null">and bil.repayment_time &gt;= #{updateTime1}</if>
        <if test="updateTime2 != null">and bil.repayment_time &lt;= #{updateTime2}</if>
    </select>

    <select id="selectHistoryMoney" resultType="java.math.BigDecimal">
        select sum(remaining_due) AS remainingDue
        from case_allocated_history
        where del_flag = 0 and team_id = #{teamId}
        <if test="updateTime1 != null">and entrusting_case_date &gt;= #{updateTime1}</if>
        <if test="updateTime2 != null">and entrusting_case_date &lt;= #{updateTime2}</if>
    </select>

    <select id="selectRepaymentRecordIdByTime" resultType="java.math.BigDecimal">
        select sum(repayment_money) AS repaymentMoney
        from case_repayment_record
        where del_flag = 0 and team_id = #{teamId} and examine_state = #{examineState}
        <if test="updateTime1 != null">and update_time &gt;= #{updateTime1}</if>
        <if test="updateTime2 != null">and update_time &lt;= #{updateTime2}</if>
    </select>

    <select id="selectRepaymentRecordById" resultType="com.zws.appeal.domain.record.RepaymentRecord">
        select repayment_proof AS repaymentProof, examine_state AS examineState
        from case_repayment_record
        where del_flag = 0
          and id = #{id}
    </select>

    <select id="selectRepaymentRecordUrl" resultType="com.zws.appeal.domain.record.RepaymentRecord">
        select settle_certificate_url AS settleCertificateUrl
        from case_repayment_record
        where del_flag = 0
          and id = #{id}
    </select>

    <!--还款方式下拉查询-->
    <select id="selectRepaymentSetup" resultType="com.zws.appeal.domain.RepaymentSetup">
        select id,
        repayment_method AS repaymentMethod,
        register_payment AS registerPayment,
        reconciliation,
        remark,
        state,
        del_flag AS delFlag,
        create_by AS createBy,
        create_time AS createTime,
        update_by AS updateBy,
        update_time AS updateTime
        from case_repayment_setup
        where del_flag = 0
        <if test="repaymentMethod != null and repaymentMethod != ''">
            and repayment_method = #{repaymentMethod}
        </if>
        <if test="state != null">
            and state = #{state}
        </if>
        order by id desc
    </select>

    <!--获取还款方式的登记必填字段-->
    <select id="selectRepaymentSetupId" resultType="com.zws.appeal.domain.RepaymentSetup">
        select id,
               repayment_method AS repaymentMethod,
               register_payment AS registerPayment,
               reconciliation,
               remark,
               state,
               del_flag         AS delFlag,
               create_by        AS createBy,
               create_time      AS createTime,
               update_by        AS updateBy,
               update_time      AS updateTime
        from case_repayment_setup
        where del_flag = 0
          and id = #{id}
    </select>

    <!--减免记录-->
    <select id="selectReductionRecord" resultMap="ReductionRecordResultMap">
        <include refid="selectReductionRecordVo"></include>
        where del_flag = 0 and case_id = #{caseId}
        order by apply_date desc
    </select>

    <select id="selectReductionRecordById" resultMap="ReductionRecordResultMap">
        <include refid="selectReductionRecordVo"></include>
        where del_flag = 0 and id = #{id}
        order by apply_date desc limit 1
    </select>

    <select id="selectStagingRecordById" resultMap="StagingRecordResultMap">
        <include refid="selectStagingRecordVo"></include>
        where del_flag = 0 and id = #{id}
        order by apply_date desc limit 1
    </select>

    <select id="selectSettleAgreementInfo" resultMap="SettleAgreementResultMap">
        select
        cl.id as case_id,
        IFNULL(am.protocol,'') as protocol,
        IFNULL(am.protocol_number,'') as protocol_number,
        IFNULL(ao.`name`,'') as `name`,
        IFNULL(am.signing_date,'') as signing_date,
        IFNULL(cil.residual_principal,'') as residual_principal,
        IFNULL(DATE_FORMAT(cil.base_date, '%Y-%m-%d'),'') as base_date,
        IFNULL(cil.sy_yh_interest,'') as sy_yh_interest,
        IFNULL(cil.entrust_money,'') as entrust_money,
        IFNULL(csr.staging_num,'') as staging_num,
        IFNULL(csr.repayment_monthly,'') as repayment_monthly,
        IFNULL(cil.sy_yh_principal,'') as sy_yh_principal,
        IFNULL(cil.amount_after_deduction,'') as amount_after_deduction,
        IFNULL(DATE_FORMAT(cil.repayment_date, '%Y-%m-%d'),'') as repayment_date,
        IFNULL(DATE_FORMAT(cil.overdue_start, '%Y-%m-%d'),'') as overdue_start,
        IFNULL(DATE_FORMAT(cil.amount_final_date, '%Y-%m-%d'),'') as amount_final_date,
        IFNULL(fa.account_name,'') as account_name,
        IFNULL(fa.account_number,'') as account_number,
        IFNULL(fa.bank_name,'') as bank_name,
        IFNULL(DATE_FORMAT(csr.apply_date, '%Y-%m-%d'),'') as apply_date
        from case_library cl
        left join asset_manage am on cl.batch_no = am.batch_num
        left join case_info_loan cil on cl.id = cil.case_id
        left join asset_owner ao on am.owner_id = ao.id
        left join case_staging_record csr on csr.case_id = cl.id
        left join financial_account fa on fa.account_number = am.open_account
        where cl.del_flag = 0
        and cl.id = #{caseId}
        <if test="stagingId != null">
            and csr.id = #{stagingId}
        </if>

    </select>

    <select id="selectSettleAgreementInfoOfReduction" resultMap="SettleAgreementResultMap">
        select
        cl.id as case_id,
        IFNULL(am.protocol,'') as protocol,
        IFNULL(am.protocol_number,'') as protocol_number,
        IFNULL(ao.`name`,'') as `name`,
        IFNULL(am.signing_date,'') as signing_date,
        IFNULL(cil.residual_principal,'') as residual_principal,
        IFNULL(DATE_FORMAT(cil.base_date, '%Y-%m-%d'),'') as base_date,
        IFNULL(cil.sy_yh_interest,'') as sy_yh_interest,
        IFNULL(cil.entrust_money,'') as entrust_money,
        IFNULL(cil.sy_yh_principal,'') as sy_yh_principal,
        IFNULL(cil.amount_after_deduction,'') as amount_after_deduction,
        IFNULL(DATE_FORMAT(cil.repayment_date, '%Y-%m-%d'),'') as repayment_date,
        IFNULL(DATE_FORMAT(cil.overdue_start, '%Y-%m-%d'),'') as overdue_start,
        IFNULL(DATE_FORMAT(cil.amount_final_date, '%Y-%m-%d'),'') as amount_final_date,
        IFNULL(fa.account_name,'') as account_name,
        IFNULL(fa.account_number,'') as account_number,
        IFNULL(fa.bank_name,'') as bank_name,
        IFNULL(DATE_FORMAT(crr.apply_date, '%Y-%m-%d'),'') as apply_date
        from case_library cl
        left join asset_manage am on cl.batch_no = am.batch_num
        left join case_info_loan cil on cl.id = cil.case_id
        left join asset_owner ao on am.owner_id = ao.id
        left join financial_account fa on fa.account_number = am.open_account
        left join case_reduction_record crr on crr.case_id = cl.id
        where cl.del_flag = 0
        and cl.id = #{caseId}
        <if test="reductionId != null">
            and crr.id = #{reductionId}
        </if>
    </select>

    <select id="selectReductionRecordCreateId" resultMap="ReductionRecordResultMap">
        <include refid="selectReductionRecordVo"></include>
        where del_flag = 0 and case_id = #{caseId} and team_id = #{teamId} and applicant_id = #{applicantId}
    </select>

    <select id="selectInfoLoan" resultType="java.math.BigDecimal">
        select remaining_due AS remainingDue
        from case_info_loan
        where del_flag = 0
          and case_id = #{caseId}
    </select>

    <select id="selectReductionRecordCaseId" resultMap="ReductionRecordResultMap">
        <include refid="selectReductionRecordVo"></include>
        where del_flag = 0 and case_id = #{caseId}
        <if test="createTime != null">and apply_date &gt;= #{createTime}</if>
        <if test="createId != null">and team_id = #{createId}</if>
        order by apply_date desc
    </select>

    <select id="selectReductionRecordId" resultType="com.zws.appeal.pojo.InheritanceCollection">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.amount_after_deduction AS amountAfterDeduction,
        rep.update_time AS updateTime,
        rep.applicant AS applicant,
        rep.apply_date AS applyDate,
        rep.reason AS reason,
        rep.state AS state,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.remaining_due as remainingDue,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_census_register AS clientCensusRegister,
        cm.client_id_type as clientIdType,
        rep.after_reduction_date as afterReductionDate
        from case_reduction_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        where rep.del_flag = 0 and rep.applicant_id = #{applicantId} and rep.operation_type = #{operationType}
        <if test="state != null and state != ''">and rep.state =#{state}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant =#{applicant}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="updateTime1 != null">and rep.update_time &gt;= #{updateTime1}</if>
        <if test="updateTime2 != null">and rep.update_time &lt;= #{updateTime2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by rep.apply_date desc
    </select>

    <select id="selectReductionFile" resultType="com.zws.appeal.domain.record.ReductionFile">
        select id,
               record_id     AS recordId,
               first_name    AS firstName,
               modify_name   AS modifyName,
               file_url      AS fileUrl,
               founder       AS founder,
               creation_time AS creationTime
        from case_reduction_file
        where record_id = #{id}
    </select>

    <!--外访记录-->
    <select id="selectOutsideRecord" resultMap="OutsideRecordResultMap">
        <include refid="selectOutsideRecordVo"></include>
        where del_flag = 0 and case_id = #{caseId}
        <if test="createTime != null">and create_time &gt;= #{createTime}</if>
        <if test="teamId !=null">and team_id=#{teamId}</if>
        order by create_time desc
    </select>

    <!--外访资源分组数量查询-->
    <select id="selectOutsideResource" resultType="map">
        select count(1)      AS number,
               resource_type AS resourceType
        from case_outside_resource
        where del_flag = 0
          and outside_id = #{outsideId}
        group by resource_type
    </select>

    <!--外访资源查询-->
    <select id="selectOutsideResourceById" resultType="com.zws.appeal.domain.record.OutsideResource">
        select id,
               outside_id    AS outsideId,
               resource_type AS resourceType,
               resource_name AS resourceName,
               content,
               create_by     AS createBy,
               create_time   AS createTime
        from case_outside_resource
        where del_flag = 0
          and outside_id = #{outsideId}
          and resource_type = #{resourceType}
    </select>

    <select id="selectOutsideRecordByUserId" resultMap="OutsideRecordResultMap">
        <include refid="selectOutsideRecordVo"></include>
        where del_flag = 0 and case_id = #{caseId} and create_by_id = #{userId} and team_id = #{teamId}
    </select>

    <select id="selectOutsideRecordIdBy" resultMap="OutsideRecordResultMap">
        <include refid="selectOutsideRecordVo"></include>
        where del_flag = 0 and case_id = #{caseId} and id = #{id}
    </select>

    <select id="selectOutsideRecordById" resultMap="OutsideRecordResultMap">
        <include refid="selectOutsideRecordVo"></include>
        where del_flag = 0 and id = #{id}
    </select>

    <select id="selectOutsideRecordId" resultType="com.zws.appeal.pojo.OutsideRecordCollection">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.create_by AS createBy,
        rep.create_time AS createTime,
        rep.odv_name AS odvName,
        rep.outside_address AS outsideAddress,
        rep.reason AS reason,
        rep.outside_start AS outsideStart,
        rep.state AS state,
        rep.examine_time AS examineTime,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_census_register AS clientCensusRegister,
        cm.product_name AS productName,
        cm.client_id_type as clientIdType
        from case_outside_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        where rep.del_flag = 0 and rep.create_by_id = #{createById} and rep.operation_type = #{operationType}

        <if test="state != null and state != ''">and rep.state =#{state}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="createBy != null and createBy != ''">and rep.create_by =#{createBy}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="examineTime1 != null">and rep.examine_time &gt;= #{examineTime1}</if>
        <if test="examineTime2 != null">and rep.examine_time &lt;= #{examineTime2}</if>
        <if test="createTime1 != null">and rep.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and rep.create_time &lt;= #{createTime2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by rep.create_time desc
    </select>

    <select id="selectOutsideRecordIdByTime" resultType="int">
        select count(1) AS number
        from case_outside_record
        where del_flag = 0 and team_id = #{teamId} and state_code in(3,4,5)
        <if test="recordTime1 != null">and examine_time &gt;= #{recordTime1}</if>
        <if test="recordTime2 != null">and examine_time &lt;= #{recordTime2}</if>
    </select>

    <!--便签记录-->
    <select id="selectNoteRecord" resultMap="NoteRecordResultMap">
        <include refid="selectNoteRecordVo"></include>
        where del_flag = 0 and case_id = #{caseId}
        <if test="createTime != null">and note_date &gt;= #{createTime}</if>
        <if test="createId != null">and team_id = #{createId}</if>
        order by note_date desc
    </select>
    <!--诉讼记录-->
    <select id="selectLawsuitRecord" resultType="com.zws.appeal.domain.record.LawsuitRecord">
        select ase.id         AS id,
               ase.case_id    AS caseId,
               ase.start_time AS lawsuitDate,
               ase.state      AS state,
               ase.explain    AS remarks
        from case_lawsuit_status AS ase
        where ase.del_flag = 0
          and ase.case_id = #{caseId}
        order by ase.start_time desc
    </select>
    <!--投诉记录-->
    <select id="selectComplaintRecord" resultMap="ComplaintRecordResultMap">
        <include refid="selectComplaintRecordVo"></include>
        where del_flag = 0 and case_id = #{caseId}
        <if test="createTime != null">and complaint_date &gt;= #{createTime}</if>
        <if test="createId != null">and team_id = #{createId}</if>
        order by complaint_date desc
    </select>
    <!--协催记录-->
    <select id="selectAssistRecord" resultType="com.zws.appeal.domain.record.AssistRecord">
        select
        ass.applicant AS applicant,
        ass.reason AS reason,

        det.create_time AS applyDate,
        det.id AS detailsId,
        det.create_by AS helper,
        det.state AS state,
        det.assist_content AS assistContent
        from case_assist_record AS ass
        LEFT JOIN case_assist_details AS det ON (ass.id = det.assist_id)
        where ass.del_flag = 0
        and ass.case_id = #{caseId}
        <if test="createTime != null">and ass.apply_date &gt;= #{createTime}</if>
        <if test="teamId != null">and ass.team_id = #{teamId}</if>
        order by det.create_time desc
    </select>

    <select id="selectAssistRecordId" resultType="com.zws.appeal.pojo.InheritanceCollection">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.helper AS helper,
        rep.state AS state,
        rep.helper_id AS helperId,
        rep.apply_date AS applyDate,
        rep.reason AS reason,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_money AS clientMoney,
        cm.client_census_register AS clientCensusRegister,
        cm.client_id_type as clientIdType
        from case_assist_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        where rep.del_flag = 0 and rep.applicant_id = #{applicantId} and rep.invalid = #{invalid} and rep.operation_type
        = #{operationType}
        <if test="state != null">and rep.state =#{state}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="entrustingPartyId != null">and cm.entrusting_party_id = #{entrustingPartyId}</if>
        <if test="clientMoney1 != null">and cm.client_money &gt;= #{clientMoney1}</if>
        <if test="clientMoney2 != null">and cm.client_money &lt;= #{clientMoney2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        order by rep.apply_date desc
    </select>

    <select id="selectAssistRecordHelperId" resultType="com.zws.appeal.pojo.InheritanceCollection">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.applicant AS applicant,
        rep.state AS state,
        rep.apply_date AS applyDate,
        rep.reason AS reason,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_money AS clientMoney,
        cm.client_phone AS clientPhone,
        cm.client_id_type as clientIdType
        from case_assist_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        where rep.del_flag = 0 and rep.helper_id = #{helperId} and invalid = #{invalid}
        <if test="state != null">and rep.state =#{state}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="clientPhone != null and clientPhone != ''">and cm.client_phone =#{clientPhone}</if>
        <if test="productId != null">and cm.product_name = #{productId}</if>
        <if test="clientMoney1 != null">and cm.client_money &gt;= #{clientMoney1}</if>
        <if test="clientMoney2 != null">and cm.client_money &lt;= #{clientMoney2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        order by rep.apply_date desc
    </select>

    <select id="selectAssistRecordNumber" resultType="map">
        select count(1) AS number,
        state AS state
        from case_assist_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        where rep.del_flag = 0 and rep.helper_id = #{helperId} and invalid = #{invalid}
        <if test="examineState != null and examineState != ''">and rep.state =#{state}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="clientPhone != null and clientPhone != ''">and cm.client_phone =#{clientPhone}</if>
        <if test="productId != null">and cm.product_id = #{productId}</if>
        <if test="clientMoney1 != null">and cm.client_money &gt;= #{clientMoney1}</if>
        <if test="clientMoney2 != null">and cm.client_money &lt;= #{clientMoney2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        group by state
    </select>

    <select id="selectAssistDetails" resultType="com.zws.appeal.domain.AssistDetails">
        select de.id,
               de.case_id        AS caseId,
               de.assist_id      AS assistId,
               de.state,
               de.assist_content AS assistContent,
               de.create_by      AS createBy,
               de.create_time    AS createTime
        from case_assist_details AS de
                 LEFT JOIN case_assist_record AS re ON (de.assist_id = re.id)
        where de.assist_id = #{caseId}
    </select>

    <select id="selectAssistRecordInvalid" resultMap="AssistRecordResultMap">
        <include refid="selectAssistRecordVo"></include>
        where del_flag = 0 and invalid=#{invalid}
        <if test="caseId != null and caseId.size() > 0">
            and case_id in
            <foreach item="item" collection="caseId" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectNoteRecordLast" resultMap="NoteRecordResultMap">
        <include refid="selectNoteRecordVo"></include>
        where del_flag = 0 and case_id = #{caseId}
        <if test="createId != null">and team_id =#{createId}</if>
        <if test="createTime != null">and note_date &gt;= #{createTime}</if>
        order by id desc LIMIT 0,1
    </select>

    <select id="selectComplaintRecordLast" resultMap="ComplaintRecordResultMap">
        <include refid="selectComplaintRecordVo"></include>
        where del_flag = 0 and case_id = #{caseId}
        <if test="createId != null">and team_id = #{createId}</if>
        <if test="createTime != null">and complaint_date &gt;= #{createTime}</if>
        order by id desc LIMIT 0,1
    </select>

    <select id="selectApplyRecordId" resultType="com.zws.appeal.pojo.InheritanceCollection">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.examine_state AS examineState,
        rep.examine_time AS examineTime,
        rep.applicant AS applicant,
        rep.apply_date AS applyDate,
        rep.reason AS reason,
        rep.stay_case_time AS stayCaseTime,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_census_register AS clientCensusRegister,
        cm.client_id_type as clientIdType
        from case_apply_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        where rep.del_flag = 0 and rep.applicant_id = #{applicantId} and rep.apply_state = #{applyState} and
        rep.operation_type = #{operationType}
        <if test="examineState != null and examineState != ''">and rep.examine_state =#{examineState}</if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant =#{applicant}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="updateTime1 != null">and rep.examine_time &gt;= #{updateTime1}</if>
        <if test="updateTime2 != null">and rep.examine_time &lt;= #{updateTime2}</if>
        <if test="applyDate1 != null">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by rep.apply_date desc
    </select>

    <select id="selectWorkOrder" resultType="com.zws.appeal.pojo.WorkOrderExtends">
        select wor.id,
        wor.case_id AS caseId,
        wor.question_type AS questionType,
        wor.channel_source AS channelSource,
        wor.order_status AS orderStatus,
        wor.call_number AS callNumber,
        wor.question_content AS questionContent,
        wor.create_by_id AS createById,
        wor.create_by AS createBy,
        wor.create_time AS createTime,
        wor.handler_id AS handlerId,
        wor.handler AS handler,
        wor.processing_time AS processingTime,
        wor.entrusting_case_batch_num AS entrustingCaseBatchNum,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_phone AS clientPhone,
        cm.product_name AS productName,
        cm.odv_name AS odvName,
        cm.client_id_type as clientIdType,
        cm.uid as uid,
        cic.phone_state as phoneState
        from case_work_order AS wor
        LEFT JOIN case_manage AS cm ON (wor.case_id = cm.case_id and cm.del_flag = 0)
        LEFT JOIN case_info_contact AS cic ON (
        cic.case_id = cm.case_id and cic.contact_phone = wor.call_number and cic.del_flag = '0'
        )
        where wor.del_flag = 0
        and wor.odv_id = #{odvId}
        <if test="caseId != null">and wor.case_id =#{caseId}</if>
        <if test="uid != null and uid != ''">and cm.uid =#{uid}</if>
        <if test="odvName != null">and cm.odv_name =#{odvName}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="clientPhone != null and clientPhone != ''">and cm.client_phone =#{clientPhone}</if>
        <if test="questionType != null and questionType != ''">and wor.question_type =#{questionType}</if>
        <if test="orderStatus != null and orderStatus != ''">and wor.order_status =#{orderStatus}</if>
        <if test="createTime1 != null">and wor.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and wor.create_time &lt;= #{createTime2}</if>
        order by wor.create_time desc
    </select>

    <select id="selectWorkOrderById" resultType="com.zws.appeal.domain.WorkOrder">
        select ord.id,
               ord.case_id                   AS caseId,
               ord.question_type             AS questionType,
               ord.order_status              AS orderStatus,
               ord.create_time               AS createTime,
               ord.question_content          AS questionContent,
               ord.channel_source            AS channelSource,
               ord.complaint_level           AS complaintLevel,
               ord.entrusting_case_batch_num AS entrustingCaseBatchNum,
               ord.call_number               AS callNumber,
               cre.cname                     AS cname,
               emp.employee_name             AS employeeName,
               us.nick_name                  AS nickName
        from case_work_order AS ord
                 LEFT JOIN team_create AS cre ON (cre.id = ord.team_id)
                 LEFT JOIN team_employees AS emp ON (emp.id = ord.odv_id)
                 LEFT JOIN sys_user AS us ON (us.user_id = ord.create_by_id)
        where ord.del_flag = 0
          and ord.id = #{id}
    </select>

    <select id="selectDictData" resultType="com.zws.appeal.pojo.DictDataOrder">
        select dict_label AS info,
               dict_value AS code
        from sys_dict_data
        where status = 0
          and dict_type = #{dictType}
    </select>

    <select id="selectWorkFollowUpByWoId" resultType="com.zws.system.api.domain.WorkFollowUp">
        select id,
               order_id            AS orderId,
               order_state         AS orderState,
               work_follow_content AS workFollowContent,
               created_by_id       AS createdById,
               created_by          AS createdBy,
               created_time        AS createdTime
        from case_work_followUp
        where del_flag = 0
          and order_id = #{id}
    </select>

    <select id="selectWorkAnnex" resultType="com.zws.system.api.domain.WorkAnnex">
        select id,
               work_id       AS workId,
               file_url      AS fileUrl,
               file_name     AS fileName,
               created_by_id AS createdById,
               created_by    AS createdBy,
               created_time  AS createdTime
        from case_work_annex
        where del_flag = 0
          and follow_id = #{followId}
    </select>

    <select id="selectWorkOrderNumber" resultType="map">
        select count(1) AS number,
        order_status AS orderStatus
        from case_work_order AS wor
        LEFT JOIN case_manage AS cm ON (wor.case_id = cm.case_id and cm.del_flag = 0)
        where wor.del_flag = 0
        and wor.odv_id = #{odvId}
        <if test="caseId != null">and wor.case_id =#{caseId}</if>
        <if test="uid != null and uid != ''">and cm.uid =#{uid}</if>
        <if test="odvName != null">and cm.odv_name =#{odvName}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="clientPhone != null and clientPhone != ''">and cm.client_phone =#{clientPhone}</if>
        <if test="questionType != null and questionType != ''">and wor.question_type =#{questionType}</if>
        <if test="orderStatus != null and orderStatus != ''">and wor.order_status =#{orderStatus}</if>
        <if test="createTime1 != null">and wor.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and wor.create_time &lt;= #{createTime2}</if>
        group by order_status
    </select>

    <select id="selectAssistRecordCaseId" resultType="com.zws.appeal.domain.record.AssistRecord">
        select id,
               team_id        AS teamId,
               case_id        AS caseId,
               apply_date     AS applyDate,
               applicant,
               applicant_id   AS applicantId,
               reason,
               helper,
               helper_id      AS helperId,
               state,
               assist_content AS assistContent,
               del_flag       AS delFlag,
               create_by      AS createBy,
               create_time    AS createTime,
               update_by      AS updateBy,
               update_time    AS updateTime,
               invalid,
               operation_type AS operationType
        from case_assist_record
        where del_flag = 0
          and invalid = 0
          and team_id = #{teamId}
          and case_id = #{caseId}
    </select>

    <insert id="insertWorkOrderFollowUp" parameterType="com.zws.system.api.domain.WorkFollowUp" keyProperty="id"
            useGeneratedKeys="true">
        insert into case_work_followUp(order_id, order_state, work_follow_content, created_by_id, created_by,
                                       created_time, del_flag)
        values (#{orderId}, #{orderState}, #{workFollowContent}, #{createdById}, #{createdBy}, #{createdTime},
                #{delFlag})
    </insert>

    <insert id="insertWorkOrderAnnex" parameterType="com.zws.system.api.domain.WorkAnnex">
        insert into case_work_annex(work_id, follow_id, file_url, file_name, created_by_id, created_by,
                                    created_time, del_flag)
        values (#{workId}, #{followId}, #{fileUrl}, #{fileName}, #{createdById}, #{createdBy}, #{createdTime},
                #{delFlag})
    </insert>

    <insert id="insertUrgeRecord" parameterType="com.zws.appeal.domain.record.UrgeRecord">
        insert into case_urge_record(case_id, create_id, contact_id, liaison, relation, contact_mode, follow_up_state,
                                     urge_state,
                                     content, remarks, odv_id, odv_name,
                                     contact_medium, promise_repayment_time, promise_repayment_money, another_time,
                                     promise_by_stages, promise_every_money,
                                     promise_repayment_day, del_flag, create_by, create_time, entrusting_case_batch_num,
                                     entrusting_case_date, return_case_date, operation_type, urge_tpye, web_side)
        values (#{caseId}, #{createId}, #{contactId}, #{liaison}, #{relation}, #{contactMode}, #{followUpState},
                #{urgeState},
                #{content},
                #{remarks}, #{odvId}, #{odvName}, #{contactMedium}, #{promiseRepaymentTime},
                #{promiseRepaymentMoney}, #{anotherTime}, #{promiseByStages},
                #{promiseEveryMoney}, #{promiseRepaymentDay}, #{delFlag}, #{createBy}, #{createTime},
                #{entrustingCaseBatchNum}, #{entrustingCaseDate}, #{returnCaseDate}, #{operationType}, #{urgeTpye}, #{webSide})
    </insert>

    <insert id="insertNoteRecord" parameterType="com.zws.appeal.domain.record.NoteRecord">
        insert into case_note_record(team_id, case_id, note_date, registrant, registrant_id, note_content, del_flag)
        values (#{teamId}, #{caseId}, #{noteDate}, #{registrant}, #{registrantId}, #{noteContent}, #{delFlag})
    </insert>

    <insert id="insertComplaintRecord" parameterType="com.zws.appeal.domain.record.ComplaintRecord">
        insert into case_complaint_record(team_id, case_id, complaint_date, registrant, registrant_id,
                                          complaint_content,
                                          del_flag)
        values (#{teamId}, #{caseId}, #{complaintDate}, #{registrant}, #{registrantId}, #{complaintContent}, #{delFlag})
    </insert>

    <insert id="insertRepaymentRecord" parameterType="com.zws.appeal.domain.record.RepaymentRecord" keyProperty="id"
            useGeneratedKeys="true">
        insert into case_repayment_record(case_id, team_id, repayment_date, repayment_money, repayment_mode,
                                          account_number, order_no,
                                          repayment_type, examine_state, registrar_id,
                                          registrar, repayment_proof, proce, proce_sort, del_flag,
                                          create_time, transferor_name, alipay_account, entrusting_case_batch_num,
                                          entrusting_case_date, return_case_date, odv_id, odv_name, operation_type,
                                          yc_account_agency, voucher_app_upload)
        values (#{caseId}, #{teamId}, #{repaymentDate}, #{repaymentMoney}, #{repaymentMode}, #{accountNumber},
                #{orderNo}, #{repaymentType}, #{examineState}, #{registrarId}, #{registrar}, #{repaymentProof},
                #{proce}, #{proceSort}, #{delFlag}, #{createTime}, #{transferorName}, #{alipayAccount},
                #{entrustingCaseBatchNum}, #{entrustingCaseDate}, #{returnCaseDate}, #{odvId}, #{odvName},
                #{operationType}, #{ycAccountAgency}, #{voucherAppUpload})
    </insert>

    <insert id="insertReductionRecord" parameterType="com.zws.appeal.domain.record.ReductionRecord" keyProperty="id"
            useGeneratedKeys="true">
        insert into case_reduction_record(case_id, team_id, apply_date, applicant, applicant_id, reason,
                                          amount_after_deduction, state, proce, proce_sort, del_flag,
                                          entrusting_case_batch_num, entrusting_case_date, return_case_date, odv_id,
                                          odv_name, operation_type, after_reduction_date, remaining_due)
        values (#{caseId}, #{teamId}, #{applyDate}, #{applicant}, #{applicantId}, #{reason}, #{amountAfterDeduction},
                #{state}, #{proce}, #{proceSort}, #{delFlag}, #{entrustingCaseBatchNum}, #{entrustingCaseDate},
                #{returnCaseDate}, #{odvId}, #{odvName}, #{operationType}, #{afterReductionDate}, #{remainingDue})
    </insert>

    <insert id="insertReductionFile" parameterType="com.zws.appeal.domain.record.ReductionFile">
        insert into case_reduction_file(record_id,first_name,modify_name,file_url,founder,creation_time)
        values
        <foreach collection="reductionFiles" item="list" index="index" separator=",">
            (#{list.recordId},#{list.firstName}, #{list.modifyName}, #{list.fileUrl}, #{list.founder},
            #{list.creationTime})
        </foreach>
    </insert>

    <insert id="insertStagingRecord" parameterType="com.zws.appeal.domain.record.StagingRecord">
        insert into case_staging_record(case_id, apply_date, applicant, applicant_id, reason, team_id, team_name,
                                        staging_num,
                                        repayment_date, repayment_monthly,
                                        examine_state, examine_time, examine_by, examine_by_id, proce, proce_sort,
                                        del_flag, entrusting_case_batch_num, entrusting_case_date, return_case_date,
                                        odv_id, odv_name, operation_type, first_repayment_date, last_repayment_date)
        values (#{caseId}, #{applyDate}, #{applicant}, #{applicantId}, #{reason}, #{teamId}, #{teamName}, #{stagingNum},
                #{repaymentDate}, #{repaymentMonthly}, #{examineState}, #{examineTime}, #{examineBy},
                #{examineById}, #{proce}, #{proceSort}, #{delFlag}, #{entrustingCaseBatchNum}, #{entrustingCaseDate},
                #{returnCaseDate}, #{odvId}, #{odvName}, #{operationType}, #{firstRepaymentDate}, #{lastRepaymentDate})
    </insert>

    <insert id="insertRetrievalRecord" parameterType="com.zws.appeal.domain.record.RetrievalRecord">
        insert into case_retrieval_record(team_id, case_id, apply_date, applicant, applicant_id, reason,
                                          examine_state, examine_time, examine_by, examine_by_id, proce, proce_sort,
                                          del_flag, entrusting_case_batch_num, entrusting_case_date, return_case_date,
                                          odv_id, odv_name, operation_type, matching_results, can_download,
                                          expiration_time, watermark_file_state)
        values (#{teamId}, #{caseId}, #{applyDate}, #{applicant}, #{applicantId}, #{reason},
                #{examineState}, #{examineTime}, #{examineBy},
                #{examineById}, #{proce}, #{proceSort}, #{delFlag}, #{entrustingCaseBatchNum}, #{entrustingCaseDate},
                #{returnCaseDate}, #{odvId}, #{odvName}, #{operationType}, #{matchingResults}, #{canDownload},
                #{expirationTime}, #{watermarkFileState})
    </insert>

    <insert id="insertAssistRecord" parameterType="com.zws.appeal.domain.record.AssistRecord">
        insert into case_assist_record(case_id,team_id, apply_date, applicant, applicant_id, reason, helper,
        helper_id,state, assist_content, del_flag, create_by, create_time,
        invalid,entrusting_case_batch_num,entrusting_case_date,return_case_date,odv_id,odv_name,operation_type)
        values
        <foreach collection="assistRecord" item="list" index="index" separator=",">
            (#{list.caseId},#{list.teamId}, #{list.applyDate}, #{list.applicant}, #{list.applicantId}, #{list.reason},
            #{list.helper},#{list.helperId},#{list.state}, #{list.assistContent}, #{list.delFlag}, #{list.createBy},
            #{list.createTime},#{list.invalid},#{list.entrustingCaseBatchNum},#{list.entrustingCaseDate},#{list.returnCaseDate},
            #{list.odvId},#{list.odvName},#{list.operationType})
        </foreach>
    </insert>

    <insert id="insertAssistDetails" parameterType="com.zws.appeal.domain.AssistDetails">
        insert into case_assist_details(case_id, assist_id, state, assist_content, create_id, create_by, create_time)
        values (#{caseId}, #{assistId}, #{state}, #{assistContent}, #{createId}, #{createBy}, #{createTime})
    </insert>

    <insert id="insertOutsideRecord" parameterType="com.zws.appeal.domain.record.OutsideRecord">
        insert into case_outside_record(case_id, team_id, outside_start, outside_address, longitude_atitude_start,
                                        reason,
                                        odv_id, odv_name, state,
                                        client_name, del_flag, create_by_id, create_by, create_time, proce, proce_sort,
                                        state_code, entrusting_case_batch_num, entrusting_case_date, return_case_date,
                                        odv_ids, odv_names, operation_type)
        values (#{caseId}, #{teamId}, #{outsideStart}, #{outsideAddress}, #{longitudeAtitudeStart}, #{reason},
                #{odvId}, #{odvName}, #{state},
                #{clientName}, #{delFlag}, #{createById}, #{createBy}, #{createTime}, #{proce}, #{proceSort},
                #{stateCode}, #{entrustingCaseBatchNum}, #{entrustingCaseDate}, #{returnCaseDate}, #{odvIds},
                #{odvNames}, #{operationType})
    </insert>

    <update id="updateAssistRecord" parameterType="com.zws.appeal.domain.record.AssistRecord">
        <foreach collection="assistRecords" item="list" index="index" open="" close="" separator=";">
            update case_assist_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.invalid != null">invalid = #{list.invalid},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>

    <update id="updateCaseManage" parameterType="com.zws.appeal.domain.CaseManage">
        update case_manage
        <trim prefix="SET" suffixOverrides=",">
            <if test="followUpStart != null">follow_up_start = #{followUpStart},</if>
            <if test="followUpAst != null">follow_up_ast = #{followUpAst},</if>
            <if test="followUpState != null">follow_up_state = #{followUpState},</if>
            <if test="urgeState != null">urge_state = #{urgeState},</if>
        </trim>
        where case_id = #{caseId}
    </update>

    <update id="updateInfoLoan" parameterType="com.zws.appeal.domain.InfoLoan">
        update case_info_loan
        <trim prefix="SET" suffixOverrides=",">
            <if test="latestFollowUpTime != null">latest_follow_up_time = #{latestFollowUpTime},</if>
        </trim>
        where case_id = #{caseId}
    </update>

    <update id="updateAssistRecordState" parameterType="com.zws.appeal.domain.record.AssistRecord">
        update case_assist_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="assistContent != null">assist_content = #{assistContent},</if>
            <if test="state != null">state = #{state},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateWorkOrderById" parameterType="com.zws.appeal.domain.WorkOrder">
        update case_work_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="handlerId != null">handler_id = #{handlerId},</if>
            <if test="handler != null">handler = #{handler},</if>
            <if test="processingTime != null">processing_time = #{processingTime},</if>
        </trim>
        where id = #{id}
    </update>

    <!--    判断是否是待审核案件-->
    <select id="selectRepaymentRecordProce" resultMap="RepaymentRecordResultMap">
        <include refid="selectRepaymentRecordVo"></include>
        <where>
            del_flag = 0 and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectReductionRecordProce" resultMap="ReductionRecordResultMap">
        <include refid="selectReductionRecordVo"></include>
        <where>
            del_flag = 0 and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectStagingRecordProce" resultMap="StagingRecordResultMap">
        <include refid="selectStagingRecordVo"></include>
        <where>
            del_flag = 0 and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectOutsideRecordProce" resultMap="OutsideRecordResultMap">
        <include refid="selectOutsideRecordVo"></include>
        <where>
            del_flag = 0 and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectRetrievalRecordProce" resultType="com.zws.appeal.domain.record.RetrievalRecord">
        select id,
        team_id AS teamId,
        case_id AS caseId,
        apply_date AS applyDate,
        applicant AS applicant,
        applicant_id AS applicantId,
        reason AS reason,
        examine_state AS examineState,
        examine_time AS examineTime,
        examine_by AS examineBy,
        examine_by_id AS examineById,
        proce AS proce,
        proce_sort AS proceSort,
        entrusting_case_batch_num AS entrustingCaseBatchNum,
        entrusting_case_date AS entrustingCaseDate,
        return_case_date AS returnCaseDate,
        odv_id AS odvId,
        odv_name AS odvName,
        operation_type AS operationType
        from case_retrieval_record
        <where>
            del_flag = 0 and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectAssistRecordProce" resultMap="AssistRecordResultMap">
        <include refid="selectAssistRecordVo"></include>
        <where>
            del_flag = 0 and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectApplyRecordProce" resultMap="ApplyRecordResultMap">
        <include refid="selectApplyRecordVo"></include>
        <where>
            del_flag = 0 and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectWorkOrderByIds" resultType="com.zws.appeal.domain.WorkOrder">
        select id,
        case_id AS caseId,
        question_type AS questionType,
        order_status AS orderStatus,
        call_number AS callNumber,
        question_content AS questionContent,
        del_flag AS delFlag,
        create_by_id AS createById,
        create_by AS createBy,
        create_time AS createTime,
        update_by_id AS updateById,
        update_by AS updateBy,
        update_time AS updateTime,
        handler_id AS handlerId,
        handler,
        processing_time AS processingTime,
        parent_id AS parentId,
        entrusting_case_batch_num AS entrustingCaseBatchNum
        from case_work_order
        where del_flag = 0
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectNeedCaseManageCount" resultType="java.lang.Long">
        select
        distinct
        count(1)
        from case_manage AS cas
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN case_info_base AS cib ON (cib.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{outsourcingTeamId} and cas.case_state in
        ("0","1","3","6")
        and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
        <choose>
            <when test="orderBy!=null and orderBy=='clientMoney'">
                order by cas.client_money
            </when>
            <otherwise>
                order by cas.case_id
            </otherwise>
        </choose>
        <choose>
            <when test="sortOrder!=null and sortOrder==1">
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>
    <select id="selectNeedCaseManage" resultMap="BaseResultMap">
        select
        distinct
        cas.id,
        cas.case_id,
        cas.contract_no,
        cas.allocated_state,
        cas.case_state,
        cas.settlement_status,
        cas.entrusting_party_id,
        cas.entrusting_party_name,
        cas.product_id,
        cas.product_name,
        cas.batch_num,
        cas.entrusting_case_batch_num,
        cas.outsourcing_team_id,
        cas.outsourcing_team_name,
        cas.client_name,
        cas.client_sex,
        cas.client_idcard,
        cas.client_census_register,
        cas.client_phone,
        cas.client_money,
        cas.client_residual_principal,
        cas.client_overdue_start,
        cas.account_period,
        cas.follow_up_state,
        cas.follow_up_start,
        cas.follow_up_ast,
        cas.entrusting_case_date,
        cas.return_case_date,
        cas.area,
        cas.odv_id,
        cas.odv_name,
        cas.urge_state,
        cas.allocated_time,
        cas.urge_power,
        cas.del_flag,
        cas.create_by,
        cas.create_time,
        cas.update_by,
        cas.update_time,
        cas.client_id_type,
        cil.remaining_due,
        cas.uid,
        cm.id AS labelId,
        cm.label_content AS labelContent,
        cm.code AS label
        from case_manage AS cas
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN case_info_base AS cib ON (cib.case_id = cas.case_id)
        LEFT JOIN case_info_loan cil on (cas.case_id = cil.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{outsourcingTeamId} and cas.case_state in
        ("0","1","3","6")
        and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
        <choose>
            <when test="orderBy!=null and orderBy=='clientMoney'">
                order by cas.client_money
            </when>
            <otherwise>
                order by cas.case_id
            </otherwise>
        </choose>
        <choose>
            <when test="sortOrder!=null and sortOrder==1">
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectNeedSmsParameters" resultType="com.zws.common.core.domain.sms.SmsParameters">
        select
        distinct
        cib.client_phone AS clientPhone,
        cib.client_name AS customerName,
        cil.entrust_money AS initialClaim,
        cil.residual_principal AS initialPrincipal,
        cil.yc_interest_balance AS initialInterest,
        own.name AS transferor,
        cil.product_name AS productType,
        cil.yc_contract_no AS contractNo,
        cil.contract_no AS receiptNo,
        cas.case_id AS id,
        cil.yc_overdue_days AS ycOverdueDays,
        cil.remaining_due AS remainingDue,
        fa.bank_name as bankName,
        fa.account_name as accountName,
        fa.account_number as accountNumber,
        cib.bank_card_number as bankCardNumber
        from case_manage AS cas
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN case_info_base AS cib ON (cib.case_id = cas.case_id)
        LEFT JOIN case_info_loan AS cil on (cas.case_id = cil.case_id)
        LEFT JOIN asset_owner AS own ON (cas.entrusting_party_id = own.id)
        LEFT JOIN asset_manage AS am ON (am.batch_num = cas.batch_num and am.del_flag = 0)
        LEFT JOIN financial_account AS fa ON (am.account_id = fa.id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{outsourcingTeamId} and cas.case_state in
        ("0","1","3","6")
        and cas.allocated_state = 1
        <include refid="CaseManageVo"></include>
        <choose>
            <when test="orderBy!=null and orderBy=='clientMoney'">
                order by cas.client_money
            </when>
            <otherwise>
                order by cas.case_id
            </otherwise>
        </choose>
        <choose>
            <when test="sortOrder!=null and sortOrder==1">
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </select>
    <select id="selectRepaymentByCaseIdAndState" resultType="com.zws.appeal.domain.record.RepaymentRecord">
        SELECT repayment_date AS repaymentDate
        FROM case_repayment_record
        WHERE examine_state = "已通过"
          AND case_id = #{caseId}
        ORDER BY repayment_date DESC LIMIT 1

    </select>
    <select id="selectSendMsg" resultType="com.zws.appeal.domain.SendMsg">
        select cm.case_id AS caseId,
        cm.settlement_status AS settlementStatus,
        cm.client_idcard AS clientIdcard,
        cm.client_phone AS clientPhone,
        cm.client_name AS clientName
        FROM
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
        LEFT JOIN team_register_record AS trr ON (cl.id = trr.case_id AND trr.del_flag = 0)
        <where>
            cl.del_flag = 0
            <if test="caseIds != null and caseIds.size() > 0">
                and cm.case_id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="teamId != null">
                and cm.outsourcing_team_id=#{teamId}
            </if>
            <if test="clientName!=null and clientName!=''">
                and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
            </if>
            <if test="caseId != null">
                and cl.id = #{caseId}
            </if>
            <if test="packageName != null and packageName !=''">
                and am.package_name LIKE concat ('%',#{packageName},'%')
            </if>
            <if test="mediationNum != null and mediationNum !=''">
                and cm.mediation_num LIKE concat ('%',#{mediationNum},'%')
            </if>
            <if test="remainingDue != null and remainingDue !=''">
                and cil.remaining_due = #{remainingDue}
            </if>
            <if test="mediatedStage != null and mediatedStage !=''">
                and cm.mediated_stage = #{mediatedStage}
            </if>
            <if test="amount1 != null and amount1 != null">
                and cil.remaining_due >= #{amount1} and cil.remaining_due  &lt;= #{amount2}
            </if>
        </where>
    </select>
    <select id="selectFailMessage" resultType="com.zws.appeal.domain.SendRecords">
        select id as id, case_id as caseId,send_time as sendTime, send_status as sendStatus
        from sms_send_records
        <where>
            <if test="phone != null">and phone_number=#{phone}</if>
            <if test="dateTime != null">and send_time >= #{dateTime}</if>
            <if test="dateTime1 != null">and send_time &lt; #{dateTime1}</if>
        </where>
    </select>
    <select id="selectCwContact" resultType="com.zws.appeal.domain.InfoContact">
        SELECT cic.id                AS id,
               cic.case_id           AS caseId,
               cic.contact_name      AS contactName,
               cic.contact_phone     AS contactPhone,
               cic.phone_location    AS phoneLocation,
               cic.phone_state       AS phoneState,
               cic.contact_relation  AS contactRelation,
               cic.remarks           AS remarks,
               cas.settlement_status AS settlementStatus,
               cas.label             AS label
        FROM case_info_contact AS cic
                 LEFT JOIN case_manage AS cas on (cic.case_id = cas.case_id and cas.del_flag = 0)
        WHERE cic.id = #{id}
    </select>

    <select id="selectBankCardNumber" resultType="java.lang.String">
        select cib.bank_card_number
        from case_manage as cm
                 left join case_info_base cib on (cm.case_id = cib.case_id and cib.del_flag = '0')
        where cm.del_flag = 0
          and AES_DECRYPT(UNHEX(cm.client_idcard), #{fieldKey}) = #{clientIdcard}
    </select>
    <select id="selectTemplateById" resultType="com.zws.common.core.domain.sms.TemplateSms">
        select tem.id                   AS id,
               tem.autograph_id         AS autographId,
               aut.autograph_name       AS autographName,
               tem.template_content     AS templateContent,
               tem.created_by_id        AS createdById,
               tem.created_by           AS createdBy,
               tem.created_time         AS createdTime,
               tem.update_by            AS updateBy,
               tem.update_time          AS updateTime,
               tem.sms_channel          AS smsChannel,
               tem.sms_type             AS smsType,
               tem.examine              AS examine,
               tem.reason               AS reason,
               tem.danmi_template_id    AS danmiTemplateId,
               tem.danmi_template_param AS danmiTemplateParam,
               tem.status               AS status,
               tem.allow_max_num        AS allowMaxNum
        from sms_template AS tem
                 LEFT JOIN sms_autograph AS aut ON (aut.id = tem.autograph_id)
        where tem.del_flag = 0
          and tem.id = #{id}
    </select>
    <select id="selectSmsParameter" resultType="com.zws.common.core.domain.sms.SmsParameters">
        select cib.client_name         AS customerName,
               cas.client_phone        AS clientPhone,
               cil.entrust_money       AS initialClaim,
               cil.residual_principal  AS initialPrincipal,
               cil.yc_interest_balance AS initialInterest,
               own.name                AS transferor,
               cil.product_name        AS productType,
               cil.yc_contract_no      AS contractNo,
               cil.contract_no         AS receiptNo,
               cas.case_id             AS id,
               cil.yc_overdue_days     AS ycOverdueDays,
               cil.remaining_due       AS remainingDue,
               fa.bank_name            as bankName,
               fa.account_name         as accountName,
               fa.account_number       as accountNumber,
               cib.bank_card_number    as bankCardNumber,
               cib.client_id_num       as clientIdcard,
               cas.settlement_status   AS settlementStatus
        from case_manage AS cas
                 LEFT JOIN team_label AS cm ON (cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
                 LEFT JOIN team_employees AS te on (te.id = cas.odv_id)
                 LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
                 LEFT JOIN asset_manage as am on (am.batch_num = cas.batch_num)
                 left join financial_account as fa on (am.account_id = fa.id)
                 LEFT JOIN asset_owner AS own ON (cas.entrusting_party_id = own.id)
                 LEFT JOIN case_info_loan as cil ON (cas.case_id = cil.case_id)
        where cas.del_flag = 0
          and cas.case_id = #{caseId}
          and cas.allocated_state = 1
    </select>
    <select id="selectCourtSendMsg" resultType="com.zws.appeal.domain.SendMsg">
        select cm.case_id AS caseId,
        cm.settlement_status AS settlementStatus,
        cm.client_idcard AS clientIdcard,
        cm.client_phone AS clientPhone,
        cm.client_name AS clientName
        FROM
        case_manage AS cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan AS cil ON ( cil.case_id = cl.id AND cil.del_flag = 0 )
        LEFT JOIN team_register_record AS trr ON (cl.id = trr.case_id AND trr.del_flag = 0)
        LEFT JOIN law_infor AS li on (cl.id = li.case_id and li.del_flag = 0)
        LEFT JOIN case_cost_record AS ccr on (cl.id = ccr.case_id and ccr.cost_type = '开庭费')

        <where>
            cm.del_flag = 0
            <if test="caseIds != null and caseIds.size() > 0">
                and cm.case_id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="teamId != null">
                and cm.outsourcing_team_id=#{teamId}
            </if>
            <if test="clientName!=null and clientName!=''">
                and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
            </if>
            <if test="caseId != null and caseId !=''">
                and cl.id = #{caseId}
            </if>
            <if test="amount1 != null and amount1 != null">
                and cil.remaining_due >= #{amount1} and cil.remaining_due &lt;= #{amount2}
            </if>
            <if test="trialLawyer != null and trialLawyer != ''">
                and li.trial_lawyer LIKE concat ('%',#{trialLawyer},'%')
            </if>
            <if test="trialCourt != null and trialCourt != ''">
                and li.trial_court = #{trialCourt}
            </if>
            <if test="trialTime1 != null ">
                and li.trial_time &gt;=  #{trialTime1}
            </if>
            <if test="trialTime2 != null ">
                and li.trial_time &lt;= #{trialTime2}
            </if>
            <if test="amount1 != null and amount1 != null">
                and cil.remaining_due >= #{amount1} and cil.remaining_due  &lt;= #{amount2}
            </if>
            <if test="!allQuery and ids!=null and ids.size()>0">
                and cm.case_id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectFillingSendMsg" resultType="com.zws.appeal.domain.SendMsg">
        select cm.case_id AS caseId,
        cm.settlement_status AS settlementStatus,
        cm.client_idcard AS clientIdcard,
        cm.client_phone AS clientPhone,
        cm.client_name AS clientName
        from
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.product_id = cil.product_id and am.del_flag=0)
        LEFT JOIN team_filing_case as tfc on (cm.case_id = tfc.case_id and tfc.del_flag=0)
        LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM team_register_record
        where del_flag=0 and register_type = 2
        )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        <where>
            cl.del_flag=0
            <include refid="searchValue"/>
        </where>
    </select>
    <select id="selectFreezeSendMsg" resultType="com.zws.appeal.domain.SendMsg">
        select cm.case_id AS caseId,
               cm.settlement_status AS settlementStatus,
               cm.client_idcard AS clientIdcard,
               cm.client_phone AS clientPhone,
               cm.client_name AS clientName
        from
        team_freeze_record as tfr
        LEFT JOIN case_library as cl on (cl.id = tfr.case_id and cl.del_flag=0)
        LEFT JOIN case_manage  as cm on (cm.case_id = cl.id and cm.del_flag=0)
        LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM team_register_record
        where del_flag=0 and register_type = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        <where>
            tfr.del_flag=0
            <include refid="searchValues"/>
        </where>
    </select>
    <select id="selectJudgeSendMsg" resultType="com.zws.appeal.domain.SendMsg">
        select cm.case_id AS caseId,
               cm.settlement_status AS settlementStatus,
               cm.client_idcard AS clientIdcard,
               cm.client_phone AS clientPhone,
               cm.client_name AS clientName
        FROM
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
        LEFT JOIN judge_infor as ji on (cm.case_id = ji.case_id and ji.del_flag = 0)
        LEFT JOIN law_infor as li on (cm.case_id = li.case_id and li.del_flag = 0)
        <where>
            cl.del_flag=0
            <include refid="searchValuee"/>
        </where>
    </select>
    <select id="selectConcludeSendMsg" resultType="com.zws.appeal.domain.SendMsg">
        select cm.case_id AS caseId,
        cm.settlement_status AS settlementStatus,
        cm.client_idcard AS clientIdcard,
        cm.client_phone AS clientPhone,
        cm.client_name AS clientName
        from
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN team_execute_case as tec on (cil.case_id = tec.case_id and tec.del_flag=0)
        LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM team_register_record
        where del_flag=0 and register_type = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        <where>
            cm.del_flag=0
            <include refid="searchValueess"/>
        </where>
    </select>
    <select id="selectRefundSendMsg" resultType="com.zws.appeal.domain.SendMsg">
        select cm.case_id AS caseId,
               cm.settlement_status AS settlementStatus,
               cm.client_idcard AS clientIdcard,
               cm.client_phone AS clientPhone,
               cm.client_name AS clientName
        from
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN team_refund_record as trd on (cil.case_id = trd.case_id and trd.del_flag=0)
        LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM team_register_record
        where del_flag=0 and register_type = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        <where>
            cm.del_flag=0
            <if test="caseIds != null and caseIds.size() > 0">
                and cm.case_id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="teamId != null">
                and cm.outsourcing_team_id = #{teamId}
            </if>
            <if test="caseId != null">
                and cl.id like  concat('%',#{caseId},'%')
            </if>
            <if test="clientName != null and clientName != ''">
                and cl.client_name like  concat('%',#{clientName},'%')
            </if>

            <if test="amount1 != null and amount2 != null">
                and cil.remaining_due >= #{amount1} and cil.remaining_due  &lt;= #{amount2}
            </if>
            <if test="executiveCourt != null and executiveCourt != ''">
                and trd.executive_court = #{executiveCourt}
            </if>
        </where>
    </select>
    <select id="selectSendMsgByIds" resultType="com.zws.appeal.domain.SendMsg">
        select cm.case_id AS caseId,
               cm.settlement_status AS settlementStatus,
               cm.client_idcard AS clientIdcard,
               cm.client_phone AS clientPhone,
               cm.client_name AS clientName
        from
            case_manage as cm
        <where>
            cm.del_flag=0
            <if test="ids != null and ids.size() > 0">
                and cm.case_id in
                <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="mediatedStage !=null and mediatedStage!=''">
               and cm.mediated_stage=#{mediatedStage}
            </if>
            <if test="saveStage !=null and saveStage!=''">
               and cm.save_stage=#{saveStage}
            </if>
            <if test="disposeStage !=null and disposeStage!=''">
               and cm.dispose_stage=#{disposeStage}
            </if>
        </where>
    </select>
    <select id="selectEntrustingPartyName" resultType="com.zws.appeal.domain.Library">
        SELECT cly.entrusting_party_name AS entrustingPartyName,
               am.create_time            AS createTime,
               cly.update_by              AS updateBy,
               am.update_time            AS updateTime,
               am.create_by              AS createBy
        FROM case_library as cly
                 left join asset_manage as am on (cly.asset_manage_id = am.id and am.del_flag = 0)
        WHERE cly.id = #{caseId}
          and cly.del_flag = 0
    </select>

    <select id="selectYcAbdRepayment" resultType="com.zws.appeal.domain.InfoLoan">
        select
            case_id                  AS caseId,
            abd_create_by            AS abdCreateBy,
            abd_create_time          AS abdCreateTime,
            abd_update_by            AS abdUpdateBy,
            abd_update_time          AS abdUpdateTime,
            yc_abd_repayment         AS ycAbdRepayment
        from case_info_loan
        where case_id = #{caseId}
          and del_flag = 0
          and yc_abd_repayment is not null
          and yc_abd_repayment!=0
    </select>

    <select id="selectReorganizationRecordCaseId" resultMap="ReorganizationRecordResultMap">
        select crr.id,
        crr.case_id,
        ar.apply_date,
        ar.applicant,
        ar.applicant_id,
        ar.reason,
        crr.team_id,
        crr.approve_id,
        crr.team_name,
        crr.staging_num,
        crr.repayment_date,
        crr.repayment_monthly,
        ar.approve_state,
        ar.examine_time,
        ar.examine_by,
        ar.examine_by_id,
        <!-- ar.proce, -->
        <!-- ar.proce_sort, -->
        crr.del_flag,
        crr.repayment_plan,
        crr.down_payment,
        crr.final_payment,
        crr.deduction_amount,
        crr.state,
        cl.settle_agreement_url,
        cl.settle_agreement_time,
        crr.total_repayment
        from case_reorganization_record as crr
        left join approve_record as ar on(crr.approve_id = ar.id)
        left join case_library as cl on(crr.case_id = cl.id)
        where crr.del_flag = 0 and crr.case_id = #{caseId}
        <if test="createTime != null">and ar.apply_date &gt;= #{createTime}</if>
        <if test="createId != null">and crr.team_id = #{createId}</if>
        order by ar.apply_date desc
    </select>
    <select id="selectInfoLoanByCaseId" resultType="com.zws.appeal.domain.InfoLoan">
        select service_fee - yc_abd_expense as syYhFees,
               interest_money + after_base_date_interest - yc_abd_interest as syYhInterest,
               residual_principal - yc_abd_principal as syYhPrincipal,
               yc_abd_principal as ycAbdPrincipal,
               yc_disbursement as syyhwsDisbursement,
               amount_called_back - yc_abd_repayment as amountCalledBack
        from case_info_loan where case_id = #{caseId}
    </select>
    <select id="selectApplyRecordIdNew" resultType="com.zws.appeal.pojo.InheritanceCollection">
        select rep.id AS id,
        rep.case_id AS caseId,
        zws_ar.approve_state AS examineState,
        zws_ar.examine_time AS examineTime,
        zws_ar.applicant AS applicant,
        zws_ar.apply_date AS applyDate,
        rep.reason AS reason,
        rep.stay_case_time AS stayCaseTime,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        IFNULL(rep.stop_end_approval_time, rep.stop_end_time) AS stopEndTime,
        rep.stop_end_approval_time AS stopEndApprovalTime,
        rep.permanently_stop AS permanentlyStop,
        cli.product_name AS productName,
        cli.client_name AS clientName,
        cli.client_id_num AS clientIdcard,
        cli.client_census_register AS clientCensusRegister,
        cli.client_id_type as clientIdType,
        rep.approve_id as approveId
        from ${sqlDataDto.fromData}
        LEFT JOIN case_apply_record AS rep on(rep.approve_id = zws_ar.id)
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN case_library AS cli ON(rep.case_id=cli.id)
        where ${sqlDataDto.whereData} and rep.del_flag = 0 and
        rep.operation_type = #{dto.operationType}
        <if test="dto.caseId != null">and rep.case_id =#{dto.caseId}</if>
        <if test="dto.clientName != null and dto.clientName != ''">and cm.client_name =#{dto.clientName}</if>
        <if test="dto.clientIdcard != null and dto.clientIdcard != ''">and cm.client_idcard =#{dto.clientIdcard}</if>
        <if test="dto.entrustingCaseBatchNum != null and dto.entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{dto.entrustingCaseBatchNum}
        </if>
        <if test="dto.entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{dto.entrustingCaseDate1}</if>
        <if test="dto.entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{dto.entrustingCaseDate2}</if>
        <if test="dto.returnCaseDate1 != null">and rep.return_case_date &gt;= #{dto.returnCaseDate1}</if>
        <if test="dto.returnCaseDate2 != null">and rep.return_case_date &lt;= #{dto.returnCaseDate2}</if>
        order by zws_ar.apply_date desc
    </select>

    <!--    撤销申请案件-->
    <update id="updateRepaymentRecord" parameterType="com.zws.appeal.domain.record.RepaymentRecord">
        <foreach collection="repaymentRecords" item="list" index="index" open="" close="" separator=";">
            update case_repayment_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.proce != null">proce = #{list.proce},</if>
                <if test="list.examineState != null">examine_state = #{list.examineState},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>

    <update id="updateReductionRecord" parameterType="com.zws.appeal.domain.record.ReductionRecord">
        <foreach collection="reductionRecord" item="list" index="index" open="" close="" separator=";">
            update case_reduction_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.proce != null">proce = #{list.proce},</if>
                <if test="list.state != null">state = #{list.state},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>

    <update id="updateReductionRecordInfo" parameterType="com.zws.appeal.domain.record.ReductionRecord">
        update case_reduction_record
        set reduction_url = #{reductionUrl}
        where id = #{id}
    </update>

    <update id="updateStagingRecord" parameterType="com.zws.appeal.domain.record.StagingRecord">
        <foreach collection="stagingRecords" item="list" index="index" open="" close="" separator=";">
            update case_staging_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.proce != null">proce = #{list.proce},</if>
                <if test="list.examineState != null">examine_state = #{list.examineState},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>

    <update id="updateOutsideRecord" parameterType="com.zws.appeal.domain.record.OutsideRecord">
        <foreach collection="outsideRecords" item="list" index="index" open="" close="" separator=";">
            update case_outside_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.proce != null">proce = #{list.proce},</if>
                <if test="list.state != null">state = #{list.state},</if>
                <if test="list.stateCode != null">state_code = #{list.stateCode},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>

    <update id="updateRetrievalRecord" parameterType="com.zws.appeal.domain.record.RetrievalRecord">
        <foreach collection="retrievalRecords" item="list" index="index" open="" close="" separator=";">
            update case_retrieval_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.proce != null">proce = #{list.proce},</if>
                <if test="list.examineState != null">examine_state = #{list.examineState},</if>
                <if test="list.examineTime != null">examine_time = #{list.examineTime},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>

    <update id="updateAssistRecords" parameterType="com.zws.appeal.domain.record.AssistRecord">
        <foreach collection="assistRecords" item="list" index="index" open="" close="" separator=";">
            update case_assist_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.state != null">state = #{list.state},</if>
                <if test="list.updateBy != null">update_by = #{list.updateBy},</if>
                <if test="list.updateTime != null">update_time = #{list.updateTime},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>

    <update id="updateApplyRecord" parameterType="com.zws.appeal.domain.ApplyRecord">
        <foreach collection="applyRecords" item="list" index="index" open="" close="" separator=";">
            update case_apply_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.proce != null">proce = #{list.proce},</if>
                <if test="list.examineState != null">examine_state = #{list.examineState},</if>
            </trim>
            where id = #{list.id} and apply_state = #{list.applyState}
        </foreach>
    </update>

    <update id="updateWorkOrder" parameterType="com.zws.appeal.domain.WorkOrder">
        <foreach collection="workOrders" item="list" index="index" open="" close="" separator=";">
            update case_work_order
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.orderStatus != null and list.orderStatus != ''">order_status = #{list.orderStatus},</if>
                <if test="list.handlerId != null">handler_id = #{list.handlerId},</if>
                <if test="list.handler != null and list.handler != ''">handler = #{list.handler},</if>
                <if test="list.processingTime != null">processing_time = #{list.processingTime},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>

    <update id="updateVisitStatus" parameterType="com.zws.appeal.domain.record.OutsideRecord">
        update case_outside_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="stateCode != null">state_code = #{stateCode},</if>
            <if test="outsideEnd != null">outside_end = #{outsideEnd},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateCaseDisposeStage">
        update case_manage
        set dispose_stage=#{mediateStage}
        where case_id = #{caseId}
    </update>

</mapper>
