<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.CollectionMapper">

    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.CaseManage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="allocated_state" jdbcType="VARCHAR" property="allocatedState"/>
        <result column="settlement_status" jdbcType="VARCHAR" property="settlementStatus"/>
        <result column="case_state" jdbcType="VARCHAR" property="caseState"/>
        <result column="entrusting_party_id" jdbcType="BIGINT" property="entrustingPartyId"/>
        <result column="entrusting_party_name" jdbcType="VARCHAR" property="entrustingPartyName"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="batch_num" jdbcType="VARCHAR" property="batchNum"/>
        <result column="entrusting_case_batch_num" jdbcType="VARCHAR" property="entrustingCaseBatchNum"/>
        <result column="outsourcing_team_id" jdbcType="BIGINT" property="outsourcingTeamId"/>
        <result column="outsourcing_team_name" jdbcType="VARCHAR" property="outsourcingTeamName"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="client_sex" jdbcType="VARCHAR" property="clientSex"/>
        <result column="client_idcard" jdbcType="VARCHAR" property="clientIdcard"/>
        <result column="client_census_register" jdbcType="VARCHAR" property="clientCensusRegister"/>
        <result column="client_phone" jdbcType="VARCHAR" property="clientPhone"/>
        <result column="client_money" jdbcType="DECIMAL" property="clientMoney"/>
        <result column="client_residual_principal" jdbcType="DECIMAL" property="clientResidualPrincipal"/>
        <result column="client_overdue_start" jdbcType="TIMESTAMP" property="clientOverdueStart"/>
        <result column="account_period" jdbcType="INTEGER" property="accountPeriod"/>
        <result column="follow_up_state" jdbcType="VARCHAR" property="followUpState"/>
        <result column="follow_up_start" jdbcType="TIMESTAMP" property="followUpStart"/>
        <result column="follow_up_ast" jdbcType="TIMESTAMP" property="followUpAst"/>
        <result column="entrusting_case_date" jdbcType="TIMESTAMP" property="entrustingCaseDate"/>
        <result column="return_case_date" jdbcType="TIMESTAMP" property="returnCaseDate"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="odv_name" jdbcType="VARCHAR" property="odvName"/>
        <result column="urge_state" jdbcType="VARCHAR" property="urgeState"/>
        <result column="allocated_time" jdbcType="TIMESTAMP" property="allocatedTime"/>
        <result column="urge_power" jdbcType="VARCHAR" property="urgePower"/>
        <result column="del_flag" jdbcType="BIT" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="history_power" jdbcType="VARCHAR" property="historyPower"/>
        <result column="dispose_stage" jdbcType="VARCHAR" property="disposeStage"/>
        <result column="mediated_stage" jdbcType="VARCHAR" property="mediatedStage"/>
    </resultMap>

    <resultMap id="InfoBaseResultMap" type="com.zws.appeal.domain.InfoBase">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="client_name_enc" jdbcType="VARCHAR" property="clientNameEnc"/>
        <result column="client_id_num" jdbcType="VARCHAR" property="clientIdNum"/>
        <result column="client_id_num_enc" jdbcType="VARCHAR" property="clientIdNumEnc"/>
        <result column="client_census_register" jdbcType="VARCHAR" property="clientCensusRegister"/>
        <result column="client_census_register_enc" jdbcType="VARCHAR" property="clientCensusRegisterEnc"/>
        <result column="client_phone" jdbcType="VARCHAR" property="clientPhone"/>
        <result column="client_phone_enc" jdbcType="VARCHAR" property="clientPhoneEnc"/>
        <result column="client_sex" jdbcType="VARCHAR" property="clientSex"/>
        <result column="client_age" jdbcType="INTEGER" property="clientAge"/>
        <result column="client_birthday" jdbcType="DATE" property="clientBirthday"/>
        <result column="occupation" jdbcType="VARCHAR" property="occupation"/>
        <result column="marital_status" jdbcType="VARCHAR" property="maritalStatus"/>
        <result column="qq" jdbcType="VARCHAR" property="qq"/>
        <result column="weixin" jdbcType="VARCHAR" property="weixin"/>
        <result column="mailbox" jdbcType="VARCHAR" property="mailbox"/>
        <result column="place_of_work" jdbcType="VARCHAR" property="placeOfWork"/>
        <result column="working_address" jdbcType="VARCHAR" property="workingAddress"/>
        <result column="registered_address" jdbcType="VARCHAR" property="registeredAddress"/>
        <result column="residential_address" jdbcType="VARCHAR" property="residentialAddress"/>
        <result column="home_address" jdbcType="VARCHAR" property="homeAddress"/>
        <result column="bank_name" jdbcType="VARBINARY" property="bankName"/>
        <result column="bank_card_number" jdbcType="VARCHAR" property="bankCardNumber"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="client_id_type" property="clientIdType"/>
        <result column="security_name" property="securityName"/>
        <result column="security_name_enc" property="securityNameEnc"/>
        <result column="security_id_type" property="securityIdType"/>
        <result column="security_id_num" property="securityIdNum"/>
        <result column="security_id_num_enc" property="securityIdNumEnc"/>
        <result column="security_phone" property="securityPhone"/>
        <result column="security_phone_enc" property="securityPhoneEnc"/>
        <result column="asset_no" property="assetNo"/>
        <result column="administrative_bi" property="administrativeBi"/>
        <result column="uid" property="uid"/>
        <result column="education" property="education"/>
        <result column="academic_degree" property="academicDegree"/>
        <result column="employment_status" property="employmentStatus"/>
        <result column="residential_status" property="residentialStatus"/>
        <result column="home_phone" property="homePhone"/>
        <result column="duties" property="duties"/>
        <result column="title" property="title"/>
        <result column="unit_start_year" property="unitStartYear"/>
        <result column="unit_industry" property="unitIndustry"/>
        <result column="unit_postal_code" property="unitPostalCode"/>
        <result column="unit_telephone" property="unitTelephone"/>
        <result column="residence_postal_code" property="residencePostalCode"/>
        <result column="unit_properties" property="unitProperties"/>
        <result column="customer_code" property="customerCode"/>
    </resultMap>

    <resultMap id="InfoContactResultMap" type="com.zws.appeal.domain.InfoContact">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="phone_location" jdbcType="VARCHAR" property="phoneLocation"/>
        <result column="phone_state" jdbcType="VARCHAR" property="phoneState"/>
        <result column="contact_relation" jdbcType="VARCHAR" property="contactRelation"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="app_flag" jdbcType="CHAR" property="appFlag"/>
    </resultMap>

    <resultMap id="InfoExtraResultMap" type="com.zws.appeal.domain.InfoExtra">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="extra_name" jdbcType="VARCHAR" property="extraName"/>
        <result column="extra_value" jdbcType="VARCHAR" property="extraValue"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="InfoLoanResultMap" type="com.zws.appeal.domain.InfoLoan">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="case_manage_id" jdbcType="BIGINT" property="caseManageId"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="loan_money" jdbcType="DECIMAL" property="loanMoney"/>
        <result column="overdue_start" jdbcType="TIMESTAMP" property="overdueStart"/>
        <result column="account_period" jdbcType="INTEGER" property="accountPeriod"/>
        <result column="loan_institution" jdbcType="VARCHAR" property="loanInstitution"/>
        <result column="loan_periods" jdbcType="INTEGER" property="loanPeriods"/>
        <result column="already_periods" jdbcType="INTEGER" property="alreadyPeriods"/>
        <result column="not_periods" jdbcType="INTEGER" property="notPeriods"/>
        <result column="entrust_money" jdbcType="DECIMAL" property="entrustMoney"/>
        <result column="loan_principal" jdbcType="DECIMAL" property="loanPrincipal"/>
        <result column="late_fee" jdbcType="DECIMAL" property="lateFee"/>
        <result column="service_fee" jdbcType="DECIMAL" property="serviceFee"/>
        <result column="residual_principal" jdbcType="DECIMAL" property="residualPrincipal"/>
        <result column="interest_money" jdbcType="DECIMAL" property="interestMoney"/>
        <result column="interest_penalty" jdbcType="DECIMAL" property="interestPenalty"/>
        <result column="actual_amount_received" jdbcType="DECIMAL" property="actualAmountReceived"/>
        <result column="overdue_premium" jdbcType="DECIMAL" property="overduePremium"/>
        <result column="repayment_date" jdbcType="DATE" property="repaymentDate"/>
        <result column="repayment_monthly" jdbcType="DECIMAL" property="repaymentMonthly"/>
        <result column="amount_after_deduction" jdbcType="DECIMAL" property="amountAfterDeduction"/>
        <result column="amount_called_back" jdbcType="DECIMAL" property="amountCalledBack"/>
        <result column="amount_due" jdbcType="DECIMAL" property="amountDue"/>
        <result column="remaining_due" jdbcType="DECIMAL" property="remainingDue"/>
        <result column="loan_date" jdbcType="TIMESTAMP" property="loanDate"/>
        <result column="amount_final_repayment" jdbcType="DECIMAL" property="amountFinalRepayment"/>
        <result column="amount_final_date" jdbcType="TIMESTAMP" property="amountFinalDate"/>
        <result column="latest_follow_up_time" jdbcType="TIMESTAMP" property="latestFollowUpTime"/>
        <result column="entrusting_case_date" jdbcType="TIMESTAMP" property="entrustingCaseDate"/>
        <result column="return_case_date" jdbcType="TIMESTAMP" property="returnCaseDate"/>
        <result column="other_fees" jdbcType="DECIMAL" property="otherFees"/>
        <result column="other_fees_remarks" jdbcType="VARCHAR" property="otherFeesRemarks"/>
        <result column="case_region" jdbcType="VARCHAR" property="caseRegion"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>

        <result column="yc_overdue_days" property="ycOverdueDays"/>
        <result column="yc_contract_no" property="ycContractNo"/>
        <result column="yc_five_level" property="ycFiveLevel"/>
        <result column="yc_currencies" property="ycCurrencies"/>
        <result column="yc_purpose" property="ycPurpose"/>
        <result column="yc_lending_rate" property="ycLendingRate"/>
        <result column="yc_repayment_method" property="ycRepaymentMethod"/>
        <result column="yc_interest_balance" property="ycInterestBalance"/>
        <result column="yc_disbursement" property="ycDisbursement"/>
        <result column="yc_litigation_status" property="ycLitigationStatus"/>
        <result column="yc_is_dishonest" property="ycIsDishonest"/>
        <result column="yc_is_limit_consumption" property="ycIsLimitConsumption"/>
        <result column="sy_yh_principal" property="syYhPrincipal"/>
        <result column="sy_yh_interest" property="syYhInterest"/>
        <result column="sy_yh_fees" property="syYhFees"/>
        <result column="sy_yh_default" property="syYhDefault"/>
        <result column="yc_loan_bank" property="ycLoanBank"/>
        <result column="yc_loan_term" property="ycLoanTerm"/>
        <result column="yc_business_type" property="ycBusinessType"/>
        <result column="yc_contract_money" property="ycContractMoney"/>
        <result column="yc_loan_issuance_date" property="ycLoanIssuanceDate"/>
        <result column="yc_loan_maturity_date" property="ycLoanMaturityDate"/>
        <result column="yc_abd_repayment" property="ycAbdRepayment"/>
        <result column="yc_abd_principal" property="ycAbdPrincipal"/>
        <result column="yc_abd_interest" property="ycAbdInterest"/>
        <result column="yc_abd_expense" property="ycAbdExpense"/>
        <result column="yc_write_date" property="ycWriteDate"/>
        <result column="yc_default_rate" property="ycDefaultRate"/>

        <result column="interest_guarantee" property="interestGuarantee"/>
        <result column="interest_advice" property="interestAdvice"/>
        <result column="number_of_periods" property="numberOfPeriods"/>
        <result column="base_overdue_days" property="baseOverdueDays"/>
        <result column="card_issuance_date" property="cardIssuanceDate"/>
        <result column="statement_date" property="statementDate"/>
        <result column="first_overdue_date" property="firstOverdueDate"/>
        <result column="base_date" property="baseDate"/>
        <result column="credit_limit" property="creditLimit"/>
        <result column="principal_interest_total" property="principalInterestTotal"/>
        <result column="interest_fees_total" property="interestFeesTotal"/>
        <result column="syyhws_disbursement" property="syyhwsDisbursement"/>
        <result column="judgment_rate" property="judgmentRate"/>
        <result column="judgment_date" property="judgmentDate"/>
        <result column="court_jurisdiction" property="courtJurisdiction"/>
        <result column="amount_after_deduction_sun" property="amountAfterDeductionSun"/>
        <result column="after_base_date_interest" property="afterBaseDateInterest"/>
        <result column="after_base_date_lpr4_interest" property="afterBaseDateLpr4Interest"/>
    </resultMap>

    <resultMap id="InfoPlanResultMap" type="com.zws.appeal.domain.InfoPlan">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_number" jdbcType="INTEGER" property="orderNumber"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="hk_periods_number" jdbcType="VARCHAR" property="hkPeriodsNumber"/>
        <result column="yh_date" jdbcType="DATE" property="yhDate"/>
        <result column="jh_yh_principal" jdbcType="DECIMAL" property="jhYhPrincipal"/>
        <result column="jh_yh_interest" jdbcType="DECIMAL" property="jhYhInterest"/>
        <result column="sy_yh_money" jdbcType="DECIMAL" property="syYhMoney"/>
        <result column="sy_yh_principal" jdbcType="DECIMAL" property="syYhPrincipal"/>
        <result column="sy_yh_interest" jdbcType="DECIMAL" property="syYhInterest"/>
        <result column="sy_yh_compound_interest" jdbcType="DECIMAL" property="syYhCompoundInterest"/>
        <result column="sy_yh_penalty_interest" jdbcType="DECIMAL" property="syYhPenaltyInterest"/>
        <result column="sy_yh_wyj" jdbcType="DECIMAL" property="syYhWyj"/>
        <result column="sy_yh_late_fee" jdbcType="DECIMAL" property="syYhLateFee"/>
        <result column="sy_yh_surplus_interest" jdbcType="DECIMAL" property="syYhSurplusInterest"/>
        <result column="already_hk_date" jdbcType="DATE" property="alreadyHkDate"/>
        <result column="ych_principal" jdbcType="DECIMAL" property="ychPrincipal"/>
        <result column="ych_interest" jdbcType="DECIMAL" property="ychInterest"/>
        <result column="ych_penalty_interest" jdbcType="DECIMAL" property="ychPenaltyInterest"/>
        <result column="ych_compound_interest" jdbcType="DECIMAL" property="ychCompoundInterest"/>
        <result column="ych_late_fee" jdbcType="DECIMAL" property="ychLateFee"/>
        <result column="ych_wyj" jdbcType="DECIMAL" property="ychWyj"/>
        <result column="ych_money" jdbcType="DECIMAL" property="ychMoney"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="jh_yh_df_surplus_interest" jdbcType="DECIMAL" property="jhYhDfSurplusInterest"/>
        <result column="ych_df_penalty_interest" jdbcType="DECIMAL" property="ychDfPenaltyInterest"/>
        <result column="sy_yh_df_surplus_interest" jdbcType="DECIMAL" property="syYhDfSurplusInterest"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="selectInfoBaseVo">
        select id,
               case_id,
               client_name,
               client_name_enc,
               client_id_num,
               client_id_num_enc,
               client_census_register,
               client_census_register_enc,
               client_phone,
               client_phone_enc,
               client_sex,
               client_age,
               client_birthday,
               occupation,
               marital_status,
               qq,
               weixin,
               mailbox,
               place_of_work,
               registered_address,
               working_address,
               residential_address,
               home_address,
               bank_name,
               bank_card_number,
               create_by,
               create_time,
               update_by,
               update_time,
               client_id_type,
               security_name,
               security_id_type,
               security_id_num,
               security_phone,
               administrative_bi,
               asset_no,
               uid,
               education,
               academic_degree,
               employment_status,
               residential_status,
               home_phone,
               duties,
               title,
               unit_start_year,
               unit_industry,
               unit_postal_code,
               unit_telephone,
               residence_postal_code,
               unit_properties,
               customer_code
        from case_info_base
    </sql>

    <sql id="selectInfoContactVo">
        SELECT cic.id,
               cic.case_id,
               cic.contact_name,
               cic.contact_phone,
               cic.phone_location,
               cic.phone_state,
               cic.contact_relation,
               cic.remarks,
               cic.del_flag,
               cic.create_by,
               cic.create_time,
               cic.update_by,
               cic.update_time,
               ts.push_app_status,
               cic.app_flag
        FROM case_info_contact cic
                 left join case_manage cm on cm.case_id = cic.case_id
                 left join team_create tc on cm.outsourcing_team_id = tc.id
                 left join team_states ts on tc.id = ts.create_id
    </sql>

    <sql id="selectInfoExtraVo">
        select id,
               case_id,
               extra_name,
               extra_value,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from case_info_extra
    </sql>

    <sql id="selectInfoLoanVo">
        select id,
               case_id,
               case_manage_id,
               contract_no,
               product_id,
               product_name,
               product_type,
               loan_money,
               overdue_start,
               account_period,
               loan_institution,
               loan_periods,
               already_periods,
               not_periods,
               entrust_money,
               loan_principal,
               late_fee,
               service_fee,
               residual_principal,
               interest_money,
               interest_penalty,
               actual_amount_received,
               overdue_premium,
               repayment_date,
               repayment_monthly,
               amount_after_deduction,
               amount_called_back,
               amount_due,
               remaining_due,
               loan_date,
               amount_final_repayment,
               amount_final_date,
               latest_follow_up_time,
               entrusting_case_date,
               return_case_date,
               other_fees,
               other_fees_remarks,
               case_region,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from case_info_loan
    </sql>

    <sql id="selectInfoPlanVo">
        select id,
               order_number,
               case_id,
               hk_periods_number,
               yh_date,
               jh_yh_principal,
               jh_yh_interest,
               sy_yh_money,
               sy_yh_principal,
               sy_yh_interest,
               sy_yh_compound_interest,
               sy_yh_penalty_interest,
               sy_yh_wyj,
               sy_yh_late_fee,
               sy_yh_surplus_interest,
               already_hk_date,
               ych_principal,
               ych_interest,
               ych_penalty_interest,
               ych_compound_interest,
               ych_late_fee,
               ych_wyj,
               ych_money,
               del_flag,
               create_by,
               create_time,
               update_by,
               jh_yh_df_surplus_interest,
               ych_df_penalty_interest,
               sy_yh_df_surplus_interest,
               update_time
        from case_info_plan
    </sql>

    <sql id="selectCaseManageVo">
        select id,
               case_id,
               contract_no,
               allocated_state,
               settlement_status,
               case_state,
               entrusting_party_id,
               entrusting_party_name,
               product_id,
               product_name,
               batch_num,
               entrusting_case_batch_num,
               outsourcing_team_id,
               outsourcing_team_name,
               client_name,
               client_sex,
               client_idcard,
               client_census_register,
               client_phone,
               client_money,
               client_residual_principal,
               client_overdue_start,
               account_period,
               follow_up_state,
               follow_up_start,
               follow_up_ast,
               entrusting_case_date,
               return_case_date,
               area,
               label,
               odv_id,
               odv_name,
               urge_state,
               allocated_time,
               urge_power,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               history_power,
               dispose_stage,
               mediated_stage
        from case_manage
    </sql>
    <!--根据标签颜色和团队id查询案件标签信息-->
    <select id="selectLabelCode" resultType="com.zws.appeal.domain.Label">
        select id,
               label_content AS labelContent,
               state_label   AS stateLabel,
               code
        from team_label
        where create_id = #{createId}
          and code = #{code}
          and state_label = 1
    </select>

    <select id="selectInfoContactById" resultMap="InfoContactResultMap">
        SELECT cic.id,
               cic.case_id,
               cic.contact_name,
               cic.contact_phone,
               cic.phone_location,
               cic.phone_state,
               cic.contact_relation,
               cic.remarks,
               cic.del_flag,
               cic.create_by,
               cic.create_time,
               cic.update_by,
               cic.update_time
        FROM case_info_contact cic
        where cic.del_flag = 0
          and cic.id = #{id}
          and cic.phone_state = 0
    </select>

    <!--根据案件id查询案件详情-->
    <select id="selectCaseManageId" resultMap="BaseResultMap">
        <include refid="selectCaseManageVo"></include>
        where del_flag = 0 and case_id = #{caseId}
    </select>
    <!--根据证件号码以及团队id查询共债案件id集合-->
    <select id="selectIdentificationAndTeamId" resultType="java.lang.Long">
        select case_id AS caseId
        from case_manage
        where del_flag = 0
          and client_idcard = #{clientIdcard}
          and outsourcing_team_id = #{createId}
    </select>

    <select id="selectIdentification" resultType="java.lang.Long">
        select id AS caseId
        from case_library
        where del_flag = 0
          and client_id_num = #{clientIdcard}
    </select>
    <!--案件信息-基础信息-->
    <select id="selectInfoBase" resultMap="InfoBaseResultMap">
        <include refid="selectInfoBaseVo"></include>
        where del_flag = 0 and case_id = #{caseId}
    </select>
    <!--案件联系人信息-->
    <select id="selectInfoContact" resultMap="InfoContactResultMap">
        <include refid="selectInfoContactVo"></include>
        where cic.del_flag = 0
        and (case when ts.push_app_status = 0 then (cic.app_flag is null or cic.app_flag = 1) else cic.app_flag is null
        end )
        and cic.case_id = #{caseId}
        <if test="contactNameOrPhone != null and contactNameOrPhone != ''">
            and AES_DECRYPT(UNHEX(cic.contact_name), #{decryptKey}) = #{contactNameOrPhone}
            or
            AES_DECRYPT(UNHEX(cic.contact_phone), #{decryptKey}) = #{contactNameOrPhone}
        </if>
    </select>
    <!--案件附加信息-->
    <select id="selectInfoExtra" resultMap="InfoExtraResultMap">
        <include refid="selectInfoExtraVo"></include>
        where del_flag = 0 and case_id = #{caseId}
    </select>
    <!--贷款信息(案件信息)表-->
    <select id="selectInfoLoan" resultMap="InfoLoanResultMap">
        select cas.id,
               cas.case_id,
               cas.case_manage_id,
               cas.contract_no,
               cas.product_id,
               cas.product_name,
               cas.product_type,
               cas.loan_money,
               cas.overdue_start,
               cas.account_period,
               cas.loan_institution,
               cas.loan_periods,
               cas.already_periods,
               cas.not_periods,
               cas.entrust_money,
               cas.loan_principal,
               cas.late_fee,
               cas.service_fee,
               cas.residual_principal,
               cas.interest_money,
               cas.interest_penalty,
               cas.actual_amount_received,
               cas.overdue_premium,
               cas.repayment_date,
               cas.repayment_monthly,
               cas.amount_after_deduction,
               cas.amount_called_back,
               cas.amount_due,
               cas.remaining_due,
               cas.loan_date,
               cas.amount_final_repayment,
               cas.amount_final_date,
               cas.latest_follow_up_time,
               cil.entrusting_case_date,
               cil.return_case_date,
               cas.other_fees,
               cas.other_fees_remarks,
               cas.case_region,
               cas.del_flag,
               cas.create_by,
               cas.create_time,
               cas.update_by,
               cas.update_time,
               cas.yc_overdue_days,
               cas.yc_contract_no,
               cas.yc_five_level,
               cas.yc_currencies,
               cas.yc_purpose,
               cas.yc_lending_rate,
               cas.yc_repayment_method,
               cas.yc_interest_balance,
               cas.yc_disbursement,
               cas.yc_litigation_status,
               cas.yc_is_dishonest,
               cas.yc_is_limit_consumption,
               cas.sy_yh_principal,
               cas.sy_yh_interest,
               cas.sy_yh_fees,
               cas.sy_yh_default,
               cas.yc_loan_bank,
               cas.yc_business_type,
               cas.yc_contract_money,
               cas.yc_loan_term,
               cas.yc_loan_issuance_date,
               cas.yc_loan_maturity_date,
               cas.yc_abd_repayment,
               cas.yc_abd_principal,
               cas.yc_abd_interest,
               cas.yc_abd_expense,
               cas.yc_write_date,
               cas.yc_default_rate,

               cas.interest_guarantee,
               cas.interest_advice,
               cas.number_of_periods,
               cas.base_overdue_days,
               cas.card_issuance_date,
               cas.statement_date,
               cas.first_overdue_date,
               cas.base_date,
               cas.credit_limit,
               cas.principal_interest_total,
               cas.interest_fees_total,
               cas.syyhws_disbursement,
               cas.judgment_rate,
               cas.judgment_date,
               cas.court_jurisdiction,
               cas.after_base_date_interest,
               cas.after_base_date_lpr4_interest,
               cas.amount_after_deduction_sun
        from case_info_loan AS cas
                 LEFT JOIN case_manage AS cil ON (cas.case_id = cil.case_id and cil.del_flag = 0)
        where cas.del_flag = 0
          and cas.case_id = #{caseId}
    </select>
    <!--还款计划信息表-->
    <select id="selectInfoPlan" resultMap="InfoPlanResultMap">
        <include refid="selectInfoPlanVo"></include>
        where del_flag = 0 and case_id = #{caseId}
        <choose>
            <when test="sortOrder != null">
                <if test="sortOrder == 1">
                    order by ISNULL(order_number),order_number asc
                </if>
                <if test="sortOrder == 2">
                    order by ISNULL(order_number),order_number desc
                </if>
            </when>
            <otherwise>
                order by ISNULL(order_number),order_number asc
            </otherwise>
        </choose>
    </select>
    <!--案件详细信息-->
    <select id="selectCaseManage" resultType="com.zws.appeal.pojo.InfoLoanPojo">
        SELECT cl.case_id                         AS caseId,
               cil.contract_no                    AS contractNo,
               cl.client_name                     AS clientName,
               cl.odv_name                        AS odvName,
               cil.product_name                   AS productName,
               cil.product_type                   AS productType,
               cil.loan_money                     AS loanMoney,
               DATEDIFF(NOW(), cil.overdue_start) AS overdueStarts,
               cil.account_period                 AS accountPeriod,
               cil.loan_institution               AS loanInstitution,
               cil.loan_periods                   AS loanPeriods,
               cil.already_periods                AS alreadyPeriods,
               cil.not_periods                    AS notPeriods,
               cil.case_region                    AS caseRegion,
               cil.entrust_money                  AS entrustMoney,
               cil.loan_principal                 AS loanPrincipal,
               cil.interest_money                 AS interestMoney,
               cil.loan_date                      AS loanDate,
               cil.overdue_start                  AS overdueStart,
               cil.sy_yh_principal                AS syYhPrincipal,
               cil.sy_yh_interest                 AS syYhInterest,
               cil.sy_yh_fees                     AS syYhFees,
               cil.sy_yh_default                  AS syYhDefault,
               cil.remaining_due                  AS remainingDue,
               ass.owner_name                     AS ownerName,
               ass.short_name                     AS shortName
        FROM case_manage AS cl
                 LEFT JOIN case_info_loan AS cil ON (cl.case_id = cil.case_id)
                 LEFT JOIN asset_product AS ass ON (ass.id = cl.product_id and ass.del_flag = 0)
        WHERE cl.del_flag = 0
          and cl.client_idcard = #{clientIdcard}
          and cl.outsourcing_team_id = #{createId}
          and cl.allocated_state = 1
    </select>

    <select id="selectCaseManageCount" resultType="int">
        SELECT count(1) AS number
        FROM case_manage AS cl
                 LEFT JOIN case_info_loan AS cil ON (cl.case_id = cil.case_id)
                 LEFT JOIN asset_product AS ass ON (ass.id = cl.product_id and ass.del_flag = 0)
        WHERE cl.del_flag = 0
          and cl.client_idcard = #{clientIdcard}
          and cl.outsourcing_team_id = #{createId}
          and cl.allocated_state = 1
    </select>

    <select id="selectStaffLabel" resultType="com.zws.appeal.domain.StaffLabel">
        select id,
        employees_id AS employeesId,
        label_content AS labelContent,
        identification
        from team_staff_label
        where employees_id = #{employeesId} and identification = #{identification}
        <if test="labelContent != null">and label_content = #{labelContent}</if>
    </select>

    <insert id="insertInfoContact" parameterType="com.zws.appeal.domain.InfoContact">
        insert into case_info_contact(case_id, contact_name, contact_phone, phone_location, phone_state,
                                      contact_relation, remarks, del_flag, create_by, create_time,
                                      contact_name_enc, contact_phone_enc)
        values (#{caseId}, #{contactName}, #{contactPhone}, #{phoneLocation}, #{phoneState}, #{contactRelation},
                #{remarks}, #{delFlag}, #{createBy}, #{createTime}, #{contactNameEnc}, #{contactPhoneEnc})
    </insert>

    <insert id="insertStaffLabel" parameterType="com.zws.appeal.domain.StaffLabel">
        insert into team_staff_label(employees_id, label_content, identification)
        values (#{employeesId}, #{labelContent}, #{identification})
    </insert>

    <update id="updateInfoContact" parameterType="com.zws.appeal.domain.CaseManage">
        update case_info_contact
        <trim prefix="SET" suffixOverrides=",">
            <if test="phoneState != null">phone_state = #{phoneState},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStaffLabel" parameterType="int">
        delete
        from team_staff_label
        where id = #{id}
    </delete>

    <select id="getProductShortName" resultType="String">
        SELECT ap.short_name
        FROM asset_product AS ap
        WHERE ap.id = #{productId}
          AND ap.del_flag = 0
    </select>

    <select id="selectPhoneCountByCaseId" resultType="java.lang.Long">
        select COUNT(1)
        from case_info_contact
        where case_id = #{caseId}
          and contact_phone = #{encryptPhone}
    </select>
    <select id="selectPhoneIsVoid" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM case_info_contact
        WHERE case_id = #{caseId}
          and contact_phone = #{clientPhone}
          and phone_state = 1
          and del_flag = 0
    </select>
    <select id="selectInfoContactListByCaseId" resultType="com.zws.appeal.domain.InfoContact">
        SELECT DISTINCT contact_phone    AS contactPhone,
                        contact_relation AS contactRelation
        FROM case_info_contact
        where del_flag = 0
          and case_id = #{caseId}
    </select>
    <select id="selectLabelAsset" resultType="com.zws.appeal.domain.Label">
        SELECT t.legal_name,
               t.legal_key labelAsset,
               t.legal_val labelAssetInfo
        FROM setup_legal t
        WHERE t.legal_type=3
          and t.legal_key=(select cm.label_asset from case_manage cm where cm.case_id=#{caseId} and cm.del_flag = 0)



    </select>
    <select id="selectAmountAfterDeductionSun" resultType="java.math.BigDecimal">
        select SUM(ABS(amount_deduction))
        from case_reduction_record
        where del_flag = 0
          and state="已通过"
          and case_id = #{caseId}
    </select>
    <select id="selectReorganizationCaseIdSum" resultType="java.math.BigDecimal">
        SELECT sum(deduction_amount)
        FROM approve_record as ar
                 left join case_reorganization_record crr on ar.id = crr.approve_id
        WHERE crr.case_id = #{caseId}
          and ar.approve_state = 2
          and ar.approve_code = "reorganization"
    </select>

</mapper>
