<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.call.CallDownloadTaskRecordMapper">
    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.call.CallDownloadTaskRecord">
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="record_id" jdbcType="BIGINT" property="recordId"/>
        <result column="download" jdbcType="CHAR" property="download"/>
    </resultMap>
    <insert id="insert" parameterType="com.zws.appeal.domain.call.CallDownloadTaskRecord">
        insert into call_download_task_record (task_id, record_id, download)
        values (#{taskId,jdbcType=BIGINT}, #{recordId,jdbcType=BIGINT}, #{download,jdbcType=CHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.appeal.domain.call.CallDownloadTaskRecord">
        insert into call_download_task_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                task_id,
            </if>
            <if test="recordId != null">
                record_id,
            </if>
            <if test="download != null">
                download,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="recordId != null">
                #{recordId,jdbcType=BIGINT},
            </if>
            <if test="download != null">
                #{download,jdbcType=CHAR},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="list">
        insert into call_download_task_record (task_id, record_id, download)
        values
        <foreach collection="records" item="record" separator="," open="" close="">
            (#{record.taskId,jdbcType=BIGINT}, #{record.recordId,jdbcType=BIGINT}, #{record.download,jdbcType=CHAR})
        </foreach>
    </insert>

</mapper>
