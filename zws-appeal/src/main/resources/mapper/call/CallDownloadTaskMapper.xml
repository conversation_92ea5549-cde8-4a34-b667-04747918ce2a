<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.call.CallDownloadTaskMapper">
    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.call.CallDownloadTask">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="team_id" jdbcType="VARCHAR" property="teamId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_by_id" jdbcType="BIGINT" property="createById"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="task_status" jdbcType="INTEGER" property="taskStatus"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , task_name, create_time, create_by, create_by_id, update_time, update_by, task_status,
    file_url, remarks
    </sql>

    <sql id="Base_Column_Join_List">
        cdt
        .
        id
        , cdt.task_name,cdt.team_id, cdt.create_time,cdt.create_by, cdt.create_by_id, cdt.update_time, cdt.update_by, cdt.task_status,
    cdt.file_url, cdt.remarks,team.cname as create_by
    </sql>


    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from call_download_task
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from call_download_task
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.appeal.domain.call.CallDownloadTask" useGeneratedKeys="true"
            keyProperty="id">
        insert into call_download_task (id, task_name, team_id, create_time,
                                        create_by, create_by_id, update_time,
                                        update_by, task_status, file_url,
                                        remarks, operation_type)
        values (#{id,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, #{teamId}, #{createTime,jdbcType=TIMESTAMP},
                #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{taskStatus,jdbcType=INTEGER}, #{fileUrl,jdbcType=VARCHAR},
                #{remarks,jdbcType=VARCHAR}, #{operationType})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.appeal.domain.call.CallDownloadTask">
        insert into call_download_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskName != null">
                task_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createById != null">
                create_by_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="taskStatus != null">
                task_status,
            </if>
            <if test="fileUrl != null">
                file_url,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="taskName != null">
                #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createById != null">
                #{createById,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="taskStatus != null">
                #{taskStatus,jdbcType=INTEGER},
            </if>
            <if test="fileUrl != null">
                #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.call.CallDownloadTask">
        update call_download_task
        <set>
            <if test="taskName != null">
                task_name = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createById != null">
                create_by_id = #{createById,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="taskStatus != null">
                task_status = #{taskStatus,jdbcType=INTEGER},
            </if>
            <if test="fileUrl != null">
                file_url = #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.call.CallDownloadTask">
        update call_download_task
        set task_name    = #{taskName,jdbcType=VARCHAR},
            create_time  = #{createTime,jdbcType=TIMESTAMP},
            create_by    = #{createBy,jdbcType=VARCHAR},
            create_by_id = #{createById,jdbcType=BIGINT},
            update_time  = #{updateTime,jdbcType=TIMESTAMP},
            update_by    = #{updateBy,jdbcType=VARCHAR},
            task_status  = #{taskStatus,jdbcType=INTEGER},
            file_url     = #{fileUrl,jdbcType=VARCHAR},
            remarks      = #{remarks,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="checkTaskNameCount" resultType="int">
        select count(1)
        from call_download_task
        where task_name = #{taskName}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_Join_List"/>
        from call_download_task as cdt
        left join team_create as team on (cdt.create_by_id=team.id)
        left join team_employees as em on (cdt.create_by_id=em.id)
        where 1=1
        and cdt.team_id=#{teamId}
        <if test="taskStatus!=null">
            and cdt.task_status = #{taskStatus}
        </if>
        <if test="taskName!=null and taskName!=''">
            and cdt.task_name like concat('%',#{taskName},'%')
        </if>
        <if test="createBy!=null and createBy!=''">
            and (
            team.cname like concat('%',#{createBy},'%')
            or em.employee_name like concat('%',#{createBy},'%')
            )
        </if>
        <if test="createTime1!=null">
            and cdt.create_time >= #{createTime1}
        </if>
        <if test="createTime2!=null">
            and cdt.create_time &lt;= #{createTime2}
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            and em.department_id in
            <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="employeesIds!=null and employeesIds.size()>0">
            and em.id in
            <foreach collection="employeesIds" item="employeesId" separator="," open="(" close=")">
                #{employeesId}
            </foreach>
        </if>
        order by cdt.create_time desc
    </select>

</mapper>
