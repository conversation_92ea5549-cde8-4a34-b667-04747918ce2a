<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.call.CallOutRecordMapper">
    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.call.CallOutRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="contact_id" jdbcType="BIGINT" property="contactId"/>
        <result column="call_out_to" jdbcType="VARCHAR" property="callOutTo"/>
        <result column="service_host" jdbcType="VARCHAR" property="serviceHost"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , team_id, odv_id, case_id, contact_id, call_out_to, service_host, create_by, create_time,
    del_flag
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from call_out_record
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from call_out_record
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.appeal.domain.call.CallOutRecord" keyProperty="id"
            useGeneratedKeys="true">
        insert into call_out_record (id, team_id, odv_id,
                                     case_id, contact_id, call_out_to,
                                     service_host, create_by, create_time,
                                     del_flag, remarks)
        values (#{id,jdbcType=BIGINT}, #{teamId,jdbcType=BIGINT}, #{odvId,jdbcType=BIGINT},
                #{caseId,jdbcType=BIGINT}, #{contactId,jdbcType=BIGINT}, #{callOutTo,jdbcType=VARCHAR},
                #{serviceHost,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{delFlag,jdbcType=CHAR}, #{remarks})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.appeal.domain.call.CallOutRecord">
        insert into call_out_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="odvId != null">
                odv_id,
            </if>
            <if test="caseId != null">
                case_id,
            </if>
            <if test="contactId != null">
                contact_id,
            </if>
            <if test="callOutTo != null">
                call_out_to,
            </if>
            <if test="serviceHost != null">
                service_host,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=BIGINT},
            </if>
            <if test="odvId != null">
                #{odvId,jdbcType=BIGINT},
            </if>
            <if test="caseId != null">
                #{caseId,jdbcType=BIGINT},
            </if>
            <if test="contactId != null">
                #{contactId,jdbcType=BIGINT},
            </if>
            <if test="callOutTo != null">
                #{callOutTo,jdbcType=VARCHAR},
            </if>
            <if test="serviceHost != null">
                #{serviceHost,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.call.CallOutRecord">
        update call_out_record
        <set>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
            <if test="odvId != null">
                odv_id = #{odvId,jdbcType=BIGINT},
            </if>
            <if test="caseId != null">
                case_id = #{caseId,jdbcType=BIGINT},
            </if>
            <if test="contactId != null">
                contact_id = #{contactId,jdbcType=BIGINT},
            </if>
            <if test="callOutTo != null">
                call_out_to = #{callOutTo,jdbcType=VARCHAR},
            </if>
            <if test="serviceHost != null">
                service_host = #{serviceHost,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.call.CallOutRecord">
        update call_out_record
        set team_id      = #{teamId,jdbcType=BIGINT},
            odv_id       = #{odvId,jdbcType=BIGINT},
            case_id      = #{caseId,jdbcType=BIGINT},
            contact_id   = #{contactId,jdbcType=BIGINT},
            call_out_to  = #{callOutTo,jdbcType=VARCHAR},
            service_host = #{serviceHost,jdbcType=VARCHAR},
            create_by    = #{createBy,jdbcType=VARCHAR},
            create_time  = #{createTime,jdbcType=TIMESTAMP},
            del_flag     = #{delFlag,jdbcType=CHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
