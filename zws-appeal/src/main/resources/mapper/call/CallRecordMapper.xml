<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.call.CallRecordMapper">
    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.call.CallRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="contact_id" jdbcType="BIGINT" property="contactId"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="call_time" jdbcType="TIMESTAMP" property="callTime"/>
        <result column="agent_duration" jdbcType="INTEGER" property="agentDuration"/>
        <result column="call_from" jdbcType="VARCHAR" property="callFrom"/>
        <result column="call_to" jdbcType="VARCHAR" property="callTo"/>
        <result column="number" jdbcType="VARCHAR" property="number"/>
        <result column="callroter" jdbcType="VARCHAR" property="callroter"/>
        <result column="callid" jdbcType="VARCHAR" property="callid"/>
        <result column="answer" jdbcType="CHAR" property="answer"/>
        <result column="recording" jdbcType="VARCHAR" property="recording"/>
        <result column="hangup_cause" jdbcType="VARCHAR" property="hangupCause"/>
        <result column="trunk_name" jdbcType="VARCHAR" property="trunkName"/>
        <result column="call_prefix" jdbcType="VARCHAR" property="callPrefix"/>
        <result column="company_num" jdbcType="VARCHAR" property="companyNum"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="sip_number" jdbcType="VARCHAR" property="sipNumber"/>
        <result column="answer_start" jdbcType="TIMESTAMP" property="answerStart"/>
        <result column="answer_end" jdbcType="TIMESTAMP" property="answerEnd"/>
        <result column="callback_time" jdbcType="TIMESTAMP" property="callbackTime"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="borrower" jdbcType="VARCHAR" property="borrower"/>
        <result column="service_host" jdbcType="VARCHAR" property="serviceHost"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , contact_id, case_id, call_time, agent_duration, call_from, call_to, number, callroter,
    callid, answer, recording, hangup_cause, trunk_name, call_prefix, company_num, company_name,
    sip_number, answer_start, answer_end, callback_time, team_id, odv_id, service_host,
    create_time, del_flag
    </sql>
    <sql id="Base_Column_Join_List">
        cr
        .
        id
        , cr.contact_id, cr.case_id, cr.call_time, cr.agent_duration, cr.call_from, cr.call_to, cr.number, cr.callroter,
    cr.callid, cr.answer, cr.recording, cr.hangup_cause, cr.trunk_name, cr.call_prefix, cr.company_num, cr.company_name,
    cr.sip_number, cr.answer_start, cr.answer_end, cr.callback_time, cr.team_id, cr.odv_id, cr.service_host,cr.borrower,
    cr.create_time, cr.del_flag,t.cname as teamName,tu.employee_name as odvName
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from call_record
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_Join_List"/>
        from call_record as cr
        left join team_create as t on(cr.team_id=t.id)
        left join team_employees as tu on(cr.odv_id= tu.id)
        where cr.del_flag=0
        <if test="teamId!=null">
            and cr.team_id=#{teamId}
        </if>
        <if test="answer!=null">
            and cr.answer=#{answer}
        </if>
        <if test="callFrom!=null and callFrom!=''">
            and cr.call_from like concat('%',#{callFrom},'%')
        </if>
        <if test="callTo!=null and callTo!=''">
            and cr.call_to like concat('%',#{callTo},'%')
        </if>
        <if test="teamName!=null and teamName!=''">
            and t.cname like concat('%',#{teamName},'%')
        </if>
        <if test="odvName!=null and odvName!=''">
            and tu.employee_name like concat('%',#{odvName},'%')
        </if>
        <if test="callroter!=null and callroter!=''">
            and cr.callroter like concat('%',#{callroter},'%')
        </if>
        <if test="callroter!=null and callroter!=''">
            and cr.callroter like concat('%',#{callroter},'%')
        </if>

        <if test="callTime1!=null ">
            and cr.call_time &gt;= #{callTime1}
        </if>
        <if test="callTime2!=null ">
            and cr.call_time &lt;= #{callTime2}
        </if>
        <if test="agentDuration1!=null ">
            and cr.agent_duration &gt;= #{agentDuration1}
        </if>
        <if test="agentDuration2!=null ">
            and cr.agent_duration &lt;= #{agentDuration2}
        </if>
        <if test="ids!=null and ids.size()>0">
            and cr.id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="employeesIds!=null and employeesIds.size()>0">
            and cr.odv_id in
            <foreach collection="employeesIds" item="employeesId" separator="," open="(" close=")">
                #{employeesId}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            and tu.department_id in
            <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
        </if>
        order by cr.call_time desc
    </select>
    <select id="caseInfoBase" resultType="com.zws.appeal.domain.CaseInfoBase" parameterType="java.lang.Long">
        SELECT id,
               case_id                    AS caseId,
               asset_manage_id            AS assetManageId,
               client_name                AS clientName,
               client_name_enc            AS clientNameEnc,
               client_id_type             AS clientIdType,
               client_id_num              AS clientIdNum,
               client_id_num_enc          AS clientIdNumEnc,
               client_census_register     AS clientCensusRegister,
               client_census_register_enc AS clientCensusRegisterEnc,
               client_phone               AS clientPhone,
               client_phone_enc           AS clientPhoneEnc,
               client_sex                 AS clientSex,
               client_age                 AS clientAge,
               client_birthday            AS clientBirthday,
               occupation,
               marital_status             AS maritalStatus,
               qq,
               weixin,
               mailbox,
               place_of_work              AS placeOfWork,
               working_address            AS workingAddress,
               registered_address         AS registeredAddress,
               residential_address        AS residentialAddress,
               home_address               AS homeAddress,
               bank_name                  AS bankName,
               bank_card_number           AS bankCardNumber,
               security_name              AS securityName,
               security_name_enc          AS securityNameEnc,
               security_id_type           AS securityIdType,
               security_id_num            AS securityIdNum,
               security_id_num_enc        AS securityIdNumEnc,
               security_phone             AS securityPhone,
               security_phone_enc         AS securityPhoneEnc,
               administrative_bi          AS administrativeBi,
               asset_no                   AS assetNo,
               create_by                  AS createBy,
               create_time                AS createTime,
               update_by                  AS updateBy,
               update_time                AS updateTime,
               del_flag                   AS delFlag,
               uid,
               education,
               academic_degree            AS academicDegree,
               employment_status          AS employmentStatus,
               residential_status         AS residentialStatus,
               home_phone                 AS homePhone,
               duties,
               title,
               unit_start_year            AS unitStartYear,
               unit_industry              AS unitIndustry,
               unit_postal_code           AS unitPostalCode,
               unit_telephone             AS unitTelephone,
               residence_postal_code      AS residencePostalCode
        FROM case_info_base
        WHERE del_flag = 0
          and case_id = #{caseId}

    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.call.CallRecord">
        update call_record
        <set>
            <if test="contactId != null">
                contact_id = #{contactId,jdbcType=BIGINT},
            </if>
            <if test="caseId != null">
                case_id = #{caseId,jdbcType=BIGINT},
            </if>
            <if test="callTime != null">
                call_time = #{callTime,jdbcType=TIMESTAMP},
            </if>
            <if test="agentDuration != null">
                agent_duration = #{agentDuration,jdbcType=INTEGER},
            </if>
            <if test="callFrom != null">
                call_from = #{callFrom,jdbcType=VARCHAR},
            </if>
            <if test="callTo != null">
                call_to = #{callTo,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                number = #{number,jdbcType=VARCHAR},
            </if>
            <if test="callroter != null">
                callroter = #{callroter,jdbcType=VARCHAR},
            </if>
            <if test="callid != null">
                callid = #{callid,jdbcType=VARCHAR},
            </if>
            <if test="answer != null">
                answer = #{answer,jdbcType=CHAR},
            </if>
            <if test="recording != null">
                recording = #{recording,jdbcType=VARCHAR},
            </if>
            <if test="hangupCause != null">
                hangup_cause = #{hangupCause,jdbcType=VARCHAR},
            </if>
            <if test="trunkName != null">
                trunk_name = #{trunkName,jdbcType=VARCHAR},
            </if>
            <if test="callPrefix != null">
                call_prefix = #{callPrefix,jdbcType=VARCHAR},
            </if>
            <if test="companyNum != null">
                company_num = #{companyNum,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="sipNumber != null">
                sip_number = #{sipNumber,jdbcType=VARCHAR},
            </if>
            <if test="answerStart != null">
                answer_start = #{answerStart,jdbcType=TIMESTAMP},
            </if>
            <if test="answerEnd != null">
                answer_end = #{answerEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="callbackTime != null">
                callback_time = #{callbackTime,jdbcType=TIMESTAMP},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
            <if test="odvId != null">
                odv_id = #{odvId,jdbcType=BIGINT},
            </if>
            <if test="serviceHost != null">
                service_host = #{serviceHost,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.call.CallRecord">
        update call_record
        set contact_id     = #{contactId,jdbcType=BIGINT},
            case_id        = #{caseId,jdbcType=BIGINT},
            call_time      = #{callTime,jdbcType=TIMESTAMP},
            agent_duration = #{agentDuration,jdbcType=INTEGER},
            call_from      = #{callFrom,jdbcType=VARCHAR},
            call_to        = #{callTo,jdbcType=VARCHAR},
            number         = #{number,jdbcType=VARCHAR},
            callroter      = #{callroter,jdbcType=VARCHAR},
            callid         = #{callid,jdbcType=VARCHAR},
            answer         = #{answer,jdbcType=CHAR},
            recording      = #{recording,jdbcType=VARCHAR},
            hangup_cause   = #{hangupCause,jdbcType=VARCHAR},
            trunk_name     = #{trunkName,jdbcType=VARCHAR},
            call_prefix    = #{callPrefix,jdbcType=VARCHAR},
            company_num    = #{companyNum,jdbcType=VARCHAR},
            company_name   = #{companyName,jdbcType=VARCHAR},
            sip_number     = #{sipNumber,jdbcType=VARCHAR},
            answer_start   = #{answerStart,jdbcType=TIMESTAMP},
            answer_end     = #{answerEnd,jdbcType=TIMESTAMP},
            callback_time  = #{callbackTime,jdbcType=TIMESTAMP},
            team_id        = #{teamId,jdbcType=BIGINT},
            odv_id         = #{odvId,jdbcType=BIGINT},
            service_host   = #{serviceHost,jdbcType=VARCHAR},
            create_time    = #{createTime,jdbcType=TIMESTAMP},
            del_flag       = #{delFlag,jdbcType=CHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
