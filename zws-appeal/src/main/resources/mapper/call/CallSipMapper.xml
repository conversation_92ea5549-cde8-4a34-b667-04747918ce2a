<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.call.CallSipMapper">
    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.call.CallSip">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="sip_number" jdbcType="VARCHAR" property="sipNumber"/>
        <result column="sip_password" jdbcType="VARCHAR" property="sipPassword"/>

        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="company_num" jdbcType="VARCHAR" property="companyNum"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="bind_status" jdbcType="CHAR" property="bindStatus"/>
        <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>


    </resultMap>
    <sql id="Base_Column_List">
        id
        , sip_number,sip_password, team_id, odv_id, company_num, company_name, bind_status, bind_time,
    create_by, create_time, update_by, update_time, del_flag
    </sql>

    <sql id="Base_Column_Join_List">
        cs
        .
        id
        , cs.sip_number, cs.sip_password,cs.team_id, cs.odv_id, cs.company_num, cs.company_name, cs.bind_status, cs.bind_time,
    cs.create_by, cs.create_time, cs.update_by, cs.update_time, cs.del_flag,t.cname AS teamName,te.employee_name as odvName
    </sql>


    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from call_sip
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_Join_List"/>
        from call_sip as cs
        left join team_create as t on (t.id=cs.team_id)
        left join team_employees as te on (te.id =cs.odv_id)
        where cs.del_flag=0
        <if test="teamId!=null">
            and cs.team_id=#{teamId}
        </if>
        <if test="companyNum!=null">
            and cs.company_num=#{companyNum}
        </if>
        <if test="sipNumber!=null and sipNumber!=''">
            and cs.sip_number like concat('%',#{sipNumber},'%')
        </if>
        <if test="teamName!=null and teamName!=''">
            and t.cname like concat('%',#{teamName},'%')
        </if>
        <if test="companyName!=null and companyName!=''">
            and cs.company_name like concat('%',#{companyName},'%')
        </if>
        <if test="bindStatus!=null and bindStatus!=''">
            and cs.bind_status=#{bindStatus}
        </if>
        <if test="odvName!=null and odvName!=''">
            and te.employee_name like concat('%',#{odvName},'%')
        </if>
        <if test="bindTime1!=null ">
            and cs.bind_time &gt;= #{bindTime1}
        </if>
        <if test="bindTime2!=null ">
            and cs.bind_time &lt;= #{bindTime2}
        </if>
        order by create_time desc
    </select>

    <select id="selectBySipNumber" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from call_sip
        where sip_number=#{sipNumber}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from call_sip
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="cancelSipByOdvId">
        update call_sip
        <set>
            odv_id = null,
            bind_time = null,
            <if test="bindStatus != null">
                bind_status = #{bindStatus,jdbcType=CHAR},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where odv_id = #{odvId}
    </delete>

    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.call.CallSip">
        update call_sip
        <set>
            <if test="sipNumber != null">
                sip_number = #{sipNumber,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
            <if test="odvId != null">
                odv_id = #{odvId,jdbcType=BIGINT},
            </if>
            <if test="companyNum != null">
                company_num = #{companyNum,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="bindStatus != null">
                bind_status = #{bindStatus,jdbcType=CHAR},
            </if>
            <if test="bindTime != null">
                bind_time = #{bindTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateBySipNumberSelective">
        update call_sip
        <set>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
            <if test="odvId != null">
                odv_id = #{odvId,jdbcType=BIGINT},
            </if>
            <if test="sipPassword != null">
                sip_password = #{sipPassword},
            </if>
            <if test="companyNum != null">
                company_num = #{companyNum,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="bindStatus != null">
                bind_status = #{bindStatus,jdbcType=CHAR},
            </if>
            <if test="bindTime != null">
                bind_time = #{bindTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where sip_number = #{sipNumber,jdbcType=VARCHAR}
    </update>
</mapper>
