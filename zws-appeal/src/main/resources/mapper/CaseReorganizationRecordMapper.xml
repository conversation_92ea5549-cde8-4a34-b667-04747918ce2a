<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.CaseReorganizationRecordMapper">

    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.CaseReorganizationRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="caseId" column="case_id" jdbcType="BIGINT"/>
            <result property="approveId" column="approve_id" jdbcType="BIGINT"/>
            <result property="teamId" column="team_id" jdbcType="BIGINT"/>
            <result property="teamName" column="team_name" jdbcType="VARCHAR"/>
            <result property="stagingNum" column="staging_num" jdbcType="INTEGER"/>
            <result property="repaymentDate" column="repayment_date" jdbcType="INTEGER"/>
            <result property="repaymentMonthly" column="repayment_monthly" jdbcType="DECIMAL"/>
            <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            <result property="entrustingCaseBatchNum" column="entrusting_case_batch_num" jdbcType="VARCHAR"/>
            <result property="entrustingCaseDate" column="entrusting_case_date" jdbcType="TIMESTAMP"/>
            <result property="returnCaseDate" column="return_case_date" jdbcType="DATE"/>
            <result property="odvId" column="odv_id" jdbcType="BIGINT"/>
            <result property="odvName" column="odv_name" jdbcType="VARCHAR"/>
            <result property="operationType" column="operation_type" jdbcType="INTEGER"/>
            <result property="firstRepaymentDate" column="first_repayment_date" jdbcType="DATE"/>
            <result property="lastRepaymentDate" column="last_repayment_date" jdbcType="DATE"/>
            <result property="downPayment" column="down_payment" jdbcType="DECIMAL"/>
            <result property="finalPayment" column="final_payment" jdbcType="DECIMAL"/>
            <result property="repaymentPlan" column="repayment_plan" jdbcType="VARCHAR"/>
            <result property="uploadCredentials" column="upload_credentials" jdbcType="VARCHAR"/>
            <result property="oldRemainingDue" column="old_remaining_due" jdbcType="DECIMAL"/>
            <result property="deductionAmount" column="deduction_amount" jdbcType="DECIMAL"/>
            <result property="totalRepayment" column="total_repayment" jdbcType="DECIMAL"/>
            <result property="afterBaseDateInterest" column="after_base_date_interest" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,case_id,approve_id,
        team_id,team_name,staging_num,
        repayment_date,repayment_monthly,del_flag,
        entrusting_case_batch_num,entrusting_case_date,return_case_date,
        odv_id,odv_name,operation_type,
        first_repayment_date,last_repayment_date,down_payment,
        final_payment,repayment_plan,upload_credentials,
        old_remaining_due,deduction_amount,total_repayment,after_base_date_interest
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from case_reorganization_record
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from case_reorganization_record
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.CaseReorganizationRecord" useGeneratedKeys="true">
        insert into case_reorganization_record
        ( id,case_id,approve_id
        ,team_id,team_name,staging_num
        ,repayment_date,repayment_monthly,del_flag
        ,entrusting_case_batch_num,entrusting_case_date,return_case_date
        ,odv_id,odv_name,operation_type
        ,first_repayment_date,last_repayment_date,down_payment
        ,final_payment,repayment_plan,upload_credentials
        ,old_remaining_due,deduction_amount,total_repayment,after_base_date_interest,apply_type
        )
        values (#{id,jdbcType=BIGINT},#{caseId,jdbcType=BIGINT},#{approveId,jdbcType=BIGINT}
               ,#{teamId,jdbcType=BIGINT},#{teamName,jdbcType=VARCHAR},#{stagingNum,jdbcType=INTEGER}
               ,#{repaymentDate,jdbcType=INTEGER},#{repaymentMonthly,jdbcType=DECIMAL},#{delFlag,jdbcType=INTEGER}
               ,#{entrustingCaseBatchNum,jdbcType=VARCHAR},#{entrustingCaseDate,jdbcType=TIMESTAMP},#{returnCaseDate,jdbcType=DATE}
               ,#{odvId,jdbcType=BIGINT},#{odvName,jdbcType=VARCHAR},#{operationType,jdbcType=INTEGER}
               ,#{firstRepaymentDate,jdbcType=DATE},#{lastRepaymentDate,jdbcType=DATE},#{downPayment,jdbcType=DECIMAL}
               ,#{finalPayment,jdbcType=DECIMAL},#{repaymentPlan,jdbcType=VARCHAR},#{uploadCredentials,jdbcType=VARCHAR}
               ,#{oldRemainingDue,jdbcType=DECIMAL},#{deductionAmount,jdbcType=DECIMAL},#{totalRepayment,jdbcType=DECIMAL},#{afterBaseDateInterest,jdbcType=DECIMAL},#{applyType}
               )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.CaseReorganizationRecord" useGeneratedKeys="true">
        insert into case_reorganization_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="caseId != null">case_id,</if>
            <if test="approveId != null">approve_id,</if>
            <if test="teamId != null">team_id,</if>
            <if test="teamName != null">team_name,</if>
            <if test="state != null">state,</if>
            <if test="stagingNum != null">staging_num,</if>
            <if test="repaymentDate != null">repayment_date,</if>
            <if test="repaymentMonthly != null">repayment_monthly,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="entrustingCaseBatchNum != null">entrusting_case_batch_num,</if>
            <if test="entrustingCaseDate != null">entrusting_case_date,</if>
            <if test="returnCaseDate != null">return_case_date,</if>
            <if test="odvId != null">odv_id,</if>
            <if test="odvName != null">odv_name,</if>
            <if test="operationType != null">operation_type,</if>
            <if test="firstRepaymentDate != null">first_repayment_date,</if>
            <if test="lastRepaymentDate != null">last_repayment_date,</if>
            <if test="downPayment != null">down_payment,</if>
            <if test="finalPayment != null">final_payment,</if>
            <if test="repaymentPlan != null">repayment_plan,</if>
            <if test="uploadCredentials != null">upload_credentials,</if>
            <if test="oldRemainingDue != null">old_remaining_due,</if>
            <if test="deductionAmount != null">deduction_amount,</if>
            <if test="totalRepayment != null">total_repayment,</if>
            <if test="afterBaseDateInterest != null">after_base_date_interest,</if>
            <if test="applyType != null">apply_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="caseId != null">#{caseId,jdbcType=BIGINT},</if>
            <if test="approveId != null">#{approveId,jdbcType=BIGINT},</if>
            <if test="teamId != null">#{teamId,jdbcType=BIGINT},</if>
            <if test="teamName != null">#{teamName,jdbcType=VARCHAR},</if>
            <if test="state != null">#{state,jdbcType=VARCHAR},</if>
            <if test="stagingNum != null">#{stagingNum,jdbcType=INTEGER},</if>
            <if test="repaymentDate != null">#{repaymentDate,jdbcType=INTEGER},</if>
            <if test="repaymentMonthly != null">#{repaymentMonthly,jdbcType=DECIMAL},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=INTEGER},</if>
            <if test="entrustingCaseBatchNum != null">#{entrustingCaseBatchNum,jdbcType=VARCHAR},</if>
            <if test="entrustingCaseDate != null">#{entrustingCaseDate,jdbcType=TIMESTAMP},</if>
            <if test="returnCaseDate != null">#{returnCaseDate,jdbcType=DATE},</if>
            <if test="odvId != null">#{odvId,jdbcType=BIGINT},</if>
            <if test="odvName != null">#{odvName,jdbcType=VARCHAR},</if>
            <if test="operationType != null">#{operationType,jdbcType=INTEGER},</if>
            <if test="firstRepaymentDate != null">#{firstRepaymentDate,jdbcType=DATE},</if>
            <if test="lastRepaymentDate != null">#{lastRepaymentDate,jdbcType=DATE},</if>
            <if test="downPayment != null">#{downPayment,jdbcType=DECIMAL},</if>
            <if test="finalPayment != null">#{finalPayment,jdbcType=DECIMAL},</if>
            <if test="repaymentPlan != null">#{repaymentPlan,jdbcType=VARCHAR},</if>
            <if test="uploadCredentials != null">#{uploadCredentials,jdbcType=VARCHAR},</if>
            <if test="oldRemainingDue != null">#{oldRemainingDue,jdbcType=DECIMAL},</if>
            <if test="deductionAmount != null">#{deductionAmount,jdbcType=DECIMAL},</if>
            <if test="totalRepayment != null">#{totalRepayment,jdbcType=DECIMAL},</if>
            <if test="afterBaseDateInterest != null">#{afterBaseDateInterest,jdbcType=DECIMAL},</if>
            <if test="applyType != null">#{applyType},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.CaseReorganizationRecord">
        update case_reorganization_record
        <set>
            <if test="caseId != null">
                case_id = #{caseId,jdbcType=BIGINT},
            </if>
            <if test="approveId != null">
                approve_id = #{approveId,jdbcType=BIGINT},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
            <if test="teamName != null">
                team_name = #{teamName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=VARCHAR},
            </if>
            <if test="stagingNum != null">
                staging_num = #{stagingNum,jdbcType=INTEGER},
            </if>
            <if test="repaymentDate != null">
                repayment_date = #{repaymentDate,jdbcType=INTEGER},
            </if>
            <if test="repaymentMonthly != null">
                repayment_monthly = #{repaymentMonthly,jdbcType=DECIMAL},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="entrustingCaseBatchNum != null">
                entrusting_case_batch_num = #{entrustingCaseBatchNum,jdbcType=VARCHAR},
            </if>
            <if test="entrustingCaseDate != null">
                entrusting_case_date = #{entrustingCaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="returnCaseDate != null">
                return_case_date = #{returnCaseDate,jdbcType=DATE},
            </if>
            <if test="odvId != null">
                odv_id = #{odvId,jdbcType=BIGINT},
            </if>
            <if test="odvName != null">
                odv_name = #{odvName,jdbcType=VARCHAR},
            </if>
            <if test="operationType != null">
                operation_type = #{operationType,jdbcType=INTEGER},
            </if>
            <if test="firstRepaymentDate != null">
                first_repayment_date = #{firstRepaymentDate,jdbcType=DATE},
            </if>
            <if test="lastRepaymentDate != null">
                last_repayment_date = #{lastRepaymentDate,jdbcType=DATE},
            </if>
            <if test="downPayment != null">
                down_payment = #{downPayment,jdbcType=DECIMAL},
            </if>
            <if test="finalPayment != null">
                final_payment = #{finalPayment,jdbcType=DECIMAL},
            </if>
            <if test="repaymentPlan != null">
                repayment_plan = #{repaymentPlan,jdbcType=VARCHAR},
            </if>
            <if test="uploadCredentials != null">
                upload_credentials = #{uploadCredentials,jdbcType=VARCHAR},
            </if>
            <if test="oldRemainingDue != null">
                old_remaining_due = #{oldRemainingDue,jdbcType=DECIMAL},
            </if>
            <if test="deductionAmount != null">
                deduction_amount = #{deductionAmount,jdbcType=DECIMAL},
            </if>
            <if test="totalRepayment != null">
                total_repayment = #{totalRepayment,jdbcType=DECIMAL},
            </if>
            <if test="afterBaseDateInterest != null">
                after_base_date_interest = #{afterBaseDateInterest,jdbcType=DECIMAL},
            </if>
            <if test="applyType != null">
                apply_type = #{applyType},
            </if>
        </set>
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByApproveIdSelective" parameterType="com.zws.appeal.domain.CaseReorganizationRecord">
        update case_reorganization_record
        <set>
            <if test="caseId != null">
                case_id = #{caseId,jdbcType=BIGINT},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
            <if test="teamName != null">
                team_name = #{teamName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=VARCHAR},
            </if>
            <if test="stagingNum != null">
                staging_num = #{stagingNum,jdbcType=INTEGER},
            </if>
            <if test="repaymentDate != null">
                repayment_date = #{repaymentDate,jdbcType=INTEGER},
            </if>
            <if test="repaymentMonthly != null">
                repayment_monthly = #{repaymentMonthly,jdbcType=DECIMAL},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="entrustingCaseBatchNum != null">
                entrusting_case_batch_num = #{entrustingCaseBatchNum,jdbcType=VARCHAR},
            </if>
            <if test="entrustingCaseDate != null">
                entrusting_case_date = #{entrustingCaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="returnCaseDate != null">
                return_case_date = #{returnCaseDate,jdbcType=DATE},
            </if>
            <if test="odvId != null">
                odv_id = #{odvId,jdbcType=BIGINT},
            </if>
            <if test="odvName != null">
                odv_name = #{odvName,jdbcType=VARCHAR},
            </if>
            <if test="operationType != null">
                operation_type = #{operationType,jdbcType=INTEGER},
            </if>
            <if test="firstRepaymentDate != null">
                first_repayment_date = #{firstRepaymentDate,jdbcType=DATE},
            </if>
            <if test="lastRepaymentDate != null">
                last_repayment_date = #{lastRepaymentDate,jdbcType=DATE},
            </if>
            <if test="downPayment != null">
                down_payment = #{downPayment,jdbcType=DECIMAL},
            </if>
            <if test="finalPayment != null">
                final_payment = #{finalPayment,jdbcType=DECIMAL},
            </if>
            <if test="repaymentPlan != null">
                repayment_plan = #{repaymentPlan,jdbcType=VARCHAR},
            </if>
            <if test="uploadCredentials != null">
                upload_credentials = #{uploadCredentials,jdbcType=VARCHAR},
            </if>
            <if test="oldRemainingDue != null">
                old_remaining_due = #{oldRemainingDue,jdbcType=DECIMAL},
            </if>
            <if test="deductionAmount != null">
                deduction_amount = #{deductionAmount,jdbcType=DECIMAL},
            </if>
            <if test="totalRepayment != null">
                total_repayment = #{totalRepayment,jdbcType=DECIMAL},
            </if>
            <if test="afterBaseDateInterest != null">
                after_base_date_interest = #{afterBaseDateInterest,jdbcType=DECIMAL},
            </if>
            <if test="applyType != null">
                apply_type = #{applyType},
            </if>
        </set>
        where  approve_id = #{approveId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.CaseReorganizationRecord">
        update case_reorganization_record
        set
            case_id =  #{caseId,jdbcType=BIGINT},
            approve_id =  #{approveId,jdbcType=BIGINT},
            team_id =  #{teamId,jdbcType=BIGINT},
            team_name =  #{teamName,jdbcType=VARCHAR},
            staging_num =  #{stagingNum,jdbcType=INTEGER},
            repayment_date =  #{repaymentDate,jdbcType=INTEGER},
            repayment_monthly =  #{repaymentMonthly,jdbcType=DECIMAL},
            del_flag =  #{delFlag,jdbcType=INTEGER},
            entrusting_case_batch_num =  #{entrustingCaseBatchNum,jdbcType=VARCHAR},
            entrusting_case_date =  #{entrustingCaseDate,jdbcType=TIMESTAMP},
            return_case_date =  #{returnCaseDate,jdbcType=DATE},
            odv_id =  #{odvId,jdbcType=BIGINT},
            odv_name =  #{odvName,jdbcType=VARCHAR},
            operation_type =  #{operationType,jdbcType=INTEGER},
            first_repayment_date =  #{firstRepaymentDate,jdbcType=DATE},
            last_repayment_date =  #{lastRepaymentDate,jdbcType=DATE},
            down_payment =  #{downPayment,jdbcType=DECIMAL},
            final_payment =  #{finalPayment,jdbcType=DECIMAL},
            repayment_plan =  #{repaymentPlan,jdbcType=VARCHAR},
            upload_credentials =  #{uploadCredentials,jdbcType=VARCHAR},
            old_remaining_due =  #{oldRemainingDue,jdbcType=DECIMAL},
            deduction_amount =  #{deductionAmount,jdbcType=DECIMAL},
            total_repayment =  #{totalRepayment,jdbcType=DECIMAL},
            after_base_date_interest =  #{afterBaseDateInterest,jdbcType=DECIMAL},
            apply_type =  #{applyType}
        where   id = #{id,jdbcType=BIGINT}
    </update>

    <select id="jgQueryApproveList" parameterType="com.zws.appeal.pojo.PaymentCollectionUtils" resultType="com.zws.appeal.pojo.myApproval.MyStagingRecordUtils">
        select distinct rep.id AS applyIds,
        rep.case_id AS caseId,
        ar.applicant,
        ar.apply_date AS applyDate,
        ar.reason AS reason,
        rep.approve_id AS approveId,
        rep.staging_num AS stagingNum,
        rep.repayment_date AS repaymentDate,
        rep.repayment_monthly AS repaymentMonthly,
        rep.repayment_plan AS repaymentPlan,
        rep.total_repayment AS totalRepayment,
        rep.deduction_amount AS deductionAmount,
        rep.down_payment AS downPayment,
        rep.final_payment AS finalPayment,
        ar.approve_state AS examineState,
        ar.examine_time AS examineTime,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.return_case_date AS returnCaseDate,
        rep.upload_credentials AS uploadCredentials,

        <!-- ap.approve_state AS approveStart, -->
        <!--  ap.approve_time AS approveTime, -->
        cm.outsourcing_team_id as teamId,
        cm.case_state AS caseState,
        cli.product_name AS productName,
        cli.client_name AS clientName,
        cli.client_id_num AS clientIdcard,
        cli.client_census_register AS clientCensusRegister,
        cli.client_id_type as clientIdType,
        cas.id AS labelId,
        cas.label_content AS labelContent,
        cas.code AS label,
        cil.sy_yh_principal AS syYhPrincipal,
        cil.remaining_due AS remainingDue,
        cil.entrust_money AS entrustMoney,
        cil.residual_principal AS residualPrincipal,
        am.package_name AS packageName


        from case_reorganization_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN case_info_loan AS cil ON(rep.case_id=cil.case_id and cil.del_flag = 0)
        LEFT JOIN asset_manage AS am ON (am.batch_num = cm.batch_num and am.del_flag = 0)
        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)
        LEFT JOIN team_employees AS te ON (rep.odv_id=te.id)
        LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        LEFT JOIN case_library AS cli ON(rep.case_id=cli.id)
        left join approve_record as ar on(rep.approve_id=ar.id)
        <if test="approveProcessIds!=null and approveProcessIds.size()>0">
            left join approve_process as ap on(rep.approve_id=ap.approve_id)
        </if>
        <!-- left join approve_process as ap on(ap.approve_id=ar.id) -->
        WHERE rep.del_flag = 0 and rep.team_id = #{teamId}
        <!-- and ar.`examine_state` = "已退案关闭" -->
        <if test="approveIds!=null and approveIds.size()>0">
            and ar.id in
            <foreach collection="approveIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="approveProcessIds!=null and approveProcessIds.size()>0">
            and ap.id in
            <foreach collection="approveProcessIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="employeesIds!=null and employeesIds.size()>0">
            and te.id in
            <foreach collection="employeesIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            and td.id in
            <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="ids!=null and ids.size()>0">
            and rep.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <!--        <choose>-->
        <!--            <when test="selectAll!=null">-->
        <!--                and (-->
        <!--                <if test="approveSort!=null and approveSort == 1">-->
        <!--                    (ar.proce =0) OR-->
        <!--                </if>-->
        <!--                (ar.proce =1 AND ar.proce_sort=#{approveSort}-1)-->
        <!--                or (ar.proce in (1,2,3,4,5) and c.reviewer_id=#{reviewerId} )-->
        <!--                )-->
        <!--            </when>-->
        <!--        </choose>-->
        <!--        <if test="examineStateList != null ">-->
        <!--            and ar.examine_state in-->
        <!--            <foreach item="item" collection="examineStateList" separator="," open="(" close=")" index="">-->
        <!--                #{item}-->
        <!--            </foreach>-->
        <!--        </if>-->

        <if test="caseId != null ">and rep.case_id=#{caseId}</if>
        <!-- <if test="applicant != null and applicant != ''">and ar.applicant=#{applicant}</if> -->
        <if test="clientName != null ">and cm.client_name=#{clientName}</if>
        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>
        <!--        <if test="registrar != null ">and ar.applicant=#{applicant}</if>-->
        <!--        <if test="updateTime1 != null ">and ar.approve_time &gt;= #{updateTime1}</if>-->
        <!--        <if test="updateTime2 != null ">and ar.approve_time &lt;= #{updateTime2}</if>-->
        <!--        <if test="applyDate1 != null ">and ar.apply_date &gt;= #{applyDate1}</if>-->
        <!--        <if test="applyDate2 != null ">and ar.apply_date &lt;= #{applyDate2}</if>-->
        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by ar.apply_date desc
    </select>
    <select id="queryJgMyApplyList" resultType="com.zws.appeal.pojo.InheritanceCollection">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.approve_id AS approveId,
        rep.staging_num AS stagingNum,
        rep.repayment_monthly AS repaymentMonthly,
        rep.repayment_date AS repaymentDates,
        ar.approve_state AS examineState,
        ar.examine_time AS examineTime,
        ar.applicant AS applicant,
        ar.apply_date AS applyDate,
        ar.reason AS reason,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.upload_credentials as uploadCredentials,
        rep.repayment_plan AS repaymentPlan,
        rep.total_repayment AS totalRepayment,
        rep.deduction_amount AS deductionAmount,
        rep.down_payment AS downPayment,
        rep.final_payment AS finalPayment,
        cli.client_name AS clientName,
        cli.client_id_num AS clientIdcard,
        cli.client_census_register AS clientCensusRegister,
        cli.product_name AS productName,
        cli.client_id_type as clientIdType,
        cil.sy_yh_principal            as syYhPrincipal,
        cil.entrust_money            as entrustMoney,
        cil.remaining_due           as remainingDue,
        am.package_name   packageName,
        cil.residual_principal          as residualPrincipal,

        cm.outsourcing_team_id as teamId
        from case_reorganization_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN case_library AS cli ON(rep.case_id=cli.id)
        LEFT JOIN asset_manage AS am ON (am.id = cli.asset_manage_id and cm.del_flag = 0)
        LEFT JOIN case_info_loan AS cil ON (rep.case_id = cil.case_id)
        left join approve_record AS ar ON (rep.approve_id = ar.id)
        where rep.del_flag = 0  and rep.operation_type = #{operationType}
        <if test="approveIds!=null and approveIds.size()>0">
            and ar.id in
            <foreach collection="approveIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by ar.apply_date desc
    </select>
    <select id="queryJgTeamApplyList"
            resultType="com.zws.appeal.pojo.teamApplication.CreateStagingRecordUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.approve_id AS approveId,
        rep.staging_num AS stagingNum,
        rep.repayment_monthly AS repaymentMonthly,
        rep.repayment_date AS repaymentDate,
        ar.approve_state AS examineState,
        ar.examine_time AS examineTime,
        ar.applicant AS applicant,
        ar.apply_date AS applyDate,
        ar.reason AS reason,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.repayment_plan AS repaymentPlan,
        rep.total_repayment AS totalRepayment,
        rep.deduction_amount AS deductionAmount,
        rep.down_payment AS downPayment,
        rep.final_payment AS finalPayment,
        rep.upload_credentials AS uploadCredentials,
        rep.repayment_plan AS repaymentPlan,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_id_type as clientIdType,
        cm.outsourcing_team_id as teamId,
        cil.sy_yh_principal AS syYhPrincipal,
        cil.remaining_due AS remainingDue,
        cil.entrust_money AS entrustMoney,
        cil.residual_principal AS residualPrincipal,
        am.package_name AS packageName,
        rep.odv_name AS odvName
        from case_reorganization_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN case_info_loan AS cil ON(rep.case_id=cil.case_id and cil.del_flag = 0)
        LEFT JOIN asset_manage AS am ON (am.batch_num = cm.batch_num and am.del_flag = 0)
        left join approve_record AS ar ON (rep.approve_id = ar.id)
        LEFT JOIN team_employees AS emp ON (ar.applicant_id = emp.id)
        where rep.del_flag = 0 and rep.team_id = #{createId}
        <if test="approveIds!=null and approveIds.size()>0">
            and ar.id in
            <foreach collection="approveIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="approveIds!=null and approveIds.size()>0">
            and ar.id in
            <foreach collection="approveIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="userId != null">and emp.id =#{userId}</if>

        <if test="caseId != null">and rep.case_id =#{caseId}</if>
        <if test="clientName != null and clientName != ''">and cm.client_name =#{clientName}</if>
        <if test="clientIdcard != null and clientIdcard != ''">and cm.client_idcard =#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null and entrustingCaseBatchNum != ''">and rep.entrusting_case_batch_num
            =#{entrustingCaseBatchNum}
        </if>
        <if test="entrustingCaseDate1 != null">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by ar.apply_date desc
    </select>
    <select id="selectRepaymentPlanReview" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from case_reorganization_record
        where case_id = #{caseId} and state = "已通过"
        order by id desc
        limit 1
    </select>
    <select id="selectNotFinishCountByCaseId" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            case_reorganization_record AS crr
                LEFT JOIN approve_record AS ar ON ( crr.approve_id = ar.id )
        WHERE
            crr.case_id = #{caseId}
          AND ar.approve_process != 9 and crr.team_id = #{teamId} and ar.applicant_id = #{odvId}
    </select>
    <select id="selectAfterBaseDateInterestByInfoLoan" resultType="java.math.BigDecimal">
        select after_base_date_interest AS remainingDue
        from case_info_loan
        where del_flag = 0
          and case_id = #{caseId}
    </select>
    <select id="jgQueryApproveListNew" resultType="com.zws.appeal.pojo.myApproval.MyStagingRecordUtils">
        select distinct rep.id AS applyIds,
        rep.case_id AS caseId,
        zws_ar.applicant,
        zws_ar.apply_date AS applyDate,
        rep.staging_num AS stagingNum,
        rep.approve_id AS approveId,
        rep.repayment_date AS repaymentDate,
        rep.repayment_monthly AS repaymentMonthly,
        rep.repayment_plan AS repaymentPlan,
        rep.total_repayment AS totalRepayment,
        rep.deduction_amount AS deductionAmount,
        rep.down_payment AS downPayment,
        rep.final_payment AS finalPayment,
        zws_ar.approve_state AS examineState,
        zws_ar.examine_time AS examineTime,
        zws_ar.reason AS reason,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.return_case_date AS returnCaseDate,
        rep.upload_credentials AS uploadCredentials,

        <!-- ap.approve_state AS approveStart, -->
        <!--  ap.approve_time AS approveTime, -->
        cm.outsourcing_team_id as teamId,
        cm.case_state AS caseState,
        cli.product_name AS productName,
        cli.client_name AS clientName,
        cli.client_id_num AS clientIdcard,
        cli.client_census_register AS clientCensusRegister,
        cli.client_id_type as clientIdType,
        cas.id AS labelId,
        cas.label_content AS labelContent,
        cas.code AS label,
        cil.sy_yh_principal AS syYhPrincipal,
        cil.remaining_due AS remainingDue,
        cil.entrust_money AS entrustMoney,
        cil.residual_principal AS residualPrincipal,
        am.package_name AS packageName

        from ${sqlDataDto.fromData}
        LEFT JOIN case_reorganization_record AS rep ON(rep.approve_id=zws_ar.id)
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN case_info_loan AS cil ON(rep.case_id=cil.case_id and cil.del_flag = 0)
        LEFT JOIN asset_manage AS am ON (am.batch_num = cm.batch_num and am.del_flag = 0)
        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)
        LEFT JOIN team_employees AS te ON (rep.odv_id=te.id)
        LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        LEFT JOIN case_library AS cli ON(rep.case_id=cli.id)
        <!-- left join approve_process as ap on(ap.approve_id=ar.id) -->
        WHERE ${sqlDataDto.whereData}
        and rep.del_flag = 0 and rep.team_id = #{queryDto.teamId}

        <if test="queryDto.caseId != null ">and rep.case_id=#{queryDto.caseId}</if>
        <!-- <if test="applicant != null and applicant != ''">and ar.applicant=#{applicant}</if> -->
        <if test="queryDto.clientName != null ">and cm.client_name=#{queryDto.clientName}</if>
        <if test="queryDto.clientIdcard != null ">and cm.client_idcard=#{queryDto.clientIdcard}</if>
        <if test="queryDto.entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{queryDto.entrustingCaseBatchNum}</if>
        <!--        <if test="registrar != null ">and ar.applicant=#{applicant}</if>-->
        <!--        <if test="updateTime1 != null ">and ar.approve_time &gt;= #{updateTime1}</if>-->
        <!--        <if test="updateTime2 != null ">and ar.approve_time &lt;= #{updateTime2}</if>-->
        <!--        <if test="applyDate1 != null ">and ar.apply_date &gt;= #{applyDate1}</if>-->
        <!--        <if test="applyDate2 != null ">and ar.apply_date &lt;= #{applyDate2}</if>-->
        <if test="queryDto.entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{queryDto.entrustingCaseDate1}</if>
        <if test="queryDto.entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{queryDto.entrustingCaseDate2}</if>
        <if test="queryDto.returnCaseDate1 != null ">and rep.return_case_date &gt;= #{queryDto.returnCaseDate1}</if>
        <if test="queryDto.returnCaseDate2 != null ">and rep.return_case_date &lt;= #{queryDto.returnCaseDate2}</if>
        order by zws_ar.apply_date desc
    </select>
</mapper>
