<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.financial.FinancialAccountMapper">
    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.financial.FinancialAccount">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bank_name" jdbcType="VARCHAR" property="bankName"/>
        <result column="account_name" jdbcType="VARCHAR" property="accountName"/>
        <result column="account_number" jdbcType="VARCHAR" property="accountNumber"/>
        <result column="account_status" jdbcType="INTEGER" property="accountStatus"/>
        <result column="created_by_id" jdbcType="BIGINT" property="createdById"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , bank_name, account_name, account_number, account_status, created_by_id, created_by,
    created_time, update_by, update_time, del_flag
    </sql>

    <sql id="AS_Base_Column_List">
        fa
        .
        id
        , fa.bank_name, fa.account_name, fa.account_number, fa.account_status, fa.created_by_id, fa.created_by,
    fa.created_time, fa.update_by, fa.update_time, fa.del_flag
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from financial_account
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByCaseId" resultMap="BaseResultMap">
        SELECT
        <include refid="AS_Base_Column_List"/>
        FROM case_library AS cl
        LEFT JOIN asset_manage AS am ON(cl.asset_manage_id=am.id)
        LEFT JOIN financial_account AS fa ON(am.account_id=fa.id)
        WHERE fa.del_flag=0 AND fa.account_status=0 AND cl.id=#{caseId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from financial_account
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.appeal.domain.financial.FinancialAccount">
        insert into financial_account (id, bank_name, account_name,
                                       account_number, account_status, created_by_id,
                                       created_by, created_time, update_by,
                                       update_time, del_flag)
        values (#{id,jdbcType=BIGINT}, #{bankName,jdbcType=VARCHAR}, #{accountName,jdbcType=VARCHAR},
                #{accountNumber,jdbcType=VARCHAR}, #{accountStatus,jdbcType=INTEGER}, #{createdById,jdbcType=BIGINT},
                #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
                #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.appeal.domain.financial.FinancialAccount">
        insert into financial_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="bankName != null">
                bank_name,
            </if>
            <if test="accountName != null">
                account_name,
            </if>
            <if test="accountNumber != null">
                account_number,
            </if>
            <if test="accountStatus != null">
                account_status,
            </if>
            <if test="createdById != null">
                created_by_id,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="bankName != null">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="accountName != null">
                #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="accountNumber != null">
                #{accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="accountStatus != null">
                #{accountStatus,jdbcType=INTEGER},
            </if>
            <if test="createdById != null">
                #{createdById,jdbcType=BIGINT},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.financial.FinancialAccount">
        update financial_account
        <set>
            <if test="bankName != null">
                bank_name = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="accountName != null">
                account_name = #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="accountNumber != null">
                account_number = #{accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="accountStatus != null">
                account_status = #{accountStatus,jdbcType=INTEGER},
            </if>
            <if test="createdById != null">
                created_by_id = #{createdById,jdbcType=BIGINT},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.financial.FinancialAccount">
        update financial_account
        set bank_name      = #{bankName,jdbcType=VARCHAR},
            account_name   = #{accountName,jdbcType=VARCHAR},
            account_number = #{accountNumber,jdbcType=VARCHAR},
            account_status = #{accountStatus,jdbcType=INTEGER},
            created_by_id  = #{createdById,jdbcType=BIGINT},
            created_by     = #{createdBy,jdbcType=VARCHAR},
            created_time   = #{createdTime,jdbcType=TIMESTAMP},
            update_by      = #{updateBy,jdbcType=VARCHAR},
            update_time    = #{updateTime,jdbcType=TIMESTAMP},
            del_flag       = #{delFlag,jdbcType=CHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
