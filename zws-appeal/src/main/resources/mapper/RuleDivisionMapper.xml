<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.RuleDivisionMapper">

    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.CaseManage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="allocated_state" jdbcType="VARCHAR" property="allocatedState"/>
        <result column="case_state" jdbcType="VARCHAR" property="caseState"/>
        <result column="entrusting_party_id" jdbcType="BIGINT" property="entrustingPartyId"/>
        <result column="entrusting_party_name" jdbcType="VARCHAR" property="entrustingPartyName"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="batch_num" jdbcType="VARCHAR" property="batchNum"/>
        <result column="entrusting_case_batch_num" jdbcType="VARCHAR" property="entrustingCaseBatchNum"/>
        <result column="outsourcing_team_id" jdbcType="BIGINT" property="outsourcingTeamId"/>
        <result column="outsourcing_team_name" jdbcType="VARCHAR" property="outsourcingTeamName"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="client_sex" jdbcType="VARCHAR" property="clientSex"/>
        <result column="client_idcard" jdbcType="VARCHAR" property="clientIdcard"/>
        <result column="client_census_register" jdbcType="VARCHAR" property="clientCensusRegister"/>
        <result column="client_phone" jdbcType="VARCHAR" property="clientPhone"/>
        <result column="client_money" jdbcType="DECIMAL" property="clientMoney"/>
        <result column="client_residual_principal" jdbcType="DECIMAL" property="clientResidualPrincipal"/>
        <result column="client_overdue_start" jdbcType="TIMESTAMP" property="clientOverdueStart"/>
        <result column="account_period" jdbcType="INTEGER" property="accountPeriod"/>
        <result column="follow_up_state" jdbcType="VARCHAR" property="followUpState"/>
        <result column="follow_up_start" jdbcType="TIMESTAMP" property="followUpStart"/>
        <result column="follow_up_ast" jdbcType="TIMESTAMP" property="followUpAst"/>
        <result column="entrusting_case_date" jdbcType="TIMESTAMP" property="entrustingCaseDate"/>
        <result column="return_case_date" jdbcType="TIMESTAMP" property="returnCaseDate"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="odv_name" jdbcType="VARCHAR" property="odvName"/>
        <result column="urge_state" jdbcType="VARCHAR" property="urgeState"/>
        <result column="allocated_time" jdbcType="TIMESTAMP" property="allocatedTime"/>
        <result column="urge_power" jdbcType="VARCHAR" property="urgePower"/>
        <result column="del_flag" jdbcType="BIT" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>

        <result column="labelId" property="labelId"/>
        <result column="code" property="code"/>
        <result column="label_content" property="labelContent"/>
    </resultMap>

    <sql id="Where_Sql">
        <if test="clientId != null">and cas.client_idcard = #{clientId}</if>
        <if test="uid !=null">and cas.uid=#{uid}</if>
        <if test="settlementStatus != null">and cas.settlement_status = #{settlementStatus}</if>
        <if test="caseState != null">and cas.case_state = #{caseState}</if>
        <if test="clientOverdueStart1 != null">and cas.client_overdue_start &gt;= #{clientOverdueStart1}</if>
        <if test="clientOverdueStart2 != null">and cas.client_overdue_start &lt;= #{clientOverdueStart2}</if>
        <if test="clientMoney1 != null">and cas.client_money &gt;= #{clientMoney1}</if>
        <if test="clientMoney2 != null">and cas.client_money &lt;= #{clientMoney2}</if>
        <if test="entrustingCaseDate1 != null">and cas.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and cas.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and cas.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and cas.return_case_date &lt;= #{returnCaseDate2}</if>
        <if test="allocatedTime1 != null">and cas.allocated_time &gt;= #{allocatedTime1}</if>
        <if test="allocatedTime2 != null">and cas.allocated_time &lt;= #{allocatedTime2}</if>

        <if test="notFollowed1 != null">and datediff(now(),cas.follow_up_ast) &gt;= #{notFollowed1}</if>
        <if test="notFollowed2 != null">and datediff(now(),cas.follow_up_ast) &lt;= #{notFollowed2}</if>

        <if test="clientIdEns!=null and clientIdEns.size()>0">
            and cas.client_idcard in
            <foreach item="item" collection="clientIdEns" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="ids != null and ids.size() > 0">
            and cas.case_id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="caseIds != null and caseIds.size() > 0">
            and cas.case_id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="clientNames != null and clientNames.size() > 0">
            and (
            <foreach item="item" collection="clientNames" separator="or" open="(" close=")" index="">
                AES_DECRYPT(UNHEX(cas.client_name), #{decryptKey}) LIKE concat ('%',#{item},'%')
            </foreach>
            )
        </if>
        <if test="clientNamesEns != null and clientNamesEns.size() > 0">
            and (
            <foreach collection="clientNamesEns" item="item" separator="or">
                cib.client_name_enc like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="clientPhones != null and clientPhones.size() > 0">
            and (
            <foreach item="item" collection="clientPhones" separator="or" open="(" close=")" index="">
                AES_DECRYPT(UNHEX(cas.client_phone), #{decryptKey}) LIKE concat ('%',#{item},'%')
            </foreach>
            )
        </if>
        <if test="clientPhonesEns != null and clientPhonesEns.size() > 0">
            and (
            <foreach collection="clientPhonesEns" item="item" separator="or">
                cib.client_phone_enc like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="clientIdcards != null and clientIdcards.size() > 0">
            and (
            <foreach item="item" collection="clientIdcards" separator="or" open="(" close=")" index="">
                AES_DECRYPT(UNHEX(cas.client_idcard), #{decryptKey}) LIKE concat ('%',#{item},'%')
            </foreach>
            )
        </if>
        <if test="clientIdcardsEns != null and clientIdcardsEns.size() > 0">
            and (
            <foreach collection="clientIdcardsEns" item="item" separator="or">
                cas.client_idcard like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="clientIdcardes != null and clientIdcardes.size() > 0">
            and AES_DECRYPT(UNHEX(cas.client_idcard), #{decryptKey}) in
            <foreach item="item" collection="clientIdcardes" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="entrustingCaseBatchNums != null and entrustingCaseBatchNums.size() > 0">
            and cas.entrusting_case_batch_num in
            <foreach item="item" collection="entrustingCaseBatchNums" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="entrustingPartyIds != null and entrustingPartyIds.size() > 0">
            and cas.entrusting_party_id in
            <foreach item="item" collection="entrustingPartyIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="followUpStates != null and followUpStates.size() > 0">
            and cas.follow_up_state in
            <foreach item="item" collection="followUpStates" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="urgeStates != null and urgeStates.size() > 0">
            and cas.urge_state in
            <foreach item="item" collection="urgeStates" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="clientCensusRegisters != null and clientCensusRegisters.size() > 0">
            and (
            <foreach item="item" collection="clientCensusRegisters" separator="," open="(" close=")" index="">
                AES_DECRYPT(UNHEX(cas.client_census_register), #{decryptKey}) LIKE concat ('%',#{item},'%')
            </foreach>
            )
        </if>
        <!--<if test="clientCensusRegistersEns != null and clientCensusRegistersEns.size() > 0">
            and (
            <foreach collection="clientCensusRegistersEns" item="item" separator="or">
                cib.client_census_register_enc like concat('%', #{item}, '%')
            </foreach>
            )
        </if>-->
        <if test="odvIds != null and odvIds.size() > 0">
            and cas.odv_id in
            <foreach item="item" collection="odvIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="areas != null and areas.size() > 0">
            and (
            <foreach item="item" collection="areas" separator="or" open="(" close=")" index="">
                cas.area like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="labels != null and labels.size() > 0">
            and cas.label in
            <foreach item="item" collection="labels" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="caseStates != null and caseStates.size() > 0">
            and cas.case_state in
            <foreach item="item" collection="caseStates" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="selectCaseManageVo">
        select cas.id,
               cas.case_id,
               cas.contract_no,
               cas.allocated_state,
               cas.case_state,
               cas.entrusting_party_id,
               cas.entrusting_party_name,
               cas.product_id,
               cas.product_name,
               cas.batch_num,
               cas.entrusting_case_batch_num,
               cas.outsourcing_team_id,
               cas.outsourcing_team_name,
               cas.client_name,
               cas.client_sex,
               cas.client_idcard,
               cas.client_census_register,
               cas.client_phone,
               cas.client_money,
               cas.client_residual_principal,
               cas.client_overdue_start,
               cas.account_period,
               cas.follow_up_state,
               cas.follow_up_start,
               cas.follow_up_ast,
               cas.entrusting_case_date,
               cas.return_case_date,
               cas.area,
               cas.label,
               cas.odv_id,
               cas.odv_name,
               cas.urge_state,
               cas.allocated_time,
               cas.urge_power
        from case_manage AS cas
                 LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
    </sql>

    <select id="selectCaseManages" resultType="com.zws.appeal.domain.CaseManage">
        select cas.id,
        cas.case_id AS caseId,
        cas.client_idcard AS clientIdcard,
        cas.client_money AS clientMoney
        from case_manage AS cas
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        <include refid="Where_Sql"></include>
    </select>

    <select id="selectCaseManageMoneyCount" resultType="map">
        select sum(cas.client_money) AS money
        from case_manage AS cas
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        <include refid="Where_Sql"></include>
    </select>

    <select id="selectDistributionHistory" resultType="long">
        select
        distinct case_id

        from team_distribution_history
        where delete_logo = 0
        and employees_id = #{employeesId}
        <if test="operation !=null">
            and operation != #{operation}
        </if>
        <if test="monthFirst != null">and creationtime &gt;= #{monthFirst}</if>
        <if test="monthEnd != null">and creationtime &lt;= #{monthEnd}</if>
        <if test="ids != null and ids.size > 0">
            and case_id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectCaseClientIdcard" resultType="string">
        SELECT
        cas.client_idcard AS clientIdcard
        from case_manage AS cas
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        <include refid="Where_Sql"></include>
        GROUP BY cas.client_idcard HAVING COUNT(cas.case_id) > 1
    </select>

    <select id="selectCaseClientIdcardOne" resultType="string">
        SELECT
        cas.client_idcard AS clientIdcard
        from case_manage AS cas
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        <include refid="Where_Sql"></include>
        GROUP BY client_idcard HAVING COUNT(cas.case_id) = 1
    </select>

    <select id="selectCaseManageClientIdcard" resultMap="BaseResultMap">
        <include refid="selectCaseManageVo"/>
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1

        <include refid="Where_Sql"></include>
    </select>

    <select id="selectCaseManageClientIdcard2" resultMap="BaseResultMap">
        select cas.id,
        cas.case_id,
        cas.contract_no,
        cas.allocated_state,
        cas.case_state,
        cas.batch_num,
        cas.outsourcing_team_id,
        cas.outsourcing_team_name,
        cas.client_name,
        cas.client_idcard,
        cas.client_money,
        cas.client_residual_principal
        from case_manage AS cas
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        <include refid="Where_Sql"></include>
    </select>


    <select id="selectCaseManageClientIdcardOne" resultMap="BaseResultMap">
        <include refid="selectCaseManageVo"/>
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        <include refid="Where_Sql"></include>
    </select>

    <select id="selectCaseManageClientIdcardOne2" resultMap="BaseResultMap">
        select cas.id,
        cas.case_id,
        cas.contract_no,
        cas.allocated_state,
        cas.case_state,
        cas.batch_num,
        cas.outsourcing_team_id,
        cas.outsourcing_team_name,
        cas.client_name,
        cas.client_idcard,
        cas.client_money,
        cas.client_residual_principal
        from case_manage AS cas
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        <include refid="Where_Sql"></include>
    </select>

    <select id="selectCaseManageMoney" resultType="map">
        select sum(cas.client_money) AS amount
        from case_manage AS cas
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        <include refid="Where_Sql"></include>
    </select>

</mapper>
