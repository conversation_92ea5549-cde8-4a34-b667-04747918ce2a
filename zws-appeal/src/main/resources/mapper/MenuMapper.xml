<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.MenuMapper">

    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.Menu">
        <result column="menu_id" property="menuId"/>
        <result column="menu_code" property="menuCode"/>
        <result column="create_id" property="createId"/>
        <result column="menu_name" property="menuName"/>
        <result column="parent_code" property="parentCode"/>
        <result column="order_num" property="orderNum"/>
        <result column="is_frame" property="isFrame"/>
        <result column="is_cache" property="isCache"/>
        <result column="menu_type" property="menuType"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="RoleTreeResult" type="com.zws.appeal.domain.Role">
        <result property="createId" column="create_id"/>
        <result property="roleName" column="role_name"/>
        <result property="remark" column="remark"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="menuCheckStrictly" column="menu_check_strictly"/>
        <result property="deptCheckStrictly" column="dept_check_strictly"/>
        <result property="status" column="status"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="founder" column="founder"/>
        <result property="creationtime" column="creationtime"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <resultMap id="BaseResultMaps" type="com.zws.appeal.domain.TeamMenuTemplate">
        <result property="menuId" column="menu_id"/>
        <result property="menuCode" column="menu_code"/>
        <result property="menuName" column="menu_name" />
        <result property="parentCode" column="parent_code" />
        <result property="orderNum" column="order_num" />
        <result property="path" column="path" />
        <result property="component" column="component" />
        <result property="query" column="query" />
        <result property="isFrame" column="is_frame"/>
        <result property="isCache" column="is_cache" />
        <result property="menuType" column="menu_type" />
        <result property="visible" column="visible" />
        <result property="status" column="status" />
        <result property="perms" column="perms" />
        <result property="icon" column="icon" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark"/>
    </resultMap>

    <!--    <resultMap type="com.zws.appeal.domain.Menu" id="SysMenuResult">-->
    <!--        <id     property="menuId"         column="menu_id"        />-->
    <!--        <result property="menuName"       column="menu_name"      />-->
    <!--        <result property="parentName"     column="parent_name"    />-->
    <!--        <result property="parentId"       column="parent_id"      />-->
    <!--        <result property="orderNum"       column="order_num"      />-->
    <!--        <result property="path"           column="path"           />-->
    <!--        <result property="component"      column="component"      />-->
    <!--        <result property="query"          column="query"          />-->
    <!--        <result property="isFrame"        column="is_frame"       />-->
    <!--        <result property="isCache"        column="is_cache"       />-->
    <!--        <result property="menuType"       column="menu_type"      />-->
    <!--        <result property="visible"        column="visible"        />-->
    <!--        <result property="status"         column="status"         />-->
    <!--        <result property="perms"          column="perms"          />-->
    <!--        <result property="icon"           column="icon"           />-->
    <!--        <result property="createBy"       column="create_by"      />-->
    <!--        <result property="createTime"     column="create_time"    />-->
    <!--        <result property="updateTime"     column="update_time"    />-->
    <!--        <result property="updateBy"       column="update_by"      />-->
    <!--        <result property="remark"         column="remark"         />-->
    <!--    </resultMap>-->

    <sql id="selectMenuVo">
        select menu_id,
               menu_code,
               create_id,
               menu_name,
               parent_code,
               order_num,
               path,
               component,
               query,
               is_frame,
               is_cache,
               menu_type,
               visible,
               status,
               perms,
               icon,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from team_menu
    </sql>

    <sql id="selectMenuVoT">
        select
            menu_id,
            menu_code,
            menu_name,
            parent_code,
            order_num,
            path,
            component,
            query,
            is_frame,
            is_cache,
            menu_type,
            visible,
            status,
            perms,
            icon,
            create_by,
            create_time,
            update_by,
            update_time,
            remark
        from team_menu_template_ts
    </sql>

    <sql id="selectRoleVo">
        select id,
               create_id,
               role_name,
               remark,
               role_key,
               role_sort,
               data_scope,
               menu_check_strictly,
               dept_check_strictly,
               status,
               delete_logo,
               founder,
               creationtime,
               modifier,
               modify_time
        from team_role
    </sql>

    <select id="selectRoles" resultType="com.zws.appeal.domain.Role" resultMap="RoleTreeResult">
        <include refid="selectRoleVo"></include>
        where delete_logo=0 and id=#{id}
    </select>

    <select id="selectMenuListByRoleId" resultType="Integer">
        select m.menu_id
        from team_menu m
        left join team_role_menu rm on m.menu_id = rm.menu_id
        where rm.role_id = #{roleId}
        <if test="menuCheckStrictly">
            and m.menu_id not in (select m.parent_code from team_menu m inner join team_role_menu rm on m.menu_id =
            rm.menu_id and rm.role_id = #{roleId})
        </if>
        order by m.parent_code, m.order_num
    </select>

    <select id="selectMenuList" parameterType="com.zws.appeal.domain.Menu" resultMap="BaseResultMap">
        <include refid="selectMenuVo"/>
        <where>
            <if test="createId != null">
                AND create_id = #{createId}
            </if>
            <if test="menuName != null and menuName != ''">
                AND menu_name like concat('%', #{menuName}, '%')
            </if>
            <if test="visible != null and visible != ''">
                AND visible = #{visible}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        order by parent_code, order_num
    </select>

    <select id="selectMenuListByUserId" parameterType="com.zws.appeal.domain.Menu" resultMap="BaseResultMap">
        select distinct m.menu_code,m.menu_id, m.parent_code, m.menu_name, m.path, m.component, m.query, m.visible, m.status,
        ifnull(m.perms,'') as perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time
        from team_menu m
        left join team_role_menu rm on m.menu_id = rm.menu_id
        -- left join sys_user_role ur on rm.role_id = ur.role_id
        left join team_role ro on rm.role_id = ro.id
        left join team_employees u on ro.id = u.role_id
        where u.id = #{params.userId}
        <if test="createId != null">
            AND m.create_id = #{createId}
        </if>
        <if test="menuName != null and menuName != ''">
            AND m.menu_name like concat('%', #{menuName}, '%')
        </if>
        <if test="visible != null and visible != ''">
            AND m.visible = #{visible}
        </if>
        <if test="status != null and status != ''">
            AND m.status = #{status}
        </if>
        order by m.parent_code, m.order_num
    </select>

    <select id="selectMenu" resultMap="BaseResultMap">
        <include refid="selectMenuVo"/>
        where 1 = 1
        <if test="createId != null">
            and create_id = #{createId}
        </if>
        <if test="menuCode != null and menuCode != ''">and menu_code = #{menuCode}</if>
        <if test="menuId != null ">and menu_id = #{menuId}</if>
        <if test="menuName != null ">and menu_name = #{menuName}</if>
        <if test="status != null ">and status = #{status}</if>
        order by order_num
    </select>

    <select id="selectMenuTreeAll" resultMap="BaseResultMap">
        select distinct m.menu_id,
        m.menu_code,
        m.parent_code,
        m.menu_name,
        m.path,
        m.component,
        m.query,
        m.visible,
        m.status,
        ifnull(m.perms, '') as perms,
        m.is_frame,
        m.is_cache,
        m.menu_type,
        m.icon,
        m.order_num,
        m.create_time
        from team_menu m
        where m.menu_type in ('M', 'C')
        and m.status = 0
        <if test="createId != null">
            and create_id = #{createId}
        </if>
        order by m.parent_code, m.order_num
    </select>

    <select id="selectMenuTreeByUserId" parameterType="Long" resultMap="BaseResultMap">
        select distinct m.menu_id,
        m.menu_code,
        m.parent_code,
        m.menu_name,
        m.path,
        m.component,
        m.query,
        m.visible,
        m.status,
        ifnull(m.perms, '') as perms,
        m.is_frame,
        m.is_cache,
        m.menu_type,
        m.icon,
        m.order_num,
        m.create_time
        from team_menu m
        left join team_role_menu rm on m.menu_id = rm.menu_id
        --                  left join sys_user_role ur on rm.role_id = ur.role_id
        left join team_role ro on rm.role_id = ro.id
        left join team_employees u on ro.id = u.role_id
        where u.id = #{userId}
        and m.menu_type in ('M', 'C')
        and m.status = 0
        and ro.status = 0
        <if test="createId != null">
            and m.create_id = #{createId}
        </if>
        order by m.parent_code, m.order_num
    </select>

    <select id="hasChildByMenuId" resultType="Integer">
        select count(1)
        from team_menu
        where parent_code = #{menuId}
        <if test="createId != null">
            and create_id = #{createId}
        </if>
    </select>

    <select id="checkMenuExistRole" resultType="Integer">
        select count(1)
        from team_role_menu
        where menu_id = #{menuId}
    </select>

    <select id="checkMenuNameUnique" parameterType="com.zws.appeal.domain.Menu" resultMap="BaseResultMap">
        <include refid="selectMenuVo"/>
        where menu_name=#{menuName}
        <if test="createId != null">
            AND create_id = #{createId}
        </if>
        and parent_code = #{parentCode} limit 1
    </select>

    <insert id="insertMenu" parameterType="com.zws.appeal.domain.Menu">
        insert into team_menu(menu_name,menu_code,create_id,parent_code, order_num, path, component, query, is_frame, is_cache, menu_type,
                              visible, status, perms, icon, create_by, create_time, remark)
        values (#{menuName},#{menuCode},#{createId}, #{parentCode}, #{orderNum}, #{path}, #{component}, #{query}, #{isFrame}, #{isCache},
                #{menuType}, #{visible}, #{status}, #{perms}, #{icon}, #{createBy}, #{createTime}, #{remark})
    </insert>

    <update id="updateMenu" parameterType="com.zws.appeal.domain.Menu">
        update team_menu
        <trim prefix="SET" suffixOverrides=",">
            <if test="menuName != null">menu_name = #{menuName},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="path != null">path = #{path},</if>
            <if test="component != null">component = #{component},</if>
            <if test="query != null">query = #{query},</if>
            <if test="isFrame != null">is_frame = #{isFrame},</if>
            <if test="isCache != null">is_cache = #{isCache},</if>
            <if test="menuType != null">menu_type = #{menuType},</if>
            <if test="visible != null">visible = #{visible},</if>
            <if test="status != null">status = #{status},</if>
            <if test="perms != null">perms = #{perms},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where menu_id = #{menuId} and create_id = #{createId}
    </update>

    <delete id="deleteMenu" parameterType="int">
        delete
        from team_menu
        where menu_id = #{id}
        <if test="createId != null">
            and create_id = #{createId}
        </if>
    </delete>
    <delete id="deleteMenuTemplate">
        delete
        from team_menu
        where menu_id = #{id}
    </delete>

    <delete id="deleteWithMenuIds" parameterType="int">
        delete
        from team_menu
        where menu_id in
        <foreach collection="menuIds" item="menuId" separator="," open="(" close=")">
            #{menuId}
        </foreach>
    </delete>
    <delete id="deleteRoleMenuWithMenuIds" parameterType="int">
        delete
        from team_role_menu
        where menu_id in
        <foreach collection="menuIds" item="menuId" separator="," open="(" close=")">
            #{menuId}
        </foreach>
    </delete>

    <select id="selectMenuPermsByUserId" parameterType="Long" resultType="String">
        select distinct m.perms
        from team_menu m
                 left join team_role_menu rm on m.menu_id = rm.menu_id
                 left join team_employees ur on ur.role_id = rm.role_id
        where m.status = '0'
          and ur.delete_logo = 0
          and ur.id = #{userId}
    </select>

    <select id="selectMenuPermsPath" parameterType="String" resultType="String">
        select distinct path
        from team_menu
        where status = '0'
          and path = #{path}
    </select>
    <select id="selectCreateId" resultType="java.lang.Integer">
        SELECT id FROM team_create
    </select>
    <select id="selectMenuTemplate"  resultMap="BaseResultMaps">
<!--        <include refid="selectMenuVoT"/>-->
            select
            menu_id,
            menu_code,
            menu_name,
            parent_code,
            order_num,
            path,
            component,
            query,
            is_frame,
            is_cache,
            menu_type,
            visible,
            status,
            perms,
            icon,
            create_by,
            create_time,
            update_by,
            update_time,
            remark
        from team_menu_template_ts
        where true
        <if test="menuId != null ">and menu_id = #{menuId}</if>
        <if test="menuName != null ">and menu_name = #{menuName}</if>
        <if test="status != null ">and status = #{status}</if>
        order by order_num
    </select>
    <select id="checkTemplateMenuNameUnique" resultType="com.zws.appeal.domain.TeamMenuTemplate" resultMap="BaseResultMaps">
        <include refid="selectMenuVoT"/>
        where menu_name=#{menuName}
        and parent_code = #{parentCode} limit 1
    </select>
    <select id="selectByMenuId" resultType="com.zws.appeal.domain.Menu" resultMap="BaseResultMap">
        <include refid="selectMenuVo"/>
        where menu_id = #{menuId}
    </select>
    <select id="hasChildByMenuCode" resultType="java.lang.Integer">
        select count(1)
        from team_menu
        where parent_code = #{menuCode}
        <if test="createId != null">
            AND create_id = #{createId}
        </if>
    </select>
    <select id="selectByMenuIdTemplate"  resultMap="BaseResultMaps">
        <include refid="selectMenuVoT"/>
        where menu_id = #{menuId}
    </select>
    <select id="hasChildByMenuCodeTemplate" resultType="java.lang.Integer">
        select count(1)
        from team_menu
        where parent_code = #{menuCode}
    </select>
    <select id="selectWithMenuCode" resultMap="BaseResultMap">
        select menu_id,menu_code,parent_code
        from team_menu
        where true
        <if test="menuCode!=null and menuCode!=''">
            and menu_code = #{menuCode}
        </if>
        <if test="parentCode!=null and parentCode!=''">
            and parent_code = #{parentCode}
        </if>
    </select>
    <select id="checkTemplateMenuPermsUnique" resultType="com.zws.appeal.domain.TeamMenuTemplate" resultMap="BaseResultMaps">
        <include refid="selectMenuVoT"/>
        where perms=#{perms}
        and parent_code = #{parentCode} limit 1
    </select>
    <select id="checkMenuPermsUnique" resultType="com.zws.appeal.domain.Menu" resultMap="BaseResultMap">
        <include refid="selectMenuVo"/>
        where perms=#{perms}
        and create_id = #{createId}
        and parent_code = #{parentCode} limit 1
    </select>

    <insert id="batchInsertMenus" keyColumn="menu_id" keyProperty="menuId" parameterType="map" useGeneratedKeys="true">
        insert into team_menu(menu_name,menu_code,create_id, parent_code,parent_id, order_num, path, component, query, is_frame, is_cache, menu_type,
        visible, status, perms, icon, create_by, create_time, remark)
        values
        <foreach collection="records" separator=","  item="record" >
            (#{record.menuName},#{record.menuCode}, #{record.createId}, #{record.parentCode}, #{record.parentId}, #{record.orderNum}, #{record.path}, #{record.component}, #{record.query}, #{record.isFrame}, #{record.isCache},
            #{record.menuType}, #{record.visible}, #{record.status}, #{record.perms}, #{record.icon}, #{record.createBy}, #{record.createTime}, #{record.remark})
        </foreach>
    </insert>
    <insert id="insertMenuTemplate" parameterType="com.zws.appeal.domain.TeamMenuTemplate">
        insert into team_menu_template_ts(menu_name,menu_code,parent_code, order_num, path, component, query, is_frame, is_cache, menu_type,
                                       visible, status, perms, icon, create_by, create_time, remark)
        values (#{menuName},#{menuCode},#{parentCode}, #{orderNum}, #{path}, #{component}, #{query}, #{isFrame}, #{isCache},
                #{menuType}, #{visible}, #{status}, #{perms}, #{icon}, #{createBy}, #{createTime}, #{remark})
    </insert>
    <insert id="batchInsert" keyColumn="menu_id" keyProperty="menuId" parameterType="map" useGeneratedKeys="true">
        insert into team_menu(menu_name,menu_code,create_id, parent_code, order_num, path, component, query, is_frame, is_cache, menu_type,
        visible, status, perms, icon, create_by, create_time, remark)
        values
        <foreach collection="records" separator=","  item="record" >
            (#{record.menuName},#{record.menuCode}, #{record.createId}, #{record.parentCode}, #{record.orderNum}, #{record.path}, #{record.component}, #{record.query}, #{record.isFrame}, #{record.isCache},
            #{record.menuType}, #{record.visible}, #{record.status}, #{record.perms}, #{record.icon}, #{record.createBy}, #{record.createTime}, #{record.remark})
        </foreach>
    </insert>

    <update id="batchUpdateMenus" parameterType="com.zws.appeal.domain.Menu">
        <foreach collection="records" item="list" index="index" open="" close="" separator=";">
            update team_menu
            <trim prefix="set" suffixOverrides=",">
                <if test="list.parentId != null">parent_code = #{list.parentCode},</if>
            </trim>
            where menu_id = #{list.menuId}
        </foreach>
    </update>
    <update id="updateMenuTemplate" parameterType="com.zws.appeal.domain.TeamMenuTemplate">
        update team_menu
        <trim prefix="SET" suffixOverrides=",">
            <if test="menuName != null">menu_name = #{menuName},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="path != null">path = #{path},</if>
            <if test="component != null">component = #{component},</if>
            <if test="query != null">query = #{query},</if>
            <if test="isFrame != null">is_frame = #{isFrame},</if>
            <if test="isCache != null">is_cache = #{isCache},</if>
            <if test="menuType != null">menu_type = #{menuType},</if>
            <if test="visible != null">visible = #{visible},</if>
            <if test="status != null">status = #{status},</if>
            <if test="perms != null">perms = #{perms},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where menu_id = #{menuId}
    </update>
    <update id="updateWithMenuCode" parameterType="com.zws.appeal.domain.Menu">
        update team_menu
        <trim prefix="SET" suffixOverrides=",">
            <if test="menuName != null">menu_name = #{menuName},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="path != null">path = #{path},</if>
            <if test="component != null">component = #{component},</if>
            <if test="query != null">query = #{query},</if>
            <if test="isFrame != null">is_frame = #{isFrame},</if>
            <if test="isCache != null">is_cache = #{isCache},</if>
            <if test="menuType != null">menu_type = #{menuType},</if>
            <if test="visible != null">visible = #{visible},</if>
            <if test="status != null">status = #{status},</if>
            <if test="perms != null">perms = #{perms},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where menu_code = #{menuCode} and create_id in
        <foreach collection="createIds" item="createId" separator="," open="(" close=")">
            #{createId}
        </foreach>
    </update>
</mapper>
