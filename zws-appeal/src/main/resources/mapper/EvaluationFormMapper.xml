<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.EvaluationFormMapper">

    <select id="selectEvaluationForm" resultType="com.zws.appeal.domain.EvaluationForm">
        select eva.id AS id,
        cre.cname AS teamName,
        cre.category AS teamType,
        eva.collection_rate AS collectionRate,
        eva.reminder_quantity AS reminderQuantity,
        eva.number_seats AS numberSeats,
        eva.call_volume AS callVolume,
        eva.country_coverage AS countryCoverage,
        eva.service_quality AS serviceQuality,
        eva.service_deduct_points AS serviceDeductPoints,
        eva.service_remarks AS serviceRemarks,
        eva.information_safety AS informationSafety,
        eva.information_deduct_points AS informationDeductPoints,
        eva.information_remarks AS informationRemarks,
        eva.compliance_management AS complianceManagement,
        eva.compliance_deduct_points AS complianceDeductPoints,
        eva.compliance_remarks AS complianceRemarks,
        eva.personal_score AS personalScore,
        eva.personal_deduct_points AS personalDeductPoints,
        eva.personal_remarks AS personalRemarks,
        eva.comprehensive_score AS comprehensiveScore,
        eva.generation_date AS generationDate,
        eva.evaluation_time AS evaluationTime
        from team_evaluation_form AS eva
        LEFT JOIN team_create AS cre ON (eva.team_id = cre.id)
        where eva.del_flag = 0
        <if test="ids != null and ids.size > 0">
            and eva.id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="teamId != null">and eva.team_id = #{teamId}</if>
        <if test="generationDate1 != null">and eva.generation_date &gt;= #{generationDate1}</if>
        <if test="generationDate2 != null">and eva.generation_date &lt;= #{generationDate2}</if>
        order by eva.generation_date desc
    </select>

    <insert id="insertEvaluationForm" parameterType="com.zws.appeal.domain.EvaluationForm">
        insert into team_evaluation_form(team_id, collection_rate, reminder_quantity, number_seats, call_volume,
                                         country_coverage, service_quality, service_deduct_points, service_remarks,
                                         information_safety, information_deduct_points, information_remarks,
                                         compliance_management, compliance_deduct_points, compliance_remarks,
                                         personal_score, personal_deduct_points, personal_remarks,
                                         comprehensive_score, generation_date, evaluation_time, del_flag)
        values (#{teamId}, #{collectionRate}, #{reminderQuantity}, #{numberSeats}, #{callVolume}, #{countryCoverage},
                #{serviceQuality}, #{serviceDeductPoints}, #{serviceRemarks}, #{informationSafety},
                #{informationDeductPoints}, #{informationRemarks}, #{complianceManagement},
                #{complianceDeductPoints}, #{complianceRemarks}, #{personalScore}, #{personalDeductPoints},
                #{personalRemarks}, #{comprehensiveScore}, #{generationDate}, #{evaluationTime}, #{delFlag})
    </insert>

    <!--    <update id="updateMenu" parameterType="com.zws.appeal.domain.Menu">-->
    <!--        update team_menu-->
    <!--        <trim prefix="SET" suffixOverrides=",">-->
    <!--            <if test="menuName != null">menu_name = #{menuName},</if>-->
    <!--            <if test="parentId != null">parent_id = #{parentId},</if>-->
    <!--            <if test="orderNum != null">order_num = #{orderNum},</if>-->
    <!--            <if test="path != null">path = #{path},</if>-->
    <!--            <if test="component != null">component = #{component},</if>-->
    <!--            <if test="query != null">query = #{query},</if>-->
    <!--            <if test="isFrame != null">is_frame = #{isFrame},</if>-->
    <!--            <if test="isCache != null">is_cache = #{isCache},</if>-->
    <!--            <if test="menuType != null">menu_type = #{menuType},</if>-->
    <!--            <if test="visible != null">visible = #{visible},</if>-->
    <!--            <if test="status != null">status = #{status},</if>-->
    <!--            <if test="perms != null">perms = #{perms},</if>-->
    <!--            <if test="icon != null">icon = #{icon},</if>-->
    <!--            <if test="updateBy != null">update_by = #{updateBy},</if>-->
    <!--            <if test="updateTime != null">update_time = #{updateTime},</if>-->
    <!--        </trim>-->
    <!--        where menu_id = #{menuId}-->
    <!--    </update>-->

    <!--    <delete id="deleteMenu" parameterType="int">-->
    <!--        delete-->
    <!--        from team_menu-->
    <!--        where menu_id = #{id}-->
    <!--    </delete>-->

</mapper>
