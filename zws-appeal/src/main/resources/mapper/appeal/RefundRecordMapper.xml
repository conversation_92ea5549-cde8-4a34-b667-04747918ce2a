<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.RefundRecordMapper">
   <resultMap id="BaseResultMap" type="com.zws.appeal.domain.appeal.RefundRecord">
    <!--@mbg.generated-->
    <!--@Table team_refund_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_id" jdbcType="BIGINT" property="caseId" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="opening_institution" jdbcType="VARCHAR" property="openingInstitution" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="court_id" jdbcType="BIGINT" property="courtId" />
    <result column="executive_court" jdbcType="VARCHAR" property="executiveCourt" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, case_id,  account_name, account_number, opening_institution, serial_number,
    refund_amount, refund_time, file_url, court_id, executive_court, team_id, create_time,
    create_by, create_by_id, update_time, update_by, update_by_id, del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from team_refund_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from team_refund_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.RefundRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_refund_record (case_id,  account_name,
      account_number, opening_institution, serial_number,
      refund_amount, refund_time, file_url,
      court_id, executive_court, team_id,
      create_time, create_by, create_by_id,
      update_time, update_by, update_by_id,
      del_flag)
    values (#{caseId,jdbcType=BIGINT},  #{accountName,jdbcType=VARCHAR},
      #{accountNumber,jdbcType=VARCHAR}, #{openingInstitution,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR},
      #{refundAmount,jdbcType=DECIMAL}, #{refundTime,jdbcType=TIMESTAMP}, #{fileUrl,jdbcType=VARCHAR},
      #{courtId,jdbcType=BIGINT}, #{executiveCourt,jdbcType=VARCHAR}, #{teamId,jdbcType=BIGINT},
      #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT},
      #{delFlag,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.RefundRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_refund_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        case_id,
      </if>

      <if test="accountName != null">
        account_name,
      </if>
      <if test="accountNumber != null">
        account_number,
      </if>
      <if test="openingInstitution != null">
        opening_institution,
      </if>
      <if test="serialNumber != null">
        serial_number,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="courtId != null">
        court_id,
      </if>
      <if test="executiveCourt != null">
        executive_court,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        #{caseId,jdbcType=BIGINT},
      </if>

      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null">
        #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="openingInstitution != null">
        #{openingInstitution,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="courtId != null">
        #{courtId,jdbcType=BIGINT},
      </if>
      <if test="executiveCourt != null">
        #{executiveCourt,jdbcType=VARCHAR},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.appeal.RefundRecord">
    <!--@mbg.generated-->
    update team_refund_record
    <set>
      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>

      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="openingInstitution != null">
        opening_institution = #{openingInstitution,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="courtId != null">
        court_id = #{courtId,jdbcType=BIGINT},
      </if>
      <if test="executiveCourt != null">
        executive_court = #{executiveCourt,jdbcType=VARCHAR},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.appeal.RefundRecord">
    <!--@mbg.generated-->
    update team_refund_record
    set case_id = #{caseId,jdbcType=BIGINT},
      account_name = #{accountName,jdbcType=VARCHAR},
      account_number = #{accountNumber,jdbcType=VARCHAR},
      opening_institution = #{openingInstitution,jdbcType=VARCHAR},
      serial_number = #{serialNumber,jdbcType=VARCHAR},
      refund_amount = #{refundAmount,jdbcType=DECIMAL},
      refund_time = #{refundTime,jdbcType=TIMESTAMP},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      court_id = #{courtId,jdbcType=BIGINT},
      executive_court = #{executiveCourt,jdbcType=VARCHAR},
      team_id = #{teamId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_by_id = #{createById,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      del_flag = #{delFlag,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_refund_record
    (case_id,  account_name, account_number, opening_institution, serial_number,
      refund_amount, refund_time, file_url, court_id, executive_court, team_id, create_time,
      create_by, create_by_id, update_time, update_by, update_by_id, del_flag)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.caseId,jdbcType=BIGINT},  #{item.accountName,jdbcType=VARCHAR},
        #{item.accountNumber,jdbcType=VARCHAR}, #{item.openingInstitution,jdbcType=VARCHAR},
        #{item.serialNumber,jdbcType=VARCHAR}, #{item.refundAmount,jdbcType=DECIMAL}, #{item.refundTime,jdbcType=TIMESTAMP},
        #{item.fileUrl,jdbcType=VARCHAR}, #{item.courtId,jdbcType=BIGINT}, #{item.executiveCourt,jdbcType=VARCHAR},
        #{item.teamId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR},
        #{item.createById,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR},
        #{item.updateById,jdbcType=BIGINT}, #{item.delFlag,jdbcType=CHAR})
    </foreach>
  </insert>


    <sql id="searchValue">

        <if test="teamId != null">
            and cm.outsourcing_team_id = #{teamId}
        </if>
        <if test="caseId != null">
            and cl.id = #{caseId}
        </if>
        <if test="clientName != null and clientName != ''">
            and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>

        <if test="amount1 != null and amount2 != null">
            and cil.remaining_due >= #{amount1} and cil.remaining_due  &lt;= #{amount2}
        </if>
        <if test="executiveCourt != null and executiveCourt != ''">
            and trd.executive_court = #{executiveCourt}
        </if>
        <if test="disposeStage != null">
            and cm.dispose_stage = #{disposeStage}
        </if>
        <if test="mediatedStage != null and mediatedStage != ''">
            and cm.mediated_stage = #{mediatedStage}
        </if>
        <if test="accountName != null and accountName != ''">
            and trd.account_name = #{accountName}
        </if>

        <if test="!allQuery and ids!=null and ids.size()>0">
        and cm.case_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        </if>

    </sql>
  <select id="selectWithRefund" resultType="com.zws.appeal.pojo.appeal.RefundRecordPojo">
 SELECT
    cl.id                       as caseId,
    cl.client_name              as clientName,
    cil.remaining_due           as remainingDue,
    cl.client_id_num            as clientIdcard,
    cl.client_census_register   as clientCensusRegister,
    cl.client_phone             as phone,
    cm.dispose_stage            as disposeStage,
    trd.executive_court         as executiveCourt,
    trd.account_name            as accountName,
    trd.opening_institution     as openingInstitution,
    trd.account_number          as accountNumber,
    trd.serial_number           as serialNumber,
    trd.refund_amount           as refundAmount,
    trd.refund_time             as refundTime,
    trr.create_by               as follower,
    trr.create_time             as followUpTime,
    cm.is_freeze                as isFreeze,
    CASE WHEN cm.is_freeze>0 then tfr.start_freeze
    ELSE null
    END                         as startFreeze ,
    CASE WHEN cm.is_freeze>0 then tfr.end_freeze
    ELSE null
    END                         as endFreeze
    from
    case_manage as cm
    LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
    LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
    LEFT JOIN team_refund_record as trd on (cil.case_id = trd.case_id and trd.del_flag=0 )
    LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
        where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
    ) as trr on (trr.case_id=cl.id)
    LEFT JOIN(
        SELECT tf.case_id ,tf.create_time,tf.start_freeze,tf.end_freeze FROM
        team_freeze_record tf
        INNER JOIN ( SELECT case_id, Max(create_time) AS maxTime FROM team_freeze_record WHERE del_flag = 0 GROUP BY case_id ) AS aa ON
        ( aa.case_id = tf.case_id AND aa.maxTime = tf.create_time )
    ) as tfr on (tfr.case_id = cm.case_id)
  <where>
      cm.del_flag=0
      <include refid="searchValue"/>
    </where>

</select>

<select id="selectWithRefundMoney" resultType="java.util.Map">
    SELECT
        count(1)                        AS `size`,
        sum(cil.remaining_due)          AS money,
        sum(cil.sy_yh_principal)        AS principal,
        sum(trd.refund_amount)          AS refundAmount
    from
    case_manage as cm
    LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
    LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
    LEFT JOIN team_refund_record as trd on (cil.case_id = trd.case_id and trd.del_flag=0 )
  <where>
      cm.del_flag=0
      <include refid="searchValue"/>
    </where>

</select>


    <select id="selectCaseIds" resultType="java.lang.Long">
        SELECT
        cl.id                       as caseId
        from
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN team_refund_record as trd on (cil.case_id = trd.case_id and trd.del_flag=0 )
      <where>
          cm.del_flag=0
          <include refid="searchValue"/>
        </where>

    </select>

<update id="updateByCaseIdSelective" parameterType="com.zws.appeal.domain.appeal.RefundRecord">
    <!--@mbg.generated-->
    update team_refund_record
    <set>
      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="openingInstitution != null">
        opening_institution = #{openingInstitution,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="courtId != null">
        court_id = #{courtId,jdbcType=BIGINT},
      </if>
      <if test="executiveCourt != null">
        executive_court = #{executiveCourt,jdbcType=VARCHAR},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
    </set>
    where case_id = #{caseId,jdbcType=BIGINT}
  </update>

  <select id="selectWithCaseIds" resultType="java.lang.Long">
  select
  case_id
  from team_refund_record
  where del_flag=0
  and case_id in
   <foreach collection="list" item="item" separator="," open="(" close=")">
    #{item}
  </foreach>
</select>

</mapper>