<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.StageConfigMapper">

 <resultMap id="BaseResultMap" type="com.zws.appeal.domain.appeal.StageConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dispose_way" jdbcType="INTEGER" property="disposeWay" />
    <result column="stage_name" jdbcType="VARCHAR" property="stageName" />
    <result column="stage_two_name" jdbcType="VARCHAR" property="stageTwoName" />
    <result column="sort_num" jdbcType="INTEGER" property="sortNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dispose_way,stage_two_name, stage_name, sort_num, status, create_time, create_by, create_by_id,
    update_time, update_by, update_by_id, del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from dispose_stage_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from dispose_stage_config
    where del_flag='0'
    <if test="disposeWay!=null">
      and dispose_way=#{disposeWay}
    </if>
    <if test="stageName!=null and stageName!=''">
      and stage_name=#{stageName}
    </if>
    <if test="status!=null">
      and status=#{status}
    </if>
    <if test="sortNum!=null">
      and sort_num=#{sortNum}
    </if>
    order by sort_num
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from dispose_stage_config
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.zws.appeal.domain.appeal.StageConfig" useGeneratedKeys="true" keyColumn="id">
    insert into dispose_stage_config (id, dispose_way, stage_name,
      sort_num, status, create_time,
      create_by, create_by_id, update_time,
      update_by, update_by_id, del_flag
      )
    values (#{id,jdbcType=BIGINT}, #{disposeWay,jdbcType=INTEGER}, #{stageName,jdbcType=VARCHAR},
      #{sortNum,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT}, #{delFlag,jdbcType=CHAR}
      )
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.appeal.StageConfig">
    update dispose_stage_config
    <set>
      <if test="disposeWay != null">
        dispose_way = #{disposeWay,jdbcType=INTEGER},
      </if>
      <if test="stageName != null">
        stage_name = #{stageName,jdbcType=VARCHAR},
      </if>
      <if test="sortNum != null">
        sort_num = #{sortNum,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


    <select id="getUnregisteredCaseId" resultType="java.lang.Long">
		SELECT cm.case_id from team_create as tc
		left join case_manage as cm on (cm.outsourcing_team_id = tc.id)
		<where>
		tc.delete_logo=0 and cm.del_flag=0
		and tc.category='调诉机构'
        <if test="_parameter != null">
        and cm.outsourcing_team_id = #{teamId}
        </if>
<!--		and cm.case_id not in (select case_id from team_register_record where del_flag=0 and register_type=2)-->
		</where>
    </select>


    <select id="selectWithStage" resultType="com.zws.common.core.domain.Option">
        SELECT
        stage_name as `code`,
        stage_two_name as info
        from dispose_stage_config
        <where>
        del_flag=0
        and `status`=0
        <choose>
            <when test="_parameter != null and _parameter == 2">
                and dispose_way in (2,4)
            </when>
            <when test="_parameter != null and _parameter == 1">
                and dispose_way in (1,4)
            </when>

            <otherwise>
                and dispose_way=#{type}
            </otherwise>
        </choose>
        </where>
        ORDER BY sort_num asc
    </select>

    <select id="selectWithLawsuitStage" resultType="com.zws.common.core.domain.Option">
        SELECT
		stage_name as `code`,
		stage_two_name as info
		from dispose_stage_config WHERE del_flag=0
	    and `status`=0
        and dispose_way=#{type}
        and (stage_name='诉讼执行' or stage_name='执行领款')
        ORDER BY sort_num asc
    </select>
    <select id="selectByStageTwoName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dispose_stage_config
        where stage_two_name = #{stageTwoName}
        and del_flag=0
        order by id desc
        limit 1
    </select>


</mapper>
