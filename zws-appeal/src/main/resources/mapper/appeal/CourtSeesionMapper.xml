<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.CourtSessionMapper">

    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.appeal.CourtSession">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_id" jdbcType="BIGINT" property="caseId" />
    <result column="trial_lawyer" jdbcType="VARCHAR" property="trialLawyer" />
    <result column="undertaking_lawyer" jdbcType="VARCHAR" property="undertakingLawyer" />
    <result column="trial_time" jdbcType="DATE" property="trialTime" />
    <result column="trial_city" jdbcType="VARCHAR" property="trialCity" />
    <result column="court_id" jdbcType="BIGINT" property="courtId" />
    <result column="trial_court" jdbcType="VARCHAR" property="trialCourt" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="perk_money" jdbcType="VARCHAR" property="perkMoney" />
    <result column="trial_method" jdbcType="VARCHAR" property="trialMethod" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="trial_sum" jdbcType="VARCHAR" property="trialSum" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="del_flag" jdbcType="VARCHAR" property="delFlag" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    </resultMap>

    <sql id="Base_Column_List">
    id, case_id, trial_lawyer, undertaking_lawyer, trial_time, trial_city, court_id,
    trial_court, `location`, perk_money, trial_method, content, trial_sum, remark, create_time,
    create_by, update_time, update_by, del_flag, create_by_id, team_id
    </sql>

    <sql id="SeachValua">
        <if test="teamId != null">
            and cm.outsourcing_team_id=#{teamId}
        </if>
        <if test="clientName!=null and clientName!=''">
            and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>
        <if test="caseId != null and caseId !=''">
            and cl.id = #{caseId}
        </if>
        <if test="amount1 != null and amount1 != null">
            and cil.remaining_due >= #{amount1} and cil.remaining_due &lt;= #{amount2}
        </if>
        <if test="trialLawyer != null and trialLawyer != ''">
            and li.trial_lawyer LIKE concat ('%',#{trialLawyer},'%')
        </if>
        <if test="trialCourt != null and trialCourt != ''">
            and li.trial_court = #{trialCourt}
        </if>
        <if test="trialTime1 != null ">
            and li.trial_time &gt;=  #{trialTime1}
        </if>
        <if test="trialTime2 != null ">
            and li.trial_time &lt;= #{trialTime2}
        </if>
        <if test="amount1 != null and amount1 != null">
            and cil.remaining_due >= #{amount1} and cil.remaining_due  &lt;= #{amount2}
        </if>
        <if test="disposeStage != null">
            and cm.dispose_stage = #{disposeStage}
        </if>
        <if test="odvId != null and odvId != ''">
            and cm.odv_id = #{odvId}
        </if>

        <if test="!allQuery and caseIds!=null and caseIds.size()>0">
            and cm.case_id in
            <foreach collection="caseIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from law_infor
        where  id = #{id,jdbcType=BIGINT}
    </select>
    <select id="getSessionList" resultType="com.zws.appeal.pojo.appeal.LawInforPojo">
        SELECT
        cl.id AS caseId,
        cm.dispose_stage AS disposeStage,
        cm.case_state AS caseState,
        cl.client_name AS clientName,
        cl.client_id_num AS clientIdNum,
        cl.client_census_register AS clientCensusRegister,
        cil.remaining_due AS remainingDue,
        trr.create_by AS updateBy,
        trr.create_time AS updateTime,
        cm.client_phone AS clientPhone,
        li.trial_lawyer AS trialLawyer,
        li.undertaking_lawyer AS undertakingLawyer,
        li.trial_sum AS trialSum,
        li.trial_time AS trialTime,
        ccr.cost_amt AS costAmt,
        li.trial_court AS trialCourt,
        li.trial_method AS trialMethod,
        li.content AS content,
        cm.is_freeze as isFreeze,
        CASE WHEN cm.is_freeze>0 then tfr.start_freeze
        ELSE null
        END as startFreeze ,
        CASE WHEN cm.is_freeze>0 then tfr.end_freeze
        ELSE null
        END as endFreeze
        FROM
        case_manage AS cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan AS cil ON ( cil.case_id = cl.id AND cil.del_flag = 0 )
        LEFT JOIN  (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
        where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        LEFT JOIN law_infor AS li on (cl.id = li.case_id and li.del_flag = 0)
        LEFT JOIN (
            WITH RankedCasess AS (
            SELECT case_id, create_by, create_time, cost_amt,
            ROW_NUMBER() OVER ( PARTITION BY case_id ORDER BY create_time DESC ) AS rn
            FROM case_cost_record WHERE del_flag = 0 AND cost_type = '开庭费'
            ) SELECT case_id, create_by, create_time, cost_amt FROM RankedCasess WHERE rn = 1
        ) AS ccr ON ( ccr.case_id = cl.id )
        LEFT JOIN(
            SELECT tf.case_id ,tf.create_time,tf.start_freeze,tf.end_freeze FROM
            team_freeze_record tf
            INNER JOIN ( SELECT case_id, Max(create_time) AS maxTime FROM team_freeze_record WHERE del_flag = 0 GROUP BY case_id ) AS aa ON
            ( aa.case_id = tf.case_id AND aa.maxTime = tf.create_time )
        ) as tfr on (tfr.case_id = cm.case_id)
        <where>
            cm.del_flag = 0
            <include refid="SeachValua"/>
        </where>
    </select>
    <select id="getCaseIds" resultType="java.lang.Long"
            parameterType="com.zws.appeal.pojo.appeal.LawInforPojo">
        SELECT
            cl.id AS caseId
        FROM
        case_library AS cl
        LEFT JOIN case_info_loan AS cil ON ( cil.case_id = cl.id AND cil.del_flag = 0 )
        LEFT JOIN case_manage AS cm ON ( cm.case_id = cil.id AND cm.del_flag = 0 )
        LEFT JOIN team_register_record AS trr ON (cl.id = trr.case_id AND trr.del_flag = 0)
        LEFT JOIN law_infor AS li on (cl.id = li.case_id and li.del_flag = 0)
        LEFT JOIN case_cost_record AS ccr on (cl.id = ccr.case_id and ccr.cost_type = '开庭费')
        <where>
            <if test="teamId != null">
                and cm.outsourcing_team_id=#{teamId}
            </if>
            <if test="clientName!=null and clientName!=''">
                and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
            </if>
            <if test="caseId != null and caseId !=''">
                and cl.id = #{caseId}
            </if>
            <if test="remainingDue != null and remainingDue != ''">
                and cil.remaining_due = #{remainingDue}
            </if>
            <if test="trialLawyer != null and trialLawyer != ''">
                and li.trial_lawyer LIKE concat ('%',#{trialLawyer},'%')
            </if>
            <if test="trialCourt != null and trialCourt != ''">
                and li.trial_court = #{trialCourt}
            </if>
            <if test="trialTime1 != null ">
                and li.trial_time &gt;= #{trialTime1}
            </if>
            <if test="trialTime2 != null ">
                and li.trial_time &lt;= #{trialTime2}
            </if>
            <if test="originalStage != null and  originalStage != '' ">
                and cm.dispose_stage  = #{originalStage}
            </if>
            <if test="amount1 != null and amount1 != null">
                and cil.remaining_due >= #{amount1} and cil.remaining_due  &lt;= #{amount2}
            </if>
        </where>
    </select>
    <select id="getCaseIdsList" resultType="java.lang.Long"
            parameterType="com.zws.appeal.domain.appeal.CourtSession">
        SELECT
            cl.id AS caseId
        FROM
        case_manage AS cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan AS cil ON ( cil.case_id = cl.id AND cil.del_flag = 0 )
        LEFT JOIN team_register_record AS trr ON (cl.id = trr.case_id AND trr.del_flag = 0)
        LEFT JOIN law_infor AS li on (cl.id = li.case_id and li.del_flag = 0)
        LEFT JOIN case_cost_record AS ccr on (cl.id = ccr.case_id and ccr.cost_type = '开庭费')
        <where>
            cm.del_flag = 0
            <include refid="SeachValua"/>
        </where>
    </select>
    <select id="selecCaseId" resultType="com.zws.appeal.domain.appeal.CourtSession"
            parameterType="java.lang.Long">
        select <include refid="Base_Column_List"/>
        from law_infor
        where case_id = #{caseId}
    </select>
    <select id="selecCaseIds" resultType="java.lang.Long">
        select case_id
        from case_manage
        where del_flag = 0 and odv_id = #{useId} and dispose_stage = '预约开庭'
    </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from law_infor
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.CourtSession" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into law_infor (case_id, trial_lawyer, undertaking_lawyer,
      trial_time, trial_city, court_id,
      trial_court, `location`, perk_money,
      trial_method, content, trial_sum,
      remark, create_time, create_by,
      update_time, update_by, del_flag,
      create_by_id, team_id)
    values (#{caseId,jdbcType=BIGINT}, #{trialLawyer,jdbcType=VARCHAR}, #{undertakingLawyer,jdbcType=VARCHAR},
      #{trialTime,jdbcType=DATE}, #{trialCity,jdbcType=VARCHAR}, #{courtId,jdbcType=BIGINT},
      #{trialCourt,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, #{perkMoney,jdbcType=VARCHAR},
      #{trialMethod,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{trialSum,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR},
      #{createById,jdbcType=BIGINT}, #{teamId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.CourtSession" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into law_infor
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        case_id,
      </if>
      <if test="trialLawyer != null">
        trial_lawyer,
      </if>
      <if test="undertakingLawyer != null">
        undertaking_lawyer,
      </if>
      <if test="trialTime != null">
        trial_time,
      </if>
      <if test="trialCity != null">
        trial_city,
      </if>
      <if test="courtId != null">
        court_id,
      </if>
      <if test="trialCourt != null">
        trial_court,
      </if>
      <if test="location != null">
        `location`,
      </if>
      <if test="perkMoney != null">
        perk_money,
      </if>
      <if test="trialMethod != null">
        trial_method,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="trialSum != null">
        trial_sum,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        #{caseId,jdbcType=BIGINT},
      </if>
      <if test="trialLawyer != null">
        #{trialLawyer,jdbcType=VARCHAR},
      </if>
      <if test="undertakingLawyer != null">
        #{undertakingLawyer,jdbcType=VARCHAR},
      </if>
      <if test="trialTime != null">
        #{trialTime,jdbcType=DATE},
      </if>
      <if test="trialCity != null">
        #{trialCity,jdbcType=VARCHAR},
      </if>
      <if test="courtId != null">
        #{courtId,jdbcType=BIGINT},
      </if>
      <if test="trialCourt != null">
        #{trialCourt,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="perkMoney != null">
        #{perkMoney,jdbcType=VARCHAR},
      </if>
      <if test="trialMethod != null">
        #{trialMethod,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="trialSum != null">
        #{trialSum,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.appeal.CourtSession">
    <!--@mbg.generated-->
    update law_infor
    <set>
      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>
      <if test="trialLawyer != null">
        trial_lawyer = #{trialLawyer,jdbcType=VARCHAR},
      </if>
      <if test="undertakingLawyer != null">
        undertaking_lawyer = #{undertakingLawyer,jdbcType=VARCHAR},
      </if>
      <if test="trialTime != null">
        trial_time = #{trialTime,jdbcType=DATE},
      </if>
      <if test="trialCity != null">
        trial_city = #{trialCity,jdbcType=VARCHAR},
      </if>
      <if test="courtId != null">
        court_id = #{courtId,jdbcType=BIGINT},
      </if>
      <if test="trialCourt != null">
        trial_court = #{trialCourt,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        `location` = #{location,jdbcType=VARCHAR},
      </if>
      <if test="perkMoney != null">
        perk_money = #{perkMoney,jdbcType=VARCHAR},
      </if>
      <if test="trialMethod != null">
        trial_method = #{trialMethod,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="trialSum != null">
        trial_sum = #{trialSum,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.appeal.CourtSession">
    <!--@mbg.generated-->
    update law_infor
    set case_id = #{caseId,jdbcType=BIGINT},
      trial_lawyer = #{trialLawyer,jdbcType=VARCHAR},
      undertaking_lawyer = #{undertakingLawyer,jdbcType=VARCHAR},
      trial_time = #{trialTime,jdbcType=DATE},
      trial_city = #{trialCity,jdbcType=VARCHAR},
      court_id = #{courtId,jdbcType=BIGINT},
      trial_court = #{trialCourt,jdbcType=VARCHAR},
      `location` = #{location,jdbcType=VARCHAR},
      perk_money = #{perkMoney,jdbcType=VARCHAR},
      trial_method = #{trialMethod,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      trial_sum = #{trialSum,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=VARCHAR},
      create_by_id = #{createById,jdbcType=BIGINT},
      team_id = #{teamId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <update id="updateByCaseId" parameterType="com.zws.appeal.domain.appeal.CourtSession">
        update law_infor
        <set>
            <if test="trialLawyer != null">
                trial_lawyer = #{trialLawyer,jdbcType=VARCHAR},
            </if>
            <if test="undertakingLawyer != null">
                undertaking_lawyer = #{undertakingLawyer,jdbcType=VARCHAR},
            </if>
            <if test="trialTime != null">
                trial_time = #{trialTime,jdbcType=DATE},
            </if>
            <if test="trialCity != null">
                trial_city = #{trialCity,jdbcType=VARCHAR},
            </if>
            <if test="courtId != null">
                court_id = #{courtId,jdbcType=BIGINT},
            </if>
            <if test="trialCourt != null">
                trial_court = #{trialCourt,jdbcType=VARCHAR},
            </if>
            <if test="location != null">
                location = #{location,jdbcType=VARCHAR},
            </if>
            <if test="perkMoney != null">
                perk_money = #{perkMoney,jdbcType=VARCHAR},
            </if>
            <if test="trialMethod != null">
                trial_method = #{trialMethod,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="trialSum != null">
                trial_sum = #{trialSum,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="createById != null">
                create_by_id = #{createById,jdbcType=BIGINT},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
        </set>
        where  case_id = #{caseId,jdbcType=BIGINT}
    </update>

</mapper>
