<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.TimeManageMapper">

    <insert id="insertInfoContact" parameterType="com.zws.common.core.domain.TimeManage">
        INSERT INTO asset_time_manage(case_id, contact_information, contact_state_content, create_time,
                                      create_by, operation_type, del_flag, contact_id, liaison,origin)
        VALUES (#{caseId}, #{contactInformation}, #{contactStateContent}, #{createTime}, #{createBy}, #{operationType},
                #{delFlag}, #{contactId}, #{liaison},#{origin})
    </insert>
    <insert id="insertCaseTime">
        INSERT INTO case_time(case_id, last_contact_information, last_contact_state_content, last_create_time,
                              residual_time, del_flag)
        VALUES (#{caseId}, #{contactInformation}, #{contactStateContent}, #{createTime}, #{residueTime}, 0)
    </insert>
    <update id="updateCaseTime">
        UPDATE case_time
        SET last_contact_information   = #{contactInformation},
            last_contact_state_content = #{contactStateContent},
            last_create_time           = #{createTime},
            residual_time              = 0
        WHERE case_id = #{caseId}
    </update>
    <update id="updateCaseTimeed">
        UPDATE case_time
        SET residual_time = #{residueTime}
        WHERE case_id = #{caseId}
    </update>
    <select id="selectCaseTime" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM case_time
        WHERE case_id = #{caseId}
          AND del_flag = 0
    </select>
    <select id="selectCaseId" resultType="java.lang.Long">
        SELECT case_id
        FROM case_manage
        WHERE display_data = 0
          AND del_flag = 0
    </select>
    <select id="selectOverdueStart" resultType="com.zws.appeal.pojo.appeal.ManageDTO">
        select client_overdue_start as clientOverdueStart,
               settlement_status    as settlementStatus
        FROM case_manage
        WHERE display_data = 0
          AND del_flag = 0
        order by create_time desc limit 1
    </select>
    <select id="selectCaseTimeed" resultType="com.zws.appeal.pojo.appeal.ManageDTO">
        select case_id          as caseId,
               last_create_time as lastContactTime
        FROM case_time
        WHERE del_flag = 0
          and last_create_time is not null
    </select>
    <select id="selectCaseTimeNo" resultType="com.zws.appeal.pojo.appeal.ManageDTO">
        select case_id          as caseId,
               last_create_time as lastContactTime
        FROM case_time
        WHERE del_flag = 0
          and last_create_time is null
    </select>
    <select id="selectAssetTimeById" resultType="com.zws.appeal.pojo.appeal.ManageDTO">
        select case_id as caseId,
               contact_information as lastInformation,
               contact_state_content as lastStateContent,
               create_time as lastContactTime
        from asset_time_manage
        where del_flag = 0
          and case_id = #{caseId} and origin &lt; 3
        ORDER BY create_time desc limit 1

    </select>


    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into asset_time_manage
    (case_id, origin, contact_information, contact_state_content, create_time, create_by,
      contact_id, operation_type, liaison,del_flag)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.caseId,jdbcType=BIGINT}, #{item.origin,jdbcType=INTEGER}, #{item.contactInformation,jdbcType=VARCHAR},
        #{item.contactStateContent,jdbcType=LONGVARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.createBy,jdbcType=VARCHAR}, #{item.contactId,jdbcType=BIGINT}, #{item.operationType,jdbcType=BIT},
        #{item.liaison,jdbcType=VARCHAR},
        #{item.delFlag,jdbcType=BIT})
    </foreach>
  </insert>
</mapper>