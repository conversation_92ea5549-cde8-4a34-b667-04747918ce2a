<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.RegisterRecordMapper">
  <resultMap id="BaseResultMap" type="com.zws.appeal.domain.appeal.RegisterRecord">
    <!--@mbg.generated-->
    <!--@Table team_register_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_id" jdbcType="BIGINT" property="caseId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="register_type" jdbcType="INTEGER" property="registerType" />
    <result column="urge_state" jdbcType="VARCHAR" property="urgeState" />
    <result column="connect_content" jdbcType="LONGVARCHAR" property="connectContent" />
    <result column="promise_repayment_time" jdbcType="TIMESTAMP" property="promiseRepaymentTime" />
    <result column="promise_repayment_money" jdbcType="DECIMAL" property="promiseRepaymentMoney" />
    <result column="promise_repayment_term" jdbcType="INTEGER" property="promiseRepaymentTerm" />
    <result column="repayment_monthly" jdbcType="DECIMAL" property="repaymentMonthly" />
    <result column="first_repayment_date" jdbcType="TIMESTAMP" property="firstRepaymentDate" />
    <result column="stage_reason" jdbcType="VARCHAR" property="stageReason" />
    <result column="mediate_stage" jdbcType="VARCHAR" property="mediateStage" />
    <result column="mediate_content" jdbcType="VARCHAR" property="mediateContent" />
    <result column="save_stage" jdbcType="VARCHAR" property="saveStage" />
    <result column="outsourcing_team_name" jdbcType="VARCHAR" property="outsourcingTeamName" />
    <result column="save_content" jdbcType="VARCHAR" property="saveContent" />
    <result column="web_side" jdbcType="INTEGER" property="webSide"/>
    <result column="pursue" jdbcType="VARCHAR" property="pursue"/>
    <result column="pursue_content" jdbcType="VARCHAR" property="pursueContent"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, case_id, create_id, del_flag, create_by, create_time, update_by, update_time,
    register_type, urge_state, connect_content, promise_repayment_time, promise_repayment_money,
    promise_repayment_term, repayment_monthly, first_repayment_date, stage_reason, mediate_stage,
    mediate_content, save_stage, outsourcing_team_name, save_content,web_side,pursue,pursue_content
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from team_register_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from team_register_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.RegisterRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_register_record (case_id, create_id, del_flag,
      create_by, create_time, update_by,
      update_time, register_type, urge_state,
      connect_content, promise_repayment_time,
      promise_repayment_money, promise_repayment_term,
      repayment_monthly, first_repayment_date,
      stage_reason, mediate_stage, mediate_content,
      save_stage, outsourcing_team_name, save_content,web_side,pursue,pursue_content
      )
    values (#{caseId,jdbcType=BIGINT}, #{createId,jdbcType=INTEGER}, #{delFlag,jdbcType=CHAR},
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{registerType,jdbcType=INTEGER}, #{urgeState,jdbcType=VARCHAR},
      #{connectContent,jdbcType=LONGVARCHAR}, #{promiseRepaymentTime,jdbcType=TIMESTAMP},
      #{promiseRepaymentMoney,jdbcType=DECIMAL}, #{promiseRepaymentTerm,jdbcType=INTEGER},
      #{repaymentMonthly,jdbcType=DECIMAL}, #{firstRepaymentDate,jdbcType=TIMESTAMP},
      #{stageReason,jdbcType=VARCHAR}, #{mediateStage,jdbcType=VARCHAR}, #{mediateContent,jdbcType=VARCHAR},
      #{saveStage,jdbcType=VARCHAR}, #{outsourcingTeamName,jdbcType=VARCHAR}, #{saveContent,jdbcType=VARCHAR},
      #{webSide,jdbcType=INTEGER},#{pursue,jdbcType=VARCHAR},#{pursueContent,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.RegisterRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_register_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        case_id,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="registerType != null">
        register_type,
      </if>
      <if test="urgeState != null">
        urge_state,
      </if>
      <if test="connectContent != null">
        connect_content,
      </if>
      <if test="promiseRepaymentTime != null">
        promise_repayment_time,
      </if>
      <if test="promiseRepaymentMoney != null">
        promise_repayment_money,
      </if>
      <if test="promiseRepaymentTerm != null">
        promise_repayment_term,
      </if>
      <if test="repaymentMonthly != null">
        repayment_monthly,
      </if>
      <if test="firstRepaymentDate != null">
        first_repayment_date,
      </if>
      <if test="stageReason != null">
        stage_reason,
      </if>
      <if test="mediateStage != null">
        mediate_stage,
      </if>
      <if test="mediateContent != null">
        mediate_content,
      </if>
      <if test="saveStage != null">
        save_stage,
      </if>
      <if test="outsourcingTeamName != null">
        outsourcing_team_name,
      </if>
      <if test="saveContent != null">
        save_content,
      </if>
      <if test="webSide != null">
        web_side,
      </if>
      <if test="pursue != null">
        pursue,
      </if>
      <if test="pursueContent != null">
        pursue_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        #{caseId,jdbcType=BIGINT},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="registerType != null">
        #{registerType,jdbcType=INTEGER},
      </if>
      <if test="urgeState != null">
        #{urgeState,jdbcType=VARCHAR},
      </if>
      <if test="connectContent != null">
        #{connectContent,jdbcType=VARCHAR},
      </if>
      <if test="promiseRepaymentTime != null">
        #{promiseRepaymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="promiseRepaymentMoney != null">
        #{promiseRepaymentMoney,jdbcType=DECIMAL},
      </if>
      <if test="promiseRepaymentTerm != null">
        #{promiseRepaymentTerm,jdbcType=INTEGER},
      </if>
      <if test="repaymentMonthly != null">
        #{repaymentMonthly,jdbcType=DECIMAL},
      </if>
      <if test="firstRepaymentDate != null">
        #{firstRepaymentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stageReason != null">
        #{stageReason,jdbcType=VARCHAR},
      </if>
      <if test="mediateStage != null">
        #{mediateStage,jdbcType=VARCHAR},
      </if>
      <if test="mediateContent != null">
        #{mediateContent,jdbcType=VARCHAR},
      </if>
      <if test="saveStage != null">
        #{saveStage,jdbcType=VARCHAR},
      </if>
      <if test="outsourcingTeamName != null">
        #{outsourcingTeamName,jdbcType=VARCHAR},
      </if>
      <if test="saveContent != null">
        #{saveContent,jdbcType=VARCHAR},
      </if>
      <if test="webSide != null">
        #{webSide,jdbcType=INTEGER},
      </if>
      <if test="pursue != null">
        #{pursue,jdbcType=VARCHAR},
      </if>
      <if test="pursueContent != null">
        #{pursueContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.appeal.RegisterRecord">
    <!--@mbg.generated-->
    update team_register_record
    <set>
      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="registerType != null">
        register_type = #{registerType,jdbcType=INTEGER},
      </if>
      <if test="urgeState != null">
        urge_state = #{urgeState,jdbcType=VARCHAR},
      </if>
      <if test="connectContent != null">
        connect_content = #{connectContent,jdbcType=VARCHAR},
      </if>
      <if test="promiseRepaymentTime != null">
        promise_repayment_time = #{promiseRepaymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="promiseRepaymentMoney != null">
        promise_repayment_money = #{promiseRepaymentMoney,jdbcType=DECIMAL},
      </if>
      <if test="promiseRepaymentTerm != null">
        promise_repayment_term = #{promiseRepaymentTerm,jdbcType=INTEGER},
      </if>
      <if test="repaymentMonthly != null">
        repayment_monthly = #{repaymentMonthly,jdbcType=DECIMAL},
      </if>
      <if test="firstRepaymentDate != null">
        first_repayment_date = #{firstRepaymentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stageReason != null">
        stage_reason = #{stageReason,jdbcType=VARCHAR},
      </if>
      <if test="mediateStage != null">
        mediate_stage = #{mediateStage,jdbcType=VARCHAR},
      </if>
      <if test="mediateContent != null">
        mediate_content = #{mediateContent,jdbcType=VARCHAR},
      </if>
      <if test="saveStage != null">
        save_stage = #{saveStage,jdbcType=VARCHAR},
      </if>
      <if test="outsourcingTeamName != null">
        outsourcing_team_name = #{outsourcingTeamName,jdbcType=VARCHAR},
      </if>
      <if test="saveContent != null">
        save_content = #{saveContent,jdbcType=VARCHAR},
      </if>
      <if test="webSide != null">
        web_side = #{webSide,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.appeal.RegisterRecord">
    <!--@mbg.generated-->
    update team_register_record
    set case_id = #{caseId,jdbcType=BIGINT},
      create_id = #{createId,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=CHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      register_type = #{registerType,jdbcType=INTEGER},
      urge_state = #{urgeState,jdbcType=VARCHAR},
      connect_content = #{connectContent,jdbcType=VARCHAR},
      promise_repayment_time = #{promiseRepaymentTime,jdbcType=TIMESTAMP},
      promise_repayment_money = #{promiseRepaymentMoney,jdbcType=DECIMAL},
      promise_repayment_term = #{promiseRepaymentTerm,jdbcType=INTEGER},
      repayment_monthly = #{repaymentMonthly,jdbcType=DECIMAL},
      first_repayment_date = #{firstRepaymentDate,jdbcType=TIMESTAMP},
      stage_reason = #{stageReason,jdbcType=VARCHAR},
      mediate_stage = #{mediateStage,jdbcType=VARCHAR},
      mediate_content = #{mediateContent,jdbcType=VARCHAR},
      save_stage = #{saveStage,jdbcType=VARCHAR},
      outsourcing_team_name = #{outsourcingTeamName,jdbcType=VARCHAR},
      save_content = #{saveContent,jdbcType=VARCHAR},
      web_side = #{webSide,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update team_register_record
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="case_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.caseId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="del_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.delFlag,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateBy,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="register_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.registerType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="urge_state = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.urgeState,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="connect_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.connectContent,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="promise_repayment_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.promiseRepaymentTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="promise_repayment_money = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.promiseRepaymentMoney,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="promise_repayment_term = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.promiseRepaymentTerm,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="repayment_monthly = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.repaymentMonthly,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="first_repayment_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.firstRepaymentDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="stage_reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.stageReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="mediate_stage = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.mediateStage,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="mediate_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.mediateContent,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="save_stage = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.saveStage,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="outsourcing_team_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.outsourcingTeamName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="save_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.saveContent,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
    <update id="updateCaseDisposeStage">
        update  case_manage set
        dispose_stage =#{mediateFieldName},
        <if test="stageValue != null and stageValue != ''">mediated_stage=#{stageValue},</if>
        follow_up_ast = #{createTime}
        where case_id=#{caseId}
    </update>
  <update id="updateCaseSaveStage">
        update  team_freeze_record set save_stage=#{saveFieldName} where id=#{id}
  </update>
  <update id="updateCaseUrge">
        update  case_manage set
        mediated_stage=#{urgeState},
        <if test="stageValue != null and stageValue != ''">dispose_stage=#{stageValue},</if>
        follow_up_ast = #{createTime}
        where case_id=#{caseId}
  </update>
  <update id="updateCaseTime" parameterType="com.zws.appeal.domain.appeal.RegisterRecord">
    update  case_manage set follow_up_ast = #{createTime} where case_id=#{caseId}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_register_record
    (case_id, create_id, del_flag, create_by, create_time, update_by, update_time, register_type,
      urge_state, connect_content, promise_repayment_time, promise_repayment_money, promise_repayment_term,
      repayment_monthly, first_repayment_date, stage_reason, mediate_stage, mediate_content,
      save_stage, outsourcing_team_name, save_content,web_side)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.caseId,jdbcType=BIGINT}, #{item.createId,jdbcType=INTEGER}, #{item.delFlag,jdbcType=CHAR},
        #{item.createBy,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR},
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.registerType,jdbcType=INTEGER}, #{item.urgeState,jdbcType=VARCHAR},
        #{item.connectContent,jdbcType=VARCHAR}, #{item.promiseRepaymentTime,jdbcType=TIMESTAMP},
        #{item.promiseRepaymentMoney,jdbcType=DECIMAL}, #{item.promiseRepaymentTerm,jdbcType=INTEGER},
        #{item.repaymentMonthly,jdbcType=DECIMAL}, #{item.firstRepaymentDate,jdbcType=TIMESTAMP},
        #{item.stageReason,jdbcType=VARCHAR}, #{item.mediateStage,jdbcType=VARCHAR}, #{item.mediateContent,jdbcType=VARCHAR},
        #{item.saveStage,jdbcType=VARCHAR}, #{item.outsourcingTeamName,jdbcType=VARCHAR},
        #{item.saveContent,jdbcType=VARCHAR}, #{webSide,jdbcType=INTEGER})
    </foreach>
  </insert>

  <select id="getRecordByType" resultMap="BaseResultMap">
  select <include refid="Base_Column_List"/>
  from team_register_record
      <where>
      del_flag=0
      <if test="registerType != null">
      and register_type=#{registerType}
      </if>
      <if test="caseId != null">
      and case_id=#{caseId}
      </if>
      <if test="webSide != null">
      and web_side=#{webSide}
      </if>
      <if test="teamId !=null">
      and create_id=#{teamId}
      </if>
      <if test="createTime !=null">
      and create_time>=#{createTime}
      </if>
      </where>
     order by update_time desc
  </select>

  <select id="getRecordByTypes" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from team_register_record
    <where>
      del_flag=0
      <if test="registerType != null">
        and (register_type=#{registerType} or register_type=#{registerType1})
      </if>
      <if test="caseId != null">
        and case_id=#{caseId}
      </if>
      <if test="webSide != null">
        and web_side=#{webSide}
      </if>
      <if test="teamId !=null">
        and create_id=#{teamId}
      </if>
      <if test="createTime !=null">
        and create_time>=#{createTime}
      </if>
    </where>
    order by update_time desc
  </select>

  <select id="getStageByType" resultMap="BaseResultMap">
  select
  <include refid="Base_Column_List"/>
  from team_register_record
  where
    del_flag=0
    <if test="mediateStage != null and mediateStage != ''">
      and mediate_stage=#{mediateStage}
    </if>
    <if test="saveStage != null and saveStage != ''">
    and save_stage=#{saveStage}
    </if>
    <if test="registerType != null">
    and register_type=#{registerType}
    </if>
    <if test="caseId != null">
      and case_id = #{caseId}
    </if>

    </select>

    <select id="getCaseIds" resultType="java.lang.String">
    select
    cm.case_id
    from team_create as tc
    left join case_manage as cm on (cm.outsourcing_team_id = tc.id)
    <where>
    tc.delete_logo=0 and cm.del_flag=0 and cm.allocated_state=1
    <if test="teamId != null">
    and tc.id=#{teamId}
    </if>
    <if test="employeeId != null">
    and cm.odv_id =#{employeeId}
    </if>
    and tc.category='调诉机构'
    </where>
    </select>

    <select id="getStageList" resultType="com.zws.appeal.domain.appeal.StageConfig">
    select
    id              as id,
    dispose_way     as disposeWay,
    stage_name      as stageName,
    sort_num        as sortNum,
    status          as status,
    create_time     as createTime,
    create_by       as createBy,
    create_by_id    as createById,
    update_time     as updateTime,
    update_by       as updateBy,
    update_by_id    as updateById
    from dispose_stage_config
    where del_flag=0 and dispose_way=2
    and status=0
    order by sort_num asc
</select>

    <select id="getStageCount" resultType="java.lang.Integer">
        select  count(1)
        from case_manage
        <where>
        del_flag=0 and allocated_state=1
        and dispose_stage=#{stageName}
        <if test="employeeId != null">
        and odv_id=#{employeeId}
        </if>
        <if test="caseIdList != null and caseIdList.size() != 0">
        and case_id in
        <foreach collection="caseIdList" item="caseId" index="index" open="(" separator="," close=")">
            #{caseId}
        </foreach>
        </if>
        </where>
    </select>

  <select id="getStage" resultType="com.zws.common.core.domain.Option">
    select id         as code,
           stage_name as info
    from dispose_stage_config
    where del_flag = '0'
      and status = 0
      and dispose_way = #{stageType}
    order by sort_num asc
  </select>

  <select id="getFormParam" resultType="com.zws.appeal.pojo.appeal.StageFieldPojo">
    SELECT dsf.id               AS id,
           dsf.dispose_stage_id AS disposeStageId,
           df.dispose_way        AS disposeWay,
           df.field_name        AS fieldName,
           df.dict_id           AS dictId,
           dsf.sort_num         AS sortNum,
           dsf.status           AS status,
           dsf.remark           AS remark,
           dsf.create_time      AS createTime,
           dsf.create_by        AS createBy,
           dsf.create_by_id     AS createById,
           dsf.update_time      AS updateTime,
           dsf.update_by        AS updateBy,
           dsf.update_by_id     AS updateById,
           df.field_type        AS fieldType
    FROM dispose_stage_field AS dsf
           LEFT JOIN dispose_field AS df ON (dsf.dispose_field_id = df.id)
           LEFT JOIN dispose_stage_config as dsc on (dsc.id=dsf.dispose_stage_id)
    WHERE dsf.del_flag = '0'
      AND df.del_flag = '0'
      AND dsf.`status` = 0
      AND df.`status` = 0
      AND df.`dispose_way` = #{disposeWay}
      AND dsc.stage_two_name = #{stageTwoName}
    ORDER BY
      dsf.sort_num,
      dsf.id
  </select>

<select id="getFieldById" resultType="com.zws.system.api.domain.SysDictType">
        select
        dict_id         as dictId,
        dict_name       as dictName,
        dict_type       as dictType,
        status          as status,
        create_by       as createBy,
        create_time     as createTime,
        remark          as remark ,
        config_status   as configStatus,
        is_default      as isDefault,
        field_type      as fieldType,
        field_classify  as fieldClassify
		from sys_dict_type
		where dict_id=#{dictId}
</select>

    <select id="getSysDictDataByType" resultType="com.zws.system.api.domain.SysDictData">
    select
    dict_code       as dictCode,
    dict_sort       as dictSort,
    dict_label      as dictLabel,
    dict_value      as dictValue,
    dict_type       as dictType,
    css_class       as cssClass,
    list_class      as listClass,
    is_default      as isDefault,
    status          as status,
    create_by       as createBy,
    create_time     as createTime,
    update_by       as updateBy,
    update_time     as updateTime,
    remark          as remark
    from
    sys_dict_data
    where status=0 and dict_type=#{dictType} order by dict_sort asc
</select>

    <select id="getStageByCaseId" resultType="java.lang.String">
    select mediate_stage
    from team_register_record
    where del_flag=0 and register_type=2
    and case_id=#{caseId}
    </select>
  <select id="getStagesList" resultType="java.lang.String">
    select field_name
    from dispose_stage_field
    WHERE
        dispose_stage_id IN (
        SELECT
          dispose_stage_id
        FROM
          dispose_stage_field
        WHERE
          del_flag = 0
          AND field_name = #{saveStage}
      )
  </select>
  <select id="getCaseStage" resultType="java.lang.String">
    select save_stage
    from case_manage
    where case_id = #{caseId}
  </select>
  <select id="getMediateStage" resultType="java.lang.String">
    select dispose_stage
    from case_manage
    where case_id = #{caseId}
  </select>

</mapper>
