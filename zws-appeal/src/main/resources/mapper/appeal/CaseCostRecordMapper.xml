<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.CaseCostRecordMapper">
  <resultMap id="BaseResultMap" type="com.zws.appeal.domain.appeal.CaseCostRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_id" jdbcType="BIGINT" property="caseId" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="cost_amt" jdbcType="DECIMAL" property="costAmt" />
    <result column="cost_date" jdbcType="TIMESTAMP" property="costDate" />
    <result column="cost_type" jdbcType="VARCHAR" property="costType" />
    <result column="pay_method" jdbcType="VARCHAR" property="payMethod" />
    <result column="fee_recipient" jdbcType="VARCHAR" property="feeRecipient" />
    <result column="account_unit" jdbcType="VARCHAR" property="accountUnit" />
    <result column="receipt_url" jdbcType="VARCHAR" property="receiptUrl" />
    <result column="receipt_file_name" jdbcType="VARCHAR" property="receiptFileName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="operation_type" jdbcType="INTEGER" property="operationType" />
    <result column="entrusting_case_batch_num" jdbcType="VARCHAR" property="entrustingCaseBatchNum" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="web_side" jdbcType="INTEGER" property="webSide"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, case_id, team_id, cost_amt, cost_date, cost_type, pay_method, fee_recipient,
    account_unit, receipt_url, receipt_file_name, remark, operation_type, entrusting_case_batch_num,
    create_by, create_by_id, update_time, update_by, update_by_id, del_flag, create_time,web_side
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from case_cost_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from case_cost_record
    where del_flag='0'
    <if test="teamId!=null">
      and team_id=#{teamId}
    </if>
    <if test="caseId!=null">
      and case_id=#{caseId}
    </if>
    <if test="costType!=null and costType!=''">
      and cost_type=#{costType}
    </if>
    <if test="webSide != null and webSide != ''">
      and web_side = #{webSide}
    </if>
    <if test="createTime!=null ">
      and create_time = #{createTime}
    </if>
    order by create_time desc
  </select>
    <select id="getAllCaseIds" resultType="java.lang.Long" parameterType="java.lang.Long">
      select case_id
      from case_manage
      where del_flag = 0 and dispose_stage = '开庭缴费' and odv_id = #{usrId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from case_cost_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.zws.appeal.domain.appeal.CaseCostRecord" keyColumn="id" useGeneratedKeys="true">
    insert into case_cost_record (id, case_id, team_id,
      cost_amt, cost_date, cost_type,
      pay_method, fee_recipient, account_unit,
      receipt_url, receipt_file_name, remark,
      operation_type, entrusting_case_batch_num,
      create_by, create_by_id, update_time,
      update_by, update_by_id, del_flag,
      create_time,web_side)
    values (#{id,jdbcType=BIGINT}, #{caseId,jdbcType=BIGINT}, #{teamId,jdbcType=BIGINT},
      #{costAmt,jdbcType=DECIMAL}, #{costDate,jdbcType=TIMESTAMP}, #{costType,jdbcType=VARCHAR},
      #{payMethod,jdbcType=VARCHAR}, #{feeRecipient,jdbcType=VARCHAR}, #{accountUnit,jdbcType=VARCHAR},
      #{receiptUrl,jdbcType=VARCHAR}, #{receiptFileName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
      #{operationType,jdbcType=INTEGER}, #{entrustingCaseBatchNum,jdbcType=VARCHAR},
      #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT}, #{delFlag,jdbcType=CHAR},
      #{createTime,jdbcType=TIMESTAMP},#{webSide,jdbcType=INTEGER})
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.appeal.CaseCostRecord">
    update case_cost_record
    <set>
      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="costAmt != null">
        cost_amt = #{costAmt,jdbcType=DECIMAL},
      </if>
      <if test="costDate != null">
        cost_date = #{costDate,jdbcType=TIMESTAMP},
      </if>
      <if test="costType != null">
        cost_type = #{costType,jdbcType=VARCHAR},
      </if>
      <if test="payMethod != null">
        pay_method = #{payMethod,jdbcType=VARCHAR},
      </if>
      <if test="feeRecipient != null">
        fee_recipient = #{feeRecipient,jdbcType=VARCHAR},
      </if>
      <if test="accountUnit != null">
        account_unit = #{accountUnit,jdbcType=VARCHAR},
      </if>
      <if test="receiptUrl != null">
        receipt_url = #{receiptUrl,jdbcType=VARCHAR},
      </if>
      <if test="receiptFileName != null">
        receipt_file_name = #{receiptFileName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType,jdbcType=INTEGER},
      </if>
      <if test="entrustingCaseBatchNum != null">
        entrusting_case_batch_num = #{entrustingCaseBatchNum,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="webSide != null">
        web_side = #{webSide,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
    <update id="updateCaseStage">
      update case_manage set dispose_stage = '预约开庭'
      where case_id = #{caseId}
    </update>


</mapper>
