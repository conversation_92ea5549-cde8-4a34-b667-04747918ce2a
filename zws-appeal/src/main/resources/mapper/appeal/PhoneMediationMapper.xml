<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.PhoneMediationMapper">


 <resultMap id="ManageBaseResultMap" type="com.zws.appeal.domain.CaseManage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="phone_state" jdbcType="INTEGER" property="phoneState"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="allocated_state" jdbcType="VARCHAR" property="allocatedState"/>
        <result column="case_state" jdbcType="VARCHAR" property="caseState"/>
        <result column="entrusting_party_id" jdbcType="BIGINT" property="entrustingPartyId"/>
        <result column="entrusting_party_name" jdbcType="VARCHAR" property="entrustingPartyName"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="batch_num" jdbcType="VARCHAR" property="batchNum"/>
        <result column="entrusting_case_batch_num" jdbcType="VARCHAR" property="entrustingCaseBatchNum"/>
        <result column="outsourcing_team_id" jdbcType="BIGINT" property="outsourcingTeamId"/>
        <result column="outsourcing_team_name" jdbcType="VARCHAR" property="outsourcingTeamName"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="client_sex" jdbcType="VARCHAR" property="clientSex"/>
        <result column="client_idcard" jdbcType="VARCHAR" property="clientIdcard"/>
        <result column="client_census_register" jdbcType="VARCHAR" property="clientCensusRegister"/>
        <result column="client_phone" jdbcType="VARCHAR" property="clientPhone"/>
        <result column="client_money" jdbcType="DECIMAL" property="clientMoney"/>
        <result column="client_residual_principal" jdbcType="DECIMAL" property="clientResidualPrincipal"/>
        <result column="client_overdue_start" jdbcType="TIMESTAMP" property="clientOverdueStart"/>
        <result column="account_period" jdbcType="INTEGER" property="accountPeriod"/>
        <result column="follow_up_state" jdbcType="VARCHAR" property="followUpState"/>
        <result column="follow_up_start" jdbcType="TIMESTAMP" property="followUpStart"/>
        <result column="follow_up_ast" jdbcType="TIMESTAMP" property="followUpAst"/>
        <result column="entrusting_case_date" jdbcType="TIMESTAMP" property="entrustingCaseDate"/>
        <result column="return_case_date" jdbcType="TIMESTAMP" property="returnCaseDate"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="odv_name" jdbcType="VARCHAR" property="odvName"/>
        <result column="urge_state" jdbcType="VARCHAR" property="urgeState"/>
        <result column="allocated_time" jdbcType="TIMESTAMP" property="allocatedTime"/>
        <result column="urge_power" jdbcType="VARCHAR" property="urgePower"/>
        <result column="del_flag" jdbcType="BIT" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="client_id_type" property="clientIdType"/>
        <result column="settlement_status" property="settlementStatus"/>
        <result column="remaining_due" property="remainingDue"/>
        <result column="remaining_due" property="remainingDue"/>
        <result column="sy_yh_principal" property="syYhPrincipal"/>
        <result column="client_birthday" property="clientBirthday"/>
    </resultMap>


    <sql id="SeachVaule">
        <if test="teamId != null">
            and cm.outsourcing_team_id=#{teamId}
        </if>
        <if test="clientName!=null and clientName!=''">
            and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>
        <if test="caseId != null">
            and cl.id = #{caseId}
        </if>
        <if test="packageName != null and packageName !=''">
            and am.package_name LIKE concat ('%',#{packageName},'%')
        </if>
        <if test="mediationNum != null and mediationNum !=''">
            and cm.mediation_num LIKE concat ('%',#{mediationNum},'%')
        </if>
        <if test="remainingDue != null and remainingDue !=''">
            and cil.remaining_due = #{remainingDue}
        </if>
        <if test="mediatedStage != null and mediatedStage !=''">
            and cm.mediated_stage = #{mediatedStage}
        </if>
        <if test="amount1 != null and amount1 != ''">
            and cil.remaining_due >= #{amount1} and cil.remaining_due  &lt;= #{amount2}
        </if>
        <if test="odvId != null and odvId != ''">
            and cm.mediator_id = #{odvId}
        </if>

         <if test="!allQuery and caseIds!=null and caseIds.size()>0">
            and cm.case_id in
            <foreach collection="caseIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

         <if test=" caseStates!=null and caseStates.size()>0">
            and cm.case_mediate_state in
            <foreach collection="caseStates" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

    <update id="addLink">
    update case_manage set publicity_link = #{publicityLink}
    <where>
       <if test="caseIds!=null and caseIds.size()>0">
           and case_id in
           <foreach collection="caseIds" index="index" item="caseId" open="(" separator="," close=")">
                #{caseId}
           </foreach>
       </if>
    </where>
    </update>
    <update id="addNum">
    update case_manage set mediation_num = #{mediationNum}
    <where>
        <if test="caseIds!=null and caseIds.size()>0">
            and case_id in
            <foreach collection="caseIds" index="index" item="caseId" open="(" separator="," close=")">
                #{caseId}
            </foreach>
        </if>
    </where>

    </update>


    <select id="getPhoneMediation" resultType="com.zws.appeal.pojo.appeal.PhoneMediationPojo">
        SELECT
            cl.id AS caseId,
            am.package_name AS packageName,
            cm.case_state AS caseState,
            cl.entrusting_party_name AS entrustingPartyName,
            cm.mediated_stage AS mediatedStage,
            cl.client_name AS clientName,
            cl.client_id_num AS clientIdNum,
            cl.client_census_register AS clientCensusRegister,
            cil.remaining_due AS remainingDue,
            trr.create_by AS updateBy,
            trr.create_time AS updateTime,
            cm.mediation_num AS mediationNum,
            cm.publicity_link AS publicityLink,
            cil.overdue_start AS overdueStart,
            cil.yc_overdue_days AS ycOverdueDays,
            cm.client_phone AS clientPhone,
            cm.is_freeze as isFreeze,
            CASE WHEN cm.is_freeze>0 then tfr.start_freeze
            ELSE null
            END as startFreeze ,
            CASE WHEN cm.is_freeze>0 then tfr.end_freeze
            ELSE null
            END as endFreeze
        FROM
            case_manage as cm
            LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
            LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
            LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
            LEFT JOIN (
                WITH RankedCases AS (
                SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
                where del_flag=0 and origin = 3 )
                SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
            ) as trr on (trr.case_id=cl.id)
            LEFT JOIN(
                SELECT tf.case_id ,tf.create_time,tf.start_freeze,tf.end_freeze FROM
                team_freeze_record tf
                INNER JOIN ( SELECT case_id, Max(create_time) AS maxTime FROM team_freeze_record WHERE del_flag = 0 GROUP BY case_id ) AS aa ON
                ( aa.case_id = tf.case_id AND aa.maxTime = tf.create_time )
            ) as tfr on (tfr.case_id = cm.case_id)
        <where>
            cl.del_flag=0
            <include refid="SeachVaule"/>
        </where>
        order by  cl.id desc
    </select>
    <select id="getCaseIds" parameterType="com.zws.appeal.pojo.appeal.PhoneMediationPojo" resultType="java.lang.Long">
        SELECT
            cl.id AS caseId
        FROM
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
        LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
        where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        <where>
            cl.del_flag=0
            <include refid="SeachVaule"/>
        </where>
    </select>

    <select id="selectCaseManages" resultMap="ManageBaseResultMap">
    SELECT
        cm.id,
        cm.case_id,
        cm.contract_no,
        cm.allocated_state,
        cm.case_state,
        cm.settlement_status,
        cm.entrusting_party_id,
        cm.entrusting_party_name,
        cm.product_id,
        cm.product_name,
        cm.batch_num,
        cm.entrusting_case_batch_num,
        cm.outsourcing_team_id,
        cm.outsourcing_team_name,
        cm.client_name,
        cm.client_sex,
        cm.client_idcard,
        cm.client_census_register,
        cm.client_phone,
        cm.client_money,
        cm.client_residual_principal,
        cm.client_overdue_start,
        cm.account_period,
        cm.follow_up_state,
        cm.follow_up_start,
        cm.follow_up_ast,
        cm.entrusting_case_date,
        cm.return_case_date,
        cm.area,
        cm.odv_id,
        cm.odv_name,
        cm.urge_state,
        cm.allocated_time,
        cm.urge_power,
        cm.del_flag,
        cm.create_by,
        cm.create_time,
        cm.update_by,
        cm.update_time,
        cm.client_id_type,
        cm.uid
        FROM
            case_manage as cm
            LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
            LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
            LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
            LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
        where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        <where>
            cl.del_flag=0
            <include refid="SeachVaule"/>
        </where>

</select>


<select id="selectCaseManageMoneySize" resultType="java.util.Map">
    SELECT
    count(1)             as zongshu,
    sum(cm.client_money) as zongjine
    FROM
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cl.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
    <where>
        cm.del_flag=0
        <include refid="SeachVaule"/>
    </where>

</select>

<select id="selectCaseStateSize" resultType="java.lang.Integer">
    SELECT
    count(cm.case_id)
    FROM
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cl.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
    <where>
        cm.del_flag=0
        <include refid="SeachVaule"/>
    </where>
</select>

</mapper>