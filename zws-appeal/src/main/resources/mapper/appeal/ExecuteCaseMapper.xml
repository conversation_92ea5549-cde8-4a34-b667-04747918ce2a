<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.ExecuteCaseMapper">
   <resultMap id="BaseResultMap" type="com.zws.appeal.domain.appeal.ExecuteCase">
    <!--@mbg.generated-->
    <!--@Table team_execute_case-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_id" jdbcType="BIGINT" property="caseId" />
    <result column="court_id" jdbcType="BIGINT" property="courtId" />
    <result column="executive_court" jdbcType="VARCHAR" property="executiveCourt" />
    <result column="conclude_case_amount" jdbcType="DECIMAL" property="concludeCaseAmount" />
    <result column="executive_amount" jdbcType="DECIMAL" property="executiveAmount" />
    <result column="involved_with" jdbcType="VARCHAR" property="involvedWith" />
    <result column="involved _region" jdbcType="VARCHAR" property="involvedRegion" />
    <result column="is_missing" jdbcType="INTEGER" property="isMissing" />
    <result column="closed_reason" jdbcType="VARCHAR" property="closedReason" />
    <result column="case_reason" jdbcType="VARCHAR" property="caseReason" />
    <result column="is_file" jdbcType="INTEGER" property="isFile" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="closed_mode" jdbcType="VARCHAR" property="closedMode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id,  case_id, court_id, executive_court, conclude_case_amount, executive_amount,
    involved_with, `involved _region`, is_missing, closed_reason, case_reason, is_file,
    file_url, closed_mode, create_time, create_by, create_by_id, update_time, update_by,
    update_by_id, del_flag, team_id
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from team_execute_case
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from team_execute_case
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.ExecuteCase" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_execute_case ( case_id, court_id,
      executive_court, conclude_case_amount, executive_amount,
      involved_with, `involved _region`, is_missing,
      closed_reason, case_reason, is_file,
      file_url, closed_mode, create_time,
      create_by, create_by_id, update_time,
      update_by, update_by_id, del_flag,
      team_id)
    values ( #{caseId,jdbcType=BIGINT}, #{courtId,jdbcType=BIGINT},
      #{executiveCourt,jdbcType=VARCHAR}, #{concludeCaseAmount,jdbcType=DECIMAL}, #{executiveAmount,jdbcType=DECIMAL},
      #{involvedWith,jdbcType=VARCHAR}, #{involvedRegion,jdbcType=VARCHAR}, #{isMissing,jdbcType=INTEGER},
      #{closedReason,jdbcType=VARCHAR}, #{caseReason,jdbcType=VARCHAR}, #{isFile,jdbcType=INTEGER},
      #{fileUrl,jdbcType=VARCHAR}, #{closedMode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT}, #{delFlag,jdbcType=CHAR},
      #{teamId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.ExecuteCase" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_execute_case
    <trim prefix="(" suffix=")" suffixOverrides=",">

      <if test="caseId != null">
        case_id,
      </if>
      <if test="courtId != null">
        court_id,
      </if>
      <if test="executiveCourt != null">
        executive_court,
      </if>
      <if test="concludeCaseAmount != null">
        conclude_case_amount,
      </if>
      <if test="executiveAmount != null">
        executive_amount,
      </if>
      <if test="involvedWith != null">
        involved_with,
      </if>
      <if test="involvedRegion != null">
        `involved _region`,
      </if>
      <if test="isMissing != null">
        is_missing,
      </if>
      <if test="closedReason != null">
        closed_reason,
      </if>
      <if test="caseReason != null">
        case_reason,
      </if>
      <if test="isFile != null">
        is_file,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="closedMode != null">
        closed_mode,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">

      <if test="caseId != null">
        #{caseId,jdbcType=BIGINT},
      </if>
      <if test="courtId != null">
        #{courtId,jdbcType=BIGINT},
      </if>
      <if test="executiveCourt != null">
        #{executiveCourt,jdbcType=VARCHAR},
      </if>
      <if test="concludeCaseAmount != null">
        #{concludeCaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="executiveAmount != null">
        #{executiveAmount,jdbcType=DECIMAL},
      </if>
      <if test="involvedWith != null">
        #{involvedWith,jdbcType=VARCHAR},
      </if>
      <if test="involvedRegion != null">
        #{involvedRegion,jdbcType=VARCHAR},
      </if>
      <if test="isMissing != null">
        #{isMissing,jdbcType=INTEGER},
      </if>
      <if test="closedReason != null">
        #{closedReason,jdbcType=VARCHAR},
      </if>
      <if test="caseReason != null">
        #{caseReason,jdbcType=VARCHAR},
      </if>
      <if test="isFile != null">
        #{isFile,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="closedMode != null">
        #{closedMode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.appeal.ExecuteCase">
    <!--@mbg.generated-->
    update team_execute_case
    <set>

      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>
      <if test="courtId != null">
        court_id = #{courtId,jdbcType=BIGINT},
      </if>
      <if test="executiveCourt != null">
        executive_court = #{executiveCourt,jdbcType=VARCHAR},
      </if>
      <if test="concludeCaseAmount != null">
        conclude_case_amount = #{concludeCaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="executiveAmount != null">
        executive_amount = #{executiveAmount,jdbcType=DECIMAL},
      </if>
      <if test="involvedWith != null">
        involved_with = #{involvedWith,jdbcType=VARCHAR},
      </if>
      <if test="involvedRegion != null">
        `involved _region` = #{involvedRegion,jdbcType=VARCHAR},
      </if>
      <if test="isMissing != null">
        is_missing = #{isMissing,jdbcType=INTEGER},
      </if>
      <if test="closedReason != null">
        closed_reason = #{closedReason,jdbcType=VARCHAR},
      </if>
      <if test="caseReason != null">
        case_reason = #{caseReason,jdbcType=VARCHAR},
      </if>
      <if test="isFile != null">
        is_file = #{isFile,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="closedMode != null">
        closed_mode = #{closedMode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.appeal.ExecuteCase">
    <!--@mbg.generated-->
    update team_execute_case
    set
      case_id = #{caseId,jdbcType=BIGINT},
      court_id = #{courtId,jdbcType=BIGINT},
      executive_court = #{executiveCourt,jdbcType=VARCHAR},
      conclude_case_amount = #{concludeCaseAmount,jdbcType=DECIMAL},
      executive_amount = #{executiveAmount,jdbcType=DECIMAL},
      involved_with = #{involvedWith,jdbcType=VARCHAR},
      `involved _region` = #{involvedRegion,jdbcType=VARCHAR},
      is_missing = #{isMissing,jdbcType=INTEGER},
      closed_reason = #{closedReason,jdbcType=VARCHAR},
      case_reason = #{caseReason,jdbcType=VARCHAR},
      is_file = #{isFile,jdbcType=INTEGER},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      closed_mode = #{closedMode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_by_id = #{createById,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      del_flag = #{delFlag,jdbcType=CHAR},
      team_id = #{teamId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_execute_case
    ( case_id, court_id, executive_court, conclude_case_amount, executive_amount,
      involved_with, `involved _region`, is_missing, closed_reason, case_reason, is_file,
      file_url, closed_mode, create_time, create_by, create_by_id, update_time, update_by,
      update_by_id, del_flag, team_id)
    values
    <foreach collection="list" item="item" separator=",">
      ( #{item.caseId,jdbcType=BIGINT}, #{item.courtId,jdbcType=BIGINT},
        #{item.executiveCourt,jdbcType=VARCHAR}, #{item.concludeCaseAmount,jdbcType=DECIMAL},
        #{item.executiveAmount,jdbcType=DECIMAL}, #{item.involvedWith,jdbcType=VARCHAR},
        #{item.involvedRegion,jdbcType=VARCHAR}, #{item.isMissing,jdbcType=INTEGER}, #{item.closedReason,jdbcType=VARCHAR},
        #{item.caseReason,jdbcType=VARCHAR}, #{item.isFile,jdbcType=INTEGER}, #{item.fileUrl,jdbcType=VARCHAR},
        #{item.closedMode,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR},
        #{item.createById,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR},
        #{item.updateById,jdbcType=BIGINT}, #{item.delFlag,jdbcType=CHAR}, #{item.teamId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>


<sql id="searchValue">
        <if test="teamId != null">
            and cm.outsourcing_team_id = #{teamId}
        </if>
        <if test="caseId != null">
            and cl.id like  concat('%',#{caseId},'%')
        </if>
        <if test="clientName != null and clientName != ''">
             and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>
        <if test="closedMode != null and closedMode != ''">
            and tec.closed_mode = #{closedMode}
        </if>

        <if test="amount1 != null and amount2 != null">
            and cil.remaining_due >= #{amount1} and cil.remaining_due  &lt;= #{amount2}
        </if>
        <if test="executiveCourt != null and executiveCourt != ''">
            and tec.executive_court = #{executiveCourt}
        </if>
        <if test="startDate1 != null and startDate2 != null">
            and tec.create_time>=#{startDate1} and tec.create_time&lt;=#{startDate2}
        </if>
        <if test="involvedWith != null and involvedWith != ''">
            and tec.involved_with like concat('%',#{involvedWith},'%')
        </if>
        <if test="mediatedStage != null and mediatedStage != ''">
            and cm.mediated_stage = #{mediatedStage}
        </if>

        <if test="!allQuery and ids!=null and ids.size()>0">
        and cm.case_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        </if>

        <choose>
        <when test=" disposeStage != null and disposeStage != '' and disposeStage.equals('待领款') ">
        and (cm.dispose_stage = #{disposeStage} or cm.dispose_stage = '执行完成')
        </when>
        <otherwise>
        <if test="disposeStage != null and disposeStage != ''">
         and cm.dispose_stage = #{disposeStage}
        </if>
        </otherwise>
        </choose>

</sql>

  <select id="selectList" resultType="com.zws.appeal.pojo.appeal.ExecuteCasePojo">
    SELECT
    cl.id                       as caseId,
    cl.client_name              as clientName,
    cil.remaining_due           as remainingDue,
    cl.client_id_num            as clientIdcard,
    cl.client_census_register   as clientCensusRegister,
    cm.dispose_stage            as disposeStage,
    cm.mediated_stage           as mediatedStage,
    cl.client_phone             as Phone,
    tec.conclude_case_amount    as concludeCaseAmount,
    tec.involved_with           as involvedWith,
    tec.is_missing              as isMissing,
    tec.executive_court         as executiveCourt,
    tec.closed_reason           as closedReason,
    tec.closed_mode             as closedMode,
    tec.is_file                 as isFile,
    trr.create_by               as follower,
    trr.create_time             as followUpTime,
    cm.is_freeze                as isFreeze,
    CASE WHEN cm.is_freeze>0 then tfr.start_freeze
    ELSE null
    END                         as startFreeze ,
    CASE WHEN cm.is_freeze>0 then tfr.end_freeze
    ELSE null
    END                         as endFreeze
    from
    case_manage as cm
    LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
    LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
    LEFT JOIN team_execute_case as tec on (cil.case_id = tec.case_id and tec.del_flag=0  and tec.team_id = #{teamId})
    LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
        where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
    ) as trr on (trr.case_id=cl.id)
    LEFT JOIN(
        SELECT tf.case_id ,tf.create_time,tf.start_freeze,tf.end_freeze FROM
        team_freeze_record tf
        INNER JOIN ( SELECT case_id, Max(create_time) AS maxTime FROM team_freeze_record WHERE del_flag = 0 GROUP BY case_id ) AS aa ON
        ( aa.case_id = tf.case_id AND aa.maxTime = tf.create_time )
    ) as tfr on (tfr.case_id = cm.case_id)
  <where>
      cm.del_flag=0
      <include refid="searchValue"/>
    </where>
      order by  cl.id desc
</select>

<select id="selectWithMoney" resultType="java.util.Map">
     select
        count(1)                        AS `size`,
        sum(cil.remaining_due)          AS money,
        sum(tec.executive_amount)   AS concludeCaseAmount
    from
    case_manage as cm
    LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
    LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
    LEFT JOIN team_execute_case as tec on (cil.case_id = tec.case_id and tec.del_flag=0  and tec.team_id = #{teamId})
  <where>
      cm.del_flag=0
      <include refid="searchValue"/>
    </where>

</select>

    <select id="selectCaseIds" resultType="java.lang.Long">
    SELECT
    cl.id                       as caseId
    from
    case_manage as cm
    LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
    LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
    LEFT JOIN team_execute_case as tec on (cil.case_id = tec.case_id and tec.del_flag=0  and tec.team_id = #{teamId})
    <where>
          cm.del_flag=0
          <include refid="searchValue"/>
    </where>
    </select>

    <select id="selectWithCaeId" resultType="java.lang.Long">
    select case_id from team_execute_case
    where del_flag=0

    and case_id in
    <foreach collection="ids" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>

    </select>



<update id="updateByCaseId" parameterType="com.zws.appeal.domain.appeal.ExecuteCase">
    <!--@mbg.generated-->
    update team_execute_case
    <set>

      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>
      <if test="courtId != null">
        court_id = #{courtId,jdbcType=BIGINT},
      </if>
      <if test="executiveCourt != null">
        executive_court = #{executiveCourt,jdbcType=VARCHAR},
      </if>
      <if test="concludeCaseAmount != null">
        conclude_case_amount = #{concludeCaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="executiveAmount != null">
        executive_amount = #{executiveAmount,jdbcType=DECIMAL},
      </if>
      <if test="involvedWith != null">
        involved_with = #{involvedWith,jdbcType=VARCHAR},
      </if>
      <if test="involvedRegion != null">
        `involved _region` = #{involvedRegion,jdbcType=VARCHAR},
      </if>
      <if test="isMissing != null">
        is_missing = #{isMissing,jdbcType=INTEGER},
      </if>
      <if test="closedReason != null">
        closed_reason = #{closedReason,jdbcType=VARCHAR},
      </if>
      <if test="caseReason != null">
        case_reason = #{caseReason,jdbcType=VARCHAR},
      </if>
      <if test="isFile != null">
        is_file = #{isFile,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="closedMode != null">
        closed_mode = #{closedMode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
    </set>
    where case_id = #{caseId,jdbcType=BIGINT}
  </update>


</mapper>