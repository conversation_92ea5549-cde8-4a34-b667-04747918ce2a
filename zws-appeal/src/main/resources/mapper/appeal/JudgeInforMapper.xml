<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.JudgeInforMapper">

    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.appeal.JudgeInfor">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="caseId" column="case_id" jdbcType="BIGINT"/>
            <result property="teamId" column="team_id" jdbcType="BIGINT"/>
            <result property="caseStatus" column="case_status" jdbcType="VARCHAR"/>
            <result property="judgeLetter" column="judge_letter" jdbcType="VARCHAR"/>
            <result property="judgeResult" column="judge_result" jdbcType="VARCHAR"/>
            <result property="judgeSum" column="judge_sum" jdbcType="DECIMAL"/>
            <result property="custSatisfy" column="cust_satisfy" jdbcType="VARCHAR"/>
            <result property="courierNum" column="courier_num" jdbcType="VARCHAR"/>
            <result property="judgeTime" column="judge_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
            <result property="judgeContent" column="judge_content" jdbcType="VARCHAR"/>
            <result property="lastStatus" column="last_status" jdbcType="VARCHAR"/>
            <result property="undertakingLawyer" column="undertaking_lawyer" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,case_id,team_id,
        case_status,judge_letter,judge_result,
        judge_sum,cust_satisfy,courier_num,
        judge_time,create_by,create_time,
        update_by,update_time,del_flag,
        judge_content,last_status,undertaking_lawyer
    </sql>

    <sql id="searchValue">
        <if test="teamId != null">
            and cm.outsourcing_team_id=#{teamId}
        </if>
        <if test="clientName!=null and clientName!=''">
            and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>
        <if test="caseId != null and caseId !=''">
            and cl.id = #{caseId}
        </if>
        <if test="caseIds != null and caseIds.size()>0">
            and cl.id in
            <foreach collection="caseIds" open="(" separator="," close=")" item="caseId">
                #{caseId}
            </foreach>
        </if>
        <if test="disposeStage != null and disposeStage != ''">
            and cm.dispose_stage = #{disposeStage}
        </if>
        <if test="judgeTime1 != null ">
            and ji.judge_time &gt;= #{judgeTime1}
        </if>
        <if test="judgeTime2 != null ">
            and ji.judge_time &lt;= #{judgeTime2}
        </if>
        <if test="amount1 != null and amount1 != null">
            and cil.remaining_due >= #{amount1} and cil.remaining_due  &lt;= #{amount2}
        </if>
        <if test="trialCourt != null and trialCourt != ''">
            and li.trial_court = #{trialCourt}
        </if>
        <if test="odvId != null and odvId != ''">
            and cm.odv_id = #{odvId}
        </if>

        <if test="!allQuery and caseIds!=null and caseIds.size()>0">
            and cm.case_id in
            <foreach collection="caseIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from judge_infor
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <select id="getJudgeList" resultType="com.zws.appeal.pojo.appeal.JudgePojo"
            parameterType="com.zws.appeal.pojo.appeal.JudgePojo">
        select
        cl.id AS caseId,
        cl.client_name AS clientName,
        cl.client_id_num AS clientIdNum,
        cil.remaining_due AS remainingDue,
        cl.entrusting_party_name AS entrustingPartyName,
        cm.client_phone AS clientPhone,
        ji.case_status AS caseStatus,
        cm.dispose_stage AS disposeStage,
        ji.judge_time AS judgeTime,
        ji.judge_sum AS judgeSum,
        li.trial_court AS trialCourt,
        ji.judge_content AS judgeContent,
        trr.create_by AS updateBy,
        trr.create_time AS updateTime,
        ji.last_status AS lastStatus,
        ji.undertaking_lawyer AS undertakingLawyer,
        cm.client_phone AS clientPhone,
        cl.client_census_register   AS clientCensusRegister,
        ji.judge_result AS judgeResult,
        cm.is_freeze as isFreeze,
        CASE WHEN cm.is_freeze>0 then tfr.start_freeze
        ELSE null
        END as startFreeze ,
        CASE WHEN cm.is_freeze>0 then tfr.end_freeze
        ELSE null
        END as endFreeze
        FROM
        case_manage as cm
            LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
            LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
            LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
            LEFT JOIN judge_infor as ji on (cm.case_id = ji.case_id and ji.del_flag = 0)
            LEFT JOIN law_infor as li on (cm.case_id = li.case_id and li.del_flag = 0)
            LEFT JOIN  (
                WITH RankedCases AS (
                SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
                where del_flag=0 and origin = 3 )
                SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
            ) as trr on (trr.case_id=cl.id)
            LEFT JOIN(
                SELECT tf.case_id ,tf.create_time,tf.start_freeze,tf.end_freeze FROM
                team_freeze_record tf
                INNER JOIN ( SELECT case_id, Max(create_time) AS maxTime FROM team_freeze_record WHERE del_flag = 0 GROUP BY case_id ) AS aa ON
                ( aa.case_id = tf.case_id AND aa.maxTime = tf.create_time )
            ) as tfr on (tfr.case_id = cm.case_id)
        <where>
            cl.del_flag=0
            <include refid="searchValue"/>
        </where>
    </select>
    <select id="getAllList" resultType="java.lang.Long">
        select
            cl.id AS caseId
        FROM
            case_manage as cm
                LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
                LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
                LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
                LEFT JOIN judge_infor as ji on (cm.case_id = ji.case_id and ji.del_flag = 0)
                LEFT JOIN law_infor as li on (cm.case_id = li.case_id and li.del_flag = 0)
        <where>
            cl.del_flag=0
            <include refid="searchValue"/>
        </where>
    </select>
    <select id="getCaseIds" resultType="java.lang.Long" parameterType="com.zws.appeal.pojo.appeal.JudgePojo">
        select
        cl.id AS caseId
        FROM
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
        LEFT JOIN judge_infor as ji on (cm.case_id = ji.case_id and ji.del_flag = 0)
        LEFT JOIN law_infor as li on (cm.case_id = li.case_id and li.del_flag = 0)
        <where>
            cl.del_flag=0
            <include refid="searchValue"/>
        </where>
    </select>
    <select id="selectCaseIds" resultType="java.lang.Long">
        select
        cl.id AS caseId
        FROM
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
        LEFT JOIN judge_infor as ji on (cm.case_id = ji.case_id and ji.del_flag = 0)
        LEFT JOIN law_infor as li on (cm.case_id = li.case_id and li.del_flag = 0)
        <where>
            cl.del_flag=0
            <include refid="searchValue"/>
        </where>
    </select>
    <select id="selectCaseId" resultType="com.zws.appeal.domain.appeal.JudgeInfor"
            parameterType="java.lang.Long">
        select <include refid="Base_Column_List"/>
        from judge_infor
        where case_id = #{caseId}
    </select>

    <select id="getCaseIdsStage" resultType="java.lang.Long">
        select case_id
        from case_manage
        where del_flag = 0 and odv_id = #{useId} and dispose_stage = '审理判决'
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from judge_infor
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.JudgeInfor" useGeneratedKeys="true">
        insert into judge_infor
        ( id,case_id,team_id
        ,case_status,judge_letter,judge_result
        ,judge_sum,cust_satisfy,courier_num
        ,judge_time,create_by,create_time
        ,update_by,update_time,del_flag
        ,judge_content,last_status,undertaking_lawyer
        )
        values (#{id,jdbcType=BIGINT},#{caseId,jdbcType=BIGINT},#{teamId,jdbcType=BIGINT}
        ,#{caseStatus,jdbcType=VARCHAR},#{judgeLetter,jdbcType=VARCHAR},#{judgeResult,jdbcType=VARCHAR}
        ,#{judgeSum,jdbcType=DECIMAL},#{custSatisfy,jdbcType=VARCHAR},#{courierNum,jdbcType=VARCHAR}
        ,#{judgeTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}
        ,#{updateBy,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP},#{delFlag,jdbcType=VARCHAR}
        ,#{judgeContent,jdbcType=VARCHAR},#{lastStatus,jdbcType=VARCHAR},#{undertakingLawyer,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.JudgeInfor" useGeneratedKeys="true">
        insert into judge_infor
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="caseId != null">case_id,</if>
                <if test="teamId != null">team_id,</if>
                <if test="caseStatus != null">case_status,</if>
                <if test="judgeLetter != null">judge_letter,</if>
                <if test="judgeResult != null">judge_result,</if>
                <if test="judgeSum != null">judge_sum,</if>
                <if test="custSatisfy != null">cust_satisfy,</if>
                <if test="courierNum != null">courier_num,</if>
                <if test="judgeTime != null">judge_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="delFlag != null">del_flag,</if>
                <if test="judgeContent != null">judge_content,</if>
                <if test="lastStatus != null">last_status,</if>
                <if test="undertakingLawyer != null">undertaking_lawyer,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="caseId != null">#{caseId,jdbcType=BIGINT},</if>
                <if test="teamId != null">#{teamId,jdbcType=BIGINT},</if>
                <if test="caseStatus != null">#{caseStatus,jdbcType=VARCHAR},</if>
                <if test="judgeLetter != null">#{judgeLetter,jdbcType=VARCHAR},</if>
                <if test="judgeResult != null">#{judgeResult,jdbcType=VARCHAR},</if>
                <if test="judgeSum != null">#{judgeSum,jdbcType=DECIMAL},</if>
                <if test="custSatisfy != null">#{custSatisfy,jdbcType=VARCHAR},</if>
                <if test="courierNum != null">#{courierNum,jdbcType=VARCHAR},</if>
                <if test="judgeTime != null">#{judgeTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="delFlag != null">#{delFlag,jdbcType=VARCHAR},</if>
                <if test="judgeContent != null">#{judgeContent,jdbcType=VARCHAR},</if>
                <if test="lastStatus != null">#{lastStatus,jdbcType=VARCHAR},</if>
                <if test="undertakingLawyer != null">#{undertakingLawyer,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.appeal.JudgeInfor">
        update judge_infor
        <set>
                <if test="caseId != null">
                    case_id = #{caseId,jdbcType=BIGINT},
                </if>
                <if test="teamId != null">
                    team_id = #{teamId,jdbcType=BIGINT},
                </if>
                <if test="caseStatus != null">
                    case_status = #{caseStatus,jdbcType=VARCHAR},
                </if>
                <if test="judgeLetter != null">
                    judge_letter = #{judgeLetter,jdbcType=VARCHAR},
                </if>
                <if test="judgeResult != null">
                    judge_result = #{judgeResult,jdbcType=VARCHAR},
                </if>
                <if test="judgeSum != null">
                    judge_sum = #{judgeSum,jdbcType=DECIMAL},
                </if>
                <if test="custSatisfy != null">
                    cust_satisfy = #{custSatisfy,jdbcType=VARCHAR},
                </if>
                <if test="courierNum != null">
                    courier_num = #{courierNum,jdbcType=VARCHAR},
                </if>
                <if test="judgeTime != null">
                    judge_time = #{judgeTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="delFlag != null">
                    del_flag = #{delFlag,jdbcType=VARCHAR},
                </if>
                <if test="judgeContent != null">
                    judge_content = #{judgeContent,jdbcType=VARCHAR},
                </if>
                <if test="lastStatus != null">
                    last_status = #{lastStatus,jdbcType=VARCHAR},
                </if>
                <if test="undertakingLawyer != null">
                    undertaking_lawyer = #{undertakingLawyer,jdbcType=VARCHAR},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.appeal.JudgeInfor">
        update judge_infor
        set 
            case_id =  #{caseId,jdbcType=BIGINT},
            team_id =  #{teamId,jdbcType=BIGINT},
            case_status =  #{caseStatus,jdbcType=VARCHAR},
            judge_letter =  #{judgeLetter,jdbcType=VARCHAR},
            judge_result =  #{judgeResult,jdbcType=VARCHAR},
            judge_sum =  #{judgeSum,jdbcType=DECIMAL},
            cust_satisfy =  #{custSatisfy,jdbcType=VARCHAR},
            courier_num =  #{courierNum,jdbcType=VARCHAR},
            judge_time =  #{judgeTime,jdbcType=TIMESTAMP},
            create_by =  #{createBy,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_by =  #{updateBy,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            del_flag =  #{delFlag,jdbcType=VARCHAR},
            judge_content =  #{judgeContent,jdbcType=VARCHAR},
            last_status =  #{lastStatus,jdbcType=VARCHAR},
            undertaking_lawyer =  #{undertakingLawyer,jdbcType=VARCHAR}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByCaseId" parameterType="com.zws.appeal.domain.appeal.JudgeInfor">
        update judge_infor
        <set>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
            <if test="caseStatus != null">
                case_status = #{caseStatus,jdbcType=VARCHAR},
            </if>
            <if test="judgeLetter != null">
                judge_letter = #{judgeLetter,jdbcType=VARCHAR},
            </if>
            <if test="judgeResult != null">
                judge_result = #{judgeResult,jdbcType=VARCHAR},
            </if>
            <if test="judgeSum != null">
                judge_sum = #{judgeSum,jdbcType=DECIMAL},
            </if>
            <if test="custSatisfy != null">
                cust_satisfy = #{custSatisfy,jdbcType=VARCHAR},
            </if>
            <if test="courierNum != null">
                courier_num = #{courierNum,jdbcType=VARCHAR},
            </if>
            <if test="judgeTime != null">
                judge_time = #{judgeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="judgeContent != null">
                judge_content = #{judgeContent,jdbcType=VARCHAR},
            </if>
            <if test="lastStatus != null">
                last_status = #{lastStatus,jdbcType=VARCHAR},
            </if>
            <if test="undertakingLawyer != null">
                undertaking_lawyer = #{undertakingLawyer,jdbcType=VARCHAR},
            </if>
        </set>
        where   case_id = #{caseId,jdbcType=BIGINT}
    </update>


</mapper>
