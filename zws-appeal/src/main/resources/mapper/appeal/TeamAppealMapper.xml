<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.TeamAppealMapper">

    <resultMap id="DeptTreeResult" type="com.zws.appeal.domain.Dept">
        <result property="id" column="id"/>
        <result property="createId" column="create_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="status" column="status"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="founder" column="founder"/>
        <result property="creationtime" column="creationtime"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="selectDeptVo">
        select id,
               create_id,
               parent_id,
               ancestors,
               dept_name,
               order_num,
               status,
               delete_logo,
               founder,
               creationtime,
               modifier,
               modify_time
        from team_dept
    </sql>

    <sql id="searchValuees">
        <if test="smallStageList != null and smallStageList.size() > 0">
            and cm.dispose_stage in
            <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="userId != null">and emp.id = #{userId}</if>
        <if test="teamId != null">
            and cm.outsourcing_team_id=#{teamId}
        </if>
        <if test="clientName!=null and clientName!=''">
            and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>
        <if test="caseId != null and caseId !=''">
            and cl.id = #{caseId}
        </if>
        <if test="caseIds != null and caseIds.size() > 0">
            and cl.id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="disposeStage != null and disposeStage != ''">
            and cm.dispose_stage = #{disposeStage}
        </if>
        <if test="judgeTime1 != null ">
            and ji.judge_time &gt;= #{judgeTime1}
        </if>
        <if test="judgeTime2 != null ">
            and ji.judge_time &lt;= #{judgeTime2}
        </if>
        <if test="remainingDue != null and remainingDue != ''">
            and cil.remaining_due = #{remainingDue}
        </if>
        <if test="trialCourt != null and trialCourt != ''">
            and li.trial_court = #{trialCourt}
        </if>
    </sql>

    <sql id="searchValue">
        <if test="smallStageList != null and smallStageList.size() > 0">
            and cm.dispose_stage in
            <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="isDishonest != null">and cil.yc_is_dishonest = #{isDishonest}</if>
        <if test="isLimitConsumption != null">and cil.yc_is_limit_consumption = #{isLimitConsumption}</if>
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="userId != null">and emp.id = #{userId}</if>
        <if test="teamId != null">
            and cm.outsourcing_team_id=#{teamId}
        </if>
        <if test="disposeStage != null and disposeStage != ''">
            and  cm.dispose_stage = #{disposeStage}
        </if>
        <if test="packageName != null and packageName != ''">
            and am.package_name like concat('%', #{packageName}, '%')
        </if>
        <if test="caseId != null">
            and cl.id like concat('%', #{caseId}, '%')
        </if>
        <if test="caseIds != null and caseIds.size() > 0">
            and cl.id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>
        <if test="filingNumber != null and filingNumber != ''">
            and tfc.filing_number like concat('%', #{filingNumber}, '%')
        </if>
        <if test="court != null and court != ''">
            and tfc.court = #{court}
        </if>
        <if test="amount1 != null and amount1 != null">
            and cil.remaining_due >= #{amount1}
        </if>
        <if test="amount2 != null and amount2 != null">
            and cil.remaining_due  &lt;= #{amount2}
        </if>
        <if test="isFreeze != null">
            and cm.is_freeze = #{isFreeze}
        </if>
        <if test="odvId != null">
            and cm.odv_id = #{odvId}
        </if>

        <if test="!allQuery and ids!=null and ids.size()>0">
            and cm.case_id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

    <sql id="searchValues">
        <if test="deptIds != null and deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="userId != null">and emp.id = #{userId}</if>
        <if test="teamId != null">
            and cm.outsourcing_team_id = #{teamId}
        </if>
        <if test="caseId != null">
            and cl.id like  concat('%',#{caseId},'%')
        </if>
        <if test="caseIds != null and caseIds.size() > 0">
            and cl.id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="clientName != null and clientName != ''">
            and cl.client_name like  concat('%',#{clientName},'%')
        </if>
        <if test="court != null and court != ''">
            and tfr.court=#{court}
        </if>
        <if test="applyTime1 != null and applyTime2 != null">
            and tfr.create_time>=#{applyTime1} and tfr.create_time&lt;=#{applyTime2}
        </if>
        <if test="applyAmount1 != null and applyAmount2 != null">
            and tfr.freeze_amount >=#{applyAmount1} and tfr.freeze_amount &lt;=#{applyAmount2}
        </if>
        <if test="actualAmount1 != null and actualAmount2 != null">
            and tfr.actual_freeze_amount >=#{actualAmount1} and tfr.actual_freeze_amount &lt;=#{actualAmount2}
        </if>
    </sql>

    <select id="selectDept" resultType="com.zws.appeal.domain.Dept">
        select dep.id,
               dep.create_id   AS createId,
               dep.parent_id   AS parentId,
               dep.ancestors,
               dep.dept_name   AS deptName,
               dep.order_num   AS orderNum,
               dep.status,
               dep.delete_logo AS deleteLogo,
               dep.founder,
               dep.creationtime,
               dep.modifier,
               dep.modify_time AS modifyTime,
               COUNT(cm.id)    as caseNum
        from team_dept as dep
                 LEFT JOIN team_employees emp on (dep.id = emp.department_id and emp.delete_logo = 0)
                 LEFT JOIN case_manage cm on (cm.odv_id = emp.id and cm.del_flag = 0)
        where dep.delete_logo = 0
          and dep.create_id = #{createId}
        GROUP BY dep.id
        ORDER BY caseNum desc
    </select>

    <select id="selectDeptByDelivery" resultType="com.zws.appeal.domain.Dept">
        select dep.id,
               dep.create_id   AS createId,
               dep.parent_id   AS parentId,
               dep.ancestors,
               dep.dept_name   AS deptName,
               dep.order_num   AS orderNum,
               dep.status,
               dep.delete_logo AS deleteLogo,
               dep.founder,
               dep.creationtime,
               dep.modifier,
               dep.modify_time AS modifyTime,
               COUNT(cm.id)    as caseNum
        from team_dept as dep
                 LEFT JOIN team_employees emp on (dep.id = emp.department_id and emp.delete_logo = 0)
                 LEFT JOIN logistics_info cm on (cm.odv_id = emp.id and cm.del_flag = 0)
                 LEFT JOIN team_create tc on (dep.create_id = tc.id)
        where dep.delete_logo = 0 and (tc.team_level_type = '调执类' OR tc.team_level_type = '调诉类')
        <if test="createId!=null and createId!=''">
            and dep.create_id = #{createId}
        </if>
        <if test="deptList!=null">
            and emp.department_id in
            <foreach item="item" collection="deptList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        GROUP BY dep.id
        ORDER BY caseNum desc
    </select>
    <select id="selectEmployeesParentId" resultType="com.zws.appeal.domain.Employees">
        select emp.id                   as id,
               emp.create_id            as createId,
               emp.department_id        as departmentId,
               emp.role_id              as roleId,
               emp.employee_name        as employeeName,
               emp.departments          as departments,
               emp.login_account        as loginAccount,
               emp.password             as password,
               emp.phone_number         as phoneNumber,
               emp.the_role             as theRole,
               emp.account_status       as accountStatus,
               emp.working_state        as workingState,
               emp.founder              as founder,
               emp.creationtime         as creationtime,
               emp.modifier             as modifier,
               emp.modify_time          as modifyTime,
               emp.employees_working    as employeesWorking,
               emp.login_date           as loginDate,
               emp.sip_password         as sipPassword,
               emp.update_password_time as updatePasswordTime,
               emp.department_head      as departmentHead,
               COUNT(cm.id) as caseNum
        from team_employees as emp
                 LEFT JOIN case_manage cm on (cm.odv_id = emp.id and cm.del_flag = 0)
        where emp.delete_logo = 0
          and emp.department_id = #{parentId}
          and emp.create_id = #{createId}
        GROUP BY emp.id
        ORDER BY caseNum desc
    </select>

    <select id="selectEmployeesByDelivery" resultType="com.zws.appeal.domain.Employees">
        select emp.id                   as id,
               emp.create_id            as createId,
               emp.department_id        as departmentId,
               emp.role_id              as roleId,
               emp.employee_name        as employeeName,
               emp.departments          as departments,
               emp.login_account        as loginAccount,
               emp.password             as password,
               emp.phone_number         as phoneNumber,
               emp.the_role             as theRole,
               emp.account_status       as accountStatus,
               emp.working_state        as workingState,
               emp.founder              as founder,
               emp.creationtime         as creationtime,
               emp.modifier             as modifier,
               emp.modify_time          as modifyTime,
               emp.employees_working    as employeesWorking,
               emp.login_date           as loginDate,
               emp.sip_password         as sipPassword,
               emp.update_password_time as updatePasswordTime,
               emp.department_head      as departmentHead,
               COUNT(cm.id) as caseNum
        from team_employees as emp
                 LEFT JOIN logistics_info cm on (cm.odv_id = emp.id and cm.del_flag = 0)
        where emp.delete_logo = 0
          and emp.department_id = #{parentId}
        <if test="createId!=null and createId!=''">
            and emp.create_id = #{createId}
        </if>
        GROUP BY emp.id
        ORDER BY caseNum desc
    </select>
    <select id="selectDeptParentId" resultType="com.zws.appeal.domain.Dept" resultMap="DeptTreeResult">
        <include refid="selectDeptVo"/>
        where delete_logo=0 and parent_id=#{parentId}
    </select>
    <select id="selectEmployeesId" resultType="com.zws.appeal.domain.Employees">
        select id,
               create_id     AS createId,
               department_id AS departmentId,
               departments
        from team_employees
        where delete_logo = 0
          and id = #{userId}
    </select>
    <select id="getPhoneMediation" resultType="com.zws.appeal.pojo.appeal.PhoneMediationPojo">
        SELECT
        cl.id AS caseId,
        am.package_name AS packageName,
        cl.entrusting_party_name AS entrustingPartyName,
        cm.mediated_stage AS mediatedStage,
        cl.client_name AS clientName,
        cl.client_id_num AS clientIdNum,
        cl.client_census_register AS clientCensusRegister,
        cil.remaining_due AS remainingDue,
        trr.create_by AS updateBy,
        trr.create_time AS updateTime,
        cm.mediation_num AS mediationNum,
        cm.publicity_link AS publicityLink,
        cil.overdue_start AS overdueStart,
        cil.yc_overdue_days AS ycOverdueDays,
        cm.client_phone AS clientPhone,
        cm.is_freeze as isFreeze,
        CASE WHEN cm.is_freeze>0 then tfr.start_freeze
        ELSE null
        END as startFreeze ,
        CASE WHEN cm.is_freeze>0 then tfr.end_freeze
        ELSE null
        END as endFreeze
        FROM
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
        LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
        where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        LEFT JOIN(
            SELECT tf.case_id ,tf.create_time,tf.start_freeze,tf.end_freeze FROM
            team_freeze_record tf
            INNER JOIN ( SELECT case_id, Max(create_time) AS maxTime FROM team_freeze_record WHERE del_flag = 0 GROUP BY case_id ) AS aa ON
            ( aa.case_id = tf.case_id AND aa.maxTime = tf.create_time )
        ) as tfr on (tfr.case_id = cm.case_id)
        LEFT JOIN team_employees AS emp ON (cm.mediator_id = emp.id)
        <where>
            cm.mediated_stage is not null and cm.del_flag = 0
            <if test="smallStageList != null and smallStageList.size() > 0">
                and cm.mediated_stage in
                <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="teamId != null">
                and emp.create_id=#{teamId}
            </if>
            <if test="teamId != null">
                and cm.outsourcing_team_id=#{teamId}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and emp.department_id in
                <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="caseIds != null and caseIds.size() > 0">
                and cl.id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">and cm.mediator_id = #{userId}</if>
            <if test="mediatedStage != null and mediatedStage != ''">
                and  cm.mediated_stage = #{mediatedStage}
            </if>
            <if test="clientName!=null and clientName!=''">
                and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
            </if>
            <if test="caseId != null">
                and cl.id = #{caseId}
            </if>
            <if test="packageName != null and packageName !=''">
                and am.package_name LIKE concat ('%',#{packageName},'%')
            </if>
            <if test="mediationNum != null and mediationNum !=''">
                and cm.mediation_num LIKE concat ('%',#{mediationNum},'%')
            </if>
            <if test="remainingDue != null and remainingDue !=''">
                and cil.remaining_due = #{remainingDue}
            </if>
            <if test="mediatedStage != null and mediatedStage !=''">
                and cm.mediated_stage = #{mediatedStage}
            </if>
            <if test="amount1 != null and amount1 != null">
                and cil.remaining_due >= #{amount1}
            </if>
            <if test="amount2 != null and amount2 != null">
                and cil.remaining_due  &lt;= #{amount2}
            </if>
        </where>
    </select>
    <select id="selectFillingList" resultType="com.zws.appeal.pojo.appeal.FilingCasePojo">
        SELECT
        cl.id                       as caseId,
        am.package_name             as packageName,
        cl.entrusting_party_name    as  entrustingPartyName,
        cm.dispose_stage            as disposeStage,
        cl.client_name              as clientName,
        cl.client_id_num            as clientIdcard,
        cl.client_census_register   as clientCensusRegister,
        cil.remaining_due           as remainingDue,
        cm.is_freeze                as isFreeze,
        tfc.court,
        tfc.filing_number           as filingNumber,
        tfc.filing_time             as filingTime,
        tfc.contractor,
        tfc.clerk,
        trr.create_by               as follower,
        trr.create_time             as followUpTime,
        cil.yc_is_dishonest         as isDishonest,
        cil.yc_is_limit_consumption as isLimitConsumption,
        CASE WHEN cm.is_freeze>0 then tfr.start_freeze
        ELSE null
        END                         as startFreeze ,
        CASE WHEN cm.is_freeze>0 then tfr.end_freeze
        ELSE null
        END                         as endFreeze
        from
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
        LEFT JOIN team_filing_case as tfc on (cm.case_id = tfc.case_id and tfc.del_flag=0)
        LEFT JOIN team_employees AS emp ON (cm.odv_id = emp.id)
        LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
        where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        LEFT JOIN(
            SELECT tf.case_id ,tf.create_time,tf.start_freeze,tf.end_freeze FROM
            team_freeze_record tf
            INNER JOIN ( SELECT case_id, Max(create_time) AS maxTime FROM team_freeze_record WHERE del_flag = 0 GROUP BY case_id ) AS aa ON
            ( aa.case_id = tf.case_id AND aa.maxTime = tf.create_time )
        ) as tfr on (tfr.case_id = cm.case_id)
        <where>
            cl.del_flag=0 and cm.del_flag = 0
            <include refid="searchValue"/>
        </where>
    </select>
    <select id="selectWithMoney" resultType="java.util.Map">
        select count(1) AS `size`,
        sum(cil.remaining_due) AS money,
        sum(cil.sy_yh_principal) AS principal
        from case_manage AS cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
        LEFT JOIN team_filing_case as tfc on (cm.case_id = tfc.case_id and tfc.del_flag=0)
        LEFT JOIN team_employees AS emp ON (cm.odv_id = emp.id)
        <where>
            cl.del_flag=0
            <include refid="searchValue"/>
        </where>
    </select>
    <select id="getSessionList" resultType="com.zws.appeal.pojo.appeal.LawInforPojo">
        SELECT
        cl.id AS caseId,
        cm.dispose_stage AS disposeStage,
        cm.case_state AS caseState,
        cl.client_name AS clientName,
        cl.client_id_num AS clientIdNum,
        cl.client_census_register AS clientCensusRegister,
        cil.remaining_due AS remainingDue,
        trr.create_by AS updateBy,
        trr.create_time AS updateTime,
        cm.client_phone AS clientPhone,
        li.trial_lawyer AS trialLawyer,
        li.undertaking_lawyer AS undertakingLawyer,
        li.trial_sum AS trialSum,
        li.trial_time AS trialTime,
        ccr.cost_amt AS costAmt,
        li.trial_court AS trialCourt,
        li.trial_method AS trialMethod,
        li.content AS content,
        cm.is_freeze as isFreeze,
        CASE WHEN cm.is_freeze>0 then tfr.start_freeze
        ELSE null
        END as startFreeze ,
        CASE WHEN cm.is_freeze>0 then tfr.end_freeze
        ELSE null
        END as endFreeze
        FROM
        case_manage AS cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan AS cil ON ( cil.case_id = cl.id AND cil.del_flag = 0 )
        LEFT JOIN  (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
        where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        LEFT JOIN law_infor AS li on (cl.id = li.case_id and li.del_flag = 0)
        LEFT JOIN (
        WITH RankedCasess AS (
        SELECT case_id, create_by, create_time, cost_amt,
        ROW_NUMBER() OVER ( PARTITION BY case_id ORDER BY create_time DESC ) AS rn
        FROM case_cost_record WHERE del_flag = 0 AND cost_type = '开庭费'
        ) SELECT case_id, create_by, create_time, cost_amt FROM RankedCasess WHERE rn = 1
        ) AS ccr ON ( ccr.case_id = cl.id )
        LEFT JOIN(
            SELECT tf.case_id ,tf.create_time,tf.start_freeze,tf.end_freeze FROM
            team_freeze_record tf
            INNER JOIN ( SELECT case_id, Max(create_time) AS maxTime FROM team_freeze_record WHERE del_flag = 0 GROUP BY case_id ) AS aa ON
            ( aa.case_id = tf.case_id AND aa.maxTime = tf.create_time )
        ) as tfr on (tfr.case_id = cm.case_id)
        LEFT JOIN team_employees AS emp ON (cm.odv_id = emp.id)
        <where>
            cm.del_flag=0
            <if test="smallStageList != null and smallStageList.size() > 0">
                and cm.dispose_stage in
                <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="disposeStage != null and disposeStage != ''">
                and  cm.dispose_stage = #{disposeStage}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and emp.department_id in
                <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">and emp.id = #{userId}</if>
            <if test="teamId != null">
                and cm.outsourcing_team_id=#{teamId}
            </if>
            <if test="clientName!=null and clientName!=''">
                and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
            </if>
            <if test="caseId != null and caseId !=''">
                and cl.id = #{caseId}
            </if>
            <if test="caseIds != null and caseIds.size() > 0">
                and cl.id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="amount1 != null and amount1 != null">
                and cil.remaining_due >= #{amount1}
            </if>
            <if test="amount2 != null and amount2 != null">
                and cil.remaining_due  &lt;= #{amount2}
            </if>
            <if test="trialLawyer != null and trialLawyer != ''">
                and li.trial_lawyer LIKE concat ('%',#{trialLawyer},'%')
            </if>
            <if test="trialCourt != null and trialCourt != ''">
                and li.trial_court = #{trialCourt}
            </if>
            <if test="trialTime1 != null ">
                and li.trial_time &gt;=  #{trialTime1}
            </if>
            <if test="trialTime2 != null ">
                and li.trial_time &lt;= #{trialTime2}
            </if>
            <if test="amount1 != null and amount1 != null">
                and cil.remaining_due >= #{amount1}
            </if>
            <if test="amount2 != null and amount2 != null">
                and cil.remaining_due  &lt;= #{amount2}
            </if>
        </where>
        ORDER BY
        cl.id DESC
    </select>
    <select id="selectFreezeList" resultType="com.zws.appeal.pojo.appeal.FreezeCasePojo">
        select
        tfr.id                      as id,
        cl.id                       as caseId,
        cl.client_name              as clientName,
        cl.client_id_num            as clientIdcard,
        cl.client_census_register   as clientCensusRegister,
        cl.client_phone             as clientPhone,
        tfr.save_stage               as saveStage,
        tfr.freeze_amount           as freezeAmount,
        tfr.actual_freeze_amount    as actualFreezeAmount,
        tfr.freeze_assets           as freezeAssets,
        tfr.court,
        tfr.start_freeze            as startFreeze,
        tfr.end_freeze              as endFreeze,
        tfr.create_time             as applyTime,
        trr.create_by               as follower,
        trr.create_time             as followUpTime
        from
        team_freeze_record as tfr
        LEFT JOIN case_library as cl on (cl.id = tfr.case_id and cl.del_flag=0)
        LEFT JOIN case_manage  as cm on (cm.case_id = cl.id and cm.del_flag=0)
        LEFT JOIN team_employees AS emp ON (cm.odv_id = emp.id or cm.mediator_id = emp.id)
        LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
        where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        <where>
            tfr.del_flag=0 and  tfr.save_stage is not null
            <if test="smallStageList != null and smallStageList.size() > 0">
                and (tfr.save_stage in
                <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                <if test="smallStage != null and smallStage != ''">
                    or tfr.save_stage = #{smallStage}
                </if>
                )
            </if>
            <if test="saveStage != null and saveStage != ''">
                and tfr.save_stage = #{saveStage}
            </if>
            <if test="teamId != null">
                and cm.outsourcing_team_id = #{teamId}
                and tfr.team_id = #{teamId}
            </if>
            <if test="caseId != null">
                and cl.id like  concat('%',#{caseId},'%')
            </if>
            <if test="clientName != null and clientName != ''">
                and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
            </if>
            <if test="court != null and court != ''">
                and tfr.court=#{court}
            </if>
            <if test="startDate1 != null and startDate2 != null">
                and tfr.start_freeze>=#{startDate1} and tfr.end_freeze&lt;=#{startDate2}
            </if>
            <if test="applyAmount1 != null and applyAmount2 != null">
                and tfr.freeze_amount >=#{applyAmount1} and tfr.freeze_amount &lt;=#{applyAmount2}
            </if>
            <if test="actualAmount1 != null and actualAmount2 != null">
                and tfr.actual_freeze_amount >=#{actualAmount1} and tfr.actual_freeze_amount &lt;=#{actualAmount2}
            </if>
            <if test="startFreeze != null and endFreeze !=null">
                and tfr.start_freeze >=#{startFreeze} and tfr.end_freeze &lt;=#{endFreeze}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and emp.department_id in
                <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="userIds != null and userIds.size() > 0">
                AND (cm.odv_id IN
                <foreach collection="userIds" item="item" open="(" close=")" separator=",">
                     #{item}
                </foreach>
                OR cm.mediator_id IN
                <foreach collection="userIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userId != null">and emp.id = #{userId}</if>
            <if test="caseIds != null and caseIds.size() > 0">
                and tfr.id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
        cl.id DESC
    </select>

    <select id="selectMaterialDeliveryList" parameterType="com.zws.appeal.controller.request.MaterialDeliveryRequest" resultType="com.zws.appeal.controller.response.MaterialDeliveryResp">
        SELECT
        li.id AS id,
        cm.case_id AS caseId,
        cm.client_name AS clientName,
        cil.remaining_due AS remainingDue,
        cm.client_idcard clientIdCard,
        li.stage AS stage,
        cm.client_census_register AS clientCensusRegister,
        li.state AS sentStatus,
        ( SELECT sum( 1 ) FROM document WHERE case_id = cm.case_id AND del_flag = 0 ) AS sentCount,
        li.court AS sentCourt,
        li.express_number AS expressNumber,
        li.create_by AS creatBy,
        li.create_time AS creatTime,
        cm.is_freeze as isFreeze,
        CASE WHEN cm.is_freeze>0 then tfr.start_freeze
        ELSE null
        END as startFreeze ,
        CASE WHEN cm.is_freeze>0 then tfr.end_freeze
        ELSE null
        END as endFreeze
        FROM
        case_manage AS cm
        LEFT JOIN logistics_info li ON li.case_id = cm.case_id
        LEFT JOIN case_info_loan cil ON cil.case_id = cm.case_id
        LEFT JOIN team_employees emp ON li.odv_id = emp.id
        LEFT JOIN(
            SELECT tf.case_id ,tf.create_time,tf.start_freeze,tf.end_freeze FROM
            team_freeze_record tf
            INNER JOIN ( SELECT case_id, Max(create_time) AS maxTime FROM team_freeze_record WHERE del_flag = 0 GROUP BY case_id ) AS aa ON
            ( aa.case_id = tf.case_id AND aa.maxTime = tf.create_time )
        ) as tfr on (tfr.case_id = cm.case_id)
        <where>
            cm.del_flag = 0 and li.del_flag = 0 and cil.del_flag = 0
            <if test="teamId != null">
                and li.team_id = #{teamId}
            </if>
            <if test="caseId != null and caseId != ''">
                and li.case_id like concat('%',#{caseId},'%')
            </if>
            <if test="clientName != null and clientName != ''">
                AND AES_DECRYPT(UNHEX(cm.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
            </if>
            <choose>
                <when test="sentStatus == 0">
                    and (li.state = 0 or li.state = 5)
                </when>
                <otherwise>
                    <if test="sentStatus != null">
                        and li.state = #{sentStatus}
                    </if>
                </otherwise>
            </choose>
            <if test="expressNumber != null and expressNumber != ''">
                and li.express_number like  concat('%',#{expressNumber},'%')
            </if>
            <if test="webSide != null and webSide != ''">
                and li.web_side = #{webSide}
            </if>
            <if test="stage != null and stage != ''">
                and li.stage = #{stage}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and emp.department_id in
                <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="teamIds != null and teamIds.size() > 0">
                and emp.create_id in
                <foreach item="item" collection="teamIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">and li.odv_id = #{userId}</if>
            <if test="caseIds != null and caseIds.size() > 0">
                and cm.case_id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size() > 0 and allQuery==false">
                and li.id in
                <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectFreezeWithMoney" resultType="java.util.Map">
        select
        count(1)                        as `size` ,
        sum(cil.remaining_due) AS money,
        sum(tfr.actual_freeze_amount)   as freezeAmount
        from
        team_freeze_record as tfr
        LEFT JOIN case_library as cl on (cl.id = tfr.case_id and cl.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN case_manage  as cm on (cm.case_id = cl.id and cm.del_flag=0)
        LEFT JOIN team_employees AS emp ON (cm.odv_id = emp.id)
        <where>
            tfr.del_flag=0 and  tfr.save_stage is not null
            <if test="smallStageList != null and smallStageList.size() > 0">
                and (tfr.save_stage in
                <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                <if test="smallStage != null and smallStage != ''">
                    or tfr.save_stage = #{smallStage}
                </if>
                )
            </if>
            <if test="saveStage != null and saveStage != ''">
                and tfr.save_stage = #{saveStage}
            </if>
            <if test="teamId != null">
                and cm.outsourcing_team_id = #{teamId}
                and tfr.team_id = #{teamId}
            </if>
            <if test="caseId != null">
                and cl.id like  concat('%',#{caseId},'%')
            </if>
            <if test="clientName != null and clientName != ''">
                and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
            </if>
            <if test="court != null and court != ''">
                and tfr.court=#{court}
            </if>
            <if test="startDate1 != null and startDate2 != null">
                and tfr.create_time>=#{startDate1} and tfr.create_time&lt;=#{startDate2}
            </if>
            <if test="applyAmount1 != null and applyAmount2 != null">
                and tfr.freeze_amount >=#{applyAmount1} and tfr.freeze_amount &lt;=#{applyAmount2}
            </if>
            <if test="startFreeze != null and endFreeze !=null">
                and tfr.start_freeze >=#{startFreeze} and tfr.end_freeze &lt;=#{endFreeze}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and emp.department_id in
                <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="userIds != null and userIds.size() > 0">
                AND (cm.odv_id IN
                <foreach collection="userIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR cm.mediator_id IN
                <foreach collection="userIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="userId != null">and emp.id = #{userId}</if>
            <if test="caseIds != null and caseIds.size() > 0">
                and tfr.id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectMediationWithMoney" resultType="java.util.Map">
        select count(1) AS `size`,
               sum(cil.entrust_money) AS money,
               sum(cil.residual_principal) AS principal
        FROM
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
        LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM team_register_record
        where del_flag=0 and register_type = 1 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        LEFT JOIN team_employees AS emp ON (cm.mediator_id = emp.id)
        <where>
            cm.del_flag=0 and cm.mediated_stage is not null
            <if test="smallStageList != null and smallStageList.size() > 0">
                and cm.mediated_stage in
                <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="teamId != null">
                and emp.create_id=#{teamId}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and emp.department_id in
                <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">and cm.mediator_id = #{userId}</if>
            <if test="mediatedStage != null and mediatedStage != ''">
                and  cm.mediated_stage = #{mediatedStage}
            </if>
            <if test="clientName!=null and clientName!=''">
                and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
            </if>
            <if test="caseId != null">
                and cl.id = #{caseId}
            </if>
            <if test="caseIds != null and caseIds.size() > 0">
                and cl.id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="packageName != null and packageName !=''">
                and am.package_name LIKE concat ('%',#{packageName},'%')
            </if>
            <if test="mediationNum != null and mediationNum !=''">
                and cm.mediation_num LIKE concat ('%',#{mediationNum},'%')
            </if>
            <if test="remainingDue != null and remainingDue !=''">
                and cil.remaining_due = #{remainingDue}
            </if>
            <if test="mediatedStage != null and mediatedStage !=''">
                and cm.mediated_stage = #{mediatedStage}
            </if>
            <if test="amount1 != null and amount1 != null">
                and cil.remaining_due >= #{amount1}
            </if>
            <if test="amount2 != null and amount2 != null">
                and cil.remaining_due  &lt;= #{amount2}
            </if>
        </where>
    </select>
    <select id="selectSessionWithMoney" resultType="java.util.Map">
        select count(1) AS `size`,
        sum(cil.remaining_due) AS money,
        sum(cil.residual_principal) AS principal
        FROM
        case_manage AS cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan AS cil ON ( cil.case_id = cl.id AND cil.del_flag = 0 )
        LEFT JOIN  (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM team_register_record
        where del_flag=0 and register_type = 2
        )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        LEFT JOIN law_infor AS li on (cl.id = li.case_id and li.del_flag = 0)
        LEFT JOIN (
        WITH RankedCasess AS (
        SELECT
        case_id,
        create_by,
        create_time,
        cost_amt,
        ROW_NUMBER() OVER ( PARTITION BY case_id ORDER BY create_time DESC ) AS rn
        FROM
        case_cost_record
        WHERE
        del_flag = 0
        AND cost_type = '开庭费'
        ) SELECT
        case_id,
        create_by,
        create_time,
        cost_amt
        FROM
        RankedCasess
        WHERE
        rn = 1
        ) AS ccr ON ( ccr.case_id = cl.id )
        LEFT JOIN team_employees AS emp ON (cm.odv_id = emp.id)
        <where>
           cm.del_flag = 0
            <if test="smallStageList != null and smallStageList.size() > 0">
                and cm.dispose_stage in
                <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="disposeStage != null and disposeStage != ''">
                and  cm.dispose_stage = #{disposeStage}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and emp.department_id in
                <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">and emp.id = #{userId}</if>
            <if test="teamId != null">
                and cm.outsourcing_team_id=#{teamId}
            </if>
            <if test="clientName!=null and clientName!=''">
                and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
            </if>
            <if test="caseId != null and caseId !=''">
                and cl.id = #{caseId}
            </if>
            <if test="caseIds != null and caseIds.size() > 0">
                and cl.id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="amount1 != null and amount1 != null">
                and cil.remaining_due >= #{amount1}
            </if>
            <if test="amount2 != null and amount2 != null">
                and cil.remaining_due  &lt;= #{amount2}
            </if>
            <if test="trialLawyer != null and trialLawyer != ''">
                and li.trial_lawyer LIKE concat ('%',#{trialLawyer},'%')
            </if>
            <if test="trialCourt != null and trialCourt != ''">
                and li.trial_court = #{trialCourt}
            </if>
            <if test="trialTime1 != null ">
                and li.trial_time &gt;=  #{trialTime1}
            </if>
            <if test="trialTime2 != null ">
                and li.trial_time &lt;= #{trialTime2}
            </if>
            <if test="amount1 != null and amount1 != null">
                and cil.remaining_due >= #{amount1}
            </if>
            <if test="amount2 != null and amount2 != null">
                and cil.remaining_due  &lt;= #{amount2}
            </if>
        </where>
    </select>
    <select id="getJudgeList" resultType="com.zws.appeal.pojo.appeal.JudgePojo">
        select
        cl.id AS caseId,
        cl.client_name AS clientName,
        cl.client_id_num AS clientIdNum,
        cil.remaining_due AS remainingDue,
        cl.entrusting_party_name AS entrustingPartyName,
        cm.client_phone AS clientPhone,
        ji.case_status AS caseStatus,
        cm.dispose_stage AS disposeStage,
        ji.judge_time AS judgeTime,
        ji.judge_sum AS judgeSum,
        li.trial_court AS trialCourt,
        ji.judge_content AS judgeContent,
        trr.create_by AS updateBy,
        trr.create_time AS updateTime,
        cl.client_census_register   AS clientCensusRegister,
        cm.is_freeze as isFreeze,
        CASE WHEN cm.is_freeze>0 then tfr.start_freeze
        ELSE null
        END as startFreeze ,
        CASE WHEN cm.is_freeze>0 then tfr.end_freeze
        ELSE null
        END as endFreeze
        FROM
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
        LEFT JOIN judge_infor as ji on (cm.case_id = ji.case_id and ji.del_flag = 0)
        LEFT JOIN law_infor as li on (cm.case_id = li.case_id and li.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (cm.odv_id = emp.id)
        LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
        where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        LEFT JOIN(
            SELECT tf.case_id ,tf.create_time,tf.start_freeze,tf.end_freeze FROM
            team_freeze_record tf
            INNER JOIN ( SELECT case_id, Max(create_time) AS maxTime FROM team_freeze_record WHERE del_flag = 0 GROUP BY case_id ) AS aa ON
            ( aa.case_id = tf.case_id AND aa.maxTime = tf.create_time )
        ) as tfr on (tfr.case_id = cm.case_id)
        <where>
            cl.del_flag=0
            <include refid="searchValuees"/>
        </where>
        ORDER BY
        cl.id DESC
    </select>
    <select id="selectDeptWithDisposeStage" resultType="com.zws.appeal.domain.Dept">
        select dep.id,
               dep.create_id   AS createId,
               dep.parent_id   AS parentId,
               dep.ancestors,
               dep.dept_name   AS deptName,
               dep.order_num   AS orderNum,
               dep.status,
               dep.delete_logo AS deleteLogo,
               dep.founder,
               dep.creationtime,
               dep.modifier,
               dep.modify_time AS modifyTime,
               COUNT(cm.id)    as caseNum
        from team_dept as dep
                 LEFT JOIN team_employees emp on (dep.id = emp.department_id and emp.delete_logo = 0)
        LEFT JOIN (
        SELECT
        id,
        odv_id
        FROM
        case_manage
        WHERE
        del_flag = 0
        <if test="smallStageList != null and smallStageList.size() > 0">
            and dispose_stage in
            <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>) cm ON ( cm.odv_id = emp.id )
        where dep.delete_logo = 0
          and dep.create_id = #{createId}
        <if test="deptList != null and deptList.size() > 0">
            and emp.department_id in
            <foreach collection="deptList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY dep.id
        ORDER BY caseNum desc
    </select>
    <select id="selectEmployeesWithDisposeStage" resultType="com.zws.appeal.domain.Employees">
        select emp.id                   as id,
               emp.create_id            as createId,
               emp.department_id        as departmentId,
               emp.role_id              as roleId,
               emp.employee_name        as employeeName,
               emp.departments          as departments,
               emp.login_account        as loginAccount,
               emp.password             as password,
               emp.phone_number         as phoneNumber,
               emp.the_role             as theRole,
               emp.account_status       as accountStatus,
               emp.working_state        as workingState,
               emp.founder              as founder,
               emp.creationtime         as creationtime,
               emp.modifier             as modifier,
               emp.modify_time          as modifyTime,
               emp.employees_working    as employeesWorking,
               emp.login_date           as loginDate,
               emp.sip_password         as sipPassword,
               emp.update_password_time as updatePasswordTime,
               emp.department_head      as departmentHead,
               COUNT(cm.id) as caseNum
        from team_employees as emp
        LEFT JOIN (
        SELECT
        id,
        odv_id
        FROM
        case_manage
        WHERE
        del_flag = 0
        <if test="smallStageList != null and smallStageList.size() > 0">
            and dispose_stage in
            <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>) cm ON ( cm.odv_id = emp.id )
        where emp.delete_logo = 0
          and emp.department_id = #{parentId}
          and emp.create_id = #{createId}
        GROUP BY emp.id
        ORDER BY caseNum desc
    </select>
    <select id="selectDeptWithSaveStage" resultType="com.zws.appeal.domain.Dept">
        select dep.id,
               dep.create_id   AS createId,
               dep.parent_id   AS parentId,
               dep.ancestors,
               dep.dept_name   AS deptName,
               dep.order_num   AS orderNum,
               dep.status,
               dep.delete_logo AS deleteLogo,
               dep.founder,
               dep.creationtime,
               dep.modifier,
               dep.modify_time AS modifyTime,
               COUNT(tfr.id)    as caseNum
        from team_dept as dep
                 LEFT JOIN team_employees emp on (dep.id = emp.department_id and emp.delete_logo = 0)
                 LEFT JOIN case_manage cm on ((cm.odv_id = emp.id or cm.mediator_id = emp.id )and cm.del_flag = 0)
        LEFT JOIN (
        SELECT
        id,
        case_id,
        team_id
        FROM
        team_freeze_record
        WHERE del_flag = 0
        <if test="smallStageList != null and smallStageList.size() > 0">
            and (save_stage in
            <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="smallStage != null and smallStage != ''">
                or save_stage = #{smallStage}
            </if>
            )
        </if>) tfr ON ( cm.case_id = tfr.case_id and tfr.team_id=#{createId})
        where dep.delete_logo = 0
          and dep.create_id = #{createId}
        <if test="deptList != null and deptList.size() > 0">
            and emp.department_id in
            <foreach collection="deptList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY dep.id
        ORDER BY caseNum desc
    </select>
    <select id="selectEmployeesWithSaveStage" resultType="com.zws.appeal.domain.Employees">
        select emp.id                   as id,
               emp.create_id            as createId,
               emp.department_id        as departmentId,
               emp.role_id              as roleId,
               emp.employee_name        as employeeName,
               emp.departments          as departments,
               emp.login_account        as loginAccount,
               emp.password             as password,
               emp.phone_number         as phoneNumber,
               emp.the_role             as theRole,
               emp.account_status       as accountStatus,
               emp.working_state        as workingState,
               emp.founder              as founder,
               emp.creationtime         as creationtime,
               emp.modifier             as modifier,
               emp.modify_time          as modifyTime,
               emp.employees_working    as employeesWorking,
               emp.login_date           as loginDate,
               emp.sip_password         as sipPassword,
               emp.update_password_time as updatePasswordTime,
               emp.department_head      as departmentHead,
               COUNT(tfr.id) as caseNum
        from team_employees as emp
                 LEFT JOIN case_manage cm on ((cm.odv_id = emp.id or cm.mediator_id = emp.id ) and cm.del_flag = 0)
        LEFT JOIN (
        SELECT
        id,
        case_id
        FROM
        team_freeze_record
        WHERE del_flag = 0
        <if test="smallStageList != null and smallStageList.size() > 0">
            and (save_stage in
            <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="smallStage != null and smallStage != ''">
                or save_stage = #{smallStage}
            </if>
            )
        </if>) tfr ON ( cm.case_id = tfr.case_id )
        where emp.delete_logo = 0
          and emp.department_id = #{parentId}
          and emp.create_id = #{createId}
        GROUP BY emp.id
        ORDER BY caseNum desc
    </select>
    <select id="selectJudgeWithMoney" resultType="java.util.Map">
        select count(1) AS `size`,
        sum(cil.remaining_due) AS money,
        sum(ji.judge_sum) AS principal
        FROM
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
        LEFT JOIN judge_infor as ji on (cm.case_id = ji.case_id and ji.del_flag = 0)
        LEFT JOIN law_infor as li on (cm.case_id = li.case_id and li.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (cm.odv_id = emp.id)
        <where>
            cl.del_flag=0
            <include refid="searchValuees"/>
        </where>
    </select>
    <select id="selectExecuteList" resultType="com.zws.appeal.pojo.appeal.ExecuteCasePojo">
        SELECT
        cl.id                       as caseId,
        cl.client_name              as clientName,
        cil.remaining_due           as remainingDue,
        cl.client_id_num            as clientIdcard,
        cl.client_census_register   as clientCensusRegister,
        cm.dispose_stage            as disposeStage,
        cl.client_phone             as clientPhone,
        tec.conclude_case_amount    as concludeCaseAmount,
        tec.involved_with           as involvedWith,
        tec.is_missing              as isMissing,
        tec.executive_court         as executiveCourt,
        tec.closed_reason           as closedReason,
        tec.closed_mode             as closedMode,
        tec.is_file                 as isFile,
        trr.create_by               as createBy,
        trr.create_time             as createTime
        from
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN team_employees AS emp ON (cm.odv_id = emp.id)
        LEFT JOIN team_execute_case as tec on (cil.case_id = tec.case_id and tec.del_flag=0)
        LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
        where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        <where>
            cm.del_flag=0
            <if test="smallStageList != null and smallStageList.size() > 0">
                and cm.dispose_stage in
                <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="teamId != null">
                and cm.outsourcing_team_id = #{teamId}
            </if>
            <if test="caseId != null">
                and cl.id like  concat('%',#{caseId},'%')
            </if>
            <if test="caseIds != null and caseIds.size() > 0">
                and cl.id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="clientName != null and clientName != ''">
                AND AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) like  concat('%',#{clientName},'%')
            </if>
            <if test="closedMode != null and closedMode != ''">
                and tec.closed_mode = #{closedMode}
            </if>

            <if test="amount1 != null and amount1 != null">
                and cil.remaining_due >= #{amount1}
            </if>
            <if test="amount2 != null and amount2 != null">
                and cil.remaining_due  &lt;= #{amount2}
            </if>
            <if test="executiveCourt != null and executiveCourt != ''">
                and tec.executive_court = #{executiveCourt}
            </if>
            <if test="startDate1 != null and startDate2 != null">
                and tec.create_time>=#{startDate1} and tec.create_time&lt;=#{startDate2}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and emp.department_id in
                <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">and emp.id = #{userId}</if>
            <if test="disposeStage != null and disposeStage != ''">
                and  cm.dispose_stage = #{disposeStage}
            </if>
            <if test="involvedWith != null and involvedWith != ''">
                and tec.involved_with = #{involvedWith}
            </if>
        </where>
        ORDER BY
        cl.id DESC
    </select>
    <select id="selectExecuteMoney" resultType="java.util.Map">
        select
            count(1)                        AS `size`,
            sum(cil.remaining_due)          AS money,
            sum(tec.conclude_case_amount)   AS concludeCaseAmount,
            sum(cil.residual_principal)   AS principal
        from
        case_manage as cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cm.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN team_employees AS emp ON (cm.odv_id = emp.id)
        LEFT JOIN team_execute_case as tec on (cil.case_id = tec.case_id and tec.del_flag=0)
        LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM team_register_record
        where del_flag=0 and register_type = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id=cl.id)
        <where>
            cm.del_flag=0
            <if test="smallStageList != null and smallStageList.size() > 0">
                and cm.dispose_stage in
                <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>

            </if>
            <if test="teamId != null">
                and cm.outsourcing_team_id = #{teamId}
            </if>
            <if test="caseId != null">
                and cl.id like  concat('%',#{caseId},'%')
            </if>
            <if test="caseIds != null and caseIds.size() > 0">
                and cl.id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="clientName != null and clientName != ''">
                AND AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) like  concat('%',#{clientName},'%')
            </if>
            <if test="closedMode != null and closedMode != ''">
                and tec.closed_mode = #{closedMode}
            </if>

            <if test="amount1 != null and amount1 != null">
                and cil.remaining_due >= #{amount1}
            </if>
            <if test="amount2 != null and amount2 != null">
                and cil.remaining_due  &lt;= #{amount2}
            </if>
            <if test="executiveCourt != null and executiveCourt != ''">
                and tec.executive_court = #{executiveCourt}
            </if>
            <if test="startDate1 != null and startDate2 != null">
                and tec.create_time>=#{startDate1} and tec.create_time&lt;=#{startDate2}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and emp.department_id in
                <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">and emp.id = #{userId}</if>
            <if test="disposeStage != null and disposeStage != ''">
                and  cm.dispose_stage = #{disposeStage}
            </if>
            <if test="involvedWith != null and involvedWith != ''">
                and tec.involved_with = #{involvedWith}
            </if>
        </where>
    </select>

    <select id="selectMaterialDeliveryMoney" parameterType="com.zws.appeal.controller.request.MaterialDeliveryRequest" resultType="com.zws.appeal.controller.response.MaterialDeliveryCountResp">
        select
        count(li.case_id)                        AS `size`,
        sum(cil.remaining_due)          AS money
        from
        case_manage as cm
        LEFT JOIN case_library AS cl ON ( cl.id = cm.case_id AND cm.del_flag = 0 )
        LEFT JOIN logistics_info AS li ON ( li.case_id = cm.case_id )
        LEFT JOIN team_employees AS emp ON ( li.odv_id = emp.id )
        LEFT JOIN case_info_loan AS cil ON ( cil.case_id = li.case_id AND cil.del_flag = 0 )
        <where>
            cm.del_flag=0
            <if test="caseId != null">
                and cl.id like  concat('%',#{caseId},'%')
            </if>
            <if test="ids != null and ids.size() > 0">
                and li.id in
                <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="caseIds != null and caseIds.size() > 0">
                and li.case_id in
                <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="teamId != null">
                and li.team_id = #{teamId}
            </if>
            <choose>
                <when test="sentStatus == 0">
                    and (li.state = 0 or li.state = 5)
                </when>
                <otherwise>
                    <if test="sentStatus != null">
                        and li.state = #{sentStatus}
                    </if>
                </otherwise>
            </choose>
            <if test="expressNumber != null">
                and li.express_number = #{expressNumber}
            </if>
            <if test="stage != null">and li.stage = #{stage}</if>
            <if test="deptIds != null and deptIds.size() > 0">
                and emp.department_id in
                <foreach item="item" collection="deptIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null">and li.odv_id = #{userId}</if>
            <if test="clientName != null and clientName != ''">
                AND AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
            </if>
        </where>
    </select>
    <select id="selectEmployeesWithMediation" resultType="com.zws.appeal.domain.Employees">
        select emp.id                   as id,
        emp.create_id            as createId,
        emp.department_id        as departmentId,
        emp.role_id              as roleId,
        emp.employee_name        as employeeName,
        emp.departments          as departments,
        emp.login_account        as loginAccount,
        emp.password             as password,
        emp.phone_number         as phoneNumber,
        emp.the_role             as theRole,
        emp.account_status       as accountStatus,
        emp.working_state        as workingState,
        emp.founder              as founder,
        emp.creationtime         as creationtime,
        emp.modifier             as modifier,
        emp.modify_time          as modifyTime,
        emp.employees_working    as employeesWorking,
        emp.login_date           as loginDate,
        emp.sip_password         as sipPassword,
        emp.update_password_time as updatePasswordTime,
        emp.department_head      as departmentHead,
        COUNT(cm.id) as caseNum
        from team_employees as emp
        LEFT JOIN (
        SELECT
        id,
        mediator_id
        FROM
        case_manage
        WHERE
        del_flag = 0 and (case_state !=2 or case_mediate_state !=2)
        and outsourcing_team_id = #{createId}
        <if test="smallStageList != null and smallStageList.size() > 0">
            and mediated_stage in
            <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>) cm ON ( cm.mediator_id = emp.id )
        where emp.delete_logo = 0
        and emp.department_id = #{parentId}
        and emp.create_id = #{createId}
        GROUP BY emp.id
        ORDER BY caseNum desc
    </select>
    <select id="selectMediationDept" resultType="com.zws.appeal.domain.Dept">
        select dep.id,
        dep.create_id   AS createId,
        dep.parent_id   AS parentId,
        dep.ancestors,
        dep.dept_name   AS deptName,
        dep.order_num   AS orderNum,
        dep.status,
        dep.delete_logo AS deleteLogo,
        dep.founder,
        dep.creationtime,
        dep.modifier,
        dep.modify_time AS modifyTime,
        COUNT(cm.id)    as caseNum
        from team_dept as dep
        LEFT JOIN team_employees emp on (dep.id = emp.department_id and emp.delete_logo = 0)
        LEFT JOIN (
        SELECT
        id,
        mediator_id
        FROM
        case_manage
        WHERE
        del_flag = 0 and (case_state !=2 or case_mediate_state !=2)
        and outsourcing_team_id = #{createId}
        <if test="smallStageList != null and smallStageList.size() > 0">
            and mediated_stage in
            <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>) cm ON ( cm.mediator_id = emp.id )
        where dep.delete_logo = 0
        and dep.create_id = #{createId}
        <if test="deptList != null and smallStageList.size() > 0">
            and emp.department_id in
            <foreach collection="deptList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY dep.id
        ORDER BY caseNum desc
    </select>
    <select id="selectDeptWithExecute" resultType="com.zws.appeal.domain.Dept">
        select dep.id,
        dep.create_id   AS createId,
        dep.parent_id   AS parentId,
        dep.ancestors,
        dep.dept_name   AS deptName,
        dep.order_num   AS orderNum,
        dep.status,
        dep.delete_logo AS deleteLogo,
        dep.founder,
        dep.creationtime,
        dep.modifier,
        dep.modify_time AS modifyTime,
        COUNT(cm.id)    as caseNum
        from team_dept as dep
        LEFT JOIN team_employees emp on (dep.id = emp.department_id and emp.delete_logo = 0)
        LEFT JOIN (
        SELECT
        id,
        odv_id
        FROM
        case_manage
        WHERE
        del_flag = 0
        <if test="smallStageList != null and smallStageList.size() > 0">
            and dispose_stage in
            <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>) cm ON ( cm.odv_id = emp.id )
        where dep.delete_logo = 0
        and dep.create_id = #{createId}
        <if  test="deptList != null and smallStageList.size() > 0">
            and emp.department_id in
            <foreach collection="deptList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY dep.id
        ORDER BY caseNum desc
    </select>
    <select id="selectEmployeesWithExecute" resultType="com.zws.appeal.domain.Employees">
        select emp.id                   as id,
        emp.create_id            as createId,
        emp.department_id        as departmentId,
        emp.role_id              as roleId,
        emp.employee_name        as employeeName,
        emp.departments          as departments,
        emp.login_account        as loginAccount,
        emp.password             as password,
        emp.phone_number         as phoneNumber,
        emp.the_role             as theRole,
        emp.account_status       as accountStatus,
        emp.working_state        as workingState,
        emp.founder              as founder,
        emp.creationtime         as creationtime,
        emp.modifier             as modifier,
        emp.modify_time          as modifyTime,
        emp.employees_working    as employeesWorking,
        emp.login_date           as loginDate,
        emp.sip_password         as sipPassword,
        emp.update_password_time as updatePasswordTime,
        emp.department_head      as departmentHead,
        COUNT(cm.id) as caseNum
        from team_employees as emp
        LEFT JOIN (
        SELECT
        id,
        odv_id
        FROM
        case_manage
        WHERE
        del_flag = 0
        <if test="smallStageList != null and smallStageList.size() > 0">
            and dispose_stage in
            <foreach collection="smallStageList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>) cm ON ( cm.odv_id = emp.id )
        where emp.delete_logo = 0
        and emp.department_id = #{parentId}
        and emp.create_id = #{createId}
        GROUP BY emp.id
        ORDER BY caseNum desc
    </select>
    <select id="selectUserIds" resultType="java.lang.Integer">
        SELECT
            emp.id
        FROM
            team_employees AS emp
                LEFT JOIN team_dept AS dep ON ( emp.department_id = dep.id )
        WHERE emp.delete_logo = 0
        <if test="list != null and list.size() > 0">
            and dep.id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>