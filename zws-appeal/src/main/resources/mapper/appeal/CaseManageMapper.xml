<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.CaseManageMapper">


    <select id="getEntrustingCaseBatchNumByCaseId" resultType="java.lang.String">
        SELECT entrusting_case_batch_num FROM case_manage WHERE del_flag=0 AND case_id=#{caseId} LIMIT 1
    </select>

    <update id="updateDisposeStage">
     update case_manage
        set  dispose_stage  = #{mediateStage,jdbcType=VARCHAR},
             update_time    = NOW()
        where
        case_id =#{caseId}
    </update>


</mapper>
