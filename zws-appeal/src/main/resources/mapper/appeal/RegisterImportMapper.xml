<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.RegisterImportMapper">
  <resultMap id="BaseResultMap" type="com.zws.appeal.domain.appeal.RegisterImport">
   <!--@mbg.generated-->
    <!--@Table team_register_import-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="import_type" jdbcType="INTEGER" property="importType" />
    <result column="success_number" jdbcType="INTEGER" property="successNumber" />
    <result column="creation_by_id" jdbcType="BIGINT" property="creationById" />
    <result column="creation_by" jdbcType="VARCHAR" property="creationBy" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="import_status" jdbcType="INTEGER" property="importStatus" />
    <result column="original_file_url" jdbcType="VARCHAR" property="originalFileUrl" />
    <result column="error_file_url" jdbcType="VARCHAR" property="errorFileUrl" />
    <result column="fail_msg" jdbcType="LONGVARCHAR" property="failMsg" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, file_name, import_type, success_number, creation_by_id, creation_by, creation_time, 
    import_status, original_file_url, error_file_url, fail_msg, team_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from team_register_import
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from team_register_import
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.RegisterImport" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_register_import (file_name, import_type, success_number,
      creation_by_id, creation_by, creation_time,
      import_status, original_file_url, error_file_url,
      fail_msg, team_id)
    values (#{fileName,jdbcType=VARCHAR}, #{importType,jdbcType=INTEGER}, #{successNumber,jdbcType=INTEGER},
      #{creationById,jdbcType=BIGINT}, #{creationBy,jdbcType=VARCHAR}, #{creationTime,jdbcType=TIMESTAMP},
      #{importStatus,jdbcType=INTEGER}, #{originalFileUrl,jdbcType=VARCHAR}, #{errorFileUrl,jdbcType=VARCHAR},
      #{failMsg,jdbcType=LONGVARCHAR}, #{teamId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.RegisterImport" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_register_import
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fileName != null">
        file_name,
      </if>
      <if test="importType != null">
        import_type,
      </if>
      <if test="successNumber != null">
        success_number,
      </if>
      <if test="creationById != null">
        creation_by_id,
      </if>
      <if test="creationBy != null">
        creation_by,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="importStatus != null">
        import_status,
      </if>
      <if test="originalFileUrl != null">
        original_file_url,
      </if>
      <if test="errorFileUrl != null">
        error_file_url,
      </if>
      <if test="failMsg != null">
        fail_msg,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="importType != null">
        #{importType,jdbcType=INTEGER},
      </if>
      <if test="successNumber != null">
        #{successNumber,jdbcType=INTEGER},
      </if>
      <if test="creationById != null">
        #{creationById,jdbcType=BIGINT},
      </if>
      <if test="creationBy != null">
        #{creationBy,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="importStatus != null">
        #{importStatus,jdbcType=INTEGER},
      </if>
      <if test="originalFileUrl != null">
        #{originalFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="errorFileUrl != null">
        #{errorFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="failMsg != null">
        #{failMsg,jdbcType=LONGVARCHAR},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.appeal.RegisterImport">
    <!--@mbg.generated-->
    update team_register_import
    <set>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="importType != null">
        import_type = #{importType,jdbcType=INTEGER},
      </if>
      <if test="successNumber != null">
        success_number = #{successNumber,jdbcType=INTEGER},
      </if>
      <if test="creationById != null">
        creation_by_id = #{creationById,jdbcType=BIGINT},
      </if>
      <if test="creationBy != null">
        creation_by = #{creationBy,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="importStatus != null">
        import_status = #{importStatus,jdbcType=INTEGER},
      </if>
      <if test="originalFileUrl != null">
        original_file_url = #{originalFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="errorFileUrl != null">
        error_file_url = #{errorFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="failMsg != null">
        fail_msg = #{failMsg,jdbcType=LONGVARCHAR},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.appeal.RegisterImport">
    <!--@mbg.generated-->
    update team_register_import
    set file_name = #{fileName,jdbcType=VARCHAR},
      import_type = #{importType,jdbcType=INTEGER},
      success_number = #{successNumber,jdbcType=INTEGER},
      creation_by_id = #{creationById,jdbcType=BIGINT},
      creation_by = #{creationBy,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      import_status = #{importStatus,jdbcType=INTEGER},
      original_file_url = #{originalFileUrl,jdbcType=VARCHAR},
      error_file_url = #{errorFileUrl,jdbcType=VARCHAR},
      fail_msg = #{failMsg,jdbcType=LONGVARCHAR},
      team_id = #{teamId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_register_import
    (file_name, import_type, success_number, creation_by_id, creation_by, creation_time,
      import_status, original_file_url, error_file_url, fail_msg, team_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fileName,jdbcType=VARCHAR}, #{item.importType,jdbcType=INTEGER}, #{item.successNumber,jdbcType=INTEGER},
        #{item.creationById,jdbcType=BIGINT}, #{item.creationBy,jdbcType=VARCHAR}, #{item.creationTime,jdbcType=TIMESTAMP},
        #{item.importStatus,jdbcType=INTEGER}, #{item.originalFileUrl,jdbcType=VARCHAR},
        #{item.errorFileUrl,jdbcType=VARCHAR}, #{item.failMsg,jdbcType=LONGVARCHAR}, #{item.teamId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
</mapper>