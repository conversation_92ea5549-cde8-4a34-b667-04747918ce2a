<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.FilingCaseMapper">


  <resultMap id="BaseResultMap" type="com.zws.appeal.domain.appeal.FilingCase">
    <!--@mbg.generated-->
    <!--@Table team_filing_case-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_id" jdbcType="BIGINT" property="caseId" />
    <result column="court_id" jdbcType="BIGINT" property="courtId" />
    <result column="court" jdbcType="VARCHAR" property="court" />
    <result column="filing_number" jdbcType="VARCHAR" property="filingNumber" />
    <result column="filing_time" jdbcType="TIMESTAMP" property="filingTime" />
    <result column="contractor" jdbcType="VARCHAR" property="contractor" />
    <result column="clerk" jdbcType="VARCHAR" property="clerk" />
    <result column="check_status" jdbcType="INTEGER" property="checkStatus" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
  </resultMap>

   <resultMap id="ManageBaseResultMap" type="com.zws.appeal.domain.CaseManage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="phone_state" jdbcType="INTEGER" property="phoneState"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="allocated_state" jdbcType="VARCHAR" property="allocatedState"/>
        <result column="case_state" jdbcType="VARCHAR" property="caseState"/>
        <result column="entrusting_party_id" jdbcType="BIGINT" property="entrustingPartyId"/>
        <result column="entrusting_party_name" jdbcType="VARCHAR" property="entrustingPartyName"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="batch_num" jdbcType="VARCHAR" property="batchNum"/>
        <result column="entrusting_case_batch_num" jdbcType="VARCHAR" property="entrustingCaseBatchNum"/>
        <result column="outsourcing_team_id" jdbcType="BIGINT" property="outsourcingTeamId"/>
        <result column="outsourcing_team_name" jdbcType="VARCHAR" property="outsourcingTeamName"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="client_sex" jdbcType="VARCHAR" property="clientSex"/>
        <result column="client_idcard" jdbcType="VARCHAR" property="clientIdcard"/>
        <result column="client_census_register" jdbcType="VARCHAR" property="clientCensusRegister"/>
        <result column="client_phone" jdbcType="VARCHAR" property="clientPhone"/>
        <result column="client_money" jdbcType="DECIMAL" property="clientMoney"/>
        <result column="client_residual_principal" jdbcType="DECIMAL" property="clientResidualPrincipal"/>
        <result column="client_overdue_start" jdbcType="TIMESTAMP" property="clientOverdueStart"/>
        <result column="account_period" jdbcType="INTEGER" property="accountPeriod"/>
        <result column="follow_up_state" jdbcType="VARCHAR" property="followUpState"/>
        <result column="follow_up_start" jdbcType="TIMESTAMP" property="followUpStart"/>
        <result column="follow_up_ast" jdbcType="TIMESTAMP" property="followUpAst"/>
        <result column="entrusting_case_date" jdbcType="TIMESTAMP" property="entrustingCaseDate"/>
        <result column="return_case_date" jdbcType="TIMESTAMP" property="returnCaseDate"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="odv_name" jdbcType="VARCHAR" property="odvName"/>
        <result column="urge_state" jdbcType="VARCHAR" property="urgeState"/>
        <result column="allocated_time" jdbcType="TIMESTAMP" property="allocatedTime"/>
        <result column="urge_power" jdbcType="VARCHAR" property="urgePower"/>
        <result column="del_flag" jdbcType="BIT" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="client_id_type" property="clientIdType"/>
        <result column="settlement_status" property="settlementStatus"/>
        <result column="remaining_due" property="remainingDue"/>
        <result column="remaining_due" property="remainingDue"/>
        <result column="sy_yh_principal" property="syYhPrincipal"/>
        <result column="client_birthday" property="clientBirthday"/>
    </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, case_id, court_id, court, filing_number, filing_time, contractor, clerk, check_status,
    `type`, remark, create_time, create_by, create_by_id, update_time, update_by, update_by_id,
    del_flag, team_id
  </sql>

  <sql id="searchValue">
        <if test="teamId != null">
          and cm.outsourcing_team_id=#{teamId}
        </if>
        <if test="originalStage != null and originalStage != ''">
            and  cm.dispose_stage = #{originalStage}
        </if>
      <if test="disposeStage != null and disposeStage != '' and originalStage == null">
          and  cm.dispose_stage = #{disposeStage}
      </if>
        <if test="packageName != null and packageName != ''">
            and am.package_name like concat('%', #{packageName}, '%')
        </if>
        <if test="caseId != null">
            and cl.id like concat('%', #{caseId}, '%')
        </if>
        <if test="clientName != null and clientName != ''">
            and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
        </if>
        <if test="filingNumber != null and filingNumber != ''">
            and tfc.filing_number like concat('%', #{filingNumber}, '%')
        </if>
        <if test="court != null and court != ''">
            and tfc.court = #{court}
        </if>
        <if test="amount1 != null and amount1 != null">
            and cil.remaining_due >= #{amount1} and cil.remaining_due  &lt;= #{amount2}
        </if>
        <if test="isFreeze != null">
            and cm.is_freeze = #{isFreeze}
        </if>
        <if test="odvId != null and odvId != ''">
            and (cm.odv_id = #{odvId} or cm.mediator_id = #{odvId})
        </if>

        <if test="!allQuery and ids!=null and ids.size()>0">
            and cm.case_id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

       <if test="  caseStates!=null and caseStates.size()>0">
            and cm.case_state in
            <foreach collection="caseStates" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from team_filing_case
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from team_filing_case
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.FilingCase" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_filing_case (case_id, court_id, court,
      filing_number, filing_time, contractor,
      clerk, check_status, `type`,
      remark, create_time, create_by,
      create_by_id, update_time, update_by,
      update_by_id, del_flag, team_id
      )
    values (#{caseId,jdbcType=BIGINT}, #{courtId,jdbcType=BIGINT}, #{court,jdbcType=VARCHAR},
      #{filingNumber,jdbcType=VARCHAR}, #{filingTime,jdbcType=TIMESTAMP}, #{contractor,jdbcType=VARCHAR},
      #{clerk,jdbcType=VARCHAR}, #{checkStatus,jdbcType=INTEGER}, #{type,jdbcType=INTEGER},
      #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR},
      #{createById,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
      #{updateById,jdbcType=BIGINT}, #{delFlag,jdbcType=CHAR}, #{teamId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.FilingCase" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_filing_case
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        case_id,
      </if>
      <if test="courtId != null">
        court_id,
      </if>
      <if test="court != null">
        court,
      </if>
      <if test="filingNumber != null">
        filing_number,
      </if>
      <if test="filingTime != null">
        filing_time,
      </if>
      <if test="contractor != null">
        contractor,
      </if>
      <if test="clerk != null">
        clerk,
      </if>
      <if test="checkStatus != null">
        check_status,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        #{caseId,jdbcType=BIGINT},
      </if>
      <if test="courtId != null">
        #{courtId,jdbcType=BIGINT},
      </if>
      <if test="court != null">
        #{court,jdbcType=VARCHAR},
      </if>
      <if test="filingNumber != null">
        #{filingNumber,jdbcType=VARCHAR},
      </if>
      <if test="filingTime != null">
        #{filingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractor != null">
        #{contractor,jdbcType=VARCHAR},
      </if>
      <if test="clerk != null">
        #{clerk,jdbcType=VARCHAR},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.appeal.FilingCase">
    <!--@mbg.generated-->
    update team_filing_case
    <set>
      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>
      <if test="courtId != null">
        court_id = #{courtId,jdbcType=BIGINT},
      </if>
      <if test="court != null">
        court = #{court,jdbcType=VARCHAR},
      </if>
      <if test="filingNumber != null">
        filing_number = #{filingNumber,jdbcType=VARCHAR},
      </if>
      <if test="filingTime != null">
        filing_time = #{filingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractor != null">
        contractor = #{contractor,jdbcType=VARCHAR},
      </if>
      <if test="clerk != null">
        clerk = #{clerk,jdbcType=VARCHAR},
      </if>
      <if test="checkStatus != null">
        check_status = #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.appeal.FilingCase">
    <!--@mbg.generated-->
    update team_filing_case
    set case_id = #{caseId,jdbcType=BIGINT},
      court_id = #{courtId,jdbcType=BIGINT},
      court = #{court,jdbcType=VARCHAR},
      filing_number = #{filingNumber,jdbcType=VARCHAR},
      filing_time = #{filingTime,jdbcType=TIMESTAMP},
      contractor = #{contractor,jdbcType=VARCHAR},
      clerk = #{clerk,jdbcType=VARCHAR},
      check_status = #{checkStatus,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_by_id = #{createById,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      del_flag = #{delFlag,jdbcType=CHAR},
      team_id = #{teamId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_filing_case
    (case_id, court_id, court, filing_number, filing_time, contractor, clerk, check_status,
      `type`, remark, create_time, create_by, create_by_id, update_time, update_by, update_by_id,
      del_flag, team_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.caseId,jdbcType=BIGINT}, #{item.courtId,jdbcType=BIGINT}, #{item.court,jdbcType=VARCHAR},
        #{item.filingNumber,jdbcType=VARCHAR}, #{item.filingTime,jdbcType=TIMESTAMP}, #{item.contractor,jdbcType=VARCHAR},
        #{item.clerk,jdbcType=VARCHAR}, #{item.checkStatus,jdbcType=INTEGER}, #{item.type,jdbcType=INTEGER},
        #{item.remark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR},
        #{item.createById,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR},
        #{item.updateById,jdbcType=BIGINT}, #{item.delFlag,jdbcType=CHAR}, #{item.teamId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>

    <select id="selectList" resultType="com.zws.appeal.pojo.appeal.FilingCasePojo">
    SELECT
    cl.id                       as caseId,
    am.package_name             as packageName,
    cl.entrusting_party_name    as  entrustingPartyName,
    cm.dispose_stage            as disposeStage,
    cl.client_name              as clientName,
    cl.client_id_num            as clientIdcard,
    cl.client_census_register   as clientCensusRegister,
    cil.remaining_due           as remainingDue,
    cm.is_freeze                as isFreeze,
    tfc.court,
    tfc.filing_number           as filingNumber,
    tfc.filing_time             as filingTime,
    tfc.contractor,
    tfc.clerk,
    trr.create_by               as follower,
    trr.create_time             as followUpTime,
    cm.client_phone             as clientPhone,
    cm.label                    as label,
    cm.label_asset              as labelAsset,
    tl.label_content            as labelContent,
    CASE WHEN cm.is_freeze>0 then tfr.start_freeze
    ELSE null
    END as startFreeze ,
    CASE WHEN cm.is_freeze>0 then tfr.end_freeze
    ELSE null
    END as endFreeze
    from
    case_manage as cm
    LEFT JOIN case_library as cl on (cl.id = cm.case_id and cl.del_flag=0)
    LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
    LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
    LEFT JOIN team_label as tl on (cm.label = tl.code and tl.create_id = #{teamId})
    LEFT JOIN team_filing_case as tfc on (cm.case_id = tfc.case_id and tfc.del_flag=0 and tfc.type=0 and tfc.team_id = #{teamId})
    LEFT JOIN (
        WITH RankedCases AS (
        SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
        where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
    ) as trr on (trr.case_id=cl.id)
    LEFT JOIN(
        SELECT tf.case_id ,tf.create_time,tf.start_freeze,tf.end_freeze FROM
        team_freeze_record tf
        INNER JOIN ( SELECT case_id, Max(create_time) AS maxTime FROM team_freeze_record WHERE del_flag = 0 GROUP BY case_id ) AS aa ON
        ( aa.case_id = tf.case_id AND aa.maxTime = tf.create_time )
    ) as tfr on (tfr.case_id = cm.case_id)
    <where>
    cm.del_flag=0
    <include refid="searchValue"/>
    </where>
    order by  cl.id desc
    </select>

    <select id="selectWithCaseId" resultType="java.lang.Long">
    select case_id from team_filing_case
    where
    del_flag=0 and type=0
    and case_id in
  <foreach collection="list" item="item" separator="," open="(" close=")">
    #{item}
  </foreach>
</select>

    <select id="selectCaseIds" resultType="java.lang.Long">
    SELECT
    cm.case_id                       as caseId
    from
    case_manage as cm
    LEFT JOIN case_library as cl on (cl.id = cm.case_id and cl.del_flag=0)
    LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
    LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
    LEFT JOIN team_filing_case as tfc on (cm.case_id = tfc.case_id and tfc.del_flag=0 and tfc.type=0 and tfc.team_id = #{teamId})
    <where>
    cm.del_flag=0
    <include refid="searchValue"/>
    </where>

</select>

    <select id="selectWithMoney" resultType="java.util.Map">

        select count(1) AS `size`,
        sum(cil.remaining_due) AS money,
        sum(cil.sy_yh_principal) AS principal
        from case_manage AS cm
        LEFT JOIN case_library as cl on (cl.id = cm.case_id and cl.del_flag=0)
        LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
        LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
        LEFT JOIN team_filing_case as tfc on (cm.case_id = tfc.case_id and tfc.del_flag=0 and tfc.type=0)
        <where>
        cm.del_flag=0
        <include refid="searchValue"/>
        </where>
    </select>
    <select id="selectCase" resultType="com.zws.appeal.domain.appeal.FilingCase">
        select <include refid="Base_Column_List"/>
        from team_filing_case
        where case_id = #{id} and type = 1
    </select>

    <update id="updateWithStage">
    update
    case_manage
    <set>
    <if test="mediatedStage !=null and mediatedStage!=''">
     mediated_stage=#{mediatedStage},
    </if>
    <if test="disposeStage !=null and disposeStage!=''">
     dispose_stage=#{disposeStage},
    </if>
    <if test="updateTime != null">
     update_time=#{updateTime},
    </if>
    <if test="updateBy != null and updateBy!=''">
     update_by=#{updateBy},
    </if>
    <if test="isFreeze !=null ">
     is_freeze = #{isFreeze},
    </if>
    </set>
    where
    <if test="ids!=null and ids.size()>0">
         case_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </if>
</update>

<update id="updateWithCheck">
    update
    team_filing_case
    <set>
        <if test="userName !=null and userName !=''">
            update_by=#{userName},
        </if>
        <if test="checkStatus !=null and checkStatus!=''">
            check_status=#{checkStatus},
        </if>
        <if test="updateTime != null">
            update_time=#{updateTime},
        </if>
        <if test="updateBy != null and updateBy!=''">
            update_by=#{updateBy},
        </if>
        <if test="remark != null and remark!=''">
            remark =#{remark},
        </if>
    </set>

    where
    del_flag=0 and type=0
    <if test="ids!=null and ids.size()>0">
        and case_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </if>

</update>
    <update id="updateCaseStage" parameterType="java.lang.Long">
        update case_manage
        set dispose_stage = '执行立案'
        where case_id = #{id}
    </update>

<update id="updateByCaseIdSelective" parameterType="com.zws.appeal.domain.appeal.FilingCase">
    <!--@mbg.generated-->
    update team_filing_case
    <set>
      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>
      <if test="court != null">
        court = #{court,jdbcType=VARCHAR},
      </if>
      <if test="courtId != null">
        court_id = #{courtId,jdbcType=BIGINT},
      </if>
      <if test="filingNumber != null">
        filing_number = #{filingNumber,jdbcType=VARCHAR},
      </if>
      <if test="filingTime != null">
        filing_time = #{filingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractor != null">
        contractor = #{contractor,jdbcType=VARCHAR},
      </if>
      <if test="clerk != null">
        clerk = #{clerk,jdbcType=VARCHAR},
      </if>
      <if test="checkStatus != null">
        check_status = #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="teamId != null">
          team_id = #{teamId,jdbcType=BIGINT},
      </if>
    </set>
    where case_id = #{caseId,jdbcType=BIGINT}
  </update>

  <select id="selectCaseManages" resultMap="ManageBaseResultMap">
  SELECT
        cm.id,
        cm.case_id,
        cm.contract_no,
        cm.allocated_state,
        cm.case_state,
        cm.settlement_status,
        cm.entrusting_party_id,
        cm.entrusting_party_name,
        cm.product_id,
        cm.product_name,
        cm.batch_num,
        cm.entrusting_case_batch_num,
        cm.outsourcing_team_id,
        cm.outsourcing_team_name,
        cm.client_name,
        cm.client_sex,
        cm.client_idcard,
        cm.client_census_register,
        cm.client_phone,
        cm.client_money,
        cm.client_residual_principal,
        cm.client_overdue_start,
        cm.account_period,
        cm.follow_up_state,
        cm.follow_up_start,
        cm.follow_up_ast,
        cm.entrusting_case_date,
        cm.return_case_date,
        cm.area,
        cm.odv_id,
        cm.odv_name,
        cm.urge_state,
        cm.allocated_time,
        cm.urge_power,
        cm.del_flag,
        cm.create_by,
        cm.create_time,
        cm.update_by,
        cm.update_time,
        cm.client_id_type,
        cm.uid
    from
    case_manage as cm
    LEFT JOIN case_library as cl on (cl.id = cm.case_id and cl.del_flag=0)
    LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
    LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
    LEFT JOIN team_filing_case as tfc on (cm.case_id = tfc.case_id and tfc.del_flag=0 and tfc.type=0 and tfc.team_id = #{teamId})
    <where>
    cm.del_flag=0
    <include refid="searchValue"/>
    </where>
</select>

<select id="selectCaseManageMoneySize" resultType="java.util.Map">
    SELECT
    count(1)             as zongshu,
    sum(cm.client_money) as zongjine
    from
    case_manage as cm
    LEFT JOIN case_library as cl on (cl.id = cm.case_id and cl.del_flag=0)
    LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
    LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
    LEFT JOIN team_label as tl on (cm.label = tl.code and tl.create_id = #{teamId})
    LEFT JOIN team_filing_case as tfc on (cm.case_id = tfc.case_id and tfc.del_flag=0 and tfc.type=0 and tfc.team_id = #{teamId})
    <where>
    cm.del_flag=0
    <include refid="searchValue"/>
    </where>
</select>

<select id="selectCaseStateSize" resultType="java.lang.Integer">
  SELECT
     count(1)
    from
    case_manage as cm
    LEFT JOIN case_library as cl on (cl.id = cm.case_id and cl.del_flag=0)
    LEFT JOIN case_info_loan as cil on (cil.case_id = cl.id and cil.del_flag=0)
    LEFT JOIN asset_manage as am on (am.id = cil.asset_manage_id and am.del_flag=0)
    LEFT JOIN team_label as tl on (cm.label = tl.code and tl.create_id = #{teamId})
    LEFT JOIN team_filing_case as tfc on (cm.case_id = tfc.case_id and tfc.del_flag=0 and tfc.type=0 and tfc.team_id = #{teamId})
    <where>
    cm.del_flag=0
    <include refid="searchValue"/>
    </where>
</select>

</mapper>