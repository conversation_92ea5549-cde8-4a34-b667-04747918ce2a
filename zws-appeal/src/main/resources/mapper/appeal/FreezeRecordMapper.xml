<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.appeal.FreezeRecordMapper">
   <resultMap id="BaseResultMap" type="com.zws.appeal.domain.appeal.FreezeRecord">
    <!--@mbg.generated-->
    <!--@Table team_freeze_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="save_stage" jdbcType="VARCHAR" property="saveStage" />
    <result column="case_id" jdbcType="BIGINT" property="caseId" />
    <result column="court_id" jdbcType="BIGINT" property="courtId" />
    <result column="court" jdbcType="VARCHAR" property="court" />
    <result column="start_freeze" jdbcType="TIMESTAMP" property="startFreeze" />
    <result column="end_freeze" jdbcType="TIMESTAMP" property="endFreeze" />
    <result column="freeze_amount" jdbcType="DECIMAL" property="freezeAmount" />
    <result column="actual_freeze_amount" jdbcType="DECIMAL" property="actualFreezeAmount" />
    <result column="freeze_assets" jdbcType="VARCHAR" property="freezeAssets" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, save_stage, case_id, court_id, court, start_freeze, end_freeze, freeze_amount,
    actual_freeze_amount, freeze_assets, remark, create_time, create_by, create_by_id,
    update_time, update_by, update_by_id, del_flag, team_id
  </sql>

  <sql id="searchValue">
        <if test="teamId != null">
        and cm.outsourcing_team_id = #{teamId}
        and tfr.team_id = #{teamId}
        </if>
        <if test="caseId != null">
        and cl.id =#{caseId}
        </if>
      <if test="clientName != null and clientName != ''">
          and AES_DECRYPT(UNHEX(cl.client_name), #{decryptKey}) LIKE concat ('%',#{clientName},'%')
      </if>
        <if test="court != null and court != ''">
        and tfr.court_id=#{court}
        </if>
        <if test="startDate1 != null and startDate2 != null">
        and tfr.start_freeze>=#{startDate1} and tfr.end_freeze&lt;=#{startDate2}
        </if>
        <if test="applyAmount1 != null and applyAmount2 != null">
        and tfr.freeze_amount >=#{applyAmount1} and tfr.freeze_amount &lt;=#{applyAmount2}
        </if>
        <if test="actualAmount1 != null and actualAmount2 != null">
        and tfr.actual_freeze_amount >=#{actualAmount1} and tfr.actual_freeze_amount &lt;=#{actualAmount2}
        </if>
        <if test="saveStage != null and saveStage != ''">
        and tfr.save_stage = #{saveStage}
        </if>
        <if test="odvId != null and odvId != ''">
        and (cm.odv_id = #{odvId} or cm.mediator_id = #{odvId})
        </if>

        <if test="!allQuery and ids!=null and ids.size()>0">
            and cm.case_id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="!allQuery and freezeIds!=null and freezeIds.size()>0">
            and tfr.id in
            <foreach collection="freezeIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from team_freeze_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from team_freeze_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.FreezeRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_freeze_record (save_stage, case_id, court_id,
      court, start_freeze, end_freeze,
      freeze_amount, actual_freeze_amount, freeze_assets,
      remark, create_time, create_by,
      create_by_id, update_time, update_by,
      update_by_id, del_flag, team_id
      )
    values (#{saveStage,jdbcType=VARCHAR}, #{caseId,jdbcType=BIGINT}, #{courtId,jdbcType=BIGINT},
      #{court,jdbcType=VARCHAR}, #{startFreeze,jdbcType=TIMESTAMP}, #{endFreeze,jdbcType=TIMESTAMP},
      #{freezeAmount,jdbcType=DECIMAL}, #{actualFreezeAmount,jdbcType=DECIMAL}, #{freezeAssets,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR},
      #{createById,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
      #{updateById,jdbcType=BIGINT}, #{delFlag,jdbcType=CHAR}, #{teamId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.appeal.FreezeRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_freeze_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saveStage != null">
        save_stage,
      </if>
      <if test="caseId != null">
        case_id,
      </if>
      <if test="courtId != null">
        court_id,
      </if>
      <if test="court != null">
        court,
      </if>
      <if test="startFreeze != null">
        start_freeze,
      </if>
      <if test="endFreeze != null">
        end_freeze,
      </if>
      <if test="freezeAmount != null">
        freeze_amount,
      </if>
      <if test="actualFreezeAmount != null">
        actual_freeze_amount,
      </if>
      <if test="freezeAssets != null">
        freeze_assets,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="saveStage != null">
        #{saveStage,jdbcType=VARCHAR},
      </if>
      <if test="caseId != null">
        #{caseId,jdbcType=BIGINT},
      </if>
      <if test="courtId != null">
        #{courtId,jdbcType=BIGINT},
      </if>
      <if test="court != null">
        #{court,jdbcType=VARCHAR},
      </if>
      <if test="startFreeze != null">
        #{startFreeze,jdbcType=TIMESTAMP},
      </if>
      <if test="endFreeze != null">
        #{endFreeze,jdbcType=TIMESTAMP},
      </if>
      <if test="freezeAmount != null">
        #{freezeAmount,jdbcType=DECIMAL},
      </if>
      <if test="actualFreezeAmount != null">
        #{actualFreezeAmount,jdbcType=DECIMAL},
      </if>
      <if test="freezeAssets != null">
        #{freezeAssets,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.appeal.FreezeRecord">
    <!--@mbg.generated-->
    update team_freeze_record
    <set>
      <if test="saveStage != null">
        save_stage = #{saveStage,jdbcType=VARCHAR},
      </if>
      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>
      <if test="courtId != null">
        court_id = #{courtId,jdbcType=BIGINT},
      </if>
      <if test="court != null">
        court = #{court,jdbcType=VARCHAR},
      </if>
      <if test="startFreeze != null">
        start_freeze = #{startFreeze,jdbcType=TIMESTAMP},
      </if>
      <if test="endFreeze != null">
        end_freeze = #{endFreeze,jdbcType=TIMESTAMP},
      </if>
      <if test="freezeAmount != null">
        freeze_amount = #{freezeAmount,jdbcType=DECIMAL},
      </if>
      <if test="actualFreezeAmount != null">
        actual_freeze_amount = #{actualFreezeAmount,jdbcType=DECIMAL},
      </if>
      <if test="freezeAssets != null">
        freeze_assets = #{freezeAssets,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.appeal.FreezeRecord">
    <!--@mbg.generated-->
    update team_freeze_record
    set save_stage = #{saveStage,jdbcType=VARCHAR},
      case_id = #{caseId,jdbcType=BIGINT},
      court_id = #{courtId,jdbcType=BIGINT},
      court = #{court,jdbcType=VARCHAR},
      start_freeze = #{startFreeze,jdbcType=TIMESTAMP},
      end_freeze = #{endFreeze,jdbcType=TIMESTAMP},
      freeze_amount = #{freezeAmount,jdbcType=DECIMAL},
      actual_freeze_amount = #{actualFreezeAmount,jdbcType=DECIMAL},
      freeze_assets = #{freezeAssets,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_by_id = #{createById,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      del_flag = #{delFlag,jdbcType=CHAR},
      team_id = #{teamId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into team_freeze_record
    (save_stage, case_id, court_id, court, start_freeze, end_freeze, freeze_amount, actual_freeze_amount,
      freeze_assets, remark, create_time, create_by, create_by_id, update_time, update_by,stage_two_name,
      update_by_id, del_flag, team_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.saveStage,jdbcType=VARCHAR}, #{item.caseId,jdbcType=BIGINT}, #{item.courtId,jdbcType=BIGINT},
        #{item.court,jdbcType=VARCHAR}, #{item.startFreeze,jdbcType=TIMESTAMP}, #{item.endFreeze,jdbcType=TIMESTAMP},
        #{item.freezeAmount,jdbcType=DECIMAL}, #{item.actualFreezeAmount,jdbcType=DECIMAL},
        #{item.freezeAssets,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.createBy,jdbcType=VARCHAR}, #{item.createById,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
        #{item.updateBy,jdbcType=VARCHAR},#{item.stageTwoName}, #{item.updateById,jdbcType=BIGINT}, #{item.delFlag,jdbcType=CHAR},
        #{item.teamId,jdbcType=BIGINT})
    </foreach>
  </insert>

  <select id="selectList" resultType="com.zws.appeal.pojo.appeal.FreezeCasePojo">

    select
    cl.id                       as caseId,
    cl.client_phone             as clientPhone,
    cl.client_name              as clientName,
    cl.client_id_num            as clientIdcard,
    cl.client_census_register   as clientCensusRegister,
    tfr.id                      as id,
    tfr.save_stage              as saveStage,
    tfr.freeze_amount           as freezeAmount,
    tfr.actual_freeze_amount    as actualFreezeAmount,
    tfr.freeze_assets           as freezeAssets,
    tfr.court,
    tfr.start_freeze            as startFreeze,
    tfr.end_freeze              as endFreeze,
    tfr.create_time             as applyTime,
    trr.create_by               as follower,
    trr.create_time             as followUpTime
    from
    team_freeze_record as tfr
    LEFT JOIN case_library as cl on (cl.id = tfr.case_id and cl.del_flag=0)
    LEFT JOIN case_manage  as cm on (cm.case_id = cl.id and cm.del_flag=0)
    LEFT JOIN (
        WITH RankedCases AS (
      SELECT case_id,create_by,create_time ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM asset_time_manage
      where del_flag=0 and origin = 3 )
        SELECT case_id,create_by,create_time FROM RankedCases WHERE rn = 1
    ) as trr on (trr.case_id=cl.id)
    <where>
        tfr.del_flag=0
        <include refid="searchValue"/>
    </where>
      order by  cl.id desc
</select>

    <select id="selectCaseIds" resultType="java.lang.Long">
    select
    cl.id                       as caseId
    from
     team_freeze_record as tfr
    LEFT JOIN case_library as cl on (cl.id = tfr.case_id and cl.del_flag=0)
    LEFT JOIN case_manage  as cm on (cm.case_id = cl.id and cm.del_flag=0)
    <where>
        tfr.del_flag=0
        <include refid="searchValue"/>
        group by cl.id
    </where>
    </select>

    <select id="selectFreezeIds" resultType="java.lang.Long">
    select
    tfr.id                       as id
    from
     team_freeze_record as tfr
    LEFT JOIN case_library as cl on (cl.id = tfr.case_id and cl.del_flag=0)
    LEFT JOIN case_manage  as cm on (cm.case_id = cl.id and cm.del_flag=0)
    <where>
        tfr.del_flag=0
        <include refid="searchValue"/>
    </where>
    </select>

    <select id="selectWithMoney" resultType="java.util.Map">
    select
    count(1)                        as `size` ,
    sum(tfr.actual_freeze_amount)   as money
    from
    team_freeze_record as tfr
    LEFT JOIN case_library as cl on (cl.id = tfr.case_id and cl.del_flag=0)
    LEFT JOIN case_manage  as cm on (cm.case_id = cl.id and cm.del_flag=0)
    <where>
        tfr.del_flag=0
        <include refid="searchValue"/>
    </where>
</select>
    <select id="selectListByCaseId" resultType="com.zws.appeal.domain.appeal.FreezeRecord">
        select tfr.case_id caseId,
               tfr.stage_two_name stageTwoName,
               tfr.create_time createTime,
               tfr.save_stage saveStage
        from team_freeze_record tfr
        where tfr.case_id = #{caseId}
        and tfr.team_id = #{teamId}
        order by tfr.id desc
    </select>


    <update id="updateWithStage">
    update
    team_freeze_record
    <set>
    <if test="saveStage !=null and saveStage!=''">
     save_stage=#{saveStage},
    </if>
    <if test="updateTime != null">
     update_time=#{updateTime},
    </if>
    <if test="updateBy != null and updateBy!=''">
     update_by=#{updateBy},
    </if>
    </set>
    where
    <if test="freezeIds!=null and freezeIds.size()>0">
     id in
    <foreach collection="freezeIds" item="item" open="(" separator="," close=")">
    #{item}
    </foreach>
    </if>
</update>



</mapper>