<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.CaseRecordMapper">


    <select id="selectIdListByMain" resultType="java.lang.Long">
        SELECT car.id
        FROM case_apply_record AS car
        WHERE car.del_flag = '0'
          AND car.operation_type = 0
          AND car.proce = 0
          AND car.team_id = #{teamId}
          and car.apply_state = #{applyState}

    </select>
</mapper>

