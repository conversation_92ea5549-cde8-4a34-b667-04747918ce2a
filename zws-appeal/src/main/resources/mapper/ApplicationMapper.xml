<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.ApplicationMapper">

    <resultMap id="ApplyRecordResultMap" type="com.zws.appeal.domain.ApplyRecord">
        <result column="case_id" property="caseId"/>
        <result column="apply_state" property="applyState"/>
        <result column="apply_date" property="applyDate"/>
        <result column="applicant" property="applicant"/>
        <result column="applicant_id" property="applicantId"/>
        <result column="team_id" property="teamId"/>
        <result column="team_name" property="teamName"/>
        <result column="examine_state" property="examineState"/>
        <result column="examine_time" property="examineTime"/>
        <result column="examine_by" property="examineBy"/>
        <result column="examine_by_id" property="examineById"/>
        <result column="proce_sort" property="proceSort"/>
        <result column="stay_case_time" property="stayCaseTime"/>
        <result column="entrusting_case_batch_num" property="entrustingCaseBatchNum"/>
        <result column="entrusting_case_date" property="entrustingCaseDate"/>
        <result column="return_case_date" property="returnCaseDate"/>
        <result column="operation_type" property="operationType"/>
    </resultMap>

    <resultMap id="RepaymentRecordResultMap" type="com.zws.appeal.domain.record.RepaymentRecord">
        <result column="case_id" property="caseId"/>
        <result column="team_id" property="teamId"/>
        <result column="repayment_date" property="repaymentDate"/>
        <result column="repayment_money" property="repaymentMoney"/>
        <result column="repayment_mode" property="repaymentMode"/>
        <result column="repayment_type" property="repaymentType"/>
        <result column="examine_state" property="examineState"/>
        <result column="registrar_id" property="registrarId"/>
        <result column="registrar" property="registrar"/>
        <result column="create_time" property="createTime"/>
        <result column="proce_sort" property="proceSort"/>
        <result column="entrusting_case_batch_num" property="entrustingCaseBatchNum"/>
        <result column="entrusting_case_date" property="entrustingCaseDate"/>
        <result column="return_case_date" property="returnCaseDate"/>
        <result column="operation_type" property="operationType"/>
    </resultMap>

    <resultMap id="ReductionRecordResultMap" type="com.zws.appeal.domain.record.ReductionRecord">
        <result column="case_id" property="caseId"/>
        <result column="team_id" property="teamId"/>
        <result column="apply_date" property="applyDate"/>
        <result column="applicant" property="applicant"/>
        <result column="applicant_id" property="applicantId"/>
        <result column="reason" property="reason"/>
        <result column="amount_after_deduction" property="amountAfterDeduction"/>
        <result column="state" property="state"/>
        <result column="proce_sort" property="proceSort"/>
        <result column="operation_type" property="operationType"/>
    </resultMap>

    <resultMap id="StagingRecordResultMap" type="com.zws.appeal.domain.record.StagingRecord">
        <result column="case_id" property="caseId"/>
        <result column="team_id" property="teamId"/>
        <result column="apply_date" property="applyDate"/>
        <result column="applicant" property="applicant"/>
        <result column="applicant_id" property="applicantId"/>
        <result column="reason" property="reason"/>
        <result column="examine_state" property="examineState"/>
        <result column="proce_sort" property="proceSort"/>
        <result column="operation_type" property="operationType"/>
    </resultMap>

    <resultMap id="OutsideRecordResultMap" type="com.zws.appeal.domain.record.OutsideRecord">
        <result column="case_id" property="caseId"/>
        <result column="team_id" property="teamId"/>
        <result column="create_by_id" property="createById"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="state_code" property="stateCode"/>
        <result column="state" property="state"/>
        <result column="proce_sort" property="proceSort"/>
        <result column="operation_type" property="operationType"/>
    </resultMap>

    <resultMap id="RetrievalRecordResultMap" type="com.zws.appeal.domain.record.RetrievalRecord">
        <result column="case_id" property="caseId"/>
        <result column="team_id" property="teamId"/>
        <result column="applicant_id" property="applicantId"/>
        <result column="applicant" property="applicant"/>
        <result column="apply_date" property="applyDate"/>
        <result column="examine_state" property="examineState"/>
        <result column="proce_sort" property="proceSort"/>
        <result column="operation_type" property="operationType"/>
    </resultMap>

    <sql id="selectApplyRecordVo">
        select id,
               case_id,
               apply_state,
               apply_date,
               applicant,
               applicant_id,
               reason,
               team_id,
               team_name,
               examine_state,
               examine_time,
               examine_by,
               examine_by_id,
               proce,
               proce_sort,
               stay_case_time,
               entrusting_case_batch_num,
               entrusting_case_date,
               return_case_date,
               operation_type
        from case_apply_record
    </sql>

    <sql id="selectRepaymentRecordVo">
        select id,
               case_id,
               team_id,
               repayment_date,
               repayment_money,
               repayment_mode,
               repayment_type,
               examine_state,
               registrar_id,
               registrar,
               create_time,
               proce,
               proce_sort,
               operation_type
        from case_repayment_record
    </sql>

    <sql id="selectReductionRecordVo">
        select id,
               case_id,
               team_id,
               apply_date,
               applicant,
               applicant_id,
               reason,
               amount_after_deduction,
               state,
               proce,
               proce_sort,
               operation_type
        from case_reduction_record
    </sql>

    <sql id="selectStagingRecordVo">
        select id,
               case_id,
               team_id,
               apply_date,
               applicant,
               applicant_id,
               reason,
               examine_state,
               proce,
               proce_sort,
               operation_type
        from case_staging_record
    </sql>

    <sql id="selectOutsideRecordVo">
        select id,
               case_id,
               team_id,
               state,
               create_by_id,
               create_by,
               create_time,
               proce,
               proce_sort,
               state_code,
               operation_type
        from case_outside_record
    </sql>

    <sql id="selectRetrievalRecordVo">
        select id,
               case_id,
               team_id,
               examine_state,
               applicant_id,
               applicant,
               apply_date,
               proce,
               proce_sort,
               operation_type
        from case_retrieval_record
    </sql>

    <select id="selectApplyRecord" resultMap="ApplyRecordResultMap">
        <include refid="selectApplyRecordVo"></include>
        where del_flag = 0 and apply_state = #{applyState} and team_id = #{teamId} and proce in (0,1)
    </select>

    <select id="selectRepaymentRecord" resultMap="RepaymentRecordResultMap">
        <include refid="selectRepaymentRecordVo"></include>
        where del_flag = 0 and team_id = #{teamId} and proce in (0,1)
    </select>

    <select id="selectReductionRecord" resultMap="ReductionRecordResultMap">
        <include refid="selectReductionRecordVo"></include>
        where del_flag = 0 and team_id = #{teamId} and proce in (0,1)
    </select>

    <select id="selectStagingRecord" resultMap="StagingRecordResultMap">
        <include refid="selectStagingRecordVo"></include>
        where del_flag = 0 and team_id = #{teamId} and proce in (0,1)
    </select>

    <select id="selectOutsideRecord" resultMap="OutsideRecordResultMap">
        <include refid="selectOutsideRecordVo"></include>
        where del_flag = 0 and team_id = #{teamId} and state_code in (0,1)
    </select>

    <select id="selectRetrievalRecord" resultMap="RetrievalRecordResultMap">
        <include refid="selectRetrievalRecordVo"></include>
        where del_flag = 0 and team_id = #{teamId} and proce in (0,1)
    </select>

</mapper>
