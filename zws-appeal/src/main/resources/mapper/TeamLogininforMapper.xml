<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.TeamLogininforMapper">
    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.TeamLogininfor">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="ipaddr" jdbcType="VARCHAR" property="ipaddr"/>
        <result column="status" jdbcType="CHAR" property="status"/>
        <result column="msg" jdbcType="VARCHAR" property="msg"/>
        <result column="access_time" jdbcType="TIMESTAMP" property="accessTime"/>
        <result column="terminal_type" jdbcType="VARCHAR" property="terminalType"/>
        <result column="terminal_version" jdbcType="VARCHAR" property="terminalVersion"/>
        <result column="os" jdbcType="VARCHAR" property="os"/>
        <result column="devicename" jdbcType="VARCHAR" property="devicename"/>
        <result column="login_area" jdbcType="VARCHAR" property="loginArea"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , user_id, user_name, ipaddr, status, msg, access_time, terminal_type, terminal_version,
    os, devicename, login_area
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from team_logininfor
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from team_logininfor
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.appeal.domain.TeamLogininfor" keyProperty="id" useGeneratedKeys="true">
        insert into team_logininfor (id, user_id, user_name,
                                     ipaddr, status, msg, access_time,
                                     terminal_type, terminal_version, os,
                                     devicename, login_area, login_type, team_id, internal_ip, browser_fingerprint)
        values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR},
                #{ipaddr,jdbcType=VARCHAR}, #{status,jdbcType=CHAR}, #{msg,jdbcType=VARCHAR},
                #{accessTime,jdbcType=TIMESTAMP},
                #{terminalType,jdbcType=VARCHAR}, #{terminalVersion,jdbcType=VARCHAR}, #{os,jdbcType=VARCHAR},
                #{devicename,jdbcType=VARCHAR}, #{loginArea,jdbcType=VARCHAR}, #{loginType}, #{teamId}, #{internalIp},
                #{browserFingerprint})
    </insert>
    <insert id="insertSelective" parameterType="com.zws.appeal.domain.TeamLogininfor">
        insert into team_logininfor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="ipaddr != null">
                ipaddr,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="msg != null">
                msg,
            </if>
            <if test="accessTime != null">
                access_time,
            </if>
            <if test="terminalType != null">
                terminal_type,
            </if>
            <if test="terminalVersion != null">
                terminal_version,
            </if>
            <if test="os != null">
                os,
            </if>
            <if test="devicename != null">
                devicename,
            </if>
            <if test="loginArea != null">
                login_area,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="ipaddr != null">
                #{ipaddr,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=CHAR},
            </if>
            <if test="msg != null">
                #{msg,jdbcType=VARCHAR},
            </if>
            <if test="accessTime != null">
                #{accessTime,jdbcType=TIMESTAMP},
            </if>
            <if test="terminalType != null">
                #{terminalType,jdbcType=VARCHAR},
            </if>
            <if test="terminalVersion != null">
                #{terminalVersion,jdbcType=VARCHAR},
            </if>
            <if test="os != null">
                #{os,jdbcType=VARCHAR},
            </if>
            <if test="devicename != null">
                #{devicename,jdbcType=VARCHAR},
            </if>
            <if test="loginArea != null">
                #{loginArea,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.TeamLogininfor">
        update team_logininfor
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="ipaddr != null">
                ipaddr = #{ipaddr,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=CHAR},
            </if>
            <if test="msg != null">
                msg = #{msg,jdbcType=VARCHAR},
            </if>
            <if test="accessTime != null">
                access_time = #{accessTime,jdbcType=TIMESTAMP},
            </if>
            <if test="terminalType != null">
                terminal_type = #{terminalType,jdbcType=VARCHAR},
            </if>
            <if test="terminalVersion != null">
                terminal_version = #{terminalVersion,jdbcType=VARCHAR},
            </if>
            <if test="os != null">
                os = #{os,jdbcType=VARCHAR},
            </if>
            <if test="devicename != null">
                devicename = #{devicename,jdbcType=VARCHAR},
            </if>
            <if test="loginArea != null">
                login_area = #{loginArea,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.TeamLogininfor">
        update team_logininfor
        set user_id          = #{userId,jdbcType=BIGINT},
            user_name        = #{userName,jdbcType=VARCHAR},
            ipaddr           = #{ipaddr,jdbcType=VARCHAR},
            status           = #{status,jdbcType=CHAR},
            msg              = #{msg,jdbcType=VARCHAR},
            access_time      = #{accessTime,jdbcType=TIMESTAMP},
            terminal_type    = #{terminalType,jdbcType=VARCHAR},
            terminal_version = #{terminalVersion,jdbcType=VARCHAR},
            os               = #{os,jdbcType=VARCHAR},
            devicename       = #{devicename,jdbcType=VARCHAR},
            login_area       = #{loginArea,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
