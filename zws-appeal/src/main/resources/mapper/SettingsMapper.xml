<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.SettingsMapper">

    <resultMap id="DeptTreeResult" type="com.zws.appeal.domain.Dept">
        <result property="id" column="id"/>
        <result property="createId" column="create_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="status" column="status"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="founder" column="founder"/>
        <result property="creationtime" column="creationtime"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <resultMap id="RoleTreeResult" type="com.zws.appeal.domain.Role">
        <result property="createId" column="create_id"/>
        <result property="roleName" column="role_name"/>
        <result property="remark" column="remark"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="menuCheckStrictly" column="menu_check_strictly"/>
        <result property="deptCheckStrictly" column="dept_check_strictly"/>
        <result property="status" column="status"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="founder" column="founder"/>
        <result property="creationtime" column="creationtime"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="roleRemarks" column="role_remarks"/>
    </resultMap>

    <resultMap id="EmployeesTreeResult" type="com.zws.appeal.domain.Employees">
        <result property="id" column="id"/>
        <result property="createId" column="create_id"/>
        <result property="departmentId" column="department_id"/>
        <result property="departmentHead" column="department_head"/>
        <result property="roleId" column="role_id"/>
        <result property="employeeName" column="employee_name"/>
        <result property="departments" column="departments"/>
        <result property="loginAccount" column="login_account"/>
        <result property="password" column="password"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="theRole" column="the_role"/>
        <result property="accountStatus" column="account_status"/>
        <result property="workingState" column="working_state"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="founder" column="founder"/>
        <result property="creationtime" column="creationtime"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="employeesWorking" column="employees_working"/>
        <result property="loginDate" column="login_date"/>
        <result property="sipNumber" column="sip_number"/>
        <result property="sipPassword" column="sip_password"/>
        <result property="updatePasswordTime" column="update_password_time"/>
        <result property="departmentHead" column="department_head"/>

    </resultMap>

    <resultMap id="StateTreeResult" type="com.zws.appeal.domain.State">
        <result property="id" column="id"/>
        <result property="createId" column="create_id"/>
        <result property="whitelistStatus" column="whitelist_status"/>
        <result property="informationStatus" column="information_status"/>
        <result property="restrictedState" column="restricted_state"/>
        <result property="settingStatus" column="setting_status"/>
        <result property="authorizationStatus" column="authorization_status"/>
        <result property="authenticationStatus" column="authentication_status"/>
        <result property="exportSettingStatus" column="export_setting_status"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <sql id="selectDeptVo">
        select id,
               create_id,
               parent_id,
               ancestors,
               dept_name,
               order_num,
               status,
               delete_logo,
               founder,
               creationtime,
               modifier,
               modify_time
        from team_dept
    </sql>

    <sql id="selectRoleVo">
        select id,
               create_id,
               role_name,
               remark,
               role_key,
               role_sort,
               data_scope,
               menu_check_strictly,
               dept_check_strictly,
               status,
               delete_logo,
               founder,
               creationtime,
               modifier,
               modify_time,
               role_remarks
        from team_role
    </sql>

    <sql id="selectEmployeesVo">
        select id,
               create_id,
               department_id,
               role_id,
               employee_name,
               departments,
               login_account,
               password,
               phone_number,
               the_role,
               account_status,
               working_state,
               founder,
               creationtime,
               modifier,
               modify_time,
               employees_working,
               login_date,
               sip_number,
               sip_password,
               update_password_time,
               department_head
        from team_employees
    </sql>

    <select id="selectDeptId" resultType="com.zws.appeal.domain.Dept" resultMap="DeptTreeResult">
        <include refid="selectDeptVo"/>
        where id=#{id} and delete_logo=0
    </select>

    <select id="selectDept" resultType="com.zws.appeal.domain.Dept" resultMap="DeptTreeResult">
        <include refid="selectDeptVo"/>
        where delete_logo=0 and create_id = #{createId}
    </select>

    <select id="selectDeptParentId" resultType="com.zws.appeal.domain.Dept" resultMap="DeptTreeResult">
        <include refid="selectDeptVo"/>
        where delete_logo=0 and parent_id=#{parentId}
    </select>

    <select id="selectRole" resultType="com.zws.appeal.domain.Role" resultMap="RoleTreeResult">
        <include refid="selectRoleVo"></include>
        where delete_logo=0 and create_id = #{createId}
    </select>

    <select id="selectRoleId" resultType="com.zws.appeal.domain.Role" resultMap="RoleTreeResult">
        <include refid="selectRoleVo"></include>
        where delete_logo=0 and id=#{id}
    </select>

    <select id="selectRoles" resultType="com.zws.appeal.domain.Role" resultMap="RoleTreeResult">
        <include refid="selectRoleVo"></include>
        <where>
            delete_logo=0
            <if test="roleName != null ">and role_name=#{roleName}</if>
            <if test="id != null ">and create_id=#{id}</if>
        </where>
    </select>

    <select id="selectRolees" resultType="com.zws.appeal.domain.Role" resultMap="RoleTreeResult">
        <include refid="selectRoleVo"></include>
        <where>
            delete_logo=0 and role_name not in ("调诉端主账号")
            <if test="roleName != null ">and role_name=#{roleName}</if>
            <if test="createId != null ">and create_id=#{createId}</if>
        </where>
    </select>

    <select id="selectRoleeDisable" resultType="com.zws.appeal.domain.Role" resultMap="RoleTreeResult">
        <include refid="selectRoleVo"></include>
        <where>
            delete_logo=0 and status = 0 and role_name not in ("调诉端主账号")
            <if test="roleName != null ">and role_name=#{roleName}</if>
            <if test="createId != null ">and create_id=#{createId}</if>
        </where>
    </select>

    <select id="selectState" resultMap="StateTreeResult">
        select id,
               create_id,
               whitelist_status,
               information_status,
               restricted_state,
               setting_status,
               authorization_status,
               authentication_status,
               modifier,
               modify_time
        from team_states
        where create_id = #{createId}
    </select>

    <select id="selectWatermark" resultType="com.zws.appeal.domain.Watermark">
        select id,
               create_id       AS createId,
               watermark_one   AS watermarkOne,
               watermark_two   AS watermarkTwo,
               watermark_three AS watermarkThree,
               watermark_four  AS watermarkFour
        from team_watermark
        where create_id = #{createId}
    </select>

    <select id="selectEmployees" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo=0 and create_id=#{createId}
    </select>

    <select id="selectEmployeesCreateId" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo=0 and create_id=#{createId} and account_status = 0
    </select>

    <select id="selectEmployeesCount" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo = 0
        and employees_working = #{employeesWorking}
        and create_id = #{createId}
    </select>

    <select id="selectEmployeesCreateIdMax" resultType="int">
        select max(employees_working)
        from team_employees
        where delete_logo = 0
          and create_id = #{createId}
    </select>

    <select id="selectEmployeesParentId" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo=0 and department_id=#{parentId} and create_id = #{createId}
    </select>

    <select id="selectLogin" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo=0 and login_account=#{loginAccount}
    </select>

    <select id="selectLoginDate" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo=0 and id=#{userId}
    </select>

    <select id="selectCertification" resultType="com.zws.appeal.domain.Certification">
        select id,
               create_id     AS createId,
               user_id       AS userId,
               identity_card AS identityCard,
               mailbox       AS mailbox,
               state         AS state,
               founder       AS founder,
               creationtime  AS creationtime,
               modifier      AS modifier,
               modify_time   AS modifyTime
        from team_certification
        where create_id = #{createId}
          and user_id = #{userId}
    </select>

    <select id="selectWorking" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo=0 and employees_working=#{employeesWorking} and create_id = #{createId}
    </select>
    <select id="selectByLoginAccount" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo=0 and login_account=#{loginAccount} and create_id = #{createId}
    </select>
    <select id="selectEmployeesLimit" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo=0 and create_id =#{createId} order by id desc LIMIT 0,1
    </select>

    <select id="selectDeptFuzzy" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        <where>
            delete_logo=0 and create_id = #{createId}
            <if test="value != null and value != ''">
                and (employee_name like concat('%', #{value}, '%') or login_account like concat('%', #{value}, '%') or
                phone_number like concat('%', #{value}, '%'))
            </if>
            <if test="departmentId != null ">and department_id=#{departmentId}</if>
            <if test="roleId != null ">and role_id=#{roleId}</if>
            <if test="accountStatus != null ">and account_status = #{accountStatus}</if>
            <if test="workingState != null ">and working_state = #{workingState}</if>
        </where>


    </select>
    <select id="selectEmployeesList" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        select te.id,
        te.create_id,te.department_id,te.role_id, te.employee_name,
        te.departments, te.login_account,
        te.phone_number,te.the_role,te.account_status,te.working_state,
        te.founder,te.creationtime, te.modifier,te.modify_time,te.department_head,
        te.employees_working,te.login_date,te.sip_number,sip.sip_password
        from team_employees as te
        left join call_sip as sip on(te.sip_number=sip.sip_number)
        <where>
            te.delete_logo=0 and te.create_id = #{createId}
            <if test="value != null and value != ''">
                and (te.employee_name like concat('%', #{value}, '%') or te.login_account like concat('%', #{value},
                '%') or
                te.phone_number like concat('%', #{value}, '%'))
            </if>
            <if test="departmentId != null ">and te.department_id=#{departmentId}</if>

            <if test="departmentIds != null and departmentIds.size() > 0">
                and te.department_id in
                <foreach item="item" collection="departmentIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>

            <if test="roleId != null ">and te.role_id=#{roleId}</if>
            <if test="accountStatus != null ">and te.account_status = #{accountStatus}</if>
            <if test="workingState != null ">and te.working_state = #{workingState}</if>
        </where>


    </select>

    <select id="selectUserByCreateId" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo=0 and create_id = #{createId}
    </select>

    <select id="selectEmployeesId" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo=0 and id = #{id}
    </select>
    <select id="selectEmployeesById" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        select te.id,
               te.create_id,
               te.department_id,
               te.role_id,
               te.employee_name,
               te.departments,
               te.login_account,
               te.password,
               te.phone_number,
               te.the_role,
               te.account_status,
               te.working_state,
               te.founder,
               te.creationtime,
               te.modifier,
               te.modify_time,
               te.employees_working,
               te.login_date,
               te.sip_number,
               sip.sip_password,
               sip.seats_type
        from team_employees as te
                 left join call_sip as sip on (te.sip_number = sip.sip_number)
        where te.delete_logo = 0
          and te.id = #{id}
    </select>

    <select id="selectSysUserId" resultType="com.zws.system.api.domain.SysUser">
        select user_id   AS userId,
               dept_id   AS deptId,
               user_name AS userName,
               nick_name AS nickName,
               email     AS email
        from sys_user
        where del_flag = 0
          and user_id = #{id}
    </select>

    <select id="selectDeptFuzzyById" resultType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo=0 and create_id = #{createId} and id = #{userId}
    </select>

    <select id="selectTeamLogininfor" resultType="com.zws.appeal.domain.TeamLogininfor">
        select log.id,
        log.user_id AS userId,
        log.ipaddr,
        log.status,
        log.msg,
        log.access_time AS accessTime,
        log.terminal_type AS terminalType,
        log.terminal_version AS terminalVersion,
        log.os,
        log.devicename,
        log.login_area AS loginArea,
        log.login_type AS loginType
        from team_logininfor AS log
        LEFT JOIN team_employees AS emp ON(emp.id = log.user_id and log.login_type = 1 and emp.delete_logo = 0)
        LEFT JOIN team_create AS cre ON(cre.id = log.team_id and log.login_type = 0 and cre.delete_logo = 0)
        where 1 = 1
        <if test="userId != null ">
            and log.user_id = #{userId}
            and log.login_type = 1
            <if test="name != null and name != ''">
                and emp.employee_name like concat('%', #{name}, '%')
            </if>
        </if>
        <if test="teamId != null ">
            and log.team_id = #{teamId}
            <if test="name != null and name != ''">
                and (emp.employee_name like concat('%', #{name}, '%')
                or cre.cname like concat('%', #{name}, '%')
                )
            </if>
        </if>

        <if test="ipaddr != null and ipaddr != ''">
            and log.ipaddr like concat('%', #{ipaddr}, '%')
        </if>
        <if test="accessTime1 != null">and log.access_time &gt;= #{accessTime1}</if>
        <if test="accessTime2 != null">and log.access_time &lt;= #{accessTime2}</if>
        order by log.access_time desc
    </select>

    <select id="selectEmployeesName" resultType="java.lang.String">
        select employee_name AS employeeName
        from team_employees
        where delete_logo = 0
          and id = #{userId}
    </select>

    <delete id="delectTeamLogininfor" parameterType="java.util.Date">
        delete
        from team_logininfor
        where access_time &lt;= #{accessTime}
    </delete>

    <select id="selectEmployeesRole" parameterType="com.zws.appeal.domain.Employees" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        where delete_logo=0 and create_id = #{createId}
        <if test="roleId != null ">and role_id = #{roleId}</if>
        <if test="theRole != null ">and the_role = #{theRole}</if>
    </select>
    <select id="countDeptHead" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM team_employees AS te
        WHERE te.delete_logo = 0
          and te.working_state = 0
          and te.department_head = 1
          AND te.department_id = #{departmentId}
    </select>
    <select id="selectAccount" resultType="java.lang.String">
        select login_account
        from team_employees
        where login_account = #{account}
        and delete_logo = 0
    </select>
    <select id="selectRecordFile" resultType="java.lang.Long">
        select file_id
        FROM case_record_file
        WHERE record_id = #{id}
    </select>
    <select id="selectRetrievalFileSeparateIdList" resultType="com.zws.appeal.domain.RetrievalFileSeparate">
        select id AS id,
        first_name AS firstName,
        file_route AS fileRoute,
        modify_name AS modifyName
        FROM case_retrieval_file_separate
        WHERE del_flag = 0
        and id in
        <foreach collection="recordFileList" item="recordFile" open="(" close=")" separator=",">
            #{recordFile}
        </foreach>
    </select>


    <insert id="addDept" parameterType="com.zws.appeal.domain.Dept">
        insert into team_dept(create_id, parent_id, ancestors, dept_name, delete_logo, founder, creationtime)
        values (#{createId}, #{parentId}, #{ancestors}, #{deptName}, #{deleteLogo}, #{founder}, #{creationtime})
    </insert>

    <insert id="addRole" parameterType="com.zws.appeal.domain.Role" useGeneratedKeys="true" keyProperty="id">
        insert into team_role(create_id, role_name, remark, role_key, status, delete_logo, founder, creationtime)
        values (#{createId}, #{roleName}, #{remark}, #{roleKey}, #{status}, #{deleteLogo}, #{founder}, #{creationtime})
    </insert>

    <insert id="addEmployees" parameterType="com.zws.appeal.domain.Employees" keyProperty="id" useGeneratedKeys="true">
        insert into team_employees(create_id, employee_name, department_id, role_id, departments, login_account,
                                   password, phone_number, the_role, account_status, working_state, founder,
                                   creationtime, delete_logo, employees_working, update_password_time, department_head)
        values (#{createId}, #{employeeName}, #{departmentId}, #{roleId}, #{departments}, #{loginAccount}, #{password},
                #{phoneNumber}, #{theRole}, #{accountStatus}, #{workingState}, #{founder}, #{creationtime},
                #{deleteLogo}, #{employeesWorking}, #{updatePasswordTime}, #{departmentHead})
    </insert>

    <insert id="addEmployeesBatch" parameterType="com.zws.appeal.domain.Employees">
        insert into team_employees
        (create_id, employee_name, department_id, role_id, departments, login_account,
        password, phone_number, the_role, account_status, working_state, founder,
        creationtime, delete_logo,employees_working,update_password_time)
        values
        <foreach collection="employees" item="list" index="index" separator=",">
            (#{list.createId}, #{list.employeeName}, #{list.departmentId}, #{list.roleId}, #{list.departments},
            #{list.loginAccount}, #{list.password},
            #{list.phoneNumber}, #{list.theRole}, #{list.accountStatus}, #{list.workingState}, #{list.founder},
            #{list.creationtime},
            #{list.deleteLogo},#{list.employeesWorking},#{list.updatePasswordTime})
        </foreach>
    </insert>

    <insert id="addRoleMenu" parameterType="com.zws.appeal.domain.RoleMenu">
        insert into team_role_menu(role_id,menu_id)
        values
        <foreach collection="roleMenus" item="list" index="index" separator=",">
            (#{list.roleId}, #{list.menuId})
        </foreach>
    </insert>

    <update id="updateDept" parameterType="com.zws.appeal.domain.Dept">
        update team_dept
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where id=#{id} and delete_logo=0
    </update>

    <update id="updateRole" parameterType="com.zws.appeal.domain.Role">
        update team_role
        <trim prefix="SET" suffixOverrides=",">
            <if test="roleName != null">role_name = #{roleName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="roleRemarks != null">role_remarks = #{roleRemarks},</if>
            <if test="status != null">status = #{status},</if>
            <if test="roleSort != null">role_sort = #{roleSort},</if>
            <if test="roleKey != null">role_key = #{roleKey},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="menuCheckStrictly != null">menu_check_strictly = #{menuCheckStrictly},</if>
            <if test="deptCheckStrictly != null">dept_check_strictly = #{deptCheckStrictly},</if>
        </trim>
        where id=#{id}
    </update>

    <update id="updateEmployees" parameterType="com.zws.appeal.domain.Employees">
        update team_employees
        <trim prefix="SET" suffixOverrides=",">
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="departmentHead != null">department_head = #{departmentHead},</if>
            <if test="roleId != null">role_id = #{roleId},</if>
            <if test="employeeName != null">employee_name = #{employeeName},</if>
            <if test="departments != null">departments = #{departments},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="theRole != null">the_role = #{theRole},</if>
            <if test="accountStatus != null">account_status = #{accountStatus},</if>
            <if test="workingState != null">working_state = #{workingState},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="employeesWorking != null">employees_working = #{employeesWorking},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="sipNumber != null">sip_number = #{sipNumber},</if>
            <if test="sipPassword != null">sip_password = #{sipPassword},</if>
            <if test="updatePasswordTime != null">update_password_time = #{updatePasswordTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="cancelEmployeesSip">
        update team_employees
        set sip_number   = null,
            sip_password = null
        where sip_number = #{sipNumber}
    </update>
    <update id="updateEmployeesPassWord" parameterType="com.zws.appeal.domain.Employees">
        update team_employees
        <trim prefix="SET" suffixOverrides=",">
            <if test="password != null">password = #{password},</if>
            <if test="updatePasswordTime != null">update_password_time = #{updatePasswordTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteEmployeesRole" parameterType="com.zws.appeal.domain.Employees">
        update team_employees
        set role_id  = null,
            the_role = null
        where id = #{id}
    </update>

    <update id="updateEmployeesRole" parameterType="com.zws.appeal.domain.Employees">
        update team_employees
        <trim prefix="SET" suffixOverrides=",">
            <if test="theRole != null">the_role = #{theRole},</if>
        </trim>
        where role_id = #{roleId} and create_id = #{createId}
    </update>

    <update id="updateEmployeesDept" parameterType="com.zws.appeal.domain.Employees">
        update team_employees
        <trim prefix="SET" suffixOverrides=",">
            <if test="departments != null">departments = #{departments},</if>
        </trim>
        where department_id = #{departmentId} and create_id = #{createId}
    </update>

    <update id="updateAccountStatus" parameterType="com.zws.appeal.domain.Employees">
        <!--<foreach collection="employees" item="list" index="index" open="" close="" separator=";">
            update team_employees
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.departmentId != null">department_id = #{list.departmentId},</if>
                <if test="list.roleId != null">role_id = #{list.roleId},</if>
                <if test="list.employeeName != null">employee_name = #{list.employeeName},</if>
                <if test="list.departments != null">departments = #{list.departments},</if>
                <if test="list.loginAccount != null">login_account = #{list.loginAccount},</if>
                <if test="list.phoneNumber != null">phone_number = #{list.phoneNumber},</if>
                <if test="list.theRole != null">the_role = #{list.theRole},</if>
                <if test="list.accountStatus != null">account_status = #{list.accountStatus},</if>
                <if test="list.workingState != null">working_state = #{list.workingState},</if>
                <if test="list.modifier != null">modifier = #{list.modifier},</if>
                <if test="list.modifyTime != null">modify_time = #{list.modifyTime},</if>
                <if test="list.employeesWorking != null">employees_working = #{list.employeesWorking},</if>
                <if test="list.deleteLogo != null">delete_logo = #{list.deleteLogo},</if>
                <if test="list.password != null">password = #{list.password},</if>
                <if test="list.updatePasswordTime != null">update_password_time = #{list.updatePasswordTime},</if>
            </trim>
            where id=#{list.id}
        </foreach>-->

        update team_employees
        <trim prefix="SET" suffixOverrides=",">
            <if test="departmentId != null">department_id = #{departmentId},</if>
            <if test="roleId != null">role_id = #{roleId},</if>
            <if test="employeeName != null">employee_name = #{employeeName},</if>
            <if test="departments != null">departments = #{departments},</if>
            <if test="loginAccount != null">login_account = #{loginAccount},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="theRole != null">the_role = #{theRole},</if>
            <if test="accountStatus != null">account_status = #{accountStatus},</if>
            <if test="workingState != null">working_state = #{workingState},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="employeesWorking != null">employees_working = #{employeesWorking},</if>
            <if test="deleteLogo != null">delete_logo = #{deleteLogo},</if>
            <if test="password != null">password = #{password},</if>
            <if test="updatePasswordTime != null">update_password_time = #{updatePasswordTime},</if>
        </trim>
        where id=#{id}
    </update>

    <update id="deleteDept" parameterType="com.zws.appeal.domain.Dept">
        update team_dept
        set delete_logo = 1
        where id = #{id}
    </update>

    <update id="deleteRole" parameterType="com.zws.appeal.domain.Role">
        update team_role
        set delete_logo = 1
        where id = #{id}
    </update>

    <update id="deleteEmployees" parameterType="com.zws.appeal.domain.Employees">
        update team_employees
        set delete_logo = 1
        where id = #{id}
    </update>
    <update id="updateDeptHaveHead">
        UPDATE team_dept
        SET have_head=#{haveHead}
        WHERE id = #{departmentId}
    </update>

    <!--    <update id="updateRecoveryCases" parameterType="com.zws.appeal.domain.CaseManage">-->
    <!--        update case_manage-->
    <!--        set odv_id         = #{odvId},-->
    <!--            odv_name       = #{odvName},-->
    <!--            update_by      = #{updateBy},-->
    <!--            update_time    = #{updateTime},-->
    <!--            case_state     = #{caseState},-->
    <!--            allocated_time = #{allocatedTime}-->
    <!--        where odv_id = #{expeditorId}-->
    <!--    </update>-->

    <delete id="deleteRoleMenu" parameterType="int">
        delete
        from team_role_menu
        where role_id = #{roleId}
    </delete>

</mapper>
