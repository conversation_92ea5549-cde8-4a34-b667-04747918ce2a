<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.CollectorMapper">


    <select id="selectProperty" resultType="java.lang.String">
        select DISTINCT am.package_name AS packageName
        from case_manage AS cas
                 LEFT JOIN team_label AS cm ON (cas.outsourcing_team_id = cm.create_id)
                 LEFT JOIN asset_manage as am on (am.batch_num = cas.batch_num)
        where cas.del_flag = 0
          and cas.outsourcing_team_id = #{createId}
          and cas.allocated_state = 1
    </select>
</mapper>
