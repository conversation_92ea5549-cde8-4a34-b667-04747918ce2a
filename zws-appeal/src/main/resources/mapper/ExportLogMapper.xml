<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.ExportLogMapper">
    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.log.ExportLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="export_class" property="exportClass"/>
        <result column="export_type" jdbcType="VARCHAR" property="exportType"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="export_start" jdbcType="VARCHAR" property="exportStart"/>
        <result column="fail_msg" jdbcType="VARCHAR" property="failMsg"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="operation_type" jdbcType="INTEGER" property="operationType"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by_id" jdbcType="BIGINT" property="createById"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_by_id" jdbcType="BIGINT" property="updateById"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , export_type,export_class, file_url, file_name, export_start, fail_msg, team_id, operation_type,
    del_flag, create_by, create_by_id, update_by, update_by_id, update_time,create_time
    </sql>
    <sql id="Base_Column_List_AS">
        ael
        .
        id
        , ael.export_type,ael.export_class, ael.file_url, ael.file_name, ael.export_start, ael.fail_msg, ael.team_id, ael.operation_type,
    ael.del_flag, ael.create_by, ael.create_by_id, ael.update_by, ael.update_by_id, ael.update_time,ael.create_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from asset_export_log
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_AS"/>
        from asset_export_log as ael
        left join team_employees as te on (ael.create_by_id=te.id and ael.operation_type=1)
        <where>
            ael.del_flag='0'
            <if test="exportClass!=null and exportClass!=''">
                and ael.export_class=#{exportClass}
            </if>
            <if test="exportType!=null and exportType!=''">
                and ael.export_type=#{exportType}
            </if>
            <if test="fileName!=null and fileName!=''">
                and ael.file_name like concat('%',#{fileName},'%')
            </if>
            <if test="exportStart!=null and exportStart!=''">
                and ael.export_start=#{exportStart}
            </if>
            <if test="teamId!=null">
                and ael.team_id=#{teamId}
            </if>
            <if test="operationType!=null">
                and ael.operation_type=#{operationType}
            </if>
            <if test="createTime1!=null">
                and ael.create_time &gt;= #{createTime1}
            </if>
            <if test="createTime2!=null">
                and ael.create_time &lt;= #{createTime2}
            </if>
            <if test="params!=null">
                <if test="params.deptIds!=null and params.deptIds.size()>0">
                    and te.department_id in
                    <foreach collection="params.deptIds" item="deptId" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                </if>
                <if test="params.employeesIds!=null and params.employeesIds.size()>0">
                    and te.id in
                    <foreach collection="params.employeesIds" item="employeesId" separator="," open="(" close=")">
                        #{employeesId}
                    </foreach>
                </if>
            </if>

        </where>
        order by create_time desc
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from asset_export_log
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.appeal.domain.log.ExportLog" keyColumn="id" keyProperty="id"
            useGeneratedKeys="true">
        insert into asset_export_log (id, export_type, file_url,
                                      file_name, export_start, fail_msg,
                                      team_id, operation_type, del_flag,
                                      create_by, create_by_id, update_by,
                                      update_by_id, update_time, create_time, export_class)
        values (#{id,jdbcType=BIGINT}, #{exportType,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR},
                #{fileName,jdbcType=VARCHAR}, #{exportStart,jdbcType=VARCHAR}, #{failMsg,jdbcType=VARCHAR},
                #{teamId,jdbcType=BIGINT}, #{operationType,jdbcType=INTEGER}, #{delFlag,jdbcType=CHAR},
                #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT}, #{updateBy,jdbcType=VARCHAR},
                #{updateById,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime}, #{exportClass})
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.log.ExportLog">
        update asset_export_log
        <set>
            <if test="exportType != null">
                export_type = #{exportType,jdbcType=VARCHAR},
            </if>
            <if test="fileUrl != null">
                file_url = #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="exportStart != null">
                export_start = #{exportStart,jdbcType=VARCHAR},
            </if>
            <if test="failMsg != null">
                fail_msg = #{failMsg,jdbcType=VARCHAR},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
            <if test="operationType != null">
                operation_type = #{operationType,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createById != null">
                create_by_id = #{createById,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateById != null">
                update_by_id = #{updateById,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
