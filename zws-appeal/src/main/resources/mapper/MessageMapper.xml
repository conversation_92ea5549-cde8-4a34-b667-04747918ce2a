<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.MessageMapper">

    <resultMap id="MessageCenterResultMap" type="com.zws.appeal.domain.Message.MessageCenter">
        <result column="create_id" property="createId"/>
        <result column="message_type" property="messageType"/>
        <result column="push_mode" property="pushMode"/>
        <result column="push_time" property="pushTime"/>
        <result column="push_crowd_id" property="pushCrowdId"/>
        <result column="push_crowd_name" property="pushCrowdName"/>
        <result column="reminder_mode" property="reminderMode"/>
        <result column="message_title" property="messageTitle"/>
        <result column="message_content" property="messageContent"/>
        <result column="reading_volume" property="readingVolume"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_logo" property="deleteLogo"/>
    </resultMap>

    <resultMap id="UserMessageResultMap" type="com.zws.appeal.domain.Message.UserMessage">
        <result column="create_id" property="createId"/>
        <result column="message_type" property="messageType"/>
        <result column="user_id" property="userId"/>
        <result column="message_title" property="messageTitle"/>
        <result column="message_content" property="messageContent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <sql id="selectMessageCenterVo">
        select id,
               create_id,
               message_type,
               push_mode,
               push_time,
               push_crowd_id,
               push_crowd_name,
               reminder_mode,
               message_title,
               message_content,
               states,
               reading_volume,
               founder,
               creationtime,
               modifier,
               modify_time
        from team_message_center
    </sql>

    <sql id="selectUserMessageVo">
        select id,
               create_id,
               user_id,
               identification,
               message_type,
               message_title,
               message_content,
               create_time,
               update_time,
               del_flag
        from team_user_message
    </sql>

    <select id="selectMessageCenter" resultMap="MessageCenterResultMap">
        <include refid="selectMessageCenterVo"></include>
        where delete_logo = 0 and create_id = #{createId}
        <if test="id != null">and id = #{id}</if>
        order by creationtime desc
    </select>

    <select id="selectMessageCenterId" resultMap="MessageCenterResultMap">
        <include refid="selectMessageCenterVo"></include>
        where delete_logo = 0 and create_id = #{createId} and id = #{id}
    </select>

    <insert id="insertMessageCenter" parameterType="com.zws.appeal.domain.Message.MessageCenter">
        insert into team_message_center(create_id, message_type, push_mode, push_time, push_crowd_id, push_crowd_name,
                                        reminder_mode, message_title,
                                        message_content, states, founder, creationtime, delete_logo)
        values (#{createId}, #{messageType}, #{pushMode}, #{pushTime}, #{pushCrowdId}, #{pushCrowdName},
                #{reminderMode},
                #{messageTitle}, #{messageContent}, #{states}, #{founder}, #{creationtime}, #{deleteLogo})
    </insert>

    <insert id="insertUserMessage" parameterType="com.zws.appeal.domain.Message.UserMessage">
        insert into team_message_center(create_id, user_id, identification, message_type, message_title,
                                        message_content, create_time,
                                        update_time, del_flag)
        values (#{createId}, #{userId}, #{identification}, #{messageType}, #{messageTitle}, #{messageContent},
                #{createTime}, #{updateTime}, #{delFlag})
    </insert>

    <update id="updateMessageCenter" parameterType="com.zws.appeal.domain.Message.MessageCenter">
        update team_message_center
        <trim prefix="SET" suffixOverrides=",">
            <if test="messageTitle != null">message_title = #{messageTitle},</if>
            <if test="messageContent != null">message_content = #{messageContent},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="deleteLogo != null">delete_logo = #{deleteLogo},</if>
        </trim>
        where id = #{id} and create_id = #{createId}
    </update>

</mapper>
