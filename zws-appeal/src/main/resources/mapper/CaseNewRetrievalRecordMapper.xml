<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.CaseNewRetrievalRecordMapper">

    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.CaseNewRetrievalRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="approveId" column="approve_id" jdbcType="BIGINT"/>
            <result property="teamId" column="team_id" jdbcType="BIGINT"/>
            <result property="caseId" column="case_id" jdbcType="BIGINT"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="entrustingCaseBatchNum" column="entrusting_case_batch_num" jdbcType="VARCHAR"/>
            <result property="entrustingCaseDate" column="entrusting_case_date" jdbcType="TIMESTAMP"/>
            <result property="returnCaseDate" column="return_case_date" jdbcType="DATE"/>
            <result property="odvId" column="odv_id" jdbcType="BIGINT"/>
            <result property="odvName" column="odv_name" jdbcType="VARCHAR"/>
            <result property="operationType" column="operation_type" jdbcType="INTEGER"/>
            <result property="matchingResults" column="matching_results" jdbcType="INTEGER"/>
            <result property="canDownload" column="can_download" jdbcType="INTEGER"/>
            <result property="expirationTime" column="expiration_time" jdbcType="TIMESTAMP"/>
            <result property="quantity" column="quantity" jdbcType="INTEGER"/>
            <result property="watermarkFileState" column="watermark_file_state" jdbcType="INTEGER"/>
            <result property="watermarkedFileName" column="watermarked_file_name" jdbcType="VARCHAR"/>
            <result property="watermarkedFilePath" column="watermarked_file_path" jdbcType="VARCHAR"/>
            <result property="fileError" column="file_error" jdbcType="VARCHAR"/>
            <result property="random" column="random" jdbcType="VARCHAR"/>
            <result property="checkFileId" column="check_file_id" jdbcType="VARCHAR"/>
            <result property="archiveFileAddress" column="archive_file_address" jdbcType="VARCHAR"/>
            <result property="state" column="state" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,approve_id,team_id,
        case_id,del_flag,entrusting_case_batch_num,
        entrusting_case_date,return_case_date,odv_id,
        odv_name,operation_type,matching_results,
        can_download,expiration_time,quantity,
        watermark_file_state,watermarked_file_name,watermarked_file_path,
        file_error,random,check_file_id,
        archive_file_address,state,create_time,
        update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from case_new_retrieval_record
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectNotFinishCountByCaseId" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            case_new_retrieval_record AS crr
                LEFT JOIN approve_record AS ar ON ( crr.approve_id = ar.id )
        WHERE
            crr.case_id = #{caseId}
          AND ar.approve_process != 9 and crr.team_id = #{teamId} and ar.applicant_id = #{odvId}
    </select>
    <select id="selectRetrievalRecord" resultType="com.zws.appeal.pojo.myApproval.MyRetrievalRecordUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        zws_ar.approve_state AS examineState,
        zws_ar.applicant AS applicant,
        zws_ar.reason AS reason,
        zws_ar.apply_date AS applyDate,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        zws_ar.examine_time AS examineTime,
        rep.can_download AS canDownload,
        rep.random AS random,
        rep.archive_file_address AS archiveFileAddress,
        rep.watermarked_file_path AS watermarkedFilePath,
        zws_ar.id AS approveId,
        zws_ap.approve_state AS approveStart,
        zws_ap.approve_time AS updateTime,
        cm.outsourcing_team_id as teamId,
        cli.product_name AS productName,
        cli.client_name AS clientName,
        cli.client_id_num AS clientIdcard,

        cas.id AS labelId,
        cas.label_content AS labelContent,
        cas.code AS label
        from ${sqlDataDto.fromData}
        LEFT JOIN case_new_retrieval_record AS rep on(rep.approve_id=zws_ar.id)
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)
        LEFT JOIN team_employees AS te ON (rep.odv_id=te.id)
        LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        LEFT JOIN case_library AS cli ON(rep.case_id=cli.id)

        WHERE ${sqlDataDto.whereData} and rep.del_flag = 0 and rep.team_id = #{dto.teamId}
        <if test="dto.employeesIds!=null and dto.employeesIds.size()>0">
            and te.id in
            <foreach collection="dto.employeesIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.deptIds!=null and dto.deptIds.size()>0">
            and td.id in
            <foreach collection="dto.deptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.ids!=null and dto.ids.size()>0">
            and rep.id in
            <foreach collection="dto.ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>



        <if test="dto.clientName != null ">and cm.client_name=#{dto.clientName}</if>
        <if test="dto.clientIdcard != null ">and cm.client_idcard=#{dto.clientIdcard}</if>

        order by zws_ar.apply_date desc
    </select>
    <select id="selectRetrievalRecordId" resultType="com.zws.appeal.pojo.InheritanceCollection">
        select ret.id AS id,
        ret.case_id AS caseId,
        ret.approve_id AS approveId,
        ret.entrusting_case_batch_num AS entrustingCaseBatchNum,
        zws_ar.approve_state AS examineState,
        zws_ar.examine_time AS updateTime,
        zws_ar.reason AS reason,
        zws_ar.apply_date AS applyDate,
        ret.can_download AS canDownload,
        ret.random AS random,
        ret.watermarked_file_path AS watermarkedFilePath,
        ret.archive_file_address AS archiveFileAddress,
        cli.product_name AS productName,
        cli.client_id_type as clientIdType,
        cm.outsourcing_team_id as teamId,
        cli.client_name AS clientName,
        cli.client_id_num AS clientIdcard
        from ${sqlDataDto.fromData}
        LEFT JOIN case_new_retrieval_record AS ret on(ret.approve_id=zws_ar.id)
        LEFT JOIN case_manage AS cm ON (ret.case_id = cm.case_id and cm.del_flag = 0)
        LEFT JOIN case_library AS cli ON(ret.case_id=cli.id)
        where ${sqlDataDto.whereData} and ret.del_flag = 0

        <if test="dto.caseId != null">and ret.case_id =#{dto.caseId}</if>
        <if test="dto.clientName != null and dto.clientName != ''">and cm.client_name =#{dto.clientName}</if>
        <if test="dto.clientIdcard != null and dto.clientIdcard != ''">and cm.client_idcard =#{dto.clientIdcard}</if>
        order by zws_ar.apply_date desc
    </select>
    <select id="selectRetrievalRecordByTeam"
            resultType="com.zws.appeal.pojo.teamApplication.CreateRetrievalRecordUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.approve_id AS approveId,
        zws_ar.approve_state AS examineState,
        zws_ar.applicant AS applicant,
        zws_ar.reason AS reason,
        zws_ar.apply_date AS applyDate,
        zws_ar.examine_time AS examineTime,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.can_download AS canDownload,
        rep.random AS random,
        rep.archive_file_address AS archiveFileAddress,
        rep.watermarked_file_path AS watermarkedFilePath,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard
        from ${sqlDataDto.fromData}
        LEFT JOIN case_new_retrieval_record AS rep on(rep.approve_id=zws_ar.id)
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_employees AS emp ON (zws_ar.applicant_id = emp.id)
        where ${sqlDataDto.whereData} and rep.del_flag = 0 and rep.team_id = #{dto.createId}
        <if test="dto.deptIds != null and dto.deptIds.size() > 0">
            and emp.department_id in
            <foreach item="item" collection="dto.deptIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="dto.userId != null and dto.userId != ''">and emp.id =#{dto.userId}</if>

        <if test="dto.caseId != null">and rep.case_id =#{dto.caseId}</if>
        <if test="dto.clientName != null and dto.clientName != ''">and cm.client_name =#{dto.clientName}</if>
        <if test="dto.clientIdcard != null and dto.clientIdcard != ''">and cm.client_idcard =#{dto.clientIdcard}</if>

        order by zws_ar.apply_date desc
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from case_new_retrieval_record
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.CaseNewRetrievalRecord" useGeneratedKeys="true">
        insert into case_new_retrieval_record
        ( id,approve_id,team_id
        ,case_id,del_flag,entrusting_case_batch_num
        ,entrusting_case_date,return_case_date,odv_id
        ,odv_name,operation_type,matching_results
        ,can_download,expiration_time,quantity
        ,watermark_file_state,watermarked_file_name,watermarked_file_path
        ,file_error,random,check_file_id
        ,archive_file_address,state,create_time
        ,update_time)
        values (#{id,jdbcType=BIGINT},#{approveId,jdbcType=BIGINT},#{teamId,jdbcType=BIGINT}
        ,#{caseId,jdbcType=BIGINT},#{delFlag,jdbcType=CHAR},#{entrustingCaseBatchNum,jdbcType=VARCHAR}
        ,#{entrustingCaseDate,jdbcType=TIMESTAMP},#{returnCaseDate,jdbcType=DATE},#{odvId,jdbcType=BIGINT}
        ,#{odvName,jdbcType=VARCHAR},#{operationType,jdbcType=INTEGER},#{matchingResults,jdbcType=INTEGER}
        ,#{canDownload,jdbcType=INTEGER},#{expirationTime,jdbcType=TIMESTAMP},#{quantity,jdbcType=INTEGER}
        ,#{watermarkFileState,jdbcType=INTEGER},#{watermarkedFileName,jdbcType=VARCHAR},#{watermarkedFilePath,jdbcType=VARCHAR}
        ,#{fileError,jdbcType=VARCHAR},#{random,jdbcType=VARCHAR},#{checkFileId,jdbcType=VARCHAR}
        ,#{archiveFileAddress,jdbcType=VARCHAR},#{state,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}
        ,#{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.CaseNewRetrievalRecord" useGeneratedKeys="true">
        insert into case_new_retrieval_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="approveId != null">approve_id,</if>
                <if test="teamId != null">team_id,</if>
                <if test="caseId != null">case_id,</if>
                <if test="delFlag != null">del_flag,</if>
                <if test="entrustingCaseBatchNum != null">entrusting_case_batch_num,</if>
                <if test="entrustingCaseDate != null">entrusting_case_date,</if>
                <if test="returnCaseDate != null">return_case_date,</if>
                <if test="odvId != null">odv_id,</if>
                <if test="odvName != null">odv_name,</if>
                <if test="operationType != null">operation_type,</if>
                <if test="matchingResults != null">matching_results,</if>
                <if test="canDownload != null">can_download,</if>
                <if test="expirationTime != null">expiration_time,</if>
                <if test="quantity != null">quantity,</if>
                <if test="watermarkFileState != null">watermark_file_state,</if>
                <if test="watermarkedFileName != null">watermarked_file_name,</if>
                <if test="watermarkedFilePath != null">watermarked_file_path,</if>
                <if test="fileError != null">file_error,</if>
                <if test="random != null">random,</if>
                <if test="checkFileId != null">check_file_id,</if>
                <if test="archiveFileAddress != null">archive_file_address,</if>
                <if test="state != null">state,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="approveId != null">#{approveId,jdbcType=BIGINT},</if>
                <if test="teamId != null">#{teamId,jdbcType=BIGINT},</if>
                <if test="caseId != null">#{caseId,jdbcType=BIGINT},</if>
                <if test="delFlag != null">#{delFlag,jdbcType=CHAR},</if>
                <if test="entrustingCaseBatchNum != null">#{entrustingCaseBatchNum,jdbcType=VARCHAR},</if>
                <if test="entrustingCaseDate != null">#{entrustingCaseDate,jdbcType=TIMESTAMP},</if>
                <if test="returnCaseDate != null">#{returnCaseDate,jdbcType=DATE},</if>
                <if test="odvId != null">#{odvId,jdbcType=BIGINT},</if>
                <if test="odvName != null">#{odvName,jdbcType=VARCHAR},</if>
                <if test="operationType != null">#{operationType,jdbcType=INTEGER},</if>
                <if test="matchingResults != null">#{matchingResults,jdbcType=INTEGER},</if>
                <if test="canDownload != null">#{canDownload,jdbcType=INTEGER},</if>
                <if test="expirationTime != null">#{expirationTime,jdbcType=TIMESTAMP},</if>
                <if test="quantity != null">#{quantity,jdbcType=INTEGER},</if>
                <if test="watermarkFileState != null">#{watermarkFileState,jdbcType=INTEGER},</if>
                <if test="watermarkedFileName != null">#{watermarkedFileName,jdbcType=VARCHAR},</if>
                <if test="watermarkedFilePath != null">#{watermarkedFilePath,jdbcType=VARCHAR},</if>
                <if test="fileError != null">#{fileError,jdbcType=VARCHAR},</if>
                <if test="random != null">#{random,jdbcType=VARCHAR},</if>
                <if test="checkFileId != null">#{checkFileId,jdbcType=VARCHAR},</if>
                <if test="archiveFileAddress != null">#{archiveFileAddress,jdbcType=VARCHAR},</if>
                <if test="state != null">#{state,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.domain.CaseNewRetrievalRecord">
        update case_new_retrieval_record
        <set>
                <if test="approveId != null">
                    approve_id = #{approveId,jdbcType=BIGINT},
                </if>
                <if test="teamId != null">
                    team_id = #{teamId,jdbcType=BIGINT},
                </if>
                <if test="caseId != null">
                    case_id = #{caseId,jdbcType=BIGINT},
                </if>
                <if test="delFlag != null">
                    del_flag = #{delFlag,jdbcType=CHAR},
                </if>
                <if test="entrustingCaseBatchNum != null">
                    entrusting_case_batch_num = #{entrustingCaseBatchNum,jdbcType=VARCHAR},
                </if>
                <if test="entrustingCaseDate != null">
                    entrusting_case_date = #{entrustingCaseDate,jdbcType=TIMESTAMP},
                </if>
                <if test="returnCaseDate != null">
                    return_case_date = #{returnCaseDate,jdbcType=DATE},
                </if>
                <if test="odvId != null">
                    odv_id = #{odvId,jdbcType=BIGINT},
                </if>
                <if test="odvName != null">
                    odv_name = #{odvName,jdbcType=VARCHAR},
                </if>
                <if test="operationType != null">
                    operation_type = #{operationType,jdbcType=INTEGER},
                </if>
                <if test="matchingResults != null">
                    matching_results = #{matchingResults,jdbcType=INTEGER},
                </if>
                <if test="canDownload != null">
                    can_download = #{canDownload,jdbcType=INTEGER},
                </if>
                <if test="expirationTime != null">
                    expiration_time = #{expirationTime,jdbcType=TIMESTAMP},
                </if>
                <if test="quantity != null">
                    quantity = #{quantity,jdbcType=INTEGER},
                </if>
                <if test="watermarkFileState != null">
                    watermark_file_state = #{watermarkFileState,jdbcType=INTEGER},
                </if>
                <if test="watermarkedFileName != null">
                    watermarked_file_name = #{watermarkedFileName,jdbcType=VARCHAR},
                </if>
                <if test="watermarkedFilePath != null">
                    watermarked_file_path = #{watermarkedFilePath,jdbcType=VARCHAR},
                </if>
                <if test="fileError != null">
                    file_error = #{fileError,jdbcType=VARCHAR},
                </if>
                <if test="random != null">
                    random = #{random,jdbcType=VARCHAR},
                </if>
                <if test="checkFileId != null">
                    check_file_id = #{checkFileId,jdbcType=VARCHAR},
                </if>
                <if test="archiveFileAddress != null">
                    archive_file_address = #{archiveFileAddress,jdbcType=VARCHAR},
                </if>
                <if test="state != null">
                    state = #{state,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.appeal.domain.CaseNewRetrievalRecord">
        update case_new_retrieval_record
        set 
            approve_id =  #{approveId,jdbcType=BIGINT},
            team_id =  #{teamId,jdbcType=BIGINT},
            case_id =  #{caseId,jdbcType=BIGINT},
            del_flag =  #{delFlag,jdbcType=CHAR},
            entrusting_case_batch_num =  #{entrustingCaseBatchNum,jdbcType=VARCHAR},
            entrusting_case_date =  #{entrustingCaseDate,jdbcType=TIMESTAMP},
            return_case_date =  #{returnCaseDate,jdbcType=DATE},
            odv_id =  #{odvId,jdbcType=BIGINT},
            odv_name =  #{odvName,jdbcType=VARCHAR},
            operation_type =  #{operationType,jdbcType=INTEGER},
            matching_results =  #{matchingResults,jdbcType=INTEGER},
            can_download =  #{canDownload,jdbcType=INTEGER},
            expiration_time =  #{expirationTime,jdbcType=TIMESTAMP},
            quantity =  #{quantity,jdbcType=INTEGER},
            watermark_file_state =  #{watermarkFileState,jdbcType=INTEGER},
            watermarked_file_name =  #{watermarkedFileName,jdbcType=VARCHAR},
            watermarked_file_path =  #{watermarkedFilePath,jdbcType=VARCHAR},
            file_error =  #{fileError,jdbcType=VARCHAR},
            random =  #{random,jdbcType=VARCHAR},
            check_file_id =  #{checkFileId,jdbcType=VARCHAR},
            archive_file_address =  #{archiveFileAddress,jdbcType=VARCHAR},
            state =  #{state,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByApproveIdSelective">
        update case_new_retrieval_record
        <set>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=INTEGER},
            </if>
            <if test="caseId != null">
                case_id = #{caseId,jdbcType=BIGINT},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="entrustingCaseBatchNum != null">
                entrusting_case_batch_num = #{entrustingCaseBatchNum,jdbcType=VARCHAR},
            </if>
            <if test="entrustingCaseDate != null">
                entrusting_case_date = #{entrustingCaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="returnCaseDate != null">
                return_case_date = #{returnCaseDate,jdbcType=DATE},
            </if>
            <if test="odvId != null">
                odv_id = #{odvId,jdbcType=BIGINT},
            </if>
            <if test="odvName != null">
                odv_name = #{odvName,jdbcType=VARCHAR},
            </if>
            <if test="operationType != null">
                operation_type = #{operationType,jdbcType=INTEGER},
            </if>
            <if test="matchingResults != null">
                matching_results = #{matchingResults,jdbcType=INTEGER},
            </if>
            <if test="canDownload != null">
                can_download = #{canDownload,jdbcType=INTEGER},
            </if>
            <if test="expirationTime != null">
                expiration_time = #{expirationTime,jdbcType=TIMESTAMP},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="watermarkFileState != null">
                watermark_file_state = #{watermarkFileState,jdbcType=INTEGER},
            </if>
            <if test="watermarkedFileName != null">
                watermarked_file_name = #{watermarkedFileName,jdbcType=VARCHAR},
            </if>
            <if test="watermarkedFilePath != null">
                watermarked_file_path = #{watermarkedFilePath,jdbcType=VARCHAR},
            </if>
            <if test="fileError != null">
                file_error = #{fileError,jdbcType=VARCHAR},
            </if>
            <if test="random != null">
                random = #{random,jdbcType=VARCHAR},
            </if>
            <if test="checkFileId != null">
                check_file_id = #{checkFileId,jdbcType=VARCHAR},
            </if>
            <if test="archiveFileAddress != null">
                archive_file_address = #{archiveFileAddress,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where   approve_id = #{approveId,jdbcType=BIGINT}
    </update>
</mapper>
