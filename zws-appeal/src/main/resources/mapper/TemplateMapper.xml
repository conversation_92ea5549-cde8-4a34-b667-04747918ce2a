<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.TemplateMapper">
    <update id="updateTemplateStatus" parameterType="com.zws.common.core.domain.sms.TemplateSms">
        update sms_template
        set examine = #{examine},
            reason  = #{reason}
        where id = #{id}
    </update>

    <select id="selectTemplateById" resultType="com.zws.common.core.domain.sms.TemplateSms">
        select tem.id                   AS id,
               tem.autograph_id         AS autographId,
               aut.autograph_name       AS autographName,
               tem.template_content     AS templateContent,
               tem.created_by_id        AS createdById,
               tem.created_by           AS createdBy,
               tem.created_time         AS createdTime,
               tem.update_by            AS updateBy,
               tem.update_time          AS updateTime,
               tem.sms_channel          AS smsChannel,
               tem.sms_type             AS smsType,
               tem.examine              AS examine,
               tem.reason               AS reason,
               tem.danmi_template_id    AS danmiTemplateId,
               tem.danmi_template_param AS danmiTemplateParam,
               tem.status               AS status,
               tem.allow_max_num        AS allowMaxNum
        from sms_template AS tem
                 LEFT JOIN sms_autograph AS aut ON (aut.id = tem.autograph_id)
        where tem.del_flag = 0
          and tem.id = #{id}
    </select>
    <select id="selectAllNoticeTemplate" resultType="com.zws.common.core.domain.sms.TemplateSms">
        select tem.id                   AS id,
               tem.autograph_id         AS autographId,
               aut.autograph_name       AS autographName,
               tem.template_content     AS templateContent,
               tem.created_by_id        AS createdById,
               tem.created_by           AS createdBy,
               tem.created_time         AS createdTime,
               tem.update_by            AS updateBy,
               tem.update_time          AS updateTime,
               tem.sms_channel          AS smsChannel,
               tem.sms_type             AS smsType,
               tem.examine              AS examine,
               tem.reason               AS reason,
               tem.danmi_template_id    AS danmiTemplateId,
               tem.danmi_template_param AS danmiTemplateParam,
               tem.status               AS status,
               tem.allow_max_num        AS allowMaxNum
        from sms_template AS tem
                 LEFT JOIN sms_autograph AS aut ON (aut.id = tem.autograph_id)
        where tem.del_flag = 0
          and tem.sms_type = 2
          and tem.examine = 1
        order by tem.created_time desc
    </select>


</mapper>
