<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.MyApprovalMapper">

    <resultMap id="BaseResultMap" type="com.zws.appeal.pojo.CaseDetails">
        <result column="del_flag" property="delFlag"/>
        <result column="case_id" property="caseId"/>
        <result column="apply_state" property="applyState"/>
        <result column="apply_date" property="applyDate"/>
        <result column="applicant_id" property="applicantId"/>
        <result column="team_id" property="teamId"/>
        <result column="team_name" property="teamName"/>
        <result column="examine_state" property="examineState"/>
        <result column="examine_time" property="examineTime"/>
        <result column="examine_by" property="examineBy"/>
        <result column="examine_by_id" property="examineById"/>
        <result column="proce_sort" property="proceSort"/>
        <result column="case_state" property="caseState"/>
        <result column="product_name" property="productName"/>
        <result column="entrusting_case_batch_num" property="entrustingCaseBatchNum"/>
        <result column="client_name" property="clientName"/>
        <result column="client_idcard" property="clientIdcard"/>
        <result column="client_census_register" property="clientCensusRegister"/>
        <result column="entrusting_case_date" property="entrustingCaseDate"/>
        <result column="return_case_date" property="returnCaseDate"/>
        <result column="approve_start" property="approveStart"/>
        <result column="approve_time" property="approveTime"/>
        <result column="reviewer" property="reviewer"/>
        <result column="reviewer_id" property="reviewerId"/>
    </resultMap>

    <resultMap type="com.zws.system.api.domain.CaseSignRecord" id="CaseSignRecordResult">
        <result property="id"    column="id"    />
        <result property="teamId"    column="team_id"    />
        <result property="caseId"    column="case_id"    />
        <result property="examineState"    column="examine_state"    />
        <result property="registrarId"    column="registrar_id"    />
        <result property="registrar"    column="registrar"    />
        <result property="batchNum"    column="batch_num"    />
        <result property="modelType"    column="model_type"    />
        <result property="modelName"    column="model_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="proce"    column="proce"    />
        <result property="proceSort"    column="proce_sort"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="updateById"    column="update_by_id"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="invalid"    column="invalid"    />
        <result property="odvId"    column="odv_id"    />
        <result property="odvName"    column="odv_name"    />
        <result property="billId"    column="bill_id"    />
        <result property="operationType"    column="operation_type"    />
        <result property="ycAccountAgency"    column="yc_account_agency"    />
        <result property="whetherOperate"    column="whether_operate"    />
        <result property="importNot"    column="import_not"    />
    </resultMap>

    <!--    <sql id="Base_Column_List">-->
    <!--        car-->
    <!--        .-->
    <!--        id-->
    <!--        , car.case_id, car.apply_state, car.apply_date, car.applicant, car.applicant_id, car.reason, car.team_id, car.team_name,-->
    <!--        car.examine_state, car.examine_time, car.examine_by, car.examine_by_id, car.proce, car.proce_sort, car.del_flag,-->
    <!--        cm.case_state, cm.entrusting_case_batch_num, cm.product_name, cm.client_name, cm.client_idcard, cm.client_census_register,-->
    <!--        cm.entrusting_case_date, cm.return_case_date, ap.approve_start, ap.approve_time, ap.reviewer, ap.reviewer_id-->
    <!--    </sql>-->

    <sql id="Where_Sql">
        <if test="caseId != null ">and rep.case_id=#{caseId}</if>
        <if test="clientName != null ">and cm.client_name=#{clientName}</if>
        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>
        <if test="applicant != null ">and rep.applicant=#{applicant}</if>
        <if test="examineTime1 != null ">and c.approve_time &gt;= #{examineTime1}</if>
        <if test="examineTime2 != null ">and c.approve_time &lt;= #{examineTime2}</if>
        <if test="applyDate1 != null ">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null ">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
    </sql>

    <select id="selectApplyRecord" resultType="com.zws.appeal.pojo.myApproval.myApplyRecordUtils">

        select rep.id AS applyIds,
        rep.case_id AS caseId,
        rep.applicant,
        rep.apply_date AS applyDate,
        rep.reason,
        rep.examine_state AS examineState,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.return_case_date AS returnCaseDate,

        c.id AS approveId,
        c.apply_id AS applyId,
        c.approve_start AS approveStart,
        c.approve_time AS examineTime,
        c.reviewer AS examineBy,

        cm.case_state AS caseState,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_census_register AS clientCensusRegister,
        cm.client_id_type as clientIdType,
        cas.id AS labelId,
        cas.label_content AS labelContent,
        cas.code AS label

        from case_apply_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)
        LEFT JOIN team_employees AS te ON (rep.applicant_id=te.id) <!--催收员id改为申请人id-->
        LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        LEFT JOIN (
        SELECT id, apply_id,approve_start,approve_time,reviewer_id,reviewer FROM
        approve_proce WHERE approve_code=#{approveCode} AND reviewer_id= #{reviewerId}
        <choose>
            <when test="selectAll!=null"></when>
            <otherwise>
                <if test="approveSort!=null">AND approve_sort = #{approveSort}</if>
            </otherwise>
        </choose>
        AND operation_type = #{operationType}
        )AS c ON(rep.id=c.apply_id and c.reviewer_id=#{reviewerId})

        WHERE rep.del_flag = 0 and rep.team_id = #{teamId} and rep.`examine_state` != "已撤销" and rep.apply_state =
        #{applyState}
        <if test="haveHead!=null">
            AND (td.have_head = 0 OR rep.operation_type=0)
        </if>
        <if test="haveHead ==null">
            AND rep.operation_type=1
        </if>
        <if test="employeesIds!=null and employeesIds.size()>0">
            and te.id in
            <foreach collection="employeesIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            and td.id in
            <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="ids!=null and ids.size()>0">
            and rep.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <choose>
            <when test="selectAll!=null">
                and (
                <if test="approveSort!=null and approveSort == 1">
                    (rep.proce =0) OR
                </if>
                (rep.proce =1 AND rep.proce_sort=#{approveSort}-1)
                or (rep.proce in (1,2,3,4,5) and c.reviewer_id=#{reviewerId} )
                )
            </when>
            <otherwise>
                <if test="approveSort!=null">

                    <if test="approveSort==1">
                        and rep.proce =0
                    </if>
                    <if test="approveSort >1 ">
                        and rep.proce =1 and rep.proce_sort=#{approveSort}-1
                    </if>
                    AND c.approve_start IS NULL
                    AND rep.`examine_state` != "已退案关闭" <!--待处理页面可能会出现已退案关闭的数据-->
                </if>
                <if test="approveStart!=null">
                    and c.approve_start=#{approveStart}
                    and c.reviewer_id=#{reviewerId}
                </if>
            </otherwise>
        </choose>

        <!--        <if test="repaymentType != null ">and rep.repayment_type = #{repaymentType}</if>-->

        <if test="examineStateList != null ">
            and rep.examine_state in
            <foreach item="item" collection="examineStateList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <include refid="Where_Sql"/>
        order by rep.apply_date desc
    </select>

    <select id="selectRetrievalRecord" resultType="com.zws.appeal.pojo.myApproval.MyRetrievalRecordUtils">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.examine_state AS examineState,
        rep.applicant AS applicant,
        rep.reason AS reason,
        rep.apply_date AS applyDate,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.examine_time AS examineTime,
        rep.can_download AS canDownload,
        rep.random AS random,
        rep.watermarked_file_path AS watermarkedFilePath,
        rep.archive_file_address AS archiveFileAddress,
        c.id AS approveId,
        c.apply_id AS applyId,
        c.approve_start AS approveStart,
        c.approve_time AS updateTime,

        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,

        cas.id AS labelId,
        cas.label_content AS labelContent,
        cas.code AS label

        FROM case_retrieval_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)
        LEFT JOIN team_employees AS te ON (rep.odv_id=te.id)
        LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        LEFT JOIN (
        SELECT id, apply_id,approve_start,approve_time,reviewer_id FROM
        approve_proce WHERE approve_code=#{approveCode} AND reviewer_id= #{reviewerId}
        <choose>
            <when test="selectAll!=null"></when>
            <otherwise>
                <if test="approveSort!=null">AND approve_sort = #{approveSort}</if>
            </otherwise>
        </choose>
        AND operation_type = #{operationType}
        )AS c ON(rep.id=c.apply_id and c.reviewer_id=#{reviewerId})

        WHERE rep.del_flag = 0 and rep.team_id = #{teamId} and rep.`examine_state` != "已撤销"
        <if test="haveHead!=null">
            AND (td.have_head = 0 OR rep.operation_type=0)
        </if>
        <if test="employeesIds!=null and employeesIds.size()>0">
            and te.id in
            <foreach collection="employeesIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            and td.id in
            <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="ids!=null and ids.size()>0">
            and rep.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <choose>
            <when test="selectAll!=null">
                and (
                <if test="approveSort!=null and approveSort == 1">
                    (rep.proce =0) OR
                </if>
                (rep.proce =1 AND rep.proce_sort=#{approveSort}-1)
                or (rep.proce in (1,2,3,4,5) and c.reviewer_id=#{reviewerId} )
                )
            </when>
            <otherwise>
                <if test="approveSort!=null">

                    <if test="approveSort==1">
                        and rep.proce =0
                    </if>
                    <if test="approveSort >1 ">
                        and rep.proce =1 and rep.proce_sort=#{approveSort}-1
                    </if>
                    AND c.approve_start IS NULL
                </if>
                <if test="approveStart!=null">
                    and c.approve_start=#{approveStart}
                    and c.reviewer_id=#{reviewerId}
                </if>
            </otherwise>
        </choose>

        <if test="examineStateList != null">
            and rep.examine_state in
            <foreach item="item" collection="examineStateList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="examineState != null ">and rep.examine_state=#{examineState}</if>
        <if test="caseId != null ">and rep.case_id=#{caseId}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant=#{applicant}</if>
        <if test="clientName != null ">and cm.client_name=#{clientName}</if>
        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>
        <if test="applyDate1 != null ">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null ">and rep.apply_date &lt;= #{applyDate2}</if>
        order by rep.apply_date desc
    </select>

    <!--    <select id="selectCaseDetails" resultType="com.zws.appeal.pojo.myApproval.myApplyRecordUtils">-->
    <!--        select car.id AS applyIds,-->
    <!--        car.case_id AS caseId,-->
    <!--        car.applicant,-->
    <!--        car.apply_date AS applyDate,-->
    <!--        car.reason,-->
    <!--        car.examine_state AS examineState,-->
    <!--        car.entrusting_case_batch_num AS entrustingCaseBatchNum,-->
    <!--        car.entrusting_case_date AS entrustingCaseDate,-->
    <!--        car.return_case_date AS returnCaseDate,-->

    <!--        ap.id AS approveId,-->
    <!--        ap.apply_id AS applyId,-->
    <!--        ap.approve_start AS approveStart,-->
    <!--        ap.approve_time AS examineTime,-->
    <!--        ap.reviewer AS examineBy,-->

    <!--        cm.case_state AS caseState,-->
    <!--        cm.product_name AS productName,-->
    <!--        cm.client_name AS clientName,-->
    <!--        cm.client_idcard AS clientIdcard,-->
    <!--        cm.client_census_register AS clientCensusRegister,-->

    <!--        cas.id AS labelId,-->
    <!--        cas.label_content AS labelContent,-->
    <!--        cas.code AS label-->

    <!--        from case_apply_record AS car-->
    <!--        LEFT JOIN case_manage AS cm ON(car.case_id=cm.case_id)-->
    <!--        LEFT JOIN approve_proce AS ap ON(car.id = ap.apply_id)-->
    <!--        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)-->
    <!--        where car.del_flag = 0 and car.team_id = #{teamId} and ap.approve_code = #{approveCode}-->
    <!--        and car.apply_state = #{applyState}-->

    <!--        <if test="approveStart != null ">and ap.approve_start = #{approveStart}</if>-->

    <!--        <if test="reviewerId != null ">and ap.reviewer_id = #{reviewerId}</if>-->

    <!--        <if test="examineStateList != null ">-->
    <!--            and car.examine_state in-->
    <!--            <foreach item="item" collection="examineStateList" separator="," open="(" close=")" index="">-->
    <!--                #{item}-->
    <!--            </foreach>-->
    <!--        </if>-->

    <!--        <include refid="Where_Sql"/>-->
    <!--        order by car.apply_date desc-->
    <!--    </select>-->
    <!--    &lt;!&ndash;我的审批（停催/留案/退案）&ndash;&gt;-->
    <!--    <select id="selectApplyRecord" resultType="com.zws.appeal.pojo.myApproval.myApplyRecordUtils">-->
    <!--        select car.id AS applyIds,-->
    <!--        car.case_id AS caseId,-->
    <!--        car.applicant,-->
    <!--        car.apply_date AS applyDate,-->
    <!--        car.reason,-->
    <!--        car.examine_state AS examineState,-->
    <!--        car.examine_time AS examineTime,-->
    <!--        car.examine_by AS examineBy,-->
    <!--        car.entrusting_case_batch_num AS entrustingCaseBatchNum,-->
    <!--        car.entrusting_case_date AS entrustingCaseDate,-->
    <!--        car.return_case_date AS returnCaseDate,-->


    <!--        cm.case_state AS caseState,-->

    <!--        cm.product_name AS productName,-->
    <!--        cm.client_name AS clientName,-->
    <!--        cm.client_idcard AS clientIdcard,-->
    <!--        cm.client_census_register AS clientCensusRegister,-->

    <!--        cas.id AS labelId,-->
    <!--        cas.label_content AS labelContent,-->
    <!--        cas.code AS label-->

    <!--        from case_apply_record AS car-->
    <!--        LEFT JOIN case_manage AS cm ON(car.case_id=cm.case_id)-->
    <!--        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)-->
    <!--        where car.del_flag = 0 and car.team_id = #{teamId} and car.apply_state = #{applyState}-->


    <!--        <if test="proceSort != null ">and car.proce_sort = #{proceSort}</if>-->

    <!--        <if test="proce != null ">and car.proce = #{proce}</if>-->

    <!--        <if test="examineStateList != null ">-->
    <!--            and car.examine_state in-->
    <!--            <foreach item="item" collection="examineStateList" separator="," open="(" close=")" index="">-->
    <!--                #{item}-->
    <!--            </foreach>-->
    <!--        </if>-->
    <!--        <include refid="Where_Sql"/>-->
    <!--        order by car.apply_date desc-->
    <!--    </select>-->

    <!--我的审批（回款）-->
    <!--    <select id="selectRepaymentRecord" resultType="com.zws.appeal.pojo.myApproval.myRepaymentRecordUtils">-->
    <!--        select rep.id AS applyIds,-->
    <!--        rep.case_id AS caseId,-->
    <!--        rep.registrar,-->
    <!--        rep.create_time AS createTime,-->
    <!--        rep.repayment_date AS repaymentDate,-->
    <!--        rep.repayment_money AS repaymentMoney,-->
    <!--        rep.repayment_mode AS repaymentMode,-->
    <!--        rep.repayment_type AS repaymentType,-->
    <!--        rep.examine_state AS examineState,-->
    <!--        rep.repayment_proof AS repaymentProof,-->
    <!--        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,-->

    <!--        cm.case_state AS caseState,-->
    <!--        cm.product_name AS productName,-->
    <!--        cm.client_name AS clientName,-->
    <!--        cm.client_idcard AS clientIdcard,-->
    <!--        cm.client_census_register AS clientCensusRegister,-->
    <!--        cm.client_money AS clientMoney,-->

    <!--        cas.id AS labelId,-->
    <!--        cas.label_content AS labelContent,-->
    <!--        cas.code AS label-->

    <!--        from case_repayment_record AS rep-->
    <!--        LEFT JOIN case_manage AS cm ON (rep.case_id = cm.case_id)-->
    <!--        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)-->
    <!--        where rep.del_flag = 0 and rep.team_id = #{teamId}-->

    <!--        <if test="proce != null ">and rep.proce = #{proce}</if>-->
    <!--        <if test="proceSort != null ">and rep.proce_sort = #{proceSort}</if>-->


    <!--        <if test="examineStateList != null ">-->
    <!--            and rep.examine_state in-->
    <!--            <foreach item="item" collection="examineStateList" separator="," open="(" close=")" index="">-->
    <!--                #{item}-->
    <!--            </foreach>-->
    <!--        </if>-->
    <!--        <if test="repaymentType != null ">and rep.repayment_type = #{repaymentType}</if>-->
    <!--        <if test="caseId != null ">and rep.case_id=#{caseId}</if>-->
    <!--        <if test="clientName != null ">and cm.client_name=#{clientName}</if>-->
    <!--        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>-->
    <!--        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>-->
    <!--        <if test="registrar != null ">and rep.registrar=#{registrar}</if>-->
    <!--        &lt;!&ndash;        <if test="updateTime1 != null ">and ap.approve_time &gt;= #{updateTime1}</if>&ndash;&gt;-->
    <!--        &lt;!&ndash;        <if test="updateTime2 != null ">and ap.approve_time &lt;= #{updateTime2}</if>&ndash;&gt;-->
    <!--        <if test="createTime1 != null ">and rep.create_time &gt;= #{createTime1}</if>-->
    <!--        <if test="createTime2 != null ">and rep.create_time &lt;= #{createTime2}</if>-->
    <!--        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>-->
    <!--        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>-->
    <!--        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>-->
    <!--        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>-->
    <!--        order by rep.create_time desc-->
    <!--    </select>-->

    <!--    <select id="selectRepaymentRecordProce" resultType="com.zws.appeal.pojo.myApproval.myRepaymentRecordUtils">-->
    <!--        select rep.id AS applyIds,-->
    <!--        rep.case_id AS caseId,-->
    <!--        rep.registrar,-->
    <!--        rep.create_time AS createTime,-->
    <!--        rep.repayment_date AS repaymentDate,-->
    <!--        rep.repayment_money AS repaymentMoney,-->
    <!--        rep.repayment_mode AS repaymentMode,-->
    <!--        rep.repayment_type AS repaymentType,-->
    <!--        rep.examine_state AS examineState,-->
    <!--        rep.update_time AS updateTime,-->
    <!--        rep.update_by AS updateBy,-->
    <!--        rep.repayment_proof AS repaymentProof,-->
    <!--        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,-->

    <!--        ap.id AS approveId,-->
    <!--        ap.apply_id AS applyId,-->
    <!--        ap.approve_start AS approveStart,-->

    <!--        cm.case_state AS caseState,-->
    <!--        cm.product_name AS productName,-->
    <!--        cm.client_name AS clientName,-->
    <!--        cm.client_idcard AS clientIdcard,-->
    <!--        cm.client_census_register AS clientCensusRegister,-->
    <!--        cm.client_money AS clientMoney,-->

    <!--        cas.id AS labelId,-->
    <!--        cas.label_content AS labelContent,-->
    <!--        cas.code AS label-->

    <!--        from case_repayment_record AS rep-->
    <!--        LEFT JOIN case_manage AS cm ON (rep.case_id = cm.case_id)-->
    <!--        LEFT JOIN approve_proce AS ap ON(rep.id = ap.apply_id)-->
    <!--        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)-->
    <!--        where rep.del_flag = 0 and rep.team_id = #{teamId} and ap.approve_code = #{approveCode}-->
    <!--        <if test="approveStart != null ">and ap.approve_start = #{approveStart}</if>-->
    <!--        <if test="reviewerId != null ">and ap.reviewer_id = #{reviewerId}</if>-->
    <!--        <if test="repaymentType != null ">and rep.repayment_type = #{repaymentType}</if>-->


    <!--        <if test="examineStateList != null">-->
    <!--            and rep.examine_state in-->
    <!--            <foreach item="item" collection="examineStateList" separator="," open="(" close=")" index="">-->
    <!--                #{item}-->
    <!--            </foreach>-->
    <!--        </if>-->
    <!--        <if test="caseId != null ">and rep.case_id=#{caseId}</if>-->
    <!--        <if test="clientName != null ">and cm.client_name=#{clientName}</if>-->
    <!--        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>-->
    <!--        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>-->
    <!--        <if test="registrar != null ">and rep.registrar=#{registrar}</if>-->
    <!--        <if test="updateTime1 != null ">and ap.approve_time &gt;= #{updateTime1}</if>-->
    <!--        <if test="updateTime2 != null ">and ap.approve_time &lt;= #{updateTime2}</if>-->
    <!--        <if test="createTime1 != null ">and rep.create_time &gt;= #{createTime1}</if>-->
    <!--        <if test="createTime2 != null ">and rep.create_time &lt;= #{createTime2}</if>-->
    <!--        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>-->
    <!--        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>-->
    <!--        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>-->
    <!--        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>-->
    <!--        order by rep.create_time desc-->
    <!--    </select>-->

    <select id="selectToBeReviewd" resultType="com.zws.appeal.pojo.myApproval.myRepaymentRecordUtils">

        select rep.id AS applyIds,
        rep.case_id AS caseId,
        rep.registrar,
        rep.create_time AS createTime,
        rep.repayment_date AS repaymentDate,
        rep.repayment_money AS repaymentMoney,
        rep.repayment_mode AS repaymentMode,
        rep.repayment_type AS repaymentType,
        rep.examine_state AS examineState,
        rep.update_by AS updateBy,
        rep.repayment_proof AS repaymentProof,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        c.id AS approveId,
        c.apply_id AS applyId,
        c.approve_start AS approveStart,
        c.approve_time AS updateTime,
        cm.case_state AS caseState,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_census_register AS clientCensusRegister,
        cm.client_money AS clientMoney,
        cm.client_id_type as clientIdType,
        cas.id AS labelId,
        cas.label_content AS labelContent,
        cas.code AS label
        FROM case_repayment_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)
        LEFT JOIN team_employees AS te ON (rep.odv_id=te.id)
        LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        LEFT JOIN (
        SELECT id, apply_id,approve_start,approve_time,reviewer_id FROM
        approve_proce WHERE approve_code=#{approveCode} AND reviewer_id= #{reviewerId}
        <choose>
            <when test="selectAll!=null"></when>
            <otherwise>
                <if test="approveSort!=null">AND approve_sort = #{approveSort}</if>
            </otherwise>
        </choose>
        AND operation_type = #{operationType}
        )AS c ON(rep.id=c.apply_id and c.reviewer_id=#{reviewerId})

        WHERE rep.del_flag = 0 and rep.team_id = #{teamId} and rep.`examine_state` != "已撤销"
        <if test="haveHead!=null">
            AND (td.have_head = 0 OR rep.operation_type in (0,1) )
        </if>
        <if test="employeesIds!=null and employeesIds.size()>0">
            and te.id in
            <foreach collection="employeesIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            and td.id in
            <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="ids!=null and ids.size()>0">
            and rep.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <choose>
            <when test="selectAll!=null">
                and (
                <if test="approveSort!=null and approveSort == 1">
                    (rep.proce =0) OR
                </if>
                (rep.proce =1 AND rep.proce_sort=#{approveSort}-1)
                or (rep.proce in (1,2,3,4,5) and c.reviewer_id=#{reviewerId} )
                )
            </when>
            <otherwise>
                <if test="approveSort!=null">

                    <if test="approveSort==1">
                        and rep.proce =0
                    </if>
                    <if test="approveSort >1 ">
                        and rep.proce =1 and rep.proce_sort=#{approveSort}-1
                    </if>
                    AND c.approve_start IS NULL
                </if>
                <if test="approveStart!=null">
                    and c.approve_start=#{approveStart}
                    and c.reviewer_id=#{reviewerId}
                </if>
            </otherwise>
        </choose>

        <if test="repaymentType != null ">and rep.repayment_type = #{repaymentType}</if>

        <if test="examineStateList != null">
            and rep.examine_state in
            <foreach item="item" collection="examineStateList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="caseId != null ">and rep.case_id=#{caseId}</if>
        <if test="applicant != null and applicant != ''">and rep.registrar=#{applicant}</if>
        <if test="clientName != null ">and cm.client_name=#{clientName}</if>
        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>
        <if test="registrar != null ">and rep.registrar=#{registrar}</if>
        <if test="updateTime1 != null ">and c.approve_time &gt;= #{updateTime1}</if>
        <if test="updateTime2 != null ">and c.approve_time &lt;= #{updateTime2}</if>
        <if test="createTime1 != null ">and rep.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null ">and rep.create_time &lt;= #{createTime2}</if>
        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by rep.create_time desc
    </select>

    <!--我的审批（减免）-->
    <!--    <select id="selectReductionRecord" resultType="com.zws.appeal.pojo.myApproval.myReductionRecordUtils">-->
    <!--        select rep.id AS applyIds,-->
    <!--        rep.case_id AS caseId,-->
    <!--        rep.applicant,-->
    <!--        rep.apply_date AS applyDate,-->
    <!--        rep.reason AS reason,-->
    <!--        rep.amount_after_deduction AS amountAfterDeduction,-->
    <!--        rep.update_time AS updateTime,-->
    <!--        rep.state AS state,-->
    <!--        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,-->
    <!--        rep.entrusting_case_date AS entrustingCaseDate,-->
    <!--        rep.return_case_date AS returnCaseDate,-->

    <!--        cm.case_state AS caseState,-->
    <!--        cm.product_name AS productName,-->
    <!--        cm.client_name AS clientName,-->
    <!--        cm.client_idcard AS clientIdcard,-->
    <!--        cm.client_census_register AS clientCensusRegister,-->

    <!--        cas.id AS labelId,-->
    <!--        cas.label_content AS labelContent,-->
    <!--        cas.code AS label-->

    <!--        from case_reduction_record AS rep-->
    <!--        LEFT JOIN case_manage AS cm ON (rep.case_id = cm.case_id)-->
    <!--        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)-->
    <!--        where rep.del_flag = 0 and rep.team_id = #{teamId}-->

    <!--        <if test="proce != null ">and rep.proce = #{proce}</if>-->
    <!--        <if test="proceSort != null ">and rep.proce_sort = #{proceSort}</if>-->

    <!--        <if test="stateList != null ">-->
    <!--            and rep.state in-->
    <!--            <foreach item="item" collection="stateList" separator="," open="(" close=")" index="">-->
    <!--                #{item}-->
    <!--            </foreach>-->
    <!--        </if>-->
    <!--        <if test="caseId != null ">and rep.case_id=#{caseId}</if>-->
    <!--        <if test="clientName != null ">and cm.client_name=#{clientName}</if>-->
    <!--        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>-->
    <!--        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>-->
    <!--        <if test="registrar != null ">and rep.applicant=#{applicant}</if>-->
    <!--        &lt;!&ndash;        <if test="updateTime1 != null ">and ap.approve_time &gt;= #{updateTime1}</if>&ndash;&gt;-->
    <!--        &lt;!&ndash;        <if test="updateTime2 != null ">and ap.approve_time &lt;= #{updateTime2}</if>&ndash;&gt;-->
    <!--        <if test="applyDate1 != null ">and rep.apply_date &gt;= #{applyDate1}</if>-->
    <!--        <if test="applyDate2 != null ">and rep.apply_date &lt;= #{applyDate2}</if>-->
    <!--        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>-->
    <!--        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>-->
    <!--        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>-->
    <!--        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>-->
    <!--        order by rep.apply_date desc-->
    <!--    </select>-->

    <!--    <select id="selectReductionRecordProce" resultType="com.zws.appeal.pojo.myApproval.myReductionRecordUtils">-->
    <!--        select rep.id AS applyIds,-->
    <!--        rep.case_id AS caseId,-->
    <!--        rep.applicant,-->
    <!--        rep.apply_date AS applyDate,-->
    <!--        rep.reason AS reason,-->
    <!--        rep.amount_after_deduction AS amountAfterDeduction,-->
    <!--        rep.update_time AS updateTime,-->
    <!--        rep.state AS state,-->
    <!--        rep.entrusting_case_date AS entrustingCaseDate,-->
    <!--        rep.return_case_date AS returnCaseDate,-->
    <!--        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,-->

    <!--        ap.approve_start AS approveStart,-->
    <!--        ap.approve_time AS approveTime,-->

    <!--        cm.case_state AS caseState,-->
    <!--        cm.product_name AS productName,-->
    <!--        cm.client_name AS clientName,-->
    <!--        cm.client_idcard AS clientIdcard,-->
    <!--        cm.client_census_register AS clientCensusRegister,-->

    <!--        cas.id AS labelId,-->
    <!--        cas.label_content AS labelContent,-->
    <!--        cas.code AS label-->

    <!--        from case_reduction_record AS rep-->
    <!--        LEFT JOIN case_manage AS cm ON (rep.case_id = cm.case_id)-->
    <!--        LEFT JOIN approve_proce AS ap ON(rep.id = ap.apply_id)-->
    <!--        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)-->
    <!--        where rep.del_flag = 0 and rep.team_id = #{teamId} and ap.approve_code = #{approveCode}-->
    <!--        <if test="approveStart != null ">and ap.approve_start = #{approveStart}</if>-->

    <!--        <if test="reviewerId != null ">and ap.reviewer_id = #{reviewerId}</if>-->

    <!--        <if test="examineStateList != null">-->
    <!--            and rep.examine_state in-->
    <!--            <foreach item="item" collection="examineStateList" separator="," open="(" close=")" index="">-->
    <!--                #{item}-->
    <!--            </foreach>-->
    <!--        </if>-->

    <!--        <if test="stateList != null ">-->
    <!--            and rep.state in-->
    <!--            <foreach item="item" collection="stateList" separator="," open="(" close=")" index="">-->
    <!--                #{item}-->
    <!--            </foreach>-->
    <!--        </if>-->
    <!--        <if test="caseId != null ">and rep.case_id=#{caseId}</if>-->
    <!--        <if test="clientName != null ">and cm.client_name=#{clientName}</if>-->
    <!--        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>-->
    <!--        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>-->
    <!--        <if test="registrar != null ">and rep.applicant=#{applicant}</if>-->
    <!--        <if test="updateTime1 != null ">and ap.approve_time &gt;= #{updateTime1}</if>-->
    <!--        <if test="updateTime2 != null ">and ap.approve_time &lt;= #{updateTime2}</if>-->
    <!--        <if test="applyDate1 != null ">and rep.apply_date &gt;= #{applyDate1}</if>-->
    <!--        <if test="applyDate2 != null ">and rep.apply_date &lt;= #{applyDate2}</if>-->
    <!--        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>-->
    <!--        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>-->
    <!--        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>-->
    <!--        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>-->
    <!--        order by rep.apply_date desc-->
    <!--    </select>-->

    <select id="selectReductionRecord" resultType="com.zws.appeal.pojo.myApproval.myReductionRecordUtils">

        select rep.id AS applyIds,
        rep.case_id AS caseId,
        rep.applicant,
        rep.apply_date AS applyDate,
        rep.reason AS reason,
        rep.amount_after_deduction AS amountAfterDeduction,
        rep.update_time AS updateTime,
        rep.state AS state,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.remaining_due AS remainingDue,
        rep.after_reduction_date AS afterReductionDate,
        c.approve_start AS approveStart,
        c.approve_time AS approveTime,

        cm.case_state AS caseState,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_census_register AS clientCensusRegister,
        cm.client_id_type as clientIdType,
        cas.id AS labelId,
        cas.label_content AS labelContent,
        cas.code AS label

        from case_reduction_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)
        LEFT JOIN team_employees AS te ON (rep.odv_id=te.id)
        LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        LEFT JOIN (
        SELECT id, apply_id,approve_start,approve_time,reviewer_id FROM
        approve_proce WHERE approve_code=#{approveCode} AND reviewer_id= #{reviewerId}
        <choose>
            <when test="selectAll!=null"></when>
            <otherwise>
                <if test="approveSort!=null">AND approve_sort = #{approveSort}</if>
            </otherwise>
        </choose>
        AND operation_type = #{operationType}
        )AS c ON(rep.id=c.apply_id and c.reviewer_id=#{reviewerId})

        WHERE rep.del_flag = 0 and rep.team_id = #{teamId} and rep.`state` != "已撤销"
        <if test="haveHead!=null">
            AND (td.have_head = 0 OR rep.operation_type=0)
        </if>
        <if test="employeesIds!=null and employeesIds.size()>0">
            and te.id in
            <foreach collection="employeesIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            and td.id in
            <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="ids!=null and ids.size()>0">
            and rep.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <choose>
            <when test="selectAll!=null">
                and (
                <if test="approveSort!=null and approveSort == 1">
                    (rep.proce =0) OR
                </if>
                (rep.proce =1 AND rep.proce_sort=#{approveSort}-1)
                or (rep.proce in (1,2,3,4,5) and c.reviewer_id=#{reviewerId} )
                )
            </when>
            <otherwise>
                <if test="approveSort!=null">

                    <if test="approveSort==1">
                        and rep.proce =0
                    </if>
                    <if test="approveSort >1 ">
                        and rep.proce =1 and rep.proce_sort=#{approveSort}-1
                    </if>
                    AND c.approve_start IS NULL
                </if>
                <if test="approveStart!=null">
                    and c.approve_start=#{approveStart}
                    and c.reviewer_id=#{reviewerId}
                </if>
            </otherwise>
        </choose>

        <!--        <if test="repaymentType != null ">and rep.repayment_type = #{repaymentType}</if>-->

        <if test="stateList != null ">
            and rep.state in
            <foreach item="item" collection="stateList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="caseId != null ">and rep.case_id=#{caseId}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant=#{applicant}</if>
        <if test="clientName != null ">and cm.client_name=#{clientName}</if>
        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>
        <if test="registrar != null ">and rep.applicant=#{applicant}</if>
        <if test="updateTime1 != null ">and c.approve_time &gt;= #{updateTime1}</if>
        <if test="updateTime2 != null ">and c.approve_time &lt;= #{updateTime2}</if>
        <if test="applyDate1 != null ">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null ">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by rep.apply_date desc
    </select>

    <!--我的审批(分期还款)-->
    <!--    <select id="selectStagingRecord" resultType="com.zws.appeal.pojo.myApproval.MyStagingRecordUtils">-->
    <!--        select rep.id AS applyIds,-->
    <!--        rep.case_id AS caseId,-->
    <!--        rep.applicant,-->
    <!--        rep.apply_date AS applyDate,-->
    <!--        rep.staging_num AS stagingNum,-->
    <!--        rep.repayment_date AS repaymentDate,-->
    <!--        rep.repayment_monthly AS repaymentMonthly,-->
    <!--        rep.examine_state AS examineState,-->
    <!--        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,-->
    <!--        rep.entrusting_case_date AS entrustingCaseDate,-->
    <!--        rep.return_case_date AS returnCaseDate,-->

    <!--        cm.case_state AS caseState,-->
    <!--        cm.product_name AS productName,-->
    <!--        cm.client_name AS clientName,-->
    <!--        cm.client_idcard AS clientIdcard,-->
    <!--        cm.client_census_register AS clientCensusRegister,-->

    <!--        cas.id AS labelId,-->
    <!--        cas.label_content AS labelContent,-->
    <!--        cas.code AS label-->

    <!--        from case_staging_record AS rep-->
    <!--        LEFT JOIN case_manage AS cm ON (rep.case_id = cm.case_id)-->
    <!--        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)-->
    <!--        where rep.del_flag = 0 and rep.team_id = #{teamId}-->

    <!--        <if test="proce != null ">and rep.proce = #{proce}</if>-->
    <!--        <if test="proceSort != null ">and rep.proce_sort = #{proceSort}</if>-->

    <!--        <if test="examineStateList != null ">-->
    <!--            and rep.examine_state in-->
    <!--            <foreach item="item" collection="examineStateList" separator="," open="(" close=")" index="">-->
    <!--                #{item}-->
    <!--            </foreach>-->
    <!--        </if>-->

    <!--        <if test="caseId != null ">and rep.case_id=#{caseId}</if>-->
    <!--        <if test="clientName != null ">and cm.client_name=#{clientName}</if>-->
    <!--        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>-->
    <!--        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>-->
    <!--        <if test="registrar != null ">and rep.applicant=#{applicant}</if>-->
    <!--        &lt;!&ndash;        <if test="updateTime1 != null ">and ap.approve_time &gt;= #{updateTime1}</if>&ndash;&gt;-->
    <!--        &lt;!&ndash;        <if test="updateTime2 != null ">and ap.approve_time &lt;= #{updateTime2}</if>&ndash;&gt;-->
    <!--        <if test="applyDate1 != null ">and rep.apply_date &gt;= #{applyDate1}</if>-->
    <!--        <if test="applyDate2 != null ">and rep.apply_date &lt;= #{applyDate2}</if>-->
    <!--        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>-->
    <!--        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>-->
    <!--        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>-->
    <!--        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>-->
    <!--        order by rep.apply_date desc-->
    <!--    </select>-->

    <!--    <select id="selectStagingRecordProce" resultType="com.zws.appeal.pojo.myApproval.MyStagingRecordUtils">-->
    <!--        select rep.id AS applyIds,-->
    <!--        rep.case_id AS caseId,-->
    <!--        rep.applicant,-->
    <!--        rep.apply_date AS applyDate,-->
    <!--        rep.staging_num AS stagingNum,-->
    <!--        rep.repayment_date AS repaymentDate,-->
    <!--        rep.repayment_monthly AS repaymentMonthly,-->
    <!--        rep.examine_state AS examineState,-->
    <!--        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,-->
    <!--        rep.entrusting_case_date AS entrustingCaseDate,-->
    <!--        rep.return_case_date AS returnCaseDate,-->

    <!--        ap.approve_start AS approveStart,-->
    <!--        ap.approve_time AS approveTime,-->

    <!--        cm.case_state AS caseState,-->
    <!--        cm.product_name AS productName,-->
    <!--        cm.client_name AS clientName,-->
    <!--        cm.client_idcard AS clientIdcard,-->
    <!--        cm.client_census_register AS clientCensusRegister,-->

    <!--        cas.id AS labelId,-->
    <!--        cas.label_content AS labelContent,-->
    <!--        cas.code AS label-->

    <!--        from case_staging_record AS rep-->
    <!--        LEFT JOIN case_manage AS cm ON (rep.case_id = cm.case_id)-->
    <!--        LEFT JOIN approve_proce AS ap ON(rep.id = ap.apply_id)-->
    <!--        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)-->
    <!--        where rep.del_flag = 0 and rep.team_id = #{teamId} and ap.approve_code = #{approveCode}-->

    <!--        <if test="approveStart != null ">and ap.approve_start = #{approveStart}</if>-->

    <!--        <if test="reviewerId != null ">and ap.reviewer_id = #{reviewerId}</if>-->

    <!--        <if test="examineStateList != null ">-->
    <!--            and rep.examine_state in-->
    <!--            <foreach item="item" collection="examineStateList" separator="," open="(" close=")" index="">-->
    <!--                #{item}-->
    <!--            </foreach>-->
    <!--        </if>-->

    <!--        <if test="caseId != null ">and rep.case_id=#{caseId}</if>-->
    <!--        <if test="clientName != null ">and cm.client_name=#{clientName}</if>-->
    <!--        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>-->
    <!--        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>-->
    <!--        <if test="registrar != null ">and rep.applicant=#{applicant}</if>-->
    <!--        <if test="updateTime1 != null ">and ap.approve_time &gt;= #{updateTime1}</if>-->
    <!--        <if test="updateTime2 != null ">and ap.approve_time &lt;= #{updateTime2}</if>-->
    <!--        <if test="applyDate1 != null ">and rep.apply_date &gt;= #{applyDate1}</if>-->
    <!--        <if test="applyDate2 != null ">and rep.apply_date &lt;= #{applyDate2}</if>-->
    <!--        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>-->
    <!--        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>-->
    <!--        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>-->
    <!--        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>-->
    <!--        order by rep.apply_date desc-->
    <!--    </select>-->

    <select id="selectStagingRecord" resultType="com.zws.appeal.pojo.myApproval.MyStagingRecordUtils">

        select rep.id AS applyIds,
        rep.case_id AS caseId,
        rep.applicant,
        rep.apply_date AS applyDate,
        rep.staging_num AS stagingNum,
        rep.repayment_date AS repaymentDate,
        rep.repayment_monthly AS repaymentMonthly,
        rep.examine_state AS examineState,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.return_case_date AS returnCaseDate,

        c.approve_start AS approveStart,
        c.approve_time AS approveTime,

        cm.case_state AS caseState,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_census_register AS clientCensusRegister,
        cm.client_id_type as clientIdType,
        cas.id AS labelId,
        cas.label_content AS labelContent,
        cas.code AS label

        from case_staging_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)
        LEFT JOIN team_employees AS te ON (rep.odv_id=te.id)
        LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        LEFT JOIN (
        SELECT id, apply_id,approve_start,approve_time,reviewer_id FROM
        approve_proce WHERE approve_code=#{approveCode} AND reviewer_id= #{reviewerId}
        <choose>
            <when test="selectAll!=null"></when>
            <otherwise>
                <if test="approveSort!=null">AND approve_sort = #{approveSort}</if>
            </otherwise>
        </choose>
        AND operation_type = #{operationType}
        )AS c ON(rep.id=c.apply_id and c.reviewer_id=#{reviewerId})

        WHERE rep.del_flag = 0 and rep.team_id = #{teamId} and rep.`examine_state` != "已撤销"
        <if test="haveHead!=null">
            AND (td.have_head = 0 OR rep.operation_type=0)
        </if>
        <if test="employeesIds!=null and employeesIds.size()>0">
            and te.id in
            <foreach collection="employeesIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            and td.id in
            <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="ids!=null and ids.size()>0">
            and rep.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <choose>
            <when test="selectAll!=null">
                and (
                <if test="approveSort!=null and approveSort == 1">
                    (rep.proce =0) OR
                </if>
                (rep.proce =1 AND rep.proce_sort=#{approveSort}-1)
                or (rep.proce in (1,2,3,4,5) and c.reviewer_id=#{reviewerId} )
                )
            </when>
            <otherwise>
                <if test="approveSort!=null">

                    <if test="approveSort==1">
                        and rep.proce =0
                    </if>
                    <if test="approveSort >1 ">
                        and rep.proce =1 and rep.proce_sort=#{approveSort}-1
                    </if>
                    AND c.approve_start IS NULL
                </if>
                <if test="approveStart!=null">
                    and c.approve_start=#{approveStart}
                    and c.reviewer_id=#{reviewerId}
                </if>
            </otherwise>
        </choose>

        <!--        <if test="repaymentType != null ">and rep.repayment_type = #{repaymentType}</if>-->

        <if test="examineStateList != null ">
            and rep.examine_state in
            <foreach item="item" collection="examineStateList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="caseId != null ">and rep.case_id=#{caseId}</if>
        <if test="applicant != null and applicant != ''">and rep.applicant=#{applicant}</if>
        <if test="clientName != null ">and cm.client_name=#{clientName}</if>
        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>
        <if test="registrar != null ">and rep.applicant=#{applicant}</if>
        <if test="updateTime1 != null ">and c.approve_time &gt;= #{updateTime1}</if>
        <if test="updateTime2 != null ">and c.approve_time &lt;= #{updateTime2}</if>
        <if test="applyDate1 != null ">and rep.apply_date &gt;= #{applyDate1}</if>
        <if test="applyDate2 != null ">and rep.apply_date &lt;= #{applyDate2}</if>
        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by rep.apply_date desc
    </select>

    <!--我的审批(外访)-->
    <!--    <select id="selectOutsideRecord" resultType="com.zws.appeal.pojo.myApproval.MyOutsideRecordUtils">-->
    <!--        select rep.id AS applyIds,-->
    <!--        rep.case_id AS caseId,-->
    <!--        rep.create_by AS createBy,-->
    <!--        rep.create_time AS createTime,-->
    <!--        rep.reason AS reason,-->
    <!--        rep.outside_address AS outsideAddress,-->
    <!--        rep.odv_name AS odvName,-->
    <!--        rep.state AS state,-->
    <!--        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,-->
    <!--        rep.entrusting_case_date AS entrustingCaseDate,-->
    <!--        rep.return_case_date AS returnCaseDate,-->
    <!--        rep.outside_start AS outsideStart,-->

    <!--        c.approve_start AS approveStart,-->
    <!--        c.approve_time AS approveTime,-->

    <!--        cm.case_state AS caseState,-->
    <!--        cm.product_name AS productName,-->
    <!--        cm.client_name AS clientName,-->
    <!--        cm.client_idcard AS clientIdcard,-->
    <!--        cm.client_census_register AS clientCensusRegister,-->

    <!--        cas.id AS labelId,-->
    <!--        cas.label_content AS labelContent,-->
    <!--        cas.code AS label-->

    <!--        from case_outside_record AS rep-->
    <!--        LEFT JOIN case_manage AS cm ON (rep.case_id = cm.case_id)-->
    <!--        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)-->
    <!--        LEFT JOIN (-->
    <!--        SELECT approve_code, apply_id,approve_start, approve_time,reviewer,reviewer_id,refuse_reason , approve_sort FROM-->
    <!--        approve_proce WHERE approve_code = 7 and reviewer_id=#{userId} and operation_type= #{operationType}-->
    <!--        )AS c ON(rep.id=c.apply_id)-->
    <!--        where rep.del_flag = 0 and rep.team_id = #{teamId}-->

    <!--        <if test="proce != null ">and rep.proce = #{proce}</if>-->
    <!--        <if test="proceSort != null ">and rep.proce_sort = #{proceSort}</if>-->

    <!--        <if test="stateList != null ">-->
    <!--            and rep.state in-->
    <!--            <foreach item="item" collection="stateList" separator="," open="(" close=")" index="">-->
    <!--                #{item}-->
    <!--            </foreach>-->
    <!--        </if>-->

    <!--        <if test="caseId != null ">and rep.case_id=#{caseId}</if>-->
    <!--        <if test="clientName != null ">and cm.client_name=#{clientName}</if>-->
    <!--        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>-->
    <!--        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>-->
    <!--        <if test="createBy != null ">and rep.create_by=#{createBy}</if>-->
    <!--        <if test="approveTime1 != null ">and c.approve_time &gt;= #{approveTime1}</if>-->
    <!--        <if test="approveTime2 != null ">and c.approve_time &lt;= #{approveTime2}</if>-->
    <!--        <if test="createTime1 != null ">and rep.create_time &gt;= #{createTime1}</if>-->
    <!--        <if test="createTime2 != null ">and rep.create_time &lt;= #{createTime2}</if>-->
    <!--        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>-->
    <!--        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>-->
    <!--        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>-->
    <!--        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>-->
    <!--        order by rep.create_time desc-->
    <!--    </select>-->

    <!--    <select id="selectOutsideRecordProce" resultType="com.zws.appeal.pojo.myApproval.MyOutsideRecordUtils">-->
    <!--        select rep.id AS applyIds,-->
    <!--        rep.case_id AS caseId,-->
    <!--        rep.create_by AS createBy,-->
    <!--        rep.create_time AS createTime,-->
    <!--        rep.reason AS reason,-->
    <!--        rep.outside_address AS outsideAddress,-->
    <!--        rep.odv_name AS odvName,-->
    <!--        rep.state AS state,-->
    <!--        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,-->
    <!--        rep.entrusting_case_date AS entrustingCaseDate,-->
    <!--        rep.return_case_date AS returnCaseDate,-->
    <!--        rep.outside_start AS outsideStart,-->

    <!--        ap.approve_start AS approveStart,-->
    <!--        ap.approve_time AS approveTime,-->

    <!--        cm.case_state AS caseState,-->
    <!--        cm.product_name AS productName,-->
    <!--        cm.client_name AS clientName,-->
    <!--        cm.client_idcard AS clientIdcard,-->
    <!--        cm.client_census_register AS clientCensusRegister,-->

    <!--        cas.id AS labelId,-->
    <!--        cas.label_content AS labelContent,-->
    <!--        cas.code AS label-->

    <!--        from case_outside_record AS rep-->
    <!--        LEFT JOIN case_manage AS cm ON (rep.case_id = cm.case_id)-->
    <!--        LEFT JOIN approve_proce AS ap ON(rep.id = ap.apply_id)-->
    <!--        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)-->
    <!--        where rep.del_flag = 0 and rep.team_id = #{teamId} and ap.approve_code = #{approveCode}-->

    <!--        <if test="approveStart != null ">and ap.approve_start = #{approveStart}</if>-->

    <!--        <if test="reviewerId != null ">and ap.reviewer_id = #{reviewerId}</if>-->

    <!--        <if test="stateList != null ">-->
    <!--            and rep.state in-->
    <!--            <foreach item="item" collection="stateList" separator="," open="(" close=")" index="">-->
    <!--                #{item}-->
    <!--            </foreach>-->
    <!--        </if>-->

    <!--        <if test="caseId != null ">and rep.case_id=#{caseId}</if>-->
    <!--        <if test="clientName != null ">and cm.client_name=#{clientName}</if>-->
    <!--        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>-->
    <!--        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>-->
    <!--        <if test="createBy != null ">and rep.create_by=#{createBy}</if>-->
    <!--        <if test="approveTime1 != null ">and ap.approve_time &gt;= #{approveTime1}</if>-->
    <!--        <if test="approveTime2 != null ">and ap.approve_time &lt;= #{approveTime2}</if>-->
    <!--        <if test="createTime1 != null ">and rep.create_time &gt;= #{createTime1}</if>-->
    <!--        <if test="createTime2 != null ">and rep.create_time &lt;= #{createTime2}</if>-->
    <!--        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>-->
    <!--        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>-->
    <!--        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>-->
    <!--        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>-->
    <!--        order by rep.create_time desc-->
    <!--    </select>-->

    <select id="selectOutsideRecord" resultType="com.zws.appeal.pojo.myApproval.MyOutsideRecordUtils">

        select rep.id AS applyIds,
        rep.case_id AS caseId,
        rep.create_by AS createBy,
        rep.create_time AS createTime,
        rep.reason AS reason,
        rep.outside_address AS outsideAddress,
        rep.odv_name AS odvName,
        rep.state AS state,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.return_case_date AS returnCaseDate,
        rep.outside_start AS outsideStart,

        c.approve_start AS approveStart,
        c.approve_time AS approveTime,

        cm.case_state AS caseState,
        cm.product_name AS productName,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_census_register AS clientCensusRegister,
        cm.client_id_type as clientIdType,
        cas.id AS labelId,
        cas.label_content AS labelContent,
        cas.code AS label

        from case_outside_record AS rep
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)
        LEFT JOIN team_employees AS te ON (rep.create_by_id=te.id)
        LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        LEFT JOIN (
        SELECT id, apply_id,approve_start,approve_time,reviewer_id FROM
        approve_proce WHERE approve_code=#{approveCode} AND reviewer_id= #{reviewerId}
        <choose>
            <when test="selectAll!=null"></when>
            <otherwise>
                <if test="approveSort!=null">AND approve_sort = #{approveSort}</if>
            </otherwise>
        </choose>
        AND operation_type = #{operationType}
        )AS c ON(rep.id=c.apply_id and c.reviewer_id=#{reviewerId})

        WHERE rep.del_flag = 0 and rep.team_id = #{teamId} and rep.`state` != "已撤销"
        <if test="haveHead!=null">
            AND (td.have_head = 0 OR rep.operation_type=0)
        </if>
        <if test="employeesIds!=null and employeesIds.size()>0">
            and te.id in
            <foreach collection="employeesIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            and td.id in
            <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="ids!=null and ids.size()>0">
            and rep.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <choose>
            <when test="selectAll!=null">
                and (
                <if test="approveSort!=null and approveSort == 1">
                    (rep.proce =0) OR
                </if>
                (rep.proce =1 AND rep.proce_sort=#{approveSort}-1)
                or (rep.proce in (1,2,3,4,5) and c.reviewer_id=#{reviewerId} )
                )
            </when>
            <otherwise>
                <if test="approveSort!=null">

                    <if test="approveSort==1">
                        and rep.proce =0
                    </if>
                    <if test="approveSort >1 ">
                        and rep.proce =1 and rep.proce_sort=#{approveSort}-1
                    </if>
                    AND c.approve_start IS NULL
                </if>
                <if test="approveStart!=null">
                    and c.approve_start=#{approveStart}
                    and c.reviewer_id=#{reviewerId}
                </if>
            </otherwise>
        </choose>

        <!--        <if test="repaymentType != null ">and rep.repayment_type = #{repaymentType}</if>-->

        <if test="stateList != null ">
            and rep.state in
            <foreach item="item" collection="stateList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="caseId != null ">and rep.case_id=#{caseId}</if>
        <if test="applicant != null and applicant != ''">and rep.create_by=#{applicant}</if>
        <if test="clientName != null ">and cm.client_name=#{clientName}</if>
        <if test="clientIdcard != null ">and cm.client_idcard=#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>
        <if test="createBy != null ">and rep.create_by=#{createBy}</if>
        <if test="approveTime1 != null ">and c.approve_time &gt;= #{approveTime1}</if>
        <if test="approveTime2 != null ">and c.approve_time &lt;= #{approveTime2}</if>
        <if test="createTime1 != null ">and rep.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null ">and rep.create_time &lt;= #{createTime2}</if>
        <if test="entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null ">and rep.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null ">and rep.return_case_date &lt;= #{returnCaseDate2}</if>
        order by rep.create_time desc
    </select>

    <select id="selectSignRecord" resultType="com.zws.appeal.pojo.myApproval.MySignRecordUtils">

        select
        car.id,
        car.batch_num as batchNum,
        car.model_type_id as modelTypeId,
        car.model_type as modelType,
        car.model_name as modelName,
        car.registrar as registrar,
        car.create_time as createTime,
        car.model_name as productName,
        car.letter_id as letterId,
        car.registrar as createBy,
        team.cname,
        team.category,
        mes.status AS status,
        mes.quantity AS quantity,
        mes.remarks AS remarks,
        car.examine_state as examineState,
        car.update_by as updateBy,
        car.update_time as updateTime,
        c.apply_id as applyId,
        c.approve_start as approveStart,
        c.approve_time as approveTime,
        c.reviewer,
        c.reviewer_id as reviewerId,
        c.refuse_reason as refuseReason,
        c.approve_start AS approveStart,
        c.approve_time AS approveTime
        from case_sign_record as car
        left join team_create as team on(car.team_id=team.id)
        left join letter_message as mes on(mes.id=car.letter_id)
        LEFT JOIN team_employees AS te ON (car.registrar_id=te.id)
        LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        LEFT JOIN (
        SELECT approve_code, apply_id,approve_start, approve_time,reviewer,reviewer_id,refuse_reason , approve_sort FROM
        approve_proce WHERE approve_code=10
        )AS c ON(car.id=c.apply_id AND c.reviewer_id=#{reviewerId})
        WHERE 1=1 and mes.del_flag = '0'
        <if  test="teamId!=null">
            and car.team_id=#{teamId}
        </if>
        <if test="haveHead!=null">
            AND (td.have_head = 0 OR car.operation_type=0 OR c.reviewer_id=#{reviewerId})
        </if>
        <if test="employeesIds!=null and employeesIds.size()>0">
            and te.id in
            <foreach collection="employeesIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            and td.id in
            <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="ids!=null and ids.size()>0">
            and car.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <choose>
            <when test="selectAll!=null">
                and (
                (car.proce =1 and car.proce_sort=#{approveSort}-1)
                or (car.proce in (2,3,4,5) and c.reviewer_id=#{reviewerId} )
                <if test="approveSort==1">
                    or (car.proce =0)
                </if>
                )
            </when>
            <otherwise>
                <if test="approveSort!=null and approveStart==null">
                    and c.approve_code is null
                    <if test="approveSort==1">
                        and (car.proce =0 )
                    </if>
                    <if test="approveSort >1 ">
                        and car.proce =2 and car.proce_sort=#{approveSort}-1
                    </if>
                </if>
                <if test="approveStart!=null">
                    and c.approve_start=#{approveStart}
                    and c.reviewer_id=#{reviewerId}
                </if>
            </otherwise>
        </choose>
        <if test="entrustingCaseBatchNumArr!=null and entrustingCaseBatchNumArr.size()>0">
            and car.entrusting_batch_num in
            <foreach collection="entrustingCaseBatchNumArr" item="bn" separator="," open="(" close=")">
                #{bn}
            </foreach>
        </if>
        <if test="examineStateArr!=null and examineStateArr.size()>0">
            and examine_state in
            <foreach collection="examineStateArr" item="examineState" separator="," open="(" close=")">
                #{examineState}
            </foreach>
        </if>
        <if test="status!=null">
            and mes.status=#{status}
        </if>
        <if test="batchNum!=null and batchNum!=''">
            and car.batch_num like concat('%',#{batchNum},'%')
        </if>
        <if test="templateName!=null and templateName!=''">
            and car.model_name like concat('%',#{templateName},'%')
        </if>
        <if test="classifyNameList!=null and classifyNameList.size()>0">
            and car.model_type in
            <foreach collection="classifyNameList" item="classifyName" separator="," open="(" close=")">
                #{classifyName}
            </foreach>
        </if>
        <if test="createBy!=null and createBy!=''">
            and car.registrar like concat('%',#{createBy},'%')
        </if>
        <if test="createTime1!=null">
            and car.create_time &gt;= #{createTime1}
        </if>
        <if test="createTime2!=null">
            and car.create_time &lt;= #{createTime2}
        </if>
        order by car.create_time desc
    </select>

    <select id="selectReductionFile" resultType="com.zws.appeal.domain.record.ReductionFile">
        select id,
               record_id     AS recordId,
               first_name    AS firstName,
               modify_name   AS modifyName,
               file_url      AS fileUrl,
               founder,
               creation_time AS creationTime
        from case_reduction_file
        where record_id = #{id}
    </select>

    <select id="selectApproveProce" resultType="com.zws.appeal.domain.ApproveProce">
        select approve_time   AS approveTime,
               reviewer,
               reviewer_id    AS reviewerId,
               approve_start  AS approveStart,
               refuse_reason  AS refuseReason,
               operation_type AS operationType
        from approve_proce
        where apply_id = #{applyId}
          and approve_code = #{approveCode}
        order by approve_time desc
    </select>

    <insert id="insertApproveProce" parameterType="com.zws.appeal.domain.ApproveProce">
        insert into
        approve_proce(approve_code,apply_id,approve_start,approve_time,reviewer,reviewer_id,refuse_reason,approve_sort,del_flag,operation_type)
        values
        <foreach collection="approveProce" item="list" index="index" separator=",">
            (#{list.approveCode},#{list.applyId},#{list.approveStart},#{list.approveTime},#{list.reviewer},#{list.reviewerId},#{list.refuseReason},#{list.approveSort},#{list.delFlag},#{list.operationType})
        </foreach>
    </insert>

    <update id="updateApplyRecord" parameterType="com.zws.appeal.domain.ApplyRecord">
        <foreach collection="applyRecord" item="list" index="index" open="" close="" separator=";">
            update case_apply_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.examineState != null">examine_state = #{list.examineState},</if>
                <if test="list.examineTime != null">examine_time = #{list.examineTime},</if>
                <if test="list.examineBy != null">examine_by = #{list.examineBy},</if>
                <if test="list.examineById != null">examine_by_id = #{list.examineById},</if>
                <if test="list.proce != null">proce = #{list.proce},</if>
                <if test="list.proceSort != null">proce_sort = #{list.proceSort},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>

    <update id="updateRetrievalRecord" parameterType="com.zws.appeal.domain.record.RetrievalRecord">
        <foreach collection="retrievalRecords" item="list" index="index" open="" close="" separator=";">
            update case_retrieval_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.examineState != null">examine_state = #{list.examineState},</if>
                <if test="list.examineTime != null">examine_time = #{list.examineTime},</if>
                <if test="list.examineBy != null">examine_by = #{list.examineBy},</if>
                <if test="list.examineById != null">examine_by_id = #{list.examineById},</if>
                <if test="list.proce != null">proce = #{list.proce},</if>
                <if test="list.proceSort != null">proce_sort = #{list.proceSort},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>

    <!--  <update id="updateRepaymentRecord" parameterType="com.zws.appeal.domain.record.RepaymentRecord">
          <foreach collection="repaymentRecords" item="list" index="index" open="" close="" separator=";">
              update case_repayment_record
              <trim prefix="SET" suffixOverrides=",">
                  <if test="list.examineState != null">examine_state = #{list.examineState},</if>
                  <if test="list.updateTime != null">update_time = #{list.updateTime},</if>
                  <if test="list.updateBy != null">update_by = #{list.updateBy},</if>
                  <if test="list.updateById != null">update_by_id = #{list.updateById},</if>
                  <if test="list.proce != null">proce = #{list.proce},</if>
                  <if test="list.proceSort != null">proce_sort = #{list.proceSort},</if>
              </trim>
              where id = #{list.id}
          </foreach>
      </update>-->

    <update id="updateRepaymentRecord">
        update case_repayment_record
        set
        <if test="examineState != null">examine_state = #{examineState},</if>
        <if test="updateTime != null">update_time = #{updateTime},</if>
        <if test="updateBy != null">update_by = #{updateBy},</if>
        <if test="updateById != null">update_by_id = #{updateById},</if>
        <if test="proce != null">proce = #{proce},</if>
        <if test="proceSort != null">proce_sort = #{proceSort},</if>
        update_time= now()
        where id = #{id}
    </update>

    <update id="updateReductionRecord" parameterType="com.zws.appeal.domain.record.ReductionRecord">
        <foreach collection="reductionRecords" item="list" index="index" open="" close="" separator=";">
            update case_reduction_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.state != null">state = #{list.state},</if>
                <if test="list.updateTime != null">update_time = #{list.updateTime},</if>
                <if test="list.examineBy != null">examine_by = #{list.examineBy},</if>
                <if test="list.examineById != null">examine_by_id = #{list.examineById},</if>
                <if test="list.proce != null">proce = #{list.proce},</if>
                <if test="list.proceSort != null">proce_sort = #{list.proceSort},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>

    <update id="updateStagingRecord" parameterType="com.zws.appeal.domain.record.StagingRecord">
        <foreach collection="stagingRecords" item="list" index="index" open="" close="" separator=";">
            update case_staging_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.examineState != null">examine_state = #{list.examineState},</if>
                <if test="list.examineTime != null">examine_time = #{list.examineTime},</if>
                <if test="list.examineBy != null">examine_by = #{list.examineBy},</if>
                <if test="list.examineById != null">examine_by_id = #{list.examineById},</if>
                <if test="list.proce != null">proce = #{list.proce},</if>
                <if test="list.proceSort != null">proce_sort = #{list.proceSort},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>

    <update id="updateOutsideRecord" parameterType="com.zws.appeal.domain.record.OutsideRecord">
        <foreach collection="outsideRecords" item="list" index="index" open="" close="" separator=";">
            update case_outside_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.state != null">state = #{list.state},</if>
                <if test="list.examineTime != null">examine_time = #{list.examineTime},</if>
                <if test="list.examineBy != null">examine_by = #{list.examineBy},</if>
                <if test="list.examineById != null">examine_by_id = #{list.examineById},</if>
                <if test="list.proce != null">proce = #{list.proce},</if>
                <if test="list.proceSort != null">proce_sort = #{list.proceSort},</if>
                <if test="list.stateCode != null">state_code = #{list.stateCode},</if>
            </trim>
            where id = #{list.id}
        </foreach>
    </update>
    <update id="updateSignRecord">
        update case_sign_record
        set
        <if test="examineState != null">examine_state = #{examineState},</if>
        <if test="updateTime != null">update_time = #{updateTime},</if>
        <if test="updateBy != null">update_by = #{updateBy},</if>
        <if test="updateById != null">update_by_id = #{updateById},</if>
        <if test="updateTime !=null"> update_time = #{updateTime},</if>
        <if test="proce != null">proce = #{proce},</if>
        <if test="proceSort != null">proce_sort = #{proceSort},</if>
        update_time= now()
        where id = #{id}
    </update>

    <select id="selectSignRecordInfo" resultType="java.lang.Integer" resultMap="CaseSignRecordResult">
        select id, team_id, case_id, examine_state, registrar_id, registrar, batch_num, model_type
             , model_name, create_time, proce, proce_sort, del_flag, update_by_id, update_by, update_time, invalid, odv_id, odv_name, bill_id, operation_type
             , yc_account_agency, whether_operate, import_not from case_sign_record
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectRepaymentRecordById" resultType="com.zws.appeal.domain.record.RepaymentRecord">
        select id,
        team_id AS teamId,
        registrar_id AS registrarId,
        registrar AS registrar,
        create_time AS createTime,
        operation_type AS operationType
        from case_repayment_record
        where invalid = 0
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectReductionRecordById" resultType="com.zws.appeal.domain.record.ReductionRecord">
        select id,
        team_id AS teamId,
        applicant_id AS applicantId,
        applicant AS applicant,
        apply_date AS applyDate,
        operation_type AS operationType
        from case_reduction_record
        where del_flag = 0
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectStagingRecordById" resultType="com.zws.appeal.domain.record.StagingRecord">
        select id,
        team_id AS teamId,
        applicant_id AS applicantId,
        applicant AS applicant,
        apply_date AS applyDate,
        operation_type AS operationType
        from case_staging_record
        where del_flag = 0
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectOutsideRecordById" resultType="com.zws.appeal.domain.record.OutsideRecord">
        select id,
        team_id AS teamId,
        create_by_id AS createById,
        create_by AS createBy,
        create_time AS createTime,
        operation_type AS operationType
        from case_outside_record
        where del_flag = 0
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectApplyRecordById" resultType="com.zws.appeal.domain.ApplyRecord">
        select id,
        team_id AS teamId,
        applicant_id AS applicantId,
        applicant AS applicant,
        apply_date AS applyDate,
        operation_type AS operationType
        from case_apply_record
        where del_flag = 0 and apply_state = #{applyState}
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectApplyRecordByIdsAndSort" resultType="com.zws.appeal.domain.ApproveProce">
            select approve_time   AS approveTime,
            reviewer,
            reviewer_id    AS reviewerId,
            apply_id    AS applyId,
            approve_start  AS approveStart,
            refuse_reason  AS refuseReason,
            operation_type AS operationType,
            reviewer_time   AS reviewerTime
            from approve_proce
            where
            apply_id in
            <foreach collection="ids" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            and approve_code = #{approveCode}
            and approve_sort = #{sort}
        and operation_type in (0,1)
            order by approve_time desc




    </select>
    <select id="selectCaseMaterials" resultType="com.zws.appeal.domain.CaseMaterialsVo">
        select crr.id,
        crr.case_id caseId,
        crr.apply_date applyDate,
        crr.expiration_time expirationTime,
        crr.random random,
        crr.watermarked_file_name watermarkedFileName,
        crr.archive_file_address archiveFileAddress,
        crr.watermarked_file_path watermarkedFilePath
        from case_retrieval_record crr
        where crr.case_id = #{caseId}
        <if test="userId != null ">and crr.applicant_id = #{userId}</if>
        <if test="teamId != null ">and crr.team_id = #{teamId}</if>
        and crr.expiration_time &gt; #{nowDate}
        and examine_state = '已通过'


    </select>
    <select id="selectCardType" resultType="java.lang.String">
        select card_type from bank_case where case_id =#{caseId} and del_flag =0 limit 1
    </select>
    <select id="selectApplyRecordsNew" resultType="com.zws.appeal.pojo.myApproval.myApplyRecordUtils">
        select rep.id AS applyIds,
        rep.case_id AS caseId,
        zws_ar.applicant,
        zws_ar.apply_date AS applyDate,
        rep.reason,
        zws_ar.approve_state AS examineState,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.return_case_date AS returnCaseDate,
        IFNULL(rep.stop_end_approval_time, rep.stop_end_time) AS stopEndTime,
        rep.stop_end_approval_time AS stopEndApprovalTime,
        rep.permanently_stop AS permanentlyStop,

        zws_ar.id AS approveId,
        zws_ap.approve_state AS approveStart,
        zws_ap.approve_time AS examineTime,
        zws_ap.reviewer AS examineBy,
        cm.outsourcing_team_id as teamId,
        cm.case_state AS caseState,
        cli.product_name AS productName,
        cli.client_name AS clientName,
        cli.client_id_num AS clientIdcard,
        cli.client_census_register AS clientCensusRegister,
        cli.client_id_type as clientIdType,
        cas.id AS labelId,
        cas.label_content AS labelContent,
        cas.code AS label

        from ${sqlDataDto.fromData}
        LEFT JOIN case_apply_record AS rep on(rep.approve_id = zws_ar.id)
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)
        LEFT JOIN team_employees AS te ON (cm.mediator_id = te.id)
        LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        LEFT JOIN case_library AS cli ON(rep.case_id=cli.id)

        WHERE ${sqlDataDto.whereData} and rep.del_flag = 0 and rep.team_id = #{dto.teamId}
<!--        <if test="dto.haveHead!=null">-->
<!--            AND (td.have_head = 0 OR rep.operation_type=0)-->
<!--        </if>-->
<!--        <if test="dto.haveHead ==null">-->
<!--            AND rep.operation_type=1-->
<!--        </if>-->
<!--        <if test="dto.employeesIds!=null and dto.employeesIds.size()>0">-->
<!--            and te.id in-->
<!--            <foreach collection="dto.employeesIds" item="item" separator="," open="(" close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="dto.deptIds!=null and dto.deptIds.size()>0">-->
<!--            and td.id in-->
<!--            <foreach collection="dto.deptIds" item="item" separator="," open="(" close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="dto.ids!=null and dto.ids.size()>0">-->
<!--            and rep.id in-->
<!--            <foreach collection="dto.ids" item="id" open="(" close=")" separator=",">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->

        <if test="dto.caseId != null ">and rep.case_id=#{dto.caseId}</if>
        <if test="dto.clientName != null ">and cm.client_name=#{dto.clientName}</if>
        <if test="dto.clientIdcard != null ">and cm.client_idcard=#{dto.clientIdcard}</if>
        <if test="dto.entrustingCaseBatchNum != null ">and rep.entrusting_case_batch_num=#{dto.entrustingCaseBatchNum}</if>
        <if test="dto.entrustingCaseDate1 != null ">and rep.entrusting_case_date &gt;= #{dto.entrustingCaseDate1}</if>
        <if test="dto.entrustingCaseDate2 != null ">and rep.entrusting_case_date &lt;= #{dto.entrustingCaseDate2}</if>
        <if test="dto.returnCaseDate1 != null ">and rep.return_case_date &gt;= #{dto.returnCaseDate1}</if>
        <if test="dto.returnCaseDate2 != null ">and rep.return_case_date &lt;= #{dto.returnCaseDate2}</if>
        order by zws_ar.apply_date desc
    </select>
    <select id="jgQueryApproveList" resultType="com.zws.appeal.pojo.myApproval.myApplyRecordUtils">

        select rep.id                                                AS applyIds,
        rep.case_id                                           AS caseId,
        rep.applicant,
        zws_ar.id                                             AS approveId,
        zws_ap.approve_state                                  AS approveStart,
        zws_ap.approve_time                                   AS examineTime,
        zws_ap.reviewer                                       AS examineBy,
        zws_ar.apply_date                                     AS applyDate,
        zws_ar.approve_state                                  AS examineState,
        rep.entrusting_case_batch_num                         AS entrustingCaseBatchNum,
        rep.entrusting_case_date                              AS entrustingCaseDate,
        rep.return_case_date                                  AS returnCaseDate,
        IFNULL(rep.stop_end_approval_time, rep.stop_end_time) AS stopEndTime,
        rep.stop_end_approval_time                            AS stopEndApprovalTime,
        rep.permanently_stop                                  AS permanentlyStop,
        rep.reason,

        cm.outsourcing_team_id                                as teamId,
        cm.outsourcing_team_name                              AS teamName,
        cm.batch_num                                          AS assetBatchNum,
        cm.case_state                                         AS caseState,
        cli.product_name                                      AS productName,
        cli.client_name                                       AS clientName,
        cli.client_id_num                                     AS clientIdcard,
        cli.client_census_register                            AS clientCensusRegister,
        cli.client_id_type                                    as clientIdType,
        cas.id                                                AS labelId,
        cas.label_content                                     AS labelContent,
        cas.code                                              AS label,
        asma.package_name                                     AS packageName,
        asow.name                                             AS ownerName

        from ${sqlDataDto.fromData}
        left join case_apply_record AS rep on(rep.approve_id=zws_ar.id)
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN team_label AS cas ON(cm.outsourcing_team_id = cas.create_id and cm.label = cas.code)
        LEFT JOIN team_employees AS te ON (cm.mediator_id = te.id)
        LEFT JOIN team_dept AS td ON(te.department_id=td.id)
        LEFT JOIN case_library AS cli ON(rep.case_id=cli.id)
        LEFT JOIN asset_manage AS asma ON(cm.batch_num=asma.batch_num)
        LEFT JOIN asset_owner AS asow ON(asma.owner_id=asow.id)

        WHERE ${sqlDataDto.whereData}
        <if test="queryDTO.caseId !=null">
            AND rep.case_id=  #{queryDTO.caseId}
        </if>
        <if test="queryDTO.clientName !=null">
            AND cli.client_name  like concat('%',#{queryDTO.clientName},'%')
        </if>
        <if test="queryDTO.entrustingCaseBatchNum !=null">
            AND rep.entrusting_case_batch_num =  #{queryDTO.entrustingCaseBatchNum}
        </if>
        <if test="queryDTO.entrustingCaseDate1 != null">
            and rep.entrusting_case_date &gt;=
            #{queryDTO.entrustingCaseDate1}
        </if>
        <if test="queryDTO.entrustingCaseDate2 != null">
            and rep.entrusting_case_date &lt;=
            #{queryDTO.entrustingCaseDate2}
        </if>
        <if test="queryDTO.returnCaseDate1 != null">
            and rep.return_case_date &gt;=
            #{queryDTO.returnCaseDate1}
        </if>
        <if test="queryDTO.returnCaseDate2 != null">
            and rep.return_case_date &lt;=
            #{queryDTO.returnCaseDate2}
        </if>

        and  rep.del_flag = 0
        order by zws_ar.apply_date desc



    </select>
    <select id="selectApplyRecordId" resultType="com.zws.appeal.pojo.InheritanceCollection">
        select rep.id AS id,
        rep.case_id AS caseId,
        rep.approve_id AS approveId,
        zws_ar.approve_state AS examineState,
        zws_ar.examine_time AS examineTime,
        zws_ar.applicant AS applicant,
        zws_ar.apply_date AS applyDate,
        rep.reason AS reason,
        rep.stay_case_time AS stayCaseTime,
        rep.entrusting_case_batch_num AS entrustingCaseBatchNum,
        rep.return_case_date AS returnCaseDate,
        rep.entrusting_case_date AS entrustingCaseDate,
        rep.odv_name AS odvName,
        IFNULL(rep.stop_end_approval_time, rep.stop_end_time) AS stopEndTime,
        rep.stop_end_approval_time AS stopEndApprovalTime,
        rep.permanently_stop AS permanentlyStop,
        cli.product_name AS productName,
        cli.client_name AS clientName,
        cli.client_id_num AS clientIdcard,
        cli.client_census_register AS clientCensusRegister,
        cli.client_id_type as clientIdType
        from ${sqlDataDto.fromData}
        left join case_apply_record AS rep on(rep.approve_id=zws_ar.id)
        LEFT JOIN case_manage AS cm ON(rep.case_id=cm.case_id and cm.del_flag = 0)
        LEFT JOIN case_library AS cli ON(rep.case_id=cli.id)
        WHERE ${sqlDataDto.whereData}
        and   rep.del_flag = 0
        <if test="queryDTO.caseId !=null">
            AND rep.case_id=  #{queryDTO.caseId}
        </if>
        <if test="queryDTO.clientName !=null">
            AND cli.client_name  like concat('%',#{queryDTO.clientName},'%')
        </if>
        <if test="queryDTO.entrustingCaseBatchNum !=null">
            AND rep.entrusting_case_batch_num =  #{queryDTO.entrustingCaseBatchNum}
        </if>
        <if test="queryDTO.entrustingCaseDate1 != null">
            and rep.entrusting_case_date &gt;=
            #{queryDTO.entrustingCaseDate1}
        </if>
        <if test="queryDTO.entrustingCaseDate2 != null">
            and rep.entrusting_case_date &lt;=
            #{queryDTO.entrustingCaseDate2}
        </if>
        <if test="queryDTO.returnCaseDate1 != null">
            and rep.return_case_date &gt;=
            #{queryDTO.returnCaseDate1}
        </if>
        <if test="queryDTO.returnCaseDate2 != null">
            and rep.return_case_date &lt;=
            #{queryDTO.returnCaseDate2}
        </if>
        order by zws_ar.apply_date desc


    </select>

</mapper>
