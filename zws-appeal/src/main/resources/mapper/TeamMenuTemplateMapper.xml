<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.TeamMenuTemplateMapper">

    <resultMap type="com.zws.appeal.domain.TeamMenuTemplate" id="TeamMenuTemplateMap">
        <result property="menuId" column="menu_id" jdbcType="INTEGER"/>
        <result property="menuName" column="menu_name" jdbcType="VARCHAR"/>
        <result property="menuCode" column="menu_code"/>
        <result property="parentId" column="parent_id" />
        <result property="parentCode" column="parent_code" />
        <result property="orderNum" column="order_num" jdbcType="INTEGER"/>
        <result property="path" column="path" jdbcType="VARCHAR"/>
        <result property="component" column="component" jdbcType="VARCHAR"/>
        <result property="query" column="query" jdbcType="VARCHAR"/>
        <result property="isFrame" column="is_frame" jdbcType="INTEGER"/>
        <result property="isCache" column="is_cache" jdbcType="INTEGER"/>
        <result property="menuType" column="menu_type" jdbcType="VARCHAR"/>
        <result property="visible" column="visible" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="perms" column="perms" jdbcType="VARCHAR"/>
        <result property="icon" column="icon" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryAllMenus" parameterType="java.lang.Integer" resultMap="TeamMenuTemplateMap">
        select
            menu_id,
            menu_code,
            menu_name,
            parent_id,
            parent_code,
            order_num,
            path,
            component,
            query,
            is_frame,
            is_cache,
            menu_type,
            visible,
            status,
            perms,
            icon,
            create_by,
            create_time,
            update_by,
            update_time,
            remark
        from
        <choose>
            <when test="code == 0 ">
                team_menu_template
            </when>
            <when test="code == 1 ">
                team_menu_template_ts
            </when>
        </choose>

    </select>
</mapper>

