<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.controller.letterDoc.letter.mapper.LetterTemplateMapper">
  <resultMap id="BaseResultMap" type="com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="template_code" jdbcType="VARCHAR" property="templateCode" />
    <result column="classify_id" jdbcType="BIGINT" property="classifyId" />
    <result column="classify_label" jdbcType="VARCHAR" property="classifyLabel" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="template_label" jdbcType="VARCHAR" property="templateLabel" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="template_variable" jdbcType="VARCHAR" property="templateVariable" />
    <result column="law_id" jdbcType="BIGINT" property="lawId" />
    <result column="sign_id" jdbcType="BIGINT" property="signId" />
    <result column="original_url" jdbcType="VARCHAR" property="originalUrl" />
    <result column="preview_url" jdbcType="VARCHAR" property="previewUrl" />
    <result column="preview_pages" jdbcType="INTEGER" property="previewPages" />
    <result column="page_header" jdbcType="VARCHAR" property="pageHeader" />
    <result column="page_footer" jdbcType="VARCHAR" property="pageFooter" />
    <result column="cut_header" jdbcType="VARCHAR" property="cutHeader" />
    <result column="cut_footer" jdbcType="VARCHAR" property="cutFooter" />
    <result column="source_file_type" jdbcType="INTEGER" property="sourceFileType" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="type" jdbcType="CHAR" property="type" />
    <result column="body_content" jdbcType="LONGVARCHAR" property="bodyContent" />
    <result column="position_data" jdbcType="LONGVARCHAR" property="positionData" />
  </resultMap>

  <sql id="Base_Column_List">
    id, template_code, classify_id, classify_label, template_name, template_label, status,
    template_variable, law_id, sign_id, original_url, preview_url, preview_pages, page_header,
    page_footer, cut_header, cut_footer, source_file_type, team_id, create_by, create_by_id,
    create_time, update_by, update_by_id, update_time, del_flag, type,body_content, position_data
  </sql>
  <!-- 通用查询结果列 -->
  <sql id="Small_Column_List">
    id, template_code, classify_id,classify_label, template_name, template_label, status,
    template_variable, law_id, sign_id, preview_url,preview_pages, team_id, create_by, create_by_id,
    create_time, update_by, update_by_id, update_time,del_flag,
    page_header, page_footer, position_data, cut_header, cut_footer, source_file_type, original_url,type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from letter_template
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from letter_template
    where del_flag='0'
    <if test="templateCode!=null and templateCode!=''">
      and template_code=#{templateCode}
    </if>
    <if test="classifyId!=null ">
      and classify_id=#{classifyId}
    </if>
    <if test="templateName!=null and templateName!=''">
      and template_name=#{templateName}
    </if>
    <if test="status!=null ">
      and status=#{status}
    </if>
    <if test="lawId!=null ">
      and law_id=#{lawId}
    </if>
    <if test="signId!=null ">
      and sign_id=#{signId}
    </if>
    <if test="sourceFileType!=null ">
      and source_file_type=#{sourceFileType}
    </if>
    <if test="teamId!=null ">
      and team_id=#{teamId}
    </if>
    <if test="type!=null ">
      and type=#{type}
    </if>
    order  by create_time desc
  </select>
    <select id="selectSmallList" resultMap="BaseResultMap">
      select
      <include refid="Small_Column_List"></include>
      from letter_template
      where del_flag='0'
      <if test="teamId!=null">
        and team_id=#{teamId}
      </if>
      <if test="classifyIds!=null and classifyIds.size()>0">
        and classify_label in
        <foreach collection="classifyIds" item="classifyId" open="(" close=")" separator=",">
          #{classifyId}
        </foreach>
      </if>
      <if test="classifyIds!=null and classifyIds.size()>0">
        and classify_label in
        <foreach collection="classifyIds" item="classifyId" open="(" close=")" separator=",">
          #{classifyId}
        </foreach>
      </if>
      <if test="templateIds!=null and templateIds.size()>0">
        and id in
        <foreach collection="templateIds" item="templateId" open="(" close=")" separator=",">
          #{templateId}
        </foreach>
      </if>
      <if test="templateName!=null and templateName!=''">
        and template_name=#{templateName}
      </if>
      <if test="notId!=null">
        and id != #{notId}
      </if>
      <if test="createByIds!=null and createByIds.size()>0">
        and create_by_id in
        <foreach collection="createByIds" item="createById" open="(" close=")" separator=",">
          #{createById}
        </foreach>
      </if>
      order by create_time desc
    </select>
  <select id="selectOptions" resultType="com.zws.common.core.domain.Option">
    SELECT lt.id as code,lt.template_name as info
    FROM letter_template AS lt
    WHERE lt.del_flag='0' AND lt.status=0
      AND lt.team_id=#{teamId}
    <if test="sourceFileType != null">
      AND lt.source_file_type = #{sourceFileType}
    </if>
    <if test="type!=null">
        and type=#{type}
    </if>
    order by create_time
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from letter_template
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
    insert into letter_template (id, template_code, classify_id,
      classify_label, template_name, template_label,
      status, template_variable, law_id,
      sign_id, original_url, preview_url,
      preview_pages, page_header, page_footer,
      cut_header, cut_footer, source_file_type,
      team_id, create_by, create_by_id,
      create_time, update_by, update_by_id,
      update_time, del_flag, type,
      body_content, position_data)
    values (#{id,jdbcType=BIGINT}, #{templateCode,jdbcType=VARCHAR}, #{classifyId,jdbcType=BIGINT},
      #{classifyLabel,jdbcType=VARCHAR}, #{templateName,jdbcType=VARCHAR}, #{templateLabel,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER}, #{templateVariable,jdbcType=VARCHAR}, #{lawId,jdbcType=BIGINT},
      #{signId,jdbcType=BIGINT}, #{originalUrl,jdbcType=VARCHAR}, #{previewUrl,jdbcType=VARCHAR},
      #{previewPages,jdbcType=INTEGER}, #{pageHeader,jdbcType=VARCHAR}, #{pageFooter,jdbcType=VARCHAR},
      #{cutHeader,jdbcType=VARCHAR}, #{cutFooter,jdbcType=VARCHAR}, #{sourceFileType,jdbcType=INTEGER},
      #{teamId,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR}, #{type,jdbcType=CHAR},
      #{bodyContent,jdbcType=LONGVARCHAR}, #{positionData,jdbcType=LONGVARCHAR})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.controller.letterDoc.letter.domain.LetterTemplate">
    update letter_template
    <set>
      <if test="templateCode != null">
        template_code = #{templateCode,jdbcType=VARCHAR},
      </if>
      <if test="classifyId != null">
        classify_id = #{classifyId,jdbcType=BIGINT},
      </if>
      <if test="classifyLabel != null">
        classify_label = #{classifyLabel,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templateLabel != null">
        template_label = #{templateLabel,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="templateVariable != null">
        template_variable = #{templateVariable,jdbcType=VARCHAR},
      </if>
      <if test="lawId != null">
        law_id = #{lawId,jdbcType=BIGINT},
      </if>
      <if test="signId != null">
        sign_id = #{signId,jdbcType=BIGINT},
      </if>
      <if test="originalUrl != null">
        original_url = #{originalUrl,jdbcType=VARCHAR},
      </if>
      <if test="previewUrl != null">
        preview_url = #{previewUrl,jdbcType=VARCHAR},
      </if>
      <if test="previewPages != null">
        preview_pages = #{previewPages,jdbcType=INTEGER},
      </if>
      <if test="pageHeader != null">
        page_header = #{pageHeader,jdbcType=VARCHAR},
      </if>
      <if test="pageFooter != null">
        page_footer = #{pageFooter,jdbcType=VARCHAR},
      </if>
      <if test="cutHeader != null">
        cut_header = #{cutHeader,jdbcType=VARCHAR},
      </if>
      <if test="cutFooter != null">
        cut_footer = #{cutFooter,jdbcType=VARCHAR},
      </if>
      <if test="sourceFileType != null">
        source_file_type = #{sourceFileType,jdbcType=INTEGER},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=CHAR},
      </if>
      <if test="bodyContent != null">
        body_content = #{bodyContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="positionData != null">
        position_data = #{positionData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getCaseInfo" resultType="com.zws.appeal.controller.letterDoc.letter.pojo.CaseInfoPojo">
      select
<!--      cil.remaining_due      as remainingDue,-->
<!--      cil.residual_principal as residualPrincipal,-->
      cil.entrust_money         as entrustMoney,
      cil.loan_money            as loanMoney,
      cil.yc_interest_balance   as ycInterestBalance,
      cil.interest_money        as interestMoney,
      cil.residual_principal    as residualPrincipal,
      cil.service_fee           as serviceFee,
      cil.yc_disbursement       as ycDisbursement,
      cil.overdue_start         as overdueStart,
      cil.contract_no           as contractNo,
      cil.yc_contract_no        as ycContractNo,
      cil.yc_currencies         as ycCurrencies,
      cil.yc_purpose            as ycPurpose,
      cil.yc_lending_rate       as ycLendingRate,
      cil.yc_overdue_days       as ycOverdueDays,
      cil.yc_default_rate       as ycDefaultRate,
      cil.loan_principal        as loanPrincipal,
      cil.repayment_monthly     as repaymentMonthly,
      cil.loan_periods          as loanPeriods,
      cil.already_periods       as alreadyPeriods,
      cil.not_periods           as notPeriods,
      cil.repayment_date        as repaymentDate,
      cil.amount_final_date     as amountFinalDate,
      cil.loan_institution      as loanInstitution,
      cil.yc_loan_bank          as ycLoanBank,
      cil.yc_business_type      as ycBusinessType,
      cil.product_type          as productType,
      cil.yc_contract_money     as ycContractMoney,
      cil.yc_loan_term          as ycLoanTerm,
      cil.yc_loan_issuance_date as ycLoanIssuanceDate,
      cil.yc_loan_maturity_date as ycLoanMaturityDate,
      cib.id                    as id,
      cib.client_name           as clientName,
      cib.client_id_type        as clientIdType,
      cib.client_id_num         as clientIdcard,
      cib.client_phone          as clientPhone,
      cib.bank_card_number      as bankCardNumber,
      cib.registered_address    as registeredAddress,
      cib.client_sex            as clientSex,
      cib.client_age            as clientAge,
      cib.client_birthday       as clientBirthday,
      cib.security_name         as securityName,
      cib.security_id_type      as securityIdType,
      cib.security_id_num       as securityIdNum,
      cib.security_phone        as securityPhone,
      cib.place_of_work         as placeOfWork,
      cib.working_address       as workingAddress,
      cib.residential_address   as residentialAddress,
      cib.home_address          as homeAddress,
      cib.bank_name             as bankName,
      cib.occupation            as occupation,
      cib.qq                    as qq,
      cib.weixin                as weixin,
      cib.mailbox               as mailbox,
      cib.asset_no              as assetNo,
      cib.marital_status        as maritalStatus,
      cil.yc_five_level         as ycFiveLevel,
      cil.yc_repayment_method   as ycRepaymentMethod,
      cil.yc_litigation_status  as ycLitigationStatus,
      cil.yc_is_dishonest       as ycIsDishonest,
      cil.yc_is_limit_consumption as ycIsLimitConsumption,
      cil.yc_write_date         as ycWriteDate,
      cil.amount_final_repayment as amountFinalRepayment,
      cil.case_region           as caseRegion,
      cil.account_period        as accountPeriod,
      cil.yc_abd_repayment      as ycAbdRepayment,
      cil.yc_abd_principal      as ycAbdPrincipal,
      cil.yc_abd_interest       as ycAbdInterest,
      cil.sy_yh_principal       as syYhPrincipal,
      cil.sy_yh_interest        as syYhInterest,
      cil.sy_yh_fees            as syYhFees,
      cil.remaining_due         as remainingDue,
      cil.sy_yh_default         as syYhDefault
      from case_info_loan as cil
      left join case_info_base as cib on (cil.case_id = cib.case_id)
      where
      cil.del_flag=0 and cib.del_flag=0
      and cil.case_id=#{caseId}
    </select>

    <select id="selectAmountCalledBack" resultType="decimal">
        select sum(cas.amount_called_back)
        from case_info_loan as cas
        where cas.del_flag = 0
          and cas.case_id = #{caseId}
    </select>


</mapper>
