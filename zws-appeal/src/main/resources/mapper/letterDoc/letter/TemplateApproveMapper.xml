<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zws.appeal.controller.letterDoc.letter.mapper.TemplateApproveMapper">
    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO template_approve
        (approve_id,
         template_name,
         template_type,
         handle_status,
         handle_time,
         approve_status,
         approve_template_code,
         created_by_id,
         created_by,
         created_time,
         update_by,
         update_time,
         del_flag,
         preview_url,
         preview_pages)
        VALUES (#{approveId},
                #{templateName},
                #{templateType},
                #{handleStatus},
                #{handleTime},
                #{approveStatus},
                #{approveTemplateCode},
                #{createdById},
                #{createdBy},
                #{createdTime},
                #{updateBy},
                #{updateTime},
                #{delFlag},
                #{previewUrl},
                #{previewPages})
    </insert>
    <update id="removeTemplateApprove">
        update template_approve
        set del_flag = 1 , approve_status = 5
        where approve_id = #{approveId}
    </update>
    <update id="remove">
        update document_template
        set del_flag = 1
        where id = #{id}
    </update>
    <select id="selectApproveByApproveId" resultType="com.zws.standardmain.domain.entity.ZwsApproveRecord">
        select id,approve_code as approveCode,applicant_type as applicantType,
               applicant_platform as applicantPlatform,applicant,
               applicant_id as applicantId,apply_date as applyDate,approve_state as approveState,
               examine_time as examineTime,
               examine_by as examineBy,examine_by_id as examineById,reason,
               approve_platform as approvePlatform,approve_sort as approveSort,
               next_approve_platform as nextApprovePlatform,next_approve_sort as nextApproveSort,
               approve_process as approveProcess,del_flag as delFlag,tenant_id as tenantId,
               create_by as createBy,
               create_by_id as createById,create_time as createTime,update_by as updateBy,
               update_by_id as updateById,update_time as updateTime
        from approve_record
        where id = #{approveId}
    </select>
</mapper>