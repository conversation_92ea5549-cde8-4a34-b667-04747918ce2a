<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.controller.letterDoc.letter.mapper.LetterClassifyMapper">
  <resultMap id="BaseResultMap" type="com.zws.appeal.controller.letterDoc.letter.domain.LetterClassify">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="classify_name" jdbcType="VARCHAR" property="classifyName" />
    <result column="classify_label" jdbcType="VARCHAR" property="classifyLabel" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, classify_name, classify_label, status, team_id, create_by, create_by_id, create_time,
    update_by, update_by_id, update_time, del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from letter_classify
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectList" resultType="com.zws.appeal.controller.letterDoc.letter.domain.LetterClassify">
    select
        id                      as id,
        classify_name           as classifyName,
        classify_label          as classifyLabel,
        status                  as status,
        team_id                 as teamId,
        create_by               as createBy,
        create_by_id            as createById,
        create_time             as createTime,
        update_by               as updateBy,
        update_by_id            as updateById,
        update_time             as updateTime,
        del_flag                as delFlag
    from letter_classify
    where del_flag='0'
    <if test="id!=null">
      and id=#{id}
    </if>
    <if test="classifyName!=null and classifyName!=''">
      and classify_name=#{classifyName}
    </if>
    <if test="status!=null">
      and status=#{status}
    </if>
    <if test="teamId!=null">
      and team_id=#{teamId}
    </if>
    <if test="classifyLabel!=null and classifyLabel!=''">
      and classify_label=#{classifyLabel}
    </if>
    <if test="classifyNames!=null and classifyNames.size()>0 ">
      and classify_name in
      <foreach collection="classifyNames" item="classifyName" separator="," open="(" close=")">
        #{classifyName}
      </foreach>
    </if>
    <if test="createBy != null and createBy != ''">
      and create_by = #{createBy}
    </if>
    order by create_time desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from letter_classify
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.zws.appeal.controller.letterDoc.letter.domain.LetterClassify" keyColumn="id" useGeneratedKeys="true">
    insert into letter_classify (id, classify_name, classify_label,
      status, team_id, create_by,
      create_by_id, create_time, update_by,
      update_by_id, update_time, del_flag
      )
    values (#{id,jdbcType=BIGINT}, #{classifyName,jdbcType=VARCHAR}, #{classifyLabel,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER}, #{teamId,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR},
      #{createById,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
      #{updateById,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR}
      )
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.controller.letterDoc.letter.domain.LetterClassify">
    update letter_classify
    <set>
      <if test="classifyName != null">
        classify_name = #{classifyName,jdbcType=VARCHAR},
      </if>
      <if test="classifyLabel != null">
        classify_label = #{classifyLabel,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectIsExist" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM letter_classify
        where 1=1 AND classify_name=#{classifyName}
        and del_flag = 0
    </select>
  <select id="selectAllclassifyName" resultType="java.lang.String">
    select classify_name from letter_classify
    where del_flag = 0
  </select>
</mapper>
