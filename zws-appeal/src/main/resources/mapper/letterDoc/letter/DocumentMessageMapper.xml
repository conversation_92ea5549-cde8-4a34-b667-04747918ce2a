<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.controller.letterDoc.letter.mapper.DocumentMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zws.appeal.controller.letterDoc.letter.domain.DocumentMessage">
        <id column="id" property="id" />
        <result column="batch_num" property="batchNum" />
        <result column="template_id" property="templateId" />
        <result column="status" property="status" />
        <result column="quantity" property="quantity" />
        <result column="proce" property="proce" />
        <result column="proce_sort" property="proceSort" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_by_id" property="createById" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_id" property="updateById" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
        <result column="template_name" property="templateName"/>
        <result column="classify_label" property="classifyLabel"/>
        <result column="classify_name" property="classifyName"/>
        <result column="import_status" property="importStatus"/>
        <result column="source_file_type" property="sourceFileType"/>
        <result column="zip_file_url" property="zipFileUrl"/>
        <result column="not_zip_file_url" property="notZipFileUrl"/>
        <result column="source_file_url" property="sourceFileUrl"/>
        <result column="delivery_way" property="deliveryWay"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, batch_num, template_id, status, quantity, proce, proce_sort, tenant_id, create_by, create_by_id, import_status, create_time, update_by,
         update_by_id, update_time, del_flag, source_file_type, delivery_way,not_zip_file_url
    </sql>

    <sql id="Letter_SQL">
        <!--全部-->
        and (
        <!--待审核-->
        (dd.proce = 0 AND dd.proce_sort = #{approveSort} - 1)
        OR (
        <!--审核中-->
        dd.proce =1
        AND( dd.proce_sort = #{approveSort} - 1 OR c.reviewer_id = #{userId})
        )
        OR (
        <!--已完成-->
        dd.proce IN (2)
        AND c.reviewer_id = #{userId}
        )
        )
    </sql>
    <select id="selectList" resultMap="BaseResultMap">
        SELECT
            lm.id,lm.batch_num,lm.status,lm.quantity,lm.create_by,lm.create_by_id,lm.create_time,lm.import_status,lm.proce,
            lt.template_name,
               lt.classify_label,
               ltc.classify_name,
               lm.update_time
        FROM
            document_message AS lm
                LEFT JOIN letter_template AS lt ON (lm.template_id=lt.id)
                LEFT JOIN letter_template_classify AS ltc on(ltc.id=lt.classify_id)
        LEFT JOIN letter_doc AS dd on (dd.letter_id=lm.id and dd.del_flag=0)
        LEFT JOIN (
        SELECT approve_code, apply_id,approve_start, approve_time,reviewer,reviewer_id,refuse_reason , approve_sort FROM
        approve_proce WHERE approve_code= #{approveCode}  and  reviewer_id=#{userId}
        )AS c ON(dd.id=c.apply_id)
        WHERE lm.del_flag=0 AND lm.tenant_id=#{tenantId}
        <if test="approveData!=null and approveData==true">
            <include refid="Letter_SQL"/>
        </if>
        <if test="classifyId!=null">
            and  lt.classify_id=#{classifyId}
        </if>
        <if test="classifyIds!=null and classifyIds.size()>0">
          and lt.classify_label in
          <foreach collection="classifyIds" item="label" separator="," open="(" close=")">
             #{label}
          </foreach>
        </if>
        <if test="templateName!=null and templateName!=''">
            and  lt.template_name like  concat('%',#{templateName},'%')
        </if>
        <if test="createBy!=null and createBy!=''">
            and  lm.create_by like  concat('%',#{createBy},'%')
        </if>
        <if test="status!=null">
            and  lm.status=#{status}
        </if>
        <if test="batchNum!=null and batchNum!=''">
            and  lm.batch_num like concat('%',#{batchNum},'%')
        </if>
        <if test="proce!=null">
            and lm.proce=#{proce}
        </if>
          	<if test="proceList !=null and proceList.size()>0 ">
			and lm.proce in
			<foreach collection="proceList" item="proce" separator="," open="(" close=")">
				#{proce}
			</foreach>
		</if>
		<if test="importStatus != null">
            and lm.import_status = #{importStatus}
        </if>

        <if test="createTime1!=null">
            and lm.create_time>=#{createTime1}
        </if>
        <if test="createTime2!=null">
            and lm.create_time&lt;=#{createTime2}
        </if>
        <if test="updateTime1!=null">
            and lm.update_time>=#{updateTime1} and lm.status!=0
        </if>
        <if test="updateTime2!=null">
            and lm.update_time&lt;=#{updateTime2} and lm.status!=0
        </if>
        <if test="ids!=null and ids.size()>0">
            and lm.id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        group by lm.id
        order by lm.create_time desc
    </select>
    <select id="countLetterProce" resultType="java.util.Map">
        SELECT  IFNULL(l.proce,0) AS proce ,COUNT(1) AS num
        FROM document AS l
        WHERE l.letter_id=#{id} AND l.del_flag=0  GROUP BY l.proce
    </select>


    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from document_message
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectedApproveProceLetterId" resultType="java.lang.Integer">
        SELECT l.letter_id FROM document AS l
        LEFT JOIN approve_proce AS ap ON (ap.apply_id=l.id AND ap.approve_code=8)
        WHERE ap.reviewer_id=#{createById} and l.tenant_id=#{tenantId}
        GROUP BY l.letter_id
    </select>
    <select id="selectExportZip" resultMap="BaseResultMap">
        select
            id,batch_num,status,source_file_url,zip_file_url,not_zip_file_url
        from document_message
        where del_flag=0
        and id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectCount" resultType="java.lang.Long">
        SELECT COUNT(1) AS num
        FROM document AS l
        WHERE l.letter_id=#{id} AND l.del_flag=0
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from document_message
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zws.appeal.controller.letterDoc.letter.domain.DocumentMessage" keyProperty="id" useGeneratedKeys="true">
        insert into document_message (id, batch_num, template_id,
                                    status, quantity, proce,
                                    proce_sort, source_file_url, tenant_id,
                                    import_status, create_by, create_by_id,
                                    create_time, update_by, update_by_id,
                                    update_time, del_flag, source_file_type,delivery_way)
        values (#{id,jdbcType=BIGINT}, #{batchNum,jdbcType=VARCHAR}, #{templateId,jdbcType=BIGINT},
                #{status,jdbcType=INTEGER}, #{quantity,jdbcType=INTEGER}, #{proce,jdbcType=INTEGER},
                #{proceSort,jdbcType=INTEGER}, #{sourceFileUrl,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT},
                #{importStatus,jdbcType=INTEGER}, #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT},
                #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT},
                #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR}, #{sourceFileType,jdbcType=INTEGER},#{deliveryWay,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.controller.letterDoc.letter.domain.DocumentMessage">
        update document_message
        <set>
            <if test="batchNum != null">
                batch_num = #{batchNum,jdbcType=VARCHAR},
            </if>
            <if test="templateId != null">
                template_id = #{templateId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="proce != null">
                proce = #{proce,jdbcType=INTEGER},
            </if>
            <if test="proceSort != null">
                proce_sort = #{proceSort,jdbcType=INTEGER},
            </if>
            <if test="sourceFileUrl != null">
                source_file_url = #{sourceFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="importStatus != null">
                import_status = #{importStatus,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createById != null">
                create_by_id = #{createById,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateById != null">
                update_by_id = #{updateById,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sourceFileType != null">
                source_file_type = #{sourceFileType,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
           <if test="deliveryWay != null">
                delivery_way = #{deliveryWay,jdbcType=VARCHAR},
            </if>
            <if test="zipFileUrl != null">
                zip_file_url = #{zipFileUrl},
            </if>
            <if test="notZipFileUrl != null">
                not_zip_file_url = #{notZipFileUrl},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zws.appeal.controller.letterDoc.letter.domain.DocumentMessage">
        update document_message
        set batch_num = #{batchNum,jdbcType=VARCHAR},
            template_id = #{templateId,jdbcType=BIGINT},
            status = #{status,jdbcType=INTEGER},
            quantity = #{quantity,jdbcType=INTEGER},
            proce = #{proce,jdbcType=INTEGER},
            proce_sort = #{proceSort,jdbcType=INTEGER},
            source_file_url = #{sourceFileUrl,jdbcType=VARCHAR},
            tenant_id = #{tenantId,jdbcType=BIGINT},
            import_status = #{importStatus,jdbcType=INTEGER},
            create_by = #{createBy,jdbcType=VARCHAR},
            create_by_id = #{createById,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_by = #{updateBy,jdbcType=VARCHAR},
            update_by_id = #{updateById,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            source_file_type = #{sourceFileType,jdbcType=INTEGER},
            delivery_way = #{deliveryWay,jdbcType=VARCHAR},
            zip_file_url = #{zipFileUrl},
            del_flag = #{delFlag,jdbcType=CHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
