<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zws.appeal.controller.letterDoc.letter.mapper.LetterStageMapper">

    <select id="tageTotal"  resultType="integer">
        SELECT COUNT(stage_name) AS count
        FROM dispose_stage_config;
    </select>
    <select id="selectAppealStage" resultType="java.util.Map">
        SELECT t.stage_name as stageName
             , COUNT(a.stage_name) AS count
        FROM (
            SELECT DISTINCT stage_name
            FROM dispose_stage_config
            ) AS t
            JOIN dispose_stage_config AS a ON a.stage_name = t.stage_name
        GROUP BY t.stage_name;
    </select>
    <select id="selectCategoryId" resultType="java.lang.Long">
        select id
        from team_create
        where category = "调诉机构"
    </select>
    <select id="selectAllCase" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM case_manage
        WHERE outsourcing_team_id = #{id};
    </select>

</mapper>