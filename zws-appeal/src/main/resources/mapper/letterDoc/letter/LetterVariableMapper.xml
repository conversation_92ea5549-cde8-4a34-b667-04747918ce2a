<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.controller.letterDoc.letter.mapper.LetterVariableMapper">
  <resultMap id="BaseResultMap" type="com.zws.appeal.controller.letterDoc.letter.domain.LetterVariable">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="variable_name" jdbcType="VARCHAR" property="variableName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id                  as id,
    variable_name       as variableName,
    status              as status,
    team_id             as teamId,
    create_by           as createBy,
    create_by_id        as createById,
    create_time         as createTime,
    update_by           as updateBy,
    update_by_id        as updateById,
    update_time         as updateTime,
    del_flag            as delFlag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from letter_variable
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectList" resultType="com.zws.appeal.controller.letterDoc.letter.domain.LetterVariable">
    select
    <include refid="Base_Column_List" />
    from letter_variable
    where del_flag = '0'
    <if test="teamId !=null">
      and team_id = #{teamId}
    </if>
    <if test="variableName !=null and variableName!=''">
      and variable_name = #{variableName}
    </if>
    <if test="status !=null ">
      and status = #{status}
    </if>
    <if test="variableNames!=null and variableNames.size()>0 ">
      and variable_name in
      <foreach collection="variableNames" item="variableName" separator="," open="(" close=")">
        #{variableName}
      </foreach>
    </if>
    <if test="createBy != null and createBy != ''">
      and create_by = #{createBy}
    </if>

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from letter_variable
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.zws.appeal.controller.letterDoc.letter.domain.LetterVariable">
    insert into letter_variable (id, variable_name, status,
      team_id, create_by, create_by_id,
      create_time, update_by, update_by_id,
      update_time, del_flag)
    values (#{id,jdbcType=BIGINT}, #{variableName,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{teamId,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR})
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.controller.letterDoc.letter.domain.LetterVariable">
    update letter_variable
    <set>
      <if test="variableName != null">
        variable_name = #{variableName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>
