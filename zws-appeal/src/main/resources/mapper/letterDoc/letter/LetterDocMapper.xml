<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.controller.letterDoc.letter.mapper.LetterDocMapper">
  <resultMap id="BaseResultMap" type="com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_id"  property="caseId" />
    <result column="law_agency_id" jdbcType="BIGINT" property="lawAgencyId" />
    <result column="letter_template_id" jdbcType="BIGINT" property="letterTemplateId" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="item_data" jdbcType="VARCHAR" property="itemData" />
    <result column="preview_url" jdbcType="VARCHAR" property="previewUrl" />
    <result column="sign_preview_url" jdbcType="VARCHAR" property="signPreviewUrl" />
    <result column="preview_pages" jdbcType="INTEGER" property="previewPages" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="doc_type" jdbcType="INTEGER" property="docType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="sign_status" jdbcType="INTEGER" property="signStatus" />
    <result column="sign_err_msg" jdbcType="VARCHAR" property="signErrMsg" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="client_name" jdbcType="CHAR" property="clientName" />
    <result column="preview_url" jdbcType="CHAR" property="previewUrl" />
    <result column="template_name" jdbcType="CHAR" property="templateName" />
    <result column="classify_label" jdbcType="CHAR" property="classifyLabel" />
    <result column="content" jdbcType="LONGNVARCHAR" property="content"/>
    <result column="letter_id" property="letterId"/>
    <result column="proce" property="proce"/>
    <result column="proce_sort" property="proceSort"/>
    <result column="examine_time" property="examineTime"/>
    <result column="examine_by" property="examineBy"/>
    <result column="examine_by_id" property="examineById"/>
    <result column="source_file_type" property="sourceFileType"/>
    <result column="desensitize_url" property="desensitizeUrl"/>
    <result column="file_link" property="fileLink"/>
    <result column="visit_num" property="visitNum"/>
    <result column="random_id" property="randomId"/>
    <result column="mailing_status" property="mailingStatus"/>
  </resultMap>

      <resultMap id="SeparateResultMap" type="com.zws.appeal.controller.letterDoc.letter.domain.RetrievalFileSeparate">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="file_id" jdbcType="BIGINT" property="fileId"/>
        <result column="first_name" jdbcType="VARCHAR" property="firstName"/>
        <result column="modify_name" jdbcType="VARCHAR" property="modifyName"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="file_ip" jdbcType="VARCHAR" property="fileIp"/>
        <result column="file_port" jdbcType="VARCHAR" property="filePort"/>
        <result column="file_route" jdbcType="VARCHAR" property="fileRoute"/>
        <result column="founder" jdbcType="VARCHAR" property="founder"/>
        <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime"/>
        <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy"/>
        <result column="modified_time" jdbcType="TIMESTAMP" property="modifiedTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>
        <sql id="separate_Column_List">
        id
        , case_id, file_id, first_name, modify_name, file_url, file_ip, file_port, file_route,
    founder, creation_time, modified_by, modified_time, del_flag
    </sql>
  <!--简单列表数据-->
  <sql id="Simple_Column_List">
    l
    .
    id
    , l.letter_id, l.serial_no, l.status,l.sign_status, l.proce, l.proce_sort, l.examine_time, l.examine_by, l.examine_by_id, l.team_id, l.create_by, l.create_by_id, l.create_time, l.update_by, l.update_by_id, l.random_id, l.update_time,l.mailing_status,
        l.desensitize_url,l.file_link,l.visit_num,lt.template_name as template_name,lm.delivery_way as deliveryWay,ssr.send_status as sendStatus,
        li.address as address , li.express_number as expressNumber, ssr.respdesc as respdesc
  </sql>
  <sql id="Base_Column_List">
    id, case_id,law_agency_id, letter_template_id, serial_no, item_data, preview_url, sign_preview_url,
    preview_pages, file_name, doc_type, status, sign_status, sign_err_msg, sign_time,
    team_id, create_by, create_by_id, create_time, update_by, update_by_id, update_time,
    del_flag,content,
      source_file_type, mailing_status, desensitize_url ,file_link , visit_num, random_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from letter_doc
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByCaseId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from letter_doc
    where case_id = #{id,jdbcType=BIGINT}
    order by create_time desc limit 1
  </select>
  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from letter_doc
    <where>
      and del_flag='0'
      <if test="caseId!=null">
        and case_id=#{caseId}
      </if>
      <if test="lawAgencyId!=null">
        and law_agency_id=#{lawAgencyId}
      </if>
      <if test="teamId!=null">
        and team_id=#{teamId}
      </if>
      <if test="letterTemplateId!=null">
        and letter_template_id=#{letterTemplateId}
      </if>
      <if test="serialNo!=null and serialNo!=''">
        and serial_no=#{serialNo}
      </if>
      <if test="serialNo!=null and serialNo!=''">
        and serial_no=#{serialNo}
      </if>
      <if test="docType!=null">
        and doc_type=#{docType}
      </if>
      <if test="status!=null">
        and status=#{status}
      </if>
      <if test="signStatus!=null">
        and sign_status=#{signStatus}
      </if>
      <if test="signStatus!=null">
        and sign_status=#{signStatus}
      </if>
      <if test="letterId!=null">
        and letter_id=#{letterId}
      </if>
      <if test="ids!=null and ids.size()>0">
        and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
          #{id}
        </foreach>
      </if>
    </where>
    order by create_time desc
  </select>
  <select id="selectLetterIdByIds" resultType="java.lang.Integer">
    select distinct letter_id from letter_doc where id in
    <foreach collection="ids" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
    and del_flag=0
  </select>

  <select id="selectByRandomId" resultMap="BaseResultMap">
    select id, desensitize_url, random_id, visit_num, source_file_type, sign_preview_url
    from letter_doc
    where random_id = #{randomId,jdbcType=VARCHAR}
      and del_flag = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from letter_doc
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <select id="selectSimpleList" resultMap="BaseResultMap">
    SELECT
    <include refid="Simple_Column_List"></include>
    FROM letter_doc AS l
    LEFT JOIN document_message AS lm ON(l.letter_id=lm.id)
    LEFT JOIN logistics_info AS li ON (l.serial_no=li.letters_number and li.del_flag = 0)
    LEFT JOIN letter_template AS lt ON (lm.template_id=lt.id)
    LEFT JOIN sms_send_records AS ssr ON (l.id=ssr.case_id and ssr.del_flag=0)
    WHERE l.del_flag= 0 and l.team_id=#{teamId}
    <if test="serialNo!=null and serialNo!=''">
      and l.serial_no LIKE CONCAT('%',#{serialNo},'%')
    </if>
    <if test="status!=null">
      and l.status=#{status}
    </if>
    <if test="letterId!=null">
      and l.letter_id=#{letterId}
    </if>
    <if test="sendStatus != null">
      and ssr.send_status = #{sendStatus,jdbcType=INTEGER}
    </if>
    <if test="deliveryWay != null and deliveryWay != ''">
      and lm.delivery_way = #{deliveryWay,jdbcType=VARCHAR}
    </if>
    <if test="ids!=null and ids.size()>0">
      and l.id in
      <foreach collection="ids" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="deliveryWayList != null and deliveryWayList.size() > 0">
      and lm.delivery_way in
      <foreach collection="deliveryWayList" item="way" open="(" close=")" separator=",">
        #{way,jdbcType=VARCHAR}
      </foreach>
    </if>
    <if test="sendStatusList != null and sendStatusList.size() > 0">
      and ssr.send_status in
      <foreach collection="sendStatusList" item="param" open="(" close=")" separator="," index="index">
        #{param,jdbcType=INTEGER}
      </foreach>
    </if>
    order by l.create_time desc
  </select>

  <insert id="insert" parameterType="com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc" useGeneratedKeys="true" keyProperty="id">
    insert into letter_doc (id,case_id, law_agency_id, letter_template_id,
      serial_no, item_data, preview_url,
      sign_preview_url, preview_pages, file_name,
      doc_type, status, sign_status,
      sign_err_msg, sign_time, team_id,
      create_by, create_by_id, create_time,
      update_by, update_by_id, update_time,
      del_flag,content,letter_id,proce,proce_sort,examine_time,examine_by,examine_by_id,source_file_type,
                            desensitize_url,file_link,visit_num,random_id,mailing_status)
    values (#{id,jdbcType=BIGINT},#{caseId,jdbcType=BIGINT}, #{lawAgencyId,jdbcType=BIGINT}, #{letterTemplateId,jdbcType=BIGINT},
      #{serialNo,jdbcType=VARCHAR}, #{itemData,jdbcType=VARCHAR}, #{previewUrl,jdbcType=VARCHAR},
      #{signPreviewUrl,jdbcType=VARCHAR}, #{previewPages,jdbcType=INTEGER}, #{fileName,jdbcType=VARCHAR},
      #{docType,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{signStatus,jdbcType=INTEGER},
      #{signErrMsg,jdbcType=VARCHAR}, #{signTime,jdbcType=TIMESTAMP}, #{teamId,jdbcType=BIGINT},
      #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
      #{delFlag,jdbcType=CHAR},#{content,jdbcType=VARCHAR},
            #{letterId,jdbcType=BIGINT},#{proce,jdbcType=INTEGER}, #{proceSort,jdbcType=INTEGER},
            #{examineTime,jdbcType=TIMESTAMP}, #{examineBy,jdbcType=VARCHAR}, #{examineById,jdbcType=BIGINT},
            #{sourceFileType,jdbcType=INTEGER},#{desensitizeUrl,jdbcType=VARCHAR},#{fileLink,jdbcType=VARCHAR},
            #{visitNum,jdbcType=INTEGER},#{randomId,jdbcType=VARCHAR},#{mailingStatus,jdbcType=INTEGER}
            )
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc">
    update letter_doc
    <set>
      <if test="lawAgencyId != null">
        law_agency_id = #{lawAgencyId,jdbcType=BIGINT},
      </if>
      <if test="letterTemplateId != null">
        letter_template_id = #{letterTemplateId,jdbcType=BIGINT},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="itemData != null">
        item_data = #{itemData,jdbcType=VARCHAR},
      </if>
      <if test="previewUrl != null">
        preview_url = #{previewUrl,jdbcType=VARCHAR},
      </if>
      <if test="signPreviewUrl != null">
        sign_preview_url = #{signPreviewUrl,jdbcType=VARCHAR},
      </if>
      <if test="previewPages != null">
        preview_pages = #{previewPages,jdbcType=INTEGER},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="docType != null">
        doc_type = #{docType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="signStatus != null">
        sign_status = #{signStatus,jdbcType=INTEGER},
      </if>
      <if test="signErrMsg != null">
        sign_err_msg = #{signErrMsg,jdbcType=VARCHAR},
      </if>
      <if test="signTime != null">
        sign_time = #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getLetterDoc" resultMap="BaseResultMap">
  select
  <include refid="Base_Column_List" />
  from letter_doc
  <where>
         del_flag=0 and
         case_id=#{caseId} and
         doc_type=#{code}
  </where>
  </select>

  <select id="getLetterDocs" resultType="com.zws.appeal.controller.letterDoc.letter.domain.LetterDoc">
    select
    ld.id as id,
    ld.case_id as caseId,
    ld.item_data as itemData,
    ld.preview_url as previewUrl,
    ld.sign_preview_url as signPreviewUrl,
    ld.preview_pages as previewPages,
    ld.file_name as fileName,
    ld.create_by as createBy,
    lt.template_name as templateName,
    lt.classify_label as classifyLabel
    from letter_doc as ld
    left join letter_template as lt on (lt.id = ld.letter_template_id)
    <where>
      ld.del_flag=0 and
      ld.case_id=#{caseId} and
      ld.doc_type=#{code}
    </where>
  </select>

  <select id="selectDataManagementByCaseId" resultType="java.lang.String">
        SELECT client_id_num AS clientIdcard
        FROM case_info_base
        WHERE del_flag = 0
        AND case_id = #{caseId}
    </select>

    <select id="getArchivalData" resultMap="SeparateResultMap">
    select
    <include refid="separate_Column_List"/>
    from case_retrieval_file_separate
    <where>
        del_flag = 0
        <if test="_parameter != null">
            and client_idcard = #{clientIdcard}
        </if>
    </where>
    order by creation_time desc
    </select>
  <select id="selectAllList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from letter_doc
    where status = 0 and del_flag = 0;
  </select>

  <select id="getTemplateIdById" resultType="java.lang.Integer">
    SELECT distinct lt.id
    FROM letter_doc AS l
           LEFT JOIN document_message AS lm ON (l.letter_id = lm.id)
           LEFT JOIN letter_template AS lt ON (lm.template_id = lt.id)
    WHERE l.del_flag = 0
      AND l.id = #{id}
  </select>

  <select id="getPreviewUrl" resultMap="BaseResultMap">
  select
  ld.serial_no,
  ld.preview_url ,
  cm.client_name,
  lt.template_name
  from letter_doc as ld
  left join case_manage as cm  on (cm.case_id = ld.case_id)
  left join letter_template as lt on (lt.id = ld.letter_template_id)
  <where>
  cm.del_flag=0 and ld.del_flag=0
  and ld.case_id=#{id}
  and ld.letter_template_id =#{templateId}
  order by ld.create_time desc limit 1
</where>

</select>

    <select id="getTemplateId" resultType="java.lang.Long">
    select distinct letter_template_id from letter_doc
    where del_flag=0
    and case_id=#{id}
    </select>
  <select id="selectByPreviewUrl" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from letter_doc
    where preview_url = #{previewUrl}
  </select>

</mapper>
