<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.controller.letterDoc.law.mapper.LawAgencyMapper">
  <resultMap id="BaseResultMap" type="com.zws.appeal.controller.letterDoc.law.domain.LawAgency">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="court_name" jdbcType="VARCHAR" property="courtName" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="jurisdiction_economize" jdbcType="VARCHAR" property="jurisdictionEconomize" />
    <result column="jurisdiction_market" jdbcType="VARCHAR" property="jurisdictionMarket" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, team_id, agency_type, court_name, address, phone, jurisdiction_economize, jurisdiction_market,
    create_by, create_by_id, create_time, update_by, update_by_id, update_time, del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from law_agency
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectList" resultType="com.zws.appeal.controller.letterDoc.law.domain.LawAgency">
    select id AS id,
    team_id AS teamId,
    agency_type AS agencyType,
    court_name AS courtName,
    jurisdiction_economize AS jurisdictionEconomize,
    jurisdiction_market AS jurisdictionMarket,
    phone AS phone,
    address AS address,
    create_by AS createBy,
    create_time AS createTime,
    update_by AS updateBy,
    update_time AS updateTime
    from law_agency
    where del_flag = 0 and team_id = #{teamId}
    <if test="courtName != null and courtName != ''">
      and court_name like concat('%', #{courtName}, '%')
    </if>
    <if test="jurisdictionEconomize != null and jurisdictionEconomize != ''">
      and jurisdiction_economize like concat('%', #{jurisdictionEconomize}, '%')
    </if>
    <if test="jurisdictionMarket != null and jurisdictionMarket != ''">
      and jurisdiction_market like concat('%', #{jurisdictionMarket}, '%')
    </if>
    order by create_time desc
  </select>

  <select id="selectCourtId" resultType="java.lang.Long">
    select letter_template_id AS letterTemplateId
    from law_agency_letter
    where law_agency_id = #{lawAgencyId}
  </select>
  <select id="getCourtNameOptions" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from law_agency
    where del_flag = 0 and team_id = #{teamId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from law_agency
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawAgency" useGeneratedKeys="true" keyProperty="id">
    insert into law_agency (id, team_id,
                            court_name, address, phone,
                            jurisdiction_economize, jurisdiction_market, create_by,
      create_by_id, create_time, update_by, 
      update_by_id, update_time, del_flag
      )
    values (#{id,jdbcType=BIGINT}, #{teamId,jdbcType=BIGINT},
      #{courtName,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR},
      #{jurisdictionEconomize,jdbcType=VARCHAR}, #{jurisdictionMarket,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
      #{createById,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateById,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR}
      )
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawAgency">
    update law_agency
    <set>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="courtName != null">
        court_name = #{courtName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="jurisdictionEconomize != null">
        jurisdiction_economize = #{jurisdictionEconomize,jdbcType=VARCHAR},
      </if>
      <if test="jurisdictionMarket != null">
        jurisdiction_market = #{jurisdictionMarket,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

    <select id="selectWithLawAgency" resultType="com.zws.appeal.controller.letterDoc.law.domain.LawAgency">
  select
  court_name as courtName,
  id         as `id`
  from law_agency
  <where>
  del_flag=0
  and team_id=#{teamId}
</where>

</select>

</mapper>