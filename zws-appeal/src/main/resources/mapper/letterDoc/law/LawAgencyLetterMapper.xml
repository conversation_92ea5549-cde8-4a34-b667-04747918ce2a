<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.controller.letterDoc.law.mapper.LawAgencyLetterMapper">
  <resultMap id="BaseResultMap" type="com.zws.appeal.controller.letterDoc.law.domain.LawAgencyLetter">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="law_agency_id" jdbcType="BIGINT" property="lawAgencyId" />
    <result column="letter_template_id" jdbcType="BIGINT" property="letterTemplateId" />

    <result column="letter_template_name"  property="letterTemplateName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, law_agency_id, letter_template_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from law_agency_letter
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectLetterTemplateIds" resultType="java.lang.Long">
      select
        letter_template_id
      from law_agency_letter
      where law_agency_id=#{agencyId}
    </select>
  <select id="selectList" resultMap="BaseResultMap">
    select
      lal.id, lal.law_agency_id, lal.letter_template_id,
      lt.template_name as letter_template_name
    from law_agency_letter as lal
    left join document_template as lt on (lt.id = lal.letter_template_id and lt.del_flag='0')
    where lal.law_agency_id=#{lawAgencyId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from law_agency_letter
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByNotInList">
    delete from law_agency_letter
    where law_agency_id=#{agencyId} and law_agency_id not in
    <foreach collection="letterTemplateIds" item="letterTemplateId" open="(" close=")" separator=",">
      #{letterTemplateId}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawAgencyLetter">
    insert into law_agency_letter (id, law_agency_id, letter_template_id
      )
    values (#{id,jdbcType=BIGINT}, #{lawAgencyId,jdbcType=BIGINT}, #{letterTemplateId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawAgencyLetter">
    insert into law_agency_letter
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="lawAgencyId != null">
        law_agency_id,
      </if>
      <if test="letterTemplateId != null">
        letter_template_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="lawAgencyId != null">
        #{lawAgencyId,jdbcType=BIGINT},
      </if>
      <if test="letterTemplateId != null">
        #{letterTemplateId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawAgencyLetter">
    update law_agency_letter
    <set>
      <if test="lawAgencyId != null">
        law_agency_id = #{lawAgencyId,jdbcType=BIGINT},
      </if>
      <if test="letterTemplateId != null">
        letter_template_id = #{letterTemplateId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawAgencyLetter">
    update law_agency_letter
    set law_agency_id = #{lawAgencyId,jdbcType=BIGINT},
      letter_template_id = #{letterTemplateId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
