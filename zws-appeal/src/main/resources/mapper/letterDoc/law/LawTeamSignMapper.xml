<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.controller.letterDoc.law.mapper.LawTeamSignMapper">
  <resultMap id="BaseResultMap" type="com.zws.appeal.controller.letterDoc.law.domain.LawTeamSign">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="team_id" jdbcType="INTEGER" property="teamId" />
    <result column="law_sign_id" jdbcType="BIGINT" property="lawSignId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, team_id, law_sign_id, create_time, create_by_id, create_by, update_by, update_by_id, 
    update_time, del_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from law_team_sign
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from law_team_sign
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawTeamSign">
    insert into law_team_sign (id, team_id, law_sign_id, 
      create_time, create_by_id, create_by, 
      update_by, update_by_id, update_time, 
      del_flag)
    values (#{id,jdbcType=BIGINT}, #{teamId,jdbcType=INTEGER}, #{lawSignId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createById,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{delFlag,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawTeamSign">
    insert into law_team_sign
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
      <if test="lawSignId != null">
        law_sign_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=INTEGER},
      </if>
      <if test="lawSignId != null">
        #{lawSignId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawTeamSign">
    update law_team_sign
    <set>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=INTEGER},
      </if>
      <if test="lawSignId != null">
        law_sign_id = #{lawSignId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawTeamSign">
    update law_team_sign
    set team_id = #{teamId,jdbcType=INTEGER},
      law_sign_id = #{lawSignId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by_id = #{createById,jdbcType=BIGINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del_flag = #{delFlag,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>