<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.controller.letterDoc.law.mapper.LawSignMapper">
  <resultMap id="BaseResultMap" type="com.zws.appeal.controller.letterDoc.law.domain.LawSign">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sign_name" jdbcType="VARCHAR" property="signName" />
    <result column="sign_type" jdbcType="INTEGER" property="signType" />
    <result column="sign_code" jdbcType="VARCHAR" property="signCode" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="sign_pic" jdbcType="VARCHAR" property="signPic" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="blank_pdf" jdbcType="VARCHAR" property="blankPdf" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="create_id" jdbcType="BIGINT" property="createId"/>
    <result column="sign_pic_str" jdbcType="LONGVARCHAR" property="signPicStr"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, sign_name, sign_type, sign_code, status, sign_pic, remark, blank_pdf,sign_pic_str,
    create_by, create_by_id, create_time, update_by, update_by_id, update_time, del_flag,create_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from law_sign
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="getBySignCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from law_sign
    where del_flag='0' and sign_code=#{signCode}
  </select>
  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from law_sign
    where del_flag='0'
    <if test="ids != null and ids.size() != 0">
      and id in
      <foreach collection="ids" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="signCode!=null and signCode!=''">
      and sign_code=#{signCode}
    </if>
    <if test="signName!=null and signName!=''">
      and sign_name=#{signName}
    </if>
    <if test="signType!=null">
      and sign_type=#{signType}
    </if>
    <if test="status!=null">
      and status=#{status}
    </if>
    <if test="createId!=null">
      and create_id=#{createId}
    </if>
    order by create_time desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from law_sign
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawSign">
    insert into law_sign (id, sign_name,
      sign_type, sign_code, status,
      sign_pic, remark, blank_pdf,
      create_by, create_by_id, create_time,
      update_by, update_by_id, update_time,
      del_flag,create_id,sign_pic_str)
    values (#{id,jdbcType=BIGINT}, #{signName,jdbcType=VARCHAR},
      #{signType,jdbcType=INTEGER}, #{signCode,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{signPic,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{blankPdf,jdbcType=VARCHAR},
      #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
      #{delFlag,jdbcType=CHAR},#{createId,jdbcType=BIGINT},#{signPicStr,jdbcType=LONGVARCHAR})
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawSign">
    update law_sign
    <set>
      <if test="signName != null">
        sign_name = #{signName,jdbcType=VARCHAR},
      </if>
      <if test="signType != null">
        sign_type = #{signType,jdbcType=INTEGER},
      </if>
      <if test="signCode != null">
        sign_code = #{signCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
        sign_pic = #{signPic,jdbcType=VARCHAR},
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="blankPdf != null">
        blank_pdf = #{blankPdf,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=BIGINT},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
        sign_pic_str = #{signPicStr,jdbcType=LONGVARCHAR},
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


</mapper>
