<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.controller.letterDoc.letter.mapper.LawMapper">


    <select id="selectAttorneyOptions" resultType="com.zws.common.core.domain.Option">
        SELECT  ls.id  AS code,ls.sign_name  AS info FROM  law_sign  AS ls WHERE ls.del_flag=0 AND ls.status=0
<!--        <if test="lawId!=null">-->
<!--            AND ls.create_id=#{lawId}-->
<!--        </if>-->
<!--        <if test="signType!=null">-->
<!--            AND ls.sign_type=#{signType}-->
<!--        </if>-->
        order by ls.create_time
    </select>

    <select id="selectSignatureOptions" resultType="com.zws.common.core.domain.Option">
        SELECT  id  AS code,sign_pic  AS info FROM  law_sign WHERE del_flag=0 AND status=0
        <if test=" id !=null">
            AND id=#{id}
        </if>
    </select>

    <select id="getLawName" resultType="java.lang.String">
        SELECT  l.cname  FROM team_create AS l WHERE  l.delete_logo=0 and l.id=#{lawId}
    </select>
    <select id="getAttorneyName" resultType="java.lang.String">
        SELECT  ls.sign_name FROM  law_sign  AS ls
        WHERE ls.del_flag=0  and ls.id=#{signId}
    </select>
    <select id="getSignCode" resultType="java.lang.String">
        SELECT  ls.sign_code FROM  law_sign  AS ls
        <where>
            ls.del_flag=0
            <if test="signId != null">
                and ls.id=#{signId}
            </if>
        </where>
    </select>

    <select id="getLawSignById" resultType="com.zws.appeal.controller.letterDoc.law.domain.LawSign">
        SELECT
        id   ,
        sign_pic        AS signPic,
        sign_pic_str    AS signPicStr ,
        sign_code       AS signCode
        FROM  law_sign WHERE del_flag=0 AND status=0
        <if test=" id !=null">
            AND id=#{id}
        </if>

    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.zws.appeal.controller.letterDoc.law.domain.LawSign">
        <!--@mbg.generated-->
        update law_sign
        <set>
            <if test="createId != null">
                create_id = #{createId,jdbcType=BIGINT},
            </if>
            <if test="signName != null">
                sign_name = #{signName,jdbcType=VARCHAR},
            </if>
            <if test="signType != null">
                sign_type = #{signType,jdbcType=INTEGER},
            </if>
            <if test="signCode != null">
                sign_code = #{signCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="signPic != null">
                sign_pic = #{signPic,jdbcType=VARCHAR},
            </if>
            <if test="signPicStr != null">
                sign_pic_str = #{signPicStr,jdbcType=LONGVARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="blankPdf != null">
                blank_pdf = #{blankPdf,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createById != null">
                create_by_id = #{createById,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateById != null">
                update_by_id = #{updateById,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectLawOptions" resultType="com.zws.common.core.domain.Option">
        SELECT  l.id AS code,l.law_name AS info FROM law AS l WHERE l.status=0 AND l.del_flag=0
        order by l.create_time
    </select>



</mapper>
