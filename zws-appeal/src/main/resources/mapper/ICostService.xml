<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.AgCallMapper">

    <select id="selectCaseIdByPhone" resultType="java.lang.Long">
        SELECT cic.case_id
        FROM case_info_contact AS cic
                 LEFT JOIN case_manage AS cm ON (cic.case_id = cm.case_id and cm.del_flag = 0)
        WHERE cic.del_flag = '0'
          AND cic.phone_state = 0
          AND cic.contact_relation IN ("本人")
          AND cic.contact_phone = #{encrypt}
          AND cm.outsourcing_team_id = #{teamId}
    </select>
</mapper>
