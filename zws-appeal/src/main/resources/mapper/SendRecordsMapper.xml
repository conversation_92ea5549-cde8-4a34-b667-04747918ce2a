<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.SendRecordsMapper">

    <select id="selectFailMessage" resultType="com.zws.appeal.domain.SendRecords">
        select id as id, case_id as caseId,send_time as sendTime, send_status as sendStatus
        from sms_send_records
        where
        send_status="2"
        <if test="phone != null">and phone_number=#{phone}</if>
        <if test="dateTime != null">and send_time >= #{dateTime}</if>
        <if test="dateTime1 != null">and send_time &lt; #{dateTime1}</if>
        <if test="caseId != null">and case_id = #{caseId}</if>
    </select>


    <select id="selectSendRecords" resultType="com.zws.appeal.domain.SendRecords">
        select
        distinct
        ssr.id,
        ( SELECT COUNT( 1 ) FROM sms_reply_record AS srr WHERE srr.sms_id = ssr.id GROUP BY sms_id ) AS sendNum,
        ssr.case_id AS caseId,
        ssr.name AS name,
        ssr.relationship AS relationship,
        ssr.phone_number AS phoneNumber,
        ssr.created_by AS createdBy,
        ssr.sms_content AS smsContent,
        ssr.send_status AS sendStatus,
        ssr.send_time AS sendTime,
        ssr.sms_record_type AS smsRecordType,
        ssr.send_ip AS sendIp,
        ssr.respdesc AS respdesc,
        ssr.template_id AS templateId,
        tc.cname as teamName
        from sms_send_records as ssr
        LEFT JOIN team_employees AS te ON (ssr.created_by_id = te.id)
        LEFT JOIN team_create as tc on (te.create_id = tc.id)
        LEFT JOIN
        sms_reply_record as smsrr
        on ( smsrr.sms_id = ssr.id )
        where 1=1 and send_ip = 3
        <if test="createdByIds != null and createdByIds.size()>0">
            and ssr.created_by_id in
            <foreach item="item" collection="createdByIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            and te.department_id in
            <foreach item="deptId" collection="deptIds" separator="," open="(" close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="employeesIds!=null and employeesIds.size()>0">
            and te.id in
            <foreach item="employeesId" collection="employeesIds" separator="," open="(" close=")">
                #{employeesId}
            </foreach>
        </if>
        <if test="caseId != null">
            and ssr.case_id = #{caseId}
        </if>
        <if test="sendStatus != null and sendStatus != ''">
            and ssr.send_status = #{sendStatus}
        </if>
        <if test="relationship != null and relationship != ''">
            and ssr.relationship = #{relationship}
        </if>
        <if test="smsRecordType != null and smsRecordType != ''">
            and ssr.sms_record_type = #{smsRecordType}
        </if>
        <if test="name != null and name != ''">
            and ssr.name LIKE concat ('%',#{name},'%')
        </if>
        <if test="phoneNumber != null and phoneNumber != ''">
            and ssr.phone_number LIKE concat ('%',#{phoneNumber},'%')
        </if>
        <if test="sendTime1 != null ">
            and DATE_FORMAT(ssr.send_time , '%Y-%m-%d' ) >= DATE_FORMAT( #{sendTime1}, '%Y-%m-%d' )
        </if>
        <if test="sendTime2 != null ">
            and DATE_FORMAT(ssr.send_time , '%Y-%m-%d' ) &lt;= DATE_FORMAT( #{sendTime2}, '%Y-%m-%d' )
        </if>
        <if test="recvtime1 != null ">
            and DATE_FORMAT(smsrr.recvtime , '%Y-%m-%d' ) >= DATE_FORMAT( #{recvtime1}, '%Y-%m-%d' )
        </if>
        <if test="recvtime2 != null ">
            and DATE_FORMAT(smsrr.recvtime , '%Y-%m-%d' ) &lt;= DATE_FORMAT( #{recvtime2}, '%Y-%m-%d' )
        </if>
        <if test="createdBy != null and createdBy != ''">
            and ssr.created_by like concat('%',#{createdBy},'%')
        </if>
        <if test="sendNum != null and sendNum ==1">
            and ssr.id IN (
            SELECT sms_id FROM sms_reply_record GROUP BY sms_id
            )
        </if>
        <if test="sendNum != null and sendNum ==0">
            and ssr.id NOT IN (
            SELECT sms_id FROM sms_reply_record GROUP BY sms_id
            )
        </if>
        order by ssr.send_time desc
    </select>

    <select id="selectReplayById" resultType="com.zws.appeal.domain.ReplyRecord">
        select srr.id,
               srr.sms_id       AS smsId,
               srr.port         AS port,
               srr.phone        AS phone,
               srr.accesscode   AS accesscode,
               srr.recvtime     AS recvtime,
               srr.replyContent AS replyContent
        from sms_reply_record AS srr
                 LEFT JOIN sms_send_records AS ssr ON (srr.sms_id = ssr.id)
        where srr.sms_id = #{id}
    </select>

    <select id="selectReplayByIdNum" resultType="java.lang.Integer">
        select count(*)
        from sms_reply_record AS srr
                 LEFT JOIN sms_send_records AS ssr ON (srr.sms_id = ssr.id)
        where srr.sms_id = #{id}
    </select>

    <select id="selectRecordNumber" resultType="map">
        SELECT count(distinct ssr.id) as numbers,
        ssr.send_status as sendStatus
        FROM sms_send_records as ssr
        LEFT JOIN team_employees AS te ON (ssr.created_by_id = te.id)
        LEFT JOIN team_create as tc on (te.create_id = tc.id)
        LEFT JOIN
        sms_reply_record as smsrr
        on (
        smsrr.sms_id = ssr.id
        )
        where 1=1 and send_ip = 3
        <if test="createdByIds != null and createdByIds.size()>0">
            and ssr.created_by_id in
            <foreach item="item" collection="createdByIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds.size()>0">
            and te.department_id in
            <foreach item="deptId" collection="deptIds" separator="," open="(" close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="employeesIds!=null and employeesIds.size()>0">
            and te.id in
            <foreach item="employeesId" collection="employeesIds" separator="," open="(" close=")">
                #{employeesId}
            </foreach>
        </if>
        <if test="caseId != null">
            and ssr.case_id = #{caseId}
        </if>
        <if test="sendStatus != null and sendStatus != ''">
            and ssr.send_status = #{sendStatus}
        </if>
        <if test="relationship != null and relationship != ''">
            and ssr.relationship = #{relationship}
        </if>
        <if test="smsRecordType != null and smsRecordType != ''">
            and ssr.sms_record_type = #{smsRecordType}
        </if>
        <if test="name != null and name != ''">
            and ssr.name LIKE concat ('%',#{name},'%')
        </if>
        <if test="phoneNumber != null and phoneNumber != ''">
            and ssr.phone_number LIKE concat ('%',#{phoneNumber},'%')
        </if>
        <if test="sendTime1 != null ">
            and DATE_FORMAT(ssr.send_time , '%Y-%m-%d' ) >= DATE_FORMAT( #{sendTime1}, '%Y-%m-%d' )
        </if>
        <if test="sendTime2 != null ">
            and DATE_FORMAT(ssr.send_time , '%Y-%m-%d' ) &lt;= DATE_FORMAT( #{sendTime2}, '%Y-%m-%d' )
        </if>
        <if test="recvtime1 != null ">
            and DATE_FORMAT(smsrr.recvtime , '%Y-%m-%d' ) >= DATE_FORMAT( #{recvtime1}, '%Y-%m-%d' )
        </if>
        <if test="recvtime2 != null ">
            and DATE_FORMAT(smsrr.recvtime , '%Y-%m-%d' ) &lt;= DATE_FORMAT( #{recvtime2}, '%Y-%m-%d' )
        </if>
        <if test="createdBy != null and createdBy != ''">
            and ssr.created_by like concat('%',#{createdBy},'%')
        </if>
        <if test="sendNum != null and sendNum ==1">
            and ssr.id IN (
            SELECT sms_id FROM sms_reply_record GROUP BY sms_id
            )
        </if>
        <if test="sendNum != null and sendNum ==0">
            and ssr.id NOT IN (
            SELECT sms_id FROM sms_reply_record GROUP BY sms_id
            )
        </if>

        GROUP BY ssr.send_status
    </select>
    <select id="selectCountNowDay" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sms_send_records
        WHERE
            DATE (send_time) = DATE (NOW())
          and phone_number=#{clientPhone}
          and template_id = #{templateId}
    </select>
</mapper>
