<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.CaseMapper">

    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.CaseManage">
        <id column="settlement_status" property="settlementStatus"/>
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="case_id" jdbcType="BIGINT" property="caseId"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="allocated_state" jdbcType="VARCHAR" property="allocatedState"/>
        <result column="case_state" jdbcType="VARCHAR" property="caseState"/>
        <result column="entrusting_party_id" jdbcType="BIGINT" property="entrustingPartyId"/>
        <result column="entrusting_party_name" jdbcType="VARCHAR" property="entrustingPartyName"/>
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="batch_num" jdbcType="VARCHAR" property="batchNum"/>
        <result column="entrusting_case_batch_num" jdbcType="VARCHAR" property="entrustingCaseBatchNum"/>
        <result column="outsourcing_team_id" jdbcType="BIGINT" property="outsourcingTeamId"/>
        <result column="outsourcing_team_name" jdbcType="VARCHAR" property="outsourcingTeamName"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="client_sex" jdbcType="VARCHAR" property="clientSex"/>
        <result column="client_idcard" jdbcType="VARCHAR" property="clientIdcard"/>
        <result column="client_census_register" jdbcType="VARCHAR" property="clientCensusRegister"/>
        <result column="client_phone" jdbcType="VARCHAR" property="clientPhone"/>
        <result column="client_money" jdbcType="DECIMAL" property="clientMoney"/>
        <result column="client_residual_principal" jdbcType="DECIMAL" property="clientResidualPrincipal"/>
        <result column="client_overdue_start" jdbcType="TIMESTAMP" property="clientOverdueStart"/>
        <result column="account_period" jdbcType="INTEGER" property="accountPeriod"/>
        <result column="follow_up_state" jdbcType="VARCHAR" property="followUpState"/>
        <result column="follow_up_start" jdbcType="TIMESTAMP" property="followUpStart"/>
        <result column="follow_up_ast" jdbcType="TIMESTAMP" property="followUpAst"/>
        <result column="entrusting_case_date" jdbcType="TIMESTAMP" property="entrustingCaseDate"/>
        <result column="return_case_date" jdbcType="TIMESTAMP" property="returnCaseDate"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="label_asset" jdbcType="VARCHAR" property="labelAsset"/>
        <result column="odv_id" jdbcType="BIGINT" property="odvId"/>
        <result column="odv_name" jdbcType="VARCHAR" property="odvName"/>
        <result column="urge_state" jdbcType="VARCHAR" property="urgeState"/>
        <result column="allocated_time" jdbcType="TIMESTAMP" property="allocatedTime"/>
        <result column="urge_power" jdbcType="VARCHAR" property="urgePower"/>
        <result column="del_flag" jdbcType="BIT" property="delFlag"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>

        <result column="labelId" property="labelId"/>
        <result column="code" property="code"/>
        <result column="label_content" property="labelContent"/>
        <result column="client_id_type" property="clientIdType"/>

        <result column="yc_overdue_days" property="ycOverdueDays"/>
        <result column="uid" property="uid"/>

        <result column="login_account" property="loginAccount"/>
        <result column="base_overdue_days" property="baseOverdueDays"/>
        <result column="service_fee" property="serviceFee"/>
        <result column="residual_principal" property="residualPrincipal"/>
        <result column="interest_money" property="interestMoney"/>
        <result column="entrust_money" property="entrustMoney"/>
        <result column="remaining_due" property="remainingDue"/>
        <result column="sy_yh_fees" property="syYhFees"/>
        <result column="sy_yh_principal" property="syYhPrincipal"/>
        <result column="sy_yh_interest" property="syYhInterest"/>
        <result column="client_birthday" property="clientBirthday"/>
        <result column="place_of_work" property="placeOfWork"/>
        <result column="home_address" property="homeAddress"/>
        <result column="registered_address" property="registeredAddress"/>
        <result column="amount_final_date" property="amountFinalDate"/>
        <result column="package_name" property="packageName"/>
        <result column="dispose_stage" property="disposeStage"/>
        <result column="mediated_stage" property="mediatedStage"/>
        <result column="is_freeze" property="isFreeze"/>
        <result column="start_freeze" property="startFreeze"/>
        <result column="end_freeze" property="endFreeze"/>

        <result column="prosecutor" property="prosecutor"/>
        <result column="case_mediate_state" property="caseMediateState"/>
        <result column="mediator_name" property="mediatorName"/>
        <result column="mediator_id" property="mediatorId"/>
    </resultMap>

    <resultMap id="OwnerResultMap" type="com.zws.appeal.domain.Owner">
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="ApplyRecordResultMap" type="com.zws.appeal.domain.ApplyRecord">
        <result column="del_flag" property="delFlag"/>
        <result column="case_id" property="caseId"/>
        <result column="apply_state" property="applyState"/>
        <result column="apply_date" property="applyDate"/>
        <result column="applicant_id" property="applicantId"/>
        <result column="team_id" property="teamId"/>
        <result column="team_name" property="teamName"/>
        <result column="examine_state" property="examineState"/>
        <result column="examine_time" property="examineTime"/>
        <result column="examine_by" property="examineBy"/>
        <result column="examine_by_id" property="examineById"/>
        <result column="proce_sort" property="proceSort"/>
        <result column="stay_case_time" property="stayCaseTime"/>
        <result column="entrusting_case_batch_num" property="entrustingCaseBatchNum"/>
        <result column="entrusting_case_date" property="entrustingCaseDate"/>
        <result column="return_case_date" property="returnCaseDate"/>
        <result column="odv_id" property="odvId"/>
        <result column="odv_name" property="odvName"/>
        <result column="operation_type" property="operationType"/>
    </resultMap>

    <resultMap id="CaseInfoLoan" type="com.zws.appeal.domain.InfoLoan">
        <result column="yc_overdue_days" property="ycOverdueDays"/>
        <result column="case_id" property="caseId"/>
        <result column="yc_contract_no" property="ycContractNo"/>
        <result column="clear_date" property="clearDate"/>
        <result column="amount_final_date" property="amountFinalDate"/>
    </resultMap>

    <resultMap id="LetterTemplate" type="com.zws.appeal.domain.letter.LetterTemplate">
        <result column="id" property="id"/>
        <result column="template_code" property="templateCode"/>
        <result column="classify_label" property="classifyLabel"/>
        <result column="body_content" property="bodyContent"/>
        <result column="cut_header" property="cutHeader"/>
        <result column="cut_footer" property="cutFooter"/>
    </resultMap>

    <sql id="selectCaseManageVo">
        select id,
               case_id,
               contract_no,
               allocated_state,
               case_state,
               entrusting_party_id,
               entrusting_party_name,
               product_id,
               product_name,
               batch_num,
               entrusting_case_batch_num,
               outsourcing_team_id,
               outsourcing_team_name,
               client_name,
               client_sex,
               client_idcard,
               client_census_register,
               client_phone,
               client_money,
               client_residual_principal,
               client_overdue_start,
               account_period,
               follow_up_state,
               follow_up_start,
               follow_up_ast,
               entrusting_case_date,
               return_case_date,
               area,
               label,
               odv_id,
               odv_name,
               urge_state,
               allocated_time,
               urge_power,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time
        from case_manage
    </sql>

    <sql id="selectOwnerVo">
        select id,
               name,
               create_by,
               create_time,
               update_by,
               update_time
        from asset_owner
    </sql>

    <sql id="selectApplyRecordVo">
        select id,
               case_id,
               apply_state,
               apply_date,
               applicant,
               applicant_id,
               reason,
               team_id,
               team_name,
               examine_state,
               examine_time,
               examine_by,
               examine_by_id,
               proce,
               proce_sort,
               stay_case_time,
               entrusting_case_batch_num,
               entrusting_case_date,
               return_case_date,
               odv_id,
               odv_name,
               operation_type
        from case_apply_record
    </sql>

    <sql id="OrderBy">
        <choose>
            <when test="sortOrder != null ">
                <if test="sortOrder==1 and orderBy==1">
                    order by cas.follow_up_ast desc
                </if>
                <if test="sortOrder==2 and orderBy==1">
                    order by cas.follow_up_ast asc
                </if>
                <if test="sortOrder==1 and orderBy==2">
                    order by cil.entrust_money asc
                </if>
                <if test="sortOrder==2 and orderBy==2">
                    order by cil.entrust_money desc
                </if>
                <if test="sortOrder==1 and orderBy==3">
                    order by cas.client_overdue_start asc
                </if>
                <if test="sortOrder==2 and orderBy==3">
                    order by cas.client_overdue_start desc
                </if>
                <if test="sortOrder==1 and orderBy==4">
                    order by cas.client_overdue_start desc
                </if>
                <if test="sortOrder==2 and orderBy==4">
                    order by cas.client_overdue_start asc
                </if>
                <if test="sortOrder==1 and orderBy==5">
                    order by CONVERT(cas.follow_up_state USING gbk) COLLATE gbk_chinese_ci asc
                </if>
                <if test="sortOrder==2 and orderBy==5">
                    order by CONVERT(cas.follow_up_state USING gbk) COLLATE gbk_chinese_ci desc
                </if>
                <if test="sortOrder==1 and orderBy==6">
                    order by cas.entrusting_case_date asc
                </if>
                <if test="sortOrder==2 and orderBy==6">
                    order by cas.entrusting_case_date desc
                </if>
                <if test="sortOrder==1 and orderBy==7">
                    order by  cas.return_case_date asc
                </if>
                <if test="sortOrder==2 and orderBy==7">
                    order by  cas.return_case_date desc
                </if>
                <if test="sortOrder==1 and orderBy==8">
                    order by cas.allocated_time asc
                </if>
                <if test="sortOrder==2 and orderBy==8">
                    order by cas.allocated_time desc
                </if>
                <if test="sortOrder==1 and orderBy==9">
                    order by cas.follow_up_ast asc
                </if>
                <if test="sortOrder==2 and orderBy==9">
                    order by cas.follow_up_ast desc
                </if>
            </when>
            <when test="orderBy!=null and orderBy=='followUpAst'">
                order by cas.follow_up_ast
            </when>
            <otherwise>
                order by cas.case_id desc
            </otherwise>
        </choose>
    </sql>

    <sql id="selectCaseVo">
        <if test="settlementStatus != null">and cas.settlement_status = #{settlementStatus}</if>
        <if test="caseState != null">and case_state = #{caseState}</if>
        <if test="clientOverdueStart1 != null">and cas.client_overdue_start &gt;= #{clientOverdueStart1}</if>
        <if test="clientOverdueStart2 != null">and cas.client_overdue_start &lt;= #{clientOverdueStart2}</if>
        <if test="clientMoney1 != null">and cas.client_money &gt;= #{clientMoney1}</if>
        <if test="clientMoney2 != null">and cas.client_money &lt;= #{clientMoney2}</if>
        <if test="syYhPrincipal1 != null">and cil.sy_yh_principal &gt;= #{syYhPrincipal1}</if>
        <if test="syYhPrincipal2 != null">and cil.sy_yh_principal &lt;= #{syYhPrincipal2}</if>
        <if test="entrustingCaseDate1 != null">and cas.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and cas.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and cas.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and cas.return_case_date &lt;= #{returnCaseDate2}</if>
        <if test="allocatedTime1 != null">and cas.allocated_time &gt;= #{allocatedTime1}</if>
        <if test="allocatedTime2 != null">and cas.allocated_time &lt;= #{allocatedTime2}</if>
        <if test="uid!=null and uid !=''">and cas.uid=#{uid}</if>
        <if test="notFollowed1 != null">and datediff(now(),cas.follow_up_ast) &gt;= #{notFollowed1}</if>
        <if test="notFollowed2 != null">and datediff(now(),cas.follow_up_ast) &lt;= #{notFollowed2}</if>
        <if test="mediateStage != null and mediateStage != ''">and cas.dispose_stage=#{mediateStage}</if>

        <if test="ids != null and ids.size() > 0">
            and cas.case_id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>

        <if test="caseIds != null and caseIds.size() > 0">
            and cas.case_id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="clientNames != null and clientNames.size() > 0">
            and (
            <foreach item="item" collection="clientNames" separator="or" open="(" close=")" index="">
                AES_DECRYPT(UNHEX(cas.client_name), #{decryptKey}) LIKE concat ('%',#{item},'%')
            </foreach>
            )
        </if>
        <if test="clientNamesEns != null and clientNamesEns.size() > 0">
            and (
            <foreach collection="clientNamesEns" item="item" separator="or">
                cib.client_name_enc like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="clientPhones != null and clientPhones.size() > 0">
            and (
            <foreach item="item" collection="clientPhones" separator="or" open="(" close=")" index="">
                AES_DECRYPT(UNHEX(cas.client_phone), #{decryptKey}) LIKE concat ('%',#{item},'%')
            </foreach>
            )
        </if>
        <if test="clientPhonesEns != null and clientPhonesEns.size() > 0">
            and (
            <foreach collection="clientPhonesEns" item="item" separator="or">
                cib.client_phone_enc like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="clientIdcards != null and clientIdcards.size() > 0">
            and (
            <foreach item="item" collection="clientIdcards" separator="or" open="(" close=")" index="">
                AES_DECRYPT(UNHEX(cas.client_idcard), #{decryptKey}) LIKE concat ('%',#{item},'%')
            </foreach>
            )
        </if>
        <if test="clientIdcardsEns != null and clientIdcardsEns.size() > 0">
            and (
            <foreach collection="clientIdcardsEns" item="item" separator="or">
                cib.client_id_num_enc like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="entrustingCaseBatchNums != null and entrustingCaseBatchNums.size() > 0">
            and cas.entrusting_case_batch_num in
            <foreach item="item" collection="entrustingCaseBatchNums" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="entrustingPartyIds != null and entrustingPartyIds.size() > 0">
            and cas.entrusting_party_id in
            <foreach item="item" collection="entrustingPartyIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="followUpStates != null and followUpStates.size() > 0">
            and cas.follow_up_state in
            <foreach item="item" collection="followUpStates" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="urgeStates != null and urgeStates.size() > 0">
            and cas.urge_state in
            <foreach item="item" collection="urgeStates" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="clientCensusRegister!=null">
            and AES_DECRYPT(UNHEX(cas.client_census_register), #{decryptKey}) LIKE concat
            ('%',#{clientCensusRegister},'%')
        </if>
        <if test="clientCensusRegisters != null and clientCensusRegisters.size() > 0">
            and (
            <foreach item="item" collection="clientCensusRegisters" separator="or" open="(" close=")" index="">
                AES_DECRYPT(UNHEX(cas.client_census_register), #{decryptKey}) LIKE concat ('%',#{item},'%')
            </foreach>
            )
        </if>
        <if test="clientCensusRegistersEns != null and clientCensusRegistersEns.size() > 0">
            and (
            <foreach collection="clientCensusRegistersEns" item="item" separator="or">
                cib.client_census_register_enc like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="odvIds != null and odvIds.size() > 0">
            and cas.odv_id in
            <foreach item="item" collection="odvIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="userId != null">
            and cas.odv_id = #{userId}
        </if>
        <if test="deptId != null">
            and emp.department_id =#{deptId}
        </if>
        <if test="areas != null and areas.size() > 0">
            and (
            <foreach item="item" collection="areas" separator="or" open="(" close=")" index="">
                cas.area like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <choose>
            <when test="label == 'nolabel' and labels == null and assetLabels == null">
                AND label IS NULL AND label_asset IS NULL
            </when>
        </choose>
        <if test="labels != null or assetLabels != null">
            and (
            <if test="labels != null and labels.size() > 0">
                cas.label in
                <foreach item="item" collection="labels" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="assetLabels != null and assetLabels.size() > 0">
                <if test="labels != null and labels.size() > 0">
                    or
                </if>
                cas.label_asset in
                <foreach item="item" collection="assetLabels" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="label == 'nolabel'">
                or (label is null and label_asset is null)
            </if>
            )
        </if>

    </sql>

    <select id="selectCaseManage" resultMap="BaseResultMap">
        <include refid="selectCaseManageVo"/>
        where del_flag = 0 and outsourcing_team_id = #{outsourcingTeamId}
        <if test="caseId != null ">and case_id=#{caseId}</if>
        <if test="clientName != null ">and client_name=#{clientName}</if>
        <if test="clientPhone != null ">and client_phone=#{clientPhone}</if>
        <if test="clientIdcard != null ">and client_idcard=#{clientIdcard}</if>
        <if test="entrustingCaseBatchNum != null ">and entrusting_case_batch_num=#{entrustingCaseBatchNum}</if>
        <if test="entrustingPartyId != null ">and entrusting_party_id=#{entrustingPartyId}</if>
        <if test="followUpState != null ">and follow_up_state=#{followUpState}</if>
        <if test="urgeState != null ">and urge_state=#{urgeState}</if>
        <if test="clientCensusRegister != null ">and client_census_register=#{clientCensusRegister}</if>
        <if test="clientOverdueStart1 != null ">and client_overdue_start &gt;= #{clientOverdueStart1}</if>
        <if test="clientOverdueStart2 != null ">and client_overdue_start &lt;= #{clientOverdueStart2}</if>
        <if test="odvName != null ">and odv_name=#{odvName}</if>
        <if test="clientMoney1 != null ">and client_money &gt;= #{clientMoney1}</if>
        <if test="clientMoney2 != null ">and client_money &lt;= #{clientMoney2}</if>
        <if test="entrustingCaseDate1 != null ">and entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null ">and entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null ">and return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null ">and return_case_date &lt;= #{returnCaseDate2}</if>
        <if test="allocatedTime1 != null ">and allocated_time &gt;= #{allocatedTime1}</if>
        <if test="allocatedTime2 != null ">and allocated_time &lt;= #{allocatedTime2}</if>
        <if test="notFollowUpDays1 != null ">and DATEDIFF(NOW(), follow_up_ast) &gt;= #{notFollowUpDays1}</if>
        <if test="notFollowUpDays2 != null ">and DATEDIFF(NOW(), follow_up_ast) &lt;= #{notFollowUpDays2}</if>
        <if test="label != null ">and label=#{label}</if>
        <if test="area != null ">and area=#{area}</if>
    </select>

    <select id="selectCaseManages" resultMap="BaseResultMap">
        select cas.id,
        cas.case_id,
        cas.contract_no,
        cas.allocated_state,
        cas.case_state,
        cas.settlement_status,
        cas.entrusting_party_id,
        cas.entrusting_party_name,
        cas.product_id,
        cas.product_name,
        cas.batch_num,
        cas.entrusting_case_batch_num,
        cas.outsourcing_team_id,
        cas.outsourcing_team_name,
        cas.client_name,
        cas.client_sex,
        cas.client_idcard,
        cas.client_census_register,
        cas.client_phone,
        cas.client_money,
        cas.client_residual_principal,
        cas.client_overdue_start,
        cas.account_period,
        cas.follow_up_state,
        cas.follow_up_start,
        cas.follow_up_ast,
        cas.entrusting_case_date,
        cas.return_case_date,
        cas.area,
        cas.label,
        cas.label_asset,
        cas.odv_id,
        cas.odv_name,
        cas.urge_state,
        cas.allocated_time,
        cas.urge_power,
        cas.del_flag,
        cas.create_by,
        cas.create_time,
        cas.update_by,
        cas.update_time,
        cas.uid,
        cm.id AS labelId,
        cm.label_content,
        cm.code,
        cas.client_id_type,
        te.login_account,
        cil.remaining_due,
        cil.sy_yh_interest,
        cil.sy_yh_principal,
        cil.sy_yh_fees,
        cil.remaining_due,
        cil.entrust_money,
        cil.interest_money,
        cil.residual_principal,
        cil.service_fee,
        cil.base_overdue_days,
        cib.client_birthday,
        am.package_name,
        cil.amount_final_date,
        cib.registered_address,
        cib.home_address,
        cib.place_of_work,
        cil.yc_overdue_days,
        cas.dispose_stage ,
        cas.mediated_stage,
        trr.update_by as prosecutor,
        cas.case_mediate_state,
        cas.mediator_name,
        cas.mediator_id,
        cas.is_freeze,
        CASE WHEN cas.is_freeze>0 then tfr.start_freeze
        ELSE null
        END as start_freeze ,
        CASE WHEN cas.is_freeze>0 then tfr.end_freeze
        ELSE null
        END as end_freeze
        from case_manage AS cas
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN team_employees AS te on(te.id = cas.odv_id)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        LEFT JOIN asset_manage as am on (am.batch_num=cas.batch_num)
        LEFT JOIN case_info_loan as cil ON (cas.case_id = cil.case_id)
        LEFT JOIN (
         WITH RankedCases AS (
        SELECT case_id,mediate_stage,update_by ,ROW_NUMBER() OVER (PARTITION BY case_id ORDER BY create_time DESC) AS rn FROM team_register_record
        where del_flag=0 and register_type = 2 )
        SELECT case_id,update_by,mediate_stage FROM RankedCases WHERE rn = 1
        ) as trr on (trr.case_id = cas.case_id and trr.mediate_stage = cas.dispose_stage)
        LEFT JOIN(
            SELECT tf.case_id ,tf.create_time,tf.start_freeze,tf.end_freeze FROM
            team_freeze_record tf
            INNER JOIN ( SELECT case_id, Max(create_time) AS maxTime FROM team_freeze_record WHERE del_flag = 0 GROUP BY case_id ) AS aa ON
            ( aa.case_id = tf.case_id AND aa.maxTime = tf.create_time )
        ) as tfr on (tfr.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        <include refid="selectCaseVo"/>
        <choose>
            <when test="caseState != null and (caseState==0 or caseState==1)">
            <if test="caseState==0">
            and (cas.case_state = #{caseState} or cas.case_state is null
            or cas.case_mediate_state = #{caseState} or cas.case_mediate_state is null)
            </if>
            <if test="caseState==1">
            and cas.case_state = #{caseState} and cas.case_mediate_state = #{caseState}
            </if>
            </when>
            <otherwise>
            <if test="caseState != null">
            and cas.case_state = #{caseState}
            </if>
            </otherwise>
        </choose>
        <choose>
            <when test="orderBy!=null and orderBy=='clientMoney'">
                order by cas.client_money
            </when>
            <when test="orderBy!=null and orderBy=='clientOverdueStart'">
                order by cas.client_overdue_start
            </when>
            <when test="orderBy!=null and orderBy=='overdueDays'">
                order by cas.client_overdue_start
            </when>
            <when test="orderBy!=null and orderBy=='notFollowed'">
                order by cas.follow_up_ast
            </when>
            <otherwise>
                order by cas.case_id
            </otherwise>
        </choose>
        <choose>
            <when test="sortOrder!=null and sortOrder==1">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="selectCaseManageByOdvId" resultMap="BaseResultMap">
        select id,
               case_id,
               contract_no,
               allocated_state,
               case_state,
               settlement_status,
               entrusting_party_id,
               entrusting_party_name,
               product_id,
               product_name,
               batch_num,
               entrusting_case_batch_num,
               outsourcing_team_id,
               outsourcing_team_name,
               client_name,
               client_sex,
               client_idcard,
               client_census_register,
               client_phone,
               client_money,
               client_residual_principal,
               client_overdue_start,
               account_period,
               follow_up_state,
               follow_up_start,
               follow_up_ast,
               entrusting_case_date,
               return_case_date,
               area,
               odv_id,
               odv_name,
               urge_state,
               allocated_time,
               urge_power
        from case_manage
        where outsourcing_team_id = #{createId}
          and allocated_state = 1
          and odv_id = #{odvId}
    </select>

    <select id="selectCaseManagesCount" resultType="map">
        select count(1) AS size,
        sum(cil.remaining_due) AS money,
        sum(cil.sy_yh_principal) AS principal
        from case_manage AS cas
        LEFT JOIN case_info_base AS cib on (cib.case_id = cas.case_id)
        LEFT JOIN case_info_loan AS cil on (cil.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        <include refid="selectCaseVo"/>

        <choose>
            <when test="caseState != null and (caseState==0 or caseState==1)">
            <if test="caseState==0">
            and (cas.case_state = #{caseState} or cas.case_state is null
            or cas.case_mediate_state = #{caseState} or cas.case_mediate_state is null)
            </if>
            <if test="caseState==1">
            and cas.case_state = #{caseState} and cas.case_mediate_state = #{caseState}
            </if>
            </when>
            <otherwise>
            <if test="caseState != null">
            and cas.case_state = #{caseState}
            </if>
            </otherwise>
        </choose>
        <choose>
            <when test="orderBy!=null and orderBy!=''">
                order by #{orderBy}
            </when>
            <otherwise>
                order by cas.case_id
            </otherwise>
        </choose>
        <choose>
            <when test="sortOrder!=null and sortOrder==1">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="selectCaseManagesByTime" resultType="java.math.BigDecimal">
        select sum(client_money) AS money
        from case_manage
        where outsourcing_team_id = #{createId} and allocated_state = 1
        <if test="allocatedTime2 != null">and allocated_time &lt;= #{allocatedTime2}</if>
    </select>

    <select id="selectCaseManagesByTimeCount" resultType="int">
        select count(1) AS number
        from case_manage
        where outsourcing_team_id = #{createId} and allocated_state = 1
        <if test="allocatedTime2 != null">and allocated_time &lt;= #{allocatedTime2}</if>
    </select>

    <select id="selectCaseManageCaseIdList" resultType="long">
        select cas.case_id
        from case_manage AS cas
        LEFT JOIN team_employees AS emp ON (cas.odv_id = emp.id)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        <if test="caseStateList != null and caseStateList.size() > 0">
            and cas.case_state in
            <foreach item="item" collection="caseStateList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <include refid="selectCaseVo"/>
        order by cas.case_id desc
    </select>

    <select id="selectCaseManageCount" resultType="map">
        select count(1) AS quantity,
        sum(cas.client_money) AS clientMoney
        from case_manage AS cas
        LEFT JOIN team_employees AS emp ON (cas.odv_id = emp.id)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        <if test="caseStateList != null and caseStateList.size() > 0">
            and cas.case_state in
            <foreach item="item" collection="caseStateList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <include refid="selectCaseVo"/>
        order by cas.case_id desc
    </select>

    <select id="selectCaseManageId" resultMap="BaseResultMap">
        <include refid="selectCaseManageVo"/>
        <where>
            del_flag = 0 and outsourcing_team_id = #{outsourcingTeamId} and case_id in
            <foreach item="item" collection="list" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectCaseManageIdWithType" resultMap="BaseResultMap">
        <include refid="selectCaseManageVo"/>
        <where>
            del_flag = 0 and outsourcing_team_id = #{outsourcingTeamId} and case_id in
            <foreach item="item" collection="list" separator="," open="(" close=")" index="">
                #{item}
            </foreach>

        </where>
    </select>

    <select id="selectCaseManageCaseId" resultMap="BaseResultMap">
        <include refid="selectCaseManageVo"/>
        where del_flag = 0
        and case_id = #{caseId}
        and outsourcing_team_id = #{createId}
    </select>

    <select id="selectCaseId" resultMap="BaseResultMap">
        <include refid="selectCaseManageVo"/>
        where del_flag = 0 and outsourcing_team_id = #{createId} and entrusting_party_id = #{id} and client_name =
        #{clientName}
        and client_idcard = #{clientIdcard} and allocated_state = 1
    </select>

    <select id="selectOwner" resultMap="OwnerResultMap">
        <include refid="selectOwnerVo"/>
        where del_flag = 0 and name =#{name}
    </select>

    <select id="selectCaseIdName" resultMap="BaseResultMap">
        <include refid="selectCaseManageVo"/>
        where del_flag = 0 and allocated_state = 1
        and
        <foreach item="item" collection="excelBatches" separator="or" open="(" close=")" index="">
            ( entrusting_party_id = #{item.id} and client_name = #{item.clientName} and client_idcard =
            #{item.clientIdcard} and outsourcing_team_id = #{item.createId})

        </foreach>
    </select>

    <select id="selectCaseIdNameById" resultMap="BaseResultMap">
        <include refid="selectCaseManageVo"/>
        where del_flag = 0 and allocated_state = 1
        and entrusting_party_id = #{id} and client_name = #{clientName} and client_idcard =
        #{clientIdcard} and outsourcing_team_id = #{createId}
    </select>

    <select id="selectApplyRecord" resultMap="ApplyRecordResultMap">
        <include refid="selectApplyRecordVo"/>
        where del_flag=0 and case_id = #{id} and proce not in (4,5,6) and examine_state in ("待审核","审核中")
    </select>

    <select id="selectApplyRecordByUserId" resultMap="ApplyRecordResultMap">
        <include refid="selectApplyRecordVo"/>
        where del_flag=0 and case_id = #{id} and proce not in (4,5,6) and examine_state in ("待审核","审核中")
        and team_id = #{createId}
        <if test="userId!=null">
            and applicant_id = #{userId}
        </if>
        <if test="operationType!=null">
            and operation_type = #{operationType}
        </if>
    </select>

    <select id="selectApplyRecordByUserIdList" resultMap="ApplyRecordResultMap">
        <include refid="selectApplyRecordVo"/>
        where del_flag=0 and case_id = #{id} and proce not in (4,5,6) and examine_state in ("待审核","审核中")
        and team_id = #{createId}
    </select>

    <select id="selectApplyRecordCondition" resultMap="ApplyRecordResultMap">
        <include refid="selectApplyRecordVo"/>
        where apply_state=#{applyState} and team_id = #{teamId} and proce = #{proce}
        <if test="caseId != null ">and proce_sort=#{proceSort}</if>
    </select>

    <!--导出催记查询-->
    <select id="selectUrgeRecord" resultType="com.zws.appeal.pojo.ExportDataUtils">
        select urg.create_time AS createTime,
        urg.case_id AS caseId,
        urg.create_by AS createBy,
        urg.odv_id AS odvId,
        urg.liaison AS liaison,
        urg.relation AS relation,
        urg.contact_mode AS contactMode,
        urg.contact_medium AS contactMedium,
        urg.content AS content,
        urg.follow_up_state AS followUpState,
        urg.urge_state AS urgeState,
        urg.promise_repayment_money AS promiseRepaymentMoney,
        urg.promise_repayment_time AS promiseRepaymentTime,
        cm.entrusting_case_batch_num AS entrustingCaseBatchNum,
        cm.client_name AS clientName,
        cm.client_idcard AS clientIdcard,
        cm.client_money AS clientMoney,
        cm.contract_no AS contractNo,
        cm.client_id_type as clientIdType

        from case_urge_record AS urg
        LEFT JOIN case_manage AS cm ON (urg.case_id = cm.case_id)
        where (cm.outsourcing_team_id = #{createId}
        and urg.create_id = #{createId}
        <if test="settlementStatus != null">and cm.settlement_status = #{settlementStatus}</if>
        <if test="caseState != null">and cm.case_state = #{caseState}</if>
        <if test="createTime1 != null">and urg.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and urg.create_time &lt;= #{createTime2}</if>
        <if test="clientOverdueStart1 != null">and cm.client_overdue_start &gt;= #{clientOverdueStart1}</if>
        <if test="clientOverdueStart2 != null">and cm.client_overdue_start &lt;= #{clientOverdueStart2}</if>
        <if test="clientMoney1 != null">and cm.client_money &gt;= #{clientMoney1}</if>
        <if test="clientMoney2 != null">and cm.client_money &lt;= #{clientMoney2}</if>
        <if test="entrustingCaseDate1 != null">and cm.entrusting_case_date &gt;= #{entrustingCaseDate1}</if>
        <if test="entrustingCaseDate2 != null">and cm.entrusting_case_date &lt;= #{entrustingCaseDate2}</if>
        <if test="returnCaseDate1 != null">and cm.return_case_date &gt;= #{returnCaseDate1}</if>
        <if test="returnCaseDate2 != null">and cm.return_case_date &lt;= #{returnCaseDate2}</if>
        <if test="allocatedTime1 != null">and cm.allocated_time &gt;= #{allocatedTime1}</if>
        <if test="allocatedTime2 != null">and cm.allocated_time &lt;= #{allocatedTime2}</if>
        <if test="followUpAst1 != null">and cm.follow_up_ast &gt;= #{followUpAst1}</if>
        <if test="followUpAst2 != null">and cm.follow_up_ast &lt;= #{followUpAst2}</if>
        <if test="caseId != null and caseId!=''">and urg.case_id = #{caseId}</if>
        <if test="ids != null and ids.size() > 0">
            and urg.case_id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="caseIds != null and caseIds.size() > 0">
            and urg.case_id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="clientNames != null and clientNames.size() > 0">
            and (
            <foreach item="item" collection="clientNames" separator="or" open="(" close=")" index="">
                cm.client_name like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="clientPhones != null and clientPhones.size() > 0">
            and (
            <foreach item="item" collection="clientPhones" separator="," open="(" close=")" index="">
                cm.client_phone like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="clientIdcards != null and clientIdcards.size() > 0">
            and (
            <foreach item="item" collection="clientIdcards" separator="," open="(" close=")" index="">
                cm.client_idcard like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="entrustingCaseBatchNums != null and entrustingCaseBatchNums.size() > 0">
            and cm.entrusting_case_batch_num in
            <foreach item="item" collection="entrustingCaseBatchNums" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="entrustingPartyIds != null and entrustingPartyIds.size() > 0">
            and cm.entrusting_party_id in
            <foreach item="item" collection="entrustingPartyIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="followUpStates != null and followUpStates.size() > 0">
            and cm.follow_up_state in
            <foreach item="item" collection="followUpStates" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="urgeStates != null and urgeStates.size() > 0">
            and cm.urge_state in
            <foreach item="item" collection="urgeStates" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="clientCensusRegisters != null and clientCensusRegisters.size() > 0">
            and cm.client_census_register in
            <foreach item="item" collection="clientCensusRegisters" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="odvIds != null and odvIds.size() > 0">
            and urg.odv_id in
            <foreach item="item" collection="odvIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="areas != null and areas.size() > 0">
            and (
            <foreach item="item" collection="areas" separator="," open="(" close=")" index="">
                cm.area like concat('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="labels != null and labels.size() > 0">
            and cm.label in
            <foreach item="item" collection="labels" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        )
        or(
        urg.import_reminder = 0
        <if test="createTime1 != null">and urg.create_time &gt;= #{createTime1}</if>
        <if test="createTime2 != null">and urg.create_time &lt;= #{createTime2}</if>
        <if test="ids != null and ids.size() > 0">
            and urg.case_id in
            <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="caseIds != null and caseIds.size() > 0">
            and urg.case_id in
            <foreach item="item" collection="caseIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        )
    </select>

    <insert id="insertDistributionHistory" parameterType="com.zws.appeal.domain.DistributionHistory">
        insert into
        team_distribution_history(case_id,operation,employees_id,employees_name,delete_logo,founder,creationtime)
        values
        <foreach collection="distributionHistory" item="list" index="index" separator=",">
            (#{list.caseId},#{list.operation},#{list.employeesId},#{list.employeesName},#{list.deleteLogo},#{list.founder},#{list.creationtime})
        </foreach>
    </insert>

    <insert id="insertApplyRecord" parameterType="com.zws.appeal.domain.ApplyRecord">
        insert into case_apply_record(case_id, apply_state, apply_date, applicant, applicant_id, team_id, team_name,approve_id,
        examine_state, proce, proce_sort, del_flag,
        reason,entrusting_case_batch_num,entrusting_case_date,return_case_date,odv_id,odv_name,operation_type)
        values
        <foreach collection="applyRecords" item="list" index="index" separator=",">
            (#{list.caseId},#{list.applyState},#{list.applyDate},#{list.applicant},#{list.applicantId},#{list.teamId},#{list.teamName},#{list.approveId},
            #{list.examineState},#{list.proce},#{list.proceSort},#{list.delFlag},#{list.reason},#{list.entrustingCaseBatchNum},
            #{list.entrustingCaseDate},#{list.returnCaseDate},#{list.odvId},#{list.odvName},#{list.operationType})
        </foreach>
    </insert>

    <update id="updateCaseManage" parameterType="com.zws.appeal.domain.CaseManage">
        update case_manage
        <trim prefix="SET" suffixOverrides=",">
            <if test="distribution.odvId != null">odv_id = #{distribution.odvId},</if>
            <if test="distribution.odvName != null">odv_name = #{distribution.odvName},</if>
            <if test="distribution.jobNumber != null">employees_working = #{distribution.jobNumber},</if>
            <if test="distribution.updateBy != null">update_by = #{distribution.updateBy},</if>
            <if test="distribution.updateTime != null">update_time = #{distribution.updateTime},</if>
            <if test="distribution.caseState != null">case_state = #{distribution.caseState},</if>
            <if test="distribution.allocatedTime != null">allocated_time = #{distribution.allocatedTime},</if>
            <if test="distribution.disposeStage != null">dispose_stage = #{distribution.disposeStage},</if>
            <if test="distribution.mediatedStage != null">mediated_stage = #{distribution.mediatedStage},</if>
            <if test="distribution.isFreeze != null">is_freeze = #{distribution.isFreeze},</if>
            <if test="distribution.mediatorId != null">mediator_id = #{distribution.mediatorId},</if>
            <if test="distribution.mediatorName != null">mediator_name = #{distribution.mediatorName},</if>
            <if test="distribution.caseMediateState != null">case_mediate_state = #{distribution.caseMediateState},</if>
        </trim>
        <where>
            case_id in
            <foreach collection="caseIds" item="list" index="index" open="(" close=")" separator=",">
                #{list}
            </foreach>
        </where>
    </update>

    <update id="updateCaseTemplate" parameterType="com.zws.appeal.domain.CaseManage">
        <foreach collection="caseManage" item="list" index="index" open="" close="" separator=";">
            update case_manage
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.odvId != null">odv_id = #{list.odvId},</if>
                <if test="list.odvName != null">odv_name = #{list.odvName},</if>
                <if test="list.updateBy != null">update_by = #{list.updateBy},</if>
                <if test="list.updateTime != null">update_time = #{list.updateTime},</if>
                <if test="list.caseState != null">case_state = #{list.caseState},</if>
                <if test="list.allocatedTime != null">allocated_time = #{list.allocatedTime},</if>
                <if test="list.disposeStage !=null ">dispose_stage = #{list.disposeStage},</if>
                <if test="list.mediatedStage !=null ">mediated_stage = #{list.mediatedStage},</if>
                <if test="list.isFreeze !=null ">is_freeze = #{list.isFreeze},</if>
            </trim>
            where case_id = #{list.caseId}
        </foreach>
    </update>

    <update id="updateRecoveryCases">
        update case_manage
        set odv_id = null,
        odv_name = null,
        allocated_time = null,
        dispose_stage = null,
        mediated_stage = null,
        is_freeze = null,
        mediator_name=null ,
        mediator_id=null ,
        update_by = #{updateBy},
        update_time = #{updateTime},
        case_mediate_state = #{caseMediateState},
        case_state = #{caseState}
        where case_id in
        <foreach collection="caseIds" item="caseId" open="(" close=")" separator=",">
            #{caseId}
        </foreach>
    </update>

    <update id="updateCaseRecovery" parameterType="com.zws.appeal.domain.CaseManage">
        <foreach collection="caseManage" item="list" index="index" open="" close="" separator=";">
            update case_manage
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.label != null">label_asset = #{list.label},label = null,</if>
            </trim>
            where case_id = #{list.caseId} and outsourcing_team_id = #{list.outsourcingTeamId}
        </foreach>
    </update>
    <update id="updateCaseRecovery2" parameterType="com.zws.appeal.domain.CaseManage">
        <foreach collection="caseManage" item="list" index="index" open="" close="" separator=";">
            update case_manage
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.label != null">label = #{list.label},label_asset = null,</if>
            </trim>
            where case_id = #{list.caseId} and outsourcing_team_id = #{list.outsourcingTeamId}
        </foreach>
    </update>
    <update id="updateCaseRecovery3" parameterType="com.zws.appeal.domain.CaseManage">
        <foreach collection="caseManage" item="list" index="index" open="" close="" separator=";">
            update case_manage
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.label != null">label = null,label_asset = null,</if>
            </trim>
            where case_id = #{list.caseId} and outsourcing_team_id = #{list.outsourcingTeamId}
        </foreach>
    </update>
    <update id="clearCaseMarkCase">
        update case_manage
        set label = null ,
        label_asset = null
        where case_id in
        <foreach collection="ids" item="caseId" open="(" separator="," close=")" index="index">
            #{caseId}
        </foreach>
    </update>
    <update id="updateCaseMarkCase1">
        update case_manage
        set label = #{label}
        where case_id in
        <foreach collection="ids" item="caseId" open="(" separator="," close=")" index="index">
            #{caseId}
        </foreach>
    </update>
    <update id="updateCaseMarkCase2">
        update case_manage
        set label_asset = #{label}
        where case_id in
        <foreach collection="ids" item="caseId" open="(" separator="," close=")" index="index">
            #{caseId}
        </foreach>
    </update>
    <update id="updateByApproveIdSelective">
        update case_apply_record
        set examine_state = #{examineState}
        where approve_id = #{approveId}
    </update>

    <select id="selectCaseInfoLoan" resultMap="CaseInfoLoan">
        select id,
               yc_overdue_days,
               yc_contract_no,
               clear_date,
               amount_final_date
        from case_info_loan


        where case_id = #{id}
          and del_flag = 0
    </select>

    <select id="selectLetterTemplateByLabel" resultMap="LetterTemplate">
        select id,
               template_code,
               classify_label,
               body_content,
               cut_header,
               cut_footer
        from letter_template
        where classify_label = #{id}
          and del_flag = 0
        order by create_time desc limit 1
    </select>
    <select id="selectAllSmsTemplate" resultType="com.zws.appeal.domain.TemplateSms">
        select tem.id AS id,
        aut.autograph_name AS autographName,
        tem.template_content AS templateContent,
        tem.created_time AS createdTime
        from sms_template AS tem
        left join sms_autograph AS aut on (tem.autograph_id = aut.id and aut.del_flag = 0)
        where tem.del_flag = 0
        <if test="id != null">and tem.id = #{id}</if>
        <if test="smsType != null">and tem.sms_type = #{smsType}</if>
        order by tem.created_time desc
    </select>

    <select id="selectFirstStageByWay" resultType="java.lang.String">
    SELECT
    stage_two_name
    from
    dispose_stage_config
    WHERE del_flag=0 and sort_num=1
    and dispose_way=#{disposeWay} limit 1
    </select>
    <select id="selectCaseIdList" resultType="java.lang.Long">
        select
        cas.case_id
        from case_manage AS cas
        LEFT JOIN team_label AS cm ON(cas.outsourcing_team_id = cm.create_id and cas.label = cm.code)
        LEFT JOIN team_employees AS te on(te.id = cas.odv_id)
        LEFT JOIN case_info_base as cib on (cib.case_id = cas.case_id)
        LEFT JOIN asset_manage as am on (am.batch_num=cas.batch_num)
        LEFT JOIN case_info_loan as cil ON (cas.case_id = cil.case_id)
        where cas.del_flag = 0 and cas.outsourcing_team_id = #{createId} and cas.allocated_state = 1
        and cas.case_state != '2'
        <include refid="selectCaseVo"/>
        <include refid="OrderBy"/>
    </select>
    <select id="selectState" resultType="com.zws.appeal.domain.CaseManage">
        select
        COALESCE(case_state, '0') as caseState,
        COALESCE(case_mediate_state, '0') as caseMediateState
        from case_manage
        where del_flag = 0
           and case_id in
        <foreach collection="caseIds" item="list" index="index" open="(" close=")" separator=",">
            #{list}
        </foreach>
    </select>
    <select id="getAssetKeyVal" resultType="java.util.Map">
        SELECT legal_key AS `key`, legal_val AS value
        FROM setup_legal
        WHERE legal_type = 3
          AND state = 0
          AND del_flag = '0'
    </select>


</mapper>
