<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.CensusAssetsMapper">
    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.TeamBatchDimension">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="entrusting_batch_num" jdbcType="VARCHAR" property="entrustingBatchNum"/>
        <result column="residual_principal" jdbcType="DECIMAL" property="residualPrincipal"/>
        <result column="entrust_money" jdbcType="DECIMAL" property="entrustMoney"/>
        <result column="repayment_money" jdbcType="DECIMAL" property="repaymentMoney"/>
        <result column="recovery_rate" jdbcType="DECIMAL" property="recoveryRate"/>
        <result column="recovery_target" jdbcType="DECIMAL" property="recoveryTarget"/>
        <result column="recovery_progress" jdbcType="DECIMAL" property="recoveryProgress"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, entrusting_batch_num, residual_principal, entrust_money, repayment_money, recovery_rate,
        recovery_target, recovery_progress, team_id, create_time, del_flag
    </sql>

    <resultMap id="OverviewResultMap" type="com.zws.appeal.domain.TeamDataOverview">
        <!--@mbg.generated-->
        <!--@Table team_data_overview-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="employee_id" jdbcType="BIGINT" property="employeeId"/>
        <result column="initial_debt_total" jdbcType="DECIMAL" property="initialDebtTotal"/>
        <result column="residual_debt_total" jdbcType="DECIMAL" property="residualDebtTotal"/>
        <result column="recovery_total" jdbcType="DECIMAL" property="recoveryTotal"/>
        <result column="recovery_belonged" jdbcType="DECIMAL" property="recoveryBelonged"/>
        <result column="recovery_not_belonged" jdbcType="DECIMAL" property="recoveryNotBelonged"/>
        <result column="pen_number_total" jdbcType="INTEGER" property="penNumberTotal"/>
        <result column="households_total" jdbcType="INTEGER" property="householdsTotal"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="account_type" jdbcType="INTEGER" property="accountType"/>
    </resultMap>

    <sql id="Overview_Column_List">
        id
        , team_id, employee_id, initial_debt_total, residual_debt_total, recovery_total,
    recovery_belonged, recovery_not_belonged, pen_number_total, households_total, created_time,
    del_flag, account_type
    </sql>

    <resultMap id="EmployeeResultMap" type="com.zws.appeal.domain.TeamEmployeeDimension">
        <!--@mbg.generated-->
        <!--@Table team_employee_dimension-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="employee_name" jdbcType="VARCHAR" property="employeeName"/>
        <result column="collection_amount_total" jdbcType="DECIMAL" property="collectionAmountTotal"/>
        <result column="agent_duration" jdbcType="INTEGER" property="agentDuration"/>
        <result column="call_num" jdbcType="INTEGER" property="callNum"/>
        <result column="available_num" jdbcType="INTEGER" property="availableNum"/>
        <result column="entrust_money" jdbcType="DECIMAL" property="entrustMoney"/>
        <result column="commission_households" jdbcType="INTEGER" property="commissionHouseholds"/>
        <result column="recorded_amount" jdbcType="DECIMAL" property="recordedAmount"/>
        <result column="recorded_number" jdbcType="INTEGER" property="recordedNumber"/>
        <result column="recovery_rate_cumulative" jdbcType="DECIMAL" property="recoveryRateCumulative"/>
        <result column="settlement_amount" jdbcType="DECIMAL" property="settlementAmount"/>
        <result column="month_reminder_quantity" jdbcType="INTEGER" property="monthReminderQuantity"/>
        <result column="case_volume" jdbcType="INTEGER" property="caseVolume"/>
        <result column="reminder_total" jdbcType="INTEGER" property="reminderTotal"/>
        <result column="employee_id" jdbcType="BIGINT" property="employeeId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="ts_sum" jdbcType="INTEGER" property="tsSum"/>
        <result column="urge_sum" jdbcType="INTEGER" property="urgeSum"/>
    </resultMap>

    <sql id="Employee_Column_List">
        <!--@mbg.generated-->
        id, employee_name, collection_amount_total, agent_duration, call_num, available_num,
        entrust_money, commission_households, recorded_amount, recorded_number, recovery_rate_cumulative,
        settlement_amount, month_reminder_quantity, case_volume, reminder_total, employee_id,
        create_time, del_flag, team_id
    </sql>

    <insert id="insert" parameterType="com.zws.appeal.domain.TeamBatchDimension">
        <!--@mbg.generated-->
        insert into team_batch_dimension (id, entrusting_batch_num, residual_principal,
        entrust_money, repayment_money, recovery_rate,
        recovery_target, recovery_progress, team_id,
        create_time, del_flag)
        values (#{id,jdbcType=BIGINT}, #{entrustingBatchNum,jdbcType=VARCHAR}, #{residualPrincipal,jdbcType=DECIMAL},
        #{entrustMoney,jdbcType=DECIMAL}, #{repaymentMoney,jdbcType=DECIMAL}, #{recoveryRate,jdbcType=DECIMAL},
        #{recoveryTarget,jdbcType=DECIMAL}, #{recoveryProgress,jdbcType=DECIMAL}, #{teamId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR})
    </insert>
    <insert id="insertBatchSelective" parameterType="com.zws.appeal.domain.TeamBatchDimension">
        <!--@mbg.generated-->
        insert into team_batch_dimension
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="entrustingBatchNum != null">
                entrusting_batch_num,
            </if>
            <if test="residualPrincipal != null">
                residual_principal,
            </if>
            <if test="entrustMoney != null">
                entrust_money,
            </if>
            <if test="repaymentMoney != null">
                repayment_money,
            </if>
            <if test="recoveryRate != null">
                recovery_rate,
            </if>
            <if test="recoveryTarget != null">
                recovery_target,
            </if>
            <if test="recoveryProgress != null">
                recovery_progress,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="entrustingBatchNum != null">
                #{entrustingBatchNum,jdbcType=VARCHAR},
            </if>
            <if test="residualPrincipal != null">
                #{residualPrincipal,jdbcType=DECIMAL},
            </if>
            <if test="entrustMoney != null">
                #{entrustMoney,jdbcType=DECIMAL},
            </if>
            <if test="repaymentMoney != null">
                #{repaymentMoney,jdbcType=DECIMAL},
            </if>
            <if test="recoveryRate != null">
                #{recoveryRate,jdbcType=DECIMAL},
            </if>
            <if test="recoveryTarget != null">
                #{recoveryTarget,jdbcType=DECIMAL},
            </if>
            <if test="recoveryProgress != null">
                #{recoveryProgress,jdbcType=DECIMAL},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
        </trim>
    </insert>

    <select id="censusAssets" resultType="com.zws.appeal.domain.TeamBatchDimension">
        select
        car.id as id,
        car.entrusting_batch_num as entrustingBatchNum,
        SUM(cil.remaining_due) as remainingDue,
        SUM(cil.residual_principal) as residualPrincipal,
        SUM(cil.entrust_money) as entrustMoney
        from case_allocated_record as car
        left join case_manage as cah on(car.entrusting_batch_num = cah.entrusting_case_batch_num and cah.del_flag=0)
        left join case_info_loan as cil on(cil.case_id = cah.case_id and cil.del_flag = 0)
        <where>
            car.examine_state = '已通过'
            <if test="_parameter != null">
                and car.team_id = #{teamId,jdbcType=BIGINT}
            </if>
        </where>
        group by car.entrusting_batch_num,car.id
    </select>

    <select id="getBatchNum" resultType="java.lang.String">
        select
        distinct
        entrusting_batch_num
        from case_allocated_record
        <where>
            examine_state = '已通过'
            <if test="_parameter != null">
                and team_id = #{teamId,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <select id="getRepaymentMoney" resultType="com.zws.appeal.domain.TeamBatchDimension">
        select
        car.id as id,
        car.target_back_money as targetBackMoney,
        car.entrusting_batch_num as entrustingBatchNum,
        SUM(crr.repayment_money) as repaymentMoney
        from case_allocated_record as car
        left join case_manage as cah on(car.entrusting_batch_num = cah.entrusting_case_batch_num and cah.del_flag=0)
        left join case_repayment_record as crr on (crr.case_id = cah.case_id and crr.del_flag = 0 and crr.examine_state
        = '已通过')
        <where>
            car.examine_state = '已通过'
            <if test="_parameter != null">
                and car.team_id = #{teamId,jdbcType=BIGINT}
            </if>
        </where>
        group by car.id,car.entrusting_batch_num,car.target_back_money
    </select>


    <update id="deleteBatchDimension">
        update team_batch_dimension
        set del_flag=1
        where del_flag = 0
    </update>

    <select id="getCensusBatchList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from team_batch_dimension
        <where>
            del_flag=0
            <if test="batchNums != null and batchNums.size() != 0">
                and entrusting_batch_num in
                <foreach collection="batchNums" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="teamId != null">
                and team_id =#{teamId,jdbcType=BIGINT}
            </if>
        </where>

    </select>

    <update id="deleteEmployeeDimension">
        update team_employee_dimension
        set del_flag = 1
        where del_flag = 0
    </update>

    <insert id="insertEmployeeSelective" keyColumn="id" keyProperty="id"
            parameterType="com.zws.appeal.domain.TeamEmployeeDimension" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into team_employee_dimension
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="employeeName != null">
                employee_name,
            </if>
            <if test="collectionAmountTotal != null">
                collection_amount_total,
            </if>
            <if test="agentDuration != null">
                agent_duration,
            </if>
            <if test="callNum != null">
                call_num,
            </if>
            <if test="availableNum != null">
                available_num,
            </if>
            <if test="entrustMoney != null">
                entrust_money,
            </if>
            <if test="commissionHouseholds != null">
                commission_households,
            </if>
            <if test="recordedAmount != null">
                recorded_amount,
            </if>
            <if test="recordedNumber != null">
                recorded_number,
            </if>
            <if test="recoveryRateCumulative != null">
                recovery_rate_cumulative,
            </if>
            <if test="settlementAmount != null">
                settlement_amount,
            </if>
            <if test="monthReminderQuantity != null">
                month_reminder_quantity,
            </if>
            <if test="caseVolume != null">
                case_volume,
            </if>
            <if test="reminderTotal != null">
                reminder_total,
            </if>
            <if test="employeeId != null">
                employee_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="employeeName != null">
                #{employeeName,jdbcType=VARCHAR},
            </if>
            <if test="collectionAmountTotal != null">
                #{collectionAmountTotal,jdbcType=DECIMAL},
            </if>
            <if test="agentDuration != null">
                #{agentDuration,jdbcType=INTEGER},
            </if>
            <if test="callNum != null">
                #{callNum,jdbcType=INTEGER},
            </if>
            <if test="availableNum != null">
                #{availableNum,jdbcType=INTEGER},
            </if>
            <if test="entrustMoney != null">
                #{entrustMoney,jdbcType=DECIMAL},
            </if>
            <if test="commissionHouseholds != null">
                #{commissionHouseholds,jdbcType=INTEGER},
            </if>
            <if test="recordedAmount != null">
                #{recordedAmount,jdbcType=DECIMAL},
            </if>
            <if test="recordedNumber != null">
                #{recordedNumber,jdbcType=INTEGER},
            </if>
            <if test="recoveryRateCumulative != null">
                #{recoveryRateCumulative,jdbcType=DECIMAL},
            </if>
            <if test="settlementAmount != null">
                #{settlementAmount,jdbcType=DECIMAL},
            </if>
            <if test="monthReminderQuantity != null">
                #{monthReminderQuantity,jdbcType=INTEGER},
            </if>
            <if test="caseVolume != null">
                #{caseVolume,jdbcType=INTEGER},
            </if>
            <if test="reminderTotal != null">
                #{reminderTotal,jdbcType=INTEGER},
            </if>
            <if test="employeeId != null">
                #{employeeId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="teamId != null">
                #{teamId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateEmployeeSelective" parameterType="com.zws.appeal.domain.TeamEmployeeDimension">
        <!--@mbg.generated-->
        update team_employee_dimension
        <set>
            <if test="employeeName != null">
                employee_name = #{employeeName,jdbcType=VARCHAR},
            </if>
            <if test="collectionAmountTotal != null">
                collection_amount_total = #{collectionAmountTotal,jdbcType=DECIMAL},
            </if>
            <if test="agentDuration != null">
                agent_duration = #{agentDuration,jdbcType=INTEGER},
            </if>
            <if test="callNum != null">
                call_num = #{callNum,jdbcType=INTEGER},
            </if>
            <if test="availableNum != null">
                available_num = #{availableNum,jdbcType=INTEGER},
            </if>
            <if test="entrustMoney != null">
                entrust_money = #{entrustMoney,jdbcType=DECIMAL},
            </if>
            <if test="commissionHouseholds != null">
                commission_households = #{commissionHouseholds,jdbcType=INTEGER},
            </if>
            <if test="recordedAmount != null">
                recorded_amount = #{recordedAmount,jdbcType=DECIMAL},
            </if>
            <if test="recordedNumber != null">
                recorded_number = #{recordedNumber,jdbcType=INTEGER},
            </if>
            <if test="recoveryRateCumulative != null">
                recovery_rate_cumulative = #{recoveryRateCumulative,jdbcType=DECIMAL},
            </if>
            <if test="settlementAmount != null">
                settlement_amount = #{settlementAmount,jdbcType=DECIMAL},
            </if>
            <if test="monthReminderQuantity != null">
                month_reminder_quantity = #{monthReminderQuantity,jdbcType=INTEGER},
            </if>
            <if test="caseVolume != null">
                case_volume = #{caseVolume,jdbcType=INTEGER},
            </if>
            <if test="reminderTotal != null">
                reminder_total = #{reminderTotal,jdbcType=INTEGER},
            </if>
            <if test="employeeId != null">
                employee_id = #{employeeId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=CHAR},
            </if>
            <if test="teamId != null">
                team_id = #{teamId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getEmployeeId" resultType="java.lang.Long">
        select id
        from team_employees
        where create_id = #{teamId,jdbcType=BIGINT}
          and delete_logo = 0
    </select>


    <select id="getCallRecordInfo" resultType="com.zws.appeal.domain.TeamEmployeeDimension">
        select
        SUM(agent_duration) as agentDuration,
        count(id)AS callNum,
        (SELECT count(id) from call_record WHERE team_id =#{teamId} and odv_id = #{employeeId} and answer='y' and
        del_flag=0) as availableNum
        from call_record
        <where>
            del_flag = '0'
            <if test="employeeId != null">
                and odv_id = #{employeeId}
            </if>
            <if test="teamId != null">
                and team_id = #{teamId}
            </if>
            <if test="createTime1 != null and createTime1 != null">
                and call_time between #{createTime1} and #{createTime2}
            </if>
        </where>
    </select>

    <select id="getCollectionAmountTotal" resultType="java.math.BigDecimal">
        select
        SUM(repayment_money) as collectionAmountTotal
        from case_repayment_record
        <where>
            del_flag = '0' and examine_state = '已通过'
            <if test="teamId != null">
                and team_id = #{teamId}
            </if>
            <if test="employeeId != null">
                and odv_id = #{employeeId}
            </if>
            <if test="createTime1 != null">
                and repayment_date &gt;= #{createTime1}
            </if>
            <if test="createTime2 != null">
                and repayment_date &lt;= #{createTime2}
            </if>
        </where>
    </select>


    <select id="getEntrustMoneyTotal" resultType="com.zws.appeal.domain.TeamEmployeeDimension">
        select
        (select employee_name from team_employees where id =#{employeeId} and delete_logo = 0 and working_state=0) as
        employeeName,
        sum(loa.entrust_money) as entrustMoney,
        count(DISTINCT man.client_idcard) as commissionHouseholds
        from case_manage AS man
        left join case_info_loan as loa on (man.case_id = loa.case_id and loa.del_flag = '0')
        <where>
            man.del_flag = 0
            <if test="createTime2 != null and createTime1 != null">
                and man.entrusting_case_date between #{createTime1} and #{createTime2}
            </if>
            <if test="teamId != null">
                and man.outsourcing_team_id = #{teamId}
            </if>
            <if test="employeeId != null">
                and man.odv_id = #{employeeId}
            </if>
        </where>
    </select>

    <select id="getRecordedInfo" resultType="com.zws.appeal.domain.TeamEmployeeDimension">
        select
        <!--    目前在案户数-->
        count(DISTINCT man.client_idcard) AS recordedNumber,
        <!--	现案件量-->
        count(1) AS caseVolume,
        <!--	在案金额-->
        sum(loa.entrust_money) AS recordedAmount
        from case_manage AS man
        left join case_info_loan as loa on (man.case_id = loa.case_id and loa.del_flag = '0')
        <where>
            man.del_flag = 0
            <if test="employeeId != null">
                and man.odv_id=#{employeeId}
            </if>
            <if test="teamId != null">
                and man.outsourcing_team_id=#{teamId}
            </if>
        </where>
    </select>

    <select id="getReminderTotal" resultType="java.lang.Integer">
        select
        count( 1 ) AS reminderTotal
        from case_urge_record
        <where>
            del_flag=0
            <if test="employeeId != null">
                and odv_id = #{employeeId}
            </if>
            <if test="employeeId != null">
                and create_id = #{teamId}
            </if>
        </where>
    </select>

    <select id="getMonthReminderQuantity" resultType="java.lang.Integer">
        select
        count( 1 ) AS monthReminderQuantity
        from case_urge_record
        <where>
            del_flag=0
            and operation_type = 1
            <if test="employeeId != null">
                and odv_id = #{employeeId}
            </if>
            <if test="employeeId != null">
                and create_id = #{teamId}
            </if>
            <if test="createTime1 != null and createTime2 != null">
                and create_time between #{createTime1} and #{createTime2}
            </if>
        </where>
    </select>


    <select id="getEmployeeOption" resultType="com.zws.appeal.pojo.Option">
        select id            as code,
               employee_name as info
        from team_employees
        where create_id = #{teamId}
          and delete_logo = 0
          and working_state = 0
    </select>

    <select id="getEmployeeIds" resultType="java.lang.Long">
        select id
        from team_employees
        where create_id = #{teamId}
          and delete_logo = 0
          and working_state = 0
    </select>


    <select id="selectStatistics" resultMap="OverviewResultMap">
        select
        <include refid="Overview_Column_List"></include>
        from team_data_overview
        <where>
            <if test="teamId != null">
                and team_id = #{teamId,jdbcType=BIGINT}
            </if>
            <if test="type != null">
                and account_type=#{type,jdbcType=INTEGER}
            </if>
            <if test="employeeId != null">
                and employee_id = #{employeeId,jdbcType=BIGINT}
            </if>
            and del_flag=0
        </where>
    </select>

    <select id="getAllTeamId" resultType="java.lang.Long">
        select id
        from team_create
        where delete_logo = 0
          and cooperation = 0
    </select>


    <select id="getEntrustMoneyInfo" resultType="com.zws.appeal.domain.TeamDataOverview">
        select
        sum(cil.entrust_money) as initialDebtTotal,
        sum(cil.remaining_due) as residualDebtTotal
        from case_manage as cm
        left join case_info_loan as cil on (cm.case_id = cil.case_id and cil.del_flag = '0')
        <where>
            cm.del_flag = 0 and cm.allocated_state = 1
            <if test="teamId != null">
                and cm.outsourcing_team_id =#{teamId}
            </if>
            <if test="employeeId != null">
                and cm.odv_id = #{employeeId}
            </if>
        </where>
    </select>

    <select id="getPenTotal" resultType="com.zws.appeal.domain.TeamDataOverview">
        select
        count(case_id) as penNumberTotal,
        count(distinct client_idcard) as householdsTotal
        from case_manage
        <where>
            del_flag = 0 and allocated_state = 1
            <if test="employeeId != null">
                and odv_id = #{employeeId}
            </if>
            <if test="teamId != null">
                and outsourcing_team_id = #{teamId}
            </if>
        </where>
    </select>


    <select id="getBelongedInfo" resultType="com.zws.appeal.domain.TeamDataOverview">
        select
        sum(case when crr.examine_state ='待审核' then crr.repayment_money else 0 end )as notBelonged1,
        sum(case when crr.examine_state ='审核中' then crr.repayment_money else 0 end )as notBelonged2,
        sum(case when crr.examine_state ='已通过' then crr.repayment_money else 0 end )as recoveryBelonged
        from
        case_repayment_record as crr
        <where>
            crr.del_flag=0
            <if test="teamId != null">
                and crr.team_id = #{teamId}
            </if>
            <if test="employeeId != null">
                and crr.odv_id =#{employeeId}
            </if>
        </where>
    </select>

    <insert id="addOverview" keyColumn="id" keyProperty="id" parameterType="com.zws.appeal.domain.TeamDataOverview"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into team_data_overview (team_id, employee_id, initial_debt_total,
        residual_debt_total, recovery_total, recovery_belonged,
        recovery_not_belonged, pen_number_total, households_total,
        created_time, del_flag, account_type
        )
        values (#{teamId,jdbcType=BIGINT}, #{employeeId,jdbcType=BIGINT}, #{initialDebtTotal,jdbcType=DECIMAL},
        #{residualDebtTotal,jdbcType=DECIMAL}, #{recoveryTotal,jdbcType=DECIMAL}, #{recoveryBelonged,jdbcType=DECIMAL},
        #{recoveryNotBelonged,jdbcType=DECIMAL}, #{penNumberTotal,jdbcType=INTEGER},
        #{householdsTotal,jdbcType=INTEGER},
        #{createdTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=CHAR}, #{accountType,jdbcType=INTEGER}
        )
    </insert>

    <insert id="addOverviewSelective" keyColumn="id" keyProperty="id"
            parameterType="com.zws.appeal.domain.TeamDataOverview" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into team_data_overview
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamId != null">
                team_id,
            </if>
            <if test="employeeId != null">
                employee_id,
            </if>
            <if test="initialDebtTotal != null">
                initial_debt_total,
            </if>
            <if test="residualDebtTotal != null">
                residual_debt_total,
            </if>
            <if test="recoveryTotal != null">
                recovery_total,
            </if>
            <if test="recoveryBelonged != null">
                recovery_belonged,
            </if>
            <if test="recoveryNotBelonged != null">
                recovery_not_belonged,
            </if>
            <if test="penNumberTotal != null">
                pen_number_total,
            </if>
            <if test="householdsTotal != null">
                households_total,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="accountType != null">
                account_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teamId != null">
                #{teamId,jdbcType=BIGINT},
            </if>
            <if test="employeeId != null">
                #{employeeId,jdbcType=BIGINT},
            </if>
            <if test="initialDebtTotal != null">
                #{initialDebtTotal,jdbcType=DECIMAL},
            </if>
            <if test="residualDebtTotal != null">
                #{residualDebtTotal,jdbcType=DECIMAL},
            </if>
            <if test="recoveryTotal != null">
                #{recoveryTotal,jdbcType=DECIMAL},
            </if>
            <if test="recoveryBelonged != null">
                #{recoveryBelonged,jdbcType=DECIMAL},
            </if>
            <if test="recoveryNotBelonged != null">
                #{recoveryNotBelonged,jdbcType=DECIMAL},
            </if>
            <if test="penNumberTotal != null">
                #{penNumberTotal,jdbcType=INTEGER},
            </if>
            <if test="householdsTotal != null">
                #{householdsTotal,jdbcType=INTEGER},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=CHAR},
            </if>
            <if test="accountType != null">
                #{accountType,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="deleteOverview">
        update team_data_overview
        set del_flag=1
        where del_flag = 0
          and team_id = #{teamId,jdbcType=BIGINT}
    </update>

    <select id="checkEmployeeExit" resultType="java.lang.Long">
        select id AS id
        from team_employee_dimension
        where del_flag = 0
          and team_id = #{teamId}
          and employee_id = #{employeeId}
          and create_time &gt;= #{createTime1}
          and create_time &lt;= #{createTime2}
    </select>

    <select id="getCensusEmployeeList" resultMap="EmployeeResultMap">
        select
        ted.id,
        ted.employee_name,
        ted.collection_amount_total,
        ted.agent_duration,
        ted.call_num,
        ted.available_num,
        ted.entrust_money,
        ted.commission_households,
        ted.recorded_amount,
        ted.recorded_number,
        ted.recovery_rate_cumulative,
        ted.settlement_amount,
        ted.month_reminder_quantity,
        ted.case_volume,
        ted.reminder_total,
        ted.employee_id,
        ted.create_time,
        ted.del_flag,
        ted.team_id,
        (SELECT
        COUNT(update_by ) AS ts_sum
        FROM
        team_register_record
        WHERE
        register_type = 2
        AND update_by = ted.employee_name
        AND del_flag = 0 )
        AS ts_sum,
        (SELECT
        COUNT(create_by ) AS urge_sum
        FROM
        case_urge_record
        WHERE
        del_flag = 0
        AND create_by = ted.employee_name
        AND urge_tpye = 0 )
        AS urge_sum
        FROM
        team_employee_dimension AS ted
        <where>
            del_flag=0
            <if test="employeeId != null">
                and employee_id = #{employeeId,jdbcType=BIGINT}
            </if>
            <if test="teamId != null">
                and team_id=#{teamId,jdbcType=BIGINT}
            </if>
            <if test="createTime1 != null and createTime2 != null">
                and create_time between #{createTime1,jdbcType=TIMESTAMP} and #{createTime2,jdbcType=TIMESTAMP}
            </if>
            <if test="employeeIds != null and employeeIds.size() != 0">
                and employee_id in
                <foreach collection="employeeIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="employeeName != null and employeeName != ''">
                and employee_name LIKE concat ('%',#{employeeName},'%')
            </if>
        </where>
    </select>

    <select id="selectAssetsPackName" resultType="java.lang.String">
        select entrusting_party_name AS assetsPackName
        from case_manage
        where del_flag = 0
          and outsourcing_team_id = #{teamId}
          and odv_id = #{employeeId}
          and entrusting_case_date &gt;= #{startData}
          and entrusting_case_date &lt;= #{endData}
        group by entrusting_party_name
    </select>

</mapper>
