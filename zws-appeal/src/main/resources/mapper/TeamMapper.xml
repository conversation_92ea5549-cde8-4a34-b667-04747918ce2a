<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.TeamMapper">

    <resultMap type="com.zws.appeal.domain.Label" id="LabelTreeResult">
        <result property="id" column="id"/>
        <result property="createId" column="create_id"/>
        <result property="labelContent" column="label_content"/>
        <result property="stateLabel" column="state_label"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <resultMap id="CreateTreeResult" type="com.zws.appeal.domain.Create">
        <result property="founderId" column="founder_id"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="collectionTargets" column="collection_targets"/>
        <result property="loginId" column="login_id"/>
        <result property="loginDate" column="login_date"/>
        <result property="companyNum" column="company_num"/>
        <result property="commissionRatio" column="commission_ratio"/>
        <result property="updatePasswordTime" column="update_password_time"/>
        <result property="teamType" column="team_type"/>
        <result property="teamLevelType" column="team_level_type"/>
    </resultMap>

    <resultMap id="FilesTreeResult" type="com.zws.appeal.domain.Files">
        <result property="createId" column="create_id"/>
        <result property="firstName" column="first_name"/>
        <result property="modifyName" column="modify_name"/>
        <result property="fileUrl" column="file_url"/>
        <result property="founder" column="founder"/>
        <result property="creationTime" column="creation_time"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="deletePeople" column="delete_people"/>
        <result property="deleteTime" column="delete_time"/>
    </resultMap>

    <resultMap id="DesensitizationTreeResult" type="com.zws.appeal.domain.Desensitization">
        <result property="createId" column="create_id"/>
        <result property="cardId" column="card_id"/>
        <result property="bankCard" column="bank_card"/>
        <result property="weChat" column="we_chat"/>
        <result property="unitAddress" column="unit_address"/>
        <result property="residentialAddress" column="residential_address"/>
        <result property="homeAddress" column="home_address"/>
        <result property="entityName" column="entity_name"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="deleteLogo" column="delete_logo"/>
    </resultMap>

    <resultMap id="StateTreeResult" type="com.zws.appeal.domain.State">
        <result property="createId" column="create_id"/>
        <result property="whitelistStatus" column="whitelist_status"/>
        <result property="informationStatus" column="information_status"/>
        <result property="restrictedState" column="restricted_state"/>
        <result property="settingStatus" column="setting_status"/>
        <result property="authorizationStatus" column="authorization_status"/>
        <result property="authenticationStatus" column="authentication_status"/>
        <result property="securityVerificationStatus" column="security_verification_status"/>
        <result property="exportSettingStatus" column="export_setting_status"/>
        <result property="pushAppStatus" column="push_app_status"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <resultMap id="WhiteTreeResult" type="com.zws.appeal.domain.White">
        <result property="createId" column="create_id"/>
        <result property="whitelistName" column="whitelist_name"/>
        <result property="addressIp" column="address_ip"/>
        <result property="creationTime" column="creation_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="createName" column="create_name"/>
    </resultMap>

    <resultMap id="WatermarkTreeResult" type="com.zws.appeal.domain.Watermark">
        <result property="createId" column="create_id"/>
        <result property="watermarkOne" column="watermark_one"/>
        <result property="watermarkTwo" column="watermark_two"/>
        <result property="watermarkThree" column="watermark_three"/>
        <result property="watermarkFour" column="watermark_four"/>
    </resultMap>

    <resultMap id="ApprovalSettingsTreeResult" type="com.zws.appeal.domain.ApprovalSettings">
        <result property="createId" column="create_id"/>
        <result property="approveCode" column="approve_code"/>
        <result property="approvalName" column="approval_name"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>

    <resultMap id="ApprovalStepsTreeResult" type="com.zws.appeal.domain.ApprovalSteps">
        <result property="createId" column="create_id"/>
        <result property="approveCode" column="approve_code"/>
        <result property="approvalRole" column="approval_role"/>
        <result property="approvalId" column="approval_id"/>
        <result property="approverId" column="approver_id"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="deleteLogo" column="delete_logo"/>
    </resultMap>

    <resultMap id="EmployeesTreeResult" type="com.zws.appeal.domain.Employees">
        <result property="id" column="id"/>
        <result property="createId" column="create_id"/>
        <result property="departmentId" column="department_id"/>
        <result property="roleId" column="role_id"/>
        <result property="employeeName" column="employee_name"/>
        <result property="departments" column="departments"/>
        <result property="loginAccount" column="login_account"/>
        <result property="password" column="password"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="theRole" column="the_role"/>
        <result property="accountStatus" column="account_status"/>
        <result property="workingState" column="working_state"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="founder" column="founder"/>
        <result property="creationtime" column="creationtime"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="employeesWorking" column="employees_working"/>
    </resultMap>

    <resultMap id="TeamExportResult" type="com.zws.appeal.domain.TeamExport">
        <result property="createId" column="create_id"/>
        <result property="exportStatus" column="export_status"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="creationtime" column="creation_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="modifier" column="modifier"/>
        <result property="founder" column="founder"/>

        <result property="perms" column="perms"/>
        <result property="menuName" column="menu_name"/>
        <result property="buttonName" column="button_name"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectCreateVo">
        select id,
               cname,
               category,
               account,
               password,
               numbers,
               cooperation,
               margin,
               starts,
               complete,
               contact,
               machine,
               email,
               describes,
               founder,
               creationtime,
               modifier,
               modify_time,
               collection_targets,
               delete_logo,
               login_id,
               company_num,
               commission_ratio,
               team_type,
               team_level_type
        from team_create
    </sql>

    <sql id="selectDesensitizationVo">
        select id,
               create_id,
               dname,
               numbers,
               card_id,
               bank_card,
               qq,
               we_chat,
               households,
               unit_address,
               residential_address,
               home_address,
               entity_name,
               founder,
               creationtime,
               modifier,
               modify_time,
               delete_logo
        from team_desensitization
    </sql>

    <sql id="selectApprovalSettingsVo">
        select id,
               create_id,
               approve_code,
               approval_name,
               remarks,
               founder,
               creationtime,
               modifier,
               modify_time
        from team_approval_settings
    </sql>

    <sql id="selectApprovalStepsVo">
        select id,
               create_id,
               approve_code,
               sort,
               approval_role,
               approval_id,
               approver,
               approver_id,
               founder,
               creationtime,
               modifier,
               modify_time,
               delete_logo
        from team_approval_steps
    </sql>

    <sql id="selectEmployeesVo">
        select id,
               create_id,
               department_id,
               role_id,
               employee_name,
               departments,
               login_account,
               password,
               phone_number,
               the_role,
               account_status,
               working_state,
               founder,
               creationtime,
               modifier,
               modify_time,
               employees_working
        from team_employees
    </sql>

    <select id="selectCooperation" resultType="com.zws.appeal.domain.Category">
        select cre.cname AS cname,
        sys.nick_name AS operationBy,
        emp.status_before AS statusBefore,
        emp.status_after AS statusAfter,
        emp.operation_time AS operationTime
        from team_category AS emp
        LEFT JOIN team_create AS cre ON (emp.team_id = cre.id)
        LEFT JOIN sys_user AS sys ON (emp.operation_by_id = sys.user_id)
        where emp.delete_logo = 0
        <if test="teamId != null and teamId != ''">and emp.team_id = #{teamId}</if>
        <if test="status != null">and emp.status_after = #{status}</if>
        <if test="operationBy != null and operationBy != ''">and sys.nick_name like concat('%', #{operationBy}, '%')
        </if>
        order by emp.operation_time desc
    </select>

    <select id="selectDictData" resultType="string">
        select dict_label AS dictLabel
        from sys_dict_data
        where status = 0
          and dict_type = #{dictType}
    </select>

    <select id="selectDeptFuzzy" resultMap="EmployeesTreeResult">
        <include refid="selectEmployeesVo"></include>
        <where>
            delete_logo=0 and create_id = #{createId}
            <if test="value != null and value != ''">
                and (employee_name like concat('%', #{value}, '%') or departments like concat('%', #{value}, '%') or
                phone_number like concat('%', #{value}, '%') or the_role like concat('%', #{value}, '%'))
            </if>
            <if test="departmentId != null ">and department_id=#{departmentId}</if>
        </where>
    </select>

    <select id="selectAuthentication" resultType="com.zws.appeal.pojo.Authentication">
        select cre.cname AS createName,
        cre.id AS createId,
        emp.id AS userId,
        emp.login_account AS loginAccount,
        emp.employee_name AS employeeName,
        emp.employees_working AS employeesWorking,
        emp.phone_number AS phoneNumber,
        cer.id AS id,
        cer.identity_card AS identityCard,
        cer.mailbox AS mailbox,
        cer.state AS state
        from team_employees AS emp
        LEFT JOIN team_create AS cre ON (emp.create_id = cre.id)
        LEFT JOIN team_certification AS cer ON (cer.user_id = emp.id)
        where emp.create_id = #{createId}
        and emp.delete_logo = 0
        <if test="value != null and value != ''">
            and (cre.cname like concat('%', #{value}, '%') or emp.login_account like concat('%', #{value}, '%') or
            emp.employee_name like concat('%', #{value}, '%') or emp.employees_working like concat('%', #{value}, '%')
            or emp.phone_number like concat('%', #{value}, '%'))
        </if>
    </select>

    <select id="selectCreate" resultType="com.zws.appeal.domain.Create" resultMap="CreateTreeResult">
        <include refid="selectCreateVo"/>
        where delete_logo = 0
    </select>

    <select id="selectCreateByType" resultType="com.zws.appeal.domain.Create" resultMap="CreateTreeResult">
        <include refid="selectCreateVo"/>
        where delete_logo = 0 and cooperation = 0
    </select>

    <select id="selectLabelId" resultType="com.zws.appeal.domain.Label" resultMap="LabelTreeResult">
        select id,
               create_id,
               label_content,
               state_label,
               modifier,
               modify_time
        from team_label
        where create_id = #{id}
    </select>

    <select id="selectCreateId" resultType="com.zws.appeal.domain.Create" resultMap="CreateTreeResult">
        select id,
               cname,
               category,
               account,
               numbers,
               cooperation,
               margin,
               starts,
               complete,
               contact,
               machine,
               email,
               describes,
               founder,
               creationtime,
               modifier,
               modify_time,
               collection_targets,
               delete_logo,
               login_id,
               login_date,
               commission_ratio,
               update_password_time,
               team_type,
               team_level_type,
               company_num
        from team_create
        where delete_logo = 0
          and id = #{id}
    </select>

    <select id="selectCreateIdPassword" resultType="com.zws.appeal.domain.Create" resultMap="CreateTreeResult">
        select password
        from team_create
        where id = #{id}
    </select>

    <select id="selectCreateAccount" resultType="com.zws.appeal.domain.Create" resultMap="CreateTreeResult">
        <include refid="selectCreateVo"/>
        where account=#{account}
    </select>

    <select id="selectAddressIp" resultType="int">
        select count(address_ip)
        from team_white
        where address_ip = #{addressIp}
          and create_id = #{createId}
    </select>

    <select id="selectAddressIps" resultType="int">
        select count(address_ip)
        from team_white
        where address_ip = #{addressIp}
          and create_id = #{createId}
          and id!=#{id}
    </select>

    <select id="selectAccurate" resultType="com.zws.appeal.domain.Create" resultMap="CreateTreeResult">
        <include refid="selectCreateVo"/>
        <where>
            delete_logo=0
            <if test="cname != null and cname != ''">and cname like concat('%', #{cname}, '%')</if>
            <if test="cooperation != null ">and cooperation=#{cooperation}</if>
            <if test="category != null ">and category=#{category}</if>
            <if test="startTime != null and startTime != ''">and creationtime &gt;= #{startTime}</if>
            <if test="completeTime != null and completeTime != ''">and creationtime &lt;= #{completeTime}</if>
            <if test="account != null and account != ''">and account = #{account}</if>
            <if test="teamType != null">and team_type = #{teamType}</if>
        </where>
        order by creationtime desc
    </select>

    <select id="selectRepaymentRecordId" resultType="com.zws.appeal.domain.record.RepaymentRecord">
        select bil.repayment_money AS repaymentMoney
        from case_repayment_record AS rep
        LEFT JOIN financial_bill AS bil ON (rep.bill_id = bil.id)
        where rep.team_id = #{teamId}
        and rep.del_flag = 0
        and rep.examine_state = #{examineState}
        <if test="registrarId != null">and rep.registrar_id = #{registrarId}</if>
        <if test="monthBegin != null">and bil.repayment_time &gt;= #{monthBegin}</if>
        <if test="monthEnd != null">and bil.repayment_time &lt;= #{monthEnd}</if>
    </select>

    <select id="selectRepaymentRecordIdMoney" resultType="decimal">
        select sum(repayment_money) AS money
        from case_repayment_record
        where team_id = #{teamId}
        and del_flag = 0
        and examine_state = #{examineState}
        <if test="registrarId != null">and registrar_id = #{registrarId}</if>
        <if test="monthBegin != null">and repayment_date &gt;= #{monthBegin}</if>
        <if test="monthEnd != null">and repayment_date &lt;= #{monthEnd}</if>
    </select>

    <select id="selectFiles" resultMap="FilesTreeResult" resultType="com.zws.appeal.domain.Files">
        select id,
               create_id,
               first_name,
               modify_name,
               file_url,
               founder,
               creation_time,
               delete_logo,
               delete_people,
               delete_time
        from team_file
        where create_id = #{createId}
          and delete_logo = 0
    </select>

    <select id="selectFilesId" resultMap="FilesTreeResult" resultType="com.zws.appeal.domain.Files">
        select id,
               create_id,
               first_name,
               modify_name,
               file_url,
               founder,
               creation_time,
               delete_logo,
               delete_people,
               delete_time
        from team_file
        where id = #{id}
          and delete_logo = 0
    </select>

    <select id="selectCount" resultType="java.util.HashMap">
        SELECT tc.cooperation co, COUNT(id) ct
        FROM team_create AS tc
        WHERE tc.delete_logo = 0
        GROUP BY tc.cooperation
    </select>

    <select id="selectState" resultType="com.zws.appeal.domain.State" resultMap="StateTreeResult">
        select id,
               create_id,
               whitelist_status,
               information_status,
               restricted_state,
               setting_status,
               authorization_status,
               authentication_status,
               security_verification_status,
               export_setting_status,
               push_app_status,
               modifier,
               modify_time
        from team_states
        where create_id = #{createId}
    </select>

    <select id="selectRoleMenu" resultType="string">
        select men.perms
        from team_menu AS men
                 LEFT JOIN team_role_menu as rol on (rol.menu_id = men.menu_id)
        where men.status = 0
          and men.perms is not null
          and rol.role_id = #{roleId}
    </select>

    <select id="selectMenu" resultType="string">
        select perms
        from team_menu
        where status = 0
          and perms is not null
    </select>

    <select id="selectDesensitization" resultType="com.zws.appeal.domain.Desensitization"
            resultMap="DesensitizationTreeResult">
        select id,
               create_id,
               dname,
               numbers,
               card_id,
               bank_card,
               qq,
               we_chat,
               households,
               unit_address,
               residential_address,
               home_address,
               entity_name,
               founder,
               creationtime,
               modifier,
               modify_time
        from team_desensitization
        where delete_logo = 0
          and create_id = #{createId}
    </select>

    <select id="selectWhite" resultType="com.zws.appeal.domain.White" resultMap="WhiteTreeResult">
        select id,
               create_id,
               whitelist_name,
               address_ip,
               note,
               founder,
               creation_time,
               modifier,
               modify_time,
               state,
               create_name
        from team_white
        where delete_logo = 0
          and create_id = #{createId}
          and state = 1
    </select>

    <select id="selectWhiteVague" resultType="com.zws.appeal.domain.White" resultMap="WhiteTreeResult">
        select id,
        create_id,
        whitelist_name,
        address_ip,
        note,
        founder,
        creation_time,
        modifier,
        modify_time,
        state,
        create_name
        from team_white
        <where>
            delete_logo=0 and create_id = #{createId}
            <if test="whitelistName != null and whitelistName != ''">
                and whitelist_name like concat('%', #{whitelistName}, '%')
            </if>
            <if test="addressIp != null and addressIp != ''">
                and address_ip like concat('%', #{addressIp}, '%')
            </if>
            <if test="founder != null and founder != ''">
                and founder like concat('%', #{founder}, '%')
            </if>
        </where>
    </select>

    <select id="selectWatermark" resultType="com.zws.appeal.domain.Watermark" resultMap="WatermarkTreeResult">
        select id,
               create_id,
               watermark_one,
               watermark_two,
               watermark_three,
               watermark_four
        from team_watermark
        where create_id = #{createId}
    </select>

    <select id="selectApprovalSettings" resultType="com.zws.appeal.domain.ApprovalSettings"
            resultMap="ApprovalSettingsTreeResult">
        <include refid="selectApprovalSettingsVo"/>
        where create_id=#{createId}
        order by approve_code asc
    </select>

    <select id="selectEvaluate" resultType="com.zws.appeal.domain.Evaluate">
        select
        eva.team_id AS teamId,
        cre.cname AS teamName,
        cre.category AS teamType,
        sum(eva.collected_principal) AS collectedPrincipal,
        max(eva.entrusted_amount) AS entrustedAmount,
        sum(eva.reminder_quantity) AS reminderQuantity,
        sum(eva.number_seats) AS numberSeats,
        sum(eva.call_volume) AS callVolume,
        max(eva.case_number) AS caseNumber
        from team_evaluate AS eva
        LEFT JOIN team_create AS cre ON (eva.team_id = cre.id)
        where eva.del_flag = 0
        <if test="recordTime1 != null">and eva.record_time &gt;= #{recordTime1}</if>
        <if test="recordTime2 != null">and eva.record_time &lt;= #{recordTime2}</if>
        group by eva.team_id
    </select>

    <select id="selectTeamIdEvaluate" resultType="java.math.BigDecimal">
        select
        max(month_collection_rate) AS monthCollectionRate
        from team_evaluate
        where del_flag = 0 and team_id = #{teamId}
        <if test="recordTime1 != null">and record_time &gt;= #{recordTime1}</if>
        <if test="recordTime2 != null">and record_time &lt;= #{recordTime2}</if>
    </select>

    <select id="selectEvaluateTeamId" resultType="java.lang.Long">
        select id
        from team_evaluate
        where del_flag = 0 and team_id = #{teamId}
        <if test="recordTime1 != null">and record_time &gt;= #{recordTime1}</if>
        <if test="recordTime2 != null">and record_time &lt;= #{recordTime2}</if>
    </select>

    <select id="selectEvaluateByTeamId" resultType="com.zws.appeal.domain.Evaluate">
        select
        eva.team_id AS teamId,
        cre.cname AS teamName,
        cre.category AS teamType,
        sum(eva.collected_principal) AS collectedPrincipal,
        max(eva.entrusted_amount) AS entrustedAmount,
        sum(eva.reminder_quantity) AS reminderQuantity,
        sum(eva.number_seats) AS numberSeats,
        sum(eva.call_volume) AS callVolume,
        max(eva.case_number) AS caseNumber
        from team_evaluate AS eva
        LEFT JOIN team_create AS cre ON (eva.team_id = cre.id)
        where eva.del_flag = 0 and eva.team_id = #{teamId}
        <if test="startTime != null">and eva.record_time &gt;= #{startTime}</if>
        <if test="endTime != null">and eva.record_time &lt;= #{endTime}</if>
    </select>

    <insert id="insertApprovalSettings" parameterType="com.zws.appeal.domain.ApprovalSettings">
        insert into team_approval_settings(create_id,approve_code,approval_name,founder,creationtime)
        values
        <foreach collection="approvalSettings" item="list" index="index" separator=",">
            (#{list.createId},
            #{list.approveCode},
            #{list.approvalName},
            #{list.founder},
            #{list.creationtime})
        </foreach>
    </insert>

    <insert id="addApprovalSetting" parameterType="com.zws.appeal.domain.ApprovalSettings" useGeneratedKeys="true"
            keyProperty="id">
        insert into team_approval_settings(create_id, approve_code, approval_name, founder, creationtime)
        values (#{createId}, #{approveCode}, #{approvalName}, #{founder}, #{creationtime})
    </insert>

    <select id="selectRole" resultType="com.zws.appeal.domain.Role">
        select id,
               create_id AS createId,
               role_name AS roleName
        from team_role
        where delete_logo = 0
          and status = 0
          and create_id = #{createId}
    </select>

    <select id="selectApprovalSteps" resultType="com.zws.appeal.domain.ApprovalSteps"
            resultMap="ApprovalStepsTreeResult">
        <include refid="selectApprovalStepsVo"/>
        where create_id=#{createId} and approve_code = #{approveCode}
        order by sort asc
    </select>

    <select id="selectApproval" resultType="com.zws.appeal.domain.ApprovalSteps"
            resultMap="ApprovalStepsTreeResult">
        <include refid="selectApprovalStepsVo"/>
        where create_id=#{createId} and approve_code = #{approveCode} and approver_id = #{approverId}
    </select>

    <select id="selectFounderId" resultType="com.zws.appeal.domain.Create" resultMap="CreateTreeResult">
        select id,
               cname,
               category,
               account,
               numbers,
               cooperation,
               margin,
               starts,
               complete,
               contact,
               machine,
               email,
               describes,
               founder_id,
               founder,
               creationtime,
               modifier,
               modify_time,
               collection_targets,
               delete_logo,
               login_id
        from team_create
        where delete_logo = 0
          and cooperation = 0
    </select>

    <select id="selectCreateById" resultMap="CreateTreeResult">
        select id,
               cname,
               category,
               account,
               numbers,
               cooperation,
               margin,
               starts,
               complete,
               contact,
               machine,
               email,
               describes,
               founder_id,
               founder,
               creationtime,
               modifier,
               modify_time,
               collection_targets,
               delete_logo,
               login_id
        from team_create
        where delete_logo = 0
          and id = #{id}
    </select>

    <insert id="addCertification" parameterType="com.zws.appeal.domain.Certification">
        insert into team_certification(create_id, user_id, identity_card, state, founder, creationtime)
        values (#{createId}, #{userId}, #{identityCard}, #{state}, #{founder}, #{creationtime})
    </insert>

    <update id="updateCertification" parameterType="com.zws.appeal.domain.Certification">
        update team_certification
        <trim prefix="SET" suffixOverrides=",">
            <if test="state != null">state = #{state},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where id=#{id}
    </update>

    <insert id="addCreate" parameterType="com.zws.appeal.domain.Create" useGeneratedKeys="true" keyProperty="id">
        insert into team_create
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cname != null and cname != ''">cname,</if>
            <if test="category != null ">category,</if>
            <if test="account != null and account != ''">account,</if>
            <if test="password != null">password,</if>
            <if test="numbers != null">numbers,</if>
            <if test="cooperation != null">cooperation,</if>
            <if test="margin != null">margin,</if>
            starts,
            complete,
            <if test="contact != null">contact,</if>
            <if test="machine != null">machine,</if>
            <if test="email != null">email,</if>
            describes,
            <if test="founderId != null">founder_id,</if>
            <if test="founder != null">founder,</if>
            <if test="creationtime != null">creationtime,</if>
            <if test="deleteLogo != null">delete_logo,</if>
            <if test="loginId != null">login_id,</if>
            <if test="commissionRatio != null">commission_ratio,</if>
            <if test="updatePasswordTime != null">update_password_time,</if>
            <if test="teamType != null">team_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cname != null and cname != ''">#{cname},</if>
            <if test="category != null ">#{category},</if>
            <if test="account != null and account != ''">#{account},</if>
            <if test="password != null">#{password},</if>
            <if test="numbers != null">#{numbers},</if>
            <if test="cooperation != null">#{cooperation},</if>
            <if test="margin != null">#{margin},</if>
            #{starts},
            #{complete},
            <if test="contact != null">#{contact},</if>
            <if test="machine != null">#{machine},</if>
            <if test="email != null">#{email},</if>
            #{describes},
            <if test="founderId != null">#{founderId},</if>
            <if test="founder != null">#{founder},</if>
            <if test="creationtime != null">#{creationtime},</if>
            <if test="deleteLogo != null">#{deleteLogo},</if>
            <if test="loginId != null">#{loginId},</if>
            <if test="commissionRatio != null">#{commissionRatio},</if>
            <if test="updatePasswordTime != null">#{updatePasswordTime},</if>
            <if test="teamType != null">#{teamType},</if>
        </trim>
    </insert>

    <insert id="addLabel" parameterType="com.zws.appeal.domain.Label">
        insert into team_label(create_id,label_content,state_label,code) values
        <foreach collection="label" item="list" index="index" separator=",">
            (#{list.createId},
            #{list.labelContent},
            #{list.stateLabel},
            #{list.code})
        </foreach>
    </insert>

    <insert id="addWhite" parameterType="com.zws.appeal.domain.White">
        insert into team_white(create_id, whitelist_name, address_ip, note, founder, creation_time, state, create_name)
        values (#{createId}, #{whitelistName}, #{addressIp}, #{note}, #{founder}, #{creationTime},
                #{state}, #{createName})
    </insert>

    <insert id="addState" parameterType="com.zws.appeal.domain.State">
        insert into team_states(create_id, whitelist_status, information_status, restricted_state, setting_status,
                                authorization_status, authentication_status, export_setting_status, push_app_status)
        values (#{createId}, #{whitelistStatus}, #{informationStatus}, #{restrictedState}, #{settingStatus},
                #{authorizationStatus}, #{authenticationStatus}, #{exportSettingStatus}, #{pushAppStatus})
    </insert>

    <insert id="addTargets" parameterType="com.zws.appeal.domain.Targets">
        insert into team_targets(create_id, founder, creation_time, collection_targets)
        values (#{createId}, #{founder}, #{creationTime}, #{collectionTargets})
    </insert>

    <insert id="addFile" parameterType="com.zws.appeal.domain.Files">
        insert into team_file(create_id, first_name, modify_name, file_url, founder, creation_time, delete_logo)
        values
        <foreach collection="file" item="list" index="index" separator=",">
            (#{list.createId},
            #{list.firstName},
            #{list.modifyName},
            #{list.fileUrl},
            #{list.founder},
            #{list.creationTime},
            #{list.deleteLogo})
        </foreach>
    </insert>

    <insert id="addDesensitization" parameterType="com.zws.appeal.domain.Desensitization">
        insert into team_desensitization(create_id, dname, numbers, card_id, bank_card, qq, we_chat, households,
                                         unit_address, residential_address, home_address, entity_name, founder,
                                         creationtime, delete_logo)
        values (#{createId}, #{dname}, #{numbers}, #{cardId}, #{bankCard}, #{qq}, #{weChat}, #{households},
                #{unitAddress}, #{residentialAddress}, #{homeAddress}, #{entityName}, #{founder}, #{creationtime},
                #{deleteLogo})
    </insert>

    <insert id="addWatermark" parameterType="com.zws.appeal.domain.Watermark">
        insert into team_watermark(create_id, watermark_one, watermark_two, watermark_three, watermark_four)
        values (#{createId}, #{watermarkOne}, #{watermarkTwo}, #{watermarkThree}, #{watermarkFour})
    </insert>

    <insert id="addApprovalSettings" parameterType="com.zws.appeal.domain.ApprovalSettings">
        insert into team_approval_settings(create_id, approve_code, approval_name, founder, creationtime)
        values
        <foreach collection="approvalSettings" item="list" index="index" separator=",">
            (#{list.createId}, #{list.approveCode}, #{list.approvalName}, #{list.founder}, #{list.creationtime})
        </foreach>
    </insert>

    <insert id="addEvaluate" parameterType="com.zws.appeal.domain.Evaluate">
        insert into team_evaluate(team_id, team_type, collected_principal, entrusted_amount, reminder_quantity,
                                  number_seats, call_volume, case_number, del_flag, record_time, month_collection_total,
                                  month_collection_target, month_collection_rate)
        values (#{teamId}, #{teamType}, #{collectedPrincipal}, #{entrustedAmount}, #{reminderQuantity}, #{numberSeats},
                #{callVolume}, #{caseNumber}, #{delFlag}, #{recordTime}, #{monthCollectionTotal},
                #{monthCollectionTarget}, #{monthCollectionRate})
    </insert>

    <update id="updateEvaluate" parameterType="com.zws.appeal.domain.Evaluate">
        update team_evaluate
        <trim prefix="SET" suffixOverrides=",">
            <if test="collectedPrincipal != null">collected_principal = #{collectedPrincipal},</if>
            <if test="entrustedAmount != null">entrusted_amount = #{entrustedAmount},</if>
            <if test="reminderQuantity != null">reminder_quantity = #{reminderQuantity},</if>
            <if test="numberSeats != null">number_seats = #{numberSeats},</if>
            <if test="callVolume != null">call_volume = #{callVolume},</if>
            <if test="caseNumber != null">case_number = #{caseNumber},</if>
            <if test="recordTime != null">record_time = #{recordTime},</if>
            <if test="monthCollectionTotal != null">month_collection_total = #{monthCollectionTotal},</if>
            <if test="monthCollectionTarget != null">month_collection_target = #{monthCollectionTarget},</if>
            <if test="monthCollectionRate != null">month_collection_rate = #{monthCollectionRate},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id=#{id}
    </update>

    <update id="updateApprovalSettingsDate" parameterType="com.zws.appeal.domain.ApprovalSettings">
        update team_approval_settings
        set modifier=#{modifier},
            modify_time=#{modifyTime},
            remarks=#{remarks}
        where approve_code = #{approveCode}
          and create_id = #{createId}
    </update>

    <insert id="addApprovalSteps" parameterType="com.zws.appeal.domain.ApprovalSteps">
        insert into team_approval_steps(create_id, approve_code, sort,approval_role,approval_id,approver,approver_id,
        founder, creationtime)
        values
        <foreach collection="approvalSteps" item="list" index="index" separator=",">
            (#{list.createId}, #{list.approveCode},
            #{list.sort},#{list.approvalRole},#{list.approvalId},#{list.approver},
            #{list.approverId},#{list.founder}, #{list.creationtime})
        </foreach>
    </insert>

    <insert id="insertApprovalStepsByTeamId" parameterType="com.zws.appeal.domain.ApprovalSteps">
        insert into team_approval_steps(create_id, approve_code, sort, approval_role, approval_id, approver,
                                        approver_id, founder, creationtime)
        values (#{createId}, #{approveCode}, #{sort}, #{approvalRole}, #{approvalId}, #{approver}, #{approverId},
                #{founder}, #{creationtime})
    </insert>

    <insert id="insertCategory" parameterType="com.zws.appeal.domain.Category">
        insert into team_category(team_id, status_before,
        status_after,operation_by,operation_by_id,operation_time,delete_logo)
        values
        <foreach collection="categories" item="list" index="index" separator=",">
            (#{list.teamId}, #{list.statusBefore},
            #{list.statusAfter},#{list.operationBy},#{list.operationById},#{list.operationTime},#{list.deleteLogo})
        </foreach>
    </insert>

    <update id="updateCreate" parameterType="com.zws.appeal.domain.Create">
        update team_create
        <trim prefix="SET" suffixOverrides=",">
            <if test="cname != null">cname = #{cname},</if>
            <if test="category != null">category = #{category},</if>
            <if test="password != null">password = #{password},</if>
            <if test="numbers != null">numbers = #{numbers},</if>
            <if test="cooperation != null">cooperation = #{cooperation},</if>
            <if test="margin != null">margin = #{margin},</if>
            <if test="starts != null">starts = #{starts},</if>
            <if test="complete != null">complete = #{complete},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="machine != null">machine = #{machine},</if>
            <if test="email != null">email = #{email},</if>
            <if test="describes != null">describes = #{describes},</if>
            <if test="founder != null">founder = #{founder},</if>
            <if test="creationtime != null">creationtime = #{creationtime},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="commissionRatio != null">commission_ratio = #{commissionRatio},</if>
            <if test="updatePasswordTime != null">update_password_time = #{updatePasswordTime},</if>
            <if test="teamType != null">team_type = #{teamType},</if>
        </trim>
        where id=#{id}
    </update>

    <update id="updateCompanyNum" parameterType="com.zws.appeal.domain.Create">
        update team_create
        set company_num=#{companyNum}
        where id = #{id}
    </update>
    <update id="updateCreatePassWord" parameterType="com.zws.appeal.domain.Create">
        update team_create
        <trim prefix="SET" suffixOverrides=",">
            <if test="password != null">password = #{password},</if>
            <if test="updatePasswordTime != null">update_password_time = #{updatePasswordTime},</if>
        </trim>
        where id=#{id}
    </update>

    <update id="updatePassword" parameterType="com.zws.appeal.domain.Create">
        <foreach collection="create" item="list" index="index" open="" close="" separator=";">
            update team_create
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.cooperation != null">cooperation = #{list.cooperation},</if>
                <if test="list.password != null">password = #{list.password},</if>
                <if test="list.modifier != null">modifier = #{list.modifier},</if>
                <if test="list.modifyTime != null">modify_time = #{list.modifyTime},</if>
            </trim>
            where id =#{list.id}
        </foreach>
    </update>

    <update id="updateLabel" parameterType="com.zws.appeal.domain.Label">
        <foreach collection="label" item="list" index="index" open="" close="" separator=";">
            update team_label
            <set>
                label_content = #{list.labelContent},
                state_label = #{list.stateLabel},
                modifier=#{list.modifier},
                modify_time=#{list.modifyTime}
            </set>
            where id=#{list.id} and create_id=#{list.createId}
        </foreach>
    </update>

    <update id="updateWhite" parameterType="com.zws.appeal.domain.White">
        update team_white
        set whitelist_name=#{whitelistName},
            address_ip=#{addressIp},
            note=#{note},
            modifier=#{modifier},
            modify_time=#{modifyTime}
        where id = #{id}
          and create_id = #{createId}
    </update>

    <update id="updateState" parameterType="com.zws.appeal.domain.White">
        update team_white
        set state=#{state}
        where id = #{id}
    </update>

    <update id="updateCreates" parameterType="com.zws.appeal.domain.Create">
        update team_create
        set collection_targets=#{collectionTargets}
        where id = #{id}
    </update>

    <update id="updateStates" parameterType="com.zws.appeal.domain.State">
        update team_states
        set whitelist_status=#{whitelistStatus},
            information_status=#{informationStatus},
            restricted_state=#{restrictedState},
            setting_status=#{settingStatus},
            authorization_status=#{authorizationStatus},
            authentication_status=#{authenticationStatus},
            security_verification_status = #{securityVerificationStatus},
            export_setting_status=#{exportSettingStatus},
            push_app_status=#{pushAppStatus},
            modifier=#{modifier},
            modify_time=#{modifyTime}
        where create_id = #{createId}
          and id = #{id}
    </update>

    <update id="updateWatermark" parameterType="com.zws.appeal.domain.Watermark">
        update team_watermark
        <trim prefix="SET" suffixOverrides=",">
            <if test="watermarkOne != null">watermark_one = #{watermarkOne},</if>
            <if test="watermarkTwo != null">watermark_two = #{watermarkTwo},</if>
            <if test="watermarkThree != null">watermark_three = #{watermarkThree},</if>
            <if test="watermarkFour != null">watermark_four = #{watermarkFour}</if>
        </trim>
        where create_id=#{createId}
    </update>

    <update id="updateDesensitization" parameterType="com.zws.appeal.domain.Desensitization">
        update team_desensitization
        <trim prefix="SET" suffixOverrides=",">
            <if test="dname != null">dname = #{dname},</if>
            <if test="numbers != null">numbers = #{numbers},</if>
            <if test="cardId != null">card_id = #{cardId},</if>
            <if test="bankCard != null">bank_card = #{bankCard},</if>
            <if test="qq != null">qq = #{qq},</if>
            <if test="weChat != null">we_chat = #{weChat},</if>
            <if test="households != null">households = #{households},</if>
            <if test="unitAddress != null">unit_address = #{unitAddress},</if>
            <if test="residentialAddress != null">residential_address = #{residentialAddress},</if>
            <if test="homeAddress != null">home_address = #{homeAddress},</if>
            <if test="entityName != null">entity_name = #{entityName},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where create_id=#{createId}
    </update>

    <update id="updateApprovalSteps" parameterType="com.zws.appeal.domain.ApprovalSteps">
        <foreach collection="approvalSteps" item="list" index="index" open="" close="" separator=";">
            update team_approval_steps
            <trim prefix="SET" suffixOverrides=",">
                <if test="list.sort != null">sort = #{list.sort},</if>
                <if test="list.approvalRole != null">approval_role = #{list.approvalRole},</if>
                <if test="list.approvalId != null">approval_id = #{list.approvalId},</if>
                <if test="list.approver != null">approver = #{list.approver},</if>
                <if test="list.approverId != null">approver_id = #{list.approverId},</if>
                <if test="list.modifier != null">modifier = #{list.modifier},</if>
                <if test="list.modifyTime != null">modify_time = #{list.modifyTime},</if>
            </trim>
            where id=#{list.id}
        </foreach>
    </update>

    <update id="deleteWhite" parameterType="int">
        <foreach collection="white" item="list" index="index" open="" close="" separator=";">
            update team_white
            <set>
                delete_logo = 1
            </set>
            where id = #{list.id}
        </foreach>
    </update>

    <update id="deleteFiles" parameterType="int">
        update team_file
        set delete_logo = 1
        where id = #{id}
    </update>

    <delete id="deleteApprovalSteps" parameterType="int">
        delete
        from team_approval_steps
        where id in
        <foreach collection='ids' item='item' open='(' separator=',' close=')'>
            #{item}
        </foreach>
    </delete>

    <select id="selectBaseList" resultType="int">
        select count(1) AS number
        from call_sip
        where del_flag = 0
          and team_id = #{teamId}
    </select>

    <select id="selectCallRecord" resultType="int">
        select count(1) AS number
        from call_record
        where del_flag = 0 and team_id = #{teamId}
        <if test="recordTime1 != null">and call_time &gt;= #{recordTime1}</if>
        <if test="recordTime2 != null">and call_time &lt;= #{recordTime2}</if>
    </select>

    <select id="selectTeamExport" resultType="com.zws.appeal.domain.TeamExport" resultMap="TeamExportResult">
        select id,
               create_id,
               export_status,
               modify_time,
               modifier,
               creation_time,
               founder,
               perms,
               menu_name,
               button_name,
               remark
        from team_export
        where create_id = #{createId}
          and delete_logo = 0
    </select>
    <select id="getTeamExportById" resultMap="TeamExportResult">
        select id,
               create_id,
               export_status,
               modify_time,
               modifier,
               creation_time,
               founder,
               perms,
               menu_name,
               button_name,
               remark
        from team_export
        where id = #{id}
    </select>

    <select id="selectLegalList" resultType="com.zws.system.api.domain.Legal">
        select
        id,
        legal_type legalType,
        legal_name legalName,
        legal_key legalKey,
        legal_val legalVal,
        state,
        sort,
        remark,
        del_flag delFlag,
        create_by,
        create_time,
        update_by,
        update_time
        from setup_legal
        where del_flag=0
        <if test="legalType!=null">
            and legal_type=#{legalType}
        </if>
        <if test="legalName!=null">
            and legal_name like concat("%",#{legalName},"%")
        </if>
        <if test="legalKey!=null">
            and legal_key=#{legalKey}
        </if>
        <if test="legalVal!=null">
            and legal_val=#{legalVal}
        </if>
        <if test="state!=null">
            and state=#{state}
        </if>
        <if test="params.noEqLegalKey!=null">
            and legal_key!=#{params.noEqLegalKey}
        </if>
        order by sort
    </select>
    <select id="getTeamEmployeesCooperation" resultType="java.lang.Integer">
        select tc.cooperation
        from team_employees AS te
                 left join team_create AS tc on te.create_id = tc.id
        where te.delete_logo = '0' and te.login_account = #{username}
    </select>
    <select id="getTeamCreateCooperation" resultType="java.lang.Integer">
        select cooperation
        from team_create
        where delete_logo = '0' and account = #{username}
    </select>

    <update id="updateTeamExport" parameterType="com.zws.appeal.domain.TeamExport">
        update team_export
        <trim prefix="SET" suffixOverrides=",">
            <if test="exportStatus != null">export_status = #{exportStatus},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where id=#{id}
    </update>

    <insert id="insertTeamExport" parameterType="com.zws.appeal.domain.TeamExport" useGeneratedKeys="true"
            keyProperty="id">
        insert into team_export(create_id, export_status, founder, creation_time, delete_logo,
                                perms, menu_name, button_name, remark)
        values (#{createId}, #{exportStatus}, #{founder}, #{creationtime}, #{deleteLogo},
                #{perms}, #{menuName}, #{buttonName}, #{remark})
    </insert>

</mapper>
