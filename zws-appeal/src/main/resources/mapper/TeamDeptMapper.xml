<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.TeamDeptMapper">

    <resultMap id="BaseResultMap" type="com.zws.appeal.domain.Dept">
        <result property="id" column="id"/>
        <result property="createId" column="create_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="status" column="status"/>
        <result property="deleteLogo" column="delete_logo"/>
        <result property="founder" column="founder"/>
        <result property="creationtime" column="creationtime"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
    </resultMap>


    <select id="selectProgenyDept" resultType="java.lang.Integer">
        SELECT id
        FROM team_dept
        WHERE FIND_IN_SET(#{deptId}, ancestors) > 0
          AND delete_logo = 0
          and create_id = #{teamId}
    </select>
    <select id="selectList" resultMap="BaseResultMap">
        select id,
        create_id,
        parent_id,
        ancestors,
        dept_name,
        order_num,
        status,
        delete_logo,
        founder,
        creationtime,
        modifier,
        modify_time
        from team_dept where delete_logo=0
        <where>
            <if test="createId!=null">
                and create_id=#{createId}
            </if>
            <if test="parentId!=null">
                and parent_id=#{parentId}
            </if>
        </where>
        order by order_num
    </select>


</mapper>
