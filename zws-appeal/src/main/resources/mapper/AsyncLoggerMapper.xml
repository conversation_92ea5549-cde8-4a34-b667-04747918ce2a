<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zws.appeal.mapper.AsyncLoggerMapper">

    <select id="selectTeamOperLog" resultType="com.zws.appeal.domain.log.TeamOperLog">
        select oper_id AS operId,
        title AS title,
        business_type AS businessType,
        method AS method,
        request_method AS requestMethod,
        operator_type AS operatorType,
        oper_name AS operName,
        dept_name AS deptName,
        oper_url AS operUrl,
        oper_ip AS operIp,
        oper_param AS operParam,
        json_result AS jsonResult,
        status AS status,
        error_msg AS errorMsg,
        oper_time AS operTime,
        create_id AS createId,
        user_id AS userId
        from team_oper_log
        where 1=1
        <if test="userId != null ">and user_id = #{userId} and account_type = 1</if>
        <if test="teamId != null ">and create_id = #{teamId}</if>

        <if test="businessType != null ">and business_type = #{businessType}</if>
        <if test="operName != null and operName != ''">and oper_name like concat('%', #{operName}, '%')</if>
        <if test="operTime1 != null">and oper_time &gt;= #{operTime1}</if>
        <if test="operTime2 != null">and oper_time &lt;= #{operTime2}</if>
        order by oper_time desc
    </select>

    <delete id="delectTeamLogininfor" parameterType="java.util.Date">
        delete
        from team_oper_log
        where oper_time &lt;= #{operTime}
    </delete>

    <insert id="insertTeamOperLog" parameterType="com.zws.appeal.domain.log.TeamOperLog">
        insert into team_oper_log(title, business_type, method, request_method, operator_type, oper_name, dept_name,
                                  oper_url, oper_ip, oper_param, json_result, status, error_msg, oper_time, create_id,
                                  user_id, account_type)
        values (#{title}, #{businessType}, #{method}, #{requestMethod}, #{operatorType}, #{operName}, #{deptName},
                #{operUrl}, #{operIp}, #{operParam}, #{jsonResult}, #{status}, #{errorMsg}, sysdate(), #{createId},
                #{userId}, #{accountType})
    </insert>


</mapper>
