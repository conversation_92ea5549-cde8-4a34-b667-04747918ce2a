package com.zws.saas;

import javax.imageio.ImageIO;
import java.awt.*;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * <AUTHOR>
 * @date ：Created in 2022/10/25 19:58
 */
public class Test {


    public static void main(String[] args) throws IOException {
//        int inistSchedule = 20;
//
//        int initSize = 100;
//        int jointDebts = 90;
//        System.out.println("initSize:" + initSize);
//        System.out.println("jointDebts:" + jointDebts);
//        double s = (double) jointDebts / initSize;
//        System.out.println("s:" + s);
//        int si = inistSchedule + (int) (s * 25);
//        if (si > 50) si = 50;
//        System.out.println("si:" + si);
//        String srcImgPath = "https://test1.gramc.com.cn/resource/preview/2024/04/18/41764647-ab9b-4ee7-a281-4841acbab365.jpg";
//        String srcImgPath = "https://zc-uat.hdamc-npl.com/resource//demo/2024/02/23/226a5a28-b30b-4f0a-b77c-9d440d99d1e0.jpg";
        String srcImgPath = "https://test1.gramc.com.cn/resource/preview/2024/1712563522986.jpg";
        URL url = new URL(srcImgPath);
        Image srcImg= ImageIO.read(url);
        System.out.println(srcImg);
    }
}
