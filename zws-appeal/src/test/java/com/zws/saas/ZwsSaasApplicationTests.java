package com.zws.saas;

import com.zws.common.core.constant.SecurityConstants;
import com.zws.common.core.domain.SmsPhoneAndMsg;
import com.zws.appeal.service.CensusAssetsService;
import com.zws.system.api.RemoteCaseService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.*;


@SpringBootTest
class ZwsSaasApplicationTests {

    @Value("${spring.servlet.multipart.max-file-size}")
    private String value;

    @Autowired
    private RemoteCaseService remoteCaseService;

    @Autowired
    private CensusAssetsService censusAssetsService;

    @Test
    void test123(){
        censusAssetsService.autoCensusAssets();
//        censusAssetsService.getStatisticsEmployeeTask();
    }

    /**
     * 重新写文件
     *
     * @param filePath
     * @param content
     */
    public static void contentToTxt(String filePath, String content) {
        FileOutputStream fos = null;
        OutputStreamWriter osw = null;
        BufferedWriter writer = null;
        try {
            File file = new File(filePath);
            if (!(file.isFile() && file.exists())) {
                file.createNewFile();// 文件不存在则创建
            }
            fos = new FileOutputStream(file);
            osw = new OutputStreamWriter(fos, "UTF-8");
            writer = new BufferedWriter(osw);
            writer.write(content);
            writer.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (writer != null) {
                    writer.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (osw != null) {
                    osw.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 读取文件内容
     *
     * @param filePathAndName String 如 c:\\1.txt 绝对路径
     * @return boolean
     */
    @Test
    public static String readFile(String filePathAndName) {
        String result = "";
        StringBuffer sb = new StringBuffer();
        FileInputStream fis = null;
        InputStreamReader read = null;
        BufferedReader reader = null;
        try {
            File file = new File(filePathAndName);
            if (file.isFile() && file.exists()) {
                fis = new FileInputStream(file);
                read = new InputStreamReader(fis, "UTF-8");
                reader = new BufferedReader(read);
                String line = "";
                while ((line = reader.readLine()) != null) {
                    sb.append(line);
                }
                result = sb.toString();
                sb.setLength(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                    reader = null;
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (read != null) {
                    read.close();
                    read = null;
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (fis != null) {
                    fis.close();
                    fis = null;
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    @Test
    void contextLoads() {

        SmsPhoneAndMsg smsPhoneAndMsg = new SmsPhoneAndMsg();
        smsPhoneAndMsg.setPhone("***********");
        smsPhoneAndMsg.setMsg("5210");
        smsPhoneAndMsg.setName("催收管理系统");
        smsPhoneAndMsg.setLoginAccount("ceshizhanghu");
        remoteCaseService.sendSmsCommission(smsPhoneAndMsg, SecurityConstants.INNER);
    }

}
